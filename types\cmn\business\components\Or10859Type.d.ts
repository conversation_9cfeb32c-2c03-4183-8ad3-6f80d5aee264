/**
 * 単方向バインドModelValue
 */
export interface Or10859OnewayType {
  /**
   * タイトルリスト
   */
  sortList: Title[]
}

/**
 * 双方向バインドModelValue
 */
export interface Or10859Type {
  /**
   * タイトルリスト
   */
  sortList: Title[]
}

/**
 * タイトル情報
 */
export interface Title {
  /**
   * 生活全般の解決すべき課題
   */
  lifeIssues?: string
  /**
   * 長期目標
   */
  longTermGoal?: string
  /**
   * 短期目標
   */
  shortTermGoal?: string
}
