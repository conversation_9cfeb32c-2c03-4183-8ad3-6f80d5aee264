<script setup lang="ts">
/**
 * Or31868:［フェースシート（パッケージプラン）］画面テンプレート
 *
 * @description
 * ［フェースシート（パッケージプラン）］画面テンプレート
 *
 * <AUTHOR>
 */
import { cloneDeep } from 'lodash'
import { computed, onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or28779Const } from '~/components/custom-components/organisms/Or28779/Or28779.constants'
import { Or28779Logic } from '~/components/custom-components/organisms/Or28779/Or28779.logic'
import { Or33216Const } from '~/components/custom-components/organisms/Or33216/Or33216.constants'
import { Or33216Logic } from '~/components/custom-components/organisms/Or33216/Or33216.logic'
import { Or33396Const } from '~/components/custom-components/organisms/Or33396/Or33396.constants'
import { Or33396Logic } from '~/components/custom-components/organisms/Or33396/Or33396.logic'
import { Or33423Const } from '~/components/custom-components/organisms/Or33423/Or33423.constants'
import { Or33423Logic } from '~/components/custom-components/organisms/Or33423/Or33423.logic'
import { Or35170Const } from '~/components/custom-components/organisms/Or35170/Or35170.constants'
import { Or35170Logic } from '~/components/custom-components/organisms/Or35170/Or35170.logic'
import { Or35478Const } from '~/components/custom-components/organisms/Or35478/Or35478.constants'
import { Or35478Logic } from '~/components/custom-components/organisms/Or35478/Or35478.logic'
import { OrX0001Const } from '~/components/custom-components/organisms/OrX0001/OrX0001.constants'
import { OrX0001Logic } from '~/components/custom-components/organisms/OrX0001/OrX0001.logic'
import { OrX0007Const } from '~/components/custom-components/organisms/OrX0007/OrX0007.constants'
import { OrX0007Logic } from '~/components/custom-components/organisms/OrX0007/OrX0007.logic'
import { OrX0008Const } from '~/components/custom-components/organisms/OrX0008/OrX0008.constants'
import { OrX0008Logic } from '~/components/custom-components/organisms/OrX0008/OrX0008.logic'
import { OrX0009Const } from '~/components/custom-components/organisms/OrX0009/OrX0009.constants'
import { OrX0009Logic } from '~/components/custom-components/organisms/OrX0009/OrX0009.logic'
import { OrX0010Const } from '~/components/custom-components/organisms/OrX0010/OrX0010.constants'
import { OrX0010Logic } from '~/components/custom-components/organisms/OrX0010/OrX0010.logic'
import { OrX0115Const } from '~/components/custom-components/organisms/OrX0115/OrX0115.constants'
import { OrX0115Logic } from '~/components/custom-components/organisms/OrX0115/OrX0115.logic'
import { Or31868Const } from '~/components/custom-components/template/Or31868/Or31868.constants'
import type {
  HistoryInfo,
  PlanPeriodInfo,
} from '~/components/custom-components/template/Or31868/Or31868.type'
import { useCommonProps } from '~/composables/useCommonProps'
import { useSetupChildProps } from '~/composables/useComponentVue'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  FaceSheet3SelectDataEntity,
  FaceSheet3SelectInEntity,
  FaceSheet3SelectSelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheet3SelectEntity'
import type {
  FaceSheet3UpdateInEntity,
  FaceSheet3UpdateInfo,
} from '~/repositories/cmn/entities/FaceSheet3UpdateEntity'
import type {
  FaceSheetPackage1HistorySelectInEntity,
  FaceSheetPackage1HistorySelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheetPackage1HistorySelectEntity'
import type {
  FaceSheet1Info,
  FaceSheetPackage1InitSelectDataEntity,
  FaceSheetPackage1InitSelectInEntity,
  FaceSheetPackage1InitSelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheetPackage1InitSelectEntity'
import type {
  FaceSheetPackage1PlanningPeriodSelectInEntity,
  FaceSheetPackage1PlanningPeriodSelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheetPackage1PlanningPeriodSelectEntity'
import type { FaceSheetPackage1UpdateInEntity } from '~/repositories/cmn/entities/FaceSheetPackage1UpdateEntity'
import type {
  FaceSheet2Info,
  FaceSheet2OtherInfo,
  FaceSheetPackage2InitSelectDataEntity,
  FaceSheetPackage2InitSelectInEntity,
  FaceSheetPackage2InitSelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheetPackage2InitSelectEntity'
import type { FaceSheetPackage2UpdateInEntity } from '~/repositories/cmn/entities/FaceSheetPackage2UpdateEntity'
import type {
  FaceSheetPackage4InitSelectEntity,
  FaceSheetPackage4InitSelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheetPackage4InitSelectEntity'
import type {
  CpnTucSypFace1,
  FaceSheet3InfoSelDmyYoshien,
  FaceSheetPackage4UpdateInEntity,
} from '~/repositories/cmn/entities/FaceSheetPackage4UpdateEntity'
import { useScreenStore } from '~/stores/session/screen'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00038OnewayType, Mo00038Type } from '~/types/business/components/Mo00038Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Or28779OnewayType, Or28779Type } from '~/types/cmn/business/components/Or28779Type'
import type { Or33423OnewayType } from '~/types/cmn/business/components/Or33423Type'
import type { OrX0001OnewayType, OrX0001Type } from '~/types/cmn/business/components/OrX0001Type'
import type { OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import type { OrX0008OnewayType, RirekiInfo } from '~/types/cmn/business/components/OrX0008Type'
import type { OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import type { OrX0010OnewayType } from '~/types/cmn/business/components/OrX0010Type'
import { useJigyoList } from '~/utils/useJigyoList'
import { useUserListInfo } from '~/utils/useUserListInfo'

/**************************************************
 * Props
 **************************************************/
const props = defineProps(useCommonProps())

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()
const { syscomUserListFunc, syscomUserSelectWatchFunc } = useUserListInfo()
const { updateJigyoList, jigyoListWatch } = useJigyoList()

const orX0001 = ref({ uniqueCpId: '' })
const orX0007 = ref({ uniqueCpId: '' })
const orX0008 = ref({ uniqueCpId: '' })
const orX0009 = ref({ uniqueCpId: '' })
const orX0010 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const or11871 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or28779 = ref({ uniqueCpId: '' })
const or33216 = ref({ uniqueCpId: '' }) // フェースシート①
const or35170 = ref({ uniqueCpId: '' }) // フェースシート②
const or33396 = ref({ uniqueCpId: '' }) // フェースシート③
const or33423 = ref({ uniqueCpId: '' }) // フェースシート④
const or33423CP = ref<{ validate: () => boolean }>()
const or35478 = ref({ uniqueCpId: '' }) // フェースシート複写
const or41179 = ref({ uniqueCpId: '' })

const or00249 = ref({ uniqueCpId: '' })
const or00094 = ref({ uniqueCpId: '' })

const orX0115 = ref({ uniqueCpId: '' }) // 対象期間画面

// ローカルTwoway
const local = reactive({
  // タブID
  currentTabId: '1',
  // フラグ
  flag: {
    // 期間管理
    periodManage: '',
    // 計画期間が登録されていない(true:いる、false:いない)
    periodManageRegistration: false,
  },
  // 計画対象期間情報ID
  planPeriodId: '0',
  // 計画期間ペイジ番号
  planNo: 0,
  // 計画期間総件数
  planCount: 0,
  // 履歴情報ID
  historyId: '0',
  // 履歴情報ペイジ番号
  historyNo: 0,
  // 履歴情報総件数
  historyCount: 0,
  // 作成日
  createDate: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  // 初回作成日
  firstTimeCreateDate: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  // ケース番号
  caseNum: { value: '' } as Mo00045Type,
  // ケース変更回数
  caseCount: { mo00045: { value: '' } as Mo00045Type } as Mo00038Type,
  // 改訂フラグ
  kaiteiFlg: Or31868Const.KAITEI_FLAG_NEW,
  // 履歴更新回数
  modifiedCnt: '',
  // タブ
  mo00043: {
    id: '1',
  } as Mo00043Type,
})

// ローカルOneway
const localOneway = reactive({
  orX0007Oneway: { planTargetPeriodData: { currentIndex: 0, totalCount: 0 } } as OrX0007OnewayType,
  // 履歴
  orX0008Oneway: {
    createData: {} as RirekiInfo,
    useId: systemCommonsStore.getUserSelectSelfId() ?? '',
    screenID: Or31868Const.SCREEN_ID,
  } as OrX0008OnewayType,
  //作成者
  orX0009Oneway: {} as OrX0009OnewayType,
  //作成日
  orX0010Oneway: {} as OrX0010OnewayType,
  mo00045Oneway: {
    itemLabel: t('label.caseNo'),
    maxLength: '10',
    isVerticalLabel: false,
    width: '105',
    customClass: {
      labelClass: 'ma-1 mr-2',
    },
  } as Mo00045OnewayType,
  mo00038Oneway: {
    mo00045Oneway: {
      maxLength: '3',
      showItemLabel: false,
    },
    min: -99,
    max: 999,
  } as Mo00038OnewayType,
  //回目ボタン
  mo00611CountOneway: {
    btnLabel: t('btn.times'),
    minWidth: '0',
  } as Mo00611OnewayType,
  //初回作成日
  mo00020FirstCreateOneway: {
    itemLabel: t('label.first-create-date'),
    showItemLabel: true,
    isVerticalLabel: false,
    isRequired: true,
    hideDetails: true,
    disabled: false,
    width: '135',
  } as Mo00020OnewayType,
  // タブ
  mo00043OnewayType: {
    tabItems: [
      {
        id: '1',
        title: t('label.face-sheet1'),
        tooltipText: t('label.face-sheet1'),
        tooltipLocation: 'bottom',
      },
      {
        id: '2',
        title: t('label.face-sheet2'),
        tooltipText: t('label.face-sheet2'),
        tooltipLocation: 'bottom',
      },
      {
        id: '3',
        title: t('label.face-sheet3'),
        tooltipText: t('label.face-sheet3'),
        tooltipLocation: 'bottom',
      },
      {
        id: '4',
        title: t('label.face-sheet4'),
        tooltipText: t('label.face-sheet4'),
        tooltipLocation: 'bottom',
      },
    ],
    minWidth: '58px',
    disabledTo: true,
  } as Mo00043OnewayType,
  // フェースシート①
  faceSheet1: cloneDeep(Or33216Const.DEFAULT.ONE_WAY),
  // フェースシート①
  faceSheet2: cloneDeep(Or35170Const.DEFAULT.ONE_WAY),
  // フェースシート③
  faceSheet3: {
    ...cloneDeep(Or33396Const.DEFAULT.ONE_WAY),
    createDate: systemCommonsStore.getSystemDate ?? '',
  },
  // フェースシート④
  faceSheet4: {
    svJigyoId: 1,
    userId: '',
    surveyAssessmentKind: '',
  } as Or33423OnewayType,
})

// メッセージ「i-cmn-11260」 - 削除確認画面
const orX0001Type = ref<OrX0001Type>({
  deleteSyubetsu: '',
})

// OrX0001単方向バインド -  削除確認画面
// メッセージ「i-cmn-11260」
const orX0001Oneway = ref<OrX0001OnewayType>({
  /**
   * 作成日
   */
  createYmd: '',
  /**
   * 機能名
   */
  kinouKnj: t('label.assessment'),
  /**
   * 選択タブ名
   */
  selectTabName: '',
  /**
   * タブ名（開始）
   */
  startTabName: t('label.border-number-1'),
  /**
   * タブ名（終了）
   */
  endTabName: t('label.border-number-4'),
})

/**
 * 印刷設定双方向バインド
 */
const or28779Data = ref<Or28779Type>({})

/**
 * 印刷設定単方向バインド
 */
const or28779Oneway = ref<Or28779OnewayType>({
  /** システムコード */
  systemCode: '',
  /** システム略称 */
  systemNameShort: '',
  /** 計画期間管理フラグ */
  planPeriodManagementFlg: '',
  /** 法人ID */
  corporationId: '',
  /** 施設ID */
  facilityId: '',
  /** 事業者ID */
  officeId: '',
  /** 利用者ID */
  userId: '',
  /** 職員ID */
  staffId: '',
  /** 担当ケアマネ設定フラグ */
  careManagerSettingFlg: '',
  /** 担当者ID */
  tantoshaId: '',
  /** セクション名 */
  sectionName: '',
  /** 選択帳票番号 */
  selectedLedgerNo: '',
  /** 処理年月日 */
  processYmd: '',
  /** 50音行番号 */
  gojuuonRowNo: '',
  /** 50音母音 */
  gojuuonKana: [],
  /** 履歴ID */
  historyId: '',
  /** 利用者情報リスト */
  userInfoList: [],
  /** アセスメントID */
  assessmentId: '',
})
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  // コントロール設定
  void init()
})

/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0001Const.CP_ID(1)]: orX0001.value,
  [OrX0007Const.CP_ID(1)]: orX0007.value,
  [OrX0008Const.CP_ID(1)]: orX0008.value,
  [OrX0009Const.CP_ID(1)]: orX0009.value,
  [OrX0010Const.CP_ID(1)]: orX0010.value,
  [Or00248Const.CP_ID(1)]: or00248.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or28779Const.CP_ID(1)]: or28779.value,
  [Or33423Const.CP_ID(1)]: or33423.value,
  [Or35170Const.CP_ID(1)]: or35170.value,
  [Or33396Const.CP_ID(1)]: or33396.value,
  [Or33216Const.CP_ID(1)]: or33216.value,
  [Or35478Const.CP_ID(1)]: or35478.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [Or00249Const.CP_ID(0)]: or00249.value,
  [Or00094Const.CP_ID(0)]: or00094.value,
})

useSetupChildProps(orX0007.value.uniqueCpId, {
  [OrX0115Const.CP_ID(0)]: orX0115.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  let isTabEdit = false
  switch (local.currentTabId) {
    // タブ1
    case Or31868Const.TAB_NUM_1: {
      isTabEdit = useScreenStore().getCpNavControl(or33216.value.uniqueCpId) ?? false
      break
    }
    // タブ2
    case Or31868Const.TAB_NUM_2: {
      isTabEdit = useScreenStore().getCpNavControl(or35170.value.uniqueCpId) ?? false
      break
    }
    // タブ3
    case Or31868Const.TAB_NUM_3: {
      isTabEdit = useScreenStore().getCpNavControl(or33396.value.uniqueCpId) ?? false
      break
    }
    // タブ4
    case Or31868Const.TAB_NUM_4: {
      isTabEdit = useScreenStore().getCpNavControl(or33423.value.uniqueCpId) ?? false
      break
    }
  }

  const isTabDeleted = deleteFlag.value !== Or31868Const.DEL_FLAG_NONE

  return isTabEdit || isTabDeleted
})
// インフォメーションダイアログの表示フラグ
const showOr21814 = computed(() => {
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
// 削除確認画面の表示フラグ
const showOrX0001 = computed(() => {
  return OrX0001Logic.state.get(orX0001.value.uniqueCpId)?.isOpen ?? false
})
// ［フェースシート複写］画面の表示フラグ
const showOr35478 = computed(() => {
  return Or35478Logic.state.get(or35478.value.uniqueCpId)?.isOpen ?? false
})
// ［印刷設定］画面の表示フラグ
const showOr28779 = computed(() => {
  return Or28779Logic.state.get(or28779.value.uniqueCpId)?.isOpen ?? false
})
// 計画期間表示フラグ
const planPeriodShow = ref<boolean>(true)
// 履歴表示フラグ
const historyShow = ref<boolean>(true)
// 作成者表示フラグ
const authorShow = ref<boolean>(true)
// 作成日表示フラグ
const createDateShow = ref<boolean>(true)
// ケース番号表示フラグ
const caseNumShow = ref<boolean>(true)
// 初回作成日表示フラグ
const firstCreateDateShow = ref<boolean>(true)
// 入力フーム表示フラグ
const inputBoomShow = ref<boolean>(true)

// 削除処理区分
const deleteFlag = ref<string>(Or31868Const.DEL_FLAG_NONE)
// 更新区分
const updateFlag = ref<string>(Or31868Const.UPD_FLAG_NONE)
// 履歴更新区分
const updateHisFlag = ref<string>(Or31868Const.UPD_FLAG_NONE)
// 二回以上続けて新規ボタン押下したフラグ
const doubleCreateFlag = ref<boolean>(false)
// 期間処理区分
const planActFlag = ref<string>(Or31868Const.ACT_FLAG_INIT)
// 履歴処理区分
const historyActFlag = ref<string>(Or31868Const.ACT_FLAG_INIT)

/** データ再取得フラグ(計画対象期間) */
const retrievePlanDataFlg = ref<boolean>(false)
/** データ再取得フラグ(履歴) */
const retrieveHistoryDataFlg = ref<boolean>(false)
/** データ再取得フラグ(タブのみ) */
const retrieveTabDataFlg = ref<boolean>(false)

// 子コンポーネントに対して初期設定を行う
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.assessment-face-sheet'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: true,
    showCreateBtn: true,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: false,
    showOptionMenuBtn: true,
    showOptionMenuDelete: false,
  },
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      favoriteIconClick()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { favoriteEventFlg: false },
      })
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      void updateBtnClick().then(() => {
        Or11871Logic.event.set({
          uniqueCpId: or11871.value.uniqueCpId,
          events: { saveEventFlg: false },
        })
      })
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      void addBtnClick().then(() => {
        Or11871Logic.event.set({
          uniqueCpId: or11871.value.uniqueCpId,
          events: { createEventFlg: false },
        })
      })
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
      void printSettingIconClick().then(() => {
        Or11871Logic.event.set({
          uniqueCpId: or11871.value.uniqueCpId,
          events: { printEventFlg: false },
        })
      })
    }
  }
)

/**
 * 利用者選択監視
 */
syscomUserSelectWatchFunc((newSelfId) => {
  if (newSelfId !== '') {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: newSelfId,
        },
      },
    })

    local.planPeriodId = '0'
    local.historyId = '0'
    planActFlag.value = Or31868Const.ACT_FLAG_INIT
    historyActFlag.value = Or31868Const.ACT_FLAG_INIT
    doubleCreateFlag.value = false

    // 情報を再取得する
    retrievePlanDataFlg.value = true
  }
})

/**
 * 事業所選択更新の監視
 */
jigyoListWatch(or41179.value.uniqueCpId, (newJigyoId: string) => {
  if (!newJigyoId) {
    return
  }

  local.planPeriodId = '0'
  local.historyId = '0'
  planActFlag.value = Or31868Const.ACT_FLAG_INIT
  historyActFlag.value = Or31868Const.ACT_FLAG_INIT
  doubleCreateFlag.value = false

  void nextTick(() => {
    // 情報を再取得する
    retrievePlanDataFlg.value = true
  })
})
/**
 * AC014_「計画対象期間-前へアイコンボタン」押下
 * AC015_「計画対象期間-次へアイコンボタン」押下
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    const planID = newValue.planTargetPeriodId
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg

    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }

    //更新フラグINIT
    OrX0007Logic.data.set({
      uniqueCpId: orX0007.value.uniqueCpId,
      value: {
        planTargetPeriodId: planID,
        PlanTargetPeriodUpdateFlg: '',
      },
    })

    // 事前チェック
    if (planUpdateFlg === Or31868Const.ORX0007_ICON_CLICK) {
      //「期間-選択確認前 アイコンボタン」押下
      if (local.planPeriodId === planID) {
        // 選択前の対象期間から変更がない場合
        return
      }
    } else if (planUpdateFlg === Or31868Const.ORX0007_PRE_PAGE) {
      //「期間-前へ アイコンボタン」押下
      if (local.planNo <= 1) {
        // 1件目の計画対象期間データが表示されている状態の場合
        await openWarningDialog(or21814.value.uniqueCpId, {
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11262'),
        })
        return
      }
    } else if (planUpdateFlg === Or31868Const.ORX0007_NEXT_PAGE) {
      //「期間-次へ アイコンボタン」押下
      if (local.planNo === local.planCount) {
        // 最終件目の計画対象期間データが表示されている状態の場合
        await openWarningDialog(or21814.value.uniqueCpId, {
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11263'),
        })
        return
      }
    }

    // 画面入力データに変更がある場合
    if (isEdit.value) {
      const result = await openWarningDialog(or21814.value.uniqueCpId, {
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })

      if (result === Or31868Const.DIALOG_RESULT_YES) {
        await updateBtnClick(true)
      } else if (result === Or31868Const.DIALOG_RESULT_CANCEL) {
        return
      }
    }

    if (planUpdateFlg === Or31868Const.ORX0007_ICON_CLICK) {
      //「期間-選択確認前 アイコンボタン」押下
      local.planPeriodId = planID
      planActFlag.value = Or31868Const.ACT_FLAG_OPEN
    } else if (planUpdateFlg === Or31868Const.ORX0007_PRE_PAGE) {
      //「期間-前へ アイコンボタン」押下
      planActFlag.value = Or31868Const.ACT_FLAG_PREV
    } else if (planUpdateFlg === Or31868Const.ORX0007_NEXT_PAGE) {
      //「期間-次へ アイコンボタン」押下
      planActFlag.value = Or31868Const.ACT_FLAG_NEXT
    }
    local.historyId = '0'
    historyActFlag.value = Or31868Const.ACT_FLAG_INIT

    //データ再取得フラグ設定
    retrievePlanDataFlg.value = true
  },
  { deep: true }
)

/**
 * AC017_「履歴-前へアイコンボタン」押下
 * AC018_「履歴-次へアイコンボタン」押下
 * 履歴変更の監視
 */
watch(
  () => OrX0008Logic.data.get(orX0008.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    const createId = newValue.createId
    const createUpateFlg = newValue.createUpateFlg

    if (createUpateFlg === undefined || createUpateFlg === '') {
      return
    }
    //更新フラグINIT
    OrX0008Logic.data.set({
      uniqueCpId: orX0008.value.uniqueCpId,
      value: {
        createId: createId,
        createUpateFlg: '',
      },
    })

    // 事前チェック
    if (createUpateFlg === Or31868Const.ORX0008_ICON_CLICK) {
      //「履歴-選択確認後 アイコンボタン」押下
      if (local.historyId === createId) {
        // 選択前の対象期間から変更がない場合
        return
      }
    } else if (createUpateFlg === Or31868Const.ORX0008_PRE_PAGE) {
      //「履歴-前へ アイコンボタン」押下
      if (local.historyNo <= 1) {
        // 1件目の履歴データが表示されている状態の場合
        return
      }
    } else if (createUpateFlg === Or31868Const.ORX0008_NEXT_PAGE) {
      //「履歴-次へ アイコンボタン」押下
      if (local.historyNo === local.historyCount) {
        // 最終件目の履歴データが表示されている状態の場合
        return
      }
    }

    // 画面入力データに変更がある場合
    if (isEdit.value) {
      const result = await openWarningDialog(or21814.value.uniqueCpId, {
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })

      if (result === Or31868Const.DIALOG_RESULT_YES) {
        await updateBtnClick(true)
      } else if (result === Or31868Const.DIALOG_RESULT_CANCEL) {
        return
      }
    }

    if (createUpateFlg === Or31868Const.ORX0008_ICON_CLICK) {
      //「履歴-選択確認後 アイコンボタン」押下
      local.historyId = createId
      historyActFlag.value = Or31868Const.ACT_FLAG_OPEN
    } else if (createUpateFlg === Or31868Const.ORX0008_PRE_PAGE) {
      //「履歴-前へ アイコンボタン」押下
      historyActFlag.value = Or31868Const.ACT_FLAG_PREV
    } else if (createUpateFlg === Or31868Const.ORX0008_NEXT_PAGE) {
      //「履歴-次へ アイコンボタン」押下
      historyActFlag.value = Or31868Const.ACT_FLAG_NEXT
    }
    planActFlag.value = Or31868Const.ACT_FLAG_INIT

    //データ再取得フラグ設定
    retrieveHistoryDataFlg.value = true
  },
  { deep: true }
)

/**
 * 作成日変更の監視
 */
watch(
  () => OrX0010Logic.data.get(orX0010.value.uniqueCpId),
  (newValue) => {
    localOneway.faceSheet3.createDate = newValue?.value ?? ''
  },
  { deep: true }
)

/**
 * データ再取得フラグの監視
 */
watch(retrievePlanDataFlg, (newValue) => {
  if (newValue) {
    // 計画期間情報取得
    void getPlanData().finally(() => {
      retrievePlanDataFlg.value = false
    })
  }
})
watch(retrieveHistoryDataFlg, (newValue) => {
  if (newValue) {
    // 履歴情報取得
    void getHistoryData().finally(() => {
      retrieveHistoryDataFlg.value = false
    })
  }
})
watch(retrieveTabDataFlg, (newValue) => {
  if (newValue) {
    // 各タブの最新情報を取得する
    void getTabsData().finally(() => {
      retrieveTabDataFlg.value = false
    })
  }
})

/**
 * AC011_「削除」押下_AC011
 */
watch(
  () => orX0001Type.value,
  (newValue) => {
    if (newValue) {
      if (
        Or31868Const.ORX0001_ITEM_1 === newValue.deleteSyubetsu ||
        Or31868Const.ORX0001_ITEM_2 === newValue.deleteSyubetsu
      ) {
        // 削除確認画面ダイアログに「現在表示している画面のみ削除する。」を選択する場合
        if (Or31868Const.ORX0001_ITEM_1 === newValue.deleteSyubetsu) {
          deleteFlag.value = Or31868Const.DEL_FLAG_CURRENT
        }
        // 削除確認画面ダイアログに「表示している画面を履歴ごと削除する」を選択する場合
        else if (Or31868Const.ORX0001_ITEM_2 === newValue.deleteSyubetsu) {
          deleteFlag.value = Or31868Const.DEL_FLAG_ALL
        }
        updateHisFlag.value = Or31868Const.UPD_FLAG_D
        updateFlag.value = Or31868Const.UPD_FLAG_D

        // 以下の項目を非活性にする。
        // 作成者選択アイコンボタン、作成日、作成日カレンダ、
        // ケース番号、変更回数、回目ボタン、初回作成日、初回作成日カレンダ
        localOneway.orX0009Oneway.isDisabled = true
        localOneway.orX0010Oneway.isDisabled = true
        localOneway.mo00045Oneway.disabled = true
        localOneway.mo00038Oneway.mo00045Oneway!.disabled = true
        localOneway.mo00611CountOneway.disabled = true
        localOneway.mo00020FirstCreateOneway.disabled = true

        // 入力フームのすべて項目を非表示
        inputBoomShow.value = false
      }
    }
  }
)

/**************************************************
 * 関数
 **************************************************/
/**
 *  AC001_初期表示
 */
const init = async () => {
  // 親画面データ設定

  // 利用者を取得
  await syscomUserListFunc(false, props.uniqueCpId)
  // 五十音フィルタを設定
  const filter = systemCommonsStore.getUserSelectHistoryFilterInitials()
  if (filter === undefined || filter.length === 0) {
    systemCommonsStore.setUserSelectFilterInitials([Or31868Const.GOJUUON_ALL])
  }

  // 事業所を設定
  updateJigyoList(or41179.value.uniqueCpId)
  if (systemCommonsStore.getUserSelectSelfId()) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })

    // 計画期間情報取得フラグ設定
    retrievePlanDataFlg.value = true
  }
}

/**
 * 計画期間情報取得
 */
const getPlanData = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: FaceSheetPackage1PlanningPeriodSelectInEntity = {
    syoriKbn: planActFlag.value,
    sc1Id: local.planPeriodId,
    defSvJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    syubetuId: systemCommonsStore.getSyubetu ?? '',
    jigyoIdList: systemCommonsStore.getSvJigyoIdList.slice() ?? [],
    groupId: systemCommonsStore.getTekiyouGroupId ?? '',
    faceId: local.historyId,
    kaiteiFlg: local.kaiteiFlg,
  }

  const resData: FaceSheetPackage1PlanningPeriodSelectOutEntity = await ScreenRepository.select(
    'FaceSheetPackage1PlanningPeriodSelect',
    inputData
  )

  // 画面項目設定
  if (Or31868Const.RES_SUCCESS === resData.statusCode && resData.data) {
    // 計画期間情報
    const kikanKanriFlg = resData.data.kikanKanriFlg

    let planPeriodInfo: PlanPeriodInfo | undefined = undefined
    if (resData.data.kikanInfo.length > 0) {
      planPeriodInfo = {
        kknStartYmd: resData.data.kikanInfo[0].startYmd,
        kknEndYmd: resData.data.kikanInfo[0].endYmd,
        sc1Id: resData.data.kikanInfo[0].sc1Id,
        pageId: resData.data.kikanNo,
        pageCnt: resData.data.kikanCnt,
      }
    }
    setFormData(kikanKanriFlg, planPeriodInfo)

    // 履歴情報
    let historyInfo: HistoryInfo | undefined = undefined
    if (resData.data.rirekiOutD.length > 0 && resData.data.rirekiOutD[0].rirekiInfo.length > 0) {
      historyInfo = {
        sc1Id: resData.data.rirekiOutD[0].rirekiInfo[0].sc1Id,
        faceId: resData.data.rirekiOutD[0].rirekiInfo[0].faceId,
        shokuId: resData.data.rirekiOutD[0].rirekiInfo[0].shokuId,
        shokuKnj: resData.data.rirekiOutD[0].rirekiInfo[0].shokuName,
        created: resData.data.rirekiOutD[0].rirekiInfo[0].createYmd,
        pageId: resData.data.rirekiOutD[0].rirekiNo,
        pageCnt: resData.data.rirekiOutD[0].rirekiCnt,
        kaiteiFlg: resData.data.rirekiOutD[0].rirekiInfo[0].kaiteiFlg,
        shokaiYmd: resData.data.rirekiOutD[0].rirekiInfo[0].shokaiYmd,
        caseNo: resData.data.rirekiOutD[0].rirekiInfo[0].caseNo,
        henkoKaisu: resData.data.rirekiOutD[0].rirekiInfo[0].henkoKaisu,
        modifiedCnt: resData.data.rirekiOutD[0].rirekiInfo[0].modifiedCnt,
      }
    }
    setHistoryData(historyInfo)

    // タブ情報取得フラグ設定
    retrieveTabDataFlg.value = true
  }
}

/**
 * 履歴情報取得
 */
const getHistoryData = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: FaceSheetPackage1HistorySelectInEntity = {
    rirekiNo: String(local.historyNo),
    rirekiSyoriKbn: historyActFlag.value,
    syoriKbn: planActFlag.value,
    defSvJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    syubetuId: systemCommonsStore.getSyubetu ?? '',
    sc1Id: local.planPeriodId,
    faceId: local.historyId,
    jigyoIdList: systemCommonsStore.getSvJigyoIdList.slice() ?? [],
    groupId: systemCommonsStore.getTekiyouGroupId ?? '',
    kaiteiFlg: local.kaiteiFlg,
  }

  const resData: FaceSheetPackage1HistorySelectOutEntity = await ScreenRepository.select(
    'FaceSheetPackage1HistorySelect',
    inputData
  )

  // 画面項目設定
  if (Or31868Const.RES_SUCCESS === resData.statusCode && resData.data) {
    let historyInfo: HistoryInfo | undefined = undefined
    if (resData.data.rirekiInfo.length > 0) {
      historyInfo = {
        sc1Id: resData.data.rirekiInfo[0].sc1Id,
        faceId: resData.data.rirekiInfo[0].faceId,
        shokuId: resData.data.rirekiInfo[0].shokuId,
        shokuKnj: resData.data.rirekiInfo[0].shokuName,
        created: resData.data.rirekiInfo[0].createYmd,
        pageId: resData.data.rirekiNo,
        pageCnt: resData.data.rirekiCnt,
        kaiteiFlg: resData.data.rirekiInfo[0].kaiteiFlg,
        shokaiYmd: resData.data.rirekiInfo[0].shokaiYmd,
        caseNo: resData.data.rirekiInfo[0].caseNo,
        henkoKaisu: resData.data.rirekiInfo[0].henkoKaisu,
        modifiedCnt: resData.data.rirekiInfo[0].modifiedCnt,
      }
    }
    setHistoryData(historyInfo)

    // タブ情報取得フラグ設定
    retrieveTabDataFlg.value = true
  }
}

/**
 * 各タブの最新情報を取得する
 */
const getTabsData = async () => {
  switch (local.currentTabId) {
    case Or31868Const.TAB_NUM_1: {
      // タブ1
      const inputData: FaceSheetPackage1InitSelectInEntity = {
        tabId: local.currentTabId,
        faceId: local.historyId,
        syoriKbn: planActFlag.value,
        rirekiSyoriKbn: historyActFlag.value,
        defSvJigyoId: systemCommonsStore.getSvJigyoId ?? '',
        shokuinId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
        sysCd: Or31868Const.SYS_CODE,
        jigyoIdList: systemCommonsStore.getSvJigyoIdList.slice() ?? [],
        groupId: systemCommonsStore.getTekiyouGroupId ?? '',
        kaiteiFlg: local.kaiteiFlg,
      }

      const resData: FaceSheetPackage1InitSelectOutEntity = await ScreenRepository.select(
        'FaceSheetPackage1InitSelect',
        inputData
      )

      // 画面項目設定
      if (Or31868Const.RES_SUCCESS === resData.statusCode && resData.data) {
        setTab1Data(resData.data)
      }
      break
    }
    case Or31868Const.TAB_NUM_2: {
      // タブ2
      const inputData: FaceSheetPackage2InitSelectInEntity = {
        /** フェースシート履歴ID */
        faceId: local.historyId,
        /** 事業所ID */
        defSvJigyoId: systemCommonsStore.getSvJigyoId ?? '',
        /** 職員ID */
        shokuinId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
        /** システムコード */
        sysCd: Or31868Const.SYS_CODE,
      }

      const resData: FaceSheetPackage2InitSelectOutEntity = await ScreenRepository.select(
        'FaceSheetPackage2InitSelect',
        inputData
      )

      // 画面項目設定
      if (Or31868Const.RES_SUCCESS === resData.statusCode && resData.data) {
        setTab2Data(resData.data)
      }
      break
    }
    case Or31868Const.TAB_NUM_3: {
      // タブ3
      const inputData: FaceSheet3SelectInEntity = {
        userid: systemCommonsStore.getUserSelectSelfId() ?? '',
        getYmd: '', // 交付年月日 不明
        faceId: local.historyId,
      }

      const resData: FaceSheet3SelectSelectOutEntity = await ScreenRepository.select(
        'faceSheet3Select',
        inputData
      )

      // 画面項目設定
      if (Or31868Const.RES_SUCCESS === resData.statusCode && resData.data) {
        setTab3Data(resData.data)
      }
      break
    }
    case Or31868Const.TAB_NUM_4: {
      // タブ4
      const inputData: FaceSheetPackage4InitSelectEntity = {
        /** フェースシート履歴ID */
        faceId: local.historyId,
        /**
         * 利用者ID
         */
        uid: systemCommonsStore.getUserSelectSelfId() ?? '',
        /**
         * 事業者ID
         */
        jid: systemCommonsStore.getSvJigyoId ?? '',
        /**
         * 計画期間ID
         */
        sc1: local.planPeriodId,
        /**
         * 改訂フラグ
         */
        kaiteiFlg: local.kaiteiFlg,
      }
      const resData: FaceSheetPackage4InitSelectOutEntity = await ScreenRepository.select(
        'FaceSheetPackage4InitSelect',
        inputData
      )
      // 画面項目設定
      if (Or31868Const.RES_SUCCESS === resData.statusCode && resData.data) {
        setTab4Data(resData)
      }
      break
    }
    default:
      break
  }

  // 削除処理区分
  deleteFlag.value = Or31868Const.DEL_FLAG_NONE
  // 更新区分
  updateFlag.value = Or31868Const.UPD_FLAG_NONE
  // 履歴更新区分
  updateHisFlag.value = Or31868Const.UPD_FLAG_NONE
}

/**
 * 画面コントロール表示設定
 *
 * @param planPeriodFlag - 計画期間フラグ
 *
 * @param planPeriodInfo - 計画期間情報
 */
const setFormData = (planPeriodFlag: string, planPeriodInfo?: PlanPeriodInfo) => {
  // 期間管理フラグ
  local.flag.periodManage = planPeriodFlag
  local.flag.periodManageRegistration = true

  // 計画対象期間
  if (Or31868Const.PERIOD_MANAGE_NO === local.flag.periodManage) {
    // 期間管理フラグが「0:管理しない」
    // 計画対象期間 を非表示にする
    planPeriodShow.value = false
  } else {
    // 計画対象期間 を表示にする
    planPeriodShow.value = true

    if (planPeriodInfo && Number(planPeriodInfo.pageCnt) > 0) {
      // 計画期間が登録されている場合、期間情報.期間開始日 + " ～ " + 期間情報.期間終了日
      // 計画期間が登録されている場合、期間情報.期間ペイジ番号 + " / " + 期間情報.期間ペイジカウント
      local.planPeriodId = planPeriodInfo.sc1Id
      local.planNo = Number(planPeriodInfo.pageId)
      local.planCount = Number(planPeriodInfo.pageCnt)
      localOneway.orX0007Oneway.planTargetPeriodData = {
        planTargetPeriodId: Number(planPeriodInfo.sc1Id),
        planTargetPeriod: planPeriodInfo.kknStartYmd + t('label.wavy') + planPeriodInfo.kknEndYmd,
        currentIndex: Number(planPeriodInfo.pageId),
        totalCount: Number(planPeriodInfo.pageCnt),
      }
    } else {
      // 期間総件数 = 0（期間なし）
      // 計画対象期間-ページングを"0 / 0"で表示にする
      local.planPeriodId = '0'
      local.planNo = 0
      local.planCount = 0
      localOneway.orX0007Oneway.planTargetPeriodData = {
        planTargetPeriodId: 0,
        planTargetPeriod: '',
        currentIndex: 0,
        totalCount: 0,
      }

      // 計画期間が登録されていない
      local.flag.periodManageRegistration = false
    }
  }
}

/**
 * 履歴情報を設定
 *
 * @param historyInfo - 履歴情報
 */
const setHistoryData = (historyInfo?: HistoryInfo) => {
  // 履歴
  if (!local.flag.periodManageRegistration) {
    historyShow.value = false
  } else {
    historyShow.value = true

    localOneway.orX0008Oneway.sc1Id = local.planPeriodId
    localOneway.orX0008Oneway.officeId = systemCommonsStore.getSvJigyoId ?? ''
    localOneway.orX0008Oneway.useId = systemCommonsStore.getUserSelectSelfId() ?? ''

    // 履歴が存在の場合
    if (historyInfo) {
      // 履歴情報.履歴ペイジ番号+"/"＋ 履歴情報.履歴ペイジカウント
      local.historyId = historyInfo.faceId || '0'
      local.historyNo = Number(historyInfo.pageId)
      local.historyCount = Number(historyInfo.pageCnt)
      local.kaiteiFlg = historyInfo.kaiteiFlg || Or31868Const.KAITEI_FLAG_NEW
      local.modifiedCnt = historyInfo.modifiedCnt
      localOneway.orX0008Oneway.createData = {
        createId: historyInfo.faceId,
        createDate: historyInfo.created,
        staffId: historyInfo.faceId
          ? historyInfo.shokuId
          : (systemCommonsStore.getCurrentUser.chkShokuId ?? ''),
        staffName: historyInfo.faceId
          ? historyInfo.shokuKnj
          : (systemCommonsStore.getCurrentUser.shokuinKnj ?? ''),
        currentIndex: Number(historyInfo.pageId),
        totalCount: Number(historyInfo.pageCnt),
      }
    } else {
      // 履歴-ページング = "1 / 1"
      local.historyId = '0'
      local.historyNo = 1
      local.historyCount = 1
      local.kaiteiFlg = Or31868Const.KAITEI_FLAG_NEW
      local.modifiedCnt = ''
      localOneway.orX0008Oneway.createData = {
        createId: '0',
        createDate: systemCommonsStore.getSystemDate ?? '',
        staffId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
        staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
        currentIndex: 1,
        totalCount: 1,
      }
    }
  }

  // 作成者
  if (!local.flag.periodManageRegistration) {
    authorShow.value = false
  } else {
    authorShow.value = true

    // 履歴が存在の場合
    if (historyInfo) {
      // 履歴情報.作成者名
      localOneway.orX0009Oneway.createData = {
        createId: Number(historyInfo.faceId),
        createDate: historyInfo.created,
        staffId: historyInfo.faceId
          ? Number(historyInfo.shokuId)
          : Number(systemCommonsStore.getCurrentUser.chkShokuId ?? 0),
        staffName: historyInfo.faceId
          ? historyInfo.shokuKnj
          : (systemCommonsStore.getCurrentUser.shokuinKnj ?? ''),
        currentIndex: Number(historyInfo.pageId),
        totalCount: Number(historyInfo.pageCnt),
      }
    } else {
      // ログイン情報.職員名
      localOneway.orX0009Oneway.createData = {
        createId: 0,
        createDate: systemCommonsStore.getSystemDate,
        staffId: Number(systemCommonsStore.getCurrentUser.chkShokuId ?? 0),
        staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
        currentIndex: 1,
        totalCount: 1,
      }
    }
    OrX0009Logic.data.set({
      uniqueCpId: orX0009.value.uniqueCpId,
      value: {
        staffId: String(localOneway.orX0009Oneway.createData.staffId),
        staffName: String(localOneway.orX0009Oneway.createData.staffName),
      },
    })
    localOneway.orX0009Oneway.isDisabled = false
  }

  // 作成日
  if (!local.flag.periodManageRegistration) {
    createDateShow.value = false
  } else {
    createDateShow.value = true

    // 履歴が存在の場合
    if (historyInfo) {
      // 履歴情報.作成日
      OrX0010Logic.data.set({
        uniqueCpId: orX0010.value.uniqueCpId,
        value: { value: historyInfo.created, mo01343: {} as unknown as Mo01343Type },
        isInit: true,
      })
    } else {
      // 親画面.システム年月日
      OrX0010Logic.data.set({
        uniqueCpId: orX0010.value.uniqueCpId,
        value: {
          value: systemCommonsStore.getSystemDate ?? '',
          mo01343: {} as unknown as Mo01343Type,
        },
        isInit: true,
      })
    }
    localOneway.orX0010Oneway.isDisabled = false
  }

  // ケース番号
  if (!local.flag.periodManageRegistration) {
    caseNumShow.value = false
  } else {
    caseNumShow.value = true

    // 履歴が存在の場合
    if (historyInfo) {
      // 履歴情報.ケース番号
      local.caseNum.value = historyInfo.caseNo
      // 履歴情報.変更回数
      local.caseCount.mo00045.value = historyInfo.henkoKaisu
    } else {
      // 空白
      local.caseNum.value = ''
      // 空白
      local.caseCount.mo00045.value = ''
    }
    localOneway.mo00045Oneway.disabled = false
    localOneway.mo00038Oneway.mo00045Oneway!.disabled = false
    localOneway.mo00611CountOneway.disabled = false
  }

  // 初回作成日
  if (!local.flag.periodManageRegistration) {
    firstCreateDateShow.value = false
  } else {
    firstCreateDateShow.value = true

    // 履歴が存在の場合
    if (historyInfo) {
      local.firstTimeCreateDate.value = historyInfo.shokaiYmd
    } else {
      local.firstTimeCreateDate.value = systemCommonsStore.getSystemDate ?? ''
    }
    localOneway.mo00020FirstCreateOneway.disabled = false
  }

  // 入力フォーム
  if (!local.flag.periodManageRegistration) {
    inputBoomShow.value = false
  } else {
    inputBoomShow.value = true
  }
}

/**
 * フェースシート①のデータ設定
 *
 * @param facesheet1 - フェースシート①のデータ
 */
const setTab1Data = (facesheet1: FaceSheetPackage1InitSelectDataEntity) => {
  const or33216Data = Or33216Logic.data.get(or33216.value.uniqueCpId)
  if (facesheet1 && or33216Data) {
    localOneway.faceSheet1 = {
      ...localOneway.faceSheet1,
      ...Or33216Logic.convertApiEntityToOneWayValue(facesheet1),
    }

    const faceSheet1Data = {
      ...or33216Data,
      ...Or33216Logic.convertApiEntityToTwoWayValue(facesheet1),
    }

    Or33216Logic.data.set({
      uniqueCpId: or33216.value.uniqueCpId,
      value: faceSheet1Data,
      isInit: true,
    })
  }
}

/**
 * フェースシート②のデータ設定
 *
 * @param facesheet2 - フェースシート②のデータ
 */
const setTab2Data = (facesheet2: FaceSheetPackage2InitSelectDataEntity) => {
  const or35170Data = Or35170Logic.data.get(or35170.value.uniqueCpId)
  if (facesheet2 && or35170Data) {
    localOneway.faceSheet2 = {
      ...localOneway.faceSheet2,
      ...Or35170Logic.convertApiEntityToOneWayValue(facesheet2),
    }

    const faceSheet2Data = {
      ...or35170Data,
      ...Or35170Logic.convertApiEntityToTwoWayValue(facesheet2),
    }

    Or35170Logic.data.set({
      uniqueCpId: or35170.value.uniqueCpId,
      value: faceSheet2Data,
      isInit: true,
    })
  }
}

/**
 * フェースシート③のデータ設定
 *
 * @param facesheet3  - フェースシート①のデータ
 */
const setTab3Data = (facesheet3: FaceSheet3SelectDataEntity) => {
  const or33396Data = Or33396Logic.data.get(or33396.value.uniqueCpId)
  if (facesheet3 && or33396Data) {
    const faceSheet3Data = {
      ...or33396Data,
      ...Or33396Logic.convertApiEntityToTwoWayValue(facesheet3),
    }

    Or33396Logic.data.set({
      uniqueCpId: or33396.value.uniqueCpId,
      value: faceSheet3Data,
      isInit: true,
    })
  }
}
/**
 * フェースシート（パッケージプラン）］④画面
 *
 * @param facesheet4  - フェースシート（パッケージプラン）］④画面
 */
const setTab4Data = (facesheet4: FaceSheetPackage4InitSelectOutEntity) => {
  const or33423Data = Or33423Logic.data.get(or33423.value.uniqueCpId)
  Or33423Logic.state.set({
    uniqueCpId: or33423.value.uniqueCpId,
    state: {
      kaigoHokenshaList: facesheet4.data.kaigoHokenshaList,
      zokuList: facesheet4.data.zokuList,
    },
  })
  if (facesheet4 && or33423Data) {
    const resData = facesheet4.data.faceSheet3InfoSelDmyYoshien
    const faceSheet4Data = cloneDeep(or33423Data)
    faceSheet4Data[0].leftContent.data.SI011.value = resData.kHokenUmu
    faceSheet4Data[0].leftContent.data.SI013.value.modelValue = resData.kHokenCd
    faceSheet4Data[0].leftContent.data.SI015.value.value = resData.hHokenNo
    faceSheet4Data[0].leftContent.data.SI017.value = resData.kHokenTokuKbn
    faceSheet4Data[0].leftContent.data.SI019.value.value = resData.kHokenTokuYmd
    faceSheet4Data[0].leftContent.data.SI023.value = resData.ninteiUmu
    faceSheet4Data[0].leftContent.data.SI025.value = resData.yokaiKbn
    faceSheet4Data[0].leftContent.data.SI027.value = resData.yokaiKbn
    faceSheet4Data[0].leftContent.data.SI030.value.value = resData.ninStartYmd
    faceSheet4Data[0].leftContent.data.SI033.value.value = resData.ninEndYmd
    faceSheet4Data[0].rightContent.data.SI037.value.value = resData.nyushomaeKnj
    faceSheet4Data[1].leftContent.data.SI040.value = resData.techoUmu0
    faceSheet4Data[1].leftContent.data.SI042.value = resData.techoUmu1
    faceSheet4Data[1].leftContent.data.SI044.value.value = resData.techoShogaiKnj
    faceSheet4Data[1].leftContent.data.SI046.value = resData.techoUmu2
    faceSheet4Data[1].leftContent.data.SI048.value.value = resData.techoShogai2Knj
    faceSheet4Data[1].leftContent.data.SI050.value = resData.techoUmu3
    faceSheet4Data[1].leftContent.data.SI052.value.value = resData.techoShogai3Knj
    faceSheet4Data[1].leftContent.data.SI054.value = resData.shougaiNinteiUmu
    faceSheet4Data[1].leftContent.data.SI056.value = resData.teidoKbn
    faceSheet4Data[1].leftContent.data.SI059.value.value = resData.shogaiKnj
    faceSheet4Data[1].leftContent.data.SI062.value.value = resData.techoSYmd
    faceSheet4Data[1].leftContent.data.SI065.value.value = resData.techoEYmd
    faceSheet4Data[1].rightContent.data.SI069.value.value = resData.keizokuKnj
    faceSheet4Data[1].rightContent.data.SI071.value = resData.sotiKentoKbn
    faceSheet4Data[2].leftContent.data.SI074.value = resData.keiyakuKbn
    faceSheet4Data[2].leftContent.data.SI077.value.value = resData.keiyakuTokkiKnj
    faceSheet4Data[2].leftContent.data.SI079.value = resData.yogoKentoKbn
    faceSheet4Data[2].leftContent.data.SI082.value.value = resData.kenriHituyoKnj
    faceSheet4Data[2].leftContent.data.SI084.value = resData.yogoUmu
    faceSheet4Data[2].leftContent.data.SI087.value.value = resData.yogoTantoKnj
    faceSheet4Data[2].leftContent.data.SI090.value = resData.skoukenKentoKbn
    faceSheet4Data[2].leftContent.data.SI093.value.value = resData.seinenHituyoKnj
    faceSheet4Data[2].leftContent.data.SI095.value = resData.kokenUmu
    faceSheet4Data[2].leftContent.data.SI098.value.value = resData.kokenKnj
    faceSheet4Data[2].leftContent.data.SI101.value.value = resData.hosaKnj
    faceSheet4Data[2].leftContent.data.SI104.value.value = resData.hojoKnj
    faceSheet4Data[2].rightContent.data.SI106.value.modelValue = resData.nenkinUmu === '1'
    faceSheet4Data[2].rightContent.data.SI108.value.value = resData.nenkinKnj
    faceSheet4Data[2].rightContent.data.SI109.value.mo00045.value = resData.nenkinGaku
    faceSheet4Data[2].rightContent.data.SI111.value.modelValue = resData.syunyuSonotaUmu === '1'
    faceSheet4Data[2].rightContent.data.SI113.value.value = resData.syunyuSonotaKnj
    faceSheet4Data[2].rightContent.data.SI114.value.mo00045.value = resData.syunyuSonotaGaku
    faceSheet4Data[2].rightContent.data.SI118.value.value = resData.shotokuDankaiKnj
    faceSheet4Data[2].rightContent.data.SI120.value = resData.keizaiKbn
    faceSheet4Data[2].rightContent.data.SI123.value.value = resData.kazeijokyoKnj
    faceSheet4Data[2].rightContent.data.SI126.value.mo00045.value = resData.hiyogakuKaisouNyusho
    faceSheet4Data[2].rightContent.data.SI129.value.mo00045.value = resData.hiyogakuNyusho
    faceSheet4Data[2].rightContent.data.SI132.value.mo00045.value = resData.hiyogakuHuyo
    faceSheet4Data[2].rightContent.data.SI135.value.mo00045.value = resData.hiyogakuKaisouHuyo
    faceSheet4Data[2].rightContent.data.SI139.value.value = resData.shisanKnj
    faceSheet4Data[2].rightContent.data.SI141.value.mo00045.value = resData.kougakuGendogaku
    faceSheet4Data[3].leftContent.data.SI145.value = resData.seihoIryohojoUmu
    faceSheet4Data[3].leftContent.data.SI148.value.value = resData.seihoJisshiKnj
    faceSheet4Data[3].leftContent.data.SI150.value.value = resData.hujoKnj
    faceSheet4Data[3].leftContent.data.SI152.value.modelValue = resData.iryohujoUmu === '1'
    faceSheet4Data[3].rightContent.data.SI156.value.value = resData.sosaiJisshiKnj
    faceSheet4Data[3].rightContent.data.SI158.value = resData.sozokuUmu
    faceSheet4Data[3].rightContent.data.SI160.value.value = resData.sozoku1Knj
    faceSheet4Data[3].rightContent.data.SI162.value.modelValue = resData.sozoku1ZokuCd
    faceSheet4Data[3].rightContent.data.SI164.value.value = resData.sozoku2Knj
    faceSheet4Data[3].rightContent.data.SI166.value.modelValue = resData.sozoku2ZokuCd
    Or33423Logic.data.set({
      uniqueCpId: or33423.value.uniqueCpId,
      value: faceSheet4Data,
      isInit: true,
    })
  }
}
/**
 * AC002_「お気に入りアイコンボタン」押下
 */
const favoriteIconClick = () => {
  // TODO No.123246 設計に詳細がない
  // // お気に入りに該当機能がない場合
  // if (favorite.value) {
  //   // 「アセスメント(インターライ)」をお気に入り機能に追加する。
  // }
  // // お気に入りに該当機能がある場合
  // else {
  //   // 「アセスメント(インターライ)」をお気に入り機能から削除する。
  // }
}

/**
 * AC003_「保存ボタン」押下
 *
 * @param updateOnly - 強制更新かどうか
 */
const updateBtnClick = async (updateOnly = false) => {
  // 期間管理フラグが「1:管理する」、且つ、期間情報.期間ページカウントが「0（期間なし）」の場合
  if (!local.flag.periodManageRegistration) {
    return
  }
  // 画面入力データに変更がない場合
  if (!isEdit.value && !updateOnly) {
    // 処理終了にする
    await openWarningDialog(or21814.value.uniqueCpId, {
      dialogText: t('message.i-cmn-21800'),
    })
    return
  }

  // フラグ設定
  if (updateFlag.value !== Or31868Const.UPD_FLAG_D) {
    if (local.historyId === '0') {
      updateFlag.value = Or31868Const.UPD_FLAG_C
    } else {
      updateFlag.value = Or31868Const.UPD_FLAG_U
    }
  }
  if (updateHisFlag.value !== Or31868Const.UPD_FLAG_D) {
    if (local.historyId === '0') {
      updateHisFlag.value = Or31868Const.UPD_FLAG_C
    } else {
      updateHisFlag.value = Or31868Const.UPD_FLAG_U
    }
  }

  // 画面情報を保存する
  switch (local.mo00043.id) {
    case Or31868Const.TAB_NUM_1:
      // タブ1
      await saveTab1Data()
      break
    case Or31868Const.TAB_NUM_2:
      // タブ2
      await saveTab2Data()
      break
    case Or31868Const.TAB_NUM_3:
      // タブ3
      await saveTab3Data()
      break
    case Or31868Const.TAB_NUM_4:
      // タブ4
      await saveTab4Data()
      break
    default:
      break
  }

  if (!updateOnly) {
    if (
      updateFlag.value === Or31868Const.UPD_FLAG_C ||
      deleteFlag.value === Or31868Const.DEL_FLAG_ALL
    ) {
      local.historyId = '0'
    }
    planActFlag.value = Or31868Const.ACT_FLAG_INIT
    if (deleteFlag.value === Or31868Const.DEL_FLAG_ALL) {
      historyActFlag.value = Or31868Const.ACT_FLAG_INIT
    } else {
      historyActFlag.value = Or31868Const.ACT_FLAG_OPEN
    }

    // 情報を再取得する
    retrieveHistoryDataFlg.value = true
  }

  // 二回以上続けて新規ボタン押下したフラグをクリア
  doubleCreateFlag.value = false
}

/**
 * フェースシート①のデータ保存
 */
const saveTab1Data = async () => {
  const tab1Data = Or33216Logic.data.get(or33216.value.uniqueCpId)
  if (!tab1Data) {
    return
  }

  const face1: FaceSheet1Info = {
    /** 前住所 */
    zenAddressKnj: tab1Data.zenAddressKnj.value,
    /** 入所年月日 */
    nyushoYmd: tab1Data.nyushoYmd.value,
    /** 措置の実施機関 */
    kikanNameKnj: tab1Data.kikanNameKnj.value,
    /** 福祉事務所（町・村） */
    kikanKukuKbn: tab1Data.kikanKukuKbn,
    /** 担当ＣＷ */
    tantoCwId: tab1Data.tantoCwId,
    /** 担当CW氏名 */
    tantoCwName: tab1Data.tantoCw,
    /** 家族氏名1 */
    kazoku1Knj: tab1Data.kazoku1Knj.value,
    /** 身元引受人フラグ1 */
    mimoto1F: tab1Data.mimoto1F.modelValue ? Or33216Const.MIMOTO_FLAG_TRUE : '',
    /** 家族続柄1 */
    kazokuZoku1Cd: tab1Data.kazokuZoku1Cd.modelValue ?? '',
    /** 家族住所1 */
    kzokuAddr1Knj: tab1Data.kzokuAddr1Knj.value,
    /** 家族連絡先1 */
    kazokuRenraku1: tab1Data.kazokuRenraku1.value,
    /** 家族順位1 */
    kazokuYusen1: tab1Data.kazokuYusen1.mo00045.value,
    /** 家族氏名2 */
    kazoku2Knj: tab1Data.kazoku2Knj.value,
    /** 身元引受人フラグ2 */
    mimoto2F: tab1Data.mimoto2F.modelValue ? Or33216Const.MIMOTO_FLAG_TRUE : '',
    /** 家族続柄2 */
    kazokuZoku2Cd: tab1Data.kazokuZoku2Cd.modelValue ?? '',
    /** 家族住所2 */
    kzokuAddr2Knj: tab1Data.kzokuAddr2Knj.value,
    /** 家族連絡先2 */
    kazokuRenraku2: tab1Data.kazokuRenraku2.value,
    /** 家族順位2 */
    kazokuYusen2: tab1Data.kazokuYusen2.mo00045.value,
    /** 家族氏名3 */
    kazoku3Knj: tab1Data.kazoku3Knj.value,
    /** 身元引受人フラグ3 */
    mimoto3F: tab1Data.mimoto3F.modelValue ? Or33216Const.MIMOTO_FLAG_TRUE : '',
    /** 家族続柄3 */
    kazokuZoku3Cd: tab1Data.kazokuZoku3Cd.modelValue ?? '',
    /** 家族住所3 */
    kzokuAddr3Knj: tab1Data.kzokuAddr3Knj.value,
    /** 家族連絡先3 */
    kazokuRenraku3: tab1Data.kazokuRenraku3.value,
    /** 家族順位3 */
    kazokuYusen3: tab1Data.kazokuYusen3.mo00045.value,
    /** 家族氏名4 */
    kazoku4Knj: tab1Data.kazoku4Knj.value,
    /** 身元引受人フラグ4 */
    mimoto4F: tab1Data.mimoto4F.modelValue ? Or33216Const.MIMOTO_FLAG_TRUE : '',
    /** 家族続柄4 */
    kazokuZoku4Cd: tab1Data.kazokuZoku4Cd.modelValue ?? '',
    /** 家族住所4 */
    kzokuAddr4Knj: tab1Data.kzokuAddr4Knj.value,
    /** 家族連絡先4 */
    kazokuRenraku4: tab1Data.kazokuRenraku4.value,
    /** 家族順位4 */
    kazokuYusen4: tab1Data.kazokuYusen4.mo00045.value,
    /** 入所措置の端緒 */
    nyushoSochiKnj: tab1Data.nyushoSochiKnj.value ?? '',
    /** 生活歴 */
    seikaturekiKnj: tab1Data.seikaturekiKnj.value ?? '',
    /** 住居の状況 */
    jukyoKbn: tab1Data.jukyoKbn,
    /** 住居の状況その他 */
    jukyoSonotaKnj: tab1Data.jukyoSonotaKnj.value ?? '',
    /** 帰来先の有無 */
    kiraisakiUmu: tab1Data.kiraisakiUmu,
    /** 有りの場合 */
    kiraisakiAriKnj: tab1Data.kiraisakiAriKnj.value,
    /** 住居の状況メモ */
    jukyoMemoKnj: '',
    /** 入居初期の留意すべき事項 */
    shokiRyuiKnj: '',
    /** 環境その他の特記すべき事項 */
    tokkiKnj: '',
    /** 環境その他の特記すべき事項（虐待等） */
    gyakutaiKnj: '',
    /** 家族図縮小フラグ */
    shukushoFlg: '',
    /** 家族氏名5 */
    kazoku5Knj: tab1Data.kazoku5Knj.value,
    /** 身元引受人フラグ5 */
    mimoto5F: tab1Data.mimoto5F.modelValue ? Or33216Const.MIMOTO_FLAG_TRUE : '',
    /** 家族続柄5 */
    kazokuZoku5Cd: tab1Data.kazokuZoku5Cd.modelValue ?? '',
    /** 家族住所5 */
    kzokuAddr5Knj: tab1Data.kzokuAddr5Knj.value,
    /** 家族連絡先5 */
    kazokuRenraku5: tab1Data.kazokuRenraku5.value,
    /** 家族順位5 */
    kazokuYusen5: tab1Data.kazokuYusen5.mo00045.value,
    /** 家族氏名6 */
    kazoku6Knj: tab1Data.kazoku6Knj.value,
    /** 身元引受人フラグ6 */
    mimoto6F: tab1Data.mimoto6F.modelValue ? Or33216Const.MIMOTO_FLAG_TRUE : '',
    /** 家族続柄6 */
    kazokuZoku6Cd: tab1Data.kazokuZoku6Cd.modelValue ?? '',
    /** 家族住所6 */
    kzokuAddr6Knj: tab1Data.kzokuAddr6Knj.value,
    /** 家族連絡先6 */
    kazokuRenraku6: tab1Data.kazokuRenraku6.value,
    /** 家族順位6 */
    kazokuYusen6: tab1Data.kazokuYusen6.mo00045.value,
    /** 家族氏名7 */
    kazoku7Knj: tab1Data.kazoku7Knj.value,
    /** 身元引受人フラグ7 */
    mimoto7F: tab1Data.mimoto7F.modelValue ? Or33216Const.MIMOTO_FLAG_TRUE : '',
    /** 家族続柄7 */
    kazokuZoku7Cd: tab1Data.kazokuZoku7Cd.modelValue ?? '',
    /** 家族住所7 */
    kzokuAddr7Knj: tab1Data.kzokuAddr7Knj.value,
    /** 家族連絡先7 */
    kazokuRenraku7: tab1Data.kazokuRenraku7.value,
    /** 家族順位7 */
    kazokuYusen7: tab1Data.kazokuYusen7.mo00045.value,
    /** 家族氏名8 */
    kazoku8Knj: tab1Data.kazoku8Knj.value,
    /** 身元引受人フラグ8 */
    mimoto8F: tab1Data.mimoto8F.modelValue ? Or33216Const.MIMOTO_FLAG_TRUE : '',
    /** 家族続柄8 */
    kazokuZoku8Cd: tab1Data.kazokuZoku8Cd.modelValue ?? '',
    /** 家族住所8 */
    kzokuAddr8Knj: tab1Data.kzokuAddr8Knj.value,
    /** 家族連絡先8 */
    kazokuRenraku8: tab1Data.kazokuRenraku8.value,
    /** 家族順位8 */
    kazokuYusen8: tab1Data.kazokuYusen8.mo00045.value,
    /** 更新回数 */
    modifiedCnt: localOneway.faceSheet1.modifiedCnt,
  }

  // バックエンドAPIを呼び出す
  const inputData: FaceSheetPackage1UpdateInEntity = {
    /** 計画期間ID */
    sc1Id: local.planPeriodId,
    /** フェースシート履歴ＩＤ */
    faceId: local.historyId,
    /** 改訂フラグ */
    kaiteiFlg: local.kaiteiFlg,
    /** 共通改訂フラグ */
    kyotuKaiteiFlg: '2',
    /** 期間管理フラグ */
    kknKanriFlg: local.flag.periodManage,
    /** タブID */
    tabId: local.currentTabId,
    /** 更新区分 */
    updateKbn: updateFlag.value,
    /** 履歴更新区分 */
    historyUpdateKbn: updateHisFlag.value,
    /** 削除処理区分 */
    delKbn: deleteFlag.value,
    /** 当履歴ページ番号 */
    pageId: String(local.historyNo),
    /** 作成日 */
    createYmd: local.createDate.value,
    /** 初回作成日 */
    shokaiYmd: local.firstTimeCreateDate.value,
    /** 作成者 */
    shokuId: OrX0009Logic.data.get(orX0009.value.uniqueCpId)?.staffId ?? '',
    /** ケース番号 */
    caseNo: local.caseNum.value,
    /** 変更回数 */
    henkoKaisu: local.caseCount.mo00045.value,
    /** 法人ID */
    defHoujinId: systemCommonsStore.getHoujinId ?? '',
    /** 施設ID */
    defShisetuId: systemCommonsStore.getShisetuId ?? '',
    /** 事業所ID */
    defSvJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    /** 利用者ID */
    userid: systemCommonsStore.getUserSelectSelfId() ?? '',
    /** 種別ID */
    syubetuId: systemCommonsStore.getSyubetu ?? '',
    /** 職員ID */
    shokuinId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
    /** 更新回数 */
    modifiedCnt: localOneway.faceSheet1.modifiedCnt,
    /** フェースシート①リスト */
    face1List: [face1],
    /** フェースシート①家族構成図リスト */
    face2List: [],
  }

  await ScreenRepository.update('FaceSheetPackage1Update', inputData)
}

/**
 * フェースシート②のデータ保存
 */
const saveTab2Data = async () => {
  const tab2Data = Or35170Logic.data.get(or35170.value.uniqueCpId)
  if (!tab2Data) {
    return
  }

  const face1Data: FaceSheet2OtherInfo = {
    shokiRyuiKnj: tab2Data.shokiRyuiKnj?.value ?? '',
    modifiedCnt: localOneway.faceSheet2.face1modifiedCnt,
  }

  const face2Data: FaceSheet2Info = {
    /** 利用者ＩＤ */
    userid: systemCommonsStore.getUserSelectSelfId() ?? '',
    /** 一般的意識 */
    ippanKbn: tab2Data.ippanKbn ?? '',
    /** 一般的意識特記 */
    ippanTokkiKnj: tab2Data.ippanTokkiKnj?.value ?? '',
    /** 満足理由・施設設備 */
    manzoku1Kbn: tab2Data.manzoku1Kbn ? Or35170Const.KBN_TRUE : '',
    /** 満足理由・施設環境 */
    manzoku2Kbn: tab2Data.manzoku2Kbn ? Or35170Const.KBN_TRUE : '',
    /** 満足理由・職員対応 */
    manzoku3Kbn: tab2Data.manzoku3Kbn ? Or35170Const.KBN_TRUE : '',
    /** 満足理由・他の入所者 */
    manzoku4Kbn: tab2Data.manzoku4Kbn ? Or35170Const.KBN_TRUE : '',
    /** 満足理由・同室者 */
    manzoku5Kbn: tab2Data.manzoku5Kbn ? Or35170Const.KBN_TRUE : '',
    /** 満足理由・食事 */
    manzoku6Kbn: tab2Data.manzoku6Kbn ? Or35170Const.KBN_TRUE : '',
    /** 満足理由・介護サービス */
    manzoku7Kbn: tab2Data.manzoku7Kbn ? Or35170Const.KBN_TRUE : '',
    /** 満足理由・生活援助 */
    manzoku8Kbn: tab2Data.manzoku8Kbn ? Or35170Const.KBN_TRUE : '',
    /** 満足理由・余暇活動 */
    manzoku9Kbn: tab2Data.manzoku9Kbn ? Or35170Const.KBN_TRUE : '',
    /** 満足理由・その他 */
    manzoku10Kbn: tab2Data.manzoku10Kbn ? Or35170Const.KBN_TRUE : '',
    /** 満足理由・その他詳細 */
    manzokuSonotaKnj: tab2Data.manzokuSonotaKnj?.value ?? '',
    /** 不満理由・施設設備 */
    human1Kbn: tab2Data.human1Kbn ? Or35170Const.KBN_TRUE : '',
    /** 不満理由・施設環境 */
    human2Kbn: tab2Data.human2Kbn ? Or35170Const.KBN_TRUE : '',
    /** 不満理由・職員対応 */
    human3Kbn: tab2Data.human3Kbn ? Or35170Const.KBN_TRUE : '',
    /** 不満理由・他の入所者 */
    human4Kbn: tab2Data.human4Kbn ? Or35170Const.KBN_TRUE : '',
    /** 不満理由・同室者 */
    human5Kbn: tab2Data.human5Kbn ? Or35170Const.KBN_TRUE : '',
    /** 不満理由・食事 */
    human6Kbn: tab2Data.human6Kbn ? Or35170Const.KBN_TRUE : '',
    /** 不満理由・介護サービス */
    human7Kbn: tab2Data.human7Kbn ? Or35170Const.KBN_TRUE : '',
    /** 不満理由・生活援助 */
    human8Kbn: tab2Data.human8Kbn ? Or35170Const.KBN_TRUE : '',
    /** 不満理由・余暇活動 */
    human9Kbn: tab2Data.human9Kbn ? Or35170Const.KBN_TRUE : '',
    /** 不満理由・その他 */
    human10Kbn: tab2Data.human10Kbn ? Or35170Const.KBN_TRUE : '',
    /** 不満理由・その他詳細 */
    humanSonotaKnj: tab2Data.humanSonotaKnj?.value ?? '',
    /** 信頼できる人物 */
    shinraiJinUmu: tab2Data.shinraiJinUmu ?? '',
    /** 信頼できる人物・家族 */
    shinraiKazokuKbn: tab2Data.shinraiKazokuKbn ? Or35170Const.KBN_TRUE : '',
    /** 信頼できる人物・家族詳細 */
    shinraiKazokuKnj: tab2Data.shinraiKazokuKnj?.value ?? '',
    /** 信頼できる人物・友人知人 */
    shinraiYujinKbn: tab2Data.shinraiYujinKbn ? Or35170Const.KBN_TRUE : '',
    /** 信頼できる人物・友人知人詳細 */
    shinraiYujinKnj: tab2Data.shinraiYujinKnj?.value ?? '',
    /** 信頼できる人物・施設職員 */
    shinraiShokuKbn: tab2Data.shinraiShokuKbn ? Or35170Const.KBN_TRUE : '',
    /** 信頼できる人物・施設職員詳細 */
    shinraiShokuKnj: tab2Data.shinraiShokuKnj?.value ?? '',
    /** 信頼できる人物・その他 */
    shinraiSonotaKbn: tab2Data.shinraiSonotaKbn ? Or35170Const.KBN_TRUE : '',
    /** 信頼できる人物・その他詳細 */
    shinraiSonotaKnj: tab2Data.shinraiSonotaKnj?.value ?? '',
    /** 信頼できる人物・いない */
    shinraiNoKnj: tab2Data.shinraiNoKnj?.value ?? '',
    /** 相談できる人物 */
    sodanJinUmu: tab2Data.sodanJinUmu ?? '',
    /** 相談できる人物・家族 */
    sodanKazokuKbn: tab2Data.sodanKazokuKbn ? Or35170Const.KBN_TRUE : '',
    /** 相談できる人物・家族詳細 */
    sodanKazokuKnj: tab2Data.sodanKazokuKnj?.value ?? '',
    /** 相談できる人物・友人知人 */
    sodanYujinKbn: tab2Data.sodanYujinKbn ? Or35170Const.KBN_TRUE : '',
    /** 相談できる人物・友人知人詳細 */
    sodanYujinKnj: tab2Data.sodanYujinKnj?.value ?? '',
    /** 相談できる人物・施設職員 */
    sodanShokuKbn: tab2Data.sodanShokuKbn ? Or35170Const.KBN_TRUE : '',
    /** 相談できる人物・施設職員詳細 */
    sodanShokuKnj: tab2Data.sodanShokuKnj?.value ?? '',
    /** 相談できる人物・その他 */
    sodanSonotaKbn: tab2Data.sodanSonotaKbn ? Or35170Const.KBN_TRUE : '',
    /** 相談できる人物・その他詳細 */
    sodanSonotaKnj: tab2Data.sodanSonotaKnj?.value ?? '',
    /** 相談できる人物・いない */
    sodanNoKnj: tab2Data.sodanNoKnj?.value ?? '',
    /** 性格傾向等 */
    seikakuKnj: tab2Data.seikakuKnj?.value ?? '',
    /** 今一番関心があること */
    kanshinKnj: tab2Data.kanshinKnj?.value ?? '',
    /** 人生観の表出 */
    jinseikanKnj: '',
    /** 価値観など */
    kachikanKnj: '',
    /** 看取り */
    mitoriKnj: '',
    /** 入所後の本人の希望・意向 */
    nyuHonKiboKnj: tab2Data.nyuHonKiboKnj?.value ?? '',
    /** 入所後の家族の希望・意向 */
    nyuKazKiboKnj: tab2Data.nyuKazKiboKnj?.value ?? '',
    /** 実施機関の処遇方針 */
    hoshinKnj: tab2Data.hoshinKnj?.value ?? '',
    /** 更新回数 */
    modifiedCnt: localOneway.faceSheet2.face2modifiedCnt,
  }

  // バックエンドAPIを呼び出す
  const inputData: FaceSheetPackage2UpdateInEntity = {
    /** 計画期間ID */
    sc1Id: local.planPeriodId,
    /** フェースシート履歴ＩＤ */
    faceId: local.historyId,
    /** 改訂フラグ */
    kaiteiFlg: local.kaiteiFlg,
    /** 共通改訂フラグ */
    kyotuKaiteiFlg: '2',
    /** 期間管理フラグ */
    kknKanriFlg: local.flag.periodManage,
    /** タブID */
    tabId: local.currentTabId,
    /** 更新区分 */
    updateKbn: updateFlag.value,
    /** 履歴更新区分 */
    historyUpdateKbn: updateHisFlag.value,
    /** 削除処理区分 */
    delKbn: deleteFlag.value,
    /** 当履歴ページ番号 */
    pageId: String(local.historyNo),
    /** 作成日 */
    createYmd: local.createDate.value,
    /** 初回作成日 */
    shokaiYmd: local.firstTimeCreateDate.value,
    /** 作成者 */
    shokuId: OrX0009Logic.data.get(orX0009.value.uniqueCpId)?.staffId ?? '',
    /** ケース番号 */
    caseNo: local.caseNum.value,
    /** 変更回数 */
    henkoKaisu: local.caseCount.mo00045.value,
    /** 法人ID */
    defHoujinId: systemCommonsStore.getHoujinId ?? '',
    /** 施設ID */
    defShisetuId: systemCommonsStore.getShisetuId ?? '',
    /** 事業所ID */
    defSvJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    /** 利用者ID */
    userid: systemCommonsStore.getUserSelectSelfId() ?? '',
    /** 種別ID */
    syubetuId: systemCommonsStore.getSyubetu ?? '',
    /** 職員ID */
    shokuinId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
    /** システムコード */
    sysCd: Or31868Const.SYS_CODE,
    /** 適用事業所ＩＤリスト */
    jigyoList: systemCommonsStore.getShisetuIdList.slice(),
    /** 事業者グループ適用ID */
    groupId: systemCommonsStore.getTekiyouGroupId ?? '',
    /** 更新回数 */
    modifiedCnt: local.modifiedCnt,
    /** フェースシート①リスト */
    face1List: [face1Data],
    /** フェースシート②リスト */
    face2List: [face2Data],
  }

  await ScreenRepository.update('FaceSheetPackage2Update', inputData)
}

/**
 * フェースシート③のデータ保存
 */
const saveTab3Data = async () => {
  const tab3Data = Or33396Logic.data.get(or33396.value.uniqueCpId)
  if (!tab3Data) {
    return
  }

  const face3: FaceSheet3UpdateInfo = {
    /** フェースシート履歴ID */
    faceId: local.historyId,
    /** 国民健康保険 */
    kokuhoKnj: tab3Data.kokuhoKnj.value,
    /** 社会保険 */
    shahoKnj: tab3Data.shahoKnj.value,
    /** 医療受給者証詳細 */
    iryoJukyuKnj: tab3Data.iryoJukyuKnj.value,
    /** 健康手帳の有無 */
    kenkoTecho_umu: tab3Data.kenkoTechoUmu,
    /** 国民健康保険における住所特例手続き */
    iryoTokuKbn: tab3Data.iryoTokuKbn,
    /** 国民健康保険における住所特例手続き完了年月日 */
    iryoTokuYmd: tab3Data.iryoTokuYmd.value,
    /** 利用者ID */
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    /** 国民健康保険（世帯主） */
    kokuhoSetainushiKnj: tab3Data.kokuhoSetainushiKnj.value,
    /** ①病名 */
    sick1Knj: tab3Data.sick1Knj.value,
    /** ②病名 */
    sick2Knj: tab3Data.sick2Knj.value,
    /** ③病名 */
    sick3Knj: tab3Data.sick3Knj.value,
    /** ④病名 */
    sick4Knj: tab3Data.sick4Knj.value,
    /** ⑤病名 */
    sick5Knj: tab3Data.sick5Knj.value,
    /** ⑥病名 */
    sick6Knj: tab3Data.sick6Knj.value,
    /** ⑦病名 */
    sick7Knj: tab3Data.sick7Knj.value,
    /** ⑧病名 */
    sick8Knj: tab3Data.sick8Knj.value,
    /** ⑨病名 */
    sick9Knj: tab3Data.sick9Knj.value,
    /** ⑩病名 */
    sick10Knj: tab3Data.sick10Knj.value,
    /** ①発病年月日 */
    sick1Ymd: tab3Data.sick1Ymd.value,
    /** ②発病年月日 */
    sick2Ymd: tab3Data.sick2Ymd.value,
    /** ③発病年月日 */
    sick3Ymd: tab3Data.sick3Ymd.value,
    /** ④発病年月日 */
    sick4Ymd: tab3Data.sick4Ymd.value,
    /** ⑤発病年月日 */
    sick5Ymd: tab3Data.sick5Ymd.value,
    /** ⑥発病年月日 */
    sick6Ymd: tab3Data.sick6Ymd.value,
    /** ⑦発病年月日 */
    sick7Ymd: tab3Data.sick7Ymd.value,
    /** ⑧発病年月日 */
    sick8Ymd: tab3Data.sick8Ymd.value,
    /** ⑨発病年月日 */
    sick9Ymd: tab3Data.sick9Ymd.value,
    /** ⑩発病年月日 */
    sick10Ymd: tab3Data.sick10Ymd.value,
    /** ①治癒継続フラグ1治癒2継続 */
    sick1Flg: tab3Data.sick1Flg,
    /** ②治癒継続フラグ1治癒2継続 */
    sick2Flg: tab3Data.sick2Flg,
    /** ③治癒継続フラグ1治癒2継続 */
    sick3Flg: tab3Data.sick3Flg,
    /** ④治癒継続フラグ1治癒2継続 */
    sick4Flg: tab3Data.sick4Flg,
    /** ⑤治癒継続フラグ1治癒2継続 */
    sick5Flg: tab3Data.sick5Flg,
    /** ⑥治癒継続フラグ1治癒2継続 */
    sick6Flg: tab3Data.sick6Flg,
    /** ⑦治癒継続フラグ1治癒2継続 */
    sick7Flg: tab3Data.sick7Flg,
    /** ⑧治癒継続フラグ1治癒2継続 */
    sick8Flg: tab3Data.sick8Flg,
    /** ⑨治癒継続フラグ1治癒2継続 */
    sick9Flg: tab3Data.sick9Flg,
    /** ⑩治癒継続フラグ1治癒2継続 */
    sick10Flg: tab3Data.sick10Flg,
    /** ①主たる傷病名 */
    nyuin1Knj: tab3Data.nyuin1Knj.value,
    /** ②主たる傷病名 */
    nyuin2Knj: tab3Data.nyuin2Knj.value,
    /** ③主たる傷病名 */
    nyuin3Knj: tab3Data.nyuin3Knj.value,
    /** ④主たる傷病名 */
    nyuin4Knj: tab3Data.nyuin4Knj.value,
    /** ⑤主たる傷病名 */
    nyuin5Knj: tab3Data.nyuin5Knj.value,
    /** ①主たる傷病(開始日) */
    nyuin1Symd: tab3Data.nyuin1Symd.value,
    /** ②主たる傷病(開始日) */
    nyuin2Symd: tab3Data.nyuin2Symd.value,
    /** ③主たる傷病(開始日) */
    nyuin3Symd: tab3Data.nyuin3Symd.value,
    /** ④主たる傷病(開始日) */
    nyuin4Symd: tab3Data.nyuin4Symd.value,
    /** ⑤主たる傷病(開始日) */
    nyuin5Symd: tab3Data.nyuin5Symd.value,
    /** ①主たる傷病(終了日) */
    nyuin1Eymd: tab3Data.nyuin1Eymd.value,
    /** ②主たる傷病(終了日) */
    nyuin2Eymd: tab3Data.nyuin2Eymd.value,
    /** ③主たる傷病(終了日) */
    nyuin3Eymd: tab3Data.nyuin3Eymd.value,
    /** ④主たる傷病(終了日) */
    nyuin4Eymd: tab3Data.nyuin4Eymd.value,
    /** ⑤主たる傷病(終了日) */
    nyuin5Eymd: tab3Data.nyuin5Eymd.value,
    /** 服薬状況 */
    fukuyakuKnj: tab3Data.fukuyakuKnj.value ?? '',
    /** 感染症アレルギー情報 */
    kansenKnj: tab3Data.kansenKnj.value ?? '',
    /** 属託医医療機関等の名称 */
    zokutakuiKnj: tab3Data.zokutakuiKnj.value ?? '',
    /** 国民健康保険（被保険者番号） */
    kokuhoHiHokenjaNo: tab3Data.kokuhoHiHokenjaNo.value,
    /** 後期高齢者医療保険（被保険者番号） */
    koukiHiHokenjaNo: tab3Data.koukiHiHokenjaNo.value,
    /** 社会保険（被保険者番号） */
    shahoHiHokenjaNo: tab3Data.shahoHiHokenjaNo.value,
    /** 頓服薬状況 */
    tonpukuKnj: tab3Data.tonpukuKnj.value ?? '',
    /** 社会保険（被保険者） */
    shahoHiHokenjaKnj: tab3Data.shahoHiHokenjaKnj.value,
  }

  // バックエンドAPIを呼び出す
  const inputData: FaceSheet3UpdateInEntity = {
    /** フェースシート履歴ＩＤ */
    faceId: local.historyId,
    /** 利用者ID */
    userid: systemCommonsStore.getUserSelectSelfId() ?? '',
    /** 更新区分 */
    updateKbn: updateFlag.value,
    /** 更新回数 */
    updateCnt: localOneway.faceSheet3.updateCnt,
    /** フェースシート③更新情報 */
    Gui00637FaceSheet3InfoSaveOutList: [face3],
  }

  await ScreenRepository.update('faceSheet3Update', inputData)
}
/**
 * フェースシート④画面
 */
const saveTab4Data = async () => {
  const valid = or33423CP.value?.validate()
  if (!valid) return
  const tab4Data = Or33423Logic.data.get(or33423.value.uniqueCpId)
  if (!tab4Data) {
    return
  }
  const FaceSheet3InfoSelDmyYoshien: FaceSheet3InfoSelDmyYoshien = {
    /** ダミー国民健康保険における住所特例手続き完了年月日 */
    dmyIryoTokuYmd: '',
    /** ダミー介護保険における住所特例手続き完了年月日 */
    dmyKHokenTokuYmd: '',
    /** ダミー認定有効期間（開始） */
    dmyNinStartYmd: '',
    /** ダミー認定有効期間（終了） */
    dmyNinEndYmd: '',
    /** フェースシート履歴ＩＤ */
    faceId: local.historyId,
    kHokenUmu: tab4Data[0].leftContent.data.SI011.value,
    kHokenCd: tab4Data[0].leftContent.data.SI013.value.modelValue!,
    hHokenNo: tab4Data[0].leftContent.data.SI015.value.value,
    kHokenTokuKbn: tab4Data[0].leftContent.data.SI017.value,
    kHokenTokuYmd: tab4Data[0].leftContent.data.SI019.value.value,
    ninteiUmu: tab4Data[0].leftContent.data.SI023.value,
    yokaiKbn: tab4Data[0].leftContent.data.SI025.value,
    ninStartYmd: tab4Data[0].leftContent.data.SI030.value.value,
    ninEndYmd: tab4Data[0].leftContent.data.SI033.value.value,
    nyushomaeKnj: tab4Data[0].rightContent.data.SI037.value.value!,
    techoUmu0: tab4Data[1].leftContent.data.SI040.value,
    techoUmu1: tab4Data[1].leftContent.data.SI042.value,
    techoShogaiKnj: tab4Data[1].leftContent.data.SI044.value.value,
    techoUmu2: tab4Data[1].leftContent.data.SI046.value,
    techoShogai2Knj: tab4Data[1].leftContent.data.SI048.value.value,
    techoUmu3: tab4Data[1].leftContent.data.SI050.value,
    techoShogai3Knj: tab4Data[1].leftContent.data.SI052.value.value,
    shougaiNinteiUmu: tab4Data[1].leftContent.data.SI054.value,
    teidoKbn: tab4Data[1].leftContent.data.SI056.value,
    shogaiKnj: tab4Data[1].leftContent.data.SI059.value.value,
    techoSYmd: tab4Data[1].leftContent.data.SI062.value.value,
    techoEYmd: tab4Data[1].leftContent.data.SI065.value.value,
    keizokuKnj: tab4Data[1].rightContent.data.SI069.value.value!,
    sotiKentoKbn: tab4Data[1].rightContent.data.SI071.value,
    keiyakuKbn: tab4Data[2].leftContent.data.SI074.value,
    keiyakuTokkiKnj: tab4Data[2].leftContent.data.SI077.value.value!,
    yogoKentoKbn: tab4Data[2].leftContent.data.SI079.value,
    kenriHituyoKnj: tab4Data[2].leftContent.data.SI082.value.value!,
    yogoUmu: tab4Data[2].leftContent.data.SI084.value,
    yogoTantoKnj: tab4Data[2].leftContent.data.SI087.value.value,
    skoukenKentoKbn: tab4Data[2].leftContent.data.SI090.value,
    seinenHituyoKnj: tab4Data[2].leftContent.data.SI093.value.value!,
    kokenUmu: tab4Data[2].leftContent.data.SI095.value,
    kokenKnj: tab4Data[2].leftContent.data.SI098.value.value,
    hosaKnj: tab4Data[2].leftContent.data.SI101.value.value,
    hojoKnj: tab4Data[2].leftContent.data.SI104.value.value,
    nenkinUmu: tab4Data[2].rightContent.data.SI106.value.modelValue ? '1' : '0',
    nenkinKnj: tab4Data[2].rightContent.data.SI108.value.value,
    nenkinGaku: tab4Data[2].rightContent.data.SI109.value.mo00045.value,
    syunyuSonotaUmu: tab4Data[2].rightContent.data.SI111.value.modelValue ? '1' : '0',
    syunyuSonotaKnj: tab4Data[2].rightContent.data.SI113.value.value,
    syunyuSonotaGaku: tab4Data[2].rightContent.data.SI114.value.mo00045.value,
    shotokuDankaiKnj: tab4Data[2].rightContent.data.SI118.value.value!,
    keizaiKbn: tab4Data[2].rightContent.data.SI120.value,
    kazeijokyoKnj: tab4Data[2].rightContent.data.SI123.value.value,
    hiyogakuKaisouNyusho: tab4Data[2].rightContent.data.SI126.value.mo00045.value,
    hiyogakuNyusho: tab4Data[2].rightContent.data.SI129.value.mo00045.value,
    hiyogakuHuyo: tab4Data[2].rightContent.data.SI132.value.mo00045.value,
    hiyogakuKaisouHuyo: tab4Data[2].rightContent.data.SI135.value.mo00045.value,
    shisanKnj: tab4Data[2].rightContent.data.SI139.value.value,
    kougakuGendogaku: tab4Data[2].rightContent.data.SI141.value.mo00045.value,
    seihoIryohojoUmu: tab4Data[3].leftContent.data.SI145.value,
    seihoJisshiKnj: tab4Data[3].leftContent.data.SI148.value.value,
    hujoKnj: tab4Data[3].leftContent.data.SI150.value.value,
    iryohujoUmu: tab4Data[3].leftContent.data.SI152.value.modelValue ? '1' : '0',
    sosaiJisshiKnj: tab4Data[3].rightContent.data.SI156.value.value,
    sozokuUmu: tab4Data[3].rightContent.data.SI158.value,
    sozoku1Knj: tab4Data[3].rightContent.data.SI160.value.value,
    sozoku1ZokuCd: tab4Data[3].rightContent.data.SI162.value.modelValue!,
    sozoku2Knj: tab4Data[3].rightContent.data.SI164.value.value,
    sozoku2ZokuCd: tab4Data[3].rightContent.data.SI166.value.modelValue!,
    /** 利用者ID */
    userid: '',
    /** ダミー障害手帳の有効期間(開始日) */
    dmyTechoSYmd: '',
    /** ダミー障害手帳の有効期間(終了日) */
    dmyTechoEYmd: '',
    /** ダミー要支援 */
    dmyYoshien: '',
    /** ダミー要介護 */
    dmyYokaigo: '',
    /** 更新回数 */
    modifiedCnt: '',
  }
  const CpnTucSypFace1: CpnTucSypFace1 = {
    /** フェースシート履歴ＩＤ */
    faceId: local.historyId,
    /** 計画期間ID */
    sc1Id: local.planPeriodId,
    /** 法人ID */
    houjinId: '',
    /** 施設ID */
    shisetuId: '',
    /** 事業者ID */
    svJigyoId: '',
    /** 利用者ＩＤ */
    userid: '',
    /** 作成日 */
    createYmd: '',
    /** 作成者 */
    shokuId: '',
    /** ケース番号 */
    caseNo: '',
    /** 変更回数 */
    henkoKaisu: '',
    /** フェースシート①作成 */
    face1Flg: '',
    /** フェースシート②作成 */
    face2Flg: '',
    /** フェースシート③作成 */
    face3Flg: '',
    /** 改訂フラグ */
    kaiteiFlg: '',
    /** 初回作成日 */
    shokaiYmd: '',
    /** フェースシート④作成 */
    face4Flg: '',
    /** 更新回数 */
    modifiedCnt: '',
  }
  const inputData: FaceSheetPackage4UpdateInEntity = {
    /** 計画期間ID */
    sc1Id: '',

    /** フェースシート履歴ＩＤ */
    faceId: '',

    /** 期間管理フラグ */
    kknKanriFlg: '',

    /** タブID */
    tabId: local.currentTabId,

    /** 更新区分 */
    updateKbn: updateFlag.value,

    /** 履歴更新区分 */
    updateRirekiKbn: '',

    /** 削除処理区分 */
    delKbn: '',

    /** 当履歴ページ番号 */
    pageId: '',

    /** 作成日 */
    createYmd: '',

    /** 初回作成日 */
    shokaiYmd: '',

    /** 作成者 */
    shokuId: '',

    /** ケース番号 */
    caseNo: '',

    /** 変更回数 */
    henkoKaisu: '',

    /** 法人ID */
    defHoujinId: '',

    /** 施設ID */
    defShisetuId: '',

    /** 事業所ID */
    defSvJigyoId: '',

    /** 利用者ID */
    userid: '',

    /** 種別ID */
    syubetuId: '',

    /** 職員ID */
    shokuinId: '',

    /** システムコード */
    sysCd: '',

    /** 適用事業所ＩＤリスト */
    jigyoIdList: [],
    /** 事業者グループ適用ID */
    groupId: '',

    /** フェースシート履歴の更新回数 */
    faceRirekiModifiedCnt: '',

    /** フェースシート①更新回数 */
    face1ModifiedCnt: '',

    /** フェースシート②更新回数 */
    face2ModifiedCnt: '',

    /** フェースシート③更新回数 */
    face3ModifiedCnt: '',

    /** フェースシート④更新回数 */
    face4ModifiedCnt: '',
    CpnTucSypFace1,
    FaceSheet3InfoSelDmyYoshien,
  }
  await ScreenRepository.update('FaceSheetPackage4Update', inputData)
}
/**
 * AC004_「新規ボタン」押下
 */
const addBtnClick = async () => {
  // 画面.履歴更新区分＝「D：削除」の場合
  if (updateFlag.value === Or31868Const.UPD_FLAG_D) {
    return
  }

  // 期間管理フラグが「1:管理する」、且つ、期間情報.期間ページカウントが「0（期間なし）」の場合
  if (!local.flag.periodManageRegistration) {
    await openWarningDialog(or21814.value.uniqueCpId, {
      dialogText: t('message.i-cmn-11300'),
    })
    OrX0115Logic.state.set({
      uniqueCpId: orX0115.value.uniqueCpId,
      state: { isOpen: true },
    })
    return
  }

  // 二回目新規ボタン押下する場合
  if (doubleCreateFlag.value) {
    await openWarningDialog(or21814.value.uniqueCpId, {
      dialogText: t('message.i-cmn-11265', [t('label.face-sheet')]),
    })
    return
  }

  // 画面入力データに変更がある場合
  if (isEdit.value) {
    const result = await openWarningDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    if (result === Or31868Const.DIALOG_RESULT_YES) {
      await updateBtnClick(true)
    } else if (result === Or31868Const.DIALOG_RESULT_CANCEL) {
      return
    }
  }

  local.historyId = '0'
  planActFlag.value = Or31868Const.ACT_FLAG_INIT
  historyActFlag.value = Or31868Const.ACT_FLAG_NEW

  // 情報を再取得する
  retrieveHistoryDataFlg.value = true

  // 二回以上続けて新規ボタン押下したフラグを設定
  doubleCreateFlag.value = true
}

/**
 * AC005_「複写ボタン」押下
 */
const copyBtnClick = () => {
  // 期間管理フラグが「1:管理する」、且つ、期間情報.期間ページカウントが「0（期間なし）」の場合
  if (!local.flag.periodManageRegistration) {
    return
  }
  // 画面.履歴更新区分＝「D：削除」の場合
  if (updateFlag.value === Or31868Const.UPD_FLAG_D) {
    return
  }

  // GUI00639 フェースシート複写画面をポップアップで起動する
  Or35478Logic.state.set({
    uniqueCpId: or35478.value.uniqueCpId,
    state: {
      isOpen: true,
      inParam: {
        /** タブID */
        tabId: local.currentTabId,
        /** 利用者ID */
        userId: systemCommonsStore.getUserSelectSelfId() ?? '',
        /** 事業所ID */
        svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
        /** 法人ID */
        hojinId: systemCommonsStore.getHoujinId ?? '',
        /** 施設ID */
        shisetuId: systemCommonsStore.getShisetuId ?? '',
        /** 種別ID */
        syubetuId: systemCommonsStore.getSyubetu ?? '',
        /** 計画期間ID */
        sc1Id: local.planPeriodId,
        /** 適用事業所IDリスト */
        jigyoIdList: systemCommonsStore.getSvJigyoIdList.slice() ?? [],
        /** 期間管理フラグ */
        kikanFlg: Or31868Const.PERIOD_MANAGE_NO === local.flag.periodManage ? '0' : '1',
        /** フェースシート履歴ID */
        face1Id: local.historyId,
        /** 履歴更新回数 */
        modifiedCnt: local.modifiedCnt,
        /** ユーザログイン情報.職員ID */
        staffId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
        /** 事業者グループ適用ID */
        tekiyouGroupId: systemCommonsStore.getTekiyouGroupId ?? '',
        /** 改訂フラグ */
        kaiteiFlg: local.kaiteiFlg,
        /** 期間処理区分 */
        planActFlag: Or31868Const.ACT_FLAG_INIT,
        /** 履歴処理区分 */
        historyActFlag: Or31868Const.ACT_FLAG_OPEN,
      },
    },
  })
}

/**
 * 複写ダイアログ閉じる後の処理
 */
watch(
  () => Or35478Logic.state.get(or35478.value.uniqueCpId)?.isOpen,
  (newValue) => {
    if (newValue || newValue === undefined) {
      return
    }

    const event = Or35478Logic.event.get(or35478.value.uniqueCpId)
    if (event === undefined) {
      return
    }

    if (event.singleConfirmFlg) {
      // 単タブの複写

      switch (local.currentTabId) {
        // タブ1
        case Or31868Const.TAB_NUM_1: {
          const data = Or35478Logic.state.get(or35478.value.uniqueCpId)?.faceSheet1Data
          if (data) {
            Or33216Logic.data.set({
              uniqueCpId: or33216.value.uniqueCpId,
              value: data,
            })
          }
          break
        }
        // タブ2
        case Or31868Const.TAB_NUM_2: {
          const data = Or35478Logic.state.get(or35478.value.uniqueCpId)?.faceSheet2Data
          if (data) {
            Or35170Logic.data.set({
              uniqueCpId: or35170.value.uniqueCpId,
              value: data,
            })
          }
          break
        }
        // タブ3
        case Or31868Const.TAB_NUM_3: {
          const data = Or35478Logic.state.get(or35478.value.uniqueCpId)?.faceSheet3Data
          if (data) {
            Or33396Logic.data.set({
              uniqueCpId: or33396.value.uniqueCpId,
              value: data,
            })
          }
          break
        }
        // タブ4
        case Or31868Const.TAB_NUM_4: {
          const data = Or35478Logic.state.get(or35478.value.uniqueCpId)?.faceSheet4Data
          if (data) {
            Or33423Logic.data.set({
              uniqueCpId: or33423.value.uniqueCpId,
              value: data,
            })
          }
          break
        }
        default:
          break
      }
    } else if (event.multiConfirmFlg) {
      // 複数タブの複写
      const data = Or35478Logic.state.get(or35478.value.uniqueCpId)?.outParam
      local.planPeriodId = data?.sc1Id ?? '0'
      local.historyId = data?.faceId ?? '0'

      // 期間処理区分：""
      planActFlag.value = Or31868Const.ACT_FLAG_INIT
      // 履歴処理区分："open"
      historyActFlag.value = Or31868Const.ACT_FLAG_OPEN

      // データ再取得フラグ(履歴)を設定
      retrieveHistoryDataFlg.value = true
    }
  }
)

/**
 * AC006_「印刷設定アイコンボタン」押下
 */
const printSettingIconClick = async () => {
  // 画面.履歴更新区分＝「D：削除」の場合
  if (updateFlag.value === Or31868Const.UPD_FLAG_D) {
    return
  }

  // 画面入力データに変更がある場合
  if (isEdit.value) {
    const result = await openWarningDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    if (result === Or31868Const.DIALOG_RESULT_YES) {
      await updateBtnClick(true)
    } else if (result === Or31868Const.DIALOG_RESULT_CANCEL) {
      return
    }
  }

  // GUI00651 印刷設定画面をポップアップで起動する。
  // セクション名："フェースシート"
  // 利用者リスト：親画面.ユーザリストDW
  // 利用者ID：親画面.利用者ID
  // 履歴ID：画面.フェースシート履歴ID
  // 選択帳票番号：-
  // 50音行番号：親画面.50音行番号
  // 50音母音：親画面.50音母音
  // 計画期間管理フラグ：画面.期間管理フラグ
  or28779Oneway.value.sectionName = t('label.face-sheet')
  or28779Oneway.value.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  or28779Oneway.value.historyId = local.historyId
  or28779Oneway.value.selectedLedgerNo = '-'
  or28779Oneway.value.gojuuonKana = Or00094Logic.data.get(or00094.value.uniqueCpId)
    ?.selectValueArray ?? [Or31868Const.GOJUUON_ALL]
  or28779Oneway.value.planPeriodManagementFlg =
    Or31868Const.PERIOD_MANAGE_NO === local.flag.periodManage ? '0' : '1'

  or28779Oneway.value.systemCode = systemCommonsStore.getSystemCode ?? '71101' // 印刷疎通用
  or28779Oneway.value.systemNameShort = systemCommonsStore.getSystemAbbreviation ?? '3GK' // 印刷疎通用
  or28779Oneway.value.corporationId = systemCommonsStore.getHoujinId ?? ''
  or28779Oneway.value.facilityId = systemCommonsStore.getShisetuId ?? ''
  or28779Oneway.value.officeId = systemCommonsStore.getSvJigyoId ?? ''
  or28779Oneway.value.staffId = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
  or28779Oneway.value.processYmd = systemCommonsStore.getSystemDate ?? ''

  Or28779Logic.state.set({
    uniqueCpId: or28779.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * AC011_「削除」押下
 */
const deleteClick = () => {
  // 期間管理フラグが「1:管理する」、且つ、期間情報.期間ページカウントが「0（期間なし）」の場合
  if (!local.flag.periodManageRegistration) {
    return
  }
  // 画面.履歴更新区分＝「D：削除」の場合
  if (updateFlag.value === Or31868Const.UPD_FLAG_D) {
    return
  }

  // 作成日
  const createDate = OrX0010Logic.data.get(orX0010.value.uniqueCpId)?.value
  // 当該タブタイトル
  const tabName = localOneway.mo00043OnewayType.tabItems.find(
    (tabItem) => tabItem.id === local.mo00043.id
  )

  orX0001Oneway.value.createYmd = createDate ?? ''

  orX0001Oneway.value.selectTabName = tabName?.title ?? ''

  // OrX0001のダイアログ開閉状態を更新する
  OrX0001Logic.state.set({
    uniqueCpId: orX0001.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC022_タブ選択
 */
const tabsClick = async () => {
  if (local.mo00043.id === local.currentTabId) {
    return
  }

  // 画面入力データに変更がある場合
  const newId = local.mo00043.id
  if (isEdit.value) {
    local.mo00043.id = local.currentTabId
    const result = await openWarningDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    if (result === Or31868Const.DIALOG_RESULT_YES) {
      await updateBtnClick(true)
    } else if (result === Or31868Const.DIALOG_RESULT_CANCEL) {
      return
    }
    local.mo00043.id = newId
  }

  local.currentTabId = newId

  // 期間処理区分
  planActFlag.value = Or31868Const.ACT_FLAG_INIT
  // 履歴処理区分
  historyActFlag.value = Or31868Const.ACT_FLAG_INIT

  //データ再取得フラグ設定
  retrieveTabDataFlg.value = true
}

/**
 * 確認ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openWarningDialog = (uniqueCpId: string, state: Or21814OnewayType) => {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      dialogTitle: t('label.top-btn-title'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      ...state,
      isOpen: true,
    },
  })
  return new Promise<string>((resolve) => {
    watch(
      () => Or21814Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(uniqueCpId)

        let result = Or31868Const.DIALOG_RESULT_CANCEL
        if (event?.firstBtnClickFlg) {
          result = Or31868Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or31868Const.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
</script>

<template>
  <c-v-sheet class="view d-flex flex-column h-100">
    <!-- 操作ボタンエリア -->
    <g-base-or11871 v-bind="or11871">
      <template #createItems>
        <c-v-list-item
          :title="t('btn.copy')"
          @click="copyBtnClick"
        />
      </template>
      <template #optionMenuItems>
        <c-v-list-item
          :title="
            t('label.interRAI-method-care-assessment-table-A-basic-info-section-title-menyu-delete')
          "
          prepend-icon="delete"
          @click="deleteClick"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.delete-data')"
          />
        </c-v-list-item>
      </template>
    </g-base-or11871>

    <c-v-row
      no-gutters
      class="main-Content d-flex flex-0-1 h-100 overflow-hidden"
    >
      <!-- ナビゲーションエリア -->
      <c-v-col
        cols="2"
        class="hidden-scroll h-100 pa-0"
      >
        <!-- 利用者選択一覧 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>

      <!-- コンテンツエリア -->
      <c-v-col class="main-right d-flex flex-column overflow-hidden h-100 px-2">
        <!-- 上段 -->
        <c-v-row
          no-gutters
          class="top pt-4"
        >
          <c-v-col>
            <c-v-row
              no-gutters
              class="pl-4"
            >
              <!-- 事業所 -->
              <g-base-or41179 v-bind="or41179" />
            </c-v-row>
            <c-v-row
              no-gutters
              class="pl-4 pt-4"
            >
              <c-v-col cols="auto">
                <c-v-row no-gutters>
                  <!-- 計画対象期間 -->
                  <c-v-col
                    v-if="planPeriodShow"
                    cols="auto"
                  >
                    <g-custom-or-x0007
                      v-bind="orX0007"
                      :oneway-model-value="localOneway.orX0007Oneway"
                      :unique-cp-id="orX0007.uniqueCpId"
                      :parent-method="() => updateBtnClick(true)"
                    />
                  </c-v-col>
                  <!-- 履歴 -->
                  <c-v-col
                    v-if="historyShow"
                    cols="auto"
                    :class="{
                      'pl-4': planPeriodShow,
                    }"
                  >
                    <g-custom-or-x0008
                      v-bind="orX0008"
                      :oneway-model-value="localOneway.orX0008Oneway"
                      :unique-cp-id="orX0008.uniqueCpId"
                      :is-edit="isEdit"
                      :parent-method="() => updateBtnClick(true)"
                    />
                  </c-v-col>
                </c-v-row>
              </c-v-col>
              <c-v-col>
                <c-v-row no-gutters>
                  <!-- 作成者 -->
                  <c-v-col
                    v-if="authorShow"
                    cols="auto"
                    class="pl-4 d-flex align-center"
                  >
                    <g-custom-or-x0009
                      v-bind="orX0009"
                      :oneway-model-value="localOneway.orX0009Oneway"
                      :unique-cp-id="orX0009.uniqueCpId"
                    />
                  </c-v-col>
                  <!-- 作成日 -->
                  <c-v-col
                    v-if="createDateShow"
                    cols="auto"
                    class="pl-4"
                  >
                    <g-custom-or-x0010
                      v-bind="orX0010"
                      :oneway-model-value="localOneway.orX0010Oneway"
                      :unique-cp-id="orX0010.uniqueCpId"
                    />
                  </c-v-col>
                  <v-responsive width="100%"></v-responsive>
                  <!-- ケース番号 -->
                  <c-v-col
                    v-if="caseNumShow"
                    cols="auto"
                    class="pl-4 pt-4 d-flex align-center"
                  >
                    <base-mo00045
                      v-model="local.caseNum"
                      :oneway-model-value="localOneway.mo00045Oneway"
                      class="input-text-align-right input-bg-white"
                    />
                    <base-mo00038
                      v-model="local.caseCount"
                      :oneway-model-value="localOneway.mo00038Oneway"
                      class="input-bg-white"
                    />
                    <base-mo00611 :oneway-model-value="localOneway.mo00611CountOneway" />
                  </c-v-col>
                  <!-- 初回作成日 -->
                  <c-v-col
                    v-if="firstCreateDateShow"
                    cols="auto"
                    class="pl-4 pt-4"
                  >
                    <base-mo00020
                      v-model="local.firstTimeCreateDate"
                      :oneway-model-value="localOneway.mo00020FirstCreateOneway"
                      class="input-bg-white"
                    />
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>

            <!-- コンテンツエリアタブ -->
            <c-v-row
              no-gutters
              class="mt-4"
            >
              <c-v-col class="tabItems">
                <base-mo00043
                  v-model="local.mo00043"
                  :oneway-model-value="localOneway.mo00043OnewayType"
                  @update:model-value="tabsClick"
                >
                </base-mo00043>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <!-- 中段 -->
        <c-v-row
          no-gutters
          class="middleContent flex-1-1 h-100"
        >
          <c-v-window
            v-if="inputBoomShow"
            v-model="local.currentTabId"
            class="h-100 w-100"
          >
            <c-v-window-item
              value="1"
              class="h-100 overflow-y-auto pa-2 px-14"
            >
              <!-- フェースシート① -->
              <g-custom-or33216
                v-bind="or33216"
                :oneway-model-value="localOneway.faceSheet1"
              />
            </c-v-window-item>
            <c-v-window-item
              value="2"
              class="h-100 overflow-y-auto pa-2 px-14"
            >
              <!-- フェースシート② -->
              <g-custom-or35170
                v-bind="or35170"
                :oneway-model-value="localOneway.faceSheet2"
              />
            </c-v-window-item>
            <c-v-window-item
              value="3"
              class="h-100 overflow-y-auto pa-2 px-14"
            >
              <!-- フェースシート③ -->
              <g-custom-or33396
                v-bind="or33396"
                :oneway-model-value="localOneway.faceSheet3"
              />
            </c-v-window-item>
            <c-v-window-item
              value="4"
              style="display: flex; justify-content: center"
              class="h-100 overflow-auto pa-2 px-14"
            >
              <!-- フェースシート④-->
              <g-custom-or33423
                ref="or33423CP"
                v-bind="or33423"
                :oneway-model-value="localOneway.faceSheet4"
              />
            </c-v-window-item>
          </c-v-window>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <c-v-row no-gutters>
      <c-v-col>
        <g-base-or00051 />
      </c-v-col>
    </c-v-row>
  </c-v-sheet>

  <!-- インフォメーションダイアログ -->
  <g-base-or21814
    v-if="showOr21814"
    v-bind="or21814"
  />

  <!-- 削除確認画面 -->
  <g-custom-or-x0001
    v-if="showOrX0001"
    v-bind="orX0001"
    v-model="orX0001Type"
    :oneway-model-value="orX0001Oneway"
  />

  <!-- GUI00639_［フェースシート複写］画面 -->
  <g-custom-or35478
    v-if="showOr35478"
    v-bind="or35478"
  />

  <!-- GUI00651_印刷設定 -->
  <g-custom-or28779
    v-if="showOr28779"
    v-bind="or28779"
    v-model="or28779Data"
    :oneway-model-value="or28779Oneway"
  />
</template>

<style scoped lang="scss">
.view {
  background-color: transparent;
}

.main-Content {
  .main-left {
    max-width: 20%;
  }

  .main-right {
    .middleContent {
      min-height: 0;
    }

    .top {
      flex: 0 1 auto;

      :deep(.v-sheet) {
        background-color: transparent !important;
      }
    }
  }
}

.tabItems {
  border-top: thin solid rgb(var(--v-theme-form));
}

.input-text-align-right {
  :deep(input) {
    text-align: right;
  }
}

.input-bg-white {
  :deep(.v-input__control) {
    background-color: white;
  }
}
</style>
