<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  watch,
} from '#imports'
import { Or27210Logic } from '~/components/custom-components/organisms/Or27210/Or27210.logic'
import { Or27210Const } from '~/components/custom-components/organisms/Or27210/Or27210.constants'
import type {
  notebookInfoSelectType,
  Or27210Type,
} from '~/types/cmn/business/components/Or27210Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00809'
// ルーティング
const routing = 'GUI00809/pinia'
// 画面物理名
const screenName = 'GUI00809'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or27210 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00809' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or27210Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or27210.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00809',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27210Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27210Const.CP_ID(1)]: or27210.value,
})

/**
 *  ボタン押下時の処理(Or27210)
 *
 * @param tkbn --手帳区分
 */
function onClickOr27210(tkbn: string) {
  // 引継情報.計画書様式を設定する。
  or27210Data.tkbn = tkbn

  // Or27210のダイアログ開閉状態を更新する
  Or27210Logic.state.set({
    uniqueCpId: or27210.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or27210Data: notebookInfoSelectType = {
  userId: '8',
  getYmd: '2013/03/01',
  tkbn: '2',
}

const or27210Type = ref<Or27210Type>({
  notebookInfoData:{
    getYmd: '',
    toukyuuKnj: '',
    bikoKnj: '',
    teidoKnj: '',
    gappeiShougaiKnj: '',
    tKindKnj: '',
    tTokyu: '',
    tKindCd: ''
  }
})
watch(or27210Type, () => {
  console.log(or27210Type.value)
})

// ダイアログ表示フラグ
const showDialogOr27210 = computed(() => {
  // Or27210のダイアログ開閉状態
  return Or27210Logic.state.get(or27210.value.uniqueCpId)?.isOpen ?? false
})
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>

  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27210('3')"
        >GUI00809_手帳情報選択（手帳区分が3：精神手帳 or 5：身体障害手帳の場合：tkbn=3 or =5）
      </v-btn>
      <g-custom-or-27210
        v-if="showDialogOr27210"
        v-bind="or27210"
        v-model="or27210Type"
        :oneway-model-value="or27210Data"
      />
    </c-v-col>
  </c-v-row>

  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27210('2')"
        >GUI00809_手帳情報選択（手帳区分が2：療育手帳の場合：tkbn=2）
      </v-btn>
    </c-v-col>
  </c-v-row>
</template>
