<script setup lang="ts">
/**
 * GUI00834_［アセスメント（包括）］食事画面
 *
 * @description
 *
 * ［アセスメント（包括）］食事画面
 *
 * 画面ID_ GUI00834
 *
 * <AUTHOR>
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'

import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { TeX0008Logic } from '../../template/TeX0008/TeX0008.logic'
import type { CareItems, careLocationItems, Or34069Type } from '../Or34069/Or34069.type'
import { Or34069Const } from '../Or34069/Or34069.constants'
import { Or53105Const } from '../Or53105/Or53105.constants'
import { Or53105Logic } from '../Or53105/Or53105.logic'
import type {
  ConcreteCareItemType,
  ConcreteContentType,
  OrX00096OnewayType,
  OrX0096Type,
} from '../OrX0096/OrX0096.type'
import { Or59423Const } from '../Or59423/Or59423.constants'
import { Or59423Logic } from '../Or59423/Or59423.logic'
import { TeX0008Const } from '../../template/TeX0008/TeX0008.constants'
import { OrX0096Const } from '../OrX0096/OrX0096.constants'
import type { TeX0008StateType } from '../../template/TeX0008/TeX0008.type'
import type { Mo00045Type } from '../Or00386/Or00386.type'
import { Or03250Const } from './Or03250.constants'
import { Or03250Logic } from './Or03250.logic'
import type { Or03250OnewayType } from './Or03250.type'
import type { TeX0008Type } from '~/types/cmn/business/components/TeX0008Type'
import {
  useCmnCom,
  useScreenStore,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'

import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or51105OnewayType, Or51105Type } from '~/types/cmn/business/components/Or51105Type'
import type { Or34069OnewayType } from '~/types/cmn/business/components/Or34069Type'
import type {
  assessmentComprehensiveQuestionInEntity,
  assessmentComprehensiveQuestionOutWebEntity,
} from '~/repositories/cmn/entities/assessmentComprehensiveQuestionSelect'
import type {
  AssessmentComprehensiveExcretionInitSelectInEntity,
  AssessmentComprehensiveExcretionInitSelectOutEntity,
  AssessmentComprehensiveExcretionUpdateInEntity,
  assessmentComprehensiveExcretionUpdateOutEntity,
  subInfoExcretion,
} from '~/repositories/cmn/entities/AssessmentComprehensiveExcretionInitSelectEntity'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'

/**************************************************
 * Props
 **************************************************/

interface Props {
  uniqueCpId: string
  onewayModelValue: Or03250OnewayType
  parentUniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const { t } = useI18n()

const { getChildCpBinds } = useScreenUtils()

/**
 * ロード状態制御
 */
const isLoading = ref<boolean>(false)

/**
 * componentRef
 */
const componentRef = ref<HTMLDivElement | null>(null)

/**
 * 保存用テーブルデータ
 */
const tableData = ref<Or34069Type>({} as Or34069Type)

/**
 * 画面更新区分
 */
const screenUpdateKbn = ref(UPDATE_KBN.DELETE)

/**
 * 共通情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()

/**
 * データの更新回数
 */
let updateNum = ''

/**
 * API返却値の番号リストを一時保存する
 */
let dotNumberList: assessmentComprehensiveQuestionOutWebEntity['problemDotSolutionEtcInfo']['problemDotNumberInfoList'] =
  []

/**
 * 画面複写表示モード
 */
let screenFromDuplicate = false

/**
 * Twoway
 */
const local = reactive({
  tableHeader: {},
  commonInfo: {} as TeX0008Type,
  orX0096: {
    listSection: props.onewayModelValue.questionList ?? [],
  } as OrX0096Type,
  or51105: {
    kigoImiList: [],
  } as Or51105Type,
  or34096: {
    title: t('label.excretion-wipe-care-2-title'),
    careItems: [
      {
        title: t('label.preparation-adjustment-title'),
        showMode: Or03250Const.DEFAULT.DATA_DEFAULT,
        careLabel: [
          {
            valueType: ['cn011', 'cn012', 'cn013'],
            label: t('label.excretion-wipe-care-2-cellTitle11'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn021', 'cn022', 'cn023'],
            label: t('label.excretion-wipe-care-2-cellTitle12'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn031', 'cn032', 'cn033'],
            label: t('label.excretion-wipe-care-2-cellTitle13'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn041', 'cn042', 'cn043'],
            label: t('label.excretion-wipe-care-2-cellTitle14'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn051', 'cn052', 'cn053'],
            label: t('label.excretion-wipe-care-2-cellTitle15'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn061', 'cn062', 'cn063'],
            label: t('label.excretion-wipe-care-2-cellTitle16'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn071', 'cn072', 'cn073'],
            label: t('label.excretion-wipe-care-2-cellTitle17'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.move-title'),
        showMode: Or03250Const.DEFAULT.DATA_DEFAULT,
        careLabel: [
          {
            valueType: ['cn081', 'cn082', 'cn083'],
            label: t('label.excretion-wipe-care-2-cellTitle21'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn091', 'cn092', 'cn093'],
            label: t('label.excretion-wipe-care-2-cellTitle22'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn101', 'cn102', 'cn103'],
            label: t('label.excretion-wipe-care-2-cellTitle23'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn111', 'cn112', 'cn113'],
            label: t('label.excretion-wipe-care-2-cellTitle24'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn121', 'cn122', 'cn123'],
            label: t('label.excretion-wipe-care-2-cellTitle25'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.excretion-wipe-care-2-dataTitle1'),
        showMode: Or03250Const.DEFAULT.DATA_DEFAULT,
        careLabel: [
          {
            valueType: ['cn131', 'cn132', 'cn133'],
            label: t('label.excretion-wipe-care-2-cellTitle31'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn141', 'cn142', 'cn143'],
            label: t('label.excretion-wipe-care-2-cellTitle32'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn151', 'cn152', 'cn153'],
            label: t('label.excretion-wipe-care-2-cellTitle33'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn161', 'cn162', 'cn163'],
            label: t('label.excretion-wipe-care-2-cellTitle34'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn171', 'cn172', 'cn173'],
            label: t('label.excretion-wipe-care-2-cellTitle35'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn181', 'cn182', 'cn183'],
            label: t('label.excretion-wipe-care-2-cellTitle36'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn191', 'cn192', 'cn193'],
            label: t('label.excretion-wipe-care-2-cellTitle37'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn201', 'cn202', 'cn203'],
            label: t('label.excretion-wipe-care-2-cellTitle38'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.excretion-wipe-care-2-dataTitle2'),
        showMode: Or03250Const.DEFAULT.DATA_DEFAULT,
        careLabel: [
          {
            valueType: ['cn211', 'cn212', 'cn213'],
            label: t('label.excretion-wipe-care-2-cellTitle41'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn221', 'cn222', 'cn223'],
            label: t('label.excretion-wipe-care-2-cellTitle42'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn231', 'cn232', 'cn233'],
            label: t('label.excretion-wipe-care-2-cellTitle43'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn241', 'cn242', 'cn243'],
            label: t('label.excretion-wipe-care-2-cellTitle44'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn251', 'cn252', 'cn253'],
            label: t('label.excretion-wipe-care-2-cellTitle45'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn261', 'cn262', 'cn263'],
            label: t('label.excretion-wipe-care-2-cellTitle46'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.excretion-wipe-care-2-dataTitle3-3'),
        showMode: Or03250Const.DEFAULT.DATA_DEFAULT,
        careLabel: [
          {
            valueType: ['cn271', 'cn272', 'cn273'],
            label: t('label.excretion-wipe-care-2-cellTitle51'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn281', 'cn282', 'cn283'],
            label: t('label.excretion-wipe-care-2-cellTitle52'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn291', 'cn292', 'cn293'],
            label: t('label.excretion-wipe-care-2-cellTitle53'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn301', 'cn302', 'cn303'],
            label: t('label.excretion-wipe-care-2-cellTitle54'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
    ],
    careLocationItems: [
      {
        title: t('label.excretion-wipe-care-2-dataTitle4'),
        showMode: Or03250Const.DEFAULT.DATA_DEFAULT,
        careLocationLabel: [
          {
            valueType: ['cb011'],
            locationValue: { modelValue: '' },
            label: t('label.assessment-home-6-2-checkbox-label-9'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb021'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell62'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb031'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell63'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb041', 'cb042Knj'],
            locationValue: { modelValue: '' },
            label: t('label.other-label') + '：',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
        ],
      },
      {
        title: t('label.excretion-wipe-care-2-dataTitle5'),
        showMode: Or03250Const.DEFAULT.DATA_DEFAULT,
        careLocationLabel: [
          {
            valueType: ['cb051'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell62'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb061'],
            locationValue: { modelValue: '' },
            label: t('label.assessment-home-6-2-checkbox-label-15'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb071'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell70'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb081'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell71'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb091'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell72'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb101'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell73'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb111'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell74'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb121'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell75'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb131'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell76'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb141'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell77'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb151'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell78'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.DATA_DEFAULT,
            },
          },
          {
            valueType: ['cb161', 'cb162Knj'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell79'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
          {
            valueType: ['cb171', 'cb172Knj'],
            locationValue: { modelValue: '' },
            label: t('label.bathtub-type-label-6'),
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
          {
            valueType: ['cb181', 'cb182Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            valueType: ['cb191', 'cb192Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            valueType: ['cb201', 'cb202Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            valueType: ['cb211', 'cb212Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            valueType: ['cb221', 'cb222Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            valueType: ['cb231', 'cb232Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            valueType: ['cb241', 'cb242Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            valueType: ['cb251', 'cb252Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            valueType: ['cb261', 'cb262Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            valueType: ['cb271', 'cb272Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            valueType: ['cb281', 'cb282Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            valueType: ['cb291', 'cb292Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            valueType: ['cb301', 'cb302Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST,
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
        ],
      },
    ],
  } as Or34069Type,
})

/**
 * ロカールOneway
 */
const localOneway = reactive({
  orX0096Oneway: {
    showInputFlg: false,
    tableDisplayFlg: true,
    b1Cd: Or03250Const.DEFAULT.TAB_ID,
  } as OrX00096OnewayType,
  or51105Oneway: {
    sc1Id: '',
    cc1Id: '',
  } as Or51105OnewayType,
  or34069Oneway: {
    showTableBodyFlg: true,
  } as Or34069OnewayType,
})

const or34069 = ref({ uniqueCpId: '', showTableBodyFlg: true })
const or53105 = ref({ uniqueCpId: '' })
const orX0096 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or21814 = ref({ uniqueCpId: '' })

/**************************************************
 * Pinia
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or34069Const.CP_ID(0)]: or34069.value,
  [Or53105Const.CP_ID(0)]: or53105.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [OrX0096Const.CP_ID(1)]: orX0096.value,
})

/**************************************************
 * computed
 **************************************************/

/**
 * ダイアログ表示フラグ
 */
const showDialogOr53105CksFlg1 = computed(() => {
  // Or53105 cks_flg=1 のダイアログ開閉状態
  return Or53105Logic.state.get(or53105.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const showOr21814DialogFlg = computed(() => {
  // ダイアログの開閉フラグ
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 画面データ変更フラグ
 */
const _isEdit = computed(() => {
  const isEditByUniqueCpIds = useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
  return isEditByUniqueCpIds
})

/**************************************************
 * 関数定義
 **************************************************/
/**
 * コントロール初期化
 */
const initControl = () => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      firstBtnLabel: t('btn.yes'),
      firstBtnType: 'normal1',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      dialogTitle: t('label.confirm'),
    },
  })
}

/**
 * 番号設定関数
 *
 * @param index - 変換前のインデックス
 */
function setCircleNumber(index: number) {
  const circleNumbers = [
    '①',
    '②',
    '③',
    '④',
    '⑤',
    '⑥',
    '⑦',
    '⑧',
    '⑨',
    '⑩',
    '⑪',
    '⑫',
    '⑬',
    '⑭',
    '⑮',
    '⑯',
    '⑰',
    '⑱',
    '⑲',
    '⑳',
  ]
  if (index >= 1 && index <= 20) {
    return circleNumbers[index - 1]
  }
}

/**
 * 共通情報取得
 */
function getCommonInfo() {
  const commonInfo = TeX0008Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo = commonInfo
    screenUpdateKbn.value = commonInfo.updateKbn ?? ''
    // 更新区分クリア
    if (commonInfo.updateKbn === UPDATE_KBN.DELETE) {
      localOneway.orX0096Oneway.tableDisplayFlg = false
    } else {
      localOneway.orX0096Oneway.tableDisplayFlg = true
    }
  }
}

/**
 * 複写モード共通情報取得
 */
const getDuplicateCommonInfo = () => {
  const commonInfo = Or59423Logic.data.get(props.parentUniqueCpId + Or59423Const.CP_ID(0))
  if (commonInfo) {
    local.commonInfo = {
      ninteiFormF: commonInfo.duplicateInfo?.ninteiFormF,
      activeTabId: commonInfo.duplicateInfo?.activeTabId,
      jigyoId: commonInfo.duplicateInfo?.jigyoId,
      houjinId: commonInfo.duplicateInfo?.houjinId,
      shisetuId: commonInfo.duplicateInfo?.shisetuId,
      userId: commonInfo.duplicateInfo?.userId,
      syubetsuId: commonInfo.duplicateInfo?.syubetsuId,
      createYmd: commonInfo.duplicateInfo?.createYmd,
      historyUpdateKbn: commonInfo.duplicateInfo?.historyUpdateKbn,
      historyModifiedCnt: commonInfo.duplicateInfo?.historyModifiedCnt,
      sc1Id: commonInfo.duplicateInfo?.sc1Id,
      recId: commonInfo.duplicateInfo?.recId,
      cc1Id: commonInfo.duplicateInfo?.cc1Id,
      createUserId: commonInfo.duplicateInfo?.createUserId,
      svJigyoId: commonInfo.duplicateInfo?.svJigyoId,
      updateKbn: commonInfo.duplicateInfo?.updateKbn,
      planPeriodFlg: commonInfo.duplicateInfo?.planPeriodFlg,
      svjigyoName: commonInfo.duplicateInfo.svjigyoName,
    }
  }
}

/**
 * 親画面のstateを変更する
 *
 * @param state - state
 */
const setTeX0008State = (state: TeX0008StateType) => {
  TeX0008Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

/**
 * 初期情報取得
 */
async function getInitDataInfo() {
  try {
    const inputData: AssessmentComprehensiveExcretionInitSelectInEntity = {
      cc1Id: local.commonInfo.cc1Id ?? '',
      sc1Id: local.commonInfo.sc1Id ?? '',
    }

    // 初期情報取得APIを呼び出す
    const resData: BaseResponseBody<AssessmentComprehensiveExcretionInitSelectOutEntity> =
      await ScreenRepository.select('assessmentComprehensiveExcretionInitSelect', inputData)
    // 返却値チェック
    if (resData.data) {
      processInfoData(resData, true)
      updateNum = resData.data.subInfoExcretion.modifiedCnt ?? ''
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 問題点初期情報取得
 */
const getProblemDotSolutionEtcInfoData = async () => {
  try {
    const inputData: assessmentComprehensiveQuestionInEntity = {
      cc1Id: local.commonInfo?.cc1Id,
      sc1Id: local.commonInfo?.sc1Id,
      typeId: Or03250Const.DEFAULT.TAB_ID,
    }

    const resData: BaseResponseBody<assessmentComprehensiveQuestionOutWebEntity> =
      await ScreenRepository.select('problemDotSolutionEtcInfoSelect', inputData)
    if (resData.data) {
      processParentData(resData.data.problemDotSolutionEtcInfo)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 更新回数チェック
 *
 * @param isHistory - 履歴更新区分
 */
const getRequestScreenUpdataKbn = (isHistory: boolean) => {
  // 更新回数が不存在する場合、更新区分を新規にする
  if (isHistory) {
    return local.commonInfo.historyUpdateKbn === UPDATE_KBN.NONE
      ? UPDATE_KBN.UPDATE
      : (local.commonInfo.historyUpdateKbn ?? '')
  } else {
    if (updateNum === '' && screenUpdateKbn.value === UPDATE_KBN.NONE) return UPDATE_KBN.CREATE
    return screenUpdateKbn.value === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : screenUpdateKbn.value
  }
}

/**
 * 複写情報取得
 */
const getDuplicateDataInfo = async () => {
  const inputData: AssessmentComprehensiveExcretionInitSelectInEntity = {
    cc1Id: local.commonInfo.duplicateCareCheckId ?? '',
    sc1Id: local.commonInfo.duplicatePlanId ?? '',
  }
  // 初期情報取得APIを呼び出す
  const resData: BaseResponseBody<AssessmentComprehensiveExcretionInitSelectOutEntity> =
    await ScreenRepository.select('assessmentComprehensiveExcretionInitSelect', inputData)
  // 返却値チェック
  if (resData.data) {
    processInfoData(resData, false)
  }
}

/**
 * 複写情報取得「問題点情報」
 */
const getDuplicateProblemDotSolutionEtcInfoData = async () => {
  try {
    const inputData: assessmentComprehensiveQuestionInEntity = {
      cc1Id: local.commonInfo?.duplicateCareCheckId ?? '',
      sc1Id: local.commonInfo?.duplicatePlanId ?? '',
      // タブID：「1：食事」
      typeId: Or03250Const.DEFAULT.TAB_ID,
    }

    const resData: BaseResponseBody<assessmentComprehensiveQuestionOutWebEntity> =
      await ScreenRepository.select('problemDotSolutionEtcInfoSelect', inputData)
    if (resData.data) {
      processDuplicateData(resData.data.problemDotSolutionEtcInfo)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * API返却値処理
 *
 * @param resData - API返却値
 *
 * @param updateFlg - 更新回数フラグ
 */
const processInfoData = (
  resData: BaseResponseBody<AssessmentComprehensiveExcretionInitSelectOutEntity>,
  updateFlg: boolean
) => {
  if (!resData.data) return
  // 複写モードの場合、返却値を保存する
  if (props.onewayModelValue.screenMode === TeX0008Const.DEFAULT.MODE_COPY) {
    Or03250Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        comprehensiveQuestionInfo: props.onewayModelValue.comprehensiveQuestionInfo,
        result: resData,
      },
    })
  }
  // セレクト選択肢設定
  const { careOfferLocationInfoList, scheduleInfoList, familyInfoList, offerInfoList } =
    resData.data.markInfo
  localOneway.or34069Oneway.careOfferMarkMeaning = offerInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.careFamilyMarkMeaning = familyInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.carePlanMarkMeaning = scheduleInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.careLocationMarkMeaning = careOfferLocationInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })

  // 複数回の更新による無限再帰を防ぐために、データをディープコピーし、以下の処理は別データに移管する
  tableData.value = cloneDeep(local.or34096)
  for (const key in resData.data.subInfoExcretion) {
    tableData.value.careItems.forEach((item) => {
      item.careLabel.forEach((itm) => {
        const index = itm.valueType?.indexOf(key)
        switch (index) {
          case Or03250Const.DEFAULT.TABLE_INIT_DATA_0:
            itm.offerValue.modelValue = resData.data?.subInfoExcretion[key]
            break
          case Or03250Const.DEFAULT.TABLE_INIT_DATA_1:
            itm.familyValue.modelValue = resData.data?.subInfoExcretion[key]
            break
          case Or03250Const.DEFAULT.TABLE_INIT_DATA_2:
            itm.planValue.modelValue = resData.data?.subInfoExcretion[key]
            break
        }
      })
    })
    tableData.value.careLocationItems.forEach((item) => {
      item.careLocationLabel.forEach((itm) => {
        const index = itm.valueType?.indexOf(key)
        switch (index) {
          case 0:
            itm.locationValue.modelValue = resData.data?.subInfoExcretion[key] ?? ''
            break
          case 1:
            ;(itm.inputContent as Mo00045Type).value = resData.data?.subInfoExcretion[key] ?? ''
            break
        }
      })
    })
  }
  // 処理済みのデータを画面に表示する
  local.or34096 = tableData.value
  // 複写再発火の場合は、サブ情報の更新回数は上書きしない
  if (updateFlg) {
    updateNum = resData.data.subInfoExcretion.modifiedCnt ?? ''
  } else {
    // 複写発火の場合は、画面の更新区分を `U` にする
    screenUpdateKbn.value = UPDATE_KBN.UPDATE
  }
}

/**
 * 新規処理
 */
const createNew = () => {
  local.or34096.careItems.forEach((item) => {
    // 更新区分を新規にする
    screenUpdateKbn.value = UPDATE_KBN.CREATE
    item.careLabel.forEach((sItem) => {
      sItem.planValue = { modelValue: '' }
      sItem.offerValue = { modelValue: '' }
      sItem.familyValue = { modelValue: '' }
      if (sItem.label === '') {
        sItem.inputContent = {
          value: '',
        }
      }
    })
  })
  local.or34096.careLocationItems.forEach((item) => {
    item.careLocationLabel.forEach((sItem) => {
      sItem.locationValue = { modelValue: '' }
      if (
        sItem.label === '' ||
        sItem.inputShowMode.appendInput === Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST ||
        sItem.inputShowMode.appendInput === Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_UNIT
      ) {
        sItem.inputContent = {
          value: '',
        }
      }
    })
  })
  // 各ラベルをクリア
  const newQuestionList = local.orX0096.listSection.map((item) => {
    return {
      ...item,
      isHaveIssuesFlg: false,
      isPlanningFlg: false,
    }
  })
  // 要介護課題等々クリア
  local.orX0096 = {
    listSection: newQuestionList,
    concreteCareItemList: [],
    concreteContentList: [],
  }
  // 各プルダウン選択肢リストクリア
  localOneway.or34069Oneway = {
    carePlanMarkMeaning: [],
    careOfferMarkMeaning: [],
    careFamilyMarkMeaning: [],
    careLocationMarkMeaning: [],
    showTableBodyFlg: true,
  }
}

/**
 * 問題点情報の共通処理
 *
 * @param comprehensiveQuestionInfo - API返却値
 */
function processParentData(
  comprehensiveQuestionInfo: assessmentComprehensiveQuestionOutWebEntity['problemDotSolutionEtcInfo']
) {
  localOneway.orX0096Oneway.officeId = local.commonInfo.svJigyoId ?? ''
  // 本画面のデータを絞り出す
  // 対応するケア項目リスト
  const concreteCareItemList: ConcreteCareItemType[] = comprehensiveQuestionInfo.careItemList
    .filter((item) => item.b1Cd === Or03250Const.DEFAULT.TAB_ID)
    .map((item) => {
      return {
        key: item.cc33Id,
        content: item.memoKnj,
        dmyCc32Id: item.dmyCc32Id,
        modifiedCnt: item.modifiedCnt,
        cc33Id: item.cc33Id,
        b1Cd: item.b1Cd,
        cc32Id: item.cc32Id,
        seq: item.seq,
        ci2Id: item.ci2Id,
        updateKbn: '',
      }
    })
  local.orX0096.concreteCareItemList = concreteCareItemList
  // 具体的内容
  const concreteContentList: ConcreteContentType[] =
    comprehensiveQuestionInfo.concreteContentsInfoList
      .filter((item) => item.b1Cd === Or03250Const.DEFAULT.TAB_ID)
      .map((item): ConcreteContentType => {
        return {
          key: item.cc32Id,
          correspondenceKeys: [],
          content: item.memoKnj,
          cc32Id: item.cc32Id,
          modifiedCnt: item.modifiedCnt,
          juni: item.juni,
          b1Cd: item.b1Cd,
          seq: item.seq,
          cc32Type: item.cc32Type,
          ci1Id: item.ci1Id,
          dmyB4Cd: item.dmyB4Cd,
          b4Cd: item.b4Cd,
          number: '',
          updateKbn: '',
        }
      })
  // 番号リストを作成
  const numberList = comprehensiveQuestionInfo.problemDotNumberInfoList
    .filter((item) => item.b1Cd === Or03250Const.DEFAULT.TAB_ID)
    .map((item) => {
      return {
        cc32Id: item.cc32Id,
        b4Cd: item.b4Cd,
        seq: item.seq,
        modifiedCnt: item.modifiedCnt1,
        cc34Id: item.cc34Id,
        b1Cd: item.b1Cd,
        modifiedCnt1: item.modifiedCnt1,
      }
    })
  if (concreteContentList.length > 0) {
    concreteContentList.forEach((item) => {
      const findedItem = numberList.filter((sItem) => sItem.cc32Id === item.cc32Id)
      if (findedItem) {
        item.correspondenceKeys = findedItem
        item.number = findedItem
          .map((item) => setCircleNumber(parseInt(item.b4Cd) - Or03250Const.DEFAULT.TAB_SUB_ID))
          .join('')
      }
    })
  }

  local.orX0096.concreteContentList = concreteContentList
  /**
   * 問題点リスト
   */
  const questionList = comprehensiveQuestionInfo.problemDotSolutionInfoList.filter(
    (item) => item.b1Cd === Or03250Const.DEFAULT.TAB_ID
  )
  local.orX0096.listSection = local.orX0096.listSection.map((item) => {
    const findedItem = questionList.find((sItem) => sItem.b4Cd === item.b4cd)
    if (findedItem) {
      return {
        ...item,
        cc31Id: findedItem?.cc31Id ?? '',
        isPlanningFlg: findedItem.f1 === Or03250Const.DEFAULT.API_RESULT_CHECKON,
        isHaveIssuesFlg: findedItem.f2 === Or03250Const.DEFAULT.API_RESULT_CHECKON,
        modifiedCnt: findedItem?.modifiedCnt,
      }
    } else {
      return {
        ...item,
        isPlanningFlg: false,
        isHaveIssuesFlg: false,
        cc31Id: '',
        modifiedCnt: Or03250Const.DEFAULT.DATA_DEFAULT,
      }
    }
  })
  dotNumberList = comprehensiveQuestionInfo.problemDotNumberInfoList
  localOneway.orX0096Oneway.maxCount = parseInt(comprehensiveQuestionInfo.cc32IdMax)
  // 医療画面
  local.orX0096.thingsToKeepInMindContentsInfo =
    comprehensiveQuestionInfo.thingsToKeepInMindContentsInfo
}

/**
 * tabを制御
 *
 * @param component - コンポーネント
 */
const disableTab = (component: HTMLElement) => {
  const focusableSelectors = [
    'a[href]',
    'area[href]',
    'input',
    'select',
    'textarea',
    'button',
    'iframe',
    '[tabindex]',
    '[contenteditable]',
  ]

  const focusables = component.querySelectorAll(focusableSelectors.join(','))
  focusables.forEach((el) => {
    el.setAttribute('tabindex', '-1')
  })
}

/**
 * APIリクエストパラメータ作成
 *
 * @param request - リクエスト
 */
const createRequest = (request: string | undefined) => {
  if (!request) return Or03250Const.DEFAULT.DATA_DEFAULT
  if (request === '') {
    return Or03250Const.DEFAULT.DATA_DEFAULT
  }
  if (screenUpdateKbn.value === UPDATE_KBN.CREATE) {
    return Or03250Const.DEFAULT.DATA_DEFAULT
  }
  return request
}

/**
 * 保存処理
 */
const _userSave = async () => {
  // 保存前のチェック
  if (!_isEdit.value && screenUpdateKbn.value === UPDATE_KBN.NONE && !screenFromDuplicate) {
    setShowDialog(t('message.i-cmn-21800'))
    return
  }
  try {
    isLoading.value = true
    // 子コンポーネントからデータを一括取得する
    const childrenTableCpBinds = getChildCpBinds(props.uniqueCpId, {
      [Or34069Const.CP_ID(0)]: { cpPath: Or34069Const.CP_ID(0), twoWayFlg: true },
      [OrX0096Const.CP_ID(1)]: { cpPath: OrX0096Const.CP_ID(1), twoWayFlg: true },
    })
    // ケアの内容を取得する
    const careItems = (childrenTableCpBinds[Or34069Const.CP_ID(0)].twoWayBind?.value as Or34069Type)
      .careItems
    // ケアの提供場所を取得する
    const careLocationItems = (
      childrenTableCpBinds[Or34069Const.CP_ID(0)].twoWayBind?.value as Or34069Type
    ).careLocationItems
    // 要介護者などの健康上や生活上の問題点及び解決すべき課題等を取得する
    const careRecipientHealthAndLifeIssues = childrenTableCpBinds[OrX0096Const.CP_ID(1)].twoWayBind
      ?.value as OrX0096Type
    const inputData: AssessmentComprehensiveExcretionUpdateInEntity = {
      // e文書用パラメータ
      edocumentUseParam: useCmnCom().getEDocumentParam(),
      // e文書削除用パラメータ
      edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
      // 履歴番号
      rirekiNo: local.commonInfo.cc1Id ?? '',
      // 機能ID
      functionId: systemCommonsStore.getFunctionId ?? '',
      // ログインID
      loginId: systemCommonsStore.getStaffId ?? '',
      // システムコード
      sysCd: systemCommonsStore.getSystemCode ?? '',
      // システム略称
      sysRyaku: Or03250Const.DEFAULT.SYSTEM_ACRONYM,
      // メニュー２名称
      menu2Knj: '',
      // メニュー３名称
      menu3Knj: Or03250Const.DEFAULT.MENU3_NAME,
      // セクション名
      sectionName: Or03250Const.DEFAULT.SECTION_NAME,
      // 個人情報使用フラグ
      kojinhogoUsedFlg: Or03250Const.DEFAULT.DATA_DEFAULT,
      // 個人情報番号
      sectionAddNo: Or03250Const.DEFAULT.DATA_DEFAULT,
      // 事業者名
      svJigyoKnj: local.commonInfo.svjigyoName ?? '',
      // 作成者名
      createKnj: local.commonInfo.createUserName ?? '',
      // 期間管理フラグ
      kikanKanriFlg: local.commonInfo.planPeriodFlg ?? '',
      // 計画対象期間番号
      sc1Number: local.commonInfo.sc1Id ?? '',
      // 開始日
      startYmd: local.commonInfo.startYmd ?? '',
      // 終了日
      endYmd: local.commonInfo.endYmd ?? '',
      cc1Id: createRequest(local.commonInfo.cc1Id),
      sc1Id: local.commonInfo.sc1Id ?? Or03250Const.DEFAULT.DATA_DEFAULT,
      houjinId: local.commonInfo.houjinId ?? '',
      shisetuId: local.commonInfo.shisetuId ?? '',
      svJigyoId: local.commonInfo.svJigyoId ?? '',
      userId: local.commonInfo.userId ?? '',
      syubetsuId: local.commonInfo.syubetsuId ?? '',
      createYmd: local.commonInfo.createYmd ?? '',
      updateKbn: getRequestScreenUpdataKbn(false),
      historyUpdateKbn: getRequestScreenUpdataKbn(true),
      historyModifiedCnt: createRequest(local.commonInfo.historyModifiedCnt),
      shokuId:
        local.commonInfo.createUserId === ''
          ? Or03250Const.DEFAULT.INPUT_DISPLAY_FLG_LAST
          : local.commonInfo.createUserId!,
      typeId: Or03250Const.DEFAULT.TAB_ID,
      subInfoExcretion: {
        ...handleCareItems(['offerValue', 'familyValue', 'planValue'], careItems),
        ...handleCareLocationItems(['locationValue'], careLocationItems),
        modifiedCnt: updateNum === '' ? Or03250Const.DEFAULT.DATA_DEFAULT : updateNum,
      } as subInfoExcretion,
    }

    const problemInfoList = processProblemDotLsit(careRecipientHealthAndLifeIssues)

    // 問題点番号リスト
    if (screenUpdateKbn.value !== UPDATE_KBN.CREATE) {
      inputData.problemDotNumberInfoList = dotNumberList
    } else {
      inputData.problemDotNumberInfoList = undefined
    }
    // 具体的内容リスト
    inputData.concreteContentsInfoList = problemInfoList.concreteContentsInfoList
    // 対応するケア項目リスト
    inputData.careItemList = problemInfoList.careItemList
    // 問題点解決情報リスト
    inputData.problemDotSolutionInfoList = problemInfoList.issuseList
    const resData: assessmentComprehensiveExcretionUpdateOutEntity = await ScreenRepository.update(
      'assessmentComprehensiveExcretionUpdate',
      inputData
    )
    if (resData.data) {
      // 保存処理返却値を親画面Piniaに設定する
      TeX0008Logic.data.set({
        uniqueCpId: props.parentUniqueCpId,
        value: {
          ...local.commonInfo,
          childrenScreenApiResult: {
            sc1Id: resData.data.sc1Id,
            cc1Id: resData.data.cc1Id,
          },
        },
      })
      // 保存処理完了、親画面を通知する
      setTeX0008State({ saveCompletedState: true })
      // 更新区分クリア
      screenUpdateKbn.value = UPDATE_KBN.NONE
      screenFromDuplicate = false
    }
  } finally {
    isLoading.value = false
  }
}
/**
 * データの保存処理
 *
 * @param arr - 操作したデータ
 *
 * @param careList - 各ケア内容使用したラベル
 */
function handleCareItems(arr: string[], careList: CareItems[]) {
  const obj: Record<string, unknown> = {}
  careList.forEach((item) => {
    item.careLabel.forEach((itm) => {
      itm.valueType?.forEach((i: string, x: number) => {
        if (x < arr.length) {
          // 断言处理
          const key = arr[x] as keyof typeof itm
          if (key in itm) {
            obj[i] = (itm[key] as Mo00040Type).modelValue
          } else {
            obj[i] = itm.inputContent?.value
          }
        } else {
          obj[i] = itm.inputContent?.value
        }
      })
    })
  })
  return obj
}
/**
 * データの保存処理
 *
 * @param arr - 操作したデータ
 *
 * @param careList - 各ケア内容使用したラベル
 */
function handleCareLocationItems(arr: string[], careList: careLocationItems[]) {
  const obj: Record<string, unknown> = {}
  careList.forEach((item) => {
    item.careLocationLabel.forEach((itm) => {
      itm.valueType?.forEach((i: string, x: number) => {
        if (x < arr.length) {
          const key = arr[x] as keyof typeof itm
          if (key in itm) {
            obj[i] = (itm[key] as Mo00040Type).modelValue
          } else {
            obj[i] = itm.inputContent?.value
          }
        } else {
          obj[i] = itm.inputContent?.value
        }
      })
    })
  })
  return obj
}
/**
 * 問題点や解決すべき課題等々情報保存前処理
 *
 * @param careRecipientHealthAndLifeIssues - 課題情報
 */
const processProblemDotLsit = (careRecipientHealthAndLifeIssues: OrX0096Type) => {
  return {
    issuseList: createProblemDotList(careRecipientHealthAndLifeIssues.listSection),
    concreteContentsInfoList: createConcreteContentsInfoList(
      careRecipientHealthAndLifeIssues.concreteContentList
    ),
    careItemList: createCareList(careRecipientHealthAndLifeIssues.concreteCareItemList),
  }
}

/**
 * 問題点解決情報リスト作成関数
 *
 * @param listSection - 課題情報
 */
const createProblemDotList = (listSection: OrX0096Type['listSection']) => {
  return listSection.map((item) => {
    return {
      cc31Id: item.cc31Id,
      b1Cd: Or03250Const.DEFAULT.TAB_ID,
      b4Cd: item.b4cd,
      f1: item.isPlanningFlg
        ? Or03250Const.DEFAULT.API_RESULT_CHECKON
        : Or03250Const.DEFAULT.DATA_DEFAULT,
      f2: item.isHaveIssuesFlg
        ? Or03250Const.DEFAULT.API_RESULT_CHECKON
        : Or03250Const.DEFAULT.DATA_DEFAULT,
      modifiedCnt: item.modifiedCnt,
    }
  })
}

/**
 * 具体的内容作成関数
 *
 * @param concreteContentList - 具体的内容情報
 */
const createConcreteContentsInfoList = (
  concreteContentList: OrX0096Type['concreteContentList']
) => {
  // 具体内容情報リスト
  let concreteContentsInfoList: AssessmentComprehensiveExcretionUpdateInEntity['concreteContentsInfoList'] =
    []
  concreteContentsInfoList = concreteContentList?.map((item, index) => {
    return {
      // 更新区分チェック
      cc32Id:
        item.updateKbn === UPDATE_KBN.CREATE ? Or03250Const.DEFAULT.DATA_DEFAULT : item.cc32Id,
      // 更新区分チェック、新規の場合は、画面記録IDを設定する
      cc32IdRecord: item.updateKbn === UPDATE_KBN.CREATE ? item.cc32Id : '',
      b1Cd: Or03250Const.DEFAULT.TAB_ID,
      memoKnj: item.content,
      seq: (index + 1).toString(),
      juni: item.juni,
      cc32Type: item.cc32Type,
      ci1Id: item.ci1Id,
      dmyB4Cd: item.correspondenceKeys.map((item) => {
        return {
          b4Cd: item.b4Cd,
        }
      }),
      updateKbn: item.updateKbn === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : item.updateKbn,
      modifiedCnt: item.modifiedCnt,
    }
  })
  return concreteContentsInfoList
}

/**
 * 対応するケア項目作成関数
 *
 * @param concreteCareItemList - 対応するケア項目情報
 */
const createCareList = (concreteCareItemList: OrX0096Type['concreteCareItemList']) => {
  let careItemList: AssessmentComprehensiveExcretionUpdateInEntity['careItemList'] = []
  careItemList = concreteCareItemList?.map((item, index) => {
    return {
      // 該当情報が新規の場合、画面に表示したIDを削除する
      cc33Id:
        item.updateKbn === UPDATE_KBN.CREATE ? Or03250Const.DEFAULT.DATA_DEFAULT : item.cc33Id,
      b1Cd: Or03250Const.DEFAULT.TAB_ID,
      memoKnj: item.content,
      cc32Id: item.cc32Id,
      seq: (index + 1).toString(),
      ci2Id: item.ci2Id,
      dmyCc32Id: item.dmyCc32Id,
      updateKbn: item.updateKbn === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : item.updateKbn,
      modifiedCnt: item.modifiedCnt,
    }
  })
  return careItemList
}

/**
 * 複写データ作成関数
 *
 * @param comprehensiveQuestionInfo - 複写する問題点情報データ
 */
const processDuplicateData = (
  comprehensiveQuestionInfo: assessmentComprehensiveQuestionOutWebEntity['problemDotSolutionEtcInfo']
) => {
  let maxCount = localOneway.orX0096Oneway.maxCount
  local.orX0096.listSection.forEach((item) => {
    // ID以外、他のデータを画面に設定する
    const findedItem = comprehensiveQuestionInfo.problemDotSolutionInfoList.find(
      (listSectionItem) => listSectionItem.b4Cd === item.b4Cd
    )
    if (findedItem) {
      item.isPlanningFlg = findedItem.f1 === Or03250Const.DEFAULT.API_RESULT_CHECKON
      item.isHaveIssuesFlg = findedItem.f2 === Or03250Const.DEFAULT.API_RESULT_CHECKON
    }
  })
  // 元データを削除する
  local.orX0096.concreteContentList?.forEach((item) => {
    item.updateKbn = UPDATE_KBN.DELETE
  })
  // 取得したデータ画面に表示する
  const concreteContensList = comprehensiveQuestionInfo.concreteContentsInfoList.map(
    (item): ConcreteContentType => {
      maxCount++
      return {
        key: item.cc32Id,
        correspondenceKeys: [],
        content: item.memoKnj,
        // ID再設定
        cc32Id: maxCount.toString(),
        modifiedCnt: Or03250Const.DEFAULT.DATA_DEFAULT,
        juni: item.juni,
        b1Cd: item.b1Cd,
        seq: item.seq,
        cc32Type: item.cc32Type,
        ci1Id: item.ci1Id,
        dmyB4Cd: item.dmyB4Cd,
        b4Cd: item.b4Cd,
        number: '',
        updateKbn: UPDATE_KBN.CREATE,
      }
    }
  )
  // 番号リストを作成
  const numberList = comprehensiveQuestionInfo.problemDotNumberInfoList.map((item) => {
    return {
      cc32Id: item.cc32Id,
      b4Cd: item.b4Cd,
      seq: item.seq,
      modifiedCnt: item.modifiedCnt1,
      cc34Id: item.cc34Id,
      b1Cd: item.b1Cd,
      modifiedCnt1: Or03250Const.DEFAULT.DATA_DEFAULT,
    }
  })
  // 番号リスト対応するCc32Id再設定
  numberList.forEach((item) => {
    // 対応するIDのデータを探す
    const findedItem = concreteContensList.find((sItem) => sItem.key === item.cc32Id)
    if (findedItem) {
      item.cc32Id = findedItem.cc32Id
    }
  })
  // 対応するケア項目リスト作成
  const concreteCareList = comprehensiveQuestionInfo.careItemList.map(
    (item, index): ConcreteCareItemType => {
      return {
        key: item.cc32Id,
        dmyCc32Id: item.dmyCc32Id,
        cc33Id: item.cc33Id,
        content: item.memoKnj,
        b1Cd: item.b1Cd,
        cc32Id: item.cc32Id,
        updateKbn: UPDATE_KBN.CREATE,
        modifiedCnt: Or03250Const.DEFAULT.DATA_DEFAULT,
        seq: (index + 1).toString(),
        ci2Id: item.ci2Id,
      }
    }
  )
  const updatedConcreteCareList = concreteCareList
    .map((item) => {
      const findedItem = concreteContensList.find((sItem) => sItem.key === item.cc32Id)
      if (findedItem) {
        return {
          ...item,
          cc32Id: findedItem.cc32Id,
          dmyCc32Id: findedItem.cc32Id,
        }
      } else {
        // 対応するアイテムが見つからない場合はnullを返す
        return null
      }
    })
    .filter((item) => item !== null) // nullをフィルタリングして最終的なリストを作成
  if (concreteContensList.length > 0) {
    concreteContensList.forEach((item) => {
      const findedItem = numberList.filter((sItem) => sItem.cc32Id === item.cc32Id)
      if (findedItem) {
        item.correspondenceKeys = findedItem
        item.number = findedItem.map((item) => setCircleNumber(parseInt(item.b4Cd))).join('')
      }
    })
  }
  local.orX0096.concreteContentList?.push(...concreteContensList)
  local.orX0096.concreteCareItemList?.push(...updatedConcreteCareList)
  localOneway.orX0096Oneway.maxCount = maxCount
}

/**
 * 削除処理を行う
 */
const userDelete = () => {
  // 更新区分を D に設定する
  screenUpdateKbn.value = UPDATE_KBN.DELETE
  // テーブルボディーを非表示にする
  localOneway.or34069Oneway.showTableBodyFlg = false
  localOneway.orX0096Oneway.tableDisplayFlg = false
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */

const setShowDialog = (paramDialogText: string) => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
}
/**
 * 画面再表示関数
 */
const reload = async () => {
  // 全表示フラグをtrueにする
  localOneway.or34069Oneway.showTableBodyFlg = true
  localOneway.orX0096Oneway.tableDisplayFlg = true
  screenFromDuplicate = false
  // 更新区分クリア
  screenUpdateKbn.value = UPDATE_KBN.NONE
  isLoading.value = true
  await getInitDataInfo()
  await getProblemDotSolutionEtcInfoData()
  isLoading.value = false
}

/**
 * 初期化処理
 */
onMounted(() => {
  initControl()
})

/**************************************************
 * watch関数
 **************************************************/

/** 画面イベント監視 */
watch(
  () => TeX0008Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    // 初期情報取得
    getCommonInfo()

    // 本画面ではない場所、処理を中断する
    if (local.commonInfo.activeTabId !== Or03250Const.DEFAULT.TAB_ID) {
      return
    }
    /** 再表示処理 */
    if (newValue?.isRefresh) {
      await reload()
    }
    /** 保存イベント処理 */
    if (newValue?.saveEventFlg) {
      await _userSave()
    }

    /** 削除イベント処理 */
    if (newValue?.deleteEventFlg) {
      userDelete()
    }

    // 新規処理
    if (newValue?.createEventFlg) {
      createNew()
    }

    /** 作成日変更処理 */
    if (newValue?.isCreateDateChanged) {
      return
    }

    /** お気に入りイベント処理 */
    if (newValue?.favoriteEventFlg) {
      return
    }

    /** 複写イベント処理 */
    if (newValue?.copyEventFlg) {
      isLoading.value = true
      await getDuplicateDataInfo()
      await getDuplicateProblemDotSolutionEtcInfoData()
      screenFromDuplicate = true
      isLoading.value = false
      return
    }

    /** 印刷イベント処理 */
    if (newValue?.printEventFlg) {
      return
    }

    /** マスタ他イベント処理 */
    if (newValue?.masterEventFlg) {
      return
    }

    /** 優先順位表示フラグ */
    if (newValue?.priorityOrderEventFlg) {
      return
    }
  },
  { deep: true, immediate: true }
)

/**
 * 複写モードイベント監視
 */
watch(
  () => Or59423Logic.event.get(props.parentUniqueCpId + Or59423Const.CP_ID(0)),
  async (newValue) => {
    if (!newValue) return

    // 複写モード共通情報取得
    getDuplicateCommonInfo()

    // 本画面ではない場合、処理終了
    if (local.commonInfo.activeTabId !== Or03250Const.DEFAULT.TAB_ID) {
      return
    }

    if (newValue.reloadEvent) {
      isLoading.value = true
      await getInitDataInfo()
      await getProblemDotSolutionEtcInfoData()
      // 全処理済み、タブ変更を禁止する
      if (props.onewayModelValue.screenMode === TeX0008Const.DEFAULT.MODE_COPY) {
        if (componentRef.value) {
          disableTab(componentRef.value)
        }
      }
      isLoading.value = false
    }
  }
)
</script>

<template>
  <div
    ref="componentRef"
    class="or03250Wrapper"
  >
    <v-overlay
      :model-value="isLoading"
      :persistent="false"
      class="align-center justify-center"
      ><v-progress-circular
        indeterminate
        color="primary"
      ></v-progress-circular
    ></v-overlay>
    <c-v-row no-gutters>
      <g-custom-or34069
        v-bind="or34069"
        :oneway-model-value="localOneway.or34069Oneway"
        :model-value="local.or34096"
      />
      <div
        class="w-100 mt-2"
        style="height: 1px; background-color: #cccccc"
      ></div>
      <g-custom-or-x0096
        v-bind="orX0096"
        :oneway-model-value="localOneway.orX0096Oneway"
        :model-value="local.orX0096"
      />
    </c-v-row>
    <g-custom-or-53105
      v-if="showDialogOr53105CksFlg1"
      v-bind="or53105"
    />
    <g-base-or-21814
      v-if="showOr21814DialogFlg"
      v-bind="or21814"
    />
  </div>
</template>

<style scoped lang="scss"></style>
