<script setup lang="ts">
/**
 * OrX0001:有機体:モーダル（画面/特殊コンポーネント）
 * GUI04471_［削除確認］画面
 *
 * @description
 * 削除確認
 *
 * <AUTHOR>
 */
import { watch, reactive, ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0001Const } from './OrX0001.constants'
import type { OrX0001StateType } from './OrX0001.type'
import { useScreenOneWayBind } from '#imports'

import type {
  ItemStruct,
  OrX0001Type,
  OrX0001OnewayType,
} from '~/types/cmn/business/components/OrX0001Type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo00606OnewayType } from '~/types/business/components/Mo00606Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: OrX0001Type
  onewayModelValue: OrX0001OnewayType
  uniqueCpId: string
}
/**
 * 引継情報を取得する
 */
const props = defineProps<Props>()

/** Two-way */
const local = reactive({
  modelValue: {
    ...props.modelValue,
  } as OrX0001Type,
  items: [] as ItemStruct[],
})

const localOneway = reactive({
  onewayModelValue: {
    ...props.onewayModelValue,
  } as OrX0001OnewayType,
  msg: OrX0001Const.DEFAULT.STR.EMPTY,
  mo00024Oneway: {
    persistent: true,
    showCloseBtn: true,
    class: 'mr-1',
    width: '788px',
    mo01344Oneway: {
      toolbarTitle: t('label.confirm-deletion'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    } as Mo01344OnewayType,
  } as Mo00024OnewayType,
  mo00039Oneway: {
    name: OrX0001Const.DEFAULT.STR.EMPTY,
    itemLabel: OrX0001Const.DEFAULT.STR.EMPTY,
    showItemLabel: false,
    inline: true,
    hideDetails: 'auto',
    items: undefined,
    disabled: false,
    checkOff: false,
  } as Mo00039OnewayType,
  mo00606OnewayYes: {
    name: 'deleteDialogYes',
    btnLabel: t('btn.confirm'),
    width: '60px',
  } as Mo00606OnewayType,
  mo00611OnewayCancel: {
    name: 'deleteDialogCancel',
    btnLabel: t('btn.cancel'),
    width: '80px',
    variant: 'tonal',
    color: 'gray',
  } as Mo00611OnewayType,
})
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<OrX0001StateType>({
  cpId: OrX0001Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? OrX0001Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: OrX0001Const.DEFAULT.IS_OPEN,
})

/**
 * 削除種別
 */
const mo00039OModelValue = ref(OrX0001Const.DEFAULT.STR.ONE)

/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(async () => {
  // 汎用コード取得API実行
  await initCodes()

  localOneway.msg = t('message.i-cmn-11260', [
    localOneway.onewayModelValue.createYmd,
    localOneway.onewayModelValue.kinouKnj,
  ])
})

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 削除種別
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DELETE_SYUBETSU },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 削除種別
  const deleteSyubetsuCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DELETE_SYUBETSU
  )
  if (deleteSyubetsuCodeTypes?.length > 0) {
    const list: ItemStruct[] = []
    for (const item of deleteSyubetsuCodeTypes) {
      if (item) {
        if (OrX0001Const.DEFAULT.STR.ONE === item.value) {
          list.push({
            content: item.label,
            value: item.value,
            remark: t('label.interRAI-method-care-assessment-table-delete-btn1-remark', [
              localOneway.onewayModelValue.selectTabName,
            ]),
          } as ItemStruct)
        } else {
          list.push({
            content: item.label,
            value: item.value,
            remark: t('label.interRAI-method-care-assessment-table-delete-btn2-remark', [
              localOneway.onewayModelValue.startTabName,
              localOneway.onewayModelValue.endTabName,
            ]),
          } as ItemStruct)
        }
      }
    }
    local.items = list
  }
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      local.modelValue.deleteSyubetsu = OrX0001Const.DEFAULT.STR.ZERO
      close()
    }
  }
)

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  emit('update:modelValue', local.modelValue)
  setState({ isOpen: false })
}

/**
 * 選択変更
 *
 * @param value - 選択の文字サイズ
 */
function selectRadioChange(value: string) {
  local.modelValue.deleteSyubetsu = value
}

/**
 * ボタンクリックイベント
 *
 * @param btn - ボタンタイプ
 */
const clickBtn = (btn: string) => {
  if (btn === OrX0001Const.DEFAULT.BTN_YES) {
    local.modelValue.deleteSyubetsu = mo00039OModelValue.value
  } else if (btn === OrX0001Const.DEFAULT.BTN_CANCEL) {
    local.modelValue.deleteSyubetsu = OrX0001Const.DEFAULT.STR.ZERO
  }

  close()
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row no-gutters>
        <c-v-col cols="1">
          <base-at-icon
            icon="help"
            size="50px"
            color="#7f7f7f"
          />
        </c-v-col>
        <c-v-col cols="11">
          <base-at-label :value="localOneway.msg" />
        </c-v-col>
      </c-v-row>
      <c-v-row no-gutters style="margin-top: 8px;">
        <c-v-col cols="1">
          <c-v-spacer />
        </c-v-col>
        <c-v-col cols="11">
          <c-v-row no-gutters>
            <c-v-col cols="12">
              <base-mo00039
                v-model="mo00039OModelValue"
                :oneway-model-value="localOneway.mo00039Oneway"
                @update:model-value="selectRadioChange"
              >
                <div
                  v-for="(item, index) in local.items"
                  :key="index"
                  class="customRadio"
                >
                  <c-v-row no-gutters>
                    <c-v-col cols="1">
                      <base-at-radio
                        name="small"
                        :value="item.value"
                        :radio-label="item.content"
                      >
                      </base-at-radio>
                    </c-v-col>
                    <c-v-col cols="11">
                      <c-v-row no-gutters>
                        <c-v-col cols="12">
                          <c-v-row no-gutters>
                            <c-v-col cols="12">
                              <base-at-label :value="item.content" />
                            </c-v-col>
                          </c-v-row>
                          <c-v-row no-gutters>
                            <c-v-col cols="12">
                              <base-at-label
                                :value="item.remark"
                                class="remarkLabel"
                              />
                            </c-v-col>
                          </c-v-row>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </div>
              </base-mo00039>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- キャンセルボタン -->
      <base-mo00611
        :oneway-model-value="localOneway.mo00611OnewayCancel"
        class="ml-2"
        @click.stop="clickBtn(OrX0001Const.DEFAULT.BTN_CANCEL)"
      />
      <!-- はいボタン -->
      <base-mo00606
        :oneway-model-value="localOneway.mo00606OnewayYes"
        class="ml-2"
        @click.stop="clickBtn(OrX0001Const.DEFAULT.BTN_YES)"
      />
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
.customRadio {
  width: 100%;
  margin-bottom: 16px;

  :deep(.v-label) {
    display: none;
  }

  .remarkLabel {
    color: rgb(var(--v-theme-black-500));
  }
}
</style>
