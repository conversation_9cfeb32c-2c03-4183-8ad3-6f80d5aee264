<script setup lang="ts">
/**
 * Or29829：有機体：GUI00662_［情報収集］画面（服薬状況）画面
 *
 * @description
 * ［情報収集］画面（服薬状況）
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10889Logic } from '../Or10889/Or10889.logic'
import { Or10889Const } from '../Or10889/Or10889.constants'
import { Or29829Const } from './Or29829.constants'
import { useScreenTwoWayBind, useSetupChildProps } from '#imports'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Or30980OnewayType1 } from '~/types/cmn/business/components/Or30980Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
import type { Or29829OnewayType, Or29829Type } from '~/types/cmn/business/components/Or29829Type'
import type { IInfoCollectionDrugSelectOutEntity } from '~/repositories/cmn/entities/InfoCollectionDrugSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo01274OnewayType } from '~/types/business/components/Mo01274Type'
import type { Mo01384OnewayType } from '~/types/business/components/Mo01384Type'
import type { Or10889OnewayType, Or10889Type } from '~/types/cmn/business/components/Or10889Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or29829OnewayType
  uniqueCpId: string
}

const props: Props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/

const { t } = useI18n()

// 子コンポーネント用変数
const or21815 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(0) })
const or10889 = ref({ uniqueCpId: Or10889Const.CP_ID(1) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const contentRef = ref<HTMLDivElement | null>(null)

// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

/**
 * 親画面からの初期値
 */
const or10889Data: Or10889OnewayType = {
  staffId: '1',
}

const Mo0009OnewayModelValue: Mo00009OnewayType = {
  icon: true,
  btnIcon: 'edit_square',
  prependIcon: 'edit_square',
  density: 'compact',
}

const defaultOnewayModelValue: Or29829OnewayType = {
  periodManageFlag: '0',
  deleteFlg: false,
}

const localOneway = reactive({
  or29829Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // アセスメント表タイトル
  mo01338Oneway: {
    value: t('label.info-collection-other-title'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: 'contentTitle',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  or30980Oneway: {
    btnItems: [],
  } as Or30980OnewayType1,
  // 行追加
  mo00611OneWay: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
  } as Mo00611OnewayType,
  // 行複写
  mo00611OneWayCopy: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    disabled: true,
  } as Mo00611OnewayType,
  // 行挿入
  mo00611OneWayInsert: {
    btnLabel: t('btn.insert-row'),
    prependIcon: 'add',
    disabled: true,
  } as Mo00611OnewayType,
  // 行削除
  mo01265OneWay: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    disabled: true,
  } as Mo01265OnewayType,
  mo01274Drugoneway: {
    // デフォルト値の設定
    maxLength: 1024,
  } as Mo01274OnewayType,
  mo01274Volumeoneway: {
    // デフォルト値の設定
    maxLength: 20,
  } as Mo01274OnewayType,
  mo01274Efficacyoneway: {
    // デフォルト値の設定
    maxLength: 100,
  } as Mo01274OnewayType,

  //要援助と判断される場合にレ計画した場合に○(確認) プルダウン選択肢
  mo01384Oneway: {
    items: [],
  } as Mo01384OnewayType,
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue: or29829 } = useScreenTwoWayBind<Or29829Type>({
  cpId: Or29829Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
or29829.value = { takingMedicationType: [], ...or29829.value }

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
})

/**************************************************
 * Emit
 **************************************************/

/**************************************************
 * ウォッチャー
 **************************************************/

/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(() => {
  // 初期情報取得
  void init()
})

/**
 * 初期化
 */
const init = () => {
  // データを取得し初期化
  void initCodes()
}

/**
 *  データを取得し初期化
 */
async function initCodes() {
  // バックエンドAPIから初期情報取得
  const resData: IInfoCollectionDrugSelectOutEntity = await ScreenRepository.select(
    'infoCollectionDrugSelect',
    {}
  )

  if (localOneway.mo01384Oneway.items !== undefined) {
    for (const item of resData.data.drugList) {
      localOneway.mo01384Oneway.items.push({
        title: item.drugKnj,
        value: item.drugId,
      })
    }
  }
}

/**
 * 指定行選択
 *
 * @param index - 選択した行のindex
 */
const selectListRow = (index: number) => {
  selectedItemIndex.value = index
  // 行削除活性
  localOneway.mo01265OneWay.disabled = false
  // 行複写活性
  localOneway.mo00611OneWayCopy.disabled = false
  // 行挿入活性
  localOneway.mo00611OneWayInsert.disabled = false
}

/**
 * 新規行作成
 *
 */
function createNewItem() {
  return {
    yakuzaiId: '',
    drugKnj: {
      value: '',
    },
    ryouKnj: {
      value: '',
    },
    kounouKnj: {
      value: '',
    },
    ass3Id: '',
    ass1Id: '',
    sort: '',
    modifiedCnt: '',
  }
}

/**
 * 行追加ボタン押下
 *
 */
const onAddRowClick = () => {
  //隔週基準年月日
  or29829.value!.takingMedicationType.push({
    yakuzaiId: '',
    drugKnj: {
      value: '',
    },
    ryouKnj: {
      value: '',
    },
    kounouKnj: {
      value: '',
    },
    ass3Id: '',
    ass1Id: '',
    sort: '',
    modifiedCnt: '',
  })
}
/**
 * 行挿入ボタン押下
 *
 */
const onInsertRowClick = () => {
  if (or29829.value!.takingMedicationType.length === 0) {
    // 新規追加する
    const item = createNewItem()
    or29829.value!.takingMedicationType.push(item)
    selectedItemIndex.value = or29829.value!.takingMedicationType.length - 1
  } else {
    // 選んだ行の上に追加する
    const newItem = createNewItem()
    or29829.value!.takingMedicationType.splice(selectedItemIndex.value, 0, newItem)
  }
}

/**
 * 行複写ボタン押下
 *
 */
const onCopyRowClick = () => {
  if (selectedItemIndex.value !== -1) {
    or29829.value!.takingMedicationType.push({
      ...or29829.value!.takingMedicationType[selectedItemIndex.value],
    })
  }
}

/**
 * 行削除ボタン押下
 *
 */
const onDelRowClick = () => {
  if (selectedItemIndex.value !== -1) {
    showOr21814Msg(t('message.i-cmn-10219'))
  }
}

/**
 * 確認メッセージを表示する
 *
 * @param confirmMsg - エラー内容
 */
function showOr21814Msg(confirmMsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: confirmMsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 *  ボタン押下時の処理(Or10889)
 *
 * @param index - 選択した行のindex
 */
function onClickOr10889(index: number) {
  selectedItemIndex.value = index
  // Or10889のダイアログ開閉状態を更新する
  Or10889Logic.state.set({
    uniqueCpId: or10889.value.uniqueCpId,
    state: { isOpen: true },
  })
}
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr10889 = computed(() => {
  // Or10889のダイアログ開閉状態
  return Or10889Logic.state.get(or10889.value.uniqueCpId)?.isOpen ?? false
})

const or10889Type = ref<Or10889Type>({
  drugId: '',
})

/**
 * スクロールバーのスクロール
 *
 * @param event - WheelEvent
 */
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()

  const delta = Math.sign(event.deltaY)

  const contentElement = contentRef.value
  if (!contentElement) return

  const currentScroll = contentElement.scrollTop
  const maxScroll = contentElement.scrollHeight - contentElement.clientHeight

  let newScroll = currentScroll + delta * 50

  if (newScroll < 0) newScroll = 0
  if (newScroll > maxScroll) newScroll = maxScroll

  if (newScroll !== currentScroll) {
    contentElement.scrollTo({
      top: newScroll,
      behavior: 'auto',
    })
  }
}

/**
 * 「薬剤名入力補助アイコン」押下
 */
watch(
  () => or10889Type,
  (newValue) => {
    or29829.value!.takingMedicationType[selectedItemIndex.value].drugKnj.value =
      newValue.value.drugId
  },
  { deep: true }
)

/**
 * 行削除確認ダイアログ
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.secondBtnClickFlg) {
      if (selectedItemIndex.value !== null) {
        or29829.value!.takingMedicationType.splice(selectedItemIndex.value, 1)
        if (or29829.value!.takingMedicationType.length === 0) {
          localOneway.mo01265OneWay.disabled = true
        }
      }
    } else {
      return
    }
  }
)
</script>

<template>
  <c-v-row
    no-gutters
    style="padding: 8px; background-color: white"
  >
    <c-v-col
      cols="12"
      class="h-100"
    >
      <!-- 3ボタン -->
      <c-v-row
        v-if="!props.onewayModelValue.hiddenAction"
        no-gutters
        style="padding-bottom: 8px; border-bottom: 1px solid rgb(224, 224, 224)"
      >
        <c-v-col cols="12">
          <!--行追加-->
          <base-mo00611
            :oneway-model-value="localOneway.mo00611OneWay"
            @click="onAddRowClick"
          />
          <!--行挿入-->
          <base-mo00611
            class="mx-2"
            :oneway-model-value="localOneway.mo00611OneWayInsert"
            @click="onInsertRowClick"
          />
          <!--行複写-->
          <base-mo00611
            :oneway-model-value="localOneway.mo00611OneWayCopy"
            @click="onCopyRowClick"
          />
          <!--行削除-->
          <base-mo01265
            class="mx-2"
            :oneway-model-value="localOneway.mo01265OneWay"
            @click="onDelRowClick"
          />
        </c-v-col>
      </c-v-row>
      <!-- テーブル -->
      <c-v-row class="ma-0">
        <c-v-col class="h-100" style="padding: 8px 0 0 !important;">
          <c-v-row
            no-gutters
            class="or29829-title-row"
            style="width: calc(100% - 15px)"
          >
            <!-- 薬剤名-->
            <c-v-col
              cols="auto"
              class="col-br-border-title col-br-border-first tbl-title-bg col1"
              >{{ t('label.medicine-name') }}</c-v-col
            >
            <!-- 量-->
            <c-v-col class="col-br-border-title tbl-title-bg col2">{{ t('label.volume') }}</c-v-col>
            <!-- 効能-->
            <c-v-col class="col-br-border-title tbl-title-bg col3">{{
              t('label.efficacy')
            }}</c-v-col>
          </c-v-row>
          <!-- body -->
          <div
            ref="contentRef"
            class="or29829-main-div"
          >
            <template
              v-for="(item, index) in or29829!.takingMedicationType"
              :key="index"
            >
              <c-v-row
                no-gutters
                class="or29829-row"
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="selectListRow(index)"
              >
                <!-- 薬剤名 -->
                <c-v-col
                  cols="auto"
                  class="col-br-border col-br-border-first col1"
                  style="justify-content: center"
                >
                  <div style="display: flex">
                    <base-mo01384
                      v-model="or29829!.takingMedicationType[index].drugKnj"
                      :oneway-model-value="localOneway.mo01384Oneway"
                    ></base-mo01384>
                    <c-v-divider
                      vertical
                      class="mr-1 mt-1 mb-1"
                    />
                    <base-mo00009
                      :oneway-model-value="Mo0009OnewayModelValue"
                      variant="flat"
                      density="compact"
                      @click="onClickOr10889(index)"
                    ></base-mo00009>
                  </div>
                </c-v-col>
                <!-- 量 -->
                <c-v-col class="col-br-border col2">
                  <base-mo01274
                    v-model="or29829!.takingMedicationType[index].ryouKnj"
                    :oneway-model-value="localOneway.mo01274Volumeoneway"
                  >
                  </base-mo01274>
                </c-v-col>
                <!-- 効能 -->
                <c-v-col class="col-br-border col3">
                  <base-mo01274
                    v-model="or29829!.takingMedicationType[index].kounouKnj"
                    :oneway-model-value="localOneway.mo01274Efficacyoneway"
                  >
                  </base-mo01274>
                </c-v-col>
              </c-v-row>
            </template>
          </div>
        </c-v-col>
      </c-v-row>
    </c-v-col>
  </c-v-row>
  <div
    v-if="props.onewayModelValue.copyFlg === true"
    class="overlay"
    @wheel="handleWheel"
  ></div>
  <!-- 選択行削除確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
  <g-custom-or-10889
    v-if="showDialogOr10889"
    v-bind="or10889"
    v-model="or10889Type"
    :oneway-model-value="or10889Data"
  />
</template>

<style lang="scss" scoped>
@use '@/styles/base-data-table-list.scss';

:deep(.ml-4) {
  margin-left: 0px !important;
}

/** 行選択の様式 */
.select-row {
  background: rgb(var(--v-theme-blue-100));
}

.tr-show {
  height: 50px;
}

/** タイトルの様式 */
.head-show {
  cursor: default;
  width: 250px;
  position: sticky;
  top: 0;
  z-index: 2;
}

.or29829-title-row {
  height: 32px;
}

.or29829-main-div {
  overflow-y: auto;
  height: 510px;
  scrollbar-gutter: stable;
  position: relative;
  z-index: 999;
}

.or29829-row {
  height: 50px;
}

.col-br-border {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
  height: 50px;
}

.col-br-border-title {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
  border-top: 1px rgb(var(--v-theme-black-200)) solid;
  text-align: center;
}

.col-br-border-first {
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

.tbl-title-bg {
  background-color: rgb(var(--v-theme-black-100));
}

:deep(.col1) {
  flex: 4;
}

:deep(.col2) {
  flex: 2;
}

:deep(.col3) {
  flex: 5;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 1000;
  pointer-events: auto;
  cursor: not-allowed;
}
</style>
