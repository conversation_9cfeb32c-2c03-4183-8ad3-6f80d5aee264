import type { InWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 印刷設定帳票出力入力エンティティ
 */
export interface BenefitStatusListReportEntity extends InWebEntity {
  /**
   * 印刷設定
   */
  printSet: {
    /**
     * 指定日印刷区分
     */
    shiTeiKubun: string
    /**
     * 指定日
     */
    shiTeiDate: string
    /**
     * 順序
     */
    sortFlag: string
    /**
     * 担当ケアマネ
     */
    tantoKeamane: string
    /**
     * 利用票有無
     */
    riyouhyouCheck: string
  }
  /**
   * 適用事務所IDリスト
   */
  jigyoIdList: string[]
  /**
   * 提供年月
   */
  asYymm: string
  /**
   * システム日付
   */
  asYmd: string
  /**
   * 支援事業者ID
   */
  shienId: string
  /**
   * 利用者IDリスト
   */
  useridList: string[]
}
