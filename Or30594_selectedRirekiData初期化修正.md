# Or30594 selectedRirekiData初期化問題の修正

## 問題の概要
Or30594画面初期打開時に、`selectedRirekiData`の値が空になっている問題がありました。

## 原因分析
`selectedRirekiData`は、OrX0143コンポーネントの選択イベントを監視して設定されていましたが、初期化時にOrX0143コンポーネントがまだ選択イベントを発火していないため、値が空のままでした。

### 既存のコード
```typescript
// selectedRirekiDataはOrX0143Logic.eventの監視でのみ設定
watch(
  () => OrX0143Logic.event.get(orX0143.value.uniqueCpId),
  (newValue) => {
    if (!newValue) return
    selectedRirekiData.value = newValue.orX0143DetList as KikanRirekiData[]
  }
)
```

## 修正内容

### 1. 初期選択状態取得関数の追加
```typescript
/**
 * OrX0143の初期選択状態を取得
 */
function getInitialSelectedRirekiData() {
  // OrX0143Logic.eventから現在の選択状態を取得
  const currentEvent = OrX0143Logic.event.get(orX0143.value.uniqueCpId)
  if (currentEvent?.orX0143DetList) {
    selectedRirekiData.value = currentEvent.orX0143DetList as KikanRirekiData[]
  } else {
    // イベントがない場合、rirekiListから初期選択を推測
    const rirekiList = localOneway.orX0143Oneway.rirekiList
    if (rirekiList && rirekiList.length > 0) {
      // 単一モードの場合、最初の有効な行を選択
      if (localOneway.orX0143Oneway.singleFlg === Or30594Const.DEFAULT.TANI) {
        let defaultIndex = 0
        // kikanFlgが'1'の場合は2行目（インデックス1）を選択
        if (localOneway.orX0143Oneway.kikanFlg === '1') {
          defaultIndex = 1
        }
        // 範囲内かつplanPeriodがない行を選択
        if (defaultIndex < rirekiList.length && !rirekiList[defaultIndex].planPeriod) {
          selectedRirekiData.value = [rirekiList[defaultIndex] as KikanRirekiData]
        } else if (rirekiList.length > 0 && !rirekiList[0].planPeriod) {
          selectedRirekiData.value = [rirekiList[0] as KikanRirekiData]
        }
      }
    }
  }
}
```

### 2. 履歴データ設定時の初期化処理追加

#### resetRirekiList関数の修正
```typescript
function resetRirekiList(data: KikanRirekiData[] | OrX0143TableData[]) {
  // 週間表履歴リスト
  localOneway.orX0143Oneway.rirekiList = data.map((x) => {
    const label = localOneway.revisionOneway.items?.find((y) => y.value === x.ikenshoFlg)?.label
    return {
      ...x,
      ikenshoFlg: (label ?? x.ikenshoFlg) as string,
    }
  })
  
  // 履歴データが設定された後、初期選択状態を取得
  void nextTick(() => {
    getInitialSelectedRirekiData()
  })
}
```

#### getPrintSettingList関数の修正
```typescript
async function getPrintSettingList() {
  // ... 既存の処理 ...
  
  // Onewayの期間管理フラグを設定
  localOneway.orX0143Oneway.kikanFlg = localOneway.Or30594.kikanFlg
  localOneway.orX0143Oneway.rirekiList = local.or30594.kikanRirekiList
  
  // 履歴データが設定された後、初期選択状態を取得
  void nextTick(() => {
    getInitialSelectedRirekiData()
  })
  
  // ... 残りの処理 ...
}
```

## 修正のポイント

### 1. 二段階の初期化戦略
- **第一段階**: OrX0143Logic.eventから既存の選択状態を取得
- **第二段階**: イベントがない場合、rirekiListから初期選択を推測

### 2. nextTickの使用
OrX0143コンポーネントの初期化を待つために`nextTick`を使用し、DOM更新後に初期選択状態を取得します。

### 3. 複数の呼び出しポイント
- `resetRirekiList`: 利用者変更時の履歴データ更新
- `getPrintSettingList`: 画面初期化時の履歴データ設定

## 動作仕様

### 初期選択ロジック
1. OrX0143Logic.eventに選択状態がある場合、それを使用
2. イベントがない場合：
   - 単一モードの場合のみ初期選択を設定
   - `kikanFlg='1'`の場合は2行目（インデックス1）
   - それ以外の場合は1行目（インデックス0）
   - `planPeriod`がある行（グループヘッダー）は除外

### 呼び出しタイミング
- 画面初期化時（`getPrintSettingList`）
- 利用者変更時（`resetRirekiList`）
- 履歴データ更新時（`resetRirekiList`）

## 期待される効果
- Or30594画面初期打開時に`selectedRirekiData`が適切な値を持つ
- 印刷処理で必要な履歴情報が正しく取得できる
- ユーザー体験の向上（初期状態でも印刷可能）
