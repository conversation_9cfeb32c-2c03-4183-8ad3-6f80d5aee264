<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import {
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  useValidation,
} from '#imports'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { OrX0158OnewayType, OrX0158Type } from '~/types/cmn/business/components/OrX0158Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
const { t } = useI18n()
const validation = useValidation()
/**************************************************
 * 変数定義
 **************************************************/
// 画面ID
const screenId = 'sampleOrX0158Select'
// ルーティング
const routing = 'sampleOrX0158Select'
// 画面物理名
const screenName = 'サンプル_OrX0158_入力支援付きセレクト'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'sampleOrX0158Select' },
})
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'sampleOrX0158Select',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or21814Const.CP_ID(0) }],
})
// 子コンポーネントのセットアップ
Or21814Logic.initialize(init.childCpIds.Or21814.uniqueCpId)
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
})
// セレクト
const orX0158Oneway = reactive<OrX0158OnewayType>({
  showEditBtnFlg: true,
  showItemLabel: true,
  itemLabel: 'セレクトボックス',
  items: [
    { key: '1', value: 'アイテム１' },
    { key: '2', value: 'アイテム２' },
    { key: '3', value: 'アイテム３' },
  ],
  isRequired: true,
  itemLabelFontWeight: 'bold',
  itemTitle: 'value',
  itemValue: 'key',
  rules: [validation.required],
})

// disabled
const orX0158DisabledOneway = reactive<OrX0158OnewayType>({
  showEditBtnFlg: true,
  showItemLabel: true,
  itemLabel: 'セレクトボックス',
  items: [
    { key: '1', value: 'アイテム１' },
    { key: '2', value: 'アイテム２' },
    { key: '3', value: 'アイテム３' },
  ],
  itemLabelFontWeight: 'bold',
  itemTitle: 'value',
  itemValue: 'key',
  disabled: true,
})

// readonly
const orX0158ReadonlyOneway = reactive<OrX0158OnewayType>({
  showEditBtnFlg: true,
  showItemLabel: true,
  itemLabel: 'セレクトボックス',
  items: [
    { key: '1', value: 'アイテム１' },
    { key: '2', value: 'アイテム２' },
    { key: '3', value: 'アイテム３' },
  ],
  itemLabelFontWeight: 'bold',
  itemTitle: 'value',
  itemValue: 'key',
  readonly: true,
})
// カスタイムスタイル
const orX0158CustomStyleOneway = reactive<OrX0158OnewayType>({
  editBtnClass: 'special-btn',
  showEditBtnFlg: true,
  showItemLabel: true,
  itemLabel: 'セレクトボックス',
  items: [
    { key: '1', value: 'アイテム１' },
    { key: '2', value: 'アイテム２' },
    { key: '3', value: 'アイテム３' },
  ],
  itemLabelFontWeight: 'bold',
  itemTitle: 'value',
  itemValue: 'key',
})
const orX0158Type = ref<OrX0158Type>({
  modelValue: '',
})
const orX0158CustomStyleType = ref<OrX0158Type>({
  modelValue: '',
})
/**
 * 入力補助ボタンクリック時の処理
 */
function onClickEditBtn() {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogText: 'テスト',
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
}
</script>
<template>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/care-manager/mockup-sample/picture-book-list"
        >前のページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- セレクト -->
  <div class="d-flex ma-2">
    <g-custom-or-x-0158
      v-model="orX0158Type"
      :oneway-model-value="orX0158Oneway"
      @on-click-edit-btn="onClickEditBtn"
    ></g-custom-or-x-0158>
    <div>OrX0158Type値：{{ orX0158Type.modelValue }}</div>
  </div>
  <!-- disabledセレクト -->
  <div class="d-flex ma-2">disabled</div>
  <div class="d-flex ma-2">
    <g-custom-or-x-0158
      :oneway-model-value="orX0158DisabledOneway"
      @on-click-edit-btn="onClickEditBtn"
    ></g-custom-or-x-0158>
  </div>
  <!-- readonlyセレクト -->
  <div class="d-flex ma-2">readonly</div>
  <div class="d-flex ma-2">
    <g-custom-or-x-0158
      :oneway-model-value="orX0158ReadonlyOneway"
      @on-click-edit-btn="onClickEditBtn"
    ></g-custom-or-x-0158>
  </div>
  <!-- readonlyセレクト -->
  <div class="d-flex ma-2">カスタイムスタイル</div>
  <div class="d-flex ma-2">
    <g-custom-or-x-0158
      v-model="orX0158CustomStyleType"
      :oneway-model-value="orX0158CustomStyleOneway"
      @on-click-edit-btn="onClickEditBtn"
    ></g-custom-or-x-0158>
  </div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>
<style lang="scss" scoped>
:deep(.special-btn) {
  background: #ebf2fd !important;
  color: red !important;
}
</style>
