import type { InWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 印刷設定帳票出力入力エンティティ
 */
export interface BasicSurveyReportEntity extends InWebEntity {
  /** 基準日 */
  createYmd: string
  /** 帳票タイトル */
  title: string
  /** 計画期間ID */
  sc1Id: string
  /** 調査票ID */
  cschId: string
  /** 利用者ID */
  userId: string
  /** 職員ID */
  shokuId: string
  /** オプション番号 */
  optionNo: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** サービス事業者ID, */
  svJigyoId: string
  /** 事業所名, */
  jigyosha: string
  /** システムコード, */
  syscd: string
  /** システム日付, */
  asYmd: string
  /** 現在日付 */
  stGendateYmd: string
  /** 実施日 */
  appYmd: string
  /** 対象年月 */
  taisyouDate: string

  /**
   * 印刷設定
   */
  printSet: {
    /**
     * 指定日印刷区分
     */
    shiTeiKubun: string
    /**
     * 指定日
     */
    shiTeiDate: string
  }
  /**
   * 印刷オプション
   */
  printOption: {
    /** 記入用シートを印刷するフラグ */
    emptyFlg: string
    /** サービス事業者名印刷フラグ */
    jigyouFlg: string
    /** 空白行印刷フラグ */
    choempFlg: string
    /** 項目凡例印刷フラグ */
    chohanreiFlg: string
    /** 前回結果印刷フラグ */
    zenFlg: string
    /** 利用者名印刷フラグ */
    riyonameFlg: string
    /** 集計する要介護度 */
    stTaisho: string
  }
  /**
   * 印刷設定利用者リスト
   */
  userList: {
    /** 利用者ID */
    id: string
    /** 利用者番号 */
    selfid: string
    /** 氏名（姓） */
    name1knj: string
    /** 氏名（名） */
    name2knj: string
    /** 性別 */
    sex: string
    /** 生年月日 */
    birthdayYmd: string
    /** 集計する要介護度 */
    stTaisho: string
  }[]
}
