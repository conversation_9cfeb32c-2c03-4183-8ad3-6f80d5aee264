<script setup lang="ts">
/**
 * Or26166:有機体:［履歴選択］画面 ﾁｪｯｸ項目
 * GUI00894_［履歴選択］画面 ﾁｪｯｸ項目
 *
 * @description
 * ［履歴選択］画面 ﾁｪｯｸ項目
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch } from 'vue'
import type { RirekiInfoDataListItem, Or26166StateType } from './Or26166.type'
import { Or26166Const } from './Or26166.constants'
import { useScreenOneWayBind } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type {
  rirekiInfoListType,
  Or26166OnewayType,
  Or26166Type,
} from '~/types/cmn/business/components/Or26166Type'
import type {
  HistorySelectScreenCheckItemsEntity,
  HistorySelectScreenCheckItemsOutEntity,
} from '~/repositories/cmn/entities/HistorySelectScreenCheckItemsEntity'
import type { Mo01334Items, Mo01334Type } from '~/types/business/components/Mo01334Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { ResBodyStatusCode } from '~/constants/api-constants'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or26166Type
  onewayModelValue: rirekiInfoListType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const defaultOnewayModelValue: Or26166OnewayType = {
  rirekiInfoList: {
    // 事業者ID
    svJigyoId: '0',
    // 利用者ID
    userId: '0',
    // 計画期間ID
    sc1Id: '1',
    // 履歴ID
    assId: '0',
  },
}

const localOneway = reactive({
  or26166: {
    ...defaultOnewayModelValue.rirekiInfoList,
    ...props.onewayModelValue,
  },
  // 「履歴選択」ダイアログ
  mo00024Oneway: {
    width: '430px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.history-select'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  // 履歴選択情報
  rirekiInfoListMo01334: {
    headers: [
      // 作成日
      {
        title: t('label.create-date'),
        key: 'createYmd',
        width: '110px',
        sortable: false,
      },
      // 作成者
      {
        title: t('label.author'),
        key: 'fullKana',
        width: '180px',
        sortable: false,
      },
      // 様式名
      {
        title: t('label.style-name'),
        key: 'titleKnj',
        width: '90px',
        sortable: false,
      },
    ],
    height: '326px',
    items: [] as Mo01334Items[],
  },
})

// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
}

// 確定ボタン設置
const mo00609Oneway: Mo00609OnewayType = {
  btnLabel: t('btn.confirm'),
  width: '90px',
  tooltipText: t('tooltip.confirm-btn'),
}

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or26166Const.DEFAULT.IS_OPEN,
})

// 履歴選択情報選択行データ設定
const historySelectedItem = ref<Mo01334Type>({
  value: '',
  values: [],
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or26166StateType>({
  cpId: Or26166Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or26166Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: HistorySelectScreenCheckItemsEntity = {
    /**
     * 事業者ID
     */
    svJigyoId: localOneway.or26166.svJigyoId,
    /**
     * 利用者ID
     */
    userId: localOneway.or26166.userId,
    /**
     *計画期間ID
     */
    sc1Id: localOneway.or26166.sc1Id,
  }
  const resData: HistorySelectScreenCheckItemsOutEntity = await ScreenRepository.select(
    'historySelectScreenCheckItemsSelect',
    inputData
  )
  if (resData.statusCode === ResBodyStatusCode.SUCCESS|| resData.statusCode ==='200') {
   const sortedList = resData.data.rirekiInfoList.sort((a, b) => {
      const dateA = new Date(a.createYmd).getTime()
      const dateB = new Date(b.createYmd).getTime()

      if (dateA !== dateB) {
        return dateB - dateA // 作成日 降順
      } else {
        return Number(b.assId) - Number(a.assId) // 履歴ID 降順
      }
    })

    // データ情報設定
    localOneway.rirekiInfoListMo01334.items = sortedList.map((item) => ({
      ...item,
      id: item.assId,
    }))

    // デフォルトでは第一条を選択します
    historySelectedItem.value.value =
      localOneway.or26166.assId ?? localOneway.rirekiInfoListMo01334.items?.[0]?.id
  }
}

/**
 * 履歴情報選択行
 *
 * @param item - 履歴情報選択行
 */
const clickHistorySelectRow = (item: RirekiInfoDataListItem) => {
  historySelectedItem.value.value = item.id
}

/**
 * 履歴情報の行をダブルクリックで選択
 *
 * @param item - 履歴情報選択行
 */
const doubleClickHistorySelectRow = (item: RirekiInfoDataListItem) => {
  historySelectedItem.value.value = item.id
  onConfirmBtn()
}

/**
 * 履歴情報選択行設定様式
 *
 * @param item - 履歴情報選択行
 */
const historyIsSelected = (item: { id: string }) => historySelectedItem.value.value === item.id

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = () => {
  // 履歴データがない場合、操作なし
  if (!historySelectedItem.value) return
  // 選択情報値戻り
  const findItem = localOneway.rirekiInfoListMo01334.items.find(
    (item) => item.id === historySelectedItem.value.value
  )
  emit('update:modelValue', findItem)
  onClickCloseBtn()
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}

watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      // 本画面を閉じる。（AC022と同じ）
      onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            class="table-header"
          >
            <!-- 履歴選択一覧 -->
            <base-mo01334
              v-model="historySelectedItem"
              class="list-wrapper"
              hide-default-footer
              :oneway-model-value="localOneway.rirekiInfoListMo01334"
            >
              <template #item="{ item }">
                <tr
                  style="cursor: default"
                  :class="{ 'selected-row': historyIsSelected(item) }"
                  @click="clickHistorySelectRow(item)"
                  @dblclick="doubleClickHistorySelectRow(item)"
                >
                  <td>
                    <span>{{ item.createYmd }}</span>
                  </td>
                  <td>
                    <span class="tooltip-cell">{{ item.fullKana }}</span>
                    <c-v-tooltip
                      v-if="item.fullKana"
                      activator="parent"
                      location="bottom"
                      :max-width="300"
                      :text="item.fullKana"
                      open-delay="200"
                    />
                  </td>
                  <td>
                    <div class="tooltip-div">
                      <span class="tooltip-cell">{{ item.titleKnj }}</span>
                      <c-v-tooltip
                        v-if="item.titleKnj"
                        activator="parent"
                        location="bottom"
                        :max-width="300"
                        :text="item.titleKnj"
                        open-delay="200"
                      />
                    </div>
                  </td>
                </tr>
              </template>
            </base-mo01334>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="mo00611Oneway"
          class="mr-2"
          @click="onClickCloseBtn"
        >
        </base-mo00611>
        <!-- 確定ボタン-->
        <base-mo00609
          :oneway-model-value="mo00609Oneway"
          @click="onConfirmBtn"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
.tooltip-cell {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tooltip-div {
  width: 90px;
  overflow: hidden;
}
</style>
