<script setup lang="ts">
/**
 * Or30594:有機体:印刷設定モーダル
 * GUI01290_［印刷設定］画面
 *
 * @description
 * ［印刷設定］画面
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { OrX0130TableType } from '../OrX0130/OrX0130.type'
import type { OrX0143TableData } from '../OrX0143/OrX0143.type'
import { OrX0143Logic } from '../OrX0143/OrX0143.logic'
import { Or28780Const } from '../Or28780/Or28780.constants'
import { Or28780Logic } from '../Or28780/Or28780.logic'
import { Or18615Const } from '../Or18615/Or18615.constants'
import { Or18615Logic } from '../Or18615/Or18615.logic'
import { Or26331Const } from '../Or26331/Or26331.constants'
import { Or26331Logic } from '../Or26331/Or26331.logic'
import { Or26326Const } from '../Or26326/Or26326.constants'
import { Or26326Logic } from '../Or26326/Or26326.logic'
import { Or26328Const } from '../Or26328/Or26328.constants'
import { Or26328Logic } from '../Or26328/Or26328.logic'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import type {
  InitMedMasterObj,
  MedicalHistoryInfo,
  Or30594StateType,
  RevisionType,
} from './Or30594.type'
import { useScreenOneWayBind, useSetupChildProps, useScreenUtils } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or30594OnewayType } from '~/types/cmn/business/components/Or30594Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or30594Const } from '~/components/custom-components/organisms/Or30594/Or30594.constants'

import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CustomClass } from '~/types/CustomClassType'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { OrX0143OnewayType } from '~/types/cmn/business/components/OrX0143Type'
import { OrX0143Const } from '~/components/custom-components/organisms/OrX0143/OrX0143.constants'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import type { InWebEntity } from '@/repositories/AbstructWebRepository'
import { reportOutputType } from '~/utils/useReportUtils'
import type { IPrintInfo, PrintSelectInEntity } from '~/repositories/cmn/entities/PrintSelectEntity'
import { usePrint } from '~/utils/usePrint'
import type {
  KikanRirekiData,
  AttendingPhysicianStatementPrintSettingsInitUpdateOutEntity,
} from '~/repositories/cmn/entities/AttendingPhysicianStatementPrintSettingsInitUpdateEntity'
import type { IInitMasterInfo } from '~/repositories/cmn/entities/GyoumuComSelectEntity'
import type {
  AttendingPhysicianStatementPrintSettingsSubjectSelectOutEntity,
  PrtHistoryList,
} from '~/repositories/cmn/entities/AttendingPhysicianStatementPrintSettingsSubjectSelectEntity'
import type { Or26328OnewayType } from '~/types/cmn/business/components/Or26328Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { AttendingPhysicianStatementPrintSettingsInfoUpdateInEntity } from '~/repositories/cmn/entities/AttendingPhysicianStatementPrintSettingsInfoUpdateEntity'
import type { AttendingPhysicianStatementPrintSettingsUserChangeSelectOutEntity } from '~/repositories/cmn/entities/AttendingPhysicianStatementPrintSettingsUserChangeSelectEntity'
import type { PrintUserChangeSelectInEntity } from '~/repositories/cmn/entities/PrintUserChangeSelectEntity'
import type { PrintSubjectSelectInEntity } from '~/repositories/cmn/entities/PrintSubjectSelectEntity'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'

const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()

/************************************************
 * Props
 ************************************************/
interface Props<T extends IInitMasterInfo> {
  onewayModelValue: Or30594OnewayType<T>
  uniqueCpId: string
}

const props = defineProps<Props<InitMedMasterObj>>()

// 引継情報を取得する
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const orX0130 = ref({ uniqueCpId: OrX0130Const.CP_ID(0) })
const orX0117 = ref({ uniqueCpId: OrX0117Const.CP_ID(0) })
const orX0143 = ref({ uniqueCpId: OrX0143Const.CP_ID(0) })
const orX0145 = ref({ uniqueCpId: OrX0145Const.CP_ID(0) })
const or28780 = ref({ uniqueCpId: Or28780Const.CP_ID(0) })
const or18615 = ref({ uniqueCpId: Or18615Const.CP_ID(0) })
const or26331 = ref({ uniqueCpId: Or26331Const.CP_ID(0) })
const or26326 = ref({ uniqueCpId: Or26326Const.CP_ID(0) })
const or26328 = ref({ uniqueCpId: Or26328Const.CP_ID(0) })
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

// 印刷共通処理
const printCom = usePrint()
// 印刷権限
const prtFlg: boolean = await hasPrintAuth()

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [OrX0143Const.CP_ID(0)]: orX0143.value,
  [OrX0145Const.CP_ID(0)]: orX0145.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [Or28780Const.CP_ID(0)]: or28780.value,
  [Or18615Const.CP_ID(0)]: or18615.value,
  [Or26331Const.CP_ID(0)]: or26331.value,
  [Or26326Const.CP_ID(0)]: or26326.value,
  [Or26328Const.CP_ID(0)]: or26328.value,
})

// ダイアログ表示フラグ
const showDialogOrx0117 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * 変数定義
 **************************************************/
// 利用者列幅
const userCols = ref<number>(4)
// 履歴一覧セクションフラゲ
const mo01334TypeHistoryFlag = ref<boolean>(false)
// 選択した利用者データ
const selectedUserList = ref<OrX0130TableType[]>([])
// 選択した履歴データ
const selectedRirekiData = ref<KikanRirekiData[]>([])

// ローカル双方向bind
const local = reactive({
  or30594: {
    // 印刷設定情報リスト
    prtList: [] as IPrintInfo[],
    // 期間履歴情報リスト
    kikanRirekiList: [] as KikanRirekiData[],
    // 期間管理フラグ
    kikanFlag: '',
  },
  // 帳票タイトル
  mo00040: { modelValue: '' } as Mo00040Type,
  // 出力帳票名一覧
  mo01334: {
    value: '',
    values: [],
  } as Mo01334Type,
  // 敬称
  textInput: {
    value: '',
  } as Mo00045Type,
  // 敬称を変更する
  mo00039Type: '',
  // 指定日
  mo00020Type: {
    value: '',
  } as Mo00020Type,
  // 基準日
  mo00020TypeKijunbi: {
    value: systemCommonsStore.getSystemDate!,
  } as Mo00020Type,
  // 帳票タイトル
  titleInput: {
    value: '',
  } as Mo00045Type,
  // 敬称を変更する
  mo00018TypeChangeTitle: {
    modelValue: false,
  } as Mo00018Type,
  // 記入用シートを印刷する
  mo00018TypePrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  // 作成者を印刷する
  mo00018TypePrintAuthor: {
    modelValue: false,
  } as Mo00018Type,
  // 履歴情報を印刷する
  mo00018TypePrintMode: {
    modelValue: false,
  } as Mo00018Type,
  orX0145: {
    value: {} as TantoCmnShokuin,
  } as OrX0145Type,
})

// 担当ケアマネ
local.orX0145.value = {
  counter: Or30594Const.EMPTY,
  chkShokuId: Or30594Const.EMPTY,
  houjinId: Or30594Const.EMPTY,
  shisetuId: Or30594Const.EMPTY,
  svJigyoId: Or30594Const.EMPTY,
  shokuin1Kana: Or30594Const.EMPTY,
  shokuin2Kana: Or30594Const.EMPTY,
  shokuin1Knj: Or30594Const.EMPTY,
  shokuin2Knj: Or30594Const.EMPTY,
  sex: Or30594Const.EMPTY,
  birthdayYmd: Or30594Const.EMPTY,
  zip: Or30594Const.EMPTY,
  kencode: Or30594Const.EMPTY,
  citycode: Or30594Const.EMPTY,
  areacode: Or30594Const.EMPTY,
  addressKnj: Or30594Const.EMPTY,
  tel: Or30594Const.EMPTY,
  kaikeiId: Or30594Const.EMPTY,
  kyuyoKbn: Or30594Const.EMPTY,
  partKbn: Or30594Const.EMPTY,
  inYmd: Or30594Const.EMPTY,
  outYmd: Or30594Const.EMPTY,
  shozokuId: Or30594Const.EMPTY,
  shokushuId: Or30594Const.EMPTY,
  shokuId: Or30594Const.EMPTY,
  timeStmp: Or30594Const.EMPTY,
  delFlg: Or30594Const.EMPTY,
  shokuNumber: Or30594Const.EMPTY,
  caremanagerKbn: Or30594Const.EMPTY,
  shokuType1: Or30594Const.EMPTY,
  shokuType2: Or30594Const.EMPTY,
  kGroupid: Or30594Const.EMPTY,
  bmpPath: Or30594Const.EMPTY,
  bmpYmd: Or30594Const.EMPTY,
  hankoPath: Or30594Const.EMPTY,
  kojinPath: Or30594Const.EMPTY,
  keitaitel: Or30594Const.EMPTY,
  eMail: Or30594Const.EMPTY,
  senmonNo: Or30594Const.EMPTY,
  sgfFlg: Or30594Const.EMPTY,
  srvSekiKbn: Or30594Const.EMPTY,
  shokushuId2: Or30594Const.EMPTY,
  shokushuId3: Or30594Const.EMPTY,
  shokushuId4: Or30594Const.EMPTY,
  shokushuId5: Or30594Const.EMPTY,
  shikakuId1: Or30594Const.EMPTY,
  shikakuId2: Or30594Const.EMPTY,
  shikakuId3: Or30594Const.EMPTY,
  shikakuId4: Or30594Const.EMPTY,
  shikakuId5: Or30594Const.EMPTY,
  kyuseiFlg: Or30594Const.EMPTY,
  kyuseiKana: Or30594Const.EMPTY,
  kyuseiKnj: Or30594Const.EMPTY,
  sort: Or30594Const.EMPTY,
  selfNumber: Or30594Const.EMPTY,
  ichiranShokushuIdNm: Or30594Const.EMPTY,
  shokushuId2Nm: Or30594Const.EMPTY,
  shokushuId3Nm: Or30594Const.EMPTY,
  shokushuId4Nm: Or30594Const.EMPTY,
  shokushuId5Nm: Or30594Const.EMPTY,
  stopFlg: Or30594Const.EMPTY,
  shokuinKnj: '',
  shokuinKana: Or30594Const.EMPTY,
  title: Or30594Const.EMPTY,
  value: Or30594Const.EMPTY,
} as TantoCmnShokuin

// ローカルOneway
const localOneway = reactive({
  // 利用者
  orX0130Oneway: {
    selectMode: Or30594Const.DEFAULT.TANI,
    tableStyle: 'width:270px',
    userId: props.onewayModelValue.userId,
    focusSettingInitial: props.onewayModelValue.gojuuOnKana,
  } as OrX0130OnewayType,
  // 期間履歴
  orX0143Oneway: {
    // 期間管理フラグ
    kikanFlg: '1',
    // 単一複数フラグ(0:単一,1:複数)
    singleFlg: Or30594Const.DEFAULT.TANI,
    tableStyle: 'width:335px',
    kikanRirekiTitleList: [
      // 記入日
      { title: 'entry-date', width: '140', key: 'createYmd' },
      // 医師名
      { title: 'doctorName', width: '120', key: 'ishiNameKnj' },
      // 改訂
      { title: 'revision', width: '70', key: 'ikenshoFlg' },
    ],
    // 履歴情報
    rirekiList: [] as OrX0143TableData[],
  } as OrX0143OnewayType,
  // 主治医意見書の改定フラグ
  revisionOneway: {} as RevisionType,
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  // 印刷ボタン
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  // 日付印刷
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  // 利用者選択
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  // 履歴選択
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  Or30594: {
    ...props.onewayModelValue,
  },
  // 敬称を変更するチェックボックス
  mo00018OneWayChangeTitle: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 敬称テキストボックス
  mo00045OnewayTextInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '65',
    maxLength: '4',
  } as Mo00045OnewayType,
  // 欄外に利用者氏名を印刷する
  mo00018OneWayPrintUserName: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-users-name-on-the-margin'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 記入日を印刷するチェックボックス
  mo00018OneWayPrintDate: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-entry-date'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 記入用シートを印刷するチェックボックス
  mo00018OneWayPrintTheForm: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 印刷設定帳票出力状態リスト
  orX0117Oneway: {
    reportId: '',
    type: '1',
    reportData: {} as InWebEntity,
    outputType: reportOutputType.DOWNLOAD,
    historyList: [] as OrX0117History[],
    replaceKey: 'printHistoryList',
  } as OrX0117OnewayType,
  // 「印刷設定」ダイアログ
  mo00024Oneway: {
    width: 'auto',
    maxWidth: '1420px',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: false,
    mo01344Oneway: {
      toolbarTitle: t('label.print-set'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  },
  // チェックボックス
  mo00018OnewayType: {
    showItemLabel: false,
    checkboxLabel: '',
    isVerticalLabel: true,
    hideDetails: true,
    outerDivClass: '',
  } as Mo00018OnewayType,
  // 出力帳票名一覧
  mo01334Oneway: {
    headers: [
      {
        title: t('label.report'),
        key: 'prtTitle',
        sortable: false,
        minWidth: '100',
      },
    ],
    items: [],
    height: 655,
  } as Mo01334OnewayType,
  // 帳票タイトル
  or26328Oneway: {
    maxLength: '50',
  } as Or26328OnewayType,
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-0' }),
  } as OrX0145OnewayType,
})

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or30594Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/

const { setState } = useScreenOneWayBind<Or30594StateType>({
  cpId: Or30594Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or30594Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子モジュールに値を渡す
setChildrenValue()

/**
 * エラーダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 *
 * @param isError - エラーかどうか
 *
 * @returns ダイアログの選択結果（yes）
 */
const showMessageBox = async (text: string, btn: string, isError = true) => {
  if (isError) {
    return await openErrorDialog(text, btn)
  } else {
    return await openInfoDialog(text)
  }
}

/**
 * ダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 */
function openErrorDialog(text: string, btn: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t(text),
      firstBtnType: 'normal1',
      firstBtnLabel: t(btn) ?? t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)
        let result = Or30594Const.DEFAULT.YES
        if (event?.firstBtnClickFlg) {
          result = Or30594Const.DEFAULT.YES
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログの開閉
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes）
 */
async function openInfoDialog(paramDialogText: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t(paramDialogText),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

onMounted(async () => {
  // 指定日 システム日付
  local.mo00020Type.value = systemCommonsStore.getSystemDate!
  // 汎用コードマスタデータを取得し初期化
  await initCodes()
  // 印刷設定情報リスト
  await getPrintSettingList()
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // 主治医意見書の改定フラグ
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DOCTOR_REVISION_FLAG },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )

  // 利用者選択
  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 履歴選択
  localOneway.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 主治医意見書の改定フラグ
  localOneway.revisionOneway.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DOCTOR_REVISION_FLAG
  )

  // 選択モート 初期値
  localOneway.orX0130Oneway.selectMode = Or30594Const.DEFAULT.TANI
  // 選択モート 単一複数フラグ
  localOneway.orX0143Oneway.singleFlg = Or30594Const.DEFAULT.TANI
  // 履歴一覧セクションフラグ
  mo01334TypeHistoryFlag.value = true
  // 利用者一覧の列数
  userCols.value = 6
}

/**
 * 印刷オプションの有効化設定
 */
function setPrintOptions() {
  const idx = local.or30594.prtList.findIndex((x) => x.prtNo === local.mo01334.value)
  if (idx === 0) {
    // 画面.出力帳票印刷情報リストが一行目を選択した場合、非活性
    // 敬称を変更するチェックボックス
    localOneway.mo00018OneWayChangeTitle.disabled = true
    // 欄外に利用者氏名を印刷する
    localOneway.mo00018OneWayPrintUserName.disabled = true
    // 記入日を印刷する
    localOneway.mo00018OneWayPrintDate.disabled = true
  } else {
    // 以外の場合、活性
    // 敬称を変更するチェックボックス
    localOneway.mo00018OneWayChangeTitle.disabled = false
    // 欄外に利用者氏名を印刷する
    localOneway.mo00018OneWayPrintUserName.disabled = false
    // 記入日を印刷する
    localOneway.mo00018OneWayPrintDate.disabled = false
  }
  // 画面.利用者選択方法が「単一」 、且つ、 画面.履歴選択方法が「単一」の場合、活性
  if (
    localOneway.orX0130Oneway.selectMode === Or30594Const.DEFAULT.TANI &&
    localOneway.orX0143Oneway.singleFlg === Or30594Const.DEFAULT.TANI
  ) {
    // 記入用シートを印刷する
    localOneway.mo00018OneWayPrintTheForm.disabled = false
  }
  // 以外の場合、非活性
  else {
    localOneway.mo00018OneWayPrintTheForm.disabled = true
  }
}

/**
 * 印刷設定画面初期情報を取得する
 */
async function getPrintSettingList() {
  const inputData: PrintSelectInEntity = {
    // セクション名:親画面.セクション名
    sectionName: localOneway.Or30594.sectionName,
    // 職員ID:親画面.職員ID
    shokuId: localOneway.Or30594.shokuId,
    // 法人ID:親画面.法人ID
    houjinId: localOneway.Or30594.houjinId,
    // 施設ID:親画面.施設ID
    shisetuId: localOneway.Or30594.shisetuId,
    // 事業所ID:親画面.事業所ID
    svJigyoId: localOneway.Or30594.svJigyoId,
    // システムコード:共通情報.システムコード
    gsyscd: localOneway.Or30594.systemCode,
    // 利用者ID:親画面.利用者ID
    userId: localOneway.Or30594.userId,
  }

  // バックエンドAPIから初期情報取得
  const ret = await printCom.doPrintGet<
    PrintSelectInEntity,
    IPrintInfo,
    KikanRirekiData,
    AttendingPhysicianStatementPrintSettingsInitUpdateOutEntity
  >(inputData, 'attendingPhysicianStatementPrintSettingsInitUpdate')
  // 印刷設定情報リスト、期間履歴情報リスト、期間管理フラグを取得
  printCom.doGetPrintData<
    AttendingPhysicianStatementPrintSettingsInitUpdateOutEntity,
    IPrintInfo,
    KikanRirekiData
  >(ret, local.or30594)
  // 期間履歴情報リスト
  local.or30594.kikanRirekiList = local.or30594.kikanRirekiList.map((x) => {
    return {
      ...x,
      ikenshoFlg:
        localOneway.revisionOneway.items?.find((y) => y.value === x.ikenshoFlg)?.label ?? '',
    }
  })
  // Onewayの期間管理フラグを設定
  localOneway.orX0143Oneway.kikanFlg = localOneway.Or30594.kikanFlg
  localOneway.orX0143Oneway.rirekiList = local.or30594.kikanRirekiList

  // Onewayの印刷設定情報リストを設定
  const mo01334OnewayList: Mo01334Items[] = []
  for (const item of local.or30594.prtList) {
    if (item) {
      mo01334OnewayList.push({
        id: item.prtNo,
        mo01337OnewayReport: {
          value: item.prtTitle,
          unit: '',
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
    }
  }
  // Onewayの印刷設定情報リストを設定
  localOneway.mo01334Oneway.items = mo01334OnewayList

  if (local.or30594.prtList.length > 0) {
    // 親画面.初期選択セクションが2の場合、二件目選択状態にする
    if (
      localOneway.Or30594.selectLedgerNumber === Or30594Const.DEFAULT.LEDGER_NUMBER_SECOND &&
      local.or30594.prtList.length > 1
    ) {
      local.mo01334.value = local.or30594.prtList[1].prtNo
    } else {
      //親画面.初期選択セクションが1の場合、一件目選択状態にする
      local.mo01334.value = local.or30594.prtList[0].prtNo
    }
  }
  // 印刷オプションの有効化設定
  setPrintOptions()
  // 子モジュールに値を渡す
  setChildrenValue()
}

/**
 * 履歴情報を取得する
 *
 * @param userId - 利用者ID
 */
async function getHistoricalInfoList(userId: string) {
  localOneway.orX0143Oneway.rirekiList = []
  const result = await printCom.doUserClick<
    PrintUserChangeSelectInEntity,
    KikanRirekiData,
    AttendingPhysicianStatementPrintSettingsUserChangeSelectOutEntity
  >(
    {
      // 事業所ＩＤ:親画面.事業所ＩＤ
      svJigyoId: localOneway.Or30594.svJigyoId,
      // 利用者ID:画面.利用者一覧に選択行の利用者ID
      userId: userId,
    },
    'attendingPhysicianStatementPrintSettingsUserChangeSelect'
  )
  // 週間表履歴リスト
  resetRirekiList(result)
}

/**
 * 子モジュールに値を渡す
 *
 * @param data - 週間表履歴リスト
 */
function resetRirekiList(data: KikanRirekiData[] | OrX0143TableData[]) {
  // 週間表履歴リスト
  localOneway.orX0143Oneway.rirekiList = data.map((x) => {
    const label = localOneway.revisionOneway.items?.find((y) => y.value === x.ikenshoFlg)?.label
    return {
      ...x,
      ikenshoFlg: (label ?? x.ikenshoFlg) as string,
    }
  })
}

/**
 * 子モジュールに値を渡す
 *
 */
function setChildrenValue() {
  setChildCpBinds(props.uniqueCpId, {
    // 帳票タイトル
    [Or26328Const.CP_ID(0)]: {
      twoWayValue: {
        titleInput: local.titleInput,
      },
    },
    // 出力帳票名一覧
    [Or26326Const.CP_ID(0)]: {
      twoWayValue: {
        mo01334Type: local.mo01334,
      },
    },
    // 日付印刷
    [Or28780Const.CP_ID(0)]: {
      twoWayValue: {
        mo00039Type: local.mo00039Type,
        mo00020Type: local.mo00020Type,
      },
    },
    // 印刷オプション
    [Or18615Const.CP_ID(0)]: {
      twoWayValue: {
        mo00018TypeChangeTitle: local.mo00018TypeChangeTitle,
        mo00045Type: local.textInput,
      },
    },
    // 利用者選択方法セクション
    [Or26331Const.CP_ID(0)]: {
      twoWayValue: {
        mo00039OneWayUserSelectType: localOneway.orX0130Oneway.selectMode,
        mo00020TypeKijunbi: local.mo00020TypeKijunbi,
        mo00039OneWayHistorySelectType: localOneway.orX0143Oneway.singleFlg,
      },
    },
  })
}

/**
 * 「閉じるボタン」押下
 */
async function close() {
  setPrtList(local.mo01334.value)
  // 画面の印刷設定情報を保存する
  await printCom.doPrintClose(
    local.titleInput,
    {
      // セクション名:親画面.セクション名
      sectionName: localOneway.Or30594.sectionName,
      // システムコード：親画面.システムコード
      gsyscd: localOneway.Or30594.systemCode,
      // 職員ID：親画面.職員ID
      shokuId: localOneway.Or30594.shokuId,
      // 法人ID：親画面.法人ID
      houjinId: localOneway.Or30594.houjinId,
      // 施設ID：親画面.施設ID
      shisetuId: localOneway.Or30594.shisetuId,
      // 事業者ID：親画面.事業者ID
      svJigyoId: localOneway.Or30594.svJigyoId,
      // 印刷設定情報リスト：印刷設定情報リスト
      prtList: local.or30594.prtList,
    },
    'attendingPhysicianStatementPrintSettingsInfoUpdate',
    localOneway.mo01334Oneway.items,
    local.mo01334.value,
    showMessageBox,
    closeDialog
  )
}

/**
 * 本画面を閉じる
 */
function closeDialog() {
  setState({ isOpen: false })
}

/**
 * 「印刷」ボタン押下
 */
async function print() {
  // 業務チェックを行う
  if (
    !printCom.doCheckBeforePrint(
      local.titleInput.value,
      local.mo00018TypePrintTheForm.modelValue,
      selectedUserList.value.length,
      localOneway.orX0143Oneway.rirekiList.length,
      showMessageBox
    )
  ) {
    return
  }

  setPrtList(local.mo01334.value)
  // PDFダウンロード
  await executePrint()
}

/**
 * PDFダウンロード
 */
async function executePrint() {
  // 利用者配下の履歴情報を再取得する
  const resSubjectData = await printCom.doRetryRirekiData<
    PrintSubjectSelectInEntity,
    PrtHistoryList,
    AttendingPhysicianStatementPrintSettingsSubjectSelectOutEntity
  >(
    {
      // 利用者リスト：画面.利用者一覧で選択した利用者リスト
      userList: selectedUserList.value.map((x) => {
        return {
          userId: x.selfId,
          userName: x.name1Knj + ' ' + x.name2Knj,
        }
      }),
      // 事業所ID：親画面.事業所ID
      svJigyoId: localOneway.Or30594.svJigyoId,
      // 基準日：画面.基準日
      kijunbiYmd: local.mo00020TypeKijunbi.value,
    },
    localOneway.orX0130Oneway.selectMode,
    'attendingPhysicianStatementPrintSettingsSubjectSelect'
  )

  const kaiteiFlgList: string[] = []
  // 利用者選択が「単一」の場合
  if (localOneway.orX0130Oneway.selectMode === Or30594Const.DEFAULT.TANI) {
    // 画面.記入用シートを印刷するチェックボックスがチェックOFFの場合
    if (!local.mo00018TypePrintTheForm.modelValue) {
      // 履歴一覧選択した改訂フラグを追加する。
      kaiteiFlgList.push(revertIkenshoFlg(selectedRirekiData.value[0].ikenshoFlg))
    }
    // 以外の場合
    else {
      // 履歴一覧データの件数が0より大きい場合
      if (local.or30594.kikanRirekiList.length > 0) {
        // 履歴一覧ですべて選択した改訂フラグを追加する
        selectedRirekiData.value.forEach((x) => {
          kaiteiFlgList.push(revertIkenshoFlg(x.ikenshoFlg))
        })
      }
      // 以外の場合
      else {
        // 親画面.初期設定マスタの情報.改訂フラグを追加する
        kaiteiFlgList.push(localOneway.Or30594.initMasterObj.kaiteiFlg)
      }
    }
  }
  // 利用者選択が「複数」の場合
  else {
    // AC018-2で取得した改定フラグリストの件数が0より大きい場合
    if (resSubjectData.length > 0) {
      // AC018-2で取得した改定フラグリストを設定する。
      resSubjectData.forEach((x) => {
        // ※重複を排除
        const kaiteiFlg = revertIkenshoFlg(x.ikenshoFlg)
        if (!kaiteiFlgList.includes(kaiteiFlg)) {
          kaiteiFlgList.push(revertIkenshoFlg(x.ikenshoFlg))
        }
      })
    }
  }

  const inputData: AttendingPhysicianStatementPrintSettingsInfoUpdateInEntity<IPrintInfo> = {
    // セクション名:親画面.セクション名
    sectionName: localOneway.Or30594.sectionName,
    // システムコード：親画面.システムコード
    gsyscd: localOneway.Or30594.systemCode,
    // 職員ID：親画面.職員ID
    shokuId: localOneway.Or30594.shokuId,
    // 法人ID：親画面.法人ID
    houjinId: localOneway.Or30594.houjinId,
    // 施設ID：親画面.施設ID
    shisetuId: localOneway.Or30594.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.Or30594.svJigyoId,
    // 選択帳票番号：印刷設定情報リスト選択行.帳票番号
    selectedprtNo: local.mo01334.value,
    // 選択プロファイル：印刷設定情報リスト選択行.プロファイル
    selectedprofile:
      local.or30594.prtList.find((x) => x.prtNo === local.mo01334.value)?.profile ?? '',
    // 改訂フラグリスト
    kaiteiFlgList: kaiteiFlgList,
    // 印刷設定情報リスト：印刷設定情報リスト
    prtList: local.or30594.prtList,
  }
  // 印刷設定情報を保存する
  await printCom.doPrintUpdate(
    inputData,
    'attendingPhysicianStatementPrintSettingsInfoUpdate',
    showMessageBox
  )
  const selectedIndex = local.or30594.prtList.findIndex((x) => x.prtNo === local.mo01334.value)
  if (selectedIndex > -1) {
    const selectedPrtData = local.or30594.prtList[selectedIndex]
    const printIdList: string[] = []
    // 利用者選択が「単一」の場合
    if (localOneway.orX0130Oneway.selectMode === Or30594Const.DEFAULT.TANI) {
      // 画面.記入用シートを印刷するチェックボックスがチェックOFFの場合
      if (!local.mo00018TypePrintTheForm.modelValue) {
        // 履歴一覧選択した改訂フラグが3:R3／4の場合
        selectedRirekiData.value.forEach((x) => {
          if (x.ikenshoFlg === '3') {
            resetPrintId(printIdList, selectedIndex)
          }
        })
      }
      // 以外の場合
      else {
        // 履歴一覧データの件数が0より大きい場合
        if (localOneway.orX0143Oneway.rirekiList.length > 0) {
          // 履歴一覧選択した改訂フラグが3:R3／4の場合
          selectedRirekiData.value.forEach((x) => {
            if (x.ikenshoFlg === '3') {
              resetPrintId(printIdList, selectedIndex)
            }
          })
        }
        // 以外の場合
        else {
          // 親画面.初期設定マスタの情報.主治医意見書改訂フラグが3:R3／4の場合
          if (localOneway.Or30594.initMasterObj.kaiteiFlg === '3') {
            // 帳票一覧が一行目選択した場合、"attendingPhysician1R3Report"に設定する
            if (selectedIndex === 0) {
              selectedPrtData.prtId = Or30594Const.PRINT_ID_ONE
            }
            // 帳票一覧が二行目選択した場合、"attendingPhysician2R3Report"に設定する
            else if (selectedIndex === 1) {
              selectedPrtData.prtId = Or30594Const.PRINT_ID_TWO
            }
            // 帳票一覧が三行目選択した場合、"attendingPhysicianAllR3Report"に設定する
            else if (selectedIndex === 2) {
              selectedPrtData.prtId = Or30594Const.PRINT_ID_THREE
            }
          }
        }
      }
    }
    // 利用者選択が「複数」の場合
    else {
      // 取得した印刷対象履歴リストの件数が0より大きい場合
      if (resSubjectData.length) {
        resSubjectData.forEach((x) => {
          // 取得した印刷対象履歴リスト.改訂フラグが3:R3／4の場合
          if (x.ikenshoFlg === '3') {
            resetPrintId(printIdList, selectedIndex)
          }
        })
      }
    }

    // 利用者選択が「単一」、且つ、履歴選択が「単一」の場合
    if (
      localOneway.orX0130Oneway.selectMode === Or30594Const.DEFAULT.TANI &&
      localOneway.orX0143Oneway.singleFlg === Or30594Const.DEFAULT.TANI
    ) {
      void printCom.doReportOutput(
        { ...selectedPrtData, prtId: printIdList[0] ? printIdList[0] : selectedPrtData.prtId },
        // 印刷対象履歴
        {
          userId: selectedUserList.value[0].selfId,
          rirekiId: selectedRirekiData.value[0].rirekiId,
          // 記入日
          createYmd: local.mo00018TypePrintTheForm.modelValue
            ? selectedRirekiData.value[0].createYmd
            : '',
        },
        {
          // 初期設定マスタの情報
          initMasterObj: localOneway.Or30594.initMasterObj,
          // 事業所名
          jigyoKnj: localOneway.Or30594.jigyoKnj,
        },
        {
          // 指定日
          // 印刷設定情報リスト選択行.日付表示有無が2の場合、画面の指定日
          selectDate: selectedPrtData.prnDate === '2' ? local.mo00020Type.value : '',
          // 記入用シートを印刷するフラグ
          emptyFlg: local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
        },
        // システム日付
        localOneway.Or30594.sysYmd ?? '',
        printIdList[0]
      )
    } else {
      const historyData: MedicalHistoryInfo[] = []
      if (localOneway.orX0130Oneway.selectMode === Or30594Const.DEFAULT.TANI) {
        selectedRirekiData.value.forEach((x) => {
          historyData.push({
            userId: selectedUserList.value[0].selfId,
            rirekiId: x.rirekiId,
            // 記入日
            createYmd: local.mo00018TypePrintTheForm.modelValue ? x.createYmd : '',
          })
        })
      } else {
        resSubjectData.forEach((x) => {
          historyData.push({
            userId: x.userId,
            rirekiId: x.rirekiId,
            // 記入日
            createYmd: '',
          })
        })
      }
      // 画面.印刷対象履歴リストを作成する
      printCom.doHukusuuSetting(
        selectedPrtData,
        historyData,
        localOneway.orX0117Oneway,
        localOneway.orX0130Oneway.selectMode,
        selectedUserList.value,
        {
          // 初期設定マスタの情報
          initMasterObj: localOneway.Or30594.initMasterObj,
          // 事業所名
          jigyoKnj: localOneway.Or30594.jigyoKnj,
        },
        {
          // 指定日
          // 印刷設定情報リスト選択行.日付表示有無が2の場合、画面の指定日
          selectDate: selectedPrtData.prnDate === '2' ? local.mo00020Type.value : '',
          // 記入用シートを印刷するフラグ
          emptyFlg: local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
        },
        // システム日付
        localOneway.Or30594.sysYmd ?? '',
        resSubjectData,
        selectedRirekiData.value,
        printIdList
      )
      // PDFダウンロードを行う
      OrX0117Logic.state.set({
        uniqueCpId: orX0117.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
    }
  }
}

/**
 * 帳票ID再設定処理
 *
 * @param printIdList - 帳票IDリスト
 *
 * @param selectedIndex - 帳票一覧の選択行インデックス
 */
function resetPrintId(printIdList: string[], selectedIndex: number) {
  // 帳票一覧が一行目選択した場合、"attendingPhysician1R3Report"に設定する
  if (selectedIndex === 0) {
    printIdList.push(Or30594Const.PRINT_ID_ONE)
  }
  // 帳票一覧が二行目選択した場合、"attendingPhysician2R3Report"に設定する
  else if (selectedIndex === 1) {
    printIdList.push(Or30594Const.PRINT_ID_TWO)
  }
  // 帳票一覧が三行目選択した場合、"attendingPhysicianAllR3Report"に設定する
  else if (selectedIndex === 2) {
    printIdList.push(Or30594Const.PRINT_ID_THREE)
  }
}

/**
 * 改定フラグ値設定
 *
 * @param ikenshoFlg - 改定フラグ
 */
function revertIkenshoFlg(ikenshoFlg: string) {
  return (
    localOneway.revisionOneway.items?.find((x) => x.label === ikenshoFlg)?.value.toString() ?? ''
  )
}

/**
 * 印刷設定情報リストの値を設定する
 *
 * @param index - 印刷設定情報リストのインデックス
 */
function setPrtList(index: string) {
  // 印刷設定情報リストのインデックス
  const idx = local.or30594.prtList.findIndex((x) => x.prtNo === index)
  if (idx !== -1) {
    // 選択された印刷設定情報リストのデータ
    const selectedData = local.or30594.prtList[idx]
    // 帳票タイトル名
    selectedData.prtTitle = local.titleInput.value
    // 日付表示有無
    selectedData.prnDate = local.mo00039Type
    // 敬称を変更する
    selectedData.param03 = local.mo00018TypeChangeTitle.modelValue
      ? Or30594Const.DEFAULT.CHECK_ON
      : Or30594Const.DEFAULT.CHECK_OFF
    // 敬称
    selectedData.param04 = local.textInput.value
    // 作成者を印刷する
    selectedData.param05 = local.mo00018TypePrintAuthor.modelValue
      ? Or30594Const.DEFAULT.CHECK_ON
      : Or30594Const.DEFAULT.CHECK_OFF
    // 履歴情報を印刷する
    selectedData.param06 = local.mo00018TypePrintMode.modelValue
      ? Or30594Const.DEFAULT.CHECK_ON
      : Or30594Const.DEFAULT.CHECK_OFF
  }
}

/**
 * 「出力帳票名」選択
 */
watch(
  () => Or26326Logic.data.get(or26326.value.uniqueCpId)?.mo01334Type.value,
  (newValue, oldValue) => {
    if (newValue) {
      local.mo01334.value = newValue
      setPrintOptions()
    }
    if (oldValue) {
      for (const item of localOneway.mo01334Oneway.items) {
        if (item) {
          if (oldValue === item.id) {
            // 画面.帳票タイトルが空白以外の場合
            // 画面.出力帳票一覧明細に切替前選択される行.帳票タイトル = 画面.帳票タイトル
            if (local.titleInput.value) item.prtTitle = local.titleInput.value
            // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
            item.prnDate = local.mo00039Type
            // 指定日
            item.mo00020Type = local.mo00020Type.value ?? systemCommonsStore.getSystemDate!
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ03 = 画面.敬称を変更する
            item.param03 = local.mo00018TypeChangeTitle.modelValue
              ? Or30594Const.DEFAULT.CHECK_ON
              : Or30594Const.DEFAULT.CHECK_OFF
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ04 = 画面.敬称
            item.param04 = local.textInput.value
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ05 = 画面.欄外に利用者氏名を印刷する
            item.param05 = local.mo00018TypePrintAuthor.modelValue
              ? Or30594Const.DEFAULT.CHECK_ON
              : Or30594Const.DEFAULT.CHECK_OFF
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ06 = 画面.記入日を印刷する
            item.param06 = local.mo00018TypePrintMode.modelValue
              ? Or30594Const.DEFAULT.CHECK_ON
              : Or30594Const.DEFAULT.CHECK_OFF
            // 画面.基準日
            item.mo00020TypeKijunbi = local.mo00020TypeKijunbi.value
            // 画面.記入用シートを印刷する
            item.mo00018TypePrintTheForm = local.mo00018TypePrintTheForm.modelValue
            setPrtList(oldValue)
          }
        }
      }
    }
    for (const item of localOneway.mo01334Oneway.items) {
      if (item) {
        if (newValue === item.id) {
          // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
          local.titleInput.value = item?.prtTitle as string
          // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
          local.mo00039Type = item?.prnDate as string
          // 指定日
          local.mo00020Type = {
            value: (item?.mo00020Type as string) ?? systemCommonsStore.getSystemDate!,
          } as Mo00020Type
          // 画面.敬称を変更する = 画面.出力帳票一覧明細に選択される行.パラメータ03
          local.mo00018TypeChangeTitle.modelValue = item?.param03 === Or30594Const.DEFAULT.CHECK_ON
          // 画面.敬称 = 画面.出力帳票一覧明細に選択される行.パラメータ04
          local.textInput.value = item?.param04 as string
          // 画面.記入用シートを印刷する
          local.mo00018TypePrintTheForm.modelValue = item?.mo00018TypePrintTheForm as boolean
          // 画面.欄外に利用者氏名を印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ05
          local.mo00018TypePrintAuthor.modelValue = item?.param05 === Or30594Const.DEFAULT.CHECK_ON
          // 画面.画面.記入日を印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ06
          local.mo00018TypePrintMode.modelValue = item?.param06 === Or30594Const.DEFAULT.CHECK_ON
          // 画面.基準日
          local.mo00020TypeKijunbi.value = (item?.mo00020TypeKijunbi ??
            systemCommonsStore.getSystemDate!) as string
          setPrtList(newValue)
        }
      }
    }
    setChildrenValue()
  }
)

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
      setChildrenValue()
    }
  }
)

/**
 * 履歴選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331.value.uniqueCpId)?.mo00039OneWayHistorySelectType,
  (newValue) => {
    if (newValue) {
      // 履歴選択方法が「単一」の場合
      if (newValue === Or30594Const.DEFAULT.TANI) {
        // 単一複数フラグ
        localOneway.orX0143Oneway.singleFlg = OrX0143Const.DEFAULT.TANI
        localOneway.orX0117Oneway.type = '1'
      }
      if (newValue === Or30594Const.DEFAULT.HUKUSUU) {
        // 単一複数フラグ
        localOneway.orX0143Oneway.singleFlg = OrX0143Const.DEFAULT.HUKUSUU
        localOneway.orX0117Oneway.type = '0'
        // 画面.記入用シートを印刷するをチェックオフ、非活性にする
        local.mo00018TypePrintTheForm.modelValue = false
      }
      // 印刷オプションの有効化設定
      setPrintOptions()
      // 子モジュールに値を渡す
      setChildrenValue()
    }
  }
)

/**
 * 利用者選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331.value.uniqueCpId)?.mo00039OneWayUserSelectType,
  (newValue) => {
    if (newValue) {
      if (newValue === Or30594Const.DEFAULT.TANI) {
        // 選択モート
        localOneway.orX0130Oneway.selectMode = Or30594Const.DEFAULT.TANI
        localOneway.orX0143Oneway.singleFlg = OrX0143Const.DEFAULT.TANI
        userCols.value = 6
        mo01334TypeHistoryFlag.value = true
        localOneway.orX0117Oneway.type = '1'
      }
      if (newValue === Or30594Const.DEFAULT.HUKUSUU) {
        localOneway.orX0143Oneway.singleFlg =
          localOneway.orX0143Oneway.singleFlg === OrX0143Const.DEFAULT.HUKUSUU
            ? OrX0143Const.DEFAULT.TANI
            : OrX0143Const.DEFAULT.HUKUSUU
        localOneway.orX0117Oneway.type = '0'
        // 選択モート
        localOneway.orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
        // 画面.記入用シートを印刷するをチェックオフ、非活性にする
        local.mo00018TypePrintTheForm.modelValue = false
        userCols.value = 11
        mo01334TypeHistoryFlag.value = false
      }
      // 印刷オプションの有効化設定
      setPrintOptions()
      // 子モジュールに値を渡す
      setChildrenValue()
    }
  }
)

/**
 * 「履歴一覧」明細選択の監視
 */
watch(
  () => OrX0143Logic.event.get(orX0143.value.uniqueCpId),
  (newValue) => {
    if (!newValue) return
    // 履歴一覧で選択したデータ
    selectedRirekiData.value = newValue.orX0143DetList as KikanRirekiData[]
  }
)

/**
 * 日付印刷の監視
 */
watch(
  () => Or28780Logic.data.get(or28780.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // 日付印刷区分
      local.mo00039Type = newValue.mo00039Type
      // 指定日
      local.mo00020Type = newValue.mo00020Type
    }
  }
)

/**
 * 帳票タイトルの監視
 */
watch(
  () => Or26328Logic.data.get(or26328.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // タイトル
      local.titleInput = newValue.titleInput
    }
  }
)

/**
 * 主治医意見書の改定フラグの監視
 */
watch(
  () => localOneway.revisionOneway.items,
  (newValue) => {
    if (newValue) {
      // 改訂フラグをラベルに変換
      resetRirekiList(localOneway.orX0143Oneway.rirekiList)
    }
  }
)

/**
 * 担当ケアマネの監視
 */
watch(
  () => local.orX0145,
  (newValue) => {
    if (newValue) {
      localOneway.orX0130Oneway.tantouCareManager = (newValue.value as TantoCmnShokuin).shokuinKnj
    }
  }
)

/**
 * 印刷オプションの監視
 */
watch(
  () => Or18615Logic.data.get(or18615.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // 敬称を変更する
      local.mo00018TypeChangeTitle = newValue.mo00018TypeChangeTitle
      // 敬称テキスト
      local.textInput = newValue.mo00045Type
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    selectedUserList.value = newValue!.userList
    if (newValue?.clickFlg && localOneway.orX0130Oneway.selectMode === Or30594Const.DEFAULT.TANI) {
      // 利用者選択
      // 利用者選択方法が「単一」の場合
      await getHistoricalInfoList(newValue.userList[0].userId)
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or30594_screen"
      >
        <!-- 帳票 -->
        <g-custom-or-26326
          v-bind="or26326"
          :oneway-model-value="localOneway.mo01334Oneway"
        />
        <c-v-col
          cols="12"
          sm="4"
          class="pa-0 pt-2 or30594_border_right content_center"
        >
          <!-- タイトル -->
          <g-custom-or-26328
            v-bind="or26328"
            :oneway-model-value="localOneway.or26328Oneway"
            class="pb-2"
          />
          <!-- 日付印刷 -->
          <g-custom-or-28780
            v-bind="or28780"
            :oneway-model-value="localOneway.mo00039OneWay"
          />

          <!-- 印刷オプション -->
          <g-custom-or-18615
            v-bind="or18615"
            :oneway-model-value="{
              mo00018OneWayChangeTitle: localOneway.mo00018OneWayChangeTitle,
              mo00045OnewayTextInput: localOneway.mo00045OnewayTextInput,
            }"
          >
            <template #optionPrintItems>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 記入用シートを印刷するチェックボックス -->
                <base-mo00018
                  v-model="local.mo00018TypePrintTheForm"
                  :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
                >
                </base-mo00018>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 欄外に利用者氏名を印刷するチェックボックス -->
                <base-mo00018
                  v-model="local.mo00018TypePrintAuthor"
                  :oneway-model-value="localOneway.mo00018OneWayPrintUserName"
                >
                </base-mo00018>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 記入日を印刷するチェックボックス -->
                <base-mo00018
                  v-model="local.mo00018TypePrintMode"
                  :oneway-model-value="localOneway.mo00018OneWayPrintDate"
                >
                </base-mo00018>
              </c-v-col>
            </template>
          </g-custom-or-18615>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          class="pa-2"
        >
          <c-v-row
            class="or30594_row"
            no-gutter
            style="align-items: center"
          >
            <!-- 利用者選択 -->
            <g-custom-or-26331
              v-bind="or26331"
              :oneway-model-value="{
                mo00039OneWayUserSelectType: localOneway.mo00039OneWayUserSelectType,
                mo00039OneWayHistorySelectType: localOneway.mo00039OneWayHistorySelectType,
              }"
              :unique-cp-id="or26331.uniqueCpId"
            />
            <!-- 担当ケアマネラベル -->
            <g-custom-or-x-0145
              v-bind="orX0145"
              v-model="local.orX0145"
              :oneway-model-value="localOneway.orX0145Oneway"
            ></g-custom-or-x-0145>
          </c-v-row>
          <c-v-row
            class="or30594_row grid-width"
            no-gutter
          >
            <c-v-col
              :cols="userCols"
              style="overflow-x: auto"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="localOneway.orX0130Oneway.selectMode"
                v-bind="orX0130"
                :oneway-model-value="localOneway.orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag"
              cols="12"
              sm="6"
              class="pl-2 pr-0"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0143
                v-if="localOneway.orX0143Oneway.singleFlg"
                v-bind="orX0143"
                :oneway-model-value="localOneway.orX0143Oneway"
              ></g-custom-or-x-0143>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mr-2"
          :disabled="prtFlg"
          @click="print()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  >
  </g-base-or21813>
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrx0117"
    v-bind="orX0117"
    :oneway-model-value="localOneway.orX0117Oneway"
  ></g-custom-or-x-0117>
</template>

<style scoped lang="scss">
.or30594_screen {
  margin: -8px !important;
}

.or30594_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or30594_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.customCol {
  margin-left: 0px;
  margin-right: 0px;
}

.grid-width {
  min-width: 694px;
}
</style>
