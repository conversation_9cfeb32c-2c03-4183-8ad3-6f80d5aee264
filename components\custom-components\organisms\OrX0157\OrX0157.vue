<script setup lang="ts">
/**
 * OrX0157:有機体:入力補助付きテキストフィールド
 *
 * @description
 * 入力補助付きテキストフィールド
 *
 * <AUTHOR>
 */
import { reactive, ref, watch } from 'vue'
import { OrX0157Const } from './OrX0157.constants'
import { convertMap } from '~/constants/KanaMap'
import type {
  OrX0157InputOnewayType,
  OrX0157OnewayType,
  OrX0157Type,
} from '~/types/cmn/business/components/OrX0157Type'
import { CustomClass } from '~/types/CustomClassType'

/**************************************************
 * Props
 **************************************************/

interface Props {
  modelValue?: OrX0157Type
  onewayModelValue?: OrX0157OnewayType
}

const props = defineProps<Props>()

const defaultModelValue = {
  orX0157: {
    value: '',
    emitType: 'blank',
  } as OrX0157Type,
}
const defaultOrX0157InputOneway = {
  itemLabel: '',
  isRequired: false,
  isVerticalLabel: true,
  showItemLabel: false,
  customClass: new CustomClass({ outerClass: 'mr-2', labelClass: 'ma-2' }),
  width: '',
  hint: '',
  hintTooltipText: '',
  itemLabelFontWeight: 'normal',
  appendLabel: '',
  hideDetails: 'auto',
}

const defaultOnewayModelValue = {
  orX0157Oneway: {
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        ...defaultOrX0157InputOneway,
      } as OrX0157InputOnewayType,
    },
    numeric: {
      orX0157InputOneway: {
        ...defaultOrX0157InputOneway,
        class: 'right-input',
        inputmode: 'numeric',
      } as OrX0157InputOnewayType,
      isEditCamma: true,
      isEditPeriod: false,
      periodSortDigit: 2,
      rules: undefined,
      showSpinBtn: false,
      disalbeSpinBtnDefaultProcessing: false,
      hideDetails: 'auto',
    },
    halfWidth: {
      orX0157InputOneway: {
        ...defaultOrX0157InputOneway,
        width: '200px',
      } as OrX0157InputOnewayType,
      customClass: new CustomClass({}),
      hideDetails: 'auto',
      mode: '2',
    },
  } as OrX0157OnewayType,
}

/**************************************************
 * 変数
 **************************************************/
const local = reactive({
  orX0157: {
    ...defaultModelValue.orX0157,
    ...props.modelValue,
  } as OrX0157Type,
})

const isFocus = ref<boolean>(false)

const localOneway = reactive({
  orX0157Oneway: {
    ...defaultOnewayModelValue.orX0157Oneway,
    ...props.onewayModelValue,
    text: {
      orX0157InputOneway: {
        ...defaultOnewayModelValue.orX0157Oneway?.text?.orX0157InputOneway,
        ...props.onewayModelValue?.text?.orX0157InputOneway,
        customClass: {
          ...defaultOnewayModelValue.orX0157Oneway?.text?.orX0157InputOneway.customClass,
          ...props.onewayModelValue?.text?.orX0157InputOneway.customClass,
        } as CustomClass,
      },
    },
    halfWidth: {
      orX0157InputOneway: {
        ...defaultOnewayModelValue.orX0157Oneway?.halfWidth?.orX0157InputOneway,
        ...props.onewayModelValue?.halfWidth?.orX0157InputOneway,
        customClass: {
          ...defaultOnewayModelValue.orX0157Oneway?.halfWidth?.orX0157InputOneway.customClass,
          ...props.onewayModelValue?.halfWidth?.orX0157InputOneway.customClass,
        } as CustomClass,
      },
    },
    numeric: {
      orX0157InputOneway: {
        ...defaultOnewayModelValue.orX0157Oneway?.numeric?.orX0157InputOneway,
        ...props.onewayModelValue?.numeric?.orX0157InputOneway,
        customClass: {
          ...defaultOnewayModelValue.orX0157Oneway?.numeric?.orX0157InputOneway.customClass,
          ...props.onewayModelValue?.numeric?.orX0157InputOneway.customClass,
        } as CustomClass,
      },
    },
  } as OrX0157OnewayType,
})

/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => props.modelValue,
  (newValue) => {
    local.orX0157 = {
      ...defaultModelValue.orX0157,
      ...newValue,
    }
  },
  { deep: true }
)
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.orX0157Oneway = {
      ...defaultOnewayModelValue.orX0157Oneway,
      ...newValue,
      text: {
        orX0157InputOneway: {
          ...defaultOnewayModelValue.orX0157Oneway?.text?.orX0157InputOneway,
          ...newValue?.text?.orX0157InputOneway,
          customClass: {
            ...defaultOnewayModelValue.orX0157Oneway?.text?.orX0157InputOneway.customClass,
            ...newValue?.text?.orX0157InputOneway.customClass,
          } as CustomClass,
        },
      },
      halfWidth: {
        orX0157InputOneway: {
          ...defaultOnewayModelValue.orX0157Oneway?.halfWidth?.orX0157InputOneway,
          ...newValue?.halfWidth?.orX0157InputOneway,
          customClass: {
            ...defaultOnewayModelValue.orX0157Oneway?.halfWidth?.orX0157InputOneway.customClass,
            ...newValue?.halfWidth?.orX0157InputOneway.customClass,
          } as CustomClass,
        },
      },
      numeric: {
        orX0157InputOneway: {
          ...defaultOnewayModelValue.orX0157Oneway?.numeric?.orX0157InputOneway,
          ...newValue?.numeric?.orX0157InputOneway,
          customClass: {
            ...defaultOnewayModelValue.orX0157Oneway?.numeric?.orX0157InputOneway.customClass,
            ...newValue?.numeric?.orX0157InputOneway.customClass,
          } as CustomClass,
        },
      },
    }
  },
  { deep: true }
)

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['blur', 'update:modelValue', 'onClickEditBtn'])
watch(
  () => local.orX0157.value,
  () => {
    if (localOneway.orX0157Oneway.text?.orX0157InputOneway?.errorMessages) {
      localOneway.orX0157Oneway.text.orX0157InputOneway.errorMessages = undefined
    } else if (localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.errorMessages) {
      localOneway.orX0157Oneway.halfWidth.orX0157InputOneway.errorMessages = undefined
    } else if (localOneway.orX0157Oneway.numeric?.orX0157InputOneway.errorMessages) {
      localOneway.orX0157Oneway.numeric.orX0157InputOneway.errorMessages = undefined
    }
    emit('update:modelValue', local.orX0157)
  }
)
/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * ブラー時の処理
 *
 * @description
 *  テキストフィールドの値が全角文字を含んでいるか判定
 *  全角文字があればモードによって処理する
 */
function onBlurEvent() {
  if (local.orX0157?.value) {
    // テキストフィールドの値に全角文字が含まれていた場合
    if (local.orX0157.value.match(/[^\x20-\x7E\uFF61-\uFF9F]/g)) {
      // モード1 イベントフラグを発火
      if (localOneway.orX0157Oneway.halfWidth?.mode === '1') {
        local.orX0157.emitType = 'fullWidthFlg'
      }
      // モード2 半角文字へ変換
      else {
        // 全角文字を半角文字に変換して表示上の値に設定
        local.orX0157.value = convertToHalfwidth(local.orX0157.value)
      }
    }
  }
  emit('update:modelValue', local.orX0157)
}

/**
 * 全角文字を半角文字へ変換する
 * 受け取った文字列を半角文字へ変換して呼び出し元へ返す
 *
 * @param value - 全角文字を含む文字列
 */
const convertToHalfwidth = (value: string) => {
  let res = value

  // ひらがな -> 全角カタカナ
  res = res.replace(/[\u3041-\u3096]/g, (match: string) =>
    String.fromCharCode(match.charCodeAt(0) + 0x60)
  )
  // 全角カタカナ -> 半角カタカナ
  const reg = new RegExp('(' + Object.keys(convertMap).join('|') + ')', 'g')
  res = res
    .replace(reg, (match: string) => {
      return convertMap[match]
    })
    .replace(/゛/g, 'ﾞ')
    .replace(/゜/g, 'ﾟ')
    .replace(/\u3000/g, ' ')
  // 全角英数字記号 -> 半角英数字記号
  res = res.replace(/[Ａ-Ｚａ-ｚ０-９！-／：-＠［-｀｛-～]/g, (match: string) =>
    String.fromCharCode(match.charCodeAt(0) - 0xfee0)
  )
  // その他全角文字 -> ?
  res = res.replace(/[^\x20-\x7E\uFF61-\uFF9F]/g, (_match: string) => '?')
  return res
}

//

const onFocusEvent = () => {
  isFocus.value = true
  if (localOneway.orX0157Oneway.numeric?.isEditCamma) {
    const num = deleteComma(local.orX0157.value)
    local.orX0157.value = num
  }
}

const onNumericeBlurEvent = () => {
  isFocus.value = false
  let num = removeNonNumeic(local.orX0157.value)
  num = toMinMaxRange(num)

  if (localOneway.orX0157Oneway.numeric?.isEditPeriod) {
    num = addPeriod(num)
  }
  if (localOneway.orX0157Oneway.numeric?.isEditCamma) {
    num = addComma(num)
  }

  local.orX0157.value = num
}

const onClickSpinBtnEvent = (isAdd: boolean) => {
  if (!localOneway.orX0157Oneway.numeric?.disalbeSpinBtnDefaultProcessing) {
    let num = removeNonNumeic(local.orX0157.value)

    let valTypeNum = Number(num)
    if (!isNaN(valTypeNum)) {
      valTypeNum += isAdd ? 1 : -1
    }

    num = toMinMaxRange(valTypeNum.toString())

    local.orX0157.value = num
  }

  local.orX0157.emitType = isAdd ? 'clickSpinBtnUp' : 'clickSpinBtnDown'

  emit('update:modelValue', local.orX0157)
}

/**
 * 数値以外を削除
 *
 * @param val - 値
 */
const removeNonNumeic = (val: string) => {
  if (val === null || val === undefined || val === '') {
    return val
  }

  // 全角長音（ー――－ー）、小数点（．）、全角数字を半角に変換
  let convert = val.replace(/[ー――－ー]/g, '-').replace(/[０-９．]/g, (s) => {
    return String.fromCharCode(s.charCodeAt(0) - 0xfee0)
  })

  //  先頭が「-」であるか
  const startsWithMinus = convert.startsWith('-')

  // 半角数字、小数点以外を削除
  convert = convert.replace(/[^\d.]+/g, '')

  // 先頭が「-」の場合は、最終的に先頭に「-」を追加
  if (startsWithMinus) {
    convert = `-${convert}`
  }

  // 文字列に数値変換できない値があった場合は何もしない
  const valTypeNum = Number(convert)
  if (isNaN(valTypeNum)) {
    return val
  }

  return valTypeNum.toString()
}

/**
 * 最小、最大の範囲内にする
 *
 * @param val - 値
 */
const toMinMaxRange = (val: string) => {
  if (val === null || val === undefined || val === '') {
    return val
  }

  // 文字列に数値変換できない値があった場合は何もしない
  let valTypeNum = Number(val)
  if (isNaN(valTypeNum)) {
    return val
  }

  // 最小値の範囲内かチェック
  if (
    localOneway.orX0157Oneway.numeric?.min !== undefined &&
    valTypeNum < localOneway.orX0157Oneway.numeric?.min
  ) {
    valTypeNum = localOneway.orX0157Oneway.numeric?.min
  }

  // 最大値の範囲内かチェック
  if (
    localOneway.orX0157Oneway.numeric?.max !== undefined &&
    valTypeNum > localOneway.orX0157Oneway.numeric?.max
  ) {
    valTypeNum = localOneway.orX0157Oneway.numeric?.max
  }

  return valTypeNum.toString()
}

/**
 * ピリオド制御
 *
 * @param val - 値
 */
const addPeriod = (val: string) => {
  if (val === null || val === undefined || val === '') {
    return val
  }

  // 文字列に数値変換できない値があった場合は何もしない
  const valTypeNum = Number(val)
  if (isNaN(valTypeNum)) {
    return val
  }

  // ピリオド入力済の場合は何もしない
  if (/\./.exec(val)) {
    return val
  }

  if (localOneway.orX0157Oneway.numeric?.periodSortDigit) {
    const digit = localOneway.orX0157Oneway.numeric?.periodSortDigit

    // 文字数がピリオドの挿入桁以下の場合は何もしない
    if (val.length <= digit) {
      return val
    }

    return `${val.substring(0, digit)}.${val.substring(digit, val.length)}`
  }

  return val
}

/**
 * カンマ制御
 *
 * @param val - 値
 */
const addComma = (val: string) => {
  if (val === null || val === undefined || val === '') {
    return val
  }

  // 文字列に数値変換できない値があった場合は何もしない
  const valTypeNum = Number(val)
  if (isNaN(valTypeNum)) {
    return val
  }
  return String(valTypeNum.toLocaleString())
}

/**
 * カンマ削除
 *
 * @param val - 値
 */
const deleteComma = (val: string) => {
  return val ? String(val).replace(/,/g, '') : ''
}
</script>

<template>
  <!-- 半角文字専用テキストフィールド -->
  <c-v-sheet
    v-if="localOneway.orX0157Oneway.inputMode === OrX0157Const.INPUT_MODE.HALF_WIDTH_CHARS_ONLY"
    :class="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.customClass?.outerClass"
    :style="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.customClass?.outerStyle"
  >
    <!-- 縦型ラベル -->
    <c-v-row
      v-if="
        localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.isVerticalLabel &&
        localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.showItemLabel
      "
      no-gutters
      :class="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.customClass?.labelClass"
      :style="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.customClass?.labelStyle"
    >
      <!-- 項目名あり -->
      <c-v-col
        v-if="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.itemLabel"
        class="align-self-center"
        cols="auto"
      >
        <base-at-label
          class="item-label"
          :value="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.itemLabel"
          :font-weight="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.itemLabelFontWeight"
        />
      </c-v-col>
      <!-- 項目名なし -->
      <c-v-col
        v-else
        cols="auto"
      >
        <base-at-label
          class="item-label"
          value="&emsp;"
        />
      </c-v-col>
      <!-- 必須バッチ -->
      <c-v-col
        v-if="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.isRequired"
        class="align-self-center"
        cols="auto"
      >
        <base-mo00037 class="pl-1" />
      </c-v-col>
      <!-- ヒント -->
      <c-v-col
        v-if="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.hintTooltipText !== ''"
        class="align-self-center"
        cols="auto"
      >
        <c-v-tooltip
          :text="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.hintTooltipText"
        >
          <template #activator="{ props: activatorProps }">
            <base-at-icon
              v-bind="activatorProps"
              icon="help fill"
              color="subText"
              size="18"
            />
          </template>
        </c-v-tooltip>
      </c-v-col>
      <slot name="content" />
    </c-v-row>
    <c-v-row
      no-gutters
      :class="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.customClass?.itemClass"
      :style="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.customClass?.itemStyle"
    >
      <!-- 横型ラベル:項目名あり -->
      <c-v-col
        v-if="
          !localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.isVerticalLabel &&
          localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.showItemLabel &&
          localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.itemLabel
        "
        class="pt-1"
        :class="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.customClass?.labelClass"
        :style="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.customClass?.labelStyle"
        cols="auto"
      >
        <base-at-label
          class="label-center"
          :value="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.itemLabel"
        />
      </c-v-col>
      <!-- 横型ラベル:項目名なし -->
      <c-v-col
        v-else-if="
          !localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.isVerticalLabel &&
          localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.showItemLabel &&
          !localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.itemLabel
        "
        cols="auto"
      >
        <base-at-label
          class="item-label"
          value="&emsp;"
        />
      </c-v-col>
      <!-- 横型:必須バッチ -->
      <c-v-col
        v-if="
          !localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.isVerticalLabel &&
          localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.showItemLabel &&
          localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.isRequired
        "
        class="pt-2"
        cols="auto"
      >
        <base-mo00037 class="pl-1" />
      </c-v-col>
      <!-- 横型:ヒント -->
      <c-v-col
        v-if="
          !localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.isVerticalLabel &&
          localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.showItemLabel &&
          localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.hintTooltipText !== ''
        "
        class="pt-2"
        cols="auto"
      >
        <c-v-tooltip
          :text="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.hintTooltipText"
        >
          <template #activator="{ props: activatorProps }">
            <base-at-icon
              v-bind="activatorProps"
              icon="help fill"
              color="subText"
              size="18"
            />
          </template>
        </c-v-tooltip>
      </c-v-col>
      <c-v-col>
        <!-- テキストフィールド -->
        <div class="input-field-container d-flex">
          <!-- 半角文字専用テキストフィールド -->
          <base-at-text-field
            v-model="local.orX0157.value"
            v-bind="{ ...$attrs, ...localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway }"
            class="mo-input-field"
            @blur="onBlurEvent"
            @keydown.enter.stop="$event.target.blur()"
          >
            <template
              v-if="$slots['append-inner']"
              #append-inner
            >
              <slot name="append-inner" />
            </template>
            <template #prepend>
              <g-base-or-12002
                v-if="localOneway.orX0157Oneway.showEditBtnFlg"
                :readonly="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.readonly"
                :disabled="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.disabled"
                :class="['icon-edit-btn', localOneway.orX0157Oneway.editBtnClass]"
                @click="$emit('onClickEditBtn', $event)"
              >
              </g-base-or-12002>
            </template>
            <!-- 右側にラベルを表示するために append スロットを利用 -->
            <template
              v-if="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.appendLabel !== ''"
              #append
            >
              <base-at-label
                :value="localOneway.orX0157Oneway.halfWidth?.orX0157InputOneway.appendLabel"
              />
            </template>
          </base-at-text-field>
        </div>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <!-- 数値専用テキストフィールド -->
  <c-v-sheet
    v-else-if="localOneway.orX0157Oneway.inputMode === OrX0157Const.INPUT_MODE.NUMERIC_ONLY"
    :class="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.customClass?.outerClass"
    :style="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.customClass?.outerStyle"
  >
    <!-- 縦型ラベル -->
    <c-v-row
      v-if="
        localOneway.orX0157Oneway.numeric?.orX0157InputOneway.isVerticalLabel &&
        localOneway.orX0157Oneway.numeric?.orX0157InputOneway.showItemLabel
      "
      no-gutters
      :class="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.customClass?.labelClass"
      :style="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.customClass?.labelStyle"
    >
      <!-- 項目名あり -->
      <c-v-col
        v-if="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.itemLabel"
        class="align-self-center"
        cols="auto"
      >
        <base-at-label
          class="item-label"
          :value="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.itemLabel"
          :font-weight="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.itemLabelFontWeight"
        />
      </c-v-col>
      <!-- 項目名なし -->
      <c-v-col
        v-else
        cols="auto"
      >
        <base-at-label
          class="item-label"
          value="&emsp;"
        />
      </c-v-col>
      <!-- 必須バッチ -->
      <c-v-col
        v-if="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.isRequired"
        class="align-self-center"
        cols="auto"
      >
        <base-mo00037 class="pl-1" />
      </c-v-col>
      <!-- ヒント -->
      <c-v-col
        v-if="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.hintTooltipText !== ''"
        class="align-self-center"
        cols="auto"
      >
        <c-v-tooltip :text="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.hintTooltipText">
          <template #activator="{ props: activatorProps }">
            <base-at-icon
              v-bind="activatorProps"
              icon="help fill"
              color="subText"
              size="18"
            />
          </template>
        </c-v-tooltip>
      </c-v-col>
      <slot name="content" />
    </c-v-row>
    <c-v-row
      no-gutters
      :class="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.customClass?.itemClass"
      :style="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.customClass?.itemStyle"
    >
      <!-- 横型ラベル:項目名あり -->
      <c-v-col
        v-if="
          !localOneway.orX0157Oneway.numeric?.orX0157InputOneway.isVerticalLabel &&
          localOneway.orX0157Oneway.numeric?.orX0157InputOneway.showItemLabel &&
          localOneway.orX0157Oneway.numeric?.orX0157InputOneway.itemLabel
        "
        class="pt-1"
        :class="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.customClass?.labelClass"
        :style="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.customClass?.labelStyle"
        cols="auto"
      >
        <base-at-label
          class="label-center"
          :value="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.itemLabel"
        />
      </c-v-col>
      <!-- 横型ラベル:項目名なし -->
      <c-v-col
        v-else-if="
          !localOneway.orX0157Oneway.numeric?.orX0157InputOneway.isVerticalLabel &&
          localOneway.orX0157Oneway.numeric?.orX0157InputOneway.showItemLabel &&
          !localOneway.orX0157Oneway.numeric?.orX0157InputOneway.itemLabel
        "
        cols="auto"
      >
        <base-at-label
          class="item-label"
          value="&emsp;"
        />
      </c-v-col>
      <!-- 横型:必須バッチ -->
      <c-v-col
        v-if="
          !localOneway.orX0157Oneway.numeric?.orX0157InputOneway.isVerticalLabel &&
          localOneway.orX0157Oneway.numeric?.orX0157InputOneway.showItemLabel &&
          localOneway.orX0157Oneway.numeric?.orX0157InputOneway.isRequired
        "
        class="pt-2"
        cols="auto"
      >
        <base-mo00037 class="pl-1" />
      </c-v-col>
      <!-- 横型:ヒント -->
      <c-v-col
        v-if="
          !localOneway.orX0157Oneway.numeric?.orX0157InputOneway.isVerticalLabel &&
          localOneway.orX0157Oneway.numeric?.orX0157InputOneway.showItemLabel &&
          localOneway.orX0157Oneway.numeric?.orX0157InputOneway.hintTooltipText !== ''
        "
        class="pt-2"
        cols="auto"
      >
        <c-v-tooltip :text="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.hintTooltipText">
          <template #activator="{ props: activatorProps }">
            <base-at-icon
              v-bind="activatorProps"
              icon="help fill"
              color="subText"
              size="18"
            />
          </template>
        </c-v-tooltip>
      </c-v-col>
      <c-v-col>
        <!-- テキストフィールド -->
        <div class="input-field-container d-flex">
          <!-- 数値専用テキストフィールド -->
          <base-at-text-field
            v-model="local.orX0157.value"
            v-bind="{ ...$attrs, ...localOneway.orX0157Oneway.numeric?.orX0157InputOneway }"
            class="mo-input-field right-input"
            @focus="onFocusEvent"
            @blur="onNumericeBlurEvent"
          >
            <!-- 右側にラベルを表示するために append スロットを利用 -->
            <template
              v-if="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.appendLabel !== ''"
              #append
            >
              <base-at-label
                :value="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.appendLabel"
              />
            </template>
            <template #prepend>
              <g-base-or-12002
                v-if="localOneway.orX0157Oneway.showEditBtnFlg"
                :readonly="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.readonly"
                :disabled="localOneway.orX0157Oneway.numeric?.orX0157InputOneway.disabled"
                :class="['icon-edit-btn', localOneway.orX0157Oneway.editBtnClass]"
                @click="$emit('onClickEditBtn', $event)"
              >
              </g-base-or-12002>
            </template>
            <!-- スピンボタン -->
            <template
              v-if="localOneway.orX0157Oneway.numeric?.showSpinBtn"
              #append-inner
            >
              <div class="spin-btn-box">
                <!-- ▲ボタン -->
                <base-at-button
                  class="spin-btn rounded-te"
                  color="black-100"
                  @click="onClickSpinBtnEvent(true)"
                >
                  <base-at-icon
                    icon="arrow_drop_up"
                    size="18"
                  />
                </base-at-button>
                <!-- ▼ボタン -->
                <base-at-button
                  class="spin-btn rounded-be"
                  color="black-100"
                  @click="onClickSpinBtnEvent(false)"
                >
                  <base-at-icon
                    icon="arrow_drop_down"
                    size="18"
                  />
                </base-at-button>
              </div>
            </template>
          </base-at-text-field>
        </div>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>

  <!-- テキストフィールド -->
  <c-v-sheet
    v-else
    :class="localOneway.orX0157Oneway.text?.orX0157InputOneway.customClass?.outerClass"
    :style="localOneway.orX0157Oneway.text?.orX0157InputOneway.customClass?.outerStyle"
  >
    <!-- 縦型ラベル -->
    <c-v-row
      v-if="
        localOneway.orX0157Oneway.text?.orX0157InputOneway.isVerticalLabel &&
        localOneway.orX0157Oneway.text?.orX0157InputOneway.showItemLabel
      "
      no-gutters
      :class="localOneway.orX0157Oneway.text?.orX0157InputOneway.customClass?.labelClass"
      :style="localOneway.orX0157Oneway.text?.orX0157InputOneway.customClass?.labelStyle"
    >
      <!-- 項目名あり -->
      <c-v-col
        v-if="localOneway.orX0157Oneway.text?.orX0157InputOneway.itemLabel"
        class="align-self-center"
        cols="auto"
      >
        <base-at-label
          class="item-label"
          :value="localOneway.orX0157Oneway.text?.orX0157InputOneway.itemLabel"
          :font-weight="localOneway.orX0157Oneway.text?.orX0157InputOneway.itemLabelFontWeight"
        />
      </c-v-col>
      <!-- 項目名なし -->
      <c-v-col
        v-else
        cols="auto"
      >
        <base-at-label
          class="item-label"
          value="&emsp;"
        />
      </c-v-col>
      <!-- 必須バッチ -->
      <c-v-col
        v-if="localOneway.orX0157Oneway.text?.orX0157InputOneway.isRequired"
        class="align-self-center"
        cols="auto"
      >
        <base-mo00037 class="pl-1" />
      </c-v-col>
      <!-- ヒント -->
      <c-v-col
        v-if="localOneway.orX0157Oneway.text?.orX0157InputOneway.hintTooltipText !== ''"
        class="align-self-center"
        cols="auto"
      >
        <c-v-tooltip :text="localOneway.orX0157Oneway.text?.orX0157InputOneway.hintTooltipText">
          <template #activator="{ props: activatorProps }">
            <base-at-icon
              v-bind="activatorProps"
              icon="help fill"
              color="subText"
              size="18"
            />
          </template>
        </c-v-tooltip>
      </c-v-col>
      <slot name="content" />
    </c-v-row>
    <c-v-row
      no-gutters
      :class="localOneway.orX0157Oneway.text?.orX0157InputOneway.customClass?.itemClass"
      :style="localOneway.orX0157Oneway.text?.orX0157InputOneway.customClass?.itemStyle"
    >
      <!-- 横型ラベル:項目名あり -->
      <c-v-col
        v-if="
          !localOneway.orX0157Oneway.text?.orX0157InputOneway.isVerticalLabel &&
          localOneway.orX0157Oneway.text?.orX0157InputOneway.showItemLabel &&
          localOneway.orX0157Oneway.text?.orX0157InputOneway.itemLabel
        "
        class="pt-1"
        :class="localOneway.orX0157Oneway.text?.orX0157InputOneway.customClass?.labelClass"
        :style="localOneway.orX0157Oneway.text?.orX0157InputOneway.customClass?.labelStyle"
        cols="auto"
      >
        <base-at-label
          class="label-center"
          :value="localOneway.orX0157Oneway.text?.orX0157InputOneway.itemLabel"
        />
      </c-v-col>
      <!-- 横型ラベル:項目名なし -->
      <c-v-col
        v-else-if="
          !localOneway.orX0157Oneway.text?.orX0157InputOneway.isVerticalLabel &&
          localOneway.orX0157Oneway.text?.orX0157InputOneway.showItemLabel &&
          !localOneway.orX0157Oneway.text?.orX0157InputOneway.itemLabel
        "
        cols="auto"
      >
        <base-at-label
          class="item-label"
          value="&emsp;"
        />
      </c-v-col>
      <!-- 横型:必須バッチ -->
      <c-v-col
        v-if="
          !localOneway.orX0157Oneway.text?.orX0157InputOneway.isVerticalLabel &&
          localOneway.orX0157Oneway.text?.orX0157InputOneway.showItemLabel &&
          localOneway.orX0157Oneway.text?.orX0157InputOneway.isRequired
        "
        class="pt-2"
        cols="auto"
      >
        <base-mo00037 class="pl-1" />
      </c-v-col>
      <!-- 横型:ヒント -->
      <c-v-col
        v-if="
          !localOneway.orX0157Oneway.text?.orX0157InputOneway.isVerticalLabel &&
          localOneway.orX0157Oneway.text?.orX0157InputOneway.showItemLabel &&
          localOneway.orX0157Oneway.text?.orX0157InputOneway.hintTooltipText !== ''
        "
        class="pt-2"
        cols="auto"
      >
        <c-v-tooltip :text="localOneway.orX0157Oneway.text?.orX0157InputOneway.hintTooltipText">
          <template #activator="{ props: activatorProps }">
            <base-at-icon
              v-bind="activatorProps"
              icon="help fill"
              color="subText"
              size="18"
            />
          </template>
        </c-v-tooltip>
      </c-v-col>
      <c-v-col>
        <!-- テキストフィールド -->
        <div class="input-field-container d-flex">
          <!-- テキストフィールド -->
          <base-at-text-field
            v-model="local.orX0157.value"
            v-bind="{ ...$attrs, ...localOneway.orX0157Oneway.text?.orX0157InputOneway }"
            class="mo-input-field"
            @blur="$emit('blur', $event)"
          >
            <template
              v-if="$slots['append-inner']"
              #append-inner
            >
              <slot name="append-inner" />
            </template>
            <template #prepend>
              <g-base-or-12002
                v-if="localOneway.orX0157Oneway.showEditBtnFlg"
                :readonly="localOneway.orX0157Oneway.text?.orX0157InputOneway.readonly"
                :disabled="localOneway.orX0157Oneway.text?.orX0157InputOneway.disabled"
                :class="['icon-edit-btn', localOneway.orX0157Oneway.editBtnClass]"
                @click="$emit('onClickEditBtn', $event)"
              >
              </g-base-or-12002>
            </template>
            <!-- 右側にラベルを表示するために append スロットを利用 -->
            <template
              v-if="localOneway.orX0157Oneway.text?.orX0157InputOneway.appendLabel !== ''"
              #append
            >
              <base-at-label
                :value="localOneway.orX0157Oneway.text?.orX0157InputOneway.appendLabel"
              />
            </template>
          </base-at-text-field>
        </div>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
</template>
<style scoped lang="scss">
$border-radius: 4px;
$icon-edit-btn-width: 36px;
$icon-border-radius: 3px;
$border-color: #cdcdcd;
$icon-color: #869fca;
.input-field-container {
  border-radius: $border-radius;
  // 入力フィールドのアトミックベーススタイル
  .mo-input-field {
    margin-right: 0px !important;
    border-top-right-radius: $border-radius !important;
    border-end-end-radius: $border-radius !important;
    // 入力補助ボタンが存在する場合の左側境界線の角丸除去
    :deep(.v-field) {
      border-top-left-radius: 0px !important;
      border-end-start-radius: 0px !important;
    }
  }
}
.right-input :deep(input) {
  text-align: right !important;
}
.spin-btn-box {
  display: flex;
  flex-direction: column;
  width: 20px;
  height: 100%;
  margin-left: 4px;
}

.spin-btn {
  min-width: inherit;
  min-height: inherit !important;
  border-radius: 0;
  height: 50%;
}

:deep(.v-field) {
  padding-right: 0;
  border: 1px solid $border-color;
}
:deep(.v-field.v-field--focused) {
  border-color: rgb(var(--v-theme-key));
}
:deep(.v-field.v-field--focused.v-field--error) {
  border-color: rgb(var(--v-theme-error));
}
// 入力補助ボタンのスタイル
.icon-edit-btn {
  border: 1px solid $border-color;
  background: rgb(var(--v-theme-black-100));
  color: $icon-color;
  width: $icon-edit-btn-width;
  height: 100%;
  border-top-left-radius: $icon-border-radius !important;
  border-end-start-radius: $icon-border-radius !important;
  border-top-right-radius: 0px !important;
  border-end-end-radius: 0px !important;
  border-right: none;
}
:deep(.v-field__outline__start) {
  border: none !important;
  opacity: unset !important;
}
:deep(.v-field__outline__end) {
  border: none !important;
  opacity: unset !important;
}
:deep(.v-input__prepend) {
  margin-inline-end: unset !important;
}
</style>
