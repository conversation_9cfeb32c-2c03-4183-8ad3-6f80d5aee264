<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or52063Logic } from '../Or52063/Or52063.logic'
import { Or52063Const } from '../Or52063/Or52063.constants'
import { Or61588Const } from './Or61588.constants'
import type { DataTableListItem } from './Or61588.type'
import { useScreenTwoWayBind, useSetupChildProps } from '#imports'
import { Or21735Const } from '~/components/base-components/organisms/Or21735/Or21735.constants'
import { Or21736Const } from '~/components/base-components/organisms/Or21736/Or21736.constants'
import { Or21737Const } from '~/components/base-components/organisms/Or21737/Or21737.constants'
import { Or21738Const } from '~/components/base-components/organisms/Or21738/Or21738.constants'
import { UPDATE_KBN, DIALOG_BTN } from '~/constants/classification-constants'
import type {
  Mo01354Items,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'

import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type {
  Or61588OnewayType,
  Or61588TwowayType,
} from '~/types/cmn/business/components/Or61588Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { Or21735Logic } from '~/components/base-components/organisms/Or21735/Or21735.logic'
import { Or21736Logic } from '~/components/base-components/organisms/Or21736/Or21736.logic'
import { Or21737Logic } from '~/components/base-components/organisms/Or21737/Or21737.logic'
import { Or21738Logic } from '~/components/base-components/organisms/Or21738/Or21738.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  CustomizeDataItem,
  Or52063OneWayType,
  Or52063Type,
} from '~/types/cmn/business/components/Or52063Type'

/**
 * Or61588:GUI00816_アセスメント(パッケージプラン)
 *
 * @description
 * 分類/領域[のまとめ
 *
 * <AUTHOR>
 */

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or61588OnewayType
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

const or21735 = ref({ uniqueCpId: '' })
const or21736 = ref({ uniqueCpId: '' })
const or21737 = ref({ uniqueCpId: '' })
const or21738 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or52063 = ref({ uniqueCpId: '' })

/** 表示タイプ(1:分類, 0:領域) */
const showType = props.onewayModelValue.showType

const filedTabId = ref('')

const classificationTabId = ref('')

const local = reactive({
  /** Or61588:GUI00816_アセスメント(パッケージプラン) */
  mo01354ModelValue: {
    values: {
      selectedRowId: '',
      selectedRowIds: [],
      items: [],
    },
  } as Mo01354Type,
  /** Or52063:GUI00824_表示順変更アセスメント */
  or52063Type: {
    fixedDataList: [],
    customizeDataList: [],
  } as Or52063Type,
})

/**
 * ロカールoneway
 */
const localOneway = reactive({
  importBtnMo00609Oneway: {
    btnLabel: t('btn.import'),
  } as Mo00609OnewayType,
  displayOrderMo00609Oneway: {
    btnLabel: t('btn.display-order'),
  } as Mo00609OnewayType,
  filterImportBtnMo00609Oneway: {
    btnLabel: t('btn.filter-import'),
  } as Mo00609OnewayType,
  displayOrderMo0009Oneway: {
    btnIcon: 'edit_square',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  },
  caseMo00609Oneway: {
    btnLabel: t('label.case-import'),
  } as Mo00609OnewayType,
  caseMo0009Oneway: {
    btnIcon: 'edit_square',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  },
  foucsRowChangeOneway: {
    btnIcon: 'chevron_left',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  },
  mo01354Oneway: {
    headers: [
      /** 領域/分類タイトル */
      {
        title: showType === '1' ? t('label.classificationLb') : t('label.region'),
        key: 'title',
        width: '150px',
        sortable: false,
      },

      {
        /** 課題 */
        title: t('label.tab7-title-all'),
        key: 'matomeKnj',
        sortable: false,
      },
      {
        /** 今後の見通し */
        key: 'mitoshiKnj',
        title: t('label.future-outlook'),
        sortable: false,
      },
    ],
    height: '100%',
    density: 'compact',
  } as Mo01354OnewayType,
  editIcon: {
    btnIcon: 'edit_square',
    name: 'editIcon',
    density: 'compact',
  } as Mo00009OnewayType,
  or52063OneWayType: {
    styleId: '',
    headers: [],
  } as Or52063OneWayType,
})

const defultRowData = {
  /** タイトル */
  title: '',
  /** ID */
  id: '',
  /** アセスメント履歴ID */
  assId: '',
  /** 利用者ID */
  userId: '',
  /** 領域区分 */
  ryoikiKbn: '',
  /** 分類区分 */
  bunruiKbn: '',
  /** 課題 */
  matomeKnj: { value: '' },
  /** 今後の見通し */
  mitoshiKnj: { value: '' },
  /** 表示順 */
  seqNo: '',
  /** dmyBunrui */
  dmyBunrui: '',
  /** 適用チェックボックス */
  dmySel: '',
  /** 更新回数 */
  modifiedCnt: '0',
  // 更新区分：'C'
  updateKbn: UPDATE_KBN.CREATE,
}

// ダイアログ表示フラグ
const showDialogOr52063 = computed(() => {
  // Or52063のダイアログ開閉状態
  return Or52063Logic.state.get(or52063.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * Pinia
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or21735Const.CP_ID(1)]: or21735.value,
  [Or21736Const.CP_ID(1)]: or21736.value,
  [Or21737Const.CP_ID(1)]: or21737.value,
  [Or21738Const.CP_ID(1)]: or21737.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or52063Const.CP_ID(0)]: or52063.value,
})

const { refValue } = useScreenTwoWayBind<Or61588TwowayType>({
  cpId: Or61588Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**
 * ケース取り込み画面表示
 */
function setShowCaseImport() {
  //   if (!authorityManagementInfo.value.isCanView) {
  //     setShowDialog(t('message.i-cmn-10423'))
  //   }
}

/**
 *  refValueを更新する
 */
// const setRefValue = () => {
//   useScreenStore().setCpTwoWay({
//     cpId: Or61588Const.CP_ID(0),
//     uniqueCpId: props.uniqueCpId,
//     value: refValue.value,
//     isInit: true,
//   })
// }

/**
 * 「表示順ボタン」押下
 *
 * @description
 * GUI00824 ［表示順変更アセスメント］画面をポップアップで起動する。
 */
const onOpenSortModal = () => {
  localOneway.or52063OneWayType = {
    styleId: '1',
    headers: [],
  }
  local.or52063Type.customizeDataList = (
    local.mo01354ModelValue.values.items as DataTableListItem[]
  ).map((item) => {
    return {
      ...item,
      displayOrder: {
        value: item.seqNo,
      },
      classification: item.title,
      summary: item.matomeKnj.value,
      outlook: item.mitoshiKnj.value,
    } as CustomizeDataItem
  })
  Or52063Logic.state.set({
    uniqueCpId: or52063.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「行追加ボタン」押下処理
 */
const addRowLine = () => {
  const { items } = local.mo01354ModelValue.values
  const id = String(items.length + 1)
  local.mo01354ModelValue.values.items = [...items, { ...defultRowData, id }]
  local.mo01354ModelValue.values.selectedRowId = id
  refValue.value!.or61588ModelValue = [
    ...refValue.value!.or61588ModelValue,
    { ...defultRowData, id },
  ]
}
/**
 * 「行挿入ボタン」押下
 */
const insertRowLine = () => {
  const { items, selectedRowId } = local.mo01354ModelValue.values
  const id = String(items.length + 1)
  const findIndex = items.findIndex((item) => item.id === selectedRowId)
  items.splice(findIndex, 0, {
    ...defultRowData,
    id,
  })
  local.mo01354ModelValue.values.selectedRowId = id
  refValue.value!.or61588ModelValue.splice(findIndex, 0, {
    ...defultRowData,
    id,
  } as DataTableListItem)
}
/**
 * 「行複写ボタン」押下
 */
const copyRowLine = () => {
  const { items, selectedRowId } = local.mo01354ModelValue.values
  const id = String(items.length + 1)
  const findObj = items.find((item) => item.id === selectedRowId)
  const findIndex = items.findIndex((item) => item.id === selectedRowId)
  items.splice(findIndex + 1, 0, { ...findObj, id })
  refValue.value!.or61588ModelValue.splice(findIndex + 1, 0, {
    ...findObj,
    id,
  } as DataTableListItem)
}
/**
 * 「行削除ボタン」押下
 */
const deleteRowLine = async () => {
  const { items, selectedRowId } = local.mo01354ModelValue.values
  if (!selectedRowId) return
  // i.cmn.10882
  const result = await getDeleteConfirmResult(t('message.i-cmn-10882'))
  if (result === DIALOG_BTN.YES) {
    const findIndex = items.findIndex((item) => item.id === selectedRowId)
    // 選択行を削除
    refValue.value!.or61588ModelValue = items.map((item) => {
      if (item.id === selectedRowId) {
        return { ...item, updateKbn: UPDATE_KBN.DELETE }
      }
      return item
    }) as DataTableListItem[]
    local.mo01354ModelValue.values.items = items.filter((item) => item.id !== selectedRowId)
    // 削除後、フォーカスを次の行のまとめに設定する。
    if (findIndex + 1 >= items.length) {
      local.mo01354ModelValue.values.selectedRowId = items[findIndex - 1]?.id ?? ''
    } else {
      local.mo01354ModelValue.values.selectedRowId = items[findIndex + 1]?.id ?? ''
    }
  }
}

/**
 * 削除確認ダイアログ
 *
 * @param dialogText - ダイアログテキスト
 */
const getDeleteConfirmResult = (dialogText: string): Promise<'yes' | 'no'> => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      thirdBtnLabel: t('btn.close'),
      isOpen: true,
      dialogText: dialogText,
    },
  })

  // 閉じるタイミングで監視を実行
  return new Promise((resolve) => {
    let result: 'yes' | 'no'
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      (newValue) => {
        if (!newValue) {
          const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
          if (event?.firstBtnClickFlg) {
            result = 'yes'
          }
          if (event?.secondBtnClickFlg) {
            result = 'no'
          }
          Or21814Logic.event.set({
            uniqueCpId: or21814.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              closeBtnClickFlg: false,
            },
          })
          resolve(result)
        }
      },
      { once: true }
    )
  })
}

/**************************************************
 * 画面イベント監視
 **************************************************/

watch(
  () => props.onewayModelValue,
  (newValue) => {
    console.log(refValue.value, 'refValue.value')

    if (!newValue || !refValue.value) return
    // タブIDの変換
    filedTabId.value = String.fromCharCode(Number(newValue.filedTabId) + 64)
    classificationTabId.value = String.fromCharCode(Number(newValue.classificationTabId) + 96)
    local.mo01354ModelValue.values.items = refValue.value.or61588ModelValue.filter((item) => {
      if (showType === '1') {
        return (
          item.bunruiKbn === newValue.classificationTabId && item.ryoikiKbn === newValue.filedTabId
        )
      } else {
        return item.ryoikiKbn === newValue.filedTabId
      }
    })
    local.mo01354ModelValue.values.selectedRowId = local.mo01354ModelValue.values.items[0]?.id ?? ''
  },
  { deep: true, immediate: true }
)

// 「行追加ボタン」押下
watch(
  () => Or21735Logic.event.get(or21735.value.uniqueCpId),
  () => {
    addRowLine()
  }
)
// 「行挿入ボタン」押下
watch(
  () => Or21736Logic.event.get(or21736.value.uniqueCpId),
  () => {
    insertRowLine()
  }
)

// 「行複写ボタン」押下
watch(
  () => Or21737Logic.event.get(or21737.value.uniqueCpId),
  () => {
    copyRowLine()
  }
)

// 「行削除ボタン」押下
watch(
  () => Or21738Logic.event.get(or21738.value.uniqueCpId),
  async () => {
    await deleteRowLine()
  }
)

watch(
  () => local.or52063Type,
  (newVal) => {
    local.mo01354ModelValue.values.items = (
      newVal.customizeDataList as (DataTableListItem & CustomizeDataItem)[]
    ).map((item) => {
      return {
        /** タイトル */
        title: item.classification,
        /** ID */
        id: item.id,
        /** アセスメント履歴ID */
        assId: item,
        /** 利用者ID */
        userId: item.userId,
        /** 領域区分 */
        ryoikiKbn: item.ryoikiKbn,
        /** 分類区分 */
        bunruiKbn: item.bunruiKbn,
        /** 課題 */
        matomeKnj: item.matomeKnj,
        /** 今後の見通し */
        mitoshiKnj: item.mitoshiKnj,
        /** 表示順 */
        seqNo: item.displayOrder.value,
        /** dmyBunrui */
        dmyBunrui: item,
        /** 適用チェックボックス */
        dmySel: item.dmySel,
        /** 更新回数 */
        modifiedCnt: item.modifiedCnt,
        /** 更新区分 */
        updateKbn: item.updateKbn,
      } as Mo01354Items
    })
  },
  {
    deep: true,
  }
)
</script>

<template>
  <c-v-row no-gutters>
    <c-v-col class="heading-text-m pa-2">
      {{
        showType === '1'
          ? t('label.classifica-summary', [classificationTabId])
          : t('label.region-summary', [filedTabId])
      }}
    </c-v-col>
  </c-v-row>
  <!-- アセスメント総括ボタン組 -->
  <c-v-row
    no-gutters
    class="px-2 pb-2"
  >
    <!-- アクションボタン組 -->
    <c-v-col cols="8 d-flex">
      <!-- 行追加ボタン -->
      <div>
        <g-base-or-21735 v-bind="or21735" />
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.add-row')"
        />
      </div>
      <!-- 行挿入ボタン -->
      <div class="ml-2">
        <g-base-or-21736 v-bind="or21736" />
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.insert-row')"
        />
      </div>
      <!-- 行複写ボタン: Or21737 -->
      <div class="ml-2">
        <g-base-or-21737 v-bind="or21737" />
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.duplicate-row')"
        />
      </div>
      <!-- 行削除ボタン: Or21738 -->
      <div class="ml-2">
        <g-base-or-21738 v-bind="or21738" />
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.delete-row')"
        ></c-v-tooltip>
      </div>
      <c-v-divider
        vertical
        class="divider mx-2"
      />
      <!-- 取込 -->
      <div class="mr-2">
        <base-mo00611
          :oneway-model-value="localOneway.importBtnMo00609Oneway"
          @click="onOpenSortModal"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="t('tooltip.import')"
            open-delay="200"
          />
        </base-mo00611>
      </div>
      <!-- ケース取込エリア -->
      <div v-if="showType === '0'">
        <base-mo00611
          :oneway-model-value="localOneway.caseMo00609Oneway"
          @click="setShowCaseImport"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('まとめ情報を表示します')"
            open-delay="200"
          />
        </base-mo00611>
      </div>
    </c-v-col>
    <c-v-col
      cols="4"
      class="d-flex justify-end align-center"
    >
      <!-- 絞込みボタン -->
      <base-mo00611
        :oneway-model-value="localOneway.filterImportBtnMo00609Oneway"
        class="mr-2"
        @click="onOpenSortModal"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="t('データを絞り込みます')"
          open-delay="200"
        />
      </base-mo00611>
      <!-- 表示順変更ボタン -->
      <base-mo00611
        :oneway-model-value="localOneway.displayOrderMo00609Oneway"
        @click="onOpenSortModal"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="t('tooltip.display-order')"
          open-delay="200"
        />
      </base-mo00611>
      <!--件数ラベル  -->
      <div class="ml-2">
        {{ local.mo01354ModelValue.values.items.length + t('label.item') }}
      </div>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col
      cols="12"
      class="table-header px-2"
    >
      <base-mo-01354
        v-model="local.mo01354ModelValue"
        hide-default-footer
        :oneway-model-value="localOneway.mo01354Oneway"
        class="list-wrapper"
      >
        <!--課題  -->
        <template #[`item.matomeKnj`]="{ item }">
          <div class="d-flex align-center rowHeight">
            <div class="px-4">
              <base-mo00009 :oneway-model-value="localOneway.editIcon" />
            </div>
            <div class="overflowText h-100 flex-grow-1">
              <!-- 内容 -->
              <base-mo01280 v-model="item.matomeKnj" />
            </div>
          </div>
        </template>
        <!--今後の見通し  -->
        <template #[`item.mitoshiKnj`]="{ item }">
          <div class="d-flex align-center rowHeight">
            <div class="px-4">
              <base-mo00009 :oneway-model-value="localOneway.editIcon" />
            </div>
            <div class="overflowText h-100 flex-grow-1">
              <!-- 内容 -->
              <base-mo01280 v-model="item.mitoshiKnj" />
            </div>
          </div>
        </template>
      </base-mo-01354>
    </c-v-col>
  </c-v-row>
  <g-base-or21814 v-bind="or21814" />
  <!-- GUI00824_表示順変更アセスメント -->
  <g-custom-or-52063
    v-if="showDialogOr52063"
    v-bind="or52063"
    v-model="local.or52063Type"
    :oneway-model-value="localOneway.or52063OneWayType"
  />
</template>

<style lang="scss" scoped>
@use '@/styles/cmn/mo-data-table.scss';
:deep(.v-table__wrapper table tr:not(:first-child) th:last-child) {
  border-right: 1px solid rgba(var(--v-theme-black-200)) !important;
}
.table-header :deep(.v-table__wrapper tr th) {
  background-color: rgb(var(--v-theme-blue-200)) !important;
}
.table-header :deep(.v-table__wrapper tr td:not(:first-child)) {
  padding: 1px !important;
}
.table-header :deep(.v-table__wrapper tr td:first-child) {
  padding: 0 16px !important;
}
.divider {
  height: 36px;
}
.background-transparent {
  background-color: transparent;
}
.rowHeight {
  height: 70px;
}
</style>
