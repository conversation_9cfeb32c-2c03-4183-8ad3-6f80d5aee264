<script setup lang="ts">
/**
 * Or29976:有機体:印刷設定モーダル
 * GUI00986_［印刷設定］画面
 *
 * @description
 * ［印刷設定］画面
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import type { rirekiList, CheckRow } from '../Or10464/Or10464.type'
import type { Or29976StateType, Or29976TwoWayData } from './Or29976.type'
import { OrX0133Logic } from '~/components/custom-components/organisms/OrX0133/OrX0133.logic'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenStore,
  useScreenTwoWayBind,
} from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or29976OnewayType } from '~/types/cmn/business/components/Or29976Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or29976Const } from '~/components/custom-components/organisms/Or29976/Or29976.constants'

import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  printSettingsScreenInitialInfoSelectGUI00986OutEntity,
  printSettingsScreenInitialInfoSelectGUI00986InEntity,
  choPrtList,
} from '~/repositories/cmn/entities/printSettingsScreenInitialInfoSelectGUI00986'
import type {
  WeekTableHistoryInfoSelectOutEntity,
  WeekTableHistoryInfoSelectInEntity,
} from '~/repositories/cmn/entities/weekTableHistoryInfoSelect'
import type {
  LedgerInitializeDataComSelectOutEntity,
  LedgerInitializeDataComSelectInEntity,
} from '~/repositories/cmn/entities/ledgerInitializeDataComSelect'
import type { PrintSettingsInfoComUpdateInEntity } from '~/repositories/cmn/entities/printSettingsInfoComUpdate'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type {
  Or04007OnewayType,
  Or04007TwowayType,
} from '~/types/cmn/business/components/Or04007Type'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { Or10464OnewayType } from '~/types/cmn/business/components/Or10464Type'
import type { Mo00040Type, Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import { Or10016Logic } from '~/components/custom-components/organisms/Or10016/Or10016.logic'
import { Or10016Const } from '~/components/custom-components/organisms/Or10016/Or10016.constants'
import type { Or10016OnewayType } from '~/types/cmn/business/components/Or10016Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type {
  OrX0133OnewayType,
  OrX0133TableData,
} from '~/types/cmn/business/components/OrX0133Type'
import { OrX0133Const } from '~/components/custom-components/organisms/OrX0133/OrX0133.constants'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import type { InWebEntity } from '@/repositories/AbstructWebRepository'
import { reportOutputType, useReportUtils } from '~/utils/useReportUtils'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import type { ShuuKanReportEntity } from '~/repositories/cmn/entities/ShuuKanReportEntity'

const { t } = useI18n()
const { reportOutput } = useReportUtils()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or29976OnewayType
  onewayModelValues: Or10464OnewayType
  uniqueCpId: string
  parentCpId: string
}

const props = defineProps<Props>()

// 引継情報を取得する

const or21813 = ref({ uniqueCpId: '' })

const or21815 = ref({ uniqueCpId: '' })
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

const orX0128 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const or10016 = ref({ uniqueCpId: '' })
const orX0117 = ref({ uniqueCpId: '' })
const orX0133 = ref({ uniqueCpId: OrX0133Const.CP_ID(0) })
const orx0145 = ref({ uniqueCpId: '' })

// プロファイル
const choPro = ref('')

// 印刷権限
const prtFlg: boolean = await hasPrintAuth()

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [Or10016Const.CP_ID(0)]: or10016.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

// 帳票イニシャライズデータを取得する
const reportInitData = ref([
  {
    // 氏名伏字印刷
    prtName: '',
    // 文書番号印刷
    prtBng: '',
    // 個人情報印刷
    prtKojin: '',
  },
])

// ダイアログ表示フラグ
const showDialogOr10016 = computed(() => {
  // Or10016のダイアログ開閉状態
  return Or10016Logic.state.get(or10016.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOrx0117 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * 変数定義
 **************************************************/
const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(props.uniqueCpId)
})

/**
 * 利用者列幅
 */
const userCols = ref<number>(4)

/**
 * 履歴一覧セクションフラゲ
 */
const mo01334TypeHistoryFlag = ref<boolean>(false)

const orX0133OnewayModel = reactive<OrX0133OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: Or29976Const.DEFAULT.ONE,
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: Or29976Const.DEFAULT.TANI,
  tableStyle: 'width:335px',
  itemShowFlg: {},
  rirekiList: [] as OrX0133TableData[],
})

/**
 * 利用者選択
 */
const mo00039OneWayUserSelectType = ref<string>('')

/**
 * 履歴選択
 */
const mo00039OneWayHistorySelectType = ref<string>('')
/**
 * 職員基本リスト
 */
let selList: {
  chkShokuId: string
  shokuinKnj: string
}[]

// ローカル双方向bind
const local = reactive({
  mo00040: { modelValue: '' } as Mo00040Type,
  /**
   * 利用者ID
   */
  userId: '',
  /**
   * アセスメントID
   */
  assessmentId: '',
  textInput: {
    value: '',
  } as Mo00045Type,
  mo00039Type: '',
  mo00020Type: {
    value: '',
  } as Mo00020Type,
  mo00020TypeKijunbi: {
    value: '2024/11/11',
  } as Mo00020Type,
  /** 担当ケアマネ */
  mo01408modelCare: {
    value: '',
  },
  titleInput: {
    value: '',
  } as Mo00045Type,
  mo00018TypeChangeTitle: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintAuthor: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintMode: {
    modelValue: false,
  } as Mo00018Type,

  or04007: {
    rirekiList: [] as rirekiList[],
    checkRow: [] as CheckRow[],
    isCheckAll: {
      modelValue: false,
    },
    selectedItemIndex: -1,
  } as Or04007TwowayType,
  /**
   * 帳票ID
   */
  reportId: Or29976Const.DEFAULT.EMPTY,
  /**
   * 帳票番号
   */
  prtNo: Or29976Const.DEFAULT.EMPTY,
  /**
   * 帳票セクション番号
   */
  sectionNo: Or29976Const.DEFAULT.EMPTY,
  /**
   * 変数.印鑑欄を表示するフラグ
   */
  printFlg: Or29976Const.DEFAULT.EMPTY,
})

const localOneway = reactive({
  at00014: { value: t('label.h21-revision-valid-label') },
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo00020OneWay: {
    showItemLabel: true,
    width: '132px',
  } as Mo00020OnewayType,
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  Or10464: {
    ...props.onewayModelValues,
  },
  Or29976: {
    ...props.onewayModelValue,
  },
  or04007OnewayType: {
    kikanFlg: props.onewayModelValue.kikanFlg,
    historySelectType: '',
  } as Or04007OnewayType,
  mo01338OneWayTitle: {
    value: t('label.title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayLeftTitle: {
    value: t('label.left-bracket'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayRightTitle: {
    value: t('label.right-bracket'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338FivWayTitle: {
    value: t('label.printer-muki'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338FouWayTitle: {
    value: t('label.printer-blank-form'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338NinWayTitle: {
    value: t('label.print-nonweekly-services'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none;',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00045OnewayTitleInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
  } as Mo00045OnewayType,
  mo00018OneWayChangeTitle: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00045OnewayTextInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '50',
    maxLength: '2',
  } as Mo00045OnewayType,
  mo00018OneWayPrintAuthor: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-author'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintMode: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.mono-chrome-printing-mode'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintTheForm: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayCareManagerInCharge: {
    value: t('label.care-manager-in-charge'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00009OnewayType: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo01338OneWayCareManagerInChargeLabel: {
    value: 'ほのぼの 三郎',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayBaseDate: {
    value: t('label.base-date'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 印刷設定帳票出力状態リスト
   */
  orX0117Oneway: {
    reportId: '',
    type: '1',
    reportData: {} as InWebEntity,
    outputType: reportOutputType.DOWNLOAD,
    historyList: [] as OrX0117History[],
    replaceKey: 'printHistoryList',
  } as OrX0117OnewayType,
  mo00040Oneway: {
    itemLabel: 'Or16649',
    showItemLabel: false,
    isRequired: false,
    customClass: {
      outerClass: 'mr-2',
      labelClass: 'ma-1',
      itemStyle: 'width: 240px;',
    } as CustomClass,
    hideDetails: true,
  } as Mo00040OnewayType,
  // 「印刷設定」ダイアログ
  mo00024Oneway: {
    width: 'auto',
    maxWidth: '1420px',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: false,
    mo01344Oneway: {
      toolbarTitle: t('label.print-set'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  },
  or29976Oneway: {
    ...props.onewayModelValue,
  } as Or29976OnewayType,
  mo00611OneWayClose: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // チェックボックス
  mo00018OnewayType: {
    showItemLabel: false,
    checkboxLabel: '',
    isVerticalLabel: true,
    hideDetails: true,
    outerDivClass: '',
  } as Mo00018OnewayType,
  mo00610OneWayCopy: {
    btnLabel: t('btn.seal-column'),
    disabled: false,
  } as Mo00610OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-0' }),
  } as OrX0145OnewayType,
})

/**
 * 担当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or29976Const.DEFAULT.IS_OPEN,
})

/**
 * 利用者
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width:305px',
  focusSettingInitial: localOneway.Or29976.focusSettingInitial,
})

/**************************************************
 * Pinia
 **************************************************/

const { setState } = useScreenOneWayBind<Or29976StateType>({
  cpId: Or29976Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or29976Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

const or10016Data: Or10016OnewayType = {
  /**
   * 法人ID
   */
  houjinId: '',
  /**
   * 施設ID
   */
  shisetsuId: '',
  /**
   * 職員ID
   */
  shokuinId: '',
  /**
   * システムコード
   */
  systemCode: '',
  /**
   * 事業所ID
   */
  jigyoshoId: '',
  /**
   * ログイン番号
   */
  loginNumber: '',
  /**
   * ログインユーザタイプ
   */
  loginUserType: '',
  /**
   * 電子カルテ連携フラグ
   */
  emrLinkFlag: '',
  /**
   * 帳票セクション番号
   */
  reportSectionNumber: '3GKU0P102P0011',
  /**
   * 引続情報.アセスメント
   */
  assessment: '4',
  /**
   * 引続情報.会議禄フラグ
   */
  conferenceFlag: true,
}

/**
 * 出力帳票名一覧
 */
const mo01334OnewayReport = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'defPrtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})
/**
 * 出力帳票名一覧
 */
const mo01334TypeReport = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 「担当ケアマネ」の監視
 */
watch(
  () => local.mo01408modelCare.value,
  async () => {}
)
/**
 * 変更データを取得する
 *
 * @returns 出力帳票印刷情報リスト
 */
async function getDataTable() {
  const choPrtList = mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => {
    if (id === mo01334TypeReport.value.value) {
      return {
        ...rest,
        prtTitle: local.titleInput.value,
        prndate: local.mo00039Type,
        param03: local.mo00018TypeChangeTitle.modelValue
          ? Or29976Const.DEFAULT.ZERO
          : Or29976Const.DEFAULT.ONE,
        param04: local.textInput.value,
        param05: local.mo00018TypePrintAuthor.modelValue
          ? Or29976Const.DEFAULT.ZERO
          : Or29976Const.DEFAULT.ONE,
        param06: local.mo00018TypePrintMode.modelValue
          ? Or29976Const.DEFAULT.ZERO
          : Or29976Const.DEFAULT.ONE,
        param07: local.mo00040.modelValue,
      }
    }
    return {
      ...rest,
    }
  }) as choPrtList[]

  refValue.value = { choPrtList: choPrtList }

  await nextTick()

  return choPrtList
}

/**
 * 帳票タイトルが入力していない場合
 */
async function checkTitleInput() {
  if (local.titleInput.value) return
  const dialogResult = await showOr21815MsgOneBtn()
  switch (dialogResult) {
    case 'yes': {
      let label = ''
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (mo01334TypeReport.value.value === item.id && item.mo01337OnewayReport) {
            const data = item.mo01337OnewayReport as Mo01337OnewayType
            label = data.value
          }
        }
      }
      local.titleInput.value = label
      break
    }
  }
  return
}
/**
 * エラーダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21813MsgOneBtn() {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21815MsgOneBtn() {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
const { refValue } = useScreenTwoWayBind<Or29976TwoWayData>({
  cpId: Or29976Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
onMounted(async () => {
  local.mo00020Type.value = systemCommonsStore.getSystemDate!
  orX0133OnewayModel.itemShowFlg = {
    createYmdShokuKnjFlg: true,
    caseNoFlg: true,
    tougaiYmFlg: true,
    kaiteiKnjFlg: true,
  }
  orX0133OnewayModel.kikanFlg = localOneway.Or29976.kikanFlg
  await getPrintSettingList()
  await initCodes()
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_EVERYWEEK },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  //日付印刷区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  //週単位以外のサービス
  localOneway.mo00040Oneway.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_EVERYWEEK
  ).map((item) => {
    return { value: item.value, title: item.label }
  })
  //単複数選択区分
  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  localOneway.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 初期値
  mo00039OneWayUserSelectType.value = Or29976Const.DEFAULT.TANI
  mo00039OneWayHistorySelectType.value = Or29976Const.DEFAULT.TANI
  local.mo00020TypeKijunbi.value = localOneway.Or29976.processDate
}

/**
 * 印刷設定画面初期情報を取得する
 */
async function getPrintSettingList() {
  const inputData: printSettingsScreenInitialInfoSelectGUI00986InEntity = {
    kikanFlg: localOneway.Or29976.kikanFlg,
    sysCd: systemCommonsStore.getSystemCode!,
    sysRyaku: systemCommonsStore.getSystemAbbreviation!,
    kinounameKnj: 'PRT',
    houjinId: systemCommonsStore.getHoujinId!,
    shisetuId: systemCommonsStore.getShisetuId!,
    svJigyoId: systemCommonsStore.getSvJigyoId!,
    userId: localOneway.Or29976.userId,
    shokuId: systemCommonsStore.getStaffId!,
    // 共通情報.担当ケアマネ設定フラグ > 0、かつ、共通情報.担当者IDが0以外の場合
    // 共通情報.担当者ID
    // 上記以外の場合
    // 0
    tantoId:
      localOneway.Or29976.careManagerInChargeSettingsFlag > 0 &&
      systemCommonsStore.getManagerId! !== Or29976Const.DEFAULT.ZERO
        ? systemCommonsStore.getManagerId!
        : Or29976Const.DEFAULT.ZERO,
    appYmd: systemCommonsStore.getStartDate!,
    sectionName: localOneway.Or29976.sectionName,
    choIndex: localOneway.Or29976.choIndex,
    kojinhogoFlg: Or29976Const.DEFAULT.ZERO,
    sectionAddNo: Or29976Const.DEFAULT.ZERO,
  }

  // バックエンドAPIから初期情報取得
  const ret: printSettingsScreenInitialInfoSelectGUI00986OutEntity = await ScreenRepository.select(
    'printSettingsScreenInitialInfoSelectGUI00986',
    inputData
  )


  selList = ret.data.selList

  const mo01334OnewayList: Mo01334Items[] = []
  for (const item of ret.data.choPrtList) {
    if (item) {
      mo01334OnewayList.push({
        id: item.prtNo,
        mo01337OnewayReport: {
          value: item.defPrtTitle,
          unit: '',
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
    }
  }
  mo01334OnewayReport.value.items = mo01334OnewayList

  if (ret.data.choPrtList.length > 0) mo01334TypeReport.value.value = ret.data.choPrtList[0].prtNo

  refValue.value = { choPrtList: ret.data.choPrtList }

  useScreenStore().setCpTwoWay({
    cpId: Or29976Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })

  //週間表履歴リスト
  if (ret.data.rirekiList.length > 0) {
    for (const item of ret.data.rirekiList) {
      if (localOneway.Or29976.kikanFlg === '1') {
        const tmpItem = {
          planPeriod:
            t('label.plan-period') +
            t('label.colon-mark') +
            item.startYmd +
            t('label.wavy') +
            item.endYmd,
        } as OrX0133TableData
        orX0133OnewayModel.rirekiList.push(tmpItem)
      }
      for (const e of item.historyList) {
        let shokuinKnj = '' as string
        for (const s of selList) {
          if (e.shokuId === s.chkShokuId) {
            shokuinKnj = s.shokuinKnj
          }
        }
        const tmpItem1 = {
          planPeriod: '',
          createYmd: e.createYmd,
          kijunbiYmd: '',
          shokuKnj: shokuinKnj,
          shokuinKnj: '',
          caseNo: e.caseNo,
          tougaiYm: e.tougaiYm,
          kaisuu: '',
          kaiteiKnj: e.kaiteiFlg,
          youshikiKnj: '',
          gdlId: '',
          assType: '',
          assDateYmd: '',
        } as OrX0133TableData
        orX0133OnewayModel.rirekiList.push(tmpItem1)
      }
    }
  }

  reportInitData.value = ret.data.iniDataObject
}

/**
 * 帳票イニシャライズデータを取得する。
 */
async function getReportInfoDataList() {
  const inputData: LedgerInitializeDataComSelectInEntity = {
    sysCd: localOneway.Or29976.sysCd,
    kinounameKnj: Or29976Const.DEFAULT.KINOU_NAMEKNJ,
    shokuId: localOneway.Or29976.shokuId,
    sectionKnj: choPro.value,
    kojinhogoFlg: Or29976Const.DEFAULT.ZERO,
    sectionAddNo: Or29976Const.DEFAULT.ZERO,
  }
  // バックエンドAPIから初期情報取得
  const ret: LedgerInitializeDataComSelectOutEntity = await ScreenRepository.select(
    'ledgerInitializeDataComSelect',
    inputData
  )
  reportInitData.value = ret.data.iniDataObject
}

/**
 * 「履歴選択方法」ラジオボタン押下 単一複数
 */
watch(
  () => mo00039OneWayHistorySelectType.value,
  (newValue) => {
    // 履歴選択方法が「単一」の場合
    if (newValue === OrX0128Const.DEFAULT.TANI) {
      orX0133OnewayModel.singleFlg = OrX0133Const.DEFAULT.TANI
      localOneway.orX0117Oneway.type = '1'
      //画面.記入用シートを印刷するを活性表示にする
      localOneway.mo00018OneWayPrintTheForm.disabled = false
    } else {
      orX0133OnewayModel.singleFlg = OrX0133Const.DEFAULT.HUKUSUU
      localOneway.orX0117Oneway.type = '0'
      //画面.記入用シートを印刷するを非活性表示にする
      localOneway.mo00018OneWayPrintTheForm.disabled = true
    }
  }
)

/**
 * 履歴情報を取得する
 *
 *   @param userId - 利用者ID
 */
async function getHistoricalInfoList(userId: string) {
  orX0133OnewayModel.rirekiList = []
  const inputData: WeekTableHistoryInfoSelectInEntity = {
    svJigyoId: systemCommonsStore.getSvJigyoId!,
    userId: userId,
  }
  // バックエンドAPIから初期情報取得
  const ret: WeekTableHistoryInfoSelectOutEntity = await ScreenRepository.select(
    'weekTableHistoryInfoSelect',
    inputData
  )
  //週間表履歴リスト
  if (ret.data.rirekiList.length > 0) {
    for (const item of ret.data.rirekiList) {
      if (localOneway.Or29976.kikanFlg === '1') {
        const tmpItem = {
          planPeriod:
            t('label.plan-period') +
            t('label.colon-mark') +
            item.startYmd +
            t('label.wavy') +
            item.endYmd,
        } as OrX0133TableData
        orX0133OnewayModel.rirekiList.push(tmpItem)
      }
      if (item.historyList.length > 0) {
        for (const e of item.historyList) {
          let shokuinKnj = '' as string
          for (const s of selList) {
            if (e.shokuId === s.chkShokuId) {
              shokuinKnj = s.shokuinKnj
            }
          }
          const tmpItem1 = {
            planPeriod: '',
            createYmd: e.createYmd,
            kijunbiYmd: '',
            shokuKnj: shokuinKnj,
            shokuinKnj: '',
            caseNo: e.caseNo,
            tougaiYm: e.tougaiYm,
            kaisuu: '',
            kaiteiKnj: e.kaiteiFlg,
            youshikiKnj: '',
            gdlId: e.week1Id,
            assType: '',
            assDateYmd: '',
          } as OrX0133TableData
          orX0133OnewayModel.rirekiList.push(tmpItem1)
        }
      }
    }
  }
}

/**
 * 印刷設定情報を保存する
 *
 * @param choPrtList - 出力帳票印刷情報リスト
 */
// async function savePrintSettingInfo(choPrtList: choPrtList[]) {
// }

/**
 * 「利用者選択方」ラジオボタン選択 単一複数
 */
watch(
  () => mo00039OneWayUserSelectType.value,
  (newValue) => {
    // 利用者選択方法が「単一」の場合
    if (newValue === OrX0128Const.DEFAULT.TANI) {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width: 305px'
      localOneway.mo00018OneWayPrintTheForm.disabled = false
      userCols.value = 6
      mo01334TypeHistoryFlag.value = true
      localOneway.orX0117Oneway.type = Or29976Const.DEFAULT.TANI
    } else {
      localOneway.orX0117Oneway.type = Or29976Const.DEFAULT.HUKUSUU
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width: 430px'
      localOneway.mo00018OneWayPrintTheForm.disabled = true
      userCols.value = 11
      mo01334TypeHistoryFlag.value = false
    }
  }
)

/**
 * ラジオボタンの選択状態を追跡する
 */
watch(
  () => local.mo00039Type,
  async () => {
    await checkTitleInput()
  }
)

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334TypeReport.value.value,
  async (newValue, oldValue) => {
    if (oldValue) {
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (oldValue === item.id) {
            // 画面.帳票タイトルが空白以外の場合
            // 画面.出力帳票一覧明細に切替前選択される行.帳票タイトル = 画面.帳票タイトル
            if (local.titleInput.value) item.prtTitle = local.titleInput.value
            // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
            item.prndate = local.mo00039Type
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ03 = 画面.敬称を変更する
            item.param03 = local.mo00018TypeChangeTitle.modelValue
              ? Or29976Const.DEFAULT.ONE
              : Or29976Const.DEFAULT.ZERO
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ04 = 画面.敬称
            item.param04 = local.textInput.value
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ05 = 画面.作成者を印刷する
            item.param05 = local.mo00018TypePrintAuthor.modelValue
              ? Or29976Const.DEFAULT.ONE
              : Or29976Const.DEFAULT.ZERO
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ06 = 画面.モノクロ印刷モード
            item.param06 = local.mo00018TypePrintMode.modelValue
              ? Or29976Const.DEFAULT.ONE
              : Or29976Const.DEFAULT.ZERO
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ07 = 画面.印刷する週単位以外のサービス
            item.param07 = local.mo00040.modelValue
          }
        }
      }
    }
    for (const item of mo01334OnewayReport.value.items) {
      if (item) {
        if (newValue === item.id) {
          // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
          local.titleInput.value = item?.prtTitle as string
          // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
          local.mo00039Type = item?.prndate as string
          // 画面.敬称を変更する = 画面.出力帳票一覧明細に選択される行.パラメータ03
          local.mo00018TypeChangeTitle.modelValue =
            item?.param03 === Or29976Const.DEFAULT.ONE ? true : false
          // 画面.敬称 = 画面.出力帳票一覧明細に選択される行.パラメータ04
          local.textInput.value = item?.param04 as string
          // 画面.記入用シートを印刷する = 0(チェックオフ)
          local.mo00018TypePrintTheForm.modelValue = true
          // 画面.作成者を印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ05
          local.mo00018TypePrintAuthor.modelValue =
            item?.param05 === Or29976Const.DEFAULT.ONE ? true : false
          // 画面.画面.モノクロ印刷モード = 画面.出力帳票一覧明細に選択される行.パラメータ06
          local.mo00018TypePrintMode.modelValue =
            item?.param06 === Or29976Const.DEFAULT.ONE ? true : false
          // 画面.印刷する週単位以外のサービス = 画面.出力帳票一覧明細に選択される行.パラメータ07
          local.mo00040.modelValue = item?.param07 as string

          choPro.value = item?.choPro as string
          // 帳票番号
          local.prtNo = item.prtNo as string
          // 帳票セクション番号
          local.sectionNo = item.sectionNo as string

          await getReportInfoDataList()
        }
      }
    }
  }
)

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

/**
 * 「閉じるボタン」押下
 */
async function close() {
  await checkTitleInput()
  const choPrtList = await getDataTable()
  if (isEdit.value) await savePrintSettingInfo(choPrtList)
  setState({ isOpen: false })
}

/**
 * 「印鑑欄」ボタン押下
 */
function open() {
  // Or10016のダイアログ開閉状態を更新する
  Or10016Logic.state.set({
    uniqueCpId: or10016.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「印刷」ボタン押下
 */
async function print() {
  await checkTitleInput()
  if (!choPro.value) await showOr21813MsgOneBtn()

  const userList = OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList
  const historyList = OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList
  // 利用者選択方法が「単一」、履歴一覧明細にデータを選択しない場合
  // 処理中断
  if (
    mo00039OneWayUserSelectType.value === Or29976Const.DEFAULT.TANI &&
    (historyList === undefined || historyList.length === 0)
  )
    return

  // // 利用者選択方法が「複数」、親画面.利用者情報リストにデータを選択しない場合
  // // 処理中断
  if (
    mo00039OneWayUserSelectType.value === Or29976Const.DEFAULT.HUKUSUU &&
    (userList === undefined || userList.length === 0)
  )
    return
  // 利用者選択方法が「単一」
  if (Or29976Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 履歴選択が「単一」
    if (Or29976Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      //記入用シートを印刷するチェック入れるの場合
      if (local.mo00018TypePrintTheForm.modelValue) {
        const value = OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList[0].gdlId
        await downloadPdf(value)
        return
      }
      // 記入用シートを印刷するチェック外すの場合
      else {
        // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
        // if (local.userSelect) {
        //   const dialogResult = await openConfirmDialog(
        //     or21814.value.uniqueCpId,
        //     t('message.i-cmn-11393')
        //   )
        //   // はい
        //   if (dialogResult === Or29976Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
        //     // 処理終了
        //     return
        //   }
        // }
        // 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
        // if (!OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList.length) {
        //   const dialogResult = await openConfirmDialog(
        //     or21814.value.uniqueCpId,
        //     t('message.i-cmn-11455')
        //   )
        //   // はい
        //   if (dialogResult === Or29976Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
        //     // 処理終了
        //     return
        //   }
        // }
      }
    }
    // 履歴選択方法が「複数」
    else if (Or29976Const.DEFAULT.HUKUSUU === mo00039OneWayHistorySelectType.value) {
      // 履歴情報リストにデータを選択する場合
      if (OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList.length) {
        // 印刷設定情報リストを作成
        const list = OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList
        list?.forEach((item) => {
          if (item.kaiteiKnj) {
            const value = item.gdlId
            void downloadPdf(value)
          }
        })
      }
    }
  }

  //印刷設定情報保存
  const choPrtList = await getDataTable()

  if (isEdit.value) await savePrintSettingInfo(choPrtList)

  // OrX0117のダイアログ開閉状態を更新する
  OrX0117Logic.state.set({
    uniqueCpId: orX0117.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * pdf download
 *
 * @param val - id
 */

const downloadPdf = async (val: unknown) => {

  const inputData: ShuuKanReportEntity = {
    svJigyoId: localOneway.Or29976.svJigyoId,
    houjinID: localOneway.Or29976.houjinId,
    shisetuId: localOneway.Or29976.shisetuId,
    svJigyoName: localOneway.Or29976.svJigyoName,
    shokuId: localOneway.Or29976.shokuId,
    userId: localOneway.Or29976.userId,
    sectionNo: local.sectionNo,
    week1Id: val,
    printSet: {
      shiTeiDate: local.mo00020Type.value,
      shiTeiKubun: local.mo00039Type,
      inkanFlg: local.printFlg,
    },
    printOption: {
      keishoFlg: local.mo00018TypeChangeTitle.modelValue ? '1' : '0',
      keisho: local.textInput.value,
      emptyFlg: local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
      sakuNameFlag: local.mo00018TypePrintAuthor ? '1' : '0',
      serviceFlag: local.mo00040.modelValue,
    },
  } as ShuuKanReportEntity
  console.log(inputData, '===========')
  switch (local.prtNo) {
    case Or29976Const.PRINT_NO_1:
      local.reportId = Or29976Const.PDF_DOWNLOAD_REPORT_ID.GENER_ALL
      break
    default:
      local.reportId = Or29976Const.DEFAULT.EMPTY
      break
  }

  // 帳票出力
  await reportOutput(local.reportId, inputData, reportOutputType.DOWNLOAD)
  return
}

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickFlg) {
      // 利用者選択
      // 利用者選択方法が「単一」の場合
      await getHistoricalInfoList(newValue.userList[0].userId)
    }
  }
)

/**
 * 印刷設定情報を保存する
 *
 * @param choPrtList - 出力帳票印刷情報リスト
 */
async function savePrintSettingInfo(choPrtList: choPrtList[]) {
  const inputData: PrintSettingsInfoComUpdateInEntity = {
    sysCd: localOneway.Or29976.sysCd,
    sysRyaku: localOneway.Or29976.sysRyaku,
    kinounameKnj: Or29976Const.DEFAULT.KINOU_NAMEKNJ,
    houjinId: localOneway.Or29976.houjinId,
    shisetuId: localOneway.Or29976.shisetuId,
    svJigyoId: localOneway.Or29976.svJigyoId,
    shokuId: localOneway.Or29976.shokuId,
    choPro: choPro.value,
    kojinhogoFlg: Or29976Const.DEFAULT.ZERO,
    sectionAddNo: Or29976Const.DEFAULT.ZERO,
    iniDataObject: { ...reportInitData.value },
    choPrtList: choPrtList,
    sectionName: '',
  }

  // バックエンドAPIから初期情報取得
  await ScreenRepository.update('printSettingsInfoComUpdate', inputData)
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or29976_screen"
      >
        <c-v-col
          cols="12"
          sm="2"
          class="pa-0 pt-2 px-2 or29976_border_right"
        >
          <!-- 帳票 -->
          <base-mo-01334
            v-model="mo01334TypeReport"
            :oneway-model-value="mo01334OnewayReport"
            class="list-wrapper"
          >
            <!-- （Ⅳ）週間表  -->
            <template #[`item.defPrtTitle`]="{ item }">
              <base-mo01337 :oneway-model-value="item.mo01337OnewayReport" />
            </template>
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="4"
          class="pa-0 pt-2 or29976_border_right content_center"
        >
          <c-v-row style="margin-left: 8px; margin-top: 8px; margin-bottom: 8px">
            <!--印鑑欄-->
            <base-mo00610
              class="mr-2"
              :oneway-model-value="localOneway.mo00610OneWayCopy"
              @click="open()"
            />
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="or29976_row flex-center"
          >
            <c-v-col
              cols="12"
              sm="3"
              class="pa-2"
            >
              <!-- タイトル  -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <base-mo00045
                v-model="local.titleInput"
                :oneway-model-value="localOneway.mo00045OnewayTitleInput"
                @keyup.enter="checkTitleInput"
              />
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or29976_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              class="pa-2"
            >
              <!-- 印刷日付選択ラジオボタングループ -->
              <base-mo00039
                v-model="local.mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              id="test"
              cols="12"
              sm="5"
              class="pa-2"
            >
              <!-- 印刷日付ラベル -->
              <base-mo00020
                v-show="local.mo00039Type == '2'"
                v-model="local.mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
                @mousedown="checkTitleInput"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="printerOption customCol or29976_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <!-- 印刷オプションセクション -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or29976_row row-checkbox"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2 d-flex"
            >
              <!-- 敬称を変更するチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypeChangeTitle"
                :oneway-model-value="localOneway.mo00018OneWayChangeTitle"
              >
              </base-mo00018>
              <!-- 敬称左ラベル  -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayLeftTitle"></base-mo01338>
              <!-- 敬称テキストボックス -->
              <base-mo00045
                v-model="local.textInput"
                :oneway-model-value="localOneway.mo00045OnewayTextInput"
                :disabled="!local.mo00018TypeChangeTitle.modelValue"
              />
              <!-- 敬称右ラベル  -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayRightTitle"
              ></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <!-- 記入用シートを印刷するチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypePrintTheForm"
                :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <!-- 作成者を印刷するチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypePrintAuthor"
                :oneway-model-value="localOneway.mo00018OneWayPrintAuthor"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <!-- モノクロ印刷モードチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypePrintMode"
                :oneway-model-value="localOneway.mo00018OneWayPrintMode"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2 d-flex"
            >
              <!-- 印刷する週単位以外のサービス -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338NinWayTitle"
                style="margin-left: 5px"
              ></base-mo01338>
              <!--印刷する週単位以外のサービスプルダウン-->
              <base-mo00040
                v-model="local.mo00040"
                :oneway-model-value="localOneway.mo00040Oneway"
              />
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <!-- ※H21改訂版のみ有効 -->
              <custom-at00014 :oneway-model-value="localOneway.at00014"> </custom-at00014>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          class="pa-2"
        >
          <c-v-row
            class="or29976_row"
            no-gutter
            style="align-items: center"
          >
            <c-v-col
              cols="12"
              sm="6"
              class="pa-2 col_height col-paddding"
            >
              <c-v-row
                class="or29976_row"
                style="padding-bottom: 8px"
              >
                <!-- 利用者選択ラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                  style="background-color: transparent"
                >
                </base-mo01338
              ></c-v-row>
              <c-v-row class="or29976_row">
                <!-- 利用者選択ラジオボタングループ -->
                <base-mo00039
                  v-model="mo00039OneWayUserSelectType"
                  :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                >
                </base-mo00039>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="mo00039OneWayUserSelectType === Or29976Const.DEFAULT.HUKUSUU"
              cols="12"
              sm="6"
              class="pa-2 col_height"
            >
              <c-v-row class="or29976_row">
                <!-- 基準日  -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayBaseDate"
                  style="background-color: transparent"
                >
                </base-mo01338>
              </c-v-row>
              <c-v-row class="or29976_row">
                <base-mo00020
                  v-model="local.mo00020TypeKijunbi"
                  :oneway-model-value="localOneway.mo00020KijunbiOneWay"
                />
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="mo00039OneWayUserSelectType === Or29976Const.DEFAULT.TANI"
              cols="12"
              sm="6"
              class="pa-2 col_height col-paddding"
            >
              <c-v-row
                class="or29976_row"
                style="padding-bottom: 8px"
              >
                <!-- 履歴選択ラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                  style="background-color: transparent"
                >
                </base-mo01338>
              </c-v-row>
              <c-v-row class="or29976_row">
                <!-- 履歴選択-ラジオボタングループ -->
                <base-mo00039
                  v-model="mo00039OneWayHistorySelectType"
                  :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                >
                </base-mo00039>
              </c-v-row>
            </c-v-col>

            <c-v-col
              v-if="
                systemCommonsStore.getProcessDate! !== '' &&
                (localOneway.Or29976.careManagerInChargeSettingsFlag <= 0 ||
                  systemCommonsStore.getManagerId! === '0')
              "
              cols="12"
              sm="12"
              class="pa-2 flex-center"
            >
              <!-- 担当ケアマネプルダウン -->
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
                class="search-tack"
                style="display: flex"
              ></g-custom-or-x-0145>
            </c-v-col>
          </c-v-row>
          <c-v-row
            class="or29976_row or29235-x0130"
            no-gutter
          >
            <c-v-col :cols="userCols">
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag"
              cols="12"
              sm="6"
              style="padding-left: 8px; padding-right: 0px"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0133
                v-if="orX0133OnewayModel.singleFlg"
                v-bind="orX0133"
                :oneway-model-value="orX0133OnewayModel"
              ></g-custom-or-x-0133>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          :disabled="!prtFlg"
          @click="print()"
        >
          <!--ツールチップ表示："PDFをダウンロードします"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.pdf-download')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  >
  </g-base-or21813>
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  >
  </g-base-or21815>
  <!-- GUI01110_「印鑑欄設定」画面を起動する -->
  <g-custom-or-10016
    v-if="showDialogOr10016"
    v-bind="or10016"
    :oneway-model-value="or10016Data"
  />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrx0117"
    v-bind="orX0117"
    :oneway-model-value="localOneway.orX0117Oneway"
  ></g-custom-or-x-0117>
</template>

<style scoped lang="scss">
.or29976_screen {
  margin: -8px !important;
}

.or29976_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or29976_row {
  margin: 0px !important;
}

.row-checkbox {
  padding-top: 8px;
  > div {
    padding-top: 0px !important;
  }
}

.or29235-x0130 {
  > .v-col {
    padding: 0 !important;
  }
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.customCol {
  margin-left: 0px;
  margin-right: 0px;
}
.col_height {
  height: 70px;
}
.col-paddding {
  padding: 0 !important;
}

:deep(.search-tack) {
  margin-left: 0px;
  .ma-0 {
    margin-right: 8px !important;
  }
  .v-input__control {
    width: 200px;
  }
}
</style>
