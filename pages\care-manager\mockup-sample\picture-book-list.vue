<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { OrSampleCarePlan2PopupConst } from '~/components/custom-components/organisms/OrSampleCarePlan2Popup/OrSampleCarePlan2Popup.constants'
import { OrSampleCarePlan2PopupLogic } from '~/components/custom-components/organisms/OrSampleCarePlan2Popup/OrSampleCarePlan2Popup.logic'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
// 画面ID
const screenId = 'picture-book-list'
// ルーティング
const routing = 'picture-book-list/pinia'
// 画面物理名
const screenName = 'picture-book-list'

// 画面状態管理用操作変数
const screenStore = useScreenStore()
/**************************************************
 * 画面固有処理
 **************************************************/
const orSampleCarePlan2Popup = ref({ uniqueCpId: OrSampleCarePlan2PopupConst.CP_ID(1) })
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'picture-book-list' },
})

// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'picture-book-list',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: OrSampleCarePlan2PopupConst.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [OrSampleCarePlan2PopupConst.CP_ID(1)]: orSampleCarePlan2Popup.value,
})
// コンポーネントの初期化処理を開始する
OrSampleCarePlan2PopupLogic.initialize(orSampleCarePlan2Popup.value.uniqueCpId)
/**
 * サンプル_計画書（２） TAB画面
 */
function openCarePlan2() {
  window.open('/care-manager/care-plan2', '_blank')
}

// ダイアログ表示フラグ
const showDialogOrSampleCarePlan2Popup = computed(() => {
  // Or32525のダイアログ開閉状態
  return (
    OrSampleCarePlan2PopupLogic.state.get(orSampleCarePlan2Popup.value.uniqueCpId)?.isOpen ?? false
  )
})
/**
 * ボタン押下時の処理(サンプル_計画書（２）ポップアップ)
 */
function onClickOrSampleCarePlan2Popup() {
  screenStore.screen().supplement.isInit = true
  // サンプル_計画書（２）ポップアップのダイアログ開閉状態を更新する
  OrSampleCarePlan2PopupLogic.state.set({
    uniqueCpId: orSampleCarePlan2Popup.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters><c-v-col>ケアマネモックアップ開発画面確認用ページ</c-v-col></c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >前のページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/care-manager/mockup-sample/picture-book-message-box"
        >共通有機体_Or21813_Or21814_Or21815_サンプル_メッセージボックス編
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/care-manager/mockup-sample/picture-book-buttons"
        >共通有機体_サンプル_ボタンレイアウト編
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- 週間計画カレンダーサンプル	POP画面ポップアップ KMD 陸欣 2025/04/11 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/care-manager/mockup-sample/sample-week-plan-calendar"
        value="サンプル_OrX0048_週間計画カレンダー"
        >サンプル_OrX0048_週間計画カレンダー
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_計画書（２）POPUP	POP画面ポップアップ KMD 陸欣 2025/05/13 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_care-plan2_計画書（２）ポップアップ"
        @click="onClickOrSampleCarePlan2Popup"
        >サンプル_care-plan2_計画書（２）ポップアップ
      </v-btn>
      <g-custom-orSampleCarePlan2Popup
        v-if="showDialogOrSampleCarePlan2Popup"
        v-bind="orSampleCarePlan2Popup"
      />
    </c-v-col>
  </c-v-row>
  <!-- サンプル_計画書（２ TAB画面ポップアップ KMD 陸欣 2025/05/13 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_care-plan2_計画書（２） タブ"
        @click="openCarePlan2"
        >サンプル_care-plan2_計画書（２） タブ
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_ガントカレンダー KMD 張敏捷 2025/05/13 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_OrX0123_ガントカレンダー"
        to="/care-manager/mockup-sample/sample-gantt-calendar"
        >サンプル_OrX0123_ガントカレンダー
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_印刷設定画面利用者 KMD 張敏捷 2025/05/5 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="OrX0130_印刷設定画面利用者一覧_OrX0133_計画期間＆履歴一覧"
        to="/care-manager/mockup-sample/sample-print-user-info"
        >サンプル_OrX0130_印刷設定画面利用者一覧_OrX0133_計画期間＆履歴一覧
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_Mo01408カスタムフィルタリング KMD 陸欣 2025/06/25 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_Mo01408_カスタムフィルタリング"
        to="/care-manager/mockup-sample/sample-mo01408-custom-filter"
        >サンプル_Mo01408_カスタムフィルタリング
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_サンプル_担当ケアマネ KMD 陸欣 2025/07/07 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_OrX0145_担当ケアマネ"
        to="/care-manager/mockup-sample/sample-tanto-cmn"
        >サンプル_OrX0145_担当ケアマネ
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_OrX0147_利用者情報 KMD 陸欣 2025/07/17 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_OrX0147_利用者情報"
        to="/care-manager/mockup-sample/sample-orx0147-user-info"
        >サンプル_OrX0147_利用者情報
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_OrX0155利用者一覧ケアマネ独自のコンポネント KMD 陸欣 2025/07/22 ADD START -->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_OrX0155利用者一覧ケアマネ独自のコンポネント"
        to="/care-manager/mockup-sample/sample-orx0155-user-select"
        >サンプル_OrX0155利用者一覧ケアマネ独自のコンポネント
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_モーダル選択式入力フォーム（テキストエリア） KMD張敏捷  2025/07/18 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_モーダル選択式入力フォーム（テキストエリア）"
        to="/care-manager/mockup-sample/sample-mo-modal-select-textarea"
        >サンプル_モーダル選択式入力フォーム（テキストエリア）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_モーダル選択式入力フォーム（テキストフィールド） KMD張敏捷  2025/07/18 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_モーダル選択式入力フォーム（テキストフィールド）"
        to="/care-manager/mockup-sample/sample-mo-modal-select-textfield"
        >サンプル_モーダル選択式入力フォーム（テキストフィールド）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_モーダル選択式入力フォーム（プルダウン） KMD張敏捷  2025/07/18 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_モーダル選択式入力フォーム（プルダウン）"
        to="/care-manager/mockup-sample/sample-mo-modal-select-select-field"
        >サンプル_モーダル選択式入力フォーム（プルダウン）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_表用モーダル選択式入力フォーム（テキストフィールド） KMD張敏捷  2025/07/18 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_表用モーダル選択式入力フォーム（テキストフィールド）"
        to="/care-manager/mockup-sample/sample-mo-table-fix-modal-select-textfield"
        >サンプル_表用モーダル選択式入力フォーム（テキストフィールド）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_表用モーダル選択式入力フォーム（テキストエリア） KMD張敏捷  2025/07/18 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_表用モーダル選択式入力フォーム（テキストエリア）"
        to="/care-manager/mockup-sample/sample-mo-table-fix-modal-select-textarea"
        >サンプル_表用モーダル選択式入力フォーム（テキストエリア）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_	表用モーダル選択式入力フォーム（プルダウン） KMD張敏捷  2025/07/18 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_表用モーダル選択式入力フォーム（プルダウン）"
        to="/care-manager/mockup-sample/sample-mo-table-fix-modal-select-select-field"
        >サンプル_ 表用モーダル選択式入力フォーム（プルダウン）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_OrX0157_入力支援付きテキストフィールドのコンポネント KMD 陸欣 2025/07/23 ADD START -->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_OrX0157_入力支援付きテキストフィールド
"
        to="/care-manager/mockup-sample/sample-orx0157-textfield"
        >サンプル_OrX0157_入力支援付きテキストフィールド
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_OrX0158_入力支援付きセレクトのコンポネント  KMD 陸欣 2025/07/24 ADD START -->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_OrX0158_入力支援付きセレクト
"
        to="/care-manager/mockup-sample/sample-orx0158-select"
        >サンプル_OrX0158_入力支援付きセレクト
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- サンプル_OrX0160_入力補助付き期間入力のコンポネント  KMD 陸欣 2025/07/23 ADD START -->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        value="サンプル_OrX0160_入力補助付き期間入力
"
        to="/care-manager/mockup-sample/sample-orx0160-kikan"
        >サンプル_OrX0160_入力補助付き期間入力
      </v-btn>
    </c-v-col>
  </c-v-row>
</template>
