import { Or25957Const } from '../Or25957/Or25957.constants'
import { Or25957Logic } from '../Or25957/Or25957.logic'
import { OrX0160Const } from './OrX0160.constants'
import { useInitialize } from '~/composables/useComponentLogic'

/**
 * OrX0160Logic:入力支援付き期間入力
 *
 * @description
 * initialize処理を提供する
 *
 * <AUTHOR>
 */
export namespace OrX0160Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: OrX0160Const.CP_ID(0),
      uniqueCpId,
      childCps: [{ cpId: Or25957Const.CP_ID(0) }, { cpId: Or25957Const.CP_ID(1) }],
      // 編集フラグ不要
      // ※ 元々双方向領域を持たないため記載不要だが、サンプル用にナビゲーション制御領域で持つデータを分かりやすくするために設定
      editFlgNecessity: false,
    })

    Or25957Logic.initialize(childCpIds[Or25957Const.CP_ID(0)].uniqueCpId)
    Or25957Logic.initialize(childCpIds[Or25957Const.CP_ID(1)].uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
}
