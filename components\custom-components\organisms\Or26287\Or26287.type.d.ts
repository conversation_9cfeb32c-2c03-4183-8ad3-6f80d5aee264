/**
 * Or26287Const:有機体:サービス選択モーダル
 * GUI01151_［サービス選択モーダル］画面
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> 靳先念
 */
export interface Or26287StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * 種類リスト
 */
export interface selectType {
  /**
   * サービス種類
   */
  svShuruiCd: string
  /**
   * サービス種類名
   */
  svShuruiName: string
}

/**
 * 総合事業種類コードリスト
 */
export interface isCdSougouType {
  /** サービス有効期間ID */
  svShuruiTermid: string
  /** サービス種類CD */
  svShuruiCd: string
  /** サービス種類名 */
  svShuruiName: string
  /** サービス種類略称 */
  svShuruiNameKnj: string
  /** サービス種類CD（メイン） */
  svShuruiCdMain: string
  /** 更新回数 */
  modifiedCnt: string
}
