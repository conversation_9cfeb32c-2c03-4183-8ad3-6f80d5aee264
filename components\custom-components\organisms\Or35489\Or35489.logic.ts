import { Or28626Const } from '../Or28626/Or28626.constants'
import { Or28626Logic } from '../Or28626/Or28626.logic'
import { OrX0154Const } from '../OrX0154/OrX0154.constants'
import { OrX0154Logic } from '../OrX0154/OrX0154.logic'
import { Or35489Const } from './Or35489.constants'
import type { Or35489StateType } from './Or35489.type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { OrX0053Const } from '~/components/custom-components/organisms/OrX0053/OrX0053.constants'
import { OrX0053Logic } from '~/components/custom-components/organisms/OrX0053/OrX0053.logic'

/**
 * Or35489:処理ロジック
 * GUI00995_パターン（グループ）
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR>
 */
export namespace Or35489Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or35489Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or28626Const.CP_ID(0) },
        { cpId: OrX0053Const.CP_ID(0) },
        { cpId: OrX0154Const.CP_ID(0) },
        { cpId: Or21813Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(0) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or28626Logic.initialize(childCpIds[Or28626Const.CP_ID(0)].uniqueCpId)
    OrX0053Logic.initialize(childCpIds[OrX0053Const.CP_ID(0)].uniqueCpId)
    OrX0154Logic.initialize(childCpIds[OrX0154Const.CP_ID(0)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(0)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or35489StateType>(Or35489Const.CP_ID(0))
}
