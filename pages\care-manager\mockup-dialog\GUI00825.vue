<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  watch,
} from '#imports'

import { Or09997Const } from '~/components/custom-components/organisms/Or09997/Or09997.constants'
import { Or09997Logic } from '~/components/custom-components/organisms/Or09997/Or09997.logic'
import type { Or09997Type, Or09997OnewayType } from '~/types/cmn/business/components/Or09997Type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00825'
// ルーティング
const routing = 'GUI00825/pinia'
// 画面物理名
const screenName = 'GUI00825'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or09997 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00825' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or09997.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00825',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or09997Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or09997Const.CP_ID(1)]: or09997.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or09997Logic.initialize(or09997.value.uniqueCpId)
}

const or09997Type = ref<Or09997Type>({
  sortList: [],
})

// ダイアログ表示フラグ
const showDialogOr09997 = computed(() => {
  // Or09997のダイアログ開閉状態
  return Or09997Logic.state.get(or09997.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or09997)
 *
 */
function onClickOr09997() {
  // Or09997のダイアログ開閉状態を更新する
  Or09997Logic.state.set({
    uniqueCpId: or09997.value.uniqueCpId,
    state: { isOpen: true },
  })
}

watch(or09997Type, () => {
  console.log(or09997Type.value)
})

/**
 * 親画面からの初期値
 */
const or09997Data: Or09997OnewayType = {
  sortList: [
    {
      id: '1',
      issues: '111助介し出きかの物食',
      goal: '一部介助は必要だが、自分から食事摂取',
      seqNo: '1',
    },
    {
      id: '2',
      issues: '222精神的に不安になる出来事があったようで(内容はまだわからな',
      goal: 'ベットにて安静にさせる',
      seqNo: '2',
    },
    {
      id: '3',
      issues:
        '333悪寒かある為、体を温ある。しかし、悪寒かある為、体を温ある。しかし、悪寒かある為、体を温ある。しかし、悪寒かある為、体を温ある。しかし、悪寒かある為、体を温ある。しかし、悪寒かある為、体を温ある。しかし、悪寒かある為、体を温ある。しかし、悪寒かある為、体を温ある。',
      goal: '以前高血圧で入院されていたが、今は安定している。しかし、気持以前高血圧で入院されていたが、今は安定している。しかし、気持以前高血圧で入院されていたが、今は安定している。しかし、気持以前高血圧で入院されていたが、今は安定している。しかし、気持以前高血圧で入院されていたが、今は安定している。しかし、気持以前高血圧で入院されていたが、今は安定している。しかし、気持',
      seqNo: '3',
    },
    {
      id: '4',
      issues: '444悪寒かある為、体を温ある2',
      goal: '以前高血圧で入院されていたが、今は安定している。しかし、気持',
      seqNo: '4',
    },
    {
      id: '5',
      issues: '555悪寒かある為、体を温ある3',
      goal: '以前高血圧で入院されていたが、今は安定している。しかし、気持',
      seqNo: '5',
    },
    {
      id: '6',
      issues: '666悪寒かある為、体を温ある4',
      goal: '以前高血圧で入院されていたが、今は安定している。しかし、気持以前高血圧で入院されていたが、今は安定している。しかし、気持以前高血圧で入院されていたが、今は安定している。しかし、気持以前高血圧で入院されていたが、今は安定している。しかし、気持以前高血圧で入院されていたが、今は安定している。しかし、気持以前高血圧で入院されていたが、今は安定している。しかし、気持',
      seqNo: '6',
    },
    {
      id: '7',
      issues: '777悪寒かある為、体を温ある5',
      goal: '以前高血圧で入院されていたが、今は安定している。しかし、気持',
      seqNo: '7',
    },
  ],
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr09997"
        >GUI00825_表示順変更アセスメント
      </v-btn>
      <g-custom-or-09997
        v-if="showDialogOr09997"
        v-bind="or09997"
        v-model="or09997Type"
        :oneway-model-value="or09997Data"
      />
    </c-v-col>
  </c-v-row>
</template>
