<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import {
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  useValidation,
} from '#imports'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { OrX0157Const } from '~/components/custom-components/organisms/OrX0157/OrX0157.constants'
import type { OrX0157Type, OrX0157OnewayType } from '~/types/cmn/business/components/OrX0157Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
const { t } = useI18n()
const validation = useValidation()
/**************************************************
 * 変数定義
 **************************************************/
// 画面ID
const screenId = 'sampleOrX0157Textfield'
// ルーティング
const routing = 'sampleOrX0157Textfield'
// 画面物理名
const screenName = 'サンプル_OrX0157_入力支援付きテキストフィールド'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'sampleOrX0157Textfield' },
})
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'sampleOrX0157Textfield',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or21814Const.CP_ID(0) }],
})
// 子コンポーネントのセットアップ
Or21814Logic.initialize(init.childCpIds.Or21814.uniqueCpId)
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
})
const orX0157Type = ref<OrX0157Type>({ value: 'テスト値' })
const orX0157HalfWidthType = ref<OrX0157Type>({ value: 'あいうえお' })
const orX0157NumericType = ref<OrX0157Type>({ value: '33550335' })
// テキストフィールド
const orX0157TextOneway = ref<OrX0157OnewayType>({
  inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
  showEditBtnFlg: true,
  text: {
    orX0157InputOneway: {
      width: '200px',
      showItemLabel: true,
      itemLabel: 'テキストフィールド',
    },
  },
})

// 半角文字専用
const orX0157HalfWidthOneway = ref<OrX0157OnewayType>({
  inputMode: OrX0157Const.INPUT_MODE.HALF_WIDTH_CHARS_ONLY,
  showEditBtnFlg: true,
  halfWidth: {
    orX0157InputOneway: {
      showItemLabel: true,
      width: '200px',
      itemLabel: 'テキストフィールド半角文字専用',
    },
  },
})
// 数値専用
const orX0157NumericOneway = ref<OrX0157OnewayType>({
  inputMode: OrX0157Const.INPUT_MODE.NUMERIC_ONLY,
  showEditBtnFlg: true,
  numeric: {
    orX0157InputOneway: {
      showItemLabel: true,
      width: '200px',
      itemLabel: 'テキストフィールド数値専用',
    },
  },
})

// テキストフィールド disabled
const orX0157DisabledOneway = ref<OrX0157OnewayType>({
  inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
  showEditBtnFlg: true,
  text: {
    orX0157InputOneway: {
      width: '200px',
      showItemLabel: true,
      itemLabel: 'テキストフィールド',
      disabled: true,
    },
  },
})

// テキストフィールド readonly
const orX0157ReadonlyOneway = ref<OrX0157OnewayType>({
  inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
  showEditBtnFlg: true,
  text: {
    orX0157InputOneway: {
      width: '200px',
      showItemLabel: true,
      itemLabel: 'テキストフィールド',
      readonly: true,
    },
  },
})
// カスタム入力支援ボタンスタイル readonly
const orX0157CustomStyleOneway = ref<OrX0157OnewayType>({
  inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
  showEditBtnFlg: true,
  editBtnClass: 'special-btn',
  text: {
    orX0157InputOneway: {
      width: '200px',
      showItemLabel: true,
      itemLabel: 'テキストフィールド',
      rules: [validation.required],
    },
  },
})
/**
 * 入力補助ボタンクリック時の処理
 */
function onClickEditBtn() {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogText: 'テスト',
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
}
</script>
<template>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/care-manager/mockup-sample/picture-book-list"
        >前のページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- テキストフィールド -->
  <div class="d-flex ma-2">
    <g-custom-or-x-0157
      v-model="orX0157Type"
      :oneway-model-value="orX0157TextOneway"
      @on-click-edit-btn="onClickEditBtn"
    ></g-custom-or-x-0157>
    <div>OrX0157Type値：{{ orX0157Type.value }}</div>
  </div>
  <!-- テキストフィールド半角文字専用 -->
  <div class="d-flex ma-2">
    <g-custom-or-x-0157
      v-model="orX0157HalfWidthType"
      :oneway-model-value="orX0157HalfWidthOneway"
      @on-click-edit-btn="onClickEditBtn"
    ></g-custom-or-x-0157>
    <div>orX0157HalfWidthType値：{{ orX0157HalfWidthType.value }}</div>
  </div>
  <!-- テキストフィールド数値専用 -->
  <div class="d-flex ma-2">
    <g-custom-or-x-0157
      v-model="orX0157NumericType"
      :oneway-model-value="orX0157NumericOneway"
      @on-click-edit-btn="onClickEditBtn"
    ></g-custom-or-x-0157>
    <div>orX0157NumericType値：{{ orX0157NumericType.value }}</div>
  </div>
  <div class="d-flex ma-2">disabled</div>
  <!-- テキストフィールド -->
  <div class="d-flex ma-2">
    <g-custom-or-x-0157
      v-model="orX0157Type"
      :oneway-model-value="orX0157DisabledOneway"
      @on-click-edit-btn="onClickEditBtn"
    ></g-custom-or-x-0157>
  </div>
  <div class="d-flex ma-2">readonly</div>
  <!-- テキストフィールド -->
  <div class="d-flex ma-2">
    <g-custom-or-x-0157
      v-model="orX0157Type"
      :oneway-model-value="orX0157ReadonlyOneway"
      @on-click-edit-btn="onClickEditBtn"
    ></g-custom-or-x-0157>
  </div>
  <div class="d-flex ma-2">カスタム入力支援ボタンスタイル</div>
  <div class="d-flex ma-2">
    <g-custom-or-x-0157
      v-model="orX0157Type"
      :oneway-model-value="orX0157CustomStyleOneway"
      @on-click-edit-btn="onClickEditBtn"
    ></g-custom-or-x-0157>
  </div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>
<style lang="scss" scoped>
:deep(.special-btn) {
  background-color: #ebf2fd !important;
}
</style>
