import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
/**
 * Or27210：有機体：手帳情報選択画面
 * 単方向バインドのデータ構造
 *
 * <AUTHOR>
 */
export interface Or27210OneWayType {
  /**
   * 手帳情報選択
   */
  notebookInfoSelect: notebookInfoSelectType

  /**
   * マスタ他ボタンの表示フラグ
   */
  showMasterBtn?: boolean

  /**
   * マスタ他Icon
   */
  mo00009OnewayMaster?: Mo00009OnewayType

  /**
   * 表表示標識
   */
  tableDisplayFlag: number
}
/**
 * 単方向バインドModelValue
 */
export interface notebookInfoSelectType {
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 交付年月日
   */
  getYmd?: string
  /**
   *手帳区分
   */
  tkbn: string
}
/**
 * 双方向バインドModelValue
 */
export interface Or27210Type {
  /**
   * 手帳情報選択リスト
   */
  notebookInfoData: {
    /**
     * 取得日
     */
    getYmd: string
    /**
     * 等級
     */
    toukyuuKnj: string
    /**
     * メモ
     */
    bikoKnj: string
    /**
     * 程度
     */
    teidoKnj: string
    /**
     * 合併障害
     */
    gappeiShougaiKnj: string
    /**
     * 種類
     */
    tKindKnj: string
    /**
     * 等級コード
     */
    tTokyu: string
    /**
     * 手帳種類
     */
    tKindCd: string
  }
}

/**
 * 手帳情報選択情報ヘッダー
 */
export interface notebookInfoData {
  /**
   * 取得日
   */
  getYmd: string
  /**
   * 等級
   */
  toukyuuKnj: string
  /**
   * メモ
   */
  bikoKnj: string
  /**
   * 程度
   */
  teidoKnj: string
  /**
   * 合併障害
   */
  gappeiShougaiKnj: string
  /**
   * 種類
   */
  tKindKnj: string
  /**
   * 等級コード
   */
  tTokyu: string
  /**
   * 手帳種類
   */
  tKindCd: string
}
