<script setup lang="ts">
/**
 * Or27835:有機体:表示順変更実施計画～③(支援内容)モーダル
 * GUI00967_表示順変更実施計画～③(支援内容)
 *
 * @description
 *［表示順変更:実施計画～③(支援内容)］画面では、呼び出し元の画面の項目の表示順を並べ替えます。［表示順変更］と［表示順移動］で、並べ替える場合の操作性が異なります。
 *［表示順変更:実施計画～③(支援内容)］画面は、［ケアマネ］→［計画書］→［実施計画～③］画面→［表示順］ボタンをクリックすると表示されます。
 * ※アセスメント方式（新型養護老人ホーム)
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27835Const } from './Or27835.constants'
import type { Or27835StateType, TableData } from './Or27835.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01278OnewayType, Mo01278Type } from '~/types/business/components/Mo01278Type'
import type {
  Or27835Type,
  Or27835OnewayType,
  Title,
} from '~/types/cmn/business/components/Or27835Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01336OnewayType } from '~/types/business/components/Mo01336Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { useScreenOneWayBind, useSetupChildProps } from '~/composables/useComponentVue'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or27835Type
  onewayModelValue: Or27835OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const localOneway = reactive({
  Or27835: {
    ...props.onewayModelValue,
  },
  // 情報ダイアログ
  mo00024Oneway: {
    width: '1116px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or27835',
      toolbarTitle: t('label.display-order-modified-plan3-support-content'),
      toolbarName: 'Or27835ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00043OneWay: {
    tabItems: [
      { id: 'change', title: t('label.display-order-modified') },
      { id: 'move', title: t('label.display-order-move') },
    ],
  } as Mo00043OnewayType,
  mo01265OnewayModelValue: {
    btnLabel: t('btn.delete-display-order'),
  } as Mo01265OnewayType,
  // はいボタン
  mo00609Oneway: {
    btnLabel: t('btn.yes'),
  } as Mo00609OnewayType,
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27835StateType>({
  cpId: Or27835Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or27835Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27835Const.DEFAULT.IS_OPEN,
})

const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })

const local = reactive({
  mo00043: { id: '0' } as Mo00043Type,
})
// ポスト最小幅(旧様式)
const columnOldMinWidth = ref<number[]>([213, 317, 200, 250, 120])
// ポスト最小幅(H21/4様式)
const columnMinWidth = ref<number[]>([213, 85, 232, 200, 250, 120])
// 表示順変更テーブル情報
const tableData = ref<TableData[]>([])
const selectedItemIndex = ref<number>(-1)
const tableDataMove = ref<TableData[]>([])
// 表示順変更テーブルヘッダ
const changeTableHeaders = [
  { title: t('label.display-order'), key: 'sort', align: 'start', width: '80px', sortable: false },
  {
    title: t('label.number'),
    key: 'number',
    align: 'start',
    width: '85px',
    sortable: false,
  },
  {
    title: t('label.service-contents'),
    key: 'serviceContents',
    align: 'start',
    sortable: false,
  },
  {
    title: t('label.service-kbn'),
    key: 'serviceType',
    align: 'start',
    width: '200px',
    sortable: false,
  },
  {
    title: t('label.jimusyo-name'),
    key: 'offerOfficeNm',
    align: 'start',
    width: '250px',
    sortable: false,
  },
  {
    title: t('label.frequency-period'),
    key: 'frequencyPeriod',
    align: 'start',
    width: '120px',
    sortable: false,
  },
]
// 表示順変更テーブルヘッダ
const changeTableNoNumberHeaders = [
  { title: t('label.display-order'), key: 'sort', align: 'start', width: '80px', sortable: false },
  {
    title: t('label.service-contents'),
    key: 'serviceContents',
    align: 'start',
    sortable: false,
  },
  {
    title: t('label.service-kbn'),
    key: 'serviceType',
    align: 'start',
    width: '200px',
    sortable: false,
  },
  {
    title: t('label.jimusyo-name'),
    key: 'offerOfficeNm',
    align: 'start',
    width: '250px',
    sortable: false,
  },
  {
    title: t('label.frequency-period'),
    key: 'frequencyPeriod',
    align: 'start',
    width: '120px',
    sortable: false,
  },
]
// 表示順移動テーブルヘッダ
const moveTableHeaders = [
  { title: '', key: 'action', align: 'start', width: '80px', sortable: false },
  { title: t('label.display-order'), key: 'sort', align: 'start', width: '80px', sortable: false },
  {
    title: t('label.number'),
    key: 'number',
    align: 'start',
    width: '85px',
    sortable: false,
  },
  {
    title: t('label.service-contents'),
    key: 'serviceContents',
    align: 'start',
    sortable: false,
  },
  {
    title: t('label.service-kbn'),
    key: 'serviceType',
    align: 'start',
    width: '200px',
    sortable: false,
  },
  {
    title: t('label.jimusyo-name'),
    key: 'offerOfficeNm',
    align: 'start',
    width: '250px',
    sortable: false,
  },
  {
    title: t('label.frequency-period'),
    key: 'frequencyPeriod',
    align: 'start',
    width: '120px',
    sortable: false,
  },
]
// 表示順移動テーブルヘッダ
const moveTableNoNumberHeaders = [
  { title: '', key: 'action', align: 'start', width: '80px', sortable: false },
  { title: t('label.display-order'), key: 'sort', align: 'start', width: '80px', sortable: false },
  {
    title: t('label.service-contents'),
    key: 'serviceContents',
    align: 'start',
    sortable: false,
  },
  {
    title: t('label.service-kbn'),
    key: 'serviceType',
    align: 'start',
    width: '200px',
    sortable: false,
  },
  {
    title: t('label.jimusyo-name'),
    key: 'offerOfficeNm',
    align: 'start',
    width: '250px',
    sortable: false,
  },
  {
    title: t('label.frequency-period'),
    key: 'frequencyPeriod',
    align: 'start',
    width: '120px',
    sortable: false,
  },
]

// ダイアログ表示フラグ
const showDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  init()
})

/**
 * AC001_初期情報取得
 */
function init() {
  // 「表示順変更」タブ初期表示
  // AC001-1取得した一覧リストデータを一覧に設定する。
  if (props.onewayModelValue.indexList && props.onewayModelValue.indexList.length > 0) {
    const tempArray = props.onewayModelValue.indexList
    for (const data of tempArray) {
      const item = {
        action: {
          onewayModelValue: {
            icon: 'dialpad',
            color: 'red',
            minHeight: '31px',
            maxHeight: '31px',
          },
        } as Mo00009OnewayType,
        sort: {
          modelValue: {
            value: data.sort + '',
          } as Mo01278Type,
          onewayModelValue: {
            max: 999,
            min: 1,
          } as Mo01278OnewayType,
        },
        number: {
          onewayModelValue: {
            value: data.number,
            unit: '',
          } as Mo01336OnewayType,
        },
        serviceContents: {
          onewayModelValue: {
            value: data.serviceContents,
            unit: '',
          } as Mo01337OnewayType,
        },
        serviceType: {
          onewayModelValue: {
            value: data.serviceType,
            unit: '',
          } as Mo01337OnewayType,
        },
        offerOfficeNm: {
          onewayModelValue: {
            value: data.offerOfficeNm,
            unit: '',
          } as Mo01337OnewayType,
        },
        frequencyPeriod: {
          onewayModelValue: {
            value: data.frequencyPeriod,
            unit: '',
          } as Mo01337OnewayType,
        },
        sortBackup: data.sortBackup + '',
      } as TableData
      tableData.value.push(item)
    }
  }

  // AC003_「表示順変更」押下、AC005_「表示順移動」押下--ページレイアウトコード構造はこの機能を満たしており、余計なコード作業を必要としません
}

/**
 * ポップアップウィンドウで選択確認ダイアログを表示し
 *
 * @param errormsg - Message
 */
function showOr21814Msg(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.secondBtnClickFlg) {
      // 注意ダイアログの確認ボタン押下時
      await nextTick()
      await confirmOk()
    }
    if (newValue.thirdBtnClickFlg) {
      // 注意ダイアログの閉じるボタン押下時
      confirmCancle()
    }
  }
)

/**
 * AC006_「表示順位削除」押下
 */
function sortDeleteClick() {
  // 表示順リスト表示順リスト内の順序列の内容をクリアする
  // リストのレコードを取得し、ループを使用してレコードの順序に基づいてソートする
  if (tableData.value && tableData.value.length > 0) {
    for (const item of tableData.value) {
      item.sort.modelValue.value = ''
    }
  }
}

const dragState = {
  // 開始索引
  start: -1,
  // 移動時に上書きされるインデックス
  end: -1,
  // 移動中ですか
  dragging: false,
  // 移動方向
  direction: '',
  // 上の浮遊する行
  lastSort: -1,
}

/**
 * AC007_「空白ラベル」ドラッグする_1
 *
 * @param e - $event
 *
 * @param sort - 表示順
 */
function sortBtnMousedown(e: MouseEvent, sort: Mo01278Type) {
  // 選択するとソートが成功します
  // 選択しない場合は記録位置をロールバックします
  dragState.dragging = true
  dragState.start = parseInt(sort.value)

  const htmls = document.getElementsByClassName('suspension' + sort.value)
  if (htmls) {
    for (const item of htmls) {
      if (item) {
        const html = item.parentElement?.parentElement
        if (html) {
          html.style.background = Or27835Const.DEFAULT.COLOR_F2F2F2
        }
      }
    }
  }
}

/**
 * AC007_「空白ラベル」ドラッグする_2
 *
 * @param sort - 表示順
 */
function sortBtnMouseup(sort: Mo01278Type) {
  if (
    dragState.start === dragState.end ||
    dragState.start === parseInt(sort.value) ||
    -1 === parseInt(sort.value)
  ) {
    if (-1 !== parseInt(sort.value)) {
      cssCallBack(dragState.start)
    }
    return
  }
  dragState.end = parseInt(sort.value)

  // ポップアップウィンドウで選択確認ダイアログを表示し
  showOr21814Msg(t('message.i-cmn-10678', [dragState.start, dragState.end]))
}

/**
 * AC007_「空白ラベル」ドラッグする_3
 *
 * @param e - $event
 *
 * @param sort - 表示順
 */
function sortBtnMousemove(e: MouseEvent, sort: Mo01278Type) {
  if (
    dragState.dragging &&
    parseInt(sort.value) !== dragState.lastSort &&
    parseInt(sort.value) !== dragState.start
  ) {
    if (-1 !== dragState.lastSort) {
      const lastHtmls = document.getElementsByClassName('suspension' + dragState.lastSort)
      if (lastHtmls) {
        for (const item of lastHtmls) {
          if (item) {
            const html = item.parentElement?.parentElement
            if (html) {
              html.style.background = ''
            }
          }
        }
      }
    }
    const htmls = document.getElementsByClassName('suspension' + sort.value)
    if (htmls) {
      for (const item of htmls) {
        if (item) {
          const html = item.parentElement?.parentElement
          if (html) {
            html.style.background = '#0760e652'
            dragState.lastSort = parseInt(sort.value)
          }
        }
      }
    }
  }
}

/**
 * AC007_「空白ラベル」ドラッグする_4
 *
 */
function tdMousemove() {
  if (dragState.dragging) {
    if (-1 === dragState.end) {
      const lastSort = dragState.lastSort
      cssCallBack(dragState.start)
      if (-1 !== lastSort) {
        cssCallBack(lastSort)
      }
    }
  }
}

/**
 * AC008_「確定ボタン」押下
 * 「確定」ボタン押下
 */
function confirm() {
  // 表示されているタブ画面の表示順欄の値で課題データ情報の内容をソートする
  // ※表示順欄に値のない行は順次に最後に移動する
  const respData: Or27835Type = {
    sortList: [],
  }

  if (tableData.value && tableData.value.length > 0) {
    const tempList = new Array<Title>()
    for (const item of tableData.value) {
      const data: Title = {
        sort: Number(item.sort.modelValue.value),
        number: item.number.onewayModelValue.value,
        serviceContents: item.serviceContents.onewayModelValue.value,
        serviceType: item.serviceType.onewayModelValue.value,
        offerOfficeNm: item.offerOfficeNm.onewayModelValue.value,
        frequencyPeriod: item.frequencyPeriod.onewayModelValue.value,
        sortBackup: item.sortBackup,
      }
      tempList.push(data)
    }
    tempList.sort((a, b) => a.sort - b.sort)
    respData.sortList = tempList
  }
  // 返却情報.課題データ情報 = ソート後の課題データ情報の内容
  emit('update:modelValue', respData)
  // 本画面を閉じ、親画面に返却する。
  close()
}

/**
 * AC002_「×ボタン」押下
 * AC009_「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * 元素パターン回復
 *
 * @param sort - 表示順
 */
function cssCallBack(sort: number) {
  const element = document.querySelector('.suspension' + sort)
  if (element) {
    const html = element.parentElement?.parentElement
    if (html) {
      html.style.background = 'transparent'
    }
  }

  const lastElement = document.querySelector('.suspension' + dragState.lastSort)
  if (lastElement) {
    const html = lastElement.parentElement?.parentElement
    if (html) {
      html.style.background = 'transparent'
    }
  }

  dragState.start = -1
  dragState.end = -1
  dragState.dragging = false
  dragState.direction = ''
  dragState.lastSort = -1
}

/**
 * 注意ダイアログの確認ボタン押下時
 */
async function confirmOk() {
  for (const item of tableData.value) {
    let isEdit = false
    if (item.sort.modelValue.value === String(dragState.start) && isEdit === false) {
      item.sort.modelValue.value = dragState.end + ''
      isEdit = true
    }
    if (item.sort.modelValue.value === String(dragState.end) && isEdit === false) {
      item.sort.modelValue.value = dragState.start + ''
      isEdit = true
    }
  }

  let testShowData = [...tableData.value]
  testShowData = testShowData
    .slice()
    .sort((a, b) => parseInt(a.sort.modelValue.value) - parseInt(b.sort.modelValue.value))
  await nextTick()
  tableDataMove.value = testShowData

  cssCallBack(dragState.start)
}

/**
 * 注意ダイアログの閉じるボタン押下時
 */
function confirmCancle() {
  cssCallBack(dragState.start)
}

// メニュー切替
watch(
  () => local.mo00043.id,
  async (newValue) => {
    // 変更がない場合、画面データあるかどうかを判定する。
    if (newValue !== t('label.display-order-modified')) {
      let testShowData = [...tableData.value]
      testShowData = testShowData
        .slice()
        .sort((a, b) => parseInt(a.sort.modelValue.value) - parseInt(b.sort.modelValue.value))
      await nextTick()
      tableDataMove.value = testShowData
    }
  }
)

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)
/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
}
/**
 * 「表示順」マウスダウン
 *
 * @param reSetIndex - reSetIndex
 */
function sortReSetProc(reSetIndex: number) {
  const sortVal = {
    maxSort: 0,
  }
  for (const item of tableData.value) {
    if (
      item &&
      item.sort.modelValue.value !== '' &&
      Number(item.sort.modelValue.value) > sortVal.maxSort
    ) {
      sortVal.maxSort = Number(item.sort.modelValue.value)
    }
  }
  if (tableData.value[reSetIndex].sort.modelValue.value === '') {
    tableData.value[reSetIndex].sort.modelValue.value = String(sortVal.maxSort + 1)
  }
}

/**
 * 数値入力イベント
 *
 *@param reSetIndex - reSetIndex
 */
async function onInputNumber(reSetIndex: number) {
  // 数値以外の文字を削除
  const value = tableData.value[reSetIndex].sort.modelValue.value
  tableData.value[reSetIndex].sort.modelValue.value = value.replace(/[^1-9]/g, '')
  await nextTick()
}
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043"
        :oneway-model-value="localOneway.mo00043OneWay"
        style="padding-left: 0px !important"
      >
      </base-mo00043>
      <!-- タブ switch -->
      <c-v-window v-model="local.mo00043.id">
        <c-v-window-item value="change">
          <!-- タブ：表示順変更 -->
          <c-v-card>
            <!-- タブ：表示順変更 -->
            <template #title>
              <base-mo01265
                :oneway-model-value="localOneway.mo01265OnewayModelValue"
                color="#ff0000"
                label-color="#ff0000"
                @click="sortDeleteClick"
              >
              </base-mo01265>
            </template>
            <c-v-card-text>
              <c-v-data-table
                v-resizable-grid="{
                  columnWidths:
                    localOneway.Or27835.processType === 'false'
                      ? columnMinWidth
                      : columnOldMinWidth,
                }"
                :headers="
                  localOneway.Or27835.processType === 'false'
                    ? changeTableHeaders
                    : changeTableNoNumberHeaders
                "
                class="table-wrapper"
                hide-default-footer
                :items="tableData"
                fixed-header
                hover
                :items-per-page="-1"
                style="width: 1100px; height: 395px"
              >
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedItemIndex === index }"
                    @click="selectRow(index)"
                  >
                    <td style="padding: 0 !important">
                      <base-mo01278
                        v-model="tableData[index].sort.modelValue"
                        :oneway-model-value="item.sort.onewayModelValue"
                        @click.stop="sortReSetProc(index)"
                        @input="onInputNumber(index)"
                      >
                      </base-mo01278>
                    </td>
                    <td v-if="localOneway.Or27835.processType === 'false'">
                      <base-mo01336 :oneway-model-value="item.number.onewayModelValue">
                      </base-mo01336>
                    </td>
                    <td>
                      <base-mo01337 :oneway-model-value="item.serviceContents.onewayModelValue">
                      </base-mo01337>
                    </td>
                    <td>
                      <base-mo01337 :oneway-model-value="item.serviceType.onewayModelValue">
                      </base-mo01337>
                    </td>
                    <td>
                      <base-mo01337 :oneway-model-value="item.offerOfficeNm.onewayModelValue">
                      </base-mo01337>
                    </td>
                    <td>
                      <span class="overflowText">
                        {{ item.frequencyPeriod.onewayModelValue.value }}</span
                      >
                      <c-v-tooltip
                        activator="parent"
                        location="bottom"
                        :max-width="350"
                        :text="item.frequencyPeriod.onewayModelValue.value"
                        open-delay="200"
                      />
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-card-text>
          </c-v-card>
        </c-v-window-item>
        <c-v-window-item value="move">
          <!-- タブ：表示順移動 -->
          <c-v-card @mouseup="sortBtnMouseup({ value: '-1' } as Mo01278Type)">
            <c-v-card-text id="customCard">
              <c-v-data-table
                :headers="
                  localOneway.Or27835.processType === 'false'
                    ? moveTableHeaders
                    : moveTableNoNumberHeaders
                "
                class="table-wrapper"
                hide-default-footer
                :items="tableDataMove"
                fixed-header
                hover
                :items-per-page="-1"
                style="width: 1100px; height: 395px"
              >
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedItemIndex === index }"
                    @click="selectRow(index)"
                  >
                    <td
                      @mousedown="sortBtnMousedown($event, item.sort.modelValue)"
                      @mouseup="sortBtnMouseup(item.sort.modelValue)"
                      @mousemove="sortBtnMousemove($event, item.sort.modelValue)"
                    >
                      <base-mo00009
                        icon="mdi-plus"
                        :oneway-model-value="item.action.onewayModelValue"
                        :class="'suspension' + item.sort.modelValue.value"
                      >
                        <v-icon icon="dialpad"></v-icon>
                      </base-mo00009>
                    </td>
                    <td @mousemove="tdMousemove">
                      <base-mo01278
                        :model-value="item.sort.modelValue"
                        :oneway-model-value="item.sort.onewayModelValue"
                        :disabled="true"
                      >
                      </base-mo01278>
                    </td>
                    <td
                      v-if="localOneway.Or27835.processType === 'false'"
                      @mousemove="tdMousemove"
                    >
                      <base-mo01336 :oneway-model-value="item.number.onewayModelValue">
                      </base-mo01336>
                    </td>
                    <td @mousemove="tdMousemove">
                      <base-mo01337 :oneway-model-value="item.serviceContents.onewayModelValue">
                      </base-mo01337>
                    </td>
                    <td @mousemove="tdMousemove">
                      <base-mo01337 :oneway-model-value="item.serviceType.onewayModelValue">
                      </base-mo01337>
                    </td>
                    <td @mousemove="tdMousemove">
                      <base-mo01337 :oneway-model-value="item.offerOfficeNm.onewayModelValue">
                      </base-mo01337>
                    </td>
                    <td @mousemove="tdMousemove">
                      <span class="overflowText">
                        {{ item.frequencyPeriod.onewayModelValue.value }}</span
                      >
                      <c-v-tooltip
                        activator="parent"
                        location="bottom"
                        :max-width="350"
                        :text="item.frequencyPeriod.onewayModelValue.value"
                        open-delay="200"
                      />
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-card-text>
          </c-v-card>
        </c-v-window-item>
      </c-v-window>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
        </base-mo00611>
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mx-2"
          @click="confirm"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialog"
    v-bind="or21814"
  />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';

:deep(.txt:disabled) {
  background: inherit;
}
.overflowText {
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}
:deep(.v-card-text) {
  padding-left: 0 !important;
  padding-right: 0 !important;
  padding-bottom: 0 !important;
  padding-top: 8px !important;
}

:deep(.v-card-item) {
  padding-left: 0 !important;
  padding-top: 8px !important;
  padding-bottom: 0 !important;
}
</style>
