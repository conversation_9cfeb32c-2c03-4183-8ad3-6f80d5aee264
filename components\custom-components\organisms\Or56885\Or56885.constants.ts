import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or56885:有機体:印刷設定モーダル（画面/特殊コンポーネント）
 * GUI01310_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
export namespace Or56885Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or56885', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * 帳票番号
     */
    export const PRT_NO = '1'
    /**
     * 個人情報表示フラグ
     */
    export const KOJINHOGO_USED_FLG = '0'
    /**
     * 個人情報表示値
     */
    export const SECTION_ADD_NO = '0'
    /**
     * インデックス
     */
    export const INDEX = '0'
    /**
     * 単一
     */
    export const TANI = '0'
    /**
     * 複数
     */
    export const HUKUSUU = '1'
    /**
     * 期間管理する
     */
    export const KIKAN_FLG_1 = '1'
    /**
     * 期間管理しない
     */
    export const KIKAN_FLG_0 = '0'
    /**
     * 帳票ID: PDFダウンロード
     */
    export const PDF_DOWNLOAD_REPORT_ID = 'LeavingInfoRecordReport'
    /**
     * デフォルトパース
     */
    export const PATH_GUI01310 = '/care-manager/GUI01310'
    /**
     * 固定文字セット
     */
    export namespace STR {
      /**
       * 空文字
       */
      export const EMPTY  = ''
      /**
       * ZERO
       */
      export const ZERO = '0'
      /**
       * ONE
       */
      export const ONE = '1'
      /**
       * TWO
       */
      export const TWO = '2'
      /**
       * YES
       */
      export const YES = 'yes'
      /**
       * NO
       */
      export const NO = 'no'
      /**
       * true
       */
      export const TRUE = 'true'
    }
    /**
     * 数値定数
     */
    export namespace NUMBER  {
      /**
       * ZERO
       */
      export const ZERO = 0
      /**
       * 一
       */
      export const ONE = 1
    }
  }
}
