<script setup lang="ts">
/**
 * Or26827:有機体:［履歴選択］画面 予防評価表
 * GUI01247_［履歴選択］画面 予防評価表
 *
 * @description
 * ［履歴選択］画面 予防評価表
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch } from 'vue'
import type { Or26827StateType } from './Or26827.type'
import { Or26827Const } from './Or26827.constants'
import { useScreenOneWayBind } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Or26827OnewayType, Or26827Type } from '~/types/cmn/business/components/Or26827Type'
import type {
  PreventionEvaluationTableHistInfoInEntity,
  PreventionEvaluationTableHistInfoOutEntity,
} from '~/repositories/cmn/entities/PreventionEvaluationTableHistInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  Mo01334Headers,
  Mo01334Items,
  Mo01334OnewayType,
} from '~/types/business/components/Mo01334Type'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or26827Type
  onewayModelValue: Or26827OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const defaultOnewayModelValue: Or26827OnewayType = {
  // 事業者ID
  svJigyoId: '0',
  // 利用者ID
  userId: '0',
  // 計画期間ID
  sc1Id: '1',

}

const localOneway = reactive({
  or26827Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 「履歴選択」ダイアログ
  mo00024Oneway: {
    width: '471px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.history-select'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  // 履歴選択情報データテーブルのヘッダー
  mo01334Oneway: {
    height: '294px',
    headers: [
      // 評価日
      {
        title: t('label.evaluation-day'), // ヘッダーに表示される名称
        key: 'hyoukaYmd',
        width: '110px',
        sortable: false,
      },
      // 作成者
      {
        title: t('label.author'), // ヘッダーに表示される名称
        key: 'author',
        width: '180px',
        sortable: false,
      },
      // 様式
      {
        title: t('label.style'), // ヘッダーに表示される名称
        key: 'styleName',
        width: '150px',
        sortable: false,
      },
    ] as unknown as Mo01334Headers[],
    showPaginationBottomFlg: false,
    showPaginationTopFlg: false,
    density: 'default',
    items: [
      {
        id: props.modelValue.hyouka1Id,
        hyouka1Id: props.modelValue.hyouka1Id,
        hyoukaYmd: '',
        author: '',
        styleName: '',
      },
    ],
  } as Mo01334OnewayType,
})
const local = reactive({
  or26827:{
  // 履歴ID
  hyouka1Id: props.modelValue.hyouka1Id,
  },
  // 履歴選択情報
  mo01334: {
    value: props.modelValue.hyouka1Id,
    values: [],
  },
})

// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
}

// 確定ボタン設置
const mo00609Oneway: Mo00609OnewayType = {
  btnLabel: t('btn.confirm'),
  width: '90px',
  tooltipText: t('tooltip.confirm-btn'),
}

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or26827Const.DEFAULT.IS_OPEN,
})

// 履歴選択情報選択行データ設定
const historySelectedItem = ref<Mo01334Items | undefined>(undefined)

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or26827StateType>({
  cpId: Or26827Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or26827Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: PreventionEvaluationTableHistInfoInEntity = {
    ...localOneway.or26827Oneway,
  }
  const resData: PreventionEvaluationTableHistInfoOutEntity = await ScreenRepository.select(
    'preventionEvaluationTableHistInfoSelect',
    inputData
  )
  // データ情報設定
  localOneway.mo01334Oneway.items = resData.data.preventionEvaluationTableHistList
    .map((item) => {
      return {
        id: item.hyouka1Id,
        ...item,
      }
    })
    .sort((a, b) => {
      const dateA = parseDate(a.hyoukaYmd)
      const dateB = parseDate(b.hyoukaYmd)
      if (dateA > dateB) return -1
      if (dateA < dateB) return 1
      return parseInt(b.hyouka1Id, 10) - parseInt(a.hyouka1Id, 10)
    })

  // 親画面.履歴IDが存在する場合
  const item = localOneway.mo01334Oneway.items.find((item) => item.id === local.mo01334.value)
  if (localOneway.mo01334Oneway.items.length && !item) {
    local.mo01334.value = localOneway.mo01334Oneway.items[0].id
  }
  historySelectedItem.value = localOneway.mo01334Oneway.items.find(
    (item) => item.id === local.mo01334.value
  )
}

// 日付文字列を Date オブジェクトに変換する関数を定義
function parseDate(dateStr: string) {
  return new Date(dateStr.replace(/\//g, '-'))
}

/**
 * 履歴情報選択行
 *
 * @param item - 履歴情報選択行
 */
const clickHistorySelectRow = (item: Mo01334Items) => {
  historySelectedItem.value = item
}

/**
 * 履歴情報の行をダブルクリックで選択
 *
 * @param item - 履歴情報選択行
 */
const doubleClickHistorySelectRow = (item: Mo01334Items) => {
  historySelectedItem.value = item
  onConfirmBtn()
}

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = () => {
  // 履歴データがない場合、操作なし
  if (!historySelectedItem.value) return
  // 選択情報値戻り
  const resData={hyouka1Id:historySelectedItem.value.id}as Or26827Type
  emit('update:modelValue', resData)
  onClickCloseBtn()
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            class="table-header"
          >
            <!-- 履歴選択一覧 -->
            <base-mo01334
              v-model="local.mo01334"
              :oneway-model-value="localOneway.mo01334Oneway"
              hide-default-footer
              class="list-wrapper"
            >
              <!-- 項目1 -->
              <template #[`item.hyoukaYmd`]="{ item }">
                <p
                  @click="clickHistorySelectRow(item)"
                  @dblclick="doubleClickHistorySelectRow(item)"
                >
                  {{ item.hyoukaYmd }}
                </p>
              </template>
              <!-- 項目2 -->
              <template #[`item.author`]="{ item }">
                <p
                  @click="clickHistorySelectRow(item)"
                  @dblclick="doubleClickHistorySelectRow(item)"
                >
                  {{ item.author }}
                </p>
              </template>
              <!-- 項目3 -->
              <template #[`item.styleName`]="{ item }">
                <p
                  @click="clickHistorySelectRow(item)"
                  @dblclick="doubleClickHistorySelectRow(item)"
                >
                  {{ item.styleName }}
                </p>
              </template>
            </base-mo01334>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="mo00611Oneway"
          class="mr-2"
          @click="onClickCloseBtn"
        >
        </base-mo00611>
        <!-- 確定ボタン-->
        <base-mo00609
          class="mr-2"
          :oneway-model-value="mo00609Oneway"
          @click="onConfirmBtn"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
:deep(td){
  height: 32px !important;
}
</style>
