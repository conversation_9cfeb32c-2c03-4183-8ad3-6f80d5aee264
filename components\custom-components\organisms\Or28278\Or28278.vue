<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28278Const } from './Or28278.constants'
import type { DataInfoType, Or28278StateType } from './Or28278.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or28278OnewayType, Or28278Type } from '~/types/cmn/business/components/Or28278Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { useScreenOneWayBind } from '#imports'
import type {
  MainInsuranceInfoSelectInEntity,
  MainInsuranceInfoSelectOutEntity,
} from '~/repositories/cmn/entities/MainInsuranceInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'

/**
 * Or28278:有機体:介護保険 認定情報選択モーダル
 * 画面設計書_GUI00646_［主保険選択］画面
 *
 * @description
 *［主保険選択］画面 認定情報選択画面を表示します。
 *
 * <AUTHOR> 魯帥傑
 */

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or28278Type
  onewayModelValue: Or28278OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or28278OnewayType = {
  // 利用者ID
  userId: '',
}

const localOneway = reactive({
  Or28278: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定ボタン
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  //［認定情報選択］画面
  mo00024Oneway: {
    width: '800px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or28278',
      toolbarTitle: t('label.main-insurance-select'),
      toolbarName: 'Or28278ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
    },
  } as Mo00024OnewayType,
  mo00009Oneway: {
    btnIcon: 'database',
  } as Mo00009OnewayType,
})

// ポスト最小幅
// const columnMinWidth = ref<number[]>([170, 150, 224, 240])

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28278Const.DEFAULT.IS_OPEN,
})

// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

const or21815 = ref({ uniqueCpId: '' })

// 認定情報一覧初期情報
const tableData = ref<DataInfoType>({
  hokenList: [],
})

const tableDataFilter = computed(() => {
  const hokenList = tableData.value.hokenList
  return { hokenList: hokenList }
})

const originData = {
  hokenListType: [] as Or28278Type[],
}

// テーブルヘッダ
const headers = [
  // 法制コード
  {
    title: t('label.legal-code'),
    width: '170px',
    align: 'left',
    sortable: false,
    key: 'hokensya',
  },
  // 保険者番号
  {
    title: t('label.insured-person-number'),
    width: '150px',
    align: 'left',
    sortable: false,
    key: 'hHokenNo',
  },
  // 記号.番号
  {
    title: t('label.mark-number'),
    width: '224px',
    align: 'left',
    sortable: false,
    key: 'certificationEriod',
  },
  // 期間
  {
    title: t('label.kikanKnj'),
    width: '240px',
    align: 'left',
    sortable: false,
    key: 'yokaiKnj',
  },
]

// マスタアイコンボタン
// const masterBtnMo00009 = ref<Mo00009OnewayType>({
//   btnIcon: 'database',
//   icon: true,
//   width: '32px',
//   disabled: false,
// })

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or28278StateType>({
  cpId: Or28278Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or28278Const.DEFAULT.IS_OPEN
    },
  },
})

const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await init()
})

/**
 * 認定情報一覧初期情報取得
 */
async function init() {
  const inputData: MainInsuranceInfoSelectInEntity = {
    /** ユーザID */
    userId: props.onewayModelValue.userId,
  }
  // 認定情報一覧初期情報取得
  const ret: MainInsuranceInfoSelectOutEntity = await ScreenRepository.select(
    'mainInsuranceSelect',
    inputData
  )

  for (const acquInfo of ret.data.hokenList) {
    tableData.value.hokenList.push({
      // 保険区分コード
      nameKnj: acquInfo.nameKnj,
      // 被保険者番号
      publicno: acquInfo.publicno,
      // 認定期間"認定情報リスト.認定有効開始日（表示用）+'~'+認定情報リスト.認定有効終了日（表示用）"
      certificationEriod: acquInfo.startdateYmd + t('label.wavy') + acquInfo.enddateYmd,
      // 記号番号
      kigonoKnj: acquInfo.kigonoKnj,
      type: '',
      startdateYmd: '',
      enddateYmd: '',
    })

    originData.hokenListType.push({
      // 保険区分コード
      nameKnj: acquInfo.nameKnj,
      // 被保険者番号
      publicno: acquInfo.publicno,
      // 認定有効開始日（表示用）
      startdateYmd: acquInfo.startdateYmd,
      // 認定有効終了日（表示用）
      enddateYmd: acquInfo.enddateYmd,
      // 記号番号
      kigonoKnj: acquInfo.kigonoKnj,
      type: '',
    })
  }

  if (tableData.value.hokenList.length > 0) {
    onSelectRow(0)
  }
  // if (tableData.value.hokenList.length > 7) {
  //   columnMinWidth.value = [170, 150, 208, 240]
  // }
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function onSelectRow(index: number) {
  selectedItemIndex.value = index
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 *  認定情報一覧に行が選択されない場合
 */
function onClick_Confirm() {
  if (selectedItemIndex.value === -1) {
    showOr21815Msg(t('message.w-cmn-20742'))
  } else if (selectedItemIndex.value !== -1) {
    rtnDataSet()
  }
}

/**
 * 明細行ダブルクリック
 */
function doubleClickSelectRow() {
  rtnDataSet()
}

/**
 * 入力エラーを表示する
 *
 * @param errorMsg - メッセージ内容
 */
function showOr21815Msg(errorMsg: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      dialogTitle: t('label.warning'),
      dialogText: errorMsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

//認定情報一覧に行が選択されない場合の選択結果を聞きます
watch(
  () => Or21815Logic.event.get(or21815.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.thirdBtnClickFlg) {
      return
    }
  }
)

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

// 戻り値設定
function rtnDataSet() {
  // 戻り値設定
  const rtnData: Or28278Type = {
    // 保険区分コード
    nameKnj: originData.hokenListType[selectedItemIndex.value].nameKnj,
    // 被保険者番号
    publicno: originData.hokenListType[selectedItemIndex.value].publicno,
    // 記号番号
    kigonoKnj: originData.hokenListType[selectedItemIndex.value].kigonoKnj,
    // 認定有効開始日（表示用）
    startdateYmd: originData.hokenListType[selectedItemIndex.value].startdateYmd,
    // 認定有効終了日（表示用）
    enddateYmd: originData.hokenListType[selectedItemIndex.value].enddateYmd,
    type: originData.hokenListType[selectedItemIndex.value].type,
  }
  // 選択情報値戻り
  emit('update:modelValue', rtnData)
  // 画面閉じる
  close()
}

/**
 * マスタ他ボタンクリック
 *
 * @description
 * 自身のState領域のフラグを更新する。
 */
// TODO 遷移先未実装
// function masterBtnClick() {}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <!--
      <div class="mo-1">
        <c-v-row no-gutters>
          <c-v-col>
            <div class="flex-end">
              <base-mo00009
                :oneway-model-value="masterBtnMo00009"
                @click="masterBtnClick"
              >
                <c-v-tooltip
                  :text="t('tooltip.others-function')"
                  activator="parent"
                  location="bottom"
                >
                </c-v-tooltip>
              </base-mo00009>
            </div>
          </c-v-col>
        </c-v-row>
      </div>
      -->
      <c-v-row>
        <!-- マスタアイコンボタン -->
        <c-v-col>
          <c-v-data-table
            :headers="headers"
            class="table-wrapper"
            hide-default-footer
            :items="tableDataFilter.hokenList"
            fixed-header
            hover
            height="262px"
            :items-per-page="-1"
          >
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="onSelectRow(index)"
                @dblclick="doubleClickSelectRow()"
              >
                <!-- 保険者 -->
                <td>
                  <span class="overflowText">{{ item.nameKnj }}</span>
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :max-width="350"
                    :text="item.nameKnj"
                    open-delay="200"
                  />
                </td>
                <!-- 被保険者番号 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{ value: item.publicno }"
                    style="width: 100%"
                  />
                </td>

                <!-- 记号番号 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{ value: item.kigonoKnj }"
                    style="width: 100%"
                  />
                </td>
                <!-- 認定期間 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{ value: item.certificationEriod }"
                    style="width: 100%"
                  />
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-spacer />
      <!-- 閉じるボタン Mo00611 -->
      <base-mo00611
        v-bind="localOneway.mo00611CloseBtnOneWay"
        class="mx-2"
        @click="close"
      >
        <!--ツールチップ表示："画面を閉じます"-->
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :max-width="600"
          :text="t('tooltip.screen-close')"
          open-delay="200"
        />
      </base-mo00611>
      <!-- 確定ボタン Mo00609 -->
      <base-mo00609
        v-bind="localOneway.mo00609OneWay"
        @click="onClick_Confirm"
      >
        <!--ツールチップ表示："設定を確定します。"-->
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :max-width="600"
          :text="t('tooltip.confirm-btn')"
          open-delay="200"
        />
      </base-mo00609>
    </template>
    <!-- ワーニングダイアログを表示する。 -->
    <g-base-or21815
      v-if="showDialogOr21815"
      v-bind="or21815"
    ></g-base-or21815>
  </base-mo00024>
</template>
<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
.number-right {
  text-align: right;
}
.overflowText {
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}
</style>
