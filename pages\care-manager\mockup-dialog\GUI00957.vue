<script setup lang="ts">
/**
 * GUI00957_サービス種別入力支援
 *
 * @description
 * サービス種別入力支援
 *
 * <AUTHOR> DO AI QUOC
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or50429Logic } from '~/components/custom-components/organisms/Or50429/Or50429.logic'
import { Or50429Const } from '~/components/custom-components/organisms/Or50429/Or50429.constants'
import type { Or50429OneWayType, Or50429Type } from '~/types/cmn/business/components/Or50429Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
/**
 * 画面ID
 */
const screenId = 'GUI00957'
/**
 * ルーティング
 */
const routing = 'GUI00957/pinia'
/**
 * 画面物理名
 */
const screenName = 'GUI00957'
/**
 * 画面状態管理用操作変数
 */
const screenStore = useScreenStore()

/**
 * or50429子コンポーネント用変数
 */
const or50429 = ref({ uniqueCpId: Or50429Const.CP_ID(1) })

/**************************************************
 * 画面固有処理
 **************************************************/
/**
 * piniaの画面領域を初期化する
 */
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00957' },
})

/**************************************************
 * Props
 **************************************************/
/**
 * piniaから最上位の画面コンポーネント情報を取得する
 * これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
 */
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or50429Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or50429.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00957',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or50429Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or50429Const.CP_ID(1)]: or50429.value,
})

/**
 * OneWayバインド用のローカルデータ
 */
const or50429Oneway: Or50429OneWayType = {
  screenDisplayMode: '',
  officeName: '',
  serviceContent: '',
  serviceKind: 'サービス種別',
  createDate: '2025/01',
  insuranceServiceImport: '0',
  processName: '',
  revisionFlg: ''
}
/**
 * or50429データ
 */
const or50429Type = ref<Or50429Type>({
  serviceType: {
    value: 'テスト',
  },
  offerOffice: {
    value: 'テスト',
  },
})

/**
 *  ボタン押下時の処理(Or50429)
 *
 * @param processName - 処理名
 *
 * @param mode - 画面表示モード
 *
 * @param revisionFlg - 改訂フラグ
 */
function onClickOr50927(processName: string, mode: string, revisionFlg: string) {
  or50429Oneway.screenDisplayMode = mode
  or50429Oneway.processName = processName
  or50429Oneway.revisionFlg = revisionFlg

  // or50429のダイアログ開閉状態を更新する
  Or50429Logic.state.set({
    uniqueCpId: or50429.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * ダイアログ表示フラグ
 */
const showDialogOr50429 = computed(() => {
  // Or50429のダイアログ開閉状態
  return Or50429Logic.state.get(or50429.value.uniqueCpId)?.isOpen ?? false
})
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr50927(Or50429Const.PROCESS_NAME.PROCESS_1, Or50429Const.SCREEN_DISPLAY_MODE.MODE_1, '')"
        >GUI00957_サービス種別入力支援（実施計画～②画面.サービス種別ボタン押下）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr50927(Or50429Const.PROCESS_NAME.PROCESS_1, Or50429Const.SCREEN_DISPLAY_MODE.MODE_2, '')"
        >GUI00957_サービス種別入力支援（実施計画～②画面.提供事業所名ボタン押下）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr50927(Or50429Const.PROCESS_NAME.PROCESS_2, Or50429Const.SCREEN_DISPLAY_MODE.MODE_1, Or50429Const.REVISION_FLG.FLG_1)"
        >GUI00957_サービス種別入力支援（計画書書式(パッケージ)：H21改訂版 ／ 実施計画～③画面.サービス種別ボタン押下）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr50927(Or50429Const.PROCESS_NAME.PROCESS_2, Or50429Const.SCREEN_DISPLAY_MODE.MODE_2, Or50429Const.REVISION_FLG.FLG_1)"
        >GUI00957_サービス種別入力支援（計画書書式(パッケージ)：H21改訂版 ／ 実施計画～③画面.提供事業所名ボタン押下）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr50927(Or50429Const.PROCESS_NAME.PROCESS_3, Or50429Const.SCREEN_DISPLAY_MODE.MODE_1, '')"
        >GUI00957_サービス種別入力支援（事業所：外部委託事業所含まない ／ 計画書(2)画面.サービス種別ボタン押下）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr50927(Or50429Const.PROCESS_NAME.PROCESS_3, Or50429Const.SCREEN_DISPLAY_MODE.MODE_2, '')"
        >GUI00957_サービス種別入力支援（事業所：外部委託事業所含まない ／ 計画書(2)画面.※２ボタン押下）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr50927(Or50429Const.PROCESS_NAME.PROCESS_3, Or50429Const.SCREEN_DISPLAY_MODE.MODE_3, '')"
        >GUI00957_サービス種別入力支援（事業所：外部委託事業所含まない ／ 計画書(2)画面.利用票取込ボタン押下）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-50429
    v-if="showDialogOr50429"
    v-bind="or50429"
    v-model="or50429Type"
    :oneway-model-value="or50429Oneway"
  />
</template>
