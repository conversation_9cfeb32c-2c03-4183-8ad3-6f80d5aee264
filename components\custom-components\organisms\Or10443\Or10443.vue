<script setup lang="ts">
/**
 * Or10443:処理ロジック
 * GUI01079_印刷設定
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> DAO VAN DUONG
 */

import { computed, onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10443Const } from '~/components/custom-components/organisms/Or10443/Or10443.constants'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CustomClass } from '@/types/CustomClassType'
import { Or10148Const } from '~/components/custom-components/organisms/Or10148/Or10148.constants'
import { Or10148Logic } from '~/components/custom-components/organisms/Or10148/Or10148.logic'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import { reportOutputType } from '~/utils/useReportUtils'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import {
  useReportUtils,
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
} from '#imports'

import type {
  PreventionPlanPrintUserSettingsInitSelectInEntity,
  PreventionPlanPrintUserSettingsInitSelectOutEntity,
  PreventionPlanPrintUserSettingsUserChangeSelectInEntity,
  PreventionPlanPrintUserSettingsUserChangeSelectOutEntity,
  PreventionPlanPrintUserSettingsSubjectSelectInEntity,
  PreventionPlanPrintUserSettingsSubjectSelectMapInEntity
} from '~/repositories/cmn/entities/PreventionPlanPrintUserSettings'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or10443OnewayType } from '~/types/cmn/business/components/Or10443Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Or10148Type } from '~/types/cmn/business/components/Or10148Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type {
  UserEntity
} from '~/repositories/cmn/entities/LeavingInfoRecordPrintSettingsEntity'
import type {
  OrX0128OnewayType,
  OrX0128Items,
  OrX0128Headers,
} from '~/types/cmn/business/components/OrX0128Type'
import type {
  Or10443TwoWayData,
  TyppeCustomClass,
  ChoPrtList,
  IniDataObject,
  RirekiList,
  ChoPrtListMaping
} from '~/components/custom-components/organisms/Or10443/Or10443.type'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
// import type {
//   IncentivesItemSelectInEntity,
//   OutputLedgerPrintEntity,
// } from '~/repositories/cmn/entities/IncentivesItemSelectEntity'


const { t } = useI18n()
const { reportOutput } = useReportUtils()

const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or10443OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()
const or21813 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })
const or10148 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const orX0117 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })
const loading = ref(true)
const userCols = ref(6)
const mo00610Oneway = ref<Mo00610OnewayType>({
  btnLabel: t('label.consent-column-registration'),
  width: '100px',
  disabled: false,
  prependIcon: '',
  appendIcon: '',
  class: 'btn-mo00610',
})
const orX0117Oneway: OrX0117OnewayType = {
  type: Or10443Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10443Const.DEFAULT.IS_OPEN,
})
const mo01334OnewayReport = ref({
  headers: [
    {
      title: t('label.report'),
      key: 'prtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})
const mo01334TypeReport = ref({
  value: '',
  values: [] as IniDataObject[],
})
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)
const local = reactive({
  mo00039TypeUserSelectType: '',
  mo00039TypeHistorySelectType: '',
  mo00020TypeKijunbi: {
    value: '2024/11/11',
  } as Mo00020Type,
  or10148: {
    ledgerINI: {
      parameter05: '',
      parameter06: '',
      parameter07: '',
      parameter08: '',
      parameter09: '上記計画について、 同意いたします。',
      parameter10: '',
    },
  } as Or10148Type,
  orX0128DetList: [] as OrX0128Items[],
  userList: [] as UserEntity[],
})
const localOneway = reactive({
  Or10443: {
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '1300px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10443',
      toolbarTitle: t('label.print-set'),
      toolbarName: 'Or10443ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  mo01338OneWayTitle: {
    value: t('label.title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  prndateOneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  param03OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00045OnewayTitleInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
  } as Mo00045OnewayType,
  param04Oneway: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '50',
    maxLength: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1' }),
  } as Mo00045OnewayType,
  param050OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param049OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-the-users-name-in-the-margin'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param048OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-consent-information'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param047OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.change-the-consent-text'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param046OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.do-not-print-blank-lines-if-medical-history-is-less-than-four-lines'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param045OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-h27-style'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param044OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.do-not-print-the-stamp-area'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      labelStyle: 'display: none',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      labelStyle: 'display: none',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayBaseDate: {
    value: t('label.base-date'),
    customClass: {
      labelStyle: 'display: none',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo01338OneWayCareManagerInCharge: {
    value: t('label.care-manager-in-charge'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayCareManagerInChargeLabel: {
    value: 'ほのぼの 三郎',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: false,
    multiple: false,
    selectedUserCounter: '1',
  } as OrX0145OnewayType,
})
const orX0128OnewayModel = reactive<OrX0128OnewayType>({
  kikanFlg: '1',
  singleFlg: OrX0128Const.DEFAULT.TANI,
  tableStyle: 'width:370px; height: 510px;',
  headers: [
    { title: t('label.consultation-date'), key: 'createYmd', minWidth: '180px', sortable: false, align: 'center' },
    { title: t('label.shokuName'), key: 'shokuName', minWidth: '180px', sortable: false, align: 'center' },
  ] as OrX0128Headers[],
  items: [],
  initSelectId: Or10443Const.DEFAULT.EMPTY,
})
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width: 330px',
  userId: Or10443Const.DEFAULT.EMPTY,
  showKanaSelectionCount: true,
})

/**************************************************
 * Pinia
 **************************************************/
/**
 * Piniaストアの設定
 * - ダイアログの開閉状態を管理
 * - uniqueCpIdを使用して一意の状態を識別
 */
const { setState } = useScreenOneWayBind({
  cpId: Or10443Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or10443Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/* 子コンポーネントのユニークIDを設定する */
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [Or10148Const.CP_ID(0)]: or10148.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

const { refValue } = useScreenTwoWayBind<Or10443TwoWayData>({
  cpId: Or10443Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

const showDialogOr10148 = computed(() => {
  return Or10148Logic.state.get(or10148.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOrX0117 = computed(() => {
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)
/* AC003_帳票選択切替 */
watch(
  () => mo01334TypeReport.value.value,
  () => {
    localOneway.orX0145Oneway.selectedUserCounter = refValue.value.choPrtList[mo01334TypeReport.value.value]?.param06 ?? ''
    checkDisabledCheckbox()
  },
  { immediate: true }
)
watch(
  () => local.mo00039TypeUserSelectType,
  (newValue) => {
    orX0130Oneway.selectMode = newValue
    local.userList = []
    const userList = OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList || []
    if (userList.length) {
      for (const item of userList) {
        local.userList.push({
          userId: item.userId,
          userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
        } as UserEntity)
      }
    }
    if (newValue === Or10443Const.DEFAULT.TANI) {
      userCols.value = 6
      orX0117Oneway.type = Or10443Const.DEFAULT.TWO
      orX0130Oneway.tableStyle = 'width: 320px'
    } else {
      userCols.value = 12
      orX0117Oneway.type = Or10443Const.DEFAULT.ONE
      orX0130Oneway.tableStyle = 'width: 430px'
    }
    orX0130Oneway.userId = localOneway.Or10443.userId || Or10443Const.DEFAULT.EMPTY
    checkDisabledOaram050OneWay()
  }
)
watch(
  () => local.mo00039TypeHistorySelectType,
  (newValue) => {
    if (newValue === Or10443Const.DEFAULT.TANI) {
      orX0117Oneway.type = Or10443Const.DEFAULT.TWO
    } else {
      orX0117Oneway.type = Or10443Const.DEFAULT.ZERO
    }
    orX0128OnewayModel.singleFlg = newValue
    checkDisabledOaram050OneWay()
  }
)
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    local.userList = newValue.userList ?? []
    if (loading.value) return
    await preventionPlanPrintUserSettingsUserChangeSelect()
  }
)
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    if (!newValue.historyDetClickFlg) return
    local.orX0128DetList = newValue.orX0128DetList
  }
)

onMounted(async () => {
  loading.value = true
  await preventionPlanPrintUserSettingsInitSelect()
  await initCodes()
  orX0130Oneway.userId = localOneway.Or10443.userId || Or10443Const.DEFAULT.EMPTY
  loading.value = false
})

async function initCodes() {
  const selectCodeKbnList = [
    /* ・コード区分:482 */
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    /* ・コード区分:487 */
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
  ]

  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  localOneway.prndateOneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  localOneway.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  local.mo00039TypeUserSelectType = '0'
  local.mo00039TypeHistorySelectType = '0'
}

async function showOr21813MsgOneBtn() {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

async function showOr21815MsgOneBtn() {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      dialogTitle: t('label.caution'),
      iconName: 'warning',
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
async function checkTitleInput() {
  if (refValue.value?.choPrtList[mo01334TypeReport.value.value]?.prtTitle.value) return
  const dialogResult = await showOr21815MsgOneBtn()
  if (dialogResult !== 'yes') return
  const item: ChoPrtList = mo01334OnewayReport.value.items.find(e => e.id === mo01334TypeReport.value.value)
  if (!item || !refValue.value.choPrtList[mo01334TypeReport.value.value]) return
  refValue.value.choPrtList[mo01334TypeReport.value.value].prtTitle.value = item.prtTitle
}
async function close() {
  await checkTitleInput()
  setState({ isOpen: false })
}
async function handlePrint() {
  await checkTitleInput()
  if (!refValue.value?.choPrtList[mo01334TypeReport.value.value]?.choPro)
    await showOr21813MsgOneBtn()
  if (!local.userList.length) return
  await preventionPlanPrintUserSettingsSubjectSelect()
  /* 利用者選択 */
  if (local.mo00039TypeUserSelectType === Or10443Const.DEFAULT.HUKUSUU) {
    for (const user of local.userList) {
      await handleCallApiReport(user)
    }
    return
  }
  /* 利用者選択 */
  const user = local.userList[0] || null
  for (const item of local.orX0128DetList) {
    await handleCallApiReport(user, item)
  }
  if (!orX0117Oneway.historyList.length) return
  OrX0117Logic.state.set({
    uniqueCpId: orX0117.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
async function handleCallApiReport(user: UserEntity, item: OrX0128Items | null = null) {
  if (mo01334TypeReport.value.value === '1') {
    await handleReportOutput(user, item)
  }
  if (mo01334TypeReport.value.value === '2') {
    await handleUserNonBasicInfoReport(user, item)
  }
  await handleUserMultiBasicInfoReport(user, item)
}
function checkDisabledOaram050OneWay() {
  if (local.mo00039TypeUserSelectType === Or10443Const.DEFAULT.TANI && local.mo00039TypeHistorySelectType === Or10443Const.DEFAULT.TANI) {
    localOneway.param050OneWay.disabled = false
  } else {
    localOneway.param050OneWay.disabled = true
  }
}
/* AC007_「同意欄の登録」ボタン押下 */
function handleOpenModalGUI00617() {
  Or10148Logic.state.set({
    uniqueCpId: or10148.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function checkDisabledCheckbox(block = true) {
  if (block) return
  if (mo01334TypeReport.value.value === Or10443Const.DEFAULT.HUKUSUU) {
    localOneway.param03OneWay.disabled = true
    localOneway.param04Oneway.disabled = true
    localOneway.param049OneWay.disabled = true
    localOneway.param048OneWay.disabled = true
    localOneway.param047OneWay.disabled = true
    mo00610Oneway.value.disabled = true
    localOneway.param046OneWay.disabled = true
    localOneway.param044OneWay.disabled = true
    return
  }
  localOneway.param03OneWay.disabled = false
  localOneway.param04Oneway.disabled = false
  localOneway.param049OneWay.disabled = false
  localOneway.param048OneWay.disabled = false
  localOneway.param047OneWay.disabled = false
  mo00610Oneway.value.disabled = false
  localOneway.param046OneWay.disabled = false
  localOneway.param044OneWay.disabled = false
}
function mappingDataTableKikanRireki(rirekiList: RirekiList[]) {
  orX0128OnewayModel.items = []
  const tempList: string[] = [] as string[]
  rirekiList.forEach((item, index) => {
    const obj = {...item, userId: item.userid}
    const planPeriod =
      t('label.plan-period') +
      Or10443Const.DEFAULT.SPLIT_COLON +
      item.startYmd +
      Or10443Const.DEFAULT.SPLIT_TILDE +
      item.endYmd
    if (!tempList.includes(planPeriod)) {
      tempList.push(planPeriod)
      obj.planPeriod = planPeriod
      obj.isPeriodManagementMergedRow = true
      orX0128OnewayModel.items.push(obj)
      const objMap = {id: `${index + 1}` , ...obj, isPeriodManagementMergedRow: false}
      delete objMap.planPeriod
      orX0128OnewayModel.items.push(objMap)
      return
    }
    obj.isPeriodManagementMergedRow = false
    obj.id = `${index + 1}`
    orX0128OnewayModel.items.push(obj)
  })
}
/* AC001_初期表示 */
async function preventionPlanPrintUserSettingsInitSelect() {
  const params: PreventionPlanPrintUserSettingsInitSelectInEntity = {
    sysCd: systemCommonsStore.getSystemCode ?? '',
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    shokuId: systemCommonsStore.getStaffId ?? '',
    userId: localOneway.Or10443.userId ?? '',
    sectionName: localOneway.Or10443.sectionName ?? '',
    choIndex: localOneway.Or10443.choIndex ?? '',
    kinounameKnj: localOneway.Or10443.kinounameKnj ?? '',
    kojinhogoFlg: localOneway.Or10443.kojinhogoFlg ?? '',
    sectionAddNo: localOneway.Or10443.sectionAddNo ?? '',
  }
  const res: PreventionPlanPrintUserSettingsInitSelectOutEntity = await ScreenRepository.select(
    'preventionPlanPrintUserSettingsInitSelect',
    params
  )
  if (!res.data) return
  const choPrtList: ChoPrtList[] = res.data.choPrtList ?? []
  mo01334OnewayReport.value.items = choPrtList.map((item) => {
    return {
      id: item.prtNo,
      mo01337OnewayReport: {
        value: item.prtTitle,
        unit: '',
      },
      ...item,
    }
  })
  if (choPrtList.length > 0) mo01334TypeReport.value.value = choPrtList[0].prtNo ?? ''
  mappingRefValue(choPrtList)
  await nextTick()
  localOneway.orX0145Oneway.selectedUserCounter = refValue.value.choPrtList[mo01334TypeReport.value.value]?.param06 ?? ''
  mappingDataTableKikanRireki(res.data.rirekiList)
}

function mappingRefValue(choPrtList:ChoPrtList[] = [], reverse = false) {
  if (!refValue.value) return
  if (reverse) {
    refValue.value.choPrtList[mo01334TypeReport.value.value].param06 = `${orX0145Type.value.value.value}`
    const choPrtListMap: ChoPrtListMaping[] = Object.values(refValue.value.choPrtList) ?? []
    const array = choPrtListMap.map((e: ChoPrtListMaping, index) => {
    return {
      ...e,
      prtTitle: e.prtTitle.value,
      param02: e.param02.value,
      param03: e.param03.modelValue ? 'TRUE' : 'FALSE',
      param04: e.param04.value,
      param044: e.param044.modelValue ? 'TRUE' : 'FALSE',
      param045: e.param045.modelValue ? 'TRUE' : 'FALSE',
      param046: e.param046.modelValue ? 'TRUE' : 'FALSE',
      param047: e.param047.modelValue ? 'TRUE' : 'FALSE',
      param048: e.param048.modelValue ? 'TRUE' : 'FALSE',
      param049: e.param049.modelValue ? 'TRUE' : 'FALSE',
      param050: e.param050.modelValue ? 'TRUE' : 'FALSE',
      index: `${index + 1}`,
      defPrtTitle: '1',
      profile: '1',
      prnDate: '1',
    }
  }) ?? []
    return array
  }
  const output = choPrtList.reduce((obj: Record<string, ChoPrtList>, item: ChoPrtList) => {
    obj[item.prtNo] = {
      ...item,
      prtTitle: { value: item.prtTitle },
      param02: { value: hasData(item.param02) ? item.param02 : systemCommonsStore.getSystemDate },
      param03: { modelValue: item.param03 === 'TRUE' ? true : false },
      param04: { value: item.param04 },
      param044: { modelValue: item.param044 === 'TRUE' ? true : false },
      param045: { modelValue: item.param045 === 'TRUE' ? true : false },
      param046: { modelValue: item.param046 === 'TRUE' ? true : false },
      param047: { modelValue: item.param047 === 'TRUE' ? true : false },
      param048: { modelValue: item.param048 === 'TRUE' ? true : false },
      param049: { modelValue: item.param049 === 'TRUE' ? true : false },
      param050: { modelValue: item.param050 === 'TRUE' ? true : false }
    }
    return obj
  }, {})
  refValue.value = { choPrtList: output }
  useScreenStore().setCpTwoWay({
    cpId: Or10443Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

/* AC002_「×ボタン」押下 */
/* AC016_「閉じるボタン」押下 */
async function preventionPlanPrintUserSettingsUserChangeSelect() {
  const userId = local.userList.length ? `${local.userList[0].id}` : ''
  const params: PreventionPlanPrintUserSettingsUserChangeSelectInEntity = {
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    userId: userId
  }
  const res: PreventionPlanPrintUserSettingsUserChangeSelectOutEntity =
    await ScreenRepository.select('preventionPlanPrintUserSettingsUserChangeSelect', params)
  if (!res.data) return
  mappingDataTableKikanRireki(res.data.kikanRirekiList)
}

/* AC017_「PDFダウンロード」ボタン押下 */
async function preventionPlanPrintUserSettingsSubjectSelect() {
  if (!refValue.value) return
  const sysIniInfoList = [
    {
      profile: '',
      amikakeFlg: '',
      amikakeModifiedCnt: '',
      iso9001Flg: '',
      iso9001ModifiedCnt: '',
      kojinhogoDisplayFlg: '',
      kojinhogoFlg: '',
      kojinhogoModifiedCnt: '',
    },
  ]
  const prtList = mappingRefValue([], true)
  const params: PreventionPlanPrintUserSettingsSubjectSelectInEntity = {
    sectionName: localOneway.Or10443.sectionName ?? '',
    gsyscd: systemCommonsStore.getSystemCode ?? '',
    shokuId: systemCommonsStore.getStaffId ?? '',
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    prtList: prtList,
    sysIniInfoList: sysIniInfoList,
  }
  await ScreenRepository.update('preventionPlanPrintUserSettingsSubjectUpdate', params)
}
async function handleReportOutput(user: UserEntity, item: OrX0128Items | null = null) {
  const params = getParamsUserBasicInfoReport()
  if (local.mo00039TypeUserSelectType === Or10443Const.DEFAULT.HUKUSUU) {
    params.printSubjectHistoryList = [{userName: `${user.name1Knj}${user.name2Knj}`}]
    await reportOutput(Or10443Const.USER_BASIC_INFO_REPORT_ID, params, reportOutputType.DOWNLOAD)
    return
  }
  params.printSubjectHistoryList = [{userName: `${user.name1Knj}${user.name2Knj}`}]
  params.shokuinKnj = `${item.shokuName}`
  params.soudanYmd = `${item.createYmd}`
  await reportOutput(Or10443Const.USER_BASIC_INFO_REPORT_ID, params, reportOutputType.DOWNLOAD)
}
async function handleUserNonBasicInfoReport(user: UserEntity, item: OrX0128Items | null = null) {
  const params = getParamsUserNonBasicInfoReport()
  params.printSubjectHistoryList = [{
    userName: `${user.name1Knj}${user.name2Knj}`,
    userId: `${user.id}`,
    c1Id: item ? `${item.sc1Id}` : '',
    startYmd: item ? `${item.startYmd}` : '',
    endYmd: item ? `${item.endYmd}` : '',
  }]
  await reportOutput(Or10443Const.USER_NON_BASIC_INFO_REPORT_ID, params, reportOutputType.DOWNLOAD)
}
async function handleUserMultiBasicInfoReport(user: UserEntity, item: OrX0128Items | null = null) {
  const params = getParamsUserMultiBasicInfoReport()
  params.printSubjectHistoryList = [{
    userName: `${user.name1Knj}${user.name2Knj}`,
    userId: `${user.id}`,
    c1Id: item ? `${item.sc1Id}` : '',
    startYmd: item ? `${item.startYmd}` : '',
    endYmd: item ? `${item.endYmd}` : '',
  }]
  await reportOutput(Or10443Const.USER_MULTI_BASIC_INFO_REPORT_ID, params, reportOutputType.DOWNLOAD)
}
function getParamsUserBasicInfoReport() {
  const sysIniInfoList = [
    {
      profile: '',
      amikakeFlg: '',
      amikakeModifiedCnt: '',
      iso9001Flg: '',
      iso9001ModifiedCnt: '',
      kojinhogoDisplayFlg: '',
      kojinhogoFlg: '',
      kojinhogoModifiedCnt: '',
    },
  ]
  const TypeReport: string = mo01334TypeReport.value.value ? `${mo01334TypeReport.value.value}` : ''
  const params: PreventionPlanPrintUserSettingsSubjectSelectMapInEntity = {
    sectionName: localOneway.Or10443.sectionName ?? '',
    gsyscd: systemCommonsStore.getSystemCode ?? '',
    shokuId: systemCommonsStore.getStaffId ?? '',
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    svJigyoKnj: '',
    shokuinKnj: '',
    khn11Id: '',
    soudanYmd: '',
    printSet: {
      shiTeiKubun: `${refValue.value?.choPrtList[TypeReport]?.prndate ?? ''}`,
      shiTeiDate: `${refValue.value?.choPrtList[TypeReport]?.param02.value ?? ''}`,
    },
    printOption: {
      h27PrintFlag: refValue.value?.choPrtList[TypeReport]?.param045.modelValue ? '1' : '0',
      emptyFlg: refValue.value?.choPrtList[TypeReport]?.param050.modelValue ? 'true' : 'false',
    },
    printSubjectHistoryList: [],
    sysIniInfoList: sysIniInfoList,
  }
  return params
}
function getParamsUserNonBasicInfoReport() {
  const TypeReport: string = mo01334TypeReport.value.value ? `${mo01334TypeReport.value.value}` : ''
  const params = {
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    svJigyoKnj: '',
    asYmd: '',
    chiJigyoId: '',
    itkJigyoId: '',
    kycFlg: '',
    khn11Id: '',
    keishyoHenkouFlag: '',
    keishyoHenkou: '',
    doijouhouPrintFlg: '',
    kiourekishiBlankSuppressFlg: '',
    doiBunshouChangeFlg: '',
    ouinranPrintExcludeFlg: '',
    h27PrintFlag : refValue.value?.choPrtList[TypeReport]?.param045.modelValue ? '1' : '0',
    rangaiUserNamePrintFlg: '',
    douiInfoPrt: '',
    printSet: {
      shiTeiKubun: `${refValue.value?.choPrtList[TypeReport]?.prndate ?? ''}`,
      shiTeiDate: `${refValue.value?.choPrtList[TypeReport]?.param02.value ?? ''}`,
    },
    printOption: {
      h27PrintFlag: refValue.value?.choPrtList[TypeReport]?.param045.modelValue ? '1' : '0',
      emptyFlg: refValue.value?.choPrtList[TypeReport]?.param050.modelValue ? 'true' : 'false',
    },
    printSubjectHistoryList: []
  }
  return params
}
function getParamsUserMultiBasicInfoReport() {
  const TypeReport: string = mo01334TypeReport.value.value ? `${mo01334TypeReport.value.value}` : ''
  const prtList = mappingRefValue([], true)
  const params = {
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    svJigyoKnj: '',
    asYmd: '',
    chiJigyoId: '',
    itkJigyoId: '',
    kycFlg: '',
    khn11Id: '',
    keishyoHenkouFlag: '',
    keishyoHenkou: '',
    doijouhouPrintFlg: '',
    kiourekishiBlankSuppressFlg: '',
    doiBunshouChangeFlg: '',
    ouinranPrintExcludeFlg: '',
    h27PrintFlag : refValue.value?.choPrtList[TypeReport]?.param045.modelValue ? '1' : '0',
    rangaiUserNamePrintFlg: '',
    innkanPrt: '',
    douiInfoPrt: '',
    userInfoPrt: '',
    printSet: {
      shiTeiKubun: `${refValue.value?.choPrtList[TypeReport]?.prndate ?? ''}`,
      shiTeiDate: `${refValue.value?.choPrtList[TypeReport]?.param02.value ?? ''}`,
    },
    printOption: {
      keishoFlg: '',
      emptyFlg: refValue.value?.choPrtList[TypeReport]?.param050.modelValue ? 'true' : 'false',
    },
    printSubjectHistoryList: [],
    choPrtList: prtList,
  }
  return params
}
function hasData(data: unknown) {
  if (data !== '' && data !== null && data !== undefined) return true
  else return false
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or10443_screen"
      >
        <c-v-col
          cols="12"
          sm="2"
          class="pa-0 pt-2 pl-2 box-left"
        >
          <base-mo-01334
            v-model="mo01334TypeReport"
            :oneway-model-value="mo01334OnewayReport"
            class="list-wrapper"
          >
            <!-- 帳票 -->
            <template #[`item.prtTitle`]="{ item }">
              <base-mo01337 :oneway-model-value="item.mo01337OnewayReport" />
            </template>
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          v-if="refValue.choPrtList[mo01334TypeReport.value]"
          cols="12"
          sm="3"
          class="pa-0 pt-2 content_center box-center"
        >
          <c-v-row
            no-gutter
            class="or10443_row flex-center"
          >
            <!-- タイトル -->
            <c-v-col
              cols="12"
              sm="4"
              class="pa-0 pl-2"
            >
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle" />
            </c-v-col>
            <c-v-col
              cols="12"
              sm="8"
              class="pa-0"
            >
              <base-mo00045
                v-model="refValue.choPrtList[mo01334TypeReport.value].prtTitle"
                :oneway-model-value="localOneway.mo00045OnewayTitleInput"
                @keyup.enter="checkTitleInput"
              />
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0 mt-2 mb-2" />
          <c-v-row
            no-gutter
            class="customCol or10443_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              class="pa-0"
            >
              <!-- 印刷日付選択ラジオボタングループ -->
              <base-mo00039
                v-model="refValue.choPrtList[mo01334TypeReport.value].prndate"
                :oneway-model-value="localOneway.prndateOneWay"
              />
            </c-v-col>
            <c-v-col
              id="test"
              cols="12"
              sm="5"
              class="pa-0"
            >
              <!-- 印刷日付ラベル -->
              <base-mo00020
                v-if="refValue.choPrtList[mo01334TypeReport.value].prndate === '2'"
                v-model="refValue.choPrtList[mo01334TypeReport.value].param02"
                :oneway-model-value="localOneway.prndateOneWay"
              />
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0 mt-2 mb-2" />
          <c-v-row
            no-gutter
            class="printerOption customCol or10443_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pt-0 pb-0"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or10443_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 flex flex-row align-center"
            >
              <!-- 敬称を変更する -->
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param03"
                :oneway-model-value="localOneway.param03OneWay"
              />
              <!-- 敬称を変更する -->
              (<base-mo00045
                v-model="refValue.choPrtList[mo01334TypeReport.value].param04"
                :oneway-model-value="localOneway.param04Oneway"
                :disabled="!refValue.choPrtList[mo01334TypeReport.value].param03.modelValue"
              />)
            </c-v-col>
            <!-- 記入用シートを印刷する -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param050"
                :oneway-model-value="localOneway.param050OneWay"
              />
            </c-v-col>
            <!-- 欄外に利用者氏名を印刷する -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param049"
                :oneway-model-value="localOneway.param049OneWay"
              />
            </c-v-col>
            <!-- 同意情報を印刷する -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param048"
                :oneway-model-value="localOneway.param048OneWay"
              />
            </c-v-col>
            <!-- 同意欄文章を変更する -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 flex flex-row align-center"
            >
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param047"
                :oneway-model-value="localOneway.param047OneWay"
              />
              <base-mo00610
                :oneway-model-value="mo00610Oneway"
                @click="handleOpenModalGUI00617()"
              />
            </c-v-col>
            <!-- 既往歴が4行未満の場合は空行印刷しない -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param046"
                :oneway-model-value="localOneway.param046OneWay"
              />
            </c-v-col>
            <!-- H27年様式で印刷する -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param045"
                :oneway-model-value="localOneway.param045OneWay"
              />
            </c-v-col>
            <!-- 押印欄を印刷しない -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param044"
                :oneway-model-value="localOneway.param044OneWay"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          v-if="refValue.choPrtList[mo01334TypeReport.value]"
          cols="12"
          sm="7"
          class="pa-0 box-right"
        >
          <c-v-row
            class="or10443_row pa-2"
            no-gutter
            style="align-items: center"
          >
            <c-v-col
              cols="4"
              class="pa-0"
            >
              <!-- 利用者選択 -->
              <base-mo01338
                class="mt-2 pl-2"
                :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                style="background-color: transparent"
              />
              <!-- 利用者選択ラジオボタングループ -->
              <base-mo00039
                v-model="local.mo00039TypeUserSelectType"
                :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
              />
            </c-v-col>
            <c-v-col
              v-if="local.mo00039TypeUserSelectType === Or10443Const.DEFAULT.TANI"
              cols="4"
              class="pa-0"
            >
              <!-- 履歴選択 -->
              <base-mo01338
                class="mt-2 pl-2"
                :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                style="background-color: transparent"
              />
              <!-- 履歴選択 -->
              <base-mo00039
                v-model="local.mo00039TypeHistorySelectType"
                :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
              />
            </c-v-col>
            <c-v-col
              v-if="local.mo00039TypeUserSelectType === Or10443Const.DEFAULT.HUKUSUU"
              cols="4"
              class="pa-0"
            >
              <!-- 履歴選択ラベル -->
              <base-mo01338
                class="mt-2 pl-2"
                :oneway-model-value="localOneway.mo01338OneWayBaseDate"
                style="background-color: transparent"
              />
              <base-mo00020
                v-model="local.mo00020TypeKijunbi"
                :oneway-model-value="localOneway.mo00020KijunbiOneWay"
              />
            </c-v-col>
            <c-v-col
              cols="4"
              class="pa-0"
            >
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            class="or10443_row"
            no-gutter
          >
            <c-v-col
              class="overflow-auto"
              :cols="userCols"
            >
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              />
            </c-v-col>
            <c-v-col
              v-if="local.mo00039TypeUserSelectType === Or10443Const.DEFAULT.TANI"
              cols="6"
              style="overflow-x: auto"
            >
              <g-custom-or-x-0128
                v-if="orX0128OnewayModel.singleFlg"
                v-bind="orX0128"
                :oneway-model-value="orX0128OnewayModel"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        />
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="handlePrint()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813 v-bind="or21813" />
  <g-base-or21815 v-bind="or21815" />
  <g-custom-or-10148
    v-if="showDialogOr10148"
    v-bind="or10148"
    v-model="local.or10148"
  />
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  />/
</template>

<style lang="scss">
  .or10443_row {
    table {
      th {
        padding: 0px!important;
        .v-data-table-header__content {
          padding: 0px 5px!important;
        }
      }
      td {
        padding: 0px!important;
        .v-col {
          padding: 0px 5px!important;
        }
      }
    }
  }
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';
.or10443_screen {
  margin: -8px !important;
}

.box-left, .box-center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or10443_row {
  margin: 0px !important;
}

.flex-center {
  display: flex;
  align-items: center;
}

.user_class {
  margin-left: -12px;
}
.flex {
  display: flex;
}
.flex-row {
  flex-direction: row;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.align-center {
  align-items: center;
}
.flex-1 {
  flex: 1;
}
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
.btn-mo00610 {
  outline: none;
  background-color: rgb(var(--v-theme-black-100));
  border-color: rgb(var(--v-theme-black-100));
  color: black !important;
}
.customCol {
  margin-left: 0px;
  margin-right: 0px;
}
.overflow-auto {
  overflow: auto!important;
}
.or56885-pd-8 {
  padding: 8px;
}
</style>
