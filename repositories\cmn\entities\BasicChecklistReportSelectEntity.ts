import type { InWebEntity } from '~/repositories/AbstructWebRepository'
/**
 * GUI01085_［印刷設定］画面
 * Or30590:（基本チェックリスト）印刷設定モーダル
 *
 * @description
 * エンティティ
 *
 * <AUTHOR> DO AI QUOC
 */

/**
 * 基本チェックリスト帳票入力エンティティ
 */
export interface BasicChecklistReportSelectInEntity extends InWebEntity {
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * タイトル
   */
  title: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * ヘッダID
   */
  rirekiId: string
  /**
   * メモを印刷する
   */
  memoPrtFlg: string
  /**
   * 被保険者番号を印刷する
   */
  hokenNoPrtFlg: string
  /**
   * 希望するサービス内容を印刷する
   */
  kiboSvPrtFlg: string
  /**
   * 住所を印刷する
   */
  addressPrtFlg: string
  /**
   * 記入日を印刷する
   */
  createYmdPrtFlg: string
  /**
   * 生年月日を印刷する
   */
  birthdayYmdPrtFlg: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 事業者名
   */
  svJigyoKnj: string
  /**
   * 作成年月日
   */
  createYmd: string
  /**
   * 印刷設定
   */
  printSet: PrintSetEntity
  /**
   * 印刷オプション
   */
  printOption: PrintOptionEntity
}

/**
 * 印刷設定
 */
export interface PrintSetEntity {
  /**
   * 指定日印刷区分
   */
  shiTeiKubun: string
  /**
   * 指定日
   */
  shiTeiDate: string
}

/**
 * 印刷オプション
 */
export interface PrintOptionEntity {
  /**
   * 記入用シートを印刷する
   */
  emptyFlg: string
  /**
   * 敬称変更フラグ
   */
  keishoFlg: string
  /**
   * 敬称
   */
  keishoKnj: string
}
