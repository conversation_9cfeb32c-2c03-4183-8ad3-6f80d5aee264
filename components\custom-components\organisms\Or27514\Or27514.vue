<script setup lang="ts">
/**
 * Or27514:有機体:パターン（グループ）
 * GUI00995_パターン（グループ）
 *
 * @description
 * パターン（グループ）では、内容を選択し、呼び出し元の画面に反映します。
 *
 * <AUTHOR>
 */
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0053Const } from '../OrX0053/OrX0053.constants'
import { OrX0054Const } from '../OrX0054/OrX0054.constants'
import { OrX0153Const } from '../OrX0153/OrX0153.constants'
import type { AsyncFunction, Or27514StateType } from './Or27514.Type'
import { Or27514Const } from './Or27514.constants'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { useScreenOneWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useScreenStore } from '~/stores/session/screen'
import type { Or27514OnewayType } from '~/types/cmn/business/components/Or27514Type'
import type { OrX0053OnewayType, OrX0053Type } from '~/types/cmn/business/components/OrX0053Type'
import type { OrX0153OnewayType, OrX0153Type } from '~/types/cmn/business/components/OrX0153Type'
import type { OrX0054OnewayType, OrX0054Type } from '~/types/cmn/business/components/OrX0054Type'
import type {
  Or21814FirstBtnType,
  Or21814SecondBtnType,
  Or21814ThirdBtnType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import type { NavControlJsonType } from '~/types/business/core/DefinitionJsonType'
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or27514OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or27514OnewayType = {
  mstKbn: '2',
  kaiteiFlg: '1',
  keiyakushaId: '1',
  local: '1',
  week1Id: '1',
  assessmentType: '1',
  userId: '1',
  kikan: '1',
  termid: '1',
  weeklyModel: '1',
  processYmd: '2025/05/29',
  week2List: [
    {
      week2Id: '1',
      week1Id: '1',
      youbi: '1',
      kaishiJikan: '06:00',
      shuuryouJikan: '12:00',
      naiyoCd: '1',
      naiyoKnj: '1',
      memoKnj: '右手指一部麻痺のた',
      fontSize: '12',
      dispMode: '0',
      alignment: '1',
      svShuruiCd: '1',
      svItemCd: '1',
      svJigyoId: '1',
      svShuruiKnj: '訪問看護',
      svItemKnj: '',
      svJigyoKnj: '1',
      svJigyoRks: '',
      fontColor: '#FF0000',
      backColor: '#00FF00',
      timeKbn: '1',
      igaiMoji: '1日~3日·5日~7日·\r\n9日~11日·13日~15日',
      igaiKbn: '1',
      igaiDate: '1',
      igaiWeek: '1',
    },
  ],
}

const orX0053 = ref({ uniqueCpId: OrX0053Const.CP_ID(0) })

const orX0054 = ref({ uniqueCpId: OrX0054Const.CP_ID(0) })

const orX0153 = ref({ uniqueCpId: OrX0153Const.CP_ID(0) })

const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })

// ダイアログ表示フラグ
const showInfoDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 画面.画面タイトル
let titleId: unknown
switch (props.onewayModelValue.mstKbn) {
  case Or27514Const.DEFAULT.MSTKBN_LIST.DAILY_PARTTEN:
    titleId = t('label.daily-table-pattern')
    break
  case Or27514Const.DEFAULT.MSTKBN_LIST.WEEK_PARTTEN:
    titleId = t('label.week-table-pattern')
    break
  case Or27514Const.DEFAULT.MSTKBN_LIST.MONTHYEAR_PARTTEN:
    titleId = t('label.monthly-yearly-table-pattern')
    break
  default:
    break
}

const localOneWay = reactive({
  or27514: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // タブ
  mo00043OneWay: {
    tabItems: [
      { id: Or27514Const.TAB.TAB_ID_TITLE, title: t('label.title') },
      { id: Or27514Const.TAB.TAB_ID_SET, title: t('label.settings') },
      { id: Or27514Const.TAB.TAB_ID_GROUP, title: t('label.group') },
    ],
  } as Mo00043OnewayType,
  // 閉じる
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 保存コンポーネント
  mo00609OneWay: {
    btnLabel: t('btn.save'),
  } as Mo00609OnewayType,
  // 情報ダイアログ
  mo00024Oneway: {
    width: '1400px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or27514',
      toolbarTitle: titleId,
      toolbarName: 'Or27514ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // タブ：グループ
  orX0053OneWay: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  } as OrX0053OnewayType,
  // タブ：タイトル
  orX0054OneWay: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  } as OrX0054OnewayType,
  // タブ：設定
  orX0153OneWay: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  } as OrX0153OnewayType,
})

const local = reactive({
  mo00043: { id: Or27514Const.TAB.TAB_ID_TITLE } as Mo00043Type,
  orX0053: {
    editFlg: false,
  } as OrX0053Type,
  orX0054: {
    editFlg: false,
  } as OrX0054Type,
  orX0153: {
    // その他のサービス
    weeklyModel: { value: '' },
    // (保険サービス)
    hokenModel: { value: '' },
    // 詳細リスト
    weekPlanList: [],
    // 週間表ID
    week1Id: '',
    // 有効期間ID
    termid: '',
  } as OrX0153Type,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27514StateType>({
  cpId: Or27514Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value: boolean | undefined) =>
      (mo00024.value.isOpen = value ?? Or27514Const.DEFAULT.IS_OPEN),
  },
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27514Const.DEFAULT.IS_OPEN,
})

// OrX0153 Ref
const orX0153Ref = ref<{
  saveBtnClick: AsyncFunction
  confirmBtnClick: AsyncFunction
  close: AsyncFunction
}>()

// OrX0054 Ref
const orX0054Ref = ref<{
  close: AsyncFunction
  save: AsyncFunction
}>()

// OrX0053 Ref
const orX0053Ref = ref<{
  close: AsyncFunction
  save: AsyncFunction
}>()
// メニュー切替確認ダイアログ、いいえフラグ
let noFlg = false
// ナビゲーション制御領域のいずれかの編集フラグがON
useSystemCommonsStore().setShowEditDiscardDialog(useScreenStore().isEditNavControl())
const isEdit = computed(() => {
  return useSystemCommonsStore().getShowEditDiscardDialog
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0054Const.CP_ID(0)]: orX0054.value,
  [OrX0053Const.CP_ID(0)]: orX0053.value,
  [OrX0153Const.CP_ID(0)]: orX0153.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
})
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

const isNewTab = { newTabFlg: false, newTab: '', oldTab: local.mo00043.id }

// メニュー切替
watch(
  () => local.mo00043.id,
  async (newValue) => {
    if (!noFlg) {
      // 変更がない場合、画面データあるかどうかを判定する。
      if (isNewTab.oldTab !== newValue) {
        isNewTab.newTabFlg = true
        isNewTab.newTab = newValue
        // タブを設定に切り替え
        await nextTick()
        if (isNewTab.oldTab === Or27514Const.TAB.TAB_ID_SET) {
          if (isEditSet.value) {
            // タブ切替の阻止
            local.mo00043.id = Or27514Const.TAB.TAB_ID_SET
            const dialogResult = await openInfoDialog(
              t('message.i-cmn-10430'),
              'normal1',
              'destroy1',
              'normal3'
            )
            switch (dialogResult) {
              case OrX0153Const.DEFAULT.DIALOG_RESULT_YES:
                //  AC027を行って、処理続き。
                await save()
                break
              case OrX0153Const.DEFAULT.DIALOG_RESULT_NO:
                // いいえ：処理続き。
                noFlg = true
                local.mo00043.id = isNewTab.newTab
                isNewTab.oldTab = isNewTab.newTab
                break
              case OrX0153Const.DEFAULT.DIALOG_RESULT_CANCEL:
                break
            }
          } else {
            isNewTab.oldTab = newValue
          }
        } else if (isNewTab.oldTab === Or27514Const.TAB.TAB_ID_TITLE) {
          if (isEditTitle.value) {
            // タブ切替の阻止
            local.mo00043.id = Or27514Const.TAB.TAB_ID_TITLE
            const dialogResult = await openInfoDialog(
              t('message.i-cmn-10430'),
              'normal1',
              'destroy1',
              'normal3'
            )
            switch (dialogResult) {
              case OrX0153Const.DEFAULT.DIALOG_RESULT_YES:
                //  AC027を行って、処理続き。
                await save()
                break
              case OrX0153Const.DEFAULT.DIALOG_RESULT_NO:
                // いいえ：処理続き。
                noFlg = true
                local.mo00043.id = isNewTab.newTab
                isNewTab.oldTab = isNewTab.newTab
                break
              case OrX0153Const.DEFAULT.DIALOG_RESULT_CANCEL:
                break
            }
          } else {
            isNewTab.oldTab = newValue
          }
        } else if (isNewTab.oldTab === Or27514Const.TAB.TAB_ID_GROUP) {
          if (isEditGroup.value) {
            // タブ切替の阻止
            local.mo00043.id = Or27514Const.TAB.TAB_ID_GROUP
            const dialogResult = await openInfoDialog(
              t('message.i-cmn-10430'),
              'normal1',
              'destroy1',
              'normal3'
            )
            switch (dialogResult) {
              case OrX0153Const.DEFAULT.DIALOG_RESULT_YES:
                //  AC027を行って、処理続き。
                await save()
                break
              case OrX0153Const.DEFAULT.DIALOG_RESULT_NO:
                // いいえ：処理続き。
                noFlg = true
                local.mo00043.id = isNewTab.newTab
                isNewTab.oldTab = isNewTab.newTab
                break
              case OrX0153Const.DEFAULT.DIALOG_RESULT_CANCEL:
                break
            }
          } else {
            isNewTab.oldTab = newValue
          }
        }
      }
    } else {
      isNewTab.newTabFlg = false
      noFlg = false
    }
  }
)

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  // 画面入力データ変更があるかどうかを判定する
  // 変更がない場合、画面を閉じる。
  if (!isEdit.value) {
    setState({ isOpen: false })
  } else {
    if (local.mo00043.id === Or27514Const.TAB.TAB_ID_TITLE) {
      const saveResult = await orX0054Ref.value?.close()
      if (!saveResult?.isBreak) {
        setState({ isOpen: false })
      }
    } else if (local.mo00043.id === Or27514Const.TAB.TAB_ID_SET) {
      const closeResult = await orX0153Ref.value?.close()
      if (!closeResult?.isBreak) {
        setState({ isOpen: false })
      }
    } else if (local.mo00043.id === Or27514Const.TAB.TAB_ID_GROUP) {
      const saveResult = await orX0053Ref.value?.close()
      if (!saveResult?.isBreak) {
        setState({ isOpen: false })
      }
    }
  }
}

/**
 * 保存
 */
async function save() {
  if (local.mo00043.id === Or27514Const.TAB.TAB_ID_TITLE) {
    await orX0054Ref.value?.save()
    return
  } else if (local.mo00043.id === Or27514Const.TAB.TAB_ID_SET) {
    await orX0153Ref.value?.saveBtnClick()
    return
  } else if (local.mo00043.id === Or27514Const.TAB.TAB_ID_GROUP) {
    await orX0053Ref.value?.save()
    return
  }
}

/**
 * 編集破棄ダイアログ表示
 *
 * @param msg - msg
 *
 * @param firstBtn - firstBtn
 *
 * @param secondBtn - secondBtn
 *
 * @param thirdBtn - thirdBtn
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openInfoDialog(
  msg: string,
  firstBtn: Or21814FirstBtnType,
  secondBtn: Or21814SecondBtnType,
  thirdBtn: Or21814ThirdBtnType
): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: firstBtn,
      firstBtnLabel: t('btn.yes'),
      secondBtnType: secondBtn,
      secondBtnLabel: t('btn.no'),
      thirdBtnType: thirdBtn,
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = Or27514Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or27514Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or27514Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or27514Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
// 変更されたリスニングのコンポーネントIDリスト
const groupWatchedComponents = ref<string[]>([orX0053.value.uniqueCpId])
const isEditGroup = computed(() => {
  return isEditNavControl(groupWatchedComponents.value)
})

// 変更されたリスニングのコンポーネントIDリスト
const setWatchedComponents = ref<string[]>([orX0153.value.uniqueCpId])
const isEditSet = computed(() => {
  return isEditNavControl(setWatchedComponents.value)
})

// 変更されたリスニングのコンポーネントIDリスト
const titleWatchedComponents = ref<string[]>([orX0054.value.uniqueCpId])
const isEditTitle = computed(() => {
  return isEditNavControl(titleWatchedComponents.value)
})

/**
 * いずれかの編集フラグがONになっているかチェックする
 *
 * @param components - 変更のリスニングが必要なリスト
 *
 * @returns いずれかの編集有無
 */
function isEditNavControl(components: string[]) {
  const screenStore = useScreenStore()
  const screenNavControl = screenStore.getScreenNavControl<NavControlJsonType>()

  // ナビゲーション制御領域を走査
  for (const key in screenNavControl) {
    if (screenNavControl[key] === screenNavControl.components) {
      // ナビゲーション制御領域 - コンポーネント毎の領域を走査
      for (const cpKey in screenNavControl[key]) {
        if (components.includes(cpKey)) {
          if (screenNavControl[key][cpKey].editFlg) {
            // コンポーネント毎の編集フラグがON
            return true
          }
          if (screenNavControl[key][cpKey].items) {
            // ナビゲーション制御領域 - コンポーネント毎の領域にitemsが存在する場合は走査
            for (const itemKey in screenNavControl[key][cpKey].items) {
              if (screenNavControl[key][cpKey].items[itemKey].editFlg) {
                // コンポーネント - itemsの編集フラグがON
                return true
              }
            }
          }
        }
      }
    }
  }
  return false
}
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneWay.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043"
        :oneway-model-value="localOneWay.mo00043OneWay"
        style="padding-left: 0px !important"
      ></base-mo00043>
      <!-- タブ switch -->
      <c-v-window v-model="local.mo00043.id">
        <c-v-window-item
          value="title"
          style="padding-left: 0px !important"
        >
          <div>
            <!-- タブ：タイトル -->
            <g-custom-or-x-0054
              ref="orX0054Ref"
              v-model="local.orX0054"
              v-bind="orX0054"
              :oneway-model-value="localOneWay.orX0054OneWay"
              :parent-unique-cp-id="props.uniqueCpId"
            ></g-custom-or-x-0054>
          </div>
        </c-v-window-item>
        <c-v-window-item value="settings">
          <!-- タブ：設定 -->
          <g-custom-or-x-0153
            ref="orX0153Ref"
            v-model="local.orX0153"
            v-bind="orX0153"
            :oneway-model-value="localOneWay.orX0153OneWay"
            :parent-unique-cp-id="props.uniqueCpId"
          ></g-custom-or-x-0153>
        </c-v-window-item>
        <c-v-window-item value="group">
          <!-- タブ：グループ -->
          <g-custom-or-x-0053
            ref="orX0053Ref"
            v-model="local.orX0053"
            v-bind="orX0053"
            :oneway-model-value="localOneWay.orX0053OneWay"
            :parent-unique-cp-id="props.uniqueCpId"
          ></g-custom-or-x-0053>
        </c-v-window-item>
      </c-v-window>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row class="ma-0">
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneWay.mo00611OneWay"
          class="mr-2"
          @click="close()"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.screen-close')"
          />
        </base-mo00611>
        <!-- 保存ボタン-->
        <base-mo00609
          :oneway-model-value="localOneWay.mo00609OneWay"
          @click="save()"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.save')"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showInfoDialog"
    v-bind="or21814"
  />
</template>
<style scoped lang="scss">
:deep(.pl-4) {
  padding-left: 0px !important;
}

.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

:deep(.v-window__container) {
  height: 100%;
}
</style>
