import { Or06146Const } from '../Or06146/Or06146.constants'
import { Or06146<PERSON>ogic } from '../Or06146/Or06146.logic'
import { Or60075Const } from './Or60075.constants'
import type { Or60075EventType, Or60075StateType } from './Or60075.type'
import {
  useEventStatusAccessor,
  useInitialize,
  useOneWayBindAccessor,
  useTwoWayBindAccessor,
} from '~/composables/useComponentLogic'
import type { Or60075Type } from '~/types/cmn/business/components/Or60075Type'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or17391Const } from '~/components/custom-components/organisms/Or17391/Or17391.constants'
import { Or17391Logic } from '~/components/custom-components/organisms/Or17391/Or17391.logic'
import { Or27562Const } from '~/components/custom-components/organisms/Or27562/Or27562.constants'
import { Or27562Logic } from '~/components/custom-components/organisms/Or27562/Or27562.logic'
import { OrX0115Const } from '~/components/custom-components/organisms/OrX0115/OrX0115.constants'
import { OrX0115Logic } from '~/components/custom-components/organisms/OrX0115/OrX0115.logic'
import { Or52082Const } from '~/components/custom-components/organisms/Or52082/Or52082.constants'
import { Or52082Logic } from '~/components/custom-components/organisms/Or52082/Or52082.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { OrX0009Const } from '~/components/custom-components/organisms/OrX0009/OrX0009.constants'
import { OrX0009Logic } from '~/components/custom-components/organisms/OrX0009/OrX0009.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { OrD2002Const } from '~/components/custom-components/organisms/OrD2002/OrD2002.constants'
import { OrD2002Logic } from '~/components/custom-components/organisms/OrD2002/OrD2002.logic'
import { OrX0001Const } from '~/components/custom-components/organisms/OrX0001/OrX0001.constants'
import { OrX0001Logic } from '~/components/custom-components/organisms/OrX0001/OrX0001.logic'
import { Or17392Const } from '~/components/custom-components/organisms/Or17392/Or17392.constants'
import { Or17392Logic } from '~/components/custom-components/organisms/Or17392/Or17392.logic'
import { Or17393Const } from '~/components/custom-components/organisms/Or17393/Or17393.constants'
import { Or17393Logic } from '~/components/custom-components/organisms/Or17393/Or17393.logic'
import { OrX0007Const } from '~/components/custom-components/organisms/OrX0007/OrX0007.constants'
import { OrX0007Logic } from '~/components/custom-components/organisms/OrX0007/OrX0007.logic'
import { OrX0008Const } from '~/components/custom-components/organisms/OrX0008/OrX0008.constants'
import { OrX0008Logic } from '~/components/custom-components/organisms/OrX0008/OrX0008.logic'

/**
 * Or60075:［退院・退所情報記録書］画面テンプレート
 * GUI01305_［退院・退所情報記録書］画面
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or60075Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or60075Const.CP_ID(0),
      uniqueCpId,
      initOneWayState: {
        isSaveCompleted: false, // 保存完了フラグ
      },
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or11871Const.CP_ID },
        { cpId: Or00248Const.CP_ID(0) },
        { cpId: Or41179Const.CP_ID(0) },
        { cpId: OrX0115Const.CP_ID(0) },
        { cpId: Or52082Const.CP_ID(0) },
        { cpId: OrX0009Const.CP_ID(0) },
        { cpId: OrX0007Const.CP_ID(0) },
        { cpId: OrX0008Const.CP_ID(0) },
        { cpId: OrX0001Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(2) },
        { cpId: Or06146Const.CP_ID(1) },
        // { cpId: Or17391Const.CP_ID(1) },
        // { cpId: Or17392Const.CP_ID(1) },
        // { cpId: Or17393Const.CP_ID(1) },
        // { cpId: Or27562Const.CP_ID(0) },
        // { cpId: OrD2002Const.CP_ID(0) },
      ],
      // 編集フラグ不要
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップ
    Or11871Logic.initialize(childCpIds[Or11871Const.CP_ID].uniqueCpId)
    Or00248Logic.initialize(childCpIds[Or00248Const.CP_ID(0)].uniqueCpId)
    Or41179Logic.initialize(childCpIds[Or41179Const.CP_ID(0)].uniqueCpId)
    OrX0115Logic.initialize(childCpIds[OrX0115Const.CP_ID(0)].uniqueCpId)
    Or52082Logic.initialize(childCpIds[Or52082Const.CP_ID(0)].uniqueCpId)
    OrX0009Logic.initialize(childCpIds[OrX0009Const.CP_ID(0)].uniqueCpId)
    OrX0007Logic.initialize(childCpIds[OrX0007Const.CP_ID(0)].uniqueCpId)
    OrX0008Logic.initialize(childCpIds[OrX0008Const.CP_ID(0)].uniqueCpId)
    OrX0001Logic.initialize(childCpIds[OrX0001Const.CP_ID(0)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(2)].uniqueCpId)
    Or06146Logic.initialize(childCpIds[Or06146Const.CP_ID(1)].uniqueCpId)
    // Or17391Logic.initialize(childCpIds[Or17391Const.CP_ID(1)].uniqueCpId)
    // Or17392Logic.initialize(childCpIds[Or17392Const.CP_ID(1)].uniqueCpId)
    // Or17393Logic.initialize(childCpIds[Or17393Const.CP_ID(1)].uniqueCpId)
    // Or27562Logic.initialize(childCpIds[Or27562Const.CP_ID(0)].uniqueCpId)
    // OrD2002Logic.initialize(childCpIds[OrD2002Const.CP_ID(0)].uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<Or60075Type>(Or60075Const.CP_ID(0))

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or60075StateType>(Or60075Const.CP_ID(0))

  /**
   * 自身のEventStatus領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const event = useEventStatusAccessor<Or60075EventType>(Or60075Const.CP_ID(0))
}
