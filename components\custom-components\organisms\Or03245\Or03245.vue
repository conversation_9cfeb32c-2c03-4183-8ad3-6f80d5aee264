<script setup lang="ts">
/**
 * GUI00834_［アセスメント（包括）］洗面画面
 *
 * @description
 *
 * ［アセスメント（包括）］洗面画面
 *
 * 画面ID_ GUI00834
 *
 * <AUTHOR>
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'

import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { TeX0008Logic } from '../../template/TeX0008/TeX0008.logic'
import type {
  CareItems,
  CareLabelType,
  careLocationItems,
  CareLocationLabelType,
  Or34069Type,
} from '../Or34069/Or34069.type'
import { Or34069Const } from '../Or34069/Or34069.constants'
import { Or53105Const } from '../Or53105/Or53105.constants'
import { Or53105Logic } from '../Or53105/Or53105.logic'
import type {
  ConcreteCareItemType,
  ConcreteContentType,
  OrX00096OnewayType,
  OrX0096Type,
} from '../OrX0096/OrX0096.type'
import { Or59423Const } from '../Or59423/Or59423.constants'
import { Or59423Logic } from '../Or59423/Or59423.logic'
import { TeX0008Const } from '../../template/TeX0008/TeX0008.constants'
import { OrX0096Const } from '../OrX0096/OrX0096.constants'
import type { TeX0008StateType } from '../../template/TeX0008/TeX0008.type'
import type { Or03245OnewayType } from './Or03245.type'
import { Or03245Const } from './Or03245.constants'
import { Or03245Logic } from './Or03245.logic'
import type { TeX0008Type } from '~/types/cmn/business/components/TeX0008Type'
import { useScreenStore, useScreenUtils, useSetupChildProps, useSystemCommonsStore } from '#imports'
import type { AssessmentComprehensiveMealUpdateOutEntity } from '~/repositories/cmn/entities/assessmentComprehensiveMealInitSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { AssessmentComprehensiveMealUpdateInEntity } from '~/repositories/cmn/entities/assessmentComprehensiveMealUpdateInEntity'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or51105OnewayType, Or51105Type } from '~/types/cmn/business/components/Or51105Type'
import type { Or34069OnewayType } from '~/types/cmn/business/components/Or34069Type'
import type {
  assessmentComprehensiveQuestionInEntity,
  assessmentComprehensiveQuestionOutWebEntity,
} from '~/repositories/cmn/entities/assessmentComprehensiveQuestionSelect'
import type {
  AssessmentIncludeWashRoomSelectInEntity,
  AssessmentIncludeWashRoomSelectOutEntity,
  AssessmentIncludeWashRoomUpdateInEntity,
  AssessmentIncludeWashRoomUpdateOutEntity,
  Hcc24Info,
} from '~/repositories/cmn/entities/AssessmentIncludeWashRoomSelectEntity'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'

/**************************************************
 * Props
 **************************************************/

interface Props {
  uniqueCpId: string
  onewayModelValue: Or03245OnewayType
  parentUniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const { t } = useI18n()

const { getChildCpBinds } = useScreenUtils()

/**
 * ロード状態制御
 */
const isLoading = ref<boolean>(false)

/**
 * componentRef
 */
const componentRef = ref<HTMLDivElement | null>(null)

/**
 * 保存用テーブルデータ
 */
const tableData = ref<Or34069Type>({} as Or34069Type)

/**
 * 画面更新区分
 */
const screenUpdateKbn = ref(UPDATE_KBN.DELETE)

/**
 * 共通情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()

/**
 * データの更新回数
 */
let updateNum = ''

/**
 * API返却値の番号リストを一時保存する
 */
let dotNumberList: assessmentComprehensiveQuestionOutWebEntity['problemDotSolutionEtcInfo']['problemDotNumberInfoList'] =
  []

/**
 * Twoway
 */
const local = reactive({
  tableHeader: {},
  commonInfo: {} as TeX0008Type,
  orX0096: {
    listSection: props.onewayModelValue.questionList ?? [],
  } as OrX0096Type,
  or51105: {
    kigoImiList: [],
  } as Or51105Type,
  or34096: {
    title: t('label.care-section-related-face-washing-oral-hygiene-grooming-and-dressing'),
    careItems: [
      {
        title: t('label.washface'),
        showMode: '0',
        careLabel: [
          {
            valueType: ['cn011', 'cn012', 'cn013'],
            label: t('label.guidance-to-the-washroom'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn021', 'cn022', 'cn023'],
            label: t('label.instructions-for-face-washing'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn031', 'cn032', 'cn033'],
            label: t('label.assistance-with-partial-face-washing'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn041', 'cn042', 'cn043'],
            label: t('label.full-assistance-with-partial-face-washing'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.mouth-cleaning'),
        showMode: '0',
        careLabel: [
          {
            valueType: ['cn051', 'cn052', 'cn053'],
            label: t('label.preparing-required-items-for-mouth-cleaning'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn061', 'cn062', 'cn063'],
            label: t('label.cleanup-of-items-used-for-mouth-cleaning'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn071', 'cn072', 'cn073'],
            label: t('label.oral-hygiene-such-as-tooth-brushing'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn081', 'cn082', 'cn083'],
            label: t('label.aiding-in-gargling'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn091', 'cn092', 'cn093'],
            label: t('label.maintenance-of-dentures'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn101', 'cn102', 'cn103'],
            label: t('label.preventing-dry-lips'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.plastic-surgery'),
        showMode: '0',
        careLabel: [
          {
            valueType: ['cn111', 'cn112', 'cn113'],
            label: t('label.hair-arrangement-and-styling'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn121', 'cn122', 'cn123'],
            label: t('label.getting-a-haircut'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn131', 'cn132', 'cn133'],
            label: t('label.nail-clippers'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn141', 'cn142', 'cn143'],
            label: t('label.beard-trimmer'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn151', 'cn152', 'cn153'],
            label: t('label.ear-cleaning-tools'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.changing-clothes'),
        showMode: '0',
        careLabel: [
          {
            valueType: ['cn161', 'cn162', 'cn163'],
            label: t('label.preparing-clothes-socks-and-shoes'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn171', 'cn172', 'cn173'],
            label: t('label.supervising-and-instructing-dressing-actions'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn181', 'cn182', 'cn183'],
            label: t('label.prtial-assistance-with-dressing'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn191', 'cn192', 'cn193'],
            label: t('label.full-assistance-with-dressing'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn201', 'cn202', 'cn203'],
            label: t('label.adjusting-or-tidying-clothes'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
    ],
    careLocationItems: [
      {
        title: t('label.place-for-washing-face'),
        showMode: '0',
        careLocationLabel: [
          {
            valueType: ['cb011'],
            locationValue: { modelValue: '0' },
            label: t('label.sink-area'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb021'],
            locationValue: { modelValue: '0' },
            label: t('label.hall'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb031'],
            locationValue: { modelValue: '0' },
            label: t('label.living-room-excluding-the-bed'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb041'],
            locationValue: { modelValue: '0' },
            label: t('label.bed-and-bedding'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb051', 'cb052Knj'],
            locationValue: { modelValue: '0' },
            label: t('label.other'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
        ],
      },
      {
        title: t('label.facial-cleansing-tools'),
        showMode: '0',
        careLocationLabel: [
          {
            valueType: ['cb061'],
            locationValue: { modelValue: '0' },
            label: t('label.washbasin'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb071'],
            locationValue: { modelValue: '0' },
            label: t('label.hot-towel'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb081', 'cb082Knj'],
            locationValue: { modelValue: '0' },
            label: t('label.aids-for-daily-living'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb091', 'cb092Knj'],
            locationValue: { modelValue: '0' },
            label: t('label.other'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb101', 'cb102Knj'],
            locationValue: { modelValue: '0' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb111', 'cb112Knj'],
            locationValue: { modelValue: '0' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
        ],
      },
      {
        title: t('label.oral-hygiene-tools'),
        showMode: '0',
        careLocationLabel: [
          {
            valueType: ['cb121'],
            locationValue: { modelValue: '0' },
            label: t('label.toothbrush'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb131'],
            locationValue: { modelValue: '0' },
            label: t('label.cotton-swabs-gauze-etc'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb141'],
            locationValue: { modelValue: '0' },
            label: t('label.denture-cleaning-solution'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb151', 'cb152Knj'],
            locationValue: { modelValue: '0' },
            label: t('label.aids-for-daily-living'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb161', 'cb162Knj'],
            locationValue: { modelValue: '0' },
            label: t('label.other'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb171', 'cb172Knj'],
            locationValue: { modelValue: '0' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb181', 'cb182Knj'],
            locationValue: { modelValue: '0' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb191', 'cb192Knj'],
            locationValue: { modelValue: '0' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb201', 'cb202Knj'],
            locationValue: { modelValue: '0' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
        ],
      },
    ],
  } as Or34069Type,
})

/**
 * ロカールOneway
 */
const localOneway = reactive({
  orX0096Oneway: {
    showInputFlg: false,
    tableDisplayFlg: true,
    b1Cd: Or03245Const.DEFAULT.TAB_ID,
  } as OrX00096OnewayType,
  or51105Oneway: {
    sc1Id: '',
    cc1Id: '',
  } as Or51105OnewayType,
  or34069Oneway: {
    showTableBodyFlg: true,
  } as Or34069OnewayType,
})

const or34069 = ref({ uniqueCpId: '', showTableBodyFlg: true })
const or53105 = ref({ uniqueCpId: '' })
const orX0096 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or21814 = ref({ uniqueCpId: '' })

/**************************************************
 * Pinia
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or34069Const.CP_ID(0)]: or34069.value,
  [Or53105Const.CP_ID(0)]: or53105.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [OrX0096Const.CP_ID(1)]: orX0096.value,
})

/**************************************************
 * computed
 **************************************************/

/**
 * ダイアログ表示フラグ
 */
const showDialogOr53105CksFlg1 = computed(() => {
  // Or53105 cks_flg=1 のダイアログ開閉状態
  return Or53105Logic.state.get(or53105.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const showOr21814DialogFlg = computed(() => {
  // ダイアログの開閉フラグ
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 画面データ変更フラグ
 */
const _isEdit = computed(() => {
  const isEditByUniqueCpIds = useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
  return isEditByUniqueCpIds
})

/**************************************************
 * 関数定義
 **************************************************/
/**
 * コントロール初期化
 */
const initControl = () => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      firstBtnLabel: t('btn.yes'),
      firstBtnType: 'normal1',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      dialogTitle: t('label.confirm'),
    },
  })
}

/**
 * 番号設定関数
 *
 * @param index - 変換前のインデックス
 */
function setCircleNumber(index: number) {
  const circleNumbers = [
    '①',
    '②',
    '③',
    '④',
    '⑤',
    '⑥',
    '⑦',
    '⑧',
    '⑨',
    '⑩',
    '⑪',
    '⑫',
    '⑬',
    '⑭',
    '⑮',
    '⑯',
    '⑰',
    '⑱',
    '⑲',
    '⑳',
  ]
  if (index >= 1 && index <= 20) {
    return circleNumbers[index - 1]
  }
}

/**
 * 初期情報データより、テーブルデータを設定する
 *
 * @param tableData - 初期化APIから取得したデータ
 *
 * @param items - テーブルデータ
 */
function initTableData<Label extends CareLabelType | CareLocationLabelType>(
  tableData: (string | undefined)[],
  items: Label
) {
  const getDefaultValue = (index: number, defaultValue = '') => tableData[index] ?? defaultValue

  if (tableData.length === 3) {
    if ('offerValue' in items && 'familyValue' in items && 'planValue' in items) {
      items.offerValue = { modelValue: getDefaultValue(0) }
      items.familyValue = { modelValue: getDefaultValue(1) }
      items.planValue = { modelValue: getDefaultValue(2) }
    }
  } else if (tableData.length === 1) {
    if ('locationValue' in items) {
      items.locationValue = { modelValue: getDefaultValue(0) }
    }
  } else if (tableData.length === 2) {
    if ('locationValue' in items && 'inputShowMode' in items) {
      items.locationValue = { modelValue: getDefaultValue(0) }
      items.inputContent = { value: getDefaultValue(1) }
    }
  }
}

/**
 * 共通情報取得
 */
function getCommonInfo() {
  const commonInfo = TeX0008Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo = commonInfo
    screenUpdateKbn.value = commonInfo.updateKbn ?? ''
    // 更新区分クリア
    if (commonInfo.updateKbn === UPDATE_KBN.DELETE) {
      localOneway.orX0096Oneway.tableDisplayFlg = false
    } else {
      localOneway.orX0096Oneway.tableDisplayFlg = true
    }
  }
}

/**
 * 複写モード共通情報取得
 */
const getDuplicateCommonInfo = () => {
  const commonInfo = Or59423Logic.data.get(props.parentUniqueCpId + Or59423Const.CP_ID(0))
  if (commonInfo) {
    local.commonInfo = {
      ninteiFormF: commonInfo.duplicateInfo?.ninteiFormF,
      activeTabId: commonInfo.duplicateInfo?.activeTabId,
      jigyoId: commonInfo.duplicateInfo?.jigyoId,
      houjinId: commonInfo.duplicateInfo?.houjinId,
      shisetuId: commonInfo.duplicateInfo?.shisetuId,
      userId: commonInfo.duplicateInfo?.userId,
      syubetsuId: commonInfo.duplicateInfo?.syubetsuId,
      createYmd: commonInfo.duplicateInfo?.createYmd,
      historyUpdateKbn: commonInfo.duplicateInfo?.historyUpdateKbn,
      historyModifiedCnt: commonInfo.duplicateInfo?.historyModifiedCnt,
      sc1Id: commonInfo.duplicateInfo?.sc1Id,
      recId: commonInfo.duplicateInfo?.recId,
      cc1Id: commonInfo.duplicateInfo?.cc1Id,
      createUserId: commonInfo.duplicateInfo?.createUserId,
      svJigyoId: commonInfo.duplicateInfo?.svJigyoId,
      updateKbn: commonInfo.duplicateInfo?.updateKbn,
      planPeriodFlg: commonInfo.duplicateInfo?.planPeriodFlg,
    }
  }
}

/**
 * 親画面のstateを変更する
 *
 * @param state - state
 */
const setTeX0008State = (state: TeX0008StateType) => {
  TeX0008Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

/**
 * 初期情報取得
 */
async function getInitDataInfo() {
  try {
    const inputData: AssessmentIncludeWashRoomSelectInEntity = {
      cc1Id: local.commonInfo.cc1Id,
      sc1Id: local.commonInfo.sc1Id,
    }

    // 初期情報取得APIを呼び出す
    const resData: BaseResponseBody<AssessmentIncludeWashRoomSelectOutEntity> =
      await ScreenRepository.select('assessmentComprehensiveWashroomInitSelect', inputData)
    // 返却値チェック
    if (resData.data) {
      processInfoData(resData, true)
      updateNum = resData.data.hcc24Info.modifiedCnt ?? ''
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 問題点初期情報取得
 */
const getProblemDotSolutionEtcInfoData = async () => {
  try {
    const inputData: assessmentComprehensiveQuestionInEntity = {
      cc1Id: local.commonInfo?.cc1Id,
      sc1Id: local.commonInfo?.sc1Id,
      // タブID：「4:」
      typeId: Or03245Const.DEFAULT.TAB_ID,
    }

    const resData: BaseResponseBody<assessmentComprehensiveQuestionOutWebEntity> =
      await ScreenRepository.select('problemDotSolutionEtcInfoSelect', inputData)
    if (resData.data) {
      processParentData(resData.data.problemDotSolutionEtcInfo)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 更新回数チェック
 *
 * @param isHistory - 履歴更新区分
 */
const getRequestScreenUpdataKbn = (isHistory: boolean) => {
  // 更新回数が不存在する場合、更新区分を新規にする
  if (updateNum === '') return UPDATE_KBN.CREATE
  if (isHistory) {
    return local.commonInfo.historyUpdateKbn === UPDATE_KBN.NONE
      ? UPDATE_KBN.UPDATE
      : local.commonInfo.historyUpdateKbn
  }
  return screenUpdateKbn.value === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : screenUpdateKbn.value
}

/**
 * 複写情報取得
 */
const getDuplicateDataInfo = async () => {
  const inputData: AssessmentIncludeWashRoomSelectInEntity = {
    cc1Id: local.commonInfo.duplicateCareCheckId,
    sc1Id: local.commonInfo.duplicatePlanId,
  }
  // 初期情報取得APIを呼び出す
  const resData: BaseResponseBody<AssessmentIncludeWashRoomSelectOutEntity> =
    await ScreenRepository.select('assessmentComprehensiveWashroomInitSelect', inputData)
  // 返却値チェック
  if (resData.data) {
    processInfoData(resData, false)
  }
}

/**
 * 複写情報取得「問題点情報」
 */
const getDuplicateProblemDotSolutionEtcInfoData = async () => {
  try {
    const inputData: assessmentComprehensiveQuestionInEntity = {
      cc1Id: local.commonInfo?.duplicateCareCheckId ?? '',
      sc1Id: local.commonInfo?.duplicatePlanId ?? '',
      // タブID：「1：洗面」
      typeId: Or03245Const.DEFAULT.TAB_ID,
    }

    const resData: BaseResponseBody<assessmentComprehensiveQuestionOutWebEntity> =
      await ScreenRepository.select('problemDotSolutionEtcInfoSelect', inputData)
    if (resData.data) {
      processParentData(resData.data.problemDotSolutionEtcInfo)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * API返却値処理
 *
 * @param resData - API返却値
 *
 * @param updateFlg - 更新回数フラグ
 */
const processInfoData = (
  resData: BaseResponseBody<AssessmentIncludeWashRoomSelectOutEntity>,
  updateFlg: boolean
) => {
  if (!resData.data) return
  // 複写モードの場合、返却値を保存する
  if (props.onewayModelValue.screenMode === TeX0008Const.DEFAULT.MODE_COPY) {
    Or03245Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        comprehensiveQuestionInfo: props.onewayModelValue.comprehensiveQuestionInfo,
        result: resData,
      },
    })
  }
  // セレクト選択肢設定
  const { careOfferLocationInfoList, scheduleInfoList, familyInfoList, offerInfoList } =
    resData.data.markInfo
  localOneway.or34069Oneway.careOfferMarkMeaning = offerInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.careFamilyMarkMeaning = familyInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.carePlanMarkMeaning = scheduleInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.careLocationMarkMeaning = careOfferLocationInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })

  // 複数回の更新による無限再帰を防ぐために、データをディープコピーし、以下の処理は別データに移管する
  tableData.value = cloneDeep(local.or34096)
  const {
    cn011,
    cn012,
    cn013,
    cn021,
    cn022,
    cn023,
    cn031,
    cn032,
    cn033,
    cn041,
    cn042,
    cn043,
    cn051,
    cn052,
    cn053,
    cn061,
    cn062,
    cn063,
    cn071,
    cn072,
    cn073,
    cn081,
    cn082,
    cn083,
    cn091,
    cn092,
    cn093,
    cn101,
    cn102,
    cn103,
    cn111,
    cn112,
    cn113,
    cn121,
    cn122,
    cn123,
    cn131,
    cn132,
    cn133,
    cn141,
    cn142,
    cn143,
    cn151,
    cn152,
    cn153,
    cn161,
    cn162,
    cn163,
    cn171,
    cn172,
    cn173,
    cn181,
    cn182,
    cn183,
    cn191,
    cn192,
    cn193,
    cn201,
    cn202,
    cn203,
    cb011,
    cb021,
    cb031,
    cb041,
    cb051,
    cb052Knj,
    cb061,
    cb071,
    cb081,
    cb082Knj,
    cb091,
    cb092Knj,
    cb101,
    cb102Knj,
    cb111,
    cb112Knj,
    cb121,
    cb131,
    cb141,
    cb151,
    cb152Knj,
    cb161,
    cb162Knj,
    cb171,
    cb172Knj,
    cb181,
    cb182Knj,
    cb191,
    cb192Knj,
    cb201,
    cb202Knj,
    modifiedCnt,
  } = resData.data.hcc24Info
  /**
   * 洗面
   */
  // 洗面所までの誘導
  initTableData([cn011, cn012, cn013], tableData.value.careItems[0].careLabel[0])
  // 洗面動作の指示
  initTableData([cn021, cn022, cn023], tableData.value.careItems[0].careLabel[1])
  // 洗面一部介助
  initTableData([cn031, cn032, cn033], tableData.value.careItems[0].careLabel[2])
  // 洗面全介助
  initTableData([cn041, cn042, cn043], tableData.value.careItems[0].careLabel[3])
  /**
   * 口腔清潔
   */
  // 口腔清潔の必要物品準備
  initTableData([cn051, cn052, cn053], tableData.value.careItems[1].careLabel[0])
  // 口腔清潔の使用物品後始末
  initTableData([cn061, cn062, cn063], tableData.value.careItems[1].careLabel[1])
  // 口腔清潔（歯みがき等）
  initTableData([cn071, cn072, cn073], tableData.value.careItems[1].careLabel[2])
  // うがいの介助
  initTableData([cn081, cn082, cn083], tableData.value.careItems[1].careLabel[3])
  // 入れ歯の手入れ
  initTableData([cn091, cn092, cn093], tableData.value.careItems[1].careLabel[4])
  // 口唇の乾燥を防ぐ
  initTableData([cn101, cn102, cn103], tableData.value.careItems[1].careLabel[5])
  /**
   * 整容
   */
  // 結髪、整髪
  initTableData([cn111, cn112, cn113], tableData.value.careItems[2].careLabel[0])
  // 散髪
  initTableData([cn121, cn122, cn123], tableData.value.careItems[2].careLabel[1])
  // 爪切り
  initTableData([cn131, cn132, cn133], tableData.value.careItems[2].careLabel[2])
  // 髭剃り
  initTableData([cn141, cn142, cn143], tableData.value.careItems[2].careLabel[3])
  // 耳掃除
  initTableData([cn151, cn152, cn153], tableData.value.careItems[2].careLabel[4])

  /**
   * 更衣
   */
  // 衣服・靴下・靴の準備
  initTableData([cn161, cn162, cn163], tableData.value.careItems[3].careLabel[0])
  // 更衣動作の見守り、指示
  initTableData([cn171, cn172, cn173], tableData.value.careItems[3].careLabel[1])
  // 更衣動作の一部介助
  initTableData([cn181, cn182, cn183], tableData.value.careItems[3].careLabel[2])
  // 更衣動作の全介助
  initTableData([cn191, cn192, cn193], tableData.value.careItems[3].careLabel[3])
  // 衣服を整える
  initTableData([cn201, cn202, cn203], tableData.value.careItems[3].careLabel[4])

  /**
   * 洗面の場所
   */
  // 洗面所
  initTableData([cb011], tableData.value.careLocationItems[0].careLocationLabel[0])
  // ホール
  initTableData([cb021], tableData.value.careLocationItems[0].careLocationLabel[1])
  // 居室（ベッド以外）
  initTableData([cb031], tableData.value.careLocationItems[0].careLocationLabel[2])
  // ベッド・布団
  initTableData([cb041], tableData.value.careLocationItems[0].careLocationLabel[3])
  // その他
  initTableData([cb051, cb052Knj], tableData.value.careLocationItems[0].careLocationLabel[4])

  /**
   * 洗面用具
   */
  // 洗面器
  initTableData([cb061], tableData.value.careLocationItems[1].careLocationLabel[0])
  // 蒸しタオル
  initTableData([cb071], tableData.value.careLocationItems[1].careLocationLabel[1])
  // 自助具
  initTableData([cb081, cb082Knj], tableData.value.careLocationItems[1].careLocationLabel[2])
  // その他
  initTableData([cb091, cb092Knj], tableData.value.careLocationItems[1].careLocationLabel[3])
  //
  initTableData([cb101, cb102Knj], tableData.value.careLocationItems[1].careLocationLabel[4])
  //
  initTableData([cb111, cb112Knj], tableData.value.careLocationItems[1].careLocationLabel[5])

  /**
   * 口腔清潔用具
   */
  // 歯ブラシ
  initTableData([cb121], tableData.value.careLocationItems[2].careLocationLabel[0])
  // 綿棒・ガーゼ等
  initTableData([cb131], tableData.value.careLocationItems[2].careLocationLabel[1])
  // 義歯洗浄剤
  initTableData([cb141], tableData.value.careLocationItems[2].careLocationLabel[2])
  // 自助具
  initTableData([cb151, cb152Knj], tableData.value.careLocationItems[2].careLocationLabel[3])
  // その他
  initTableData([cb161, cb162Knj], tableData.value.careLocationItems[2].careLocationLabel[4])
  //
  initTableData([cb171, cb172Knj], tableData.value.careLocationItems[2].careLocationLabel[5])
  //
  initTableData([cb181, cb182Knj], tableData.value.careLocationItems[2].careLocationLabel[6])
  //
  initTableData([cb191, cb192Knj], tableData.value.careLocationItems[2].careLocationLabel[7])
  //
  initTableData([cb201, cb202Knj], tableData.value.careLocationItems[2].careLocationLabel[8])

  // 処理済みのデータを画面に表示する
  local.or34096 = tableData.value
  // 複写再発火の場合は、サブ情報の更新回数は上書きしない
  if (updateFlg) {
    updateNum = modifiedCnt ?? ''
  }
}

/**
 * 新規処理
 */
const createNew = () => {
  local.or34096.careItems.forEach((item) => {
    // 更新区分を新規にする
    screenUpdateKbn.value = UPDATE_KBN.CREATE
    item.careLabel.forEach((sItem) => {
      sItem.planValue = { modelValue: '' }
      sItem.offerValue = { modelValue: '' }
      sItem.familyValue = { modelValue: '' }
      if (sItem.label === '') {
        sItem.inputContent = {
          value: '',
        }
      }
    })
  })
  local.or34096.careLocationItems.forEach((item) => {
    item.careLocationLabel.forEach((sItem) => {
      sItem.locationValue = { modelValue: '' }
      if (
        sItem.label === '' ||
        sItem.inputShowMode.appendInput === Or03245Const.DEFAULT.INPUT_DISPLAY_FLG_LAST ||
        sItem.inputShowMode.appendInput === Or03245Const.DEFAULT.INPUT_DISPLAY_FLG_UNIT
      ) {
        sItem.inputContent = {
          value: '',
        }
      }
    })
  })
  // 各ラベルをクリア
  const newQuestionList = local.orX0096.listSection.map((item) => {
    return {
      ...item,
      isHaveIssuesFlg: false,
      isPlanningFlg: false,
    }
  })
  // 要介護課題等々クリア
  local.orX0096 = {
    listSection: newQuestionList,
    concreteCareItemList: [],
    concreteContentList: [],
  }
  // 各プルダウン選択肢リストクリア
  localOneway.or34069Oneway = {
    carePlanMarkMeaning: [],
    careOfferMarkMeaning: [],
    careFamilyMarkMeaning: [],
    careLocationMarkMeaning: [],
    showTableBodyFlg: true,
  }
}

/**
 * 問題点情報の共通処理
 *
 * @param comprehensiveQuestionInfo - API返却値
 */
function processParentData(
  comprehensiveQuestionInfo: assessmentComprehensiveQuestionOutWebEntity['problemDotSolutionEtcInfo']
) {
  localOneway.orX0096Oneway.officeId = local.commonInfo.svJigyoId ?? ''
  // 本画面のデータを絞り出す
  // 対応するケア項目リスト
  const concreteCareItemList: ConcreteCareItemType[] = comprehensiveQuestionInfo.careItemList
    .filter((item) => item.b1Cd === Or03245Const.DEFAULT.TAB_ID)
    .map((item) => {
      return {
        key: item.cc33Id,
        content: item.memoKnj,
        dmyCc32Id: item.dmyCc32Id,
        modifiedCnt: item.modifiedCnt,
        cc33Id: item.cc33Id,
        b1Cd: item.b1Cd,
        cc32Id: item.cc32Id,
        seq: item.seq,
        ci2Id: item.ci2Id,
        updateKbn: '',
      }
    })
  local.orX0096.concreteCareItemList = concreteCareItemList
  // 具体的内容
  const concreteContentList: ConcreteContentType[] =
    comprehensiveQuestionInfo.concreteContentsInfoList
      .filter((item) => item.b1Cd === Or03245Const.DEFAULT.TAB_ID)
      .map((item): ConcreteContentType => {
        return {
          key: item.cc32Id,
          correspondenceKeys: [],
          content: item.memoKnj,
          cc32Id: item.cc32Id,
          modifiedCnt: item.modifiedCnt,
          juni: item.juni,
          b1Cd: item.b1Cd,
          seq: item.seq,
          cc32Type: item.cc32Type,
          ci1Id: item.ci1Id,
          dmyB4Cd: item.dmyB4Cd,
          b4Cd: item.b4Cd,
          number: '',
          updateKbn: '',
        }
      })
  // 番号リストを作成
  const numberList = comprehensiveQuestionInfo.problemDotNumberInfoList
    .filter((item) => item.b1Cd === Or03245Const.DEFAULT.TAB_ID)
    .map((item) => {
      return {
        cc32Id: item.cc32Id,
        b4Cd: item.b4Cd,
        seq: item.seq,
        modifiedCnt: item.modifiedCnt1,
        cc34Id: item.cc34Id,
        b1Cd: item.b1Cd,
        modifiedCnt1: item.modifiedCnt1,
      }
    })
  if (concreteContentList.length > 0) {
    concreteContentList.forEach((item) => {
      const findedItem = numberList.filter((sItem) => sItem.cc32Id === item.cc32Id)
      if (findedItem) {
        item.correspondenceKeys = findedItem
        item.number = findedItem
          .map((item) => setCircleNumber(parseInt(item.b4Cd) - Or03245Const.DEFAULT.TAB_SUB_ID))
          .join('')
      }
    })
  }

  local.orX0096.concreteContentList = concreteContentList
  /**
   * 問題点リスト
   */
  const questionList = comprehensiveQuestionInfo.problemDotSolutionInfoList.filter(
    (item) => item.b1Cd === Or03245Const.DEFAULT.TAB_ID
  )
  local.orX0096.listSection = local.orX0096.listSection.map((item) => {
    const findedItem = questionList.find((sItem) => sItem.b4Cd === item.b4cd)
    if (findedItem) {
      return {
        ...item,
        cc31Id: findedItem?.cc31Id ?? '',
        isPlanningFlg: findedItem.f1 === Or03245Const.DEFAULT.API_RESULT_CHECKON,
        isHaveIssuesFlg: findedItem.f2 === Or03245Const.DEFAULT.API_RESULT_CHECKON,
        modifiedCnt: findedItem?.modifiedCnt,
      }
    } else {
      return {
        ...item,
        isPlanningFlg: false,
        isHaveIssuesFlg: false,
        cc31Id: '',
        modifiedCnt: '0',
      }
    }
  })
  dotNumberList = comprehensiveQuestionInfo.problemDotNumberInfoList
  localOneway.orX0096Oneway.maxCount = parseInt(comprehensiveQuestionInfo.cc32IdMax)
  // 洗面画面
  local.orX0096.thingsToKeepInMindContentsInfo =
    comprehensiveQuestionInfo.thingsToKeepInMindContentsInfo
}

/**
 * tabを制御
 *
 * @param component - コンポーネント
 */
const disableTab = (component: HTMLElement) => {
  const focusableSelectors = [
    'a[href]',
    'area[href]',
    'input',
    'select',
    'textarea',
    'button',
    'iframe',
    '[tabindex]',
    '[contenteditable]',
  ]

  const focusables = component.querySelectorAll(focusableSelectors.join(','))
  focusables.forEach((el) => {
    el.setAttribute('tabindex', '-1')
  })
}

/**
 * APIリクエストパラメータ作成
 *
 * @param request - リクエスト
 */
const createRequest = (request: string | undefined) => {
  if (!request) return '0'
  if (request === '') {
    return '0'
  }
  if (screenUpdateKbn.value === UPDATE_KBN.CREATE) {
    return '0'
  }
  return request
}

/**
 * データの保存処理
 *
 * @param arr - 操作したデータ
 *
 * @param careList - 各ケア内容使用したラベル
 */
function handleCareItems(arr: string[], careList: CareItems[]) {
  const obj: Record<string, unknown> = {}
  careList.forEach((item) => {
    item.careLabel.forEach((itm) => {
      itm.valueType?.forEach((i: string, x: number) => {
        if (x < arr.length) {
          // 断言处理
          const key = arr[x] as keyof typeof itm
          if (key in itm) {
            obj[i] = (itm[key] as Mo00040Type).modelValue
          } else {
            obj[i] = itm.inputContent?.value
          }
        } else {
          obj[i] = itm.inputContent?.value
        }
      })
    })
  })
  return obj
}
/**
 * データの保存処理
 *
 * @param arr - 操作したデータ
 *
 * @param careList - 各ケア内容使用したラベル
 */
function handleCareLocationItems(arr: string[], careList: careLocationItems[]) {
  const obj: Record<string, unknown> = {}
  careList.forEach((item) => {
    item.careLocationLabel.forEach((itm) => {
      itm.valueType?.forEach((i: string, x: number) => {
        if (x < arr.length) {
          const key = arr[x] as keyof typeof itm
          if (key in itm) {
            obj[i] = (itm[key] as Mo00040Type).modelValue
          } else {
            obj[i] = itm.inputContent?.value
          }
        } else {
          obj[i] = itm.inputContent?.value
        }
      })
    })
  })
  return obj
}

/**
 * 保存処理
 */
const _userSave = async () => {
  // 保存前のチェック
  if (
    !_isEdit.value &&
    screenUpdateKbn.value !== UPDATE_KBN.DELETE &&
    screenUpdateKbn.value !== UPDATE_KBN.CREATE
  ) {
    setShowDialog(t('message.i-cmn-21800'))
    return
  }
  // 共通情報.e-文書法対象機能の電子ファイル保存設定区分が「true：適用する」の場合 Todo
  if (!systemCommonsStore) return
  try {
    isLoading.value = true
    // 子コンポーネントからデータを一括取得する
    const childrenTableCpBinds = getChildCpBinds(props.uniqueCpId, {
      [Or34069Const.CP_ID(0)]: { cpPath: Or34069Const.CP_ID(0), twoWayFlg: true },
      [OrX0096Const.CP_ID(1)]: { cpPath: OrX0096Const.CP_ID(1), twoWayFlg: true },
    })
    // ケアの内容を取得する
    const careItems = (childrenTableCpBinds[Or34069Const.CP_ID(0)].twoWayBind?.value as Or34069Type)
      .careItems
    // ケアの提供場所を取得する
    const careLocationItems = (
      childrenTableCpBinds[Or34069Const.CP_ID(0)].twoWayBind?.value as Or34069Type
    ).careLocationItems
    // 要介護者などの健康上や生活上の問題点及び解決すべき課題等を取得する
    const careRecipientHealthAndLifeIssues = childrenTableCpBinds[OrX0096Const.CP_ID(1)].twoWayBind
      ?.value as OrX0096Type
    const inputData: AssessmentIncludeWashRoomUpdateInEntity = {
      cc1Id: createRequest(local.commonInfo.cc1Id),
      sc1Id: local.commonInfo.sc1Id ?? '',
      houjinId: createRequest(local.commonInfo.houjinId),
      shisetuId: local.commonInfo.shisetuId ?? '',
      svJigyoId: local.commonInfo.svJigyoId ?? '',
      userId: local.commonInfo.userId ?? '',
      syubetsuId: local.commonInfo.syubetsuId ?? '',
      createYmd: local.commonInfo.createYmd ?? '',
      updateKbn: getRequestScreenUpdataKbn(false),
      historyUpdateKbn: getRequestScreenUpdataKbn(true),
      historyModifiedCnt: createRequest(local.commonInfo.historyModifiedCnt),
      shokuId:
        (local.commonInfo.createUserId ?? '') || (systemCommonsStore.getStaffId ?? '') || '1',
      typeId: Or03245Const.DEFAULT.TAB_ID,
    }
    const hcc24Info: AssessmentIncludeWashRoomUpdateInEntity['hcc24Info'] = {
      ...handleCareItems(['offerValue', 'familyValue', 'planValue'], careItems),
      ...handleCareLocationItems(['locationValue'], careLocationItems),
      modifiedCnt: updateNum === '' ? '0' : updateNum,
    } as unknown as Hcc24Info
    inputData.hcc24Info = hcc24Info

    // 問題点解決情報リスト
    const issuseList: AssessmentIncludeWashRoomUpdateInEntity['problemDotSolutionInfoList'] =
      careRecipientHealthAndLifeIssues.listSection.map((item) => {
        return {
          cc31Id: item.cc31Id,
          b1Cd: Or03245Const.DEFAULT.TAB_ID,
          b4Cd: item.b4cd,
          f1: item.isPlanningFlg
            ? Or03245Const.DEFAULT.API_RESULT_CHECKON
            : Or03245Const.DEFAULT.DATA_DEFAULT,
          f2: item.isHaveIssuesFlg
            ? Or03245Const.DEFAULT.API_RESULT_CHECKON
            : Or03245Const.DEFAULT.DATA_DEFAULT,
          modifiedCnt: item.modifiedCnt,
        }
      })
    inputData.problemDotSolutionInfoList = issuseList
    // 具体内容情報リスト
    const concreteContentsInfoList: AssessmentIncludeWashRoomUpdateInEntity['concreteContentsInfoList'] =
      careRecipientHealthAndLifeIssues.concreteContentList?.map((item, index) => {
        return {
          // 更新区分チェック
          cc32Id: item.updateKbn === UPDATE_KBN.CREATE ? '0' : item.cc32Id,
          // 更新区分チェック、新規の場合は、画面記録IDを設定する
          cc32IdRecord: item.updateKbn === UPDATE_KBN.CREATE ? item.cc32Id : '',
          b1Cd: Or03245Const.DEFAULT.TAB_ID,
          memoKnj: item.content,
          seq: (index + 1).toString(),
          juni: item.juni as string | undefined,
          cc32Type: item.cc32Type,
          ci1Id: item.ci1Id,
          dmyB4Cd: item.correspondenceKeys.map((item) => {
            return {
              b4Cd: item.b4Cd,
            }
          }),
          updateKbn: item.updateKbn === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : item.updateKbn,
          modifiedCnt: item.modifiedCnt,
        }
      })
    inputData.concreteContentsInfoList = concreteContentsInfoList
    // ケア情報リスト
    const careItemList: AssessmentIncludeWashRoomUpdateInEntity['careItemList'] =
      careRecipientHealthAndLifeIssues.concreteCareItemList?.map((item, index) => {
        return {
          // 該当情報が新規の場合、画面に表示したIDを削除する
          cc33Id: item.updateKbn === UPDATE_KBN.CREATE ? '0' : item.cc33Id,
          b1Cd: Or03245Const.DEFAULT.TAB_ID,
          memoKnj: item.content,
          cc32Id: item.cc32Id,
          seq: (index + 1).toString(),
          ci2Id: item.ci2Id,
          dmyCc32Id: item.dmyCc32Id,
          updateKbn: item.updateKbn === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : item.updateKbn,
          modifiedCnt: item.modifiedCnt,
        }
      })
    inputData.careItemList = careItemList
    // 問題点番号リスト
    if (screenUpdateKbn.value !== UPDATE_KBN.CREATE) {
      inputData.problemDotNumberInfoList = dotNumberList
    } else {
      inputData.problemDotNumberInfoList = undefined
    }

    const resData: AssessmentIncludeWashRoomUpdateOutEntity = await ScreenRepository.update(
      'assessmentComprehensiveWashroomUpdate',
      inputData
    )
    if (resData.data) {
      // 保存処理返却値を親画面Piniaに設定する
      TeX0008Logic.data.set({
        uniqueCpId: props.parentUniqueCpId,
        value: {
          ...local.commonInfo,
          childrenScreenApiResult: {
            sc1Id: resData.data.sc1Id,
            cc1Id: resData.data.cc1Id,
          },
        },
      })
      setTeX0008State({ saveCompletedState: true })
      // 更新区分クリア
      screenUpdateKbn.value = UPDATE_KBN.NONE
    }
  } finally {
    isLoading.value = false
  }
}

/**
 * 削除処理を行う
 */
const userDelete = () => {
  // 更新区分を D に設定する
  screenUpdateKbn.value = UPDATE_KBN.DELETE
  // テーブルボディーを非表示にする
  localOneway.or34069Oneway.showTableBodyFlg = false
  localOneway.orX0096Oneway.tableDisplayFlg = false
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */

const setShowDialog = (paramDialogText: string) => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
}

const reload = async () => {
  // 全表示フラグをtrueにする
  localOneway.or34069Oneway.showTableBodyFlg = true
  localOneway.orX0096Oneway.tableDisplayFlg = true
  // 更新区分クリア
  screenUpdateKbn.value = UPDATE_KBN.NONE
  isLoading.value = true
  await getInitDataInfo()
  await getProblemDotSolutionEtcInfoData()
  isLoading.value = false
}

/**
 * 初期化処理
 */
onMounted(() => {
  initControl()
})

/**************************************************
 * watch関数
 **************************************************/

/** 画面イベント監視 */
watch(
  () => TeX0008Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    // 初期情報取得
    getCommonInfo()

    // 本画面ではない場所、処理を中断する
    if (local.commonInfo.activeTabId !== Or03245Const.DEFAULT.TAB_ID) {
      return
    }
    /** 再表示処理 */
    if (newValue?.isRefresh) {
      await reload()
    }
    /** 保存イベント処理 */
    if (newValue?.saveEventFlg) {
      await _userSave()
    }

    /** 削除イベント処理 */
    if (newValue?.deleteEventFlg) {
      userDelete()
    }

    // 新規処理
    if (newValue?.createEventFlg) {
      createNew()
    }

    /** 作成日変更処理 */
    if (newValue?.isCreateDateChanged) {
      return
    }

    /** お気に入りイベント処理 */
    if (newValue?.favoriteEventFlg) {
      return
    }

    /** 複写イベント処理 */
    if (newValue?.copyEventFlg) {
      isLoading.value = true
      await getDuplicateDataInfo()
      await getDuplicateProblemDotSolutionEtcInfoData()
      isLoading.value = false
      return
    }

    /** 印刷イベント処理 */
    if (newValue?.printEventFlg) {
      return
    }

    /** マスタ他イベント処理 */
    if (newValue?.masterEventFlg) {
      return
    }

    /** 優先順位表示フラグ */
    if (newValue?.priorityOrderEventFlg) {
      return
    }
  },
  { deep: true, immediate: true }
)

/**
 * 複写モードイベント監視
 */
watch(
  () => Or59423Logic.event.get(props.parentUniqueCpId + Or59423Const.CP_ID(0)),
  async (newValue) => {
    if (!newValue) return

    // 複写モード共通情報取得
    getDuplicateCommonInfo()

    // 本画面ではない場合、処理終了
    if (local.commonInfo.activeTabId !== Or03245Const.DEFAULT.TAB_ID) {
      return
    }

    if (newValue.reloadEvent) {
      isLoading.value = true
      await getInitDataInfo()
      await getProblemDotSolutionEtcInfoData()
      // 全処理済み、タブ変更を禁止する
      if (props.onewayModelValue.screenMode === Or03245Const.DEFAULT.SCREEN_DIAPLAY_MODE_COPY) {
        if (componentRef.value) {
          disableTab(componentRef.value)
        }
      }
      isLoading.value = false
    }
  }
)
</script>

<template>
  <div
    ref="componentRef"
    class="or03245Wrapper"
  >
    <v-overlay
      :model-value="isLoading"
      :persistent="false"
      class="align-center justify-center"
      ><v-progress-circular
        indeterminate
        color="primary"
      ></v-progress-circular
    ></v-overlay>
    <c-v-row no-gutters>
      <g-custom-or34069
        v-bind="or34069"
        :oneway-model-value="localOneway.or34069Oneway"
        :model-value="local.or34096"
      />
      <div
        class="w-100 mt-2"
        style="height: 1px; background-color: #cccccc"
      ></div>
      <g-custom-or-x0096
        v-bind="orX0096"
        :oneway-model-value="localOneway.orX0096Oneway"
        :model-value="local.orX0096"
      />
    </c-v-row>
    <g-custom-or-53105
      v-if="showDialogOr53105CksFlg1"
      v-bind="or53105"
    />
    <g-base-or-21814
      v-if="showOr21814DialogFlg"
      v-bind="or21814"
    />
  </div>
</template>

<style scoped lang="scss">
.or03249Wrapper {
  .pageTitle {
    border: 1px rgb(var(--v-theme-black-200)) solid;
    background-color: rgb(var(--v-theme-black-100)) !important;
  }
  .titilLabel {
    font-weight: bold;
    font-size: 16px;
    padding: 8px;
  }
}
</style>
