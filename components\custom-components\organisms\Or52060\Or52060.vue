<script setup lang="ts">
/**
 * Or52060:有機体:印刷設定
 * GUI00842_印刷設定
 *
 * @description
 * GUI00842_印刷設定
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed, onUnmounted, nextTick } from 'vue'
import { isEqual } from 'lodash'
import { OrX0128Const } from '../OrX0128/OrX0128.constants'
import { OrX0128Logic } from '../OrX0128/OrX0128.logic'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import { Or52060Const } from './Or52060.constants'
import type { Or52060Param, Or52060StateType } from './Or52060.type'
import {
  useSetupChildProps,
  useScreenOneWayBind,
  useSystemCommonsStore,
  useNuxtApp,
} from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '@/types/business/components/Mo01338Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00020Type, Mo00020OnewayType } from '@/types/business/components/Mo00020Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type {
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CustomClass } from '~/types/CustomClassType'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { ResBodyStatusCode } from '~/constants/api-constants'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type {
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsPrtNoChangeUpdateEntity'
import type {
  IFreeAssessmentFacePrintSettingsUpdateInEntity,
  IFreeAssessmentFacePrintSettingsUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsUpdateEntity'
import { reportOutputType, useReportUtils } from '~/utils/useReportUtils'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type {
  CareCheckTableItemSelectInEntity,
  OutputLedgerPrintEntity,
} from '~/repositories/cmn/entities/CareCheckTableItemSelectEntity'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type {
  PeriodHistoryEntity,
  AssessmentComprehensivePrintSettingsInitialSelectInEntity,
  AssessmentComprehensivePrintSettingsInitialSelectOutEntity,
  AssessmentComprehensivePrintSettinguserSwitchingSelectInEntity,
  AssessmentComprehensivePrintSettinguserSwitchingSelectOutEntity,
  SysIniInfoEntity,
  PrtEntity,
  UserEntity,
  AssessmentComprehensivePrintSettingsHistorySelectOutEntity,
  AssessmentComprehensivePrintSettingsHistorySelectInEntity,
  AssessmentComprehensiveLetterSizeUpdateInEntity,
  AssessmentComprehensiveLetterSizeUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentComprehensivePrintSettingsInitialSelectEntity'
import type {
  OrX0128OnewayType,
  OrX0128Items,
  OrX0128Headers,
} from '~/types/cmn/business/components/OrX0128Type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import { useCmnCom } from '@/utils/useCmnCom'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { SYS_RYAKU } from '~/constants/constants'
import { DIALOG_BTN, SPACE_WAVE, SPACE_SPLIT_COLON } from '~/constants/classification-constants'

/** 国際化 */
const { t } = useI18n()
/** 共通メソッド */
const { reportOutput } = useReportUtils()
/** システムstore */
const systemCommonsStore = useSystemCommonsStore()
const $log = useNuxtApp().$log as DebugLogPluginInterface

// route共有情報
const cmnRouteCom = useCmnRouteCom()

// 画面ID
const screenId = 'GUI00842'
// ルーティング
const routing = 'GUI00842/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: ''
}
/** props取得 */
const props = defineProps<Props>()

// 子コンポーネント用変数
const or21813 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })
const orX0117 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orX0145 = ref({ uniqueCpId: '' })

/** 初期化フラグ */
const initFlg = ref<boolean>(false)

/** 帳票情報 */
const ledgerInfo = {
  /**
   * 選択された帳票のプロファイル
   */
  profile: '',
  /**
   * 履歴ID
   */
  assId: '',
  /**
   * 出力帳票名一覧に選択行番号
   */
  index: '',
  /**
   * システムINI情報
   */
  sysIniInfo: {} as SysIniInfoEntity,
  /**
   * 文字サイズ更新回数
   */
  letterModifiedCnt: '',
  /**
   * 出力帳票ID
   */
  reportId: '',
  /**
   * 利用者ID
   */
  userId: '',
  /**
   * 利用者名
   */
  userName: '',
  /**
   * セクション番号
   */
  sectionNo: '',
  /**
   * 選択した利用者ID
   */
  selectedUserId: '',
  /**
   * 印刷設定情報リスト
   */
  prtList: [] as PrtEntity[],
  /**
   * 利用者リスト
   */
  userList: [] as UserEntity[],
  /**
   * 履歴データ
   */
  historyList: [] as OrX0128Items[],
}

/** Oneway */
const localOneway = reactive({
  /** 親画面の情報 */
  or52060Param: {
    processYmd: '',
    basicDate: '',
    sectionName: '',
    shisetuId: '',
    svJigyoId: '',
    shokuId: '',
    userId: '',
    assId: '',
    tantoId: '',
    prtNo: '',
    svJigyoKnj: '',
    focusSettingInitial: [] as string[],
    selectedUserCounter: '',
  },
  kikanFlg: '',
  /** 共通情報 */
  commonInfo: {
    /** システムコード：共通情報.システムコード */
    sysCd: systemCommonsStore.getSystemCode ?? '',
    /** 法人ID：共通情報.法人ID  */
    houjinId: systemCommonsStore.getHoujinId ?? '',
    /** 職員ID：共通情報.職員ID  */
    shokuId: systemCommonsStore.getStaffId ?? '',
    /** 種別ID：共通情報.種別ID  */
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    /** 適用事業所IDリスト：共通情報.適用事業所IDリスト  */
    svJigyoIdList: systemCommonsStore.getSvJigyoIdList ?? [],
    /** 共通情報.担当ケアマネ設定フラグ  */
    kkjTantoFlg: cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? '',
  },
  /**
   * 初期化履歴データの一時保存
   */
  initPeriodHistoryList: [] as PeriodHistoryEntity[],
  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  /**
   * 基本設定
   */
  mo01338OneWayBasicSettings: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * タイトル
   */
  mo00615OneWayTitle: {
    itemLabel: t('label.title'),
    itemLabelFontWeight: 'normal',
    showItemLabel: false,
    customClass: {
      itemStyle: 'display: none',
    } as CustomClass,
  } as Mo00615OnewayType,
  /**
   * タイトルテキストフィールド
   */
  mo00045OneWay: {
    showItemLabel: false,
    width: '296',
    disabled: true,
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo00045OnewayType,
  /**
   * 日付印刷区分
   */
  mo00039OneWayDatePrint: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 印刷オプション
   */
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 指定日
   */
  mo00020OneWayDesignation: {
    showItemLabel: false,
  } as Mo00020OnewayType,
  /**
   * 氏名等を伏字にするチェックボックス
   */
  mo00018OneWayAmikake: {
    // '氏名等を伏字にする'
    checkboxLabel: t('label.name-censored'),
    showItemLabel: false,
  } as Mo00018OnewayType,
  /**
   * 記入用シートを印刷するチェックボックス
   */
  mo00018OneWayEntry: {
    name: '',
    itemLabel: '',
    // '記入用シートを印刷する'
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    customClass: new CustomClass({
      outerClass: 'entry-label',
    }),
  } as Mo00018OnewayType,
  /**
   * 印刷する要介護度ラベル
   */
  mo01338OneWayCareRequired: {
    // '印刷する要介護度'
    value: t('label.print-level-of-care-required'),
    valueFontWeight: 'bold',
    customClass: new CustomClass({
      itemClass: 'ml-2',
    }),
  } as Mo01338OnewayType,
  /**
   * 印刷する要介護度
   */
  mo00040OneWayCareRequired: {
    items: [],
    itemLabel: '',
    showItemLabel: false,
    itemTitle: 'label',
    itemValue: 'value',
    customClass: new CustomClass({
      itemClass: 'care-required-padding pl-0 pb-2 pt-2',
      outerClass: 'pl-0',
    }),
  } as Mo00040OnewayType,
  /**
   * 具体的内容と対応するケア項目の文字サイズラベル
   */
  mo01338OneWayConcrete: {
    // '具体的内容と対応するケア項目の文字サイズ'
    value: t('label.concrete-correspondence-letter-size'),
    valueFontWeight: 'bold',
    customClass: new CustomClass({
      itemClass: 'ml-2',
    }),
  } as Mo01338OnewayType,
  /**
   * 具体的内容と対応するケア項目の文字サイズ
   */
  mo00039OneWayConcrete: {
    name: '',
    showItemLabel: false,
  } as Mo00039OnewayType,
  /**
   * 利用者選択ラベル
   */
  mo01338OneWayPrinterUserSelect: {
    // '利用者選択'
    value: t('label.printer-user-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 履歴選択ラベル
   */
  mo01338OneWayPrinterHistorySelect: {
    // '履歴選択'
    value: t('label.printer-history-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択
   */
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 履歴選択
   */
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 基準日: 表用テキストフィールド
   */
  mo00020OneWayBasic: {
    // '基準日'
    itemLabel: t('label.base-date'),
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '220px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    customClass: {
      labelClass: 'tanto-label pb-2',
    } as CustomClass,
  } as OrX0145OnewayType,
})

/**************************************************
 * 変数定義
 **************************************************/
/** ダイアログOneway */
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1300px',
  height: '719px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or52060',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or52060ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'or52060-content',
  } as Mo01344OnewayType,
})
/** ダイアログTwoway */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or52060Const.DEFAULT.IS_OPEN,
})

/**
 * 出力帳票名一覧Oneway
 */
const mo01334OnewayLedger = ref<Mo01334OnewayType>({
  headers: [
    {
      // '帳票'
      title: t('label.report'),
      key: 'ledgerName',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 601,
})
/**
 * 出力帳票名一覧Twoway
 */
const mo01334TypeLedger = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * タイトルテキストフィールドmodelValue
 */
const mo00045TitleType = ref<Mo00045Type>({
  value: '',
} as Mo00045Type)

/**
 * 日付印刷区分modelValue
 */
const mo00039DatePrintType = ref<string>('')

/**
 * 指定日modelValue
 */
const mo00020DesignationType = ref<Mo00020Type>({
  // 初期値：システム日付
  value: systemCommonsStore.getSystemDate ?? '',
} as Mo00020Type)
/**
 * 指定日非表示/表示フラグ
 */
const designationShowFlag = ref<boolean>(false)

/**
 * 記入用シートを印刷するmodelValue
 */
const mo00018EntryType = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 印刷する要介護度セレクトボックスmodelValue
 */
const mo00040CareRequiredType = ref<Mo00040Type>({
  modelValue: '',
} as Mo00040Type)

/**
 * 具体的内容と対応するケア項目の文字サイズラジオボタンmodelValue
 */
const mo00039ConcreteType = ref<string>('')

/**
 * 利用者選択modelValue
 */
const mo00039UserSelectType = ref<string>('')
/**
 * 履歴選択modelValue
 */
const mo00039HistorySelectType = ref<string>('')
/**
 * 基準日modelValue
 */
const mo00020BasicType = ref<Mo00020Type>({
  value: '',
} as Mo00020Type)

/**
 * 担当ケアマネプルダウンmodelValue
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
})

/**
 * 利用者列幅
 */
const userCols = ref<number>(5)
/**
 * 利用者一覧
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: Or52060Const.DEFAULT.STR.EMPTY,
  /**
   * 複数選択時、50音の選択数を表示するか
   */
  showKanaSelectionCount: true,
  /**
   * フォーカス設定用イニシャル
   */
  focusSettingInitial: [] as string[],
  // 指定行選択
  userId: Or52060Const.DEFAULT.STR.EMPTY,
})

/**
 * 履歴一覧
 */
const orX0128Oneway = reactive<OrX0128OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: Or52060Const.DEFAULT.STR.EMPTY,
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: Or52060Const.DEFAULT.STR.EMPTY,
  tableStyle: 'height:514px',
  headers: [
    {
      title: t('label.create-date'),
      key: 'createYmd',
      minWidth: '80px',
      width: '120px',
      sortable: false,
    },
    { title: t('label.author'), key: 'shokuinKnj', minWidth: '140px', sortable: false },
  ] as OrX0128Headers[],
  items: [],
})

/**
 * 印刷設定帳票出力
 */
const orX0117Oneway: OrX0117OnewayType = {
  type: Or52060Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType

/**************************************************
 * Pinia
 **************************************************/
/** ダイヤログ状態 */
const { setState } = useScreenOneWayBind<Or52060StateType>({
  cpId: Or52060Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 開閉
     *
     * @param value -パラメータ
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or52060Const.DEFAULT.IS_OPEN
    },
    /**
     * パラメータ
     *
     * @param value -パラメータ
     */
    param: (value) => {
      if (value) {
        // 親画面のデータを取得
        localOneway.or52060Param.processYmd = value.processYmd
        localOneway.or52060Param.basicDate = value.basicDate
        localOneway.or52060Param.sectionName = value.sectionName
        localOneway.or52060Param.shisetuId = value.shisetuId
        localOneway.or52060Param.svJigyoId = value.svJigyoId
        localOneway.or52060Param.svJigyoKnj = value.svJigyoKnj
        localOneway.or52060Param.shokuId = value.shokuId
        localOneway.or52060Param.userId = value.userId
        localOneway.or52060Param.assId = value.assId
        localOneway.or52060Param.tantoId = value.tantoId
        localOneway.or52060Param.prtNo = value.prtNo
        localOneway.or52060Param.focusSettingInitial = value.focusSettingInitial
        localOneway.or52060Param.selectedUserCounter = value.selectedUserCounter
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0145Const.CP_ID(0)]: orX0145.value,
})

/**************************************************
 * 算出プロパティ
 **************************************************/
// 記入用シートを印刷するチェックボックス活性制御
/** 利用者選択方法が「単一」 また 履歴選択方法が「単一」の場合、活性表示。 */
const mo00018EntryDisabled = computed(
  () => !(mo00039UserSelectType.value === '0') || !(mo00039HistorySelectType.value === '0')
)

/**
 * 具体的内容と対応するケア項目の文字サイズラジオボタン表示制御
 * 印刷設定情報リストから選択行.帳票番号が9（具体的内容と対応するケア項目一覧）又は10（ケアチェック要約表）の場合、非表示
 */
const mo00039ConcreteShowFlg = computed(
  () => mo01334TypeLedger.value.value !== '9' && mo01334TypeLedger.value.value !== '10'
)

// 担当ケアマネ選択アイコンボタン活性制御
/** 共通情報.担当ケアマネ設定フラグ > 0、且つ、親画面.担当者IDが0以外の場合、非活性 */
const orX0145Disabled = computed(
  () => Number(localOneway.commonInfo.kkjTantoFlg) > 0 && localOneway.or52060Param.tantoId !== '0'
)

/** 印刷ダイアログ表示フラグ */
const showDialogOrX0117 = computed(() => {
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * ライフサイクル
 **************************************************/
onUnmounted(() => {
  OrX0130Logic.event.set({
    uniqueCpId: orX0130.value.uniqueCpId,
    events: {
      clickFlg: false,
      userList: [],
    },
  })
})

/**************************************************
 * 関数
 **************************************************/
/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 印刷する要介護度(認定調査票)
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRINT_LEVEL_OF_CARE_REQUIRED },
    // メモの文字サイズ
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEMO_LETTER_SIZE_KBN },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    const list: Mo00039Items[] = []
    for (const item of bizukePrintCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayDatePrint.items = list
  }

  // 印刷する要介護度(認定調査票)
  const printCareRequiredCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PRINT_LEVEL_OF_CARE_REQUIRED
  )
  if (printCareRequiredCodeTypes?.length > 0) {
    const list: CodeType[] = []
    for (const item of printCareRequiredCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        })
      }
    }
    localOneway.mo00040OneWayCareRequired.items = list
  }

  // メモの文字サイズ
  const concreteLetterSizeCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_MEMO_LETTER_SIZE_KBN
  )
  if (concreteLetterSizeCodeTypes.length > 0) {
    const list: Mo00039Items[] = []
    for (const item of concreteLetterSizeCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayConcrete.items = list
  }

  // メモの文字サイズ

  // 利用者選択 TAN_MULTIPLE_SELECT_CATEGORY
  const tanMultipleSelectCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  if (tanMultipleSelectCategoryCodeTypes?.length > 0) {
    mo00039UserSelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    mo00039HistorySelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of tanMultipleSelectCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayHistorySelectType.items = list
    localOneway.mo00039OneWayUserSelectType.items = list
  }
}

/**
 * 初期情報取得
 */
const getInitData = async () => {
  // 基準日初期値=親画面.処理年月日
  mo00020BasicType.value.value = localOneway.or52060Param.processYmd
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentComprehensivePrintSettingsInitialSelectInEntity = {
    // システムコード：共通情報.システムコード
    sysCd: localOneway.commonInfo.sysCd,
    // システム略称："3gk"
    sysRyaku: SYS_RYAKU,
    // 法人ID：共通情報.法人ID
    houjinId: localOneway.commonInfo.houjinId,
    // 施設ID：親画面.施設ID
    shisetuId: localOneway.or52060Param.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.or52060Param.svJigyoId,
    // 職員ID：親画面.職員ID
    shokuId: localOneway.or52060Param.shokuId,
    // 利用者ID：親画面.利用者ID
    userId: localOneway.or52060Param.userId,
    // セクション名：親画面.セクション名
    sectionName: localOneway.or52060Param.sectionName,
    // インデックス：0
    index: Or52060Const.DEFAULT.INDEX,
    // メニュー２名称
    menu2Knj: Or52060Const.DEFAULT.NM_MNU2_3GK_INCLUDE_ASSESSMENT,
    // メニュー３名称
    menu3Knj: Or52060Const.DEFAULT.NM_MNU3_3GK_INCLUDE_ASSESSMENT,
    // 個人情報使用フラグ：0 ※0：不使用、1：使用
    kojinhogoUsedFlg: Or52060Const.DEFAULT.KOJINHOGO_USED_FLG,
    // 個人情報番号：0 ※0：主に日誌以外、1：主に日誌系
    sectionAddNo: Or52060Const.DEFAULT.SECTION_ADD_NO,
  }
  const res: AssessmentComprehensivePrintSettingsInitialSelectOutEntity =
    await ScreenRepository.update('assessmentComprehensivePrintSettingsInitialUpdate', inputData)
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    ledgerInfo.prtList = res.data.prtList
    // 履歴一覧データの一時保存
    localOneway.initPeriodHistoryList = res.data.periodHistoryList
    const prtList: Mo01334Items[] = []
    // Onewayの期間管理フラグを設定
    localOneway.kikanFlg = res.data.kikanFlg
    // 履歴一覧の期間管理フラグを設定
    orX0128Oneway.kikanFlg = res.data.kikanFlg
    // 具体的内容と対応するケア項目の文字サイズラジオボタンを設定
    mo00039ConcreteType.value = res.data.letterSize
    // 文字サイズ更新回数を設定
    ledgerInfo.letterModifiedCnt = res.data.letterModifiedCnt
    let selectable = true
    for (const item of res.data.prtList) {
      if (res.data.prtList.length === 1) {
        selectable = false
      }
      prtList.push({
        id: item.index,
        mo01337OnewayLedgerName: {
          value: item.prtTitle,
          unit: '',
        } as Mo01337OnewayType,
        prtTitle: item.prtTitle,
        prnDate: item.prnDate,
        sectionNo: item.sectionNo,
        selectable: selectable,
        profile: item.profile,
        index: item.index,
        prtNo: item.prtNo,
        careRequiredSelectValue: item.param06,
      } as Mo01334Items)
      // 画面.帳票番号と親画面.選択帳票番号一致の帳票名を選択される
      if (item.prtNo === localOneway.or52060Param.prtNo) {
        mo01334TypeLedger.value.value = item.index
        // タイトルテキストフィールド設定
        mo00045TitleType.value.value = item.prtTitle
        // 日付表示有無を設定
        mo00039DatePrintType.value = item.prnDate
        // 印刷する要介護度セレクトボックスを設定
        mo00040CareRequiredType.value.modelValue = item.param06
        // プロファイルを設定
        ledgerInfo.profile = item.profile
        // 出力帳票名一覧の選択行番号を設定
        ledgerInfo.index = item.index
        // セクション番号を設定
        ledgerInfo.sectionNo = item.sectionNo
        // 出力帳票ID設定
        setReportId(item.index)
      }
    }
    // 初期化フラグ設定
    initFlg.value = true
    // 帳票一覧テーブルにデータを追加
    mo01334OnewayLedger.value.items = prtList
    // 履歴一覧データ処理
    setHistoryData(res.data.periodHistoryList)
    // 履歴一覧明細に親画面.アセスメントIDが存在する場合
    if (localOneway.or52060Param.assId) {
      // 履歴一覧明細に親画面.アセスメントIDを対するレコードを選択状態にする
      orX0128Oneway.initSelectId = (
        orX0128Oneway.items.findIndex((item) => item.cc1Id === localOneway.or52060Param.assId) + 1
      ).toString()
    }
  }
}

/**
 * 「出力帳票名」選択
 *
 * @param selectId - 出力帳票ID
 */
const selectLedgerName = (selectId: string) => {
  for (const item of mo01334OnewayLedger.value.items) {
    if (selectId === item.id && item.mo01337OnewayLedgerName) {
      // 「出力帳票名」選択行設定
      mo01334TypeLedger.value.value = selectId
      // タイトルテキストフィールド設定
      mo00045TitleType.value.value = item.prtTitle as string
      // 日付表示有無を設定
      mo00039DatePrintType.value = item.prnDate as string
      // 印刷する要介護度セレクトボックスを設定
      mo00040CareRequiredType.value.modelValue = item.careRequiredSelectValue as string
      // プロファイルを設定
      ledgerInfo.profile = item.profile as string
      // 出力帳票名一覧の選択行番号を設定
      ledgerInfo.index = item.index as string
      // セクション番号を設定
      ledgerInfo.sectionNo = item.sectionNo as string
      // 出力帳票ID設定
      setReportId(item.id)
      break
    }
  }
}

/**
 * 出力帳票ID設定 TODO
 *
 * @param index - インデックス
 */
const setReportId = (index: string) => {
  switch (index) {
    case '1':
      ledgerInfo.reportId = Or52060Const.DEFAULT.CARE_CHECK_TABLE_1_REPORE_ID
      break
    case '2':
      ledgerInfo.reportId = Or52060Const.DEFAULT.CARE_CHECK_TABLE_2_REPORE_ID
      break
    case '3':
      ledgerInfo.reportId = Or52060Const.DEFAULT.CARE_CHECK_TABLE_3_REPORE_ID
      break
    case '4':
      ledgerInfo.reportId = Or52060Const.DEFAULT.CARE_CHECK_TABLE_4_REPORE_ID
      break
    case '5':
      ledgerInfo.reportId = Or52060Const.DEFAULT.CARE_CHECK_TABLE_5_REPORE_ID
      break
    case '6':
      ledgerInfo.reportId = Or52060Const.DEFAULT.CARE_CHECK_TABLE_6_REPORE_ID
      break
    case '7':
      ledgerInfo.reportId = Or52060Const.DEFAULT.CARE_CHECK_TABLE_7_REPORE_ID
      break
    case '8':
      ledgerInfo.reportId = Or52060Const.DEFAULT.CARE_CHECK_TABLE_REPORE_ALL_ID
      break
    case '9':
      ledgerInfo.reportId = Or52060Const.DEFAULT.CARE_ITEM_LIST_REPORT_ID
      break
    case '10':
      ledgerInfo.reportId = Or52060Const.DEFAULT.CARE_CHECK_YOYAKU_TABLE_REPORE_ID
      break
    default:
      ledgerInfo.reportId = Or52060Const.DEFAULT.STR.EMPTY
      break
  }
}

/**
 * 選択行のシステムINI情報を取得する
 */
const getSelectedRowSysInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity = {
    // プロファイル：画面.帳票一覧選択行.プロファイル
    profile: ledgerInfo.profile,
    // システムコード：共通情報.システムコード
    gsyscd: localOneway.commonInfo.sysCd,
    // 職員ID：共通情報.職員ID
    shokuId: localOneway.commonInfo.shokuId,
    // 個人情報使用フラグ：0 ※0：不使用、1：使用
    kojinhogoUsedFlg: Or52060Const.DEFAULT.KOJINHOGO_USED_FLG,
    // 個人情報番号：0 ※0：主に日誌以外、1：主に日誌系
    sectionAddNo: Or52060Const.DEFAULT.SECTION_ADD_NO,
  } as IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity
  const res: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity =
    await ScreenRepository.update('freeAssessmentFacePrintSettingsPrtNoChangeUpdate', inputData)
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    ledgerInfo.sysIniInfo = res.data.sysIniInfo
  }
}

/**
 * アセスメント履歴情報を取得する
 */
const getPrintSettingsHistoryList = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentComprehensivePrintSettinguserSwitchingSelectInEntity = {
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.or52060Param.svJigyoId,
    // 利用者ID：画面.利用者一覧に選択した利用者ID
    userId: ledgerInfo.selectedUserId,
    // 期間管理フラグ
    kikanFlg: localOneway.kikanFlg,
  }
  const res: AssessmentComprehensivePrintSettinguserSwitchingSelectOutEntity =
    await ScreenRepository.select(
      'assessmentComprehensivePrintSettinguserSwitchingSelect',
      inputData
    )
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    // 履歴一覧データの一時保存
    localOneway.initPeriodHistoryList = res.data.periodHistoryList
    // 履歴一覧データ処理
    setHistoryData(res.data.periodHistoryList)
  }
}

/**
 * 履歴一覧データ処理
 *
 * @param periodHistoryList - 履歴リスト
 */
const setHistoryData = (periodHistoryList: PeriodHistoryEntity[]) => {
  // 期間管理フラグが「1:管理する」の場合
  if (localOneway.kikanFlg === Or52060Const.DEFAULT.KIKAN_FLG_MANAGE) {
    // ソート
    periodHistoryList.sort((a, b) => {
      if (b.sc1Id !== a.sc1Id) {
        return Number(b.sc1Id) - Number(a.sc1Id)
      }
      return new Date(b.startYmd).getTime() - new Date(a.startYmd).getTime()
    })
  }

  // 履歴テーブルデータ
  const orX0128TableData: OrX0128Items[] = []
  // 期間Id一時保存変数
  let tempSc1Id = ''
  periodHistoryList.forEach((item) => {
    // 期間管理フラグが「1:管理する」の場合
    if (localOneway.kikanFlg === Or52060Const.DEFAULT.KIKAN_FLG_MANAGE) {
      if (tempSc1Id !== item.sc1Id) {
        orX0128TableData.push({
          id: Or52060Const.DEFAULT.STR.EMPTY,
          sc1Id: item.sc1Id,
          startYmd: item.startYmd,
          endYmd: item.endYmd,
          isPeriodManagementMergedRow: true,
          planPeriod:
            t('label.plan-period') + SPACE_WAVE + item.startYmd + SPACE_SPLIT_COLON + item.endYmd,
        } as OrX0128Items)
      }
      orX0128TableData.push({
        id: Or52060Const.DEFAULT.STR.EMPTY,
        sc1Id: item.sc1Id,
        startYmd: item.startYmd,
        endYmd: item.endYmd,
        sel: item.sel,
        cc1Id: item.cc1Id,
        userId: item.userId,
        createYmd: item.createYmd,
        shokuId: item.shokuId,
        shokuinKnj: item.shokuinKnj,
      } as OrX0128Items)

      // 期間Id一時保存
      tempSc1Id = item.sc1Id
    } else {
      orX0128TableData.push({
        id: Or52060Const.DEFAULT.STR.EMPTY,
        sc1Id: item.sc1Id,
        startYmd: item.startYmd,
        endYmd: item.endYmd,
        sel: item.sel,
        cc1Id: item.cc1Id,
        userId: item.userId,
        createYmd: item.createYmd,
        shokuId: item.shokuId,
        shokuinKnj: item.shokuinKnj,
      } as OrX0128Items)
    }
  })
  orX0128TableData.forEach((item, index) => (item.id = String(index + 1)))
  // 履歴一覧テーブルを設定
  orX0128Oneway.items = orX0128TableData
}

/**
 * 画面印刷設定内容を保存
 *
 * @returns -保存出力エンティティ
 */
const save = async (): Promise<IFreeAssessmentFacePrintSettingsUpdateOutEntity> => {
  // バックエンドAPIから印刷設定情報保存
  const inputData: IFreeAssessmentFacePrintSettingsUpdateInEntity = {
    // システム略称："3gk"
    sysRyaku: SYS_RYAKU,
    // セクション名：親画面.セクション名
    sectionName: localOneway.or52060Param.sectionName,
    // システムコード：共通情報.システムコード
    gsyscd: systemCommonsStore.getSystemCode,
    // 職員ID：共通情報.職員ID
    shokuId: systemCommonsStore.getStaffId,
    // 法人ID：共通情報.法人ID
    houjinId: systemCommonsStore.getHoujinId,
    // 施設ID：親画面.施設ID
    shisetuId: localOneway.or52060Param.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.or52060Param.svJigyoId,
    // インデックス：画面.出力帳票名一覧に選択行番号
    index: ledgerInfo.index,
    // システムINI情報：画面.システムINI情報
    sysIniInfo: {
      amikakeFlg: ledgerInfo.sysIniInfo.amikakeFlg,
      amikakeModifiedCnt: ledgerInfo.sysIniInfo.amikakeModifiedCnt,
      iso9001Flg: ledgerInfo.sysIniInfo.iso9001Flg,
      iso9001ModifiedCnt: ledgerInfo.sysIniInfo.iso9001ModifiedCnt,
      kojinhogoUsedFlg: Or52060Const.DEFAULT.KOJINHOGO_USED_FLG,
      kojinhogoFlg: ledgerInfo.sysIniInfo.kojinhogoFlg,
      kojinhogoModifiedCnt: ledgerInfo.sysIniInfo.kojinhogoModifiedCnt,
      sectionAddNo: Or52060Const.DEFAULT.SECTION_ADD_NO,
    },
    // 印刷設定情報リスト：画面.印刷設定情報リスト
    prtList: ledgerInfo.prtList,
  } as IFreeAssessmentFacePrintSettingsUpdateInEntity
  const res: IFreeAssessmentFacePrintSettingsUpdateOutEntity = await ScreenRepository.update(
    'freeAssessmentFacePrintSettingsUpdate',
    inputData
  )
  return res
}

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  // 画面の印刷設定情報を保存する
  await save()
  setState({
    isOpen: false,
    param: {} as Or52060Param,
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const pdfDownload = async () => {
  // 利用者選択が「単一」、且つ、履歴選択が「単一」の場合
  // 且つ 記入用シートを印刷するチェック入れるの場合
  if (
    mo00039UserSelectType.value === Or52060Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or52060Const.DEFAULT.TANI &&
    mo00018EntryType.value.modelValue
  ) {
    // 帳票出力
    await reportOutputPdf()
    return
  }

  // 利用者選択が「単一」且つ 履歴選択が「単一」
  // 且つ 記入用シートを印刷するチェック外す 且つ 利用者一覧が0件選択の場合
  if (
    mo00039UserSelectType.value === Or52060Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or52060Const.DEFAULT.TANI &&
    !mo00018EntryType.value.modelValue &&
    OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList.length === 0
  ) {
    // メッセージを表示
    const dialogResult = await openConfirmDialog(t('message.i-cmn-11393'))
    // はい
    if (dialogResult === DIALOG_BTN.YES) {
      // 処理終了
      return
    }
  }

  // 利用者選択が「複数」、画面.利用者情報リストにデータを選択しない場合
  if (
    mo00039UserSelectType.value === Or52060Const.DEFAULT.HUKUSUU &&
    OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList.length === 0
  ) {
    // メッセージを表示
    const dialogResult = await openConfirmDialog(t('message.i-cmn-11393'))
    // はい
    if (dialogResult === DIALOG_BTN.YES) {
      // 処理終了
      return
    }
  }
  // 利用者選択が「単一」 且つ 履歴選択が「単一」
  // 且つ 記入用シートを印刷するチェック外す 履歴一覧が0件選択の場合
  if (
    mo00039UserSelectType.value === Or52060Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or52060Const.DEFAULT.TANI &&
    !mo00018EntryType.value.modelValue &&
    OrX0128Logic.event.get(orX0128.value.uniqueCpId)?.orX0128DetList.length === 0
  ) {
    // メッセージを表示
    const dialogResult = await openConfirmDialog(t('message.i-cmn-11455'))
    // はい
    if (dialogResult === DIALOG_BTN.YES) {
      // 処理終了
      return
    }
  }

  // 利用者選択が「単一」 且つ
  // 履歴選択が「複数」、履歴一覧が0件選択場合
  if (
    mo00039UserSelectType.value === Or52060Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or52060Const.DEFAULT.HUKUSUU &&
    OrX0128Logic.event.get(orX0128.value.uniqueCpId)?.orX0128DetList.length === 0
  ) {
    // メッセージを表示
    const dialogResult = await openConfirmDialog(t('message.i-cmn-11455'))
    // はい
    if (dialogResult === DIALOG_BTN.YES) {
      // 処理終了
      return
    }
  }

  // 画面の印刷設定を保存する
  await save()

  // 選択された帳票のプロファイルが''の場合
  if (!ledgerInfo.profile) {
    // メッセージを表示
    const dialogResult = await openErrorDialog(t('message.e-cmn-40172'))
    // はい
    if (dialogResult === DIALOG_BTN.YES) {
      // 処理終了
      return
    }
  }

  // 利用者選択方法が「単一」、履歴選択方法が「単一」、履歴情報リストにデータを選択する場合
  if (
    mo00039UserSelectType.value === Or52060Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or52060Const.DEFAULT.TANI &&
    (OrX0128Logic.event.get(orX0128.value.uniqueCpId)?.orX0128DetList.length ?? 0 > 0)
  ) {
    // 帳票出力
    await reportOutputPdf()
    return
  }

  // 利用者選択が「単一」、履歴選択が「複数」、履歴情報リストにデータを選択する場合の場合
  if (
    mo00039UserSelectType.value === Or52060Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or52060Const.DEFAULT.HUKUSUU &&
    (OrX0128Logic.event.get(orX0128.value.uniqueCpId)?.orX0128DetList.length ?? 0 > 0)
  ) {
    // 印刷設定情報リストを作成
    createReportOutputData(ledgerInfo.index)
  }
  // 利用者選択が「複数」の場合
  if (
    mo00039UserSelectType.value === Or52060Const.DEFAULT.HUKUSUU &&
    (OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList.length ?? 0 > 0)
  ) {
    // 印刷設定情報リストを再取得する
    await getPrintSettingsSubject()
  }

  // 「AC020-4-2とAC020-4-3」で取得する印刷設定情報リスト件数分、帳票を作成し、ローカルでダウンロードする
  if (orX0117Oneway.historyList.length > 0) {
    // OrX0117のダイアログ開閉状態を更新する
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 印刷設定情報リストを作成
 *
 * @param index - 帳票インデックス
 */
const createReportOutputData = (index: string) => {
  const orX0117HistoryList: OrX0117History[] = []
  for (const history of ledgerInfo.historyList) {
    const reportData = {
      // 事業者名=親画面.事業所名
      svJigyoKnj: localOneway.or52060Param.svJigyoKnj,
      // 印刷設定
      printConfigure: {
        shiTeiKubun: mo00039DatePrintType.value,
        shiTeiDate: mo00020DesignationType.value.value,
      },
      // 印刷オプション
      printOption: {
        emptyFlg: mo00018EntryType.value.modelValue ? '1' : '0',
        kaigodo: mo00040CareRequiredType.value.modelValue,
        letterSize: mo00039ConcreteType.value,
      },
      // 印刷対象履歴リスト
      printHistoryList: [],
    } as CareCheckTableItemSelectInEntity

    const outputLedgerPrintList: OutputLedgerPrintEntity[] = []
    for (const item of ledgerInfo.prtList) {
      //  単一帳票
      if (index === item.index) {
        // 印刷対象処理
        const printSubject = setPrintSubject(item)
        outputLedgerPrintList.push(printSubject)
      }
      // 全て帳票
      else if (index === '8') {
        // 印刷対象処理
        const printSubject = setPrintSubject(item)
        outputLedgerPrintList.push(printSubject)
      }
    }

    reportData.printHistoryList.push({
      userId:
        ledgerInfo.userList.length > 0
          ? ledgerInfo.userList[0].userId
          : Or52060Const.DEFAULT.STR.EMPTY,
      userName:
        ledgerInfo.userList.length > 0
          ? ledgerInfo.userList[0].userName
          : Or52060Const.DEFAULT.STR.EMPTY,
      sc1Id: history.sc1Id as string,
      startYmd: history.startYmd as string,
      endYmd: history.endYmd as string,
      cc1Id: history.cc1Id as string,
      createYmd: history.createYmd as string,
      shokuId: history.shokuId as string,
      shokuinKnj: history.shokuKnj as string,
      result: Or52060Const.DEFAULT.STR.EMPTY,
      outputLedgerPrintList: outputLedgerPrintList,
    })
    orX0117HistoryList.push({
      reportId: ledgerInfo.reportId,
      outputType: reportOutputType.DOWNLOAD,
      reportData: reportData,
      userName:
        ledgerInfo.userList.length > 0
          ? ledgerInfo.userList[0].userName
          : Or52060Const.DEFAULT.STR.EMPTY,
      historyDate: history.createYmd as string,
      result: Or52060Const.DEFAULT.STR.EMPTY,
    })
  }
  orX0117Oneway.historyList = orX0117HistoryList
}

/**
 * 印刷対象処理
 *
 * @param item -帳票エンティティ
 *
 * @returns 印刷対象
 */
const setPrintSubject = (item: PrtEntity): OutputLedgerPrintEntity => {
  return {
    defPrtTitle: item.defPrtTitle,
    prtTitle: item.prtTitle,
    sectionNo: item.sectionNo,
    prtNo: item.prtNo,
    choPro: item.profile,
    sectionName: localOneway.or52060Param.sectionName,
    dwObject: item.dwobject,
    prtOrient: item.prtOrient,
    prtSize: item.prtSize,
    listTitle: item.listTitle,
    mTop: item.mtop,
    mBottom: item.mbottom,
    mLeft: item.mleft,
    mRight: item.mright,
    ruler: item.ruler,
    prnDate: mo00039DatePrintType.value,
    prnShoku: item.prnshoku,
    serialFlg: item.serialFlg,
    modFlg: item.modFlg,
    secFlg: item.secFlg,
    serialHeight: item.serialHeight,
    serialPagelen: item.serialPagelen,
    zoomRate: item.zoomRate,
    param01: item.param01,
    param02: item.param02,
    param03: item.param03,
    param04: item.param04,
    param05: item.param05,
    param06: mo00040CareRequiredType.value.modelValue ?? '',
    param07: item.param07,
    param08: item.param08,
    param09: item.param09,
    param10: item.param10,
    param11: item.param11,
    param12: item.param12,
    param13: item.param13,
    param14: item.param14,
    param15: item.param15,
    param16: item.param16,
    param17: item.param17,
    param18: item.param18,
    param19: item.param19,
    param20: item.param20,
    param21: item.param21,
    param22: item.param22,
    param23: item.param23,
    param24: item.param24,
    param25: item.param25,
    param26: item.param26,
    param27: item.param28,
    param28: item.param28,
    param29: item.param29,
    param30: item.param30,
    param31: item.param31,
    param32: item.param32,
    param33: item.param33,
    param34: item.param34,
    param35: item.param35,
    param36: item.param36,
    param37: item.param37,
    param38: item.param38,
    param39: item.param39,
    param40: item.param40,
    param41: item.param41,
    param42: item.param42,
    param43: item.param43,
    param44: item.param44,
    param45: item.param45,
    param46: item.param46,
    param47: item.param47,
    param48: item.param48,
    param49: item.param49,
    param50: item.param50,
  }
}

/**
 * 印刷処理
 */
const reportOutputPdf = async () => {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    const reportData = {
      // 事業者名=親画面.事業所名
      svJigyoKnj: localOneway.or52060Param.svJigyoKnj,
      // 印刷設定
      printConfigure: {
        shiTeiKubun: mo00039DatePrintType.value,
        shiTeiDate: mo00020DesignationType.value.value,
      },
      // 印刷オプション
      printOption: {
        emptyFlg: mo00018EntryType.value.modelValue ? '1' : '0',
        kaigodo: mo00040CareRequiredType.value.modelValue,
        letterSize: mo00039ConcreteType.value,
      },
      // 印刷対象履歴リスト
      printHistoryList: [],
    } as CareCheckTableItemSelectInEntity

    const outputLedgerPrintList: OutputLedgerPrintEntity[] = []
    for (const item of ledgerInfo.prtList) {
      //  単一帳票
      if (item.index !== '8') {
        // 印刷対象処理
        const printSubject = setPrintSubject(item)
        outputLedgerPrintList.push(printSubject)
      }
      // 全て帳票
      else if (ledgerInfo.index === '8') {
        // 印刷対象処理
        const printSubject = setPrintSubject(item)
        outputLedgerPrintList.push(printSubject)
      }
    }

    reportData.printHistoryList.push({
      userId:
        ledgerInfo.userList.length > 0
          ? ledgerInfo.userList[0].userId
          : Or52060Const.DEFAULT.STR.EMPTY,
      userName:
        ledgerInfo.userList.length > 0
          ? ledgerInfo.userList[0].userName
          : Or52060Const.DEFAULT.STR.EMPTY,
      sc1Id:
        ledgerInfo.historyList.length > 0
          ? (ledgerInfo.historyList[0].sc1Id as string)
          : Or52060Const.DEFAULT.STR.EMPTY,
      startYmd:
        ledgerInfo.historyList.length > 0
          ? (ledgerInfo.historyList[0].startYmd as string)
          : Or52060Const.DEFAULT.STR.EMPTY,
      endYmd:
        ledgerInfo.historyList.length > 0
          ? (ledgerInfo.historyList[0].endYmd as string)
          : Or52060Const.DEFAULT.STR.EMPTY,
      cc1Id:
        ledgerInfo.historyList.length > 0
          ? (ledgerInfo.historyList[0].cc1Id as string)
          : Or52060Const.DEFAULT.STR.EMPTY,
      createYmd:
        ledgerInfo.historyList.length > 0
          ? (ledgerInfo.historyList[0].createYmd as string)
          : Or52060Const.DEFAULT.STR.EMPTY,
      shokuId:
        ledgerInfo.historyList.length > 0
          ? (ledgerInfo.historyList[0].shokuId as string)
          : Or52060Const.DEFAULT.STR.EMPTY,
      shokuinKnj:
        ledgerInfo.historyList.length > 0
          ? (ledgerInfo.historyList[0].shokuKnj as string)
          : Or52060Const.DEFAULT.STR.EMPTY,
      result: Or52060Const.DEFAULT.STR.EMPTY,
      outputLedgerPrintList: outputLedgerPrintList,
    })
    // 帳票出力
    await reportOutput(ledgerInfo.reportId, reportData, reportOutputType.DOWNLOAD)
  } catch (e) {
    $log.debug('帳票の出力に失敗しました。', ledgerInfo.reportId, reportOutputType.DOWNLOAD, e)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * 印刷設定情報リストを再取得する
 */
const getPrintSettingsSubject = async () => {
  // 印刷設定情報リストを作成する
  const inputData: AssessmentComprehensivePrintSettingsHistorySelectInEntity = {
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.or52060Param.svJigyoId,
    // 基準日：画面.基準日
    kijunbiYmd: mo00020BasicType.value.value,
    // 利用者リスト：画面.利用者リストの選択対象
    userList: ledgerInfo.userList,
  }
  const res: AssessmentComprehensivePrintSettingsHistorySelectOutEntity =
    await ScreenRepository.select('assessmentComprehensivePrintSettingsHistorySelect', inputData)

  const orX0117HistoryList: OrX0117History[] = []
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    for (const history of res.data.printSubjectHistoryList) {
      const reportData = {
        // 事業者名=親画面.事業所名
        svJigyoKnj: localOneway.or52060Param.svJigyoKnj,
        // 印刷設定
        printConfigure: {
          shiTeiKubun: mo00039DatePrintType.value,
          shiTeiDate: mo00020DesignationType.value.value,
        },
        // 印刷オプション
        printOption: {
          emptyFlg: mo00018EntryType.value.modelValue ? '1' : '0',
          kaigodo: mo00040CareRequiredType.value.modelValue,
          letterSize: mo00039ConcreteType.value,
        },
        // 印刷対象履歴リスト
        printHistoryList: [],
      } as CareCheckTableItemSelectInEntity

      const outputLedgerPrintList: OutputLedgerPrintEntity[] = []
      for (const item of ledgerInfo.prtList) {
        //  単一帳票
        if (item.index !== '8') {
          // 印刷対象処理
          const printSubject = setPrintSubject(item)
          outputLedgerPrintList.push(printSubject)
        }
        // 全て帳票
        else if (item.index === '8') {
          // 印刷対象処理
          const printSubject = setPrintSubject(item)
          outputLedgerPrintList.push(printSubject)
        }
      }

      reportData.printHistoryList.push({
        userId:
          ledgerInfo.userList.length > 0
            ? ledgerInfo.userList[0].userId
            : Or52060Const.DEFAULT.STR.EMPTY,
        userName:
          ledgerInfo.userList.length > 0
            ? ledgerInfo.userList[0].userName
            : Or52060Const.DEFAULT.STR.EMPTY,
        sc1Id: Or52060Const.DEFAULT.STR.EMPTY,
        startYmd: history.startYmd,
        endYmd: history.endYmd,
        cc1Id: history.cc1Id,
        createYmd: history.createYmd,
        shokuId: history.shokuId,
        shokuinKnj: Or52060Const.DEFAULT.STR.EMPTY,
        result: history.result,
        outputLedgerPrintList: outputLedgerPrintList,
      })
      orX0117HistoryList.push({
        reportId: ledgerInfo.reportId,
        outputType: reportOutputType.DOWNLOAD,
        reportData: reportData,
        userName: history.userName,
        historyDate: Or52060Const.DEFAULT.STR.EMPTY,
        result: Or52060Const.DEFAULT.STR.EMPTY,
      })
    }
  }
  orX0117Oneway.historyList = orX0117HistoryList
}

/**
 * エラーダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
const openErrorDialog = async (paramDialogText: string): Promise<'yes' | 'no'> => {
  // エラーダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // エラーダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result: 'yes' | 'no' = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // エラーダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
const openConfirmDialog = async (paramDialogText: string): Promise<'yes' | 'no'> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result: 'yes' | 'no' = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 文字列サイズを保存する
 */
const saveLetterSize = async () => {
  // バックエンドAPIから印刷設定情報保存
  const inputData: AssessmentComprehensiveLetterSizeUpdateInEntity = {
    // 職員ID：共通情報.職員ID
    shokuId: localOneway.commonInfo.shokuId,
    // システムコード：共通情報.システムコード
    sysCd: localOneway.commonInfo.sysCd,
    // 画面.文字サイズ
    letterSize: mo00039ConcreteType.value,
    // 画面.文字サイズ更新回数
    letterModifiedCnt: ledgerInfo.letterModifiedCnt,
  }
  const res: AssessmentComprehensiveLetterSizeUpdateOutEntity = await ScreenRepository.update(
    'assessmentComprehensiveLetterSizeUpdate',
    inputData
  )
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    return
  }
}

/**
 * 右上部'×'押す
 *
 * @param mo00024Type -ダイアログmodel
 */
const topRightClose = async (mo00024Type: Mo00024Type) => {
  if (!mo00024Type.isOpen) {
    await close()
  }
}

/**
 * 担当ケアマネプルダウン
 *
 * @param result - 戻り値
 */
const orx0145UpdateModelValue = (result: OrX0145Type) => {
  if (result) {
    if (result.value) {
      if (!Array.isArray(result.value) && 'chkShokuId' in result.value) {
        // TODO API疎通時に確認
        orX0130Oneway.tantouCareManager = result.value.chkShokuId
      }
    }

    // 画面.利用者選択が単一の場合
    if (Or52060Const.DEFAULT.TANI === mo00039UserSelectType.value) {
      // 利用者IDが''場合
      if (orX0130Oneway.userId === '') {
        // 画面.利用者一覧明細の1件目レコードを選択状態にする
        orX0130Oneway.userId = Or52060Const.DEFAULT.STR.EMPTY
      }
    }
  }
}

/**
 * 初期選択データ設定
 */
const selectRowDataSetting = () => {
  // フォーカス設定用イニシャル設定
  orX0130Oneway.focusSettingInitial = localOneway.or52060Param.focusSettingInitial
  orX0130Oneway.userId = localOneway.or52060Param.userId
  // 利用者一覧明細に親画面.利用者IDが存在する場合
  if (localOneway.or52060Param.userId) {
    // 利用者IDを対するレコードを選択状態にする
    orX0130Oneway.userId = localOneway.or52060Param.userId
  }
  // 親画面.利用者IDが存在しない場合
  else {
    orX0130Oneway.userId = Or52060Const.DEFAULT.STR.EMPTY
  }
  // 初期選択状態の担当者カウンタ値設定
  localOneway.orX0145Oneway.selectedUserCounter = localOneway.or52060Param.selectedUserCounter
}
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // 汎用コード取得API実行
  await initCodes()
  // 初期情報取得
  await getInitData()
  // 初期選択データ設定
  selectRowDataSetting()
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 「出力帳票名」選択の変更監視
 */
watch(
  () => mo01334TypeLedger.value.value,
  async (newVal, oldValue) => {
    if (!newVal) return
    if (oldValue && newVal !== oldValue) {
      // 「出力帳票名」選択した、画面データを変更
      selectLedgerName(newVal)
      // 選択行のシステムINI情報を取得する
      await getSelectedRowSysInfo()
    }
    // 日付印刷区分が2の場合
    if ('2' === mo00039DatePrintType.value) {
      // 指定日を活性表示にする。
      designationShowFlag.value = true
    } else {
      // 指定日を非表示にする。
      designationShowFlag.value = false
    }
  }
)

/**
 * 日付印刷区分の変更監視
 */
watch(
  () => mo00039DatePrintType.value,
  (newValue) => {
    const index = Number(ledgerInfo.index) - 1
    // 画面項目変更した後、出力帳票印刷情報リストを更新
    if (ledgerInfo.prtList[index] && mo01334OnewayLedger.value.items[index]) {
      ledgerInfo.prtList[index].prnDate = mo00039DatePrintType.value
      mo01334OnewayLedger.value.items[index].prnDate = mo00039DatePrintType.value
    }
    // 日付印刷区分が2の場合
    if ('2' === newValue) {
      // 指定日を活性表示にする。
      designationShowFlag.value = true
    } else {
      // 指定日を非表示にする。
      designationShowFlag.value = false
    }
  }
)

/**
 * 印刷する要介護度セレクトボックスの変更監視
 */
watch(
  () => mo00040CareRequiredType.value,
  () => {
    const index = Number(ledgerInfo.index) - 1
    // 画面項目変更した後、出力帳票印刷情報リストを更新
    if (ledgerInfo.prtList[index] && mo01334OnewayLedger.value.items[index]) {
      ledgerInfo.prtList[index].param06 = mo00040CareRequiredType.value.modelValue!
      mo01334OnewayLedger.value.items[index].careRequiredSelectValue =
        mo00040CareRequiredType.value.modelValue!
    }
  }
)

/**
 * 具体的内容と対応するケア項目の文字サイズラジオボタンの監視
 */
watch(
  () => mo00039ConcreteType.value,
  async (newVal, oldValue) => {
    if (!newVal) return
    if (oldValue && newVal !== oldValue) {
      // 文字列サイズの選択切替 文字列サイズを保存する
      await saveLetterSize()
    }
  }
)

/**
 * 利用者選択方法の変更監視
 */
watch(
  () => mo00039UserSelectType.value,
  async (newValue) => {
    // 利用者選択方法が「単一」 の場合
    if (newValue === OrX0130Const.DEFAULT.TANI) {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width:332px'
      orX0117Oneway.type = Or52060Const.DEFAULT.STR.TWO
      // 利用者一覧テーブルの幅は狭くなる
      userCols.value = 6
      await nextTick()
      // 履歴一覧データ処理
      setHistoryData(localOneway.initPeriodHistoryList)
      // 履歴初期化選択行IDの再設定
      orX0128Oneway.initSelectId = ''
      if (localOneway.or52060Param.userId) {
        orX0130Oneway.userId = ''
        await nextTick()
        orX0130Oneway.userId = localOneway.or52060Param.userId
        await nextTick()
      }
    } else {
      orX0117Oneway.type = Or52060Const.DEFAULT.STR.ONE
      // 利用者一覧テーブルの幅は広くなる
      userCols.value = 12
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width:500px'
    }
  }
)

/**
 * 履歴選択方法の変更監視
 */
watch(
  () => mo00039HistorySelectType.value,
  async (newValue) => {
    // 履歴選択方法が「単一」 の場合
    if (newValue === '0') {
      orX0117Oneway.type = Or52060Const.DEFAULT.STR.TWO
      orX0128Oneway.singleFlg = OrX0128Const.DEFAULT.TANI
      await nextTick()
      // 履歴一覧データ処理
      setHistoryData(localOneway.initPeriodHistoryList)
      // 履歴初期化選択行IDの再設定
      orX0128Oneway.initSelectId = ''
    } else {
      orX0117Oneway.type = Or52060Const.DEFAULT.STR.ZERO
      orX0128Oneway.singleFlg = OrX0128Const.DEFAULT.HUKUSUU
    }
  }
)

/**
 * 記入用シートを印刷するチェックボックス算出プロパティの監視
 */
watch(
  () => mo00018EntryDisabled.value,
  (newValue) => {
    if (newValue) {
      // 記入用シートを印刷するをチェックオフにする
      mo00018EntryType.value.modelValue = false
      // 以外の場合、非活性表示
      localOneway.mo00018OneWayEntry.disabled = true
    } else {
      // 利用者選択方法が「単一」 また 履歴選択方法が「単一」の場合、活性表示。
      localOneway.mo00018OneWayEntry.disabled = false
    }
  },
  { immediate: true }
)

/**
 * 担当ケアマネ選択アイコンボタン算出プロパティの監視
 */
watch(
  () => orX0145Disabled.value,
  (newValue) => {
    if (newValue) {
      localOneway.orX0145Oneway.disabled = true
    } else {
      localOneway.orX0145Oneway.disabled = false
    }
  },
  { immediate: true }
)

/**
 * 「利用者一覧」明細選択の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue, oldValue) => {
    if (newValue?.clickFlg) {
      // 利用者一覧選択行 >0 の場合
      if (newValue.userList.length > 0) {
        ledgerInfo.selectedUserId = newValue.userList[0].userId

        // 利用者リスト再設定
        ledgerInfo.userList = []
        for (const item of newValue.userList) {
          if (item) {
            ledgerInfo.userList.push({
              userId: item.userId,
              userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
            } as UserEntity)
          }
        }

        // 履歴初期化選択行IDの再設定
        orX0128Oneway.initSelectId = ''

        // 利用者選択方法が「単一」の場合
        if (Or52060Const.DEFAULT.TANI === mo00039UserSelectType.value) {
          if (
            initFlg.value &&
            oldValue?.userList &&
            (oldValue?.userList.length ?? 0) > 0 &&
            !isEqual(newValue.userList, oldValue?.userList)
          ) {
            // 印刷設定履歴リスト取得
            await getPrintSettingsHistoryList()
            // 履歴初期化選択行IDの再設定
            orX0128Oneway.initSelectId = ''
          }
        }
      } else {
        ledgerInfo.userList = []
      }
    } else {
      ledgerInfo.userList = []
    }
  },
  { deep: true }
)

/**
 * 「履歴一覧」明細選択の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    if (!newValue) return
    if (newValue.historyDetClickFlg && newValue.orX0128DetList.length > 0) {
      ledgerInfo.historyList = []
      ledgerInfo.historyList = newValue.orX0128DetList
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
    @update:model-value="topRightClose"
  >
    <template #cardItem>
      <c-v-row
        class="or52060-row"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="2"
          class="table-header"
        >
          <!-- 帳票一覧テーブル -->
          <base-mo-01334
            v-model="mo01334TypeLedger"
            :oneway-model-value="mo01334OnewayLedger"
            class="list-wrapper"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="3"
          class="content-center"
        >
          <c-v-row
            no-gutter
            class="printerOption custom-col or52060-row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pl-2"
            >
              <!-- '基本設定' -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayBasicSettings"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="custom-col or52060-row"
          >
            <c-v-col
              cols="12"
              class="label-left padding-bottom-none pl-2 pt-2 pb-2 pr-2 w-100"
            >
              <!-- 'タイトル' -->
              <base-mo01338
                :oneway-model-value="localOneway.mo00615OneWayTitle"
                class="pb-2"
              ></base-mo01338>
              <!-- 指定された帳票名 -->
              <base-mo00045
                v-model="mo00045TitleType"
                class="w-100"
                :oneway-model-value="localOneway.mo00045OneWay"
              ></base-mo00045>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            no-gutter
            class="custom-col or52060-row"
          >
            <c-v-col
              cols="12"
              sm="7"
              class="py-2"
              style="padding-left: 0px; padding-right: 0px"
            >
              <!-- 日付印刷区分 -->
              <base-mo00039
                v-model="mo00039DatePrintType"
                :oneway-model-value="localOneway.mo00039OneWayDatePrint"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              class="py-2 date-area"
              style="padding-left: 0px; padding-right: 8px"
            >
              <!-- 指定日 -->
              <base-mo00020
                v-if="designationShowFlag"
                v-model="mo00020DesignationType"
                :oneway-model-value="localOneway.mo00020OneWayDesignation"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="printerOption custom-col or52060-row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pl-2"
            >
              <!-- '印刷オプション' -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="custom-col or52060-row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pt-2 pr-2"
              style="padding-bottom: 0px; padding-left: 0px"
            >
              <div>
                <!-- 記入用シートを印刷するチェックボックス -->
                <base-mo00018
                  v-model="mo00018EntryType"
                  :oneway-model-value="localOneway.mo00018OneWayEntry"
                />
                <!-- 記入用シートを印刷するチェックボックス -->
                <!-- 印刷する要介護度ラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayCareRequired"
                  class="pt-2"
                ></base-mo01338>
                <!-- 印刷する要介護度セレクトボックス -->
                <base-mo00040
                  v-model="mo00040CareRequiredType"
                  :oneway-model-value="localOneway.mo00040OneWayCareRequired"
                  class="pl-2"
                />
              </div>

              <div v-if="mo00039ConcreteShowFlg">
                <!-- 具体的内容と対応するケア項目の文字サイズラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayConcrete"
                ></base-mo01338>
                <!-- 具体的内容と対応するケア項目の文字サイズラジオボタン -->
                <base-mo00039
                  v-model="mo00039ConcreteType"
                  :oneway-model-value="localOneway.mo00039OneWayConcrete"
                />
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="7"
          class="pa-0"
        >
          <c-v-row
            class="or52060-row"
            no-gutter
          >
            <c-v-col
              cols="4"
              class="pl-0 pb-0 pr-2"
            >
              <div>
                <!-- 利用者選択ラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                  class="pl-2 pb-0"
                >
                </base-mo01338>
                <!-- 利用者選択方法ラジオボタン -->
                <base-mo00039
                  v-model="mo00039UserSelectType"
                  :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                >
                </base-mo00039>
              </div>
            </c-v-col>
            <c-v-col
              cols="4"
              class="pl-2"
            >
              <div v-if="mo00039UserSelectType !== '1'">
                <!-- 履歴選択ラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                  class="pl-2"
                >
                </base-mo01338>
                <!-- 履歴選択方法ラジオボタン -->
                <base-mo00039
                  v-model="mo00039HistorySelectType"
                  :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                >
                </base-mo00039>
              </div>
              <div
                v-if="mo00039UserSelectType === '1'"
                class="basic-date-label"
              >
                <!-- 年月日選択セクション -->
                <base-mo00020
                  v-model="mo00020BasicType"
                  :oneway-model-value="localOneway.mo00020OneWayBasic"
                />
              </div>
            </c-v-col>
            <c-v-col
              cols="4"
              class="pl-2"
            >
              <div v-if="localOneway.or52060Param.processYmd !== ''">
                <!-- 担当ケアマネプルダウン -->
                <g-custom-or-x-0145
                  v-bind="orX0145"
                  v-model="orX0145Type"
                  :oneway-model-value="localOneway.orX0145Oneway"
                  @update:model-value="orx0145UpdateModelValue"
                />
              </div>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            class="or52060-row"
            no-gutter
          >
            <c-v-col
              :cols="userCols"
              class="user-table-area pa-0 pl-2 pb-2 pt-2"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode && initFlg"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo00039UserSelectType === '0'"
              cols="6"
              class="pa-2"
            >
              <div>
                <!-- 計画期間＆履歴一覧 -->
                <g-custom-or-x-0128
                  v-bind="orX0128"
                  :oneway-model-value="orX0128Oneway"
                ></g-custom-or-x-0128>
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="ml-2 mr-0"
          @click="pdfDownload"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813 v-bind="or21813" />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <!-- Or21815:有機体:警告ダイアログ -->
  <g-base-or-21815 v-bind="or21815" />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
</template>
<style>
.or52060-content {
  padding: 0px !important;
}
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
.or52060-row {
  margin: 0px !important;
}

// 帳票一覧表
.table-header {
  padding: 8px;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}
.content-center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label-left {
    padding-right: 0px;
  }

  .label-right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .custom-col {
    margin-left: 0px;
    margin-right: 0px;
  }
}
.padding-bottom-none {
  padding-bottom: 0px;
}
:deep(.honorifics-margin) {
  margin-left: 8px !important;
  margin-right: 8px !important;
}

// 出力帳票名一覧ヘーダ
:deep(.v-data-table-header__content) {
  font-weight: bold;
  font-size: 14px !important;
}
:deep(.basic-date-label > .v-sheet > .text-date-area-style > .pt-1) {
  padding-left: 8px !important;
  padding-top: 0px !important;
  margin-top: 0px !important;
  margin-bottom: 5px !important;
}

// 記入用シートを印刷するラベルfontweight
:deep(.entry-label .v-label) {
  font-weight: bold !important;
}

// 印刷する要介護度セレクトボックスpadding
:deep(.care-required-padding > .v-col > .v-input) {
  padding-left: 0px !important;
}
// '基本設定'margin
:deep(.ml-4) {
  margin-left: 0px !important;
}
// 担当ケアマネ
:deep(.tanto-label .v-col) {
  height: 18px !important;
}
// 利用者一覧区域
:deep(.user-table-area > .v-row > .v-col) {
  padding: 0px 0px 0px 8px;
}
// 帳票情報表
:deep(.table-header .v-data-table__td) {
  height: 43px !important;
}
// 指定日
:deep(.date-area .v-field__input) {
  padding-left: 12px !important;
}
</style>
