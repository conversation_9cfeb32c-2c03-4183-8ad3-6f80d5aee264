/**
 * GUI01259_支援経過確認一覧
 *
 * <AUTHOR> DAO DINH DUONG
 */
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * 認証情報入力エンティティ
 */
export interface SupportElapsedRecordSelectInEntity extends InWebEntity {
  /** 事業者ID */
  svJigyoId: string

  /** 利用者ID */
  userid: string

  /** 記録開始日 */
  symd: string

  /** 記録終了日 */
  eymd: string

  /** 計画対象年月 */
  taishoYm: string
}

/**
 * 認証情報入力エンティティ
 */
export interface SupportElapsedRecordSelectOutEntity extends OutWebEntity {
  /**
   * 困難度マスタリスト
   */
  data: {
    /** 表示順最大值情報 */
    displayOrderMaxValueInfo: {
      /** 最大値 */
      maxValue: string
    }
      /** 支援経過確認一覧 */
      supportElapsedRecordList: SupportElapsedRecordData[]

  }
}

/**
 * 支援経過確認一覧
 */
export interface SupportElapsedRecordData {
  /** 備考区分 */
  bikoKbn: string

  /** 法人ID */
  houjinId: string

  /** 施設ID */
  shisetuId: string

  /** 記録日 */
  yymmYmd: string

  /** 利用者ID */
  userid: string

  /** レコード番号 */
  recNo: string

  /** 開始時間 */
  timeHh: string

  /** 開始分 */
  timeMm: string

  /** ケース種別 */
  caseCd: string

  /** ケース事項 */
  caseKnj: string

  /** 記入者 */
  staffid: string

  /** ｹｰｽ転記フラグ */
  caseFlg: string

  /** 申し送りフラグ */
  moushiokuriFlg: string

  /** 結果元履歴番号 */
  baseRecNo: string

  /** システム別フラグ */
  systemFlg: string

  /** 指示フラグ */
  shijiFlg: string

  /** 共有区分 */
  kyouyuBikoKbn: string

  /** 褥瘡フラグ */
  jyoFlg: string

  /** ユニークID */
  uniqueId: string

  /** 結果元ユニークID */
  baseUniqueId: string

  /** 更新日時 */
  timeStmp: string

  /** 事業者ID */
  svJigyoId: string

  /** 表示順 */
  seqNo: string

  /** 終了時間 */
  endHh: string

  /** 終了分 */
  endMm: string

  /** 所要時間 */
  totaltime: string

  /** タイトル */
  titleKnj: string

  /** 訪問チェックフラグ */
  houmonFlg: string

  /** 計画書（1）チェックフラグ */
  kkak1Kbn: string

  /** 計画書（2）チェックフラグ */
  kkak2Kbn: string

  /** 週間計画チェックフラグ */
  weekKbn: string

  /** 利用票チェックフラグ */
  riyoKbn: string

  /** 計画対象年月 */
  taishoYm: string

  /** 担当者 */
  tantoId: string

  /** CPSシステム内部ID */
  cpsKeika2Id: string

  /** 履歴変更フラグ */
  modifyFlg: string

  /** 記録者（文字列） */
  shokuKnj: string

  /** 地域支援事業所ID */
  chiJigyoId: string

  /** 種類CD */
  shuruiCd: string
  kkak1KbnPreviousSetingsFlg: string
  kkak2KbnPreviousSetingsFlg: string
  weekKbnPreviousSetingsFlg: string
  riyoKbnPreviousSetingsFlg: string
  modifiedCnt: string

}
