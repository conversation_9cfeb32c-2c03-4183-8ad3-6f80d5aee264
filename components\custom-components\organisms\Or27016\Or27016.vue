<script setup lang="ts">
/**
 * Or27016:有機体:(利用票) カレンダー入力モーダル
 * GUI01158_カレンダー入力
 *
 * @description
 *［カレンダー入力］画面では、サービス利用の予定、実績の日付を登録します。
 *［カレンダー入力］画面は、［ケアマネ］→［利用・提供票］→［利用票］画面などで「カレンダー」ボタンをクリックすると表示されます。
 *
 * <AUTHOR> 沈溢良
 */
import { useI18n } from 'vue-i18n'
import { Or27016Const } from './Or27016.constants'
import type { Or27016StateType, TableData } from './Or27016.type'
import type { Or27016Type, Or27016OnewayType } from '~/types/cmn/business/components/Or27016Type'
import {
  computed,
  onMounted,
  reactive,
  ref,
  useScreenOneWayBind,
  useSetupChildProps,
  watch,
} from '#imports'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or26183Type } from '~/types/cmn/business/components/Or26183Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  CalendarInputInitialSelectInEntity,
  CalendarInputInitialSelectOutEntity,
} from '~/repositories/cmn/entities/CalendarInputInitialSelectEntity'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { CustomClass } from '~/types/CustomClassType'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or27016Type
  onewayModelValue: Or27016OnewayType
  uniqueCpId: string
}
const radioChange = ref('')

const isDoubleClicked = ref(false)
// 引継情報を取得する
const props = defineProps<Props>()
const localOneway = reactive({
  Or27016: {
    ...props.onewayModelValue,
  },

  // 情報ダイアログ
  mo00024Oneway: {
    width: '1400px',
    maxWidth: '1400px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or27016',
      toolbarTitle: t('label.calendar-input-title'),
      toolbarName: 'Or27016ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定ボタン
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  // 適用チェックボックス
  mo00018OnewayType: {
    showItemLabel: false,
    checkboxLabel: '',
    isVerticalLabel: true,
    hideDetails: true,
    outerDivClass: '',
  } as Mo00018OnewayType,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27016StateType>({
  cpId: Or27016Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or27016Const.DEFAULT.IS_OPEN
    },
  },
})
/**
 * 行選択
 *
 * @param id - 選択した行のid
 */
function change(id: string) {
  if (!isDoubleClicked.value) {
    radioChange.value = id
  }
  isDoubleClicked.value = false
}
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27016Const.DEFAULT.IS_OPEN,
})

// 画面.処理モード
const syoriMode = ref<string>()

const or21815 = ref({ uniqueCpId: '' })

// ダイアログ表示フラグ
const showDialog = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(1)]: or21815.value,
})

// CSSに対応するためにboolean型を追加しました
const refreshFlag = ref<boolean>(false)

const weekDays = [
  t('label.weekly-plan-day-short-saturday'),
  t('label.weekly-plan-day-short-sunday'),
  t('label.weekly-plan-day-short-monday'),
  t('label.weekly-plan-day-short-tuesday'),
  t('label.weekly-plan-day-short-wednesday'),
  t('label.weekly-plan-day-short-thursday'),
  t('label.weekly-plan-day-short-friday'),
]

// 提供年月ラベル
const offerYmLabel = ref<Mo00615OnewayType>({
  itemLabel: t('label.offer-ym'),
  showRequiredLabel: false,
})
// 提供年月表示
const provideYmShow = ref<Mo00615OnewayType>({
  itemLabel: localOneway.Or27016.provideYm,
  showRequiredLabel: false,
  itemLabelFontWeight: 'bold',
  itemLabelCustomClass: new CustomClass({ labelStyle: 'font-size:20px' }),
})
// 登録項目ラベル
const insertItemLabel = ref<Mo00615OnewayType>({
  itemLabel: t('label.insert-item'),
  showRequiredLabel: false,
})

// 注記ラベル
const calendarInputNoteFirst: string = t('label.calendar-input-note-first')
const calendarInputNoteSecond: string = t('label.calendar-input-note-second')
const calendarInputNoteThirdLeft: string = t('label.calendar-input-note-third-left')
const calendarInputNoteThirdLeft2: string = t('label.calendar-input-note-third-left2')
const calendarInputNoteThirdRight: string = t('label.calendar-input-note-third-right')

const currentDate = ref(new Date(localOneway.Or27016.provideYm))

// カレンダーデータを作成します。
const calendarWeeks = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  // 休日計算です
  const calculateHolidays = (targetYear: number) => {
    const holidays = new Set()

    // 定休日です
    const fixedDates = [
      [1, 1], // 元旦
      [2, 11], // 建国記念日
      [4, 29], // 昭和の日
      [5, 3], // 憲法記念日
      [5, 4], // みどりの日
      [5, 5], // こどもの日
      [8, 11], // 山の日
      [11, 3], // 文化の日
      [11, 23], // 勤労感謝の日
    ]

    // 祝日を追加します。
    fixedDates.forEach(([m, d]) => {
      holidays.add(`${m}-${d}`)
    })

    // n週目Xの日付を計算します
    const getNthWeekday = (m: number, n: number, weekday: number) => {
      const date = new Date(targetYear, m - 1, 1)
      let count = 0
      while (date.getMonth() === m - 1) {
        if (date.getDay() === weekday && ++count === n) {
          return date.getDate()
        }
        date.setDate(date.getDate() + 1)
      }
      return null
    }

    // 可変休日
    ;[
      [1, 2, 1], // 成人の日 1月の第2月曜日です
      [7, 3, 1], // 海の日 7月の第3月曜日です
      [9, 3, 1], // 敬老の日 9月第3月曜日です
      [10, 2, 1], // スポーツの日 10月の第2月曜日です
    ].forEach(([m, n, wd]) => {
      const d = getNthWeekday(Number(m), Number(n), Number(wd))
      if (d) holidays.add(`${m}-${d}`)
    })

    // 祝日に戻ります。
    return holidays
  }

  // 現在の年の祝日です
  const holidays = calculateHolidays(year)

  // その月の初日です
  const firstDay = new Date(year, month, 1)
  // その月の最終日です
  const lastDay = new Date(year, month + 1, 0)
  // 先月の最後の日です
  const prevLastDay = new Date(year, month, 0)

  // 日付配列を生成します
  const days = []

  // 先月末の日付を添付します。
  for (let i = firstDay.getDay(); i > 0; i--) {
    const date = new Date(year, month - 1, prevLastDay.getDate() - i + 1)
    days.push({
      number: prevLastDay.getDate() - i + 1,
      date,
      isCurrentMonth: false,
      isHoliday: date.getDay() === 0 || holidays.has(`${date.getMonth() + 1}-${date.getDate()}`),
    })
  }

  // 当月の日付を追加します。
  for (let i = 1; i <= lastDay.getDate(); i++) {
    const date = new Date(year, month, i)
    days.push({
      number: i,
      date,
      isCurrentMonth: true,
      isHoliday: date.getDay() === 0 || holidays.has(`${month + 1}-${i}`),
    })
  }

  // 来月の初めの日付を書きます。
  const nextDays = 7 - (days.length % 7)
  for (let i = 1; i <= nextDays; i++) {
    const date = new Date(year, month + 1, i)
    days.push({
      number: i,
      date,
      isCurrentMonth: false,
      isHoliday: date.getDay() === 0 || holidays.has(`${date.getMonth() + 1}-${i}`),
    })
  }

  // 週ごとに分割します
  const weeks = []
  for (let i = 0; i < days.length; i += 7) {
    weeks.push(days.slice(i, i + 7))
  }
  setYoubiDays(days)
  console.log(weeks, '------------')
  return weeks
})

const defaultModelValue: Or26183Type = {
  value: localOneway.Or27016.provideYm,
}

const local = reactive({
  or27016: {
    ...props.modelValue,
  },
  Or26183: {
    ...defaultModelValue,
  },
  // 登録項目
  insertItemList: [] as CodeType[],
  dayOfWeek0: false,
  dayOfWeek1: false,
  dayOfWeek2: false,
  dayOfWeek3: false,
  dayOfWeek4: false,
  dayOfWeek5: false,
  dayOfWeek6: false,
  dayOfWeek7: false,
  selectedDateList: [] as number[],
  weekOfDay0: [] as number[],
  weekOfDay1: [] as number[],
  weekOfDay2: [] as number[],
  weekOfDay3: [] as number[],
  weekOfDay4: [] as number[],
  weekOfDay5: [] as number[],
  weekOfDay6: [] as number[],
  weekOfDay7: [] as number[],
  rowSelList: [] as boolean[],
})

// カレンダー入力初期情報
const tableData = ref<TableData>({
  meisaiList: [],
  holidayList: [],
})

const tableDataFilter = computed(() => {
  const meisaiList = tableData.value.meisaiList
  return { meisaiList: meisaiList }
})

const selectedRows = ref<boolean[]>([])
selectedRows.value = new Array<boolean>(tableData.value.meisaiList.length).fill(false)
selectedRows.value[Number(localOneway.Or27016.currentRow)] = true

const dateHeader = reactive({
  headers: [
    // 行選択
    {
      title: '',
      key: 'dmySel',
      sortable: false,
      width: '10px',
    },
    // 事業所
    {
      title: t('label.kotobimisho'),
      align: 'start',
      key: 'jigyoRyakuKnj',
      sortable: false,
      width: '160px',
    },
    // サービス
    {
      title: t('label.service'),
      align: 'start',
      key: 'service',
      sortable: false,
      width: '120px',
    },
    // 時間帯
    {
      title: t('label.time-zone-label'),
      align: 'start',
      key: 'timeZone',
      sortable: false,
      width: '80px',
    },
    // 回数
    {
      title: t('label.number-of-times'),
      align: 'start',
      key: 'numberTimes',
      sortable: false,
      width: '30px',
    },
  ],
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await init()
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 処理モード
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PROCESS_MODE },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 処理モード
  local.insertItemList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_PROCESS_MODE)
}

/**
 * AC001_初期表示
 */
async function init() {
  local.insertItemList = []
  // 共通関数より汎用コード一覧を取得する。
  await initCodes()
  // カレンダー入力初期情報を取得する。
  const inputData: CalendarInputInitialSelectInEntity = {
    /** 提供年月 */
    yymmYm: localOneway.Or27016.provideYm,
    /** カレント行 */
    currentrow: localOneway.Or27016.currentRow,
    /** 変更日 */
    yymmD: localOneway.Or27016.updateDate,
    /** 利用票明細リスト */
    meisaiList: local.or27016.meisaiList,
  }
  // カレンダー入力初期情報取得
  const retData: CalendarInputInitialSelectOutEntity = await ScreenRepository.select(
    'calendarInputInitialSelect',
    inputData
  )
  // 親画面.予定・実績 = 1の場合
  if (localOneway.Or27016.scheduleOrReal === '1') {
    // 画面.処理モード = 0に設定
    syoriMode.value = Or27016Const.SYORI_MODE_ZERO
    // 上記以外の場合
  } else {
    // 画面.処理モード = 1に設定
    syoriMode.value = Or27016Const.SYORI_MODE_ONE
  }

  // 戻り値はテーブルデータとして処理されます
  // 利用票リスト
  tableData.value.meisaiList = retData.data.meisaiList.map((item, index) => {
    const rowNumber = (index + 1).toString()
    return {
      ...item,
      id: rowNumber,
      dmySel: rowNumber,
    }
  })

  if (tableData.value.meisaiList.length) {
    change(tableData.value.meisaiList[0].id)
  }
  // 休館日・休業日リスト
  tableData.value.holidayList = retData.data.holidayList

  getSelectRow(Number(localOneway.Or27016.currentRow))
  // CSSに対応するためにboolean型を追加しました
  refreshFlag.value = true
  // CSSに対応するためにboolean型を追加しました
  refreshFlag.value = false
}

watch(
  () => local.selectedDateList.length,
  () => {
    if (syoriMode.value === Or27016Const.SYORI_MODE_ZERO) {
      if (selectedRows.value.includes(true)) {
        tableData.value.meisaiList[selectedRows.value.indexOf(true)].ytotal =
          local.selectedDateList.length.toString()
      }
    } else {
      if (selectedRows.value.includes(true)) {
        tableData.value.meisaiList[selectedRows.value.indexOf(true)].jtotal =
          local.selectedDateList.length.toString()
      }
    }
    if (local.weekOfDay0.every((day) => local.selectedDateList.includes(day))) {
      local.dayOfWeek0 = true
    } else {
      local.dayOfWeek0 = false
    }
    if (local.weekOfDay1.every((day) => local.selectedDateList.includes(day))) {
      local.dayOfWeek1 = true
    } else {
      local.dayOfWeek1 = false
    }
    if (local.weekOfDay2.every((day) => local.selectedDateList.includes(day))) {
      local.dayOfWeek2 = true
    } else {
      local.dayOfWeek2 = false
    }
    if (local.weekOfDay3.every((day) => local.selectedDateList.includes(day))) {
      local.dayOfWeek3 = true
    } else {
      local.dayOfWeek3 = false
    }
    if (local.weekOfDay4.every((day) => local.selectedDateList.includes(day))) {
      local.dayOfWeek4 = true
    } else {
      local.dayOfWeek4 = false
    }
    if (local.weekOfDay5.every((day) => local.selectedDateList.includes(day))) {
      local.dayOfWeek5 = true
    } else {
      local.dayOfWeek5 = false
    }
    if (local.weekOfDay6.every((day) => local.selectedDateList.includes(day))) {
      local.dayOfWeek6 = true
    } else {
      local.dayOfWeek6 = false
    }
    if (local.weekOfDay7.every((day) => local.selectedDateList.includes(day))) {
      local.dayOfWeek7 = true
    } else {
      local.dayOfWeek7 = false
    }

    const weeks = calendarWeeks.value
    for (let i = 0; i < weeks.length; i++) {
      if (
        weeks[i].every((day) => local.selectedDateList.includes(day.number) || !day.isCurrentMonth)
      ) {
        local.rowSelList[i] = true
      } else {
        local.rowSelList[i] = false
      }
    }
  }
)
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * AC004_カレンダーをクリック
 */
function selectDate(day: { number: number; date: Date; isCurrentMonth: boolean }) {
  if (!day.isCurrentMonth) {
    return
  }
  if (local.selectedDateList.includes(day.number)) {
    local.selectedDateList = local.selectedDateList.filter((item) => item !== day.number)
    updateMeisaiList(day.number, '0')
  } else {
    local.selectedDateList.push(day.number)
    updateMeisaiList(day.number, '1')
  }
  // CSSに対応するためにboolean型を追加しました
  refreshFlag.value = true
  // CSSに対応するためにboolean型を追加しました
  refreshFlag.value = false
}

/**
 * setYoubiDays
 *
 * @param days - days
 */
function setYoubiDays(days: { number: number; date: Date; isCurrentMonth: boolean }[]) {
  for (let i = 0; i < days.length; i++) {
    if (days[i].isCurrentMonth) {
      local.weekOfDay0.push(days[i].number)
      if (i % 7 === 1) {
        local.weekOfDay1.push(days[i].number)
      }
      if (i % 7 === 2) {
        local.weekOfDay2.push(days[i].number)
      }
      if (i % 7 === 3) {
        local.weekOfDay3.push(days[i].number)
      }
      if (i % 7 === 4) {
        local.weekOfDay4.push(days[i].number)
      }
      if (i % 7 === 5) {
        local.weekOfDay5.push(days[i].number)
      }
      if (i % 7 === 6) {
        local.weekOfDay6.push(days[i].number)
      }
      if (i % 7 === 0) {
        local.weekOfDay7.push(days[i].number)
      }
    }
  }
}

/**
 * updateMeisaiList
 *
 * @param number -number
 *
 * @param value -value
 */
function updateMeisaiList(number: number, value: string) {
  const meisaiList = tableData.value.meisaiList
  const index = selectedRows.value.indexOf(true)
  if (syoriMode.value === Or27016Const.SYORI_MODE_ZERO) {
    if (number === 1) meisaiList[index].yday01 = value
    if (number === 2) meisaiList[index].yday02 = value
    if (number === 3) meisaiList[index].yday03 = value
    if (number === 4) meisaiList[index].yday04 = value
    if (number === 5) meisaiList[index].yday05 = value
    if (number === 6) meisaiList[index].yday06 = value
    if (number === 7) meisaiList[index].yday07 = value
    if (number === 8) meisaiList[index].yday08 = value
    if (number === 9) meisaiList[index].yday09 = value
    if (number === 10) meisaiList[index].yday10 = value
    if (number === 11) meisaiList[index].yday11 = value
    if (number === 12) meisaiList[index].yday12 = value
    if (number === 13) meisaiList[index].yday13 = value
    if (number === 14) meisaiList[index].yday14 = value
    if (number === 15) meisaiList[index].yday15 = value
    if (number === 16) meisaiList[index].yday16 = value
    if (number === 17) meisaiList[index].yday17 = value
    if (number === 18) meisaiList[index].yday18 = value
    if (number === 19) meisaiList[index].yday19 = value
    if (number === 20) meisaiList[index].yday20 = value
    if (number === 21) meisaiList[index].yday21 = value
    if (number === 22) meisaiList[index].yday22 = value
    if (number === 23) meisaiList[index].yday23 = value
    if (number === 24) meisaiList[index].yday24 = value
    if (number === 25) meisaiList[index].yday25 = value
    if (number === 26) meisaiList[index].yday26 = value
    if (number === 27) meisaiList[index].yday27 = value
    if (number === 28) meisaiList[index].yday28 = value
    if (number === 29) meisaiList[index].yday29 = value
    if (number === 30) meisaiList[index].yday30 = value
    if (number === 31) meisaiList[index].yday31 = value
  } else {
    if (number === 1) meisaiList[index].jday01 = value
    if (number === 2) meisaiList[index].jday02 = value
    if (number === 3) meisaiList[index].jday03 = value
    if (number === 4) meisaiList[index].jday04 = value
    if (number === 5) meisaiList[index].jday05 = value
    if (number === 6) meisaiList[index].jday06 = value
    if (number === 7) meisaiList[index].jday07 = value
    if (number === 8) meisaiList[index].jday08 = value
    if (number === 9) meisaiList[index].jday09 = value
    if (number === 10) meisaiList[index].jday10 = value
    if (number === 11) meisaiList[index].jday11 = value
    if (number === 12) meisaiList[index].jday12 = value
    if (number === 13) meisaiList[index].jday13 = value
    if (number === 14) meisaiList[index].jday14 = value
    if (number === 15) meisaiList[index].jday15 = value
    if (number === 16) meisaiList[index].jday16 = value
    if (number === 17) meisaiList[index].jday17 = value
    if (number === 18) meisaiList[index].jday18 = value
    if (number === 19) meisaiList[index].jday19 = value
    if (number === 20) meisaiList[index].jday20 = value
    if (number === 21) meisaiList[index].jday21 = value
    if (number === 22) meisaiList[index].jday22 = value
    if (number === 23) meisaiList[index].jday23 = value
    if (number === 24) meisaiList[index].jday24 = value
    if (number === 25) meisaiList[index].jday25 = value
    if (number === 26) meisaiList[index].jday26 = value
    if (number === 27) meisaiList[index].jday27 = value
    if (number === 28) meisaiList[index].jday28 = value
    if (number === 29) meisaiList[index].jday29 = value
    if (number === 30) meisaiList[index].jday30 = value
    if (number === 31) meisaiList[index].jday31 = value
  }
}

/**
 * rowSelected
 *
 * @param index -index
 */
function rowSelected(index: number) {
  const selectDay = []
  const unSelectDay = []
  let isAllSelected = true
  for (const day of calendarWeeks.value[index]) {
    if (day.isCurrentMonth) {
      if (local.selectedDateList.includes(day.number)) {
        selectDay.push(day)
      } else {
        unSelectDay.push(day)
        isAllSelected = false
      }
    }
  }
  if (isAllSelected) {
    for (const day of selectDay) {
      selectDate(day)
    }
  } else {
    for (const day of unSelectDay) {
      selectDate(day)
    }
  }
}

/**
 * 予定・実績切り替える
 *
 * @param item -item
 */
function clickradio(item: string) {
  if (item === syoriMode.value) {
    return true
  }
  return false
}

/**
 * 予定・実績切り替える
 */
function changeradio() {
  getSelectRow(selectedRows.value.indexOf(true))
}
/**
 * onChangeDay
 *
 * @param dayOfWeek -dayOfWeek
 */
function onChangeDay(dayOfWeek: number) {
  const weeks = calendarWeeks.value
  const selectDay = []
  const unSelectDay = []
  let isAllSelected = true
  if (dayOfWeek === 0) {
    for (const week of weeks) {
      for (const day of week) {
        if (day.isCurrentMonth) {
          if (local.selectedDateList.includes(day.number)) {
            selectDay.push(day)
          } else {
            unSelectDay.push(day)
            isAllSelected = false
          }
        }
      }
    }
    // 月チェックボックス
  } else if (dayOfWeek === 1) {
    for (const week of weeks) {
      for (let i = 0; i < week.length; i++) {
        if (i % 7 === 1 && week[i].isCurrentMonth) {
          if (local.selectedDateList.includes(week[i].number)) {
            selectDay.push(week[i])
          } else {
            unSelectDay.push(week[i])
            isAllSelected = false
          }
        }
      }
    }
    // 火チェックボックス
  } else if (dayOfWeek === 2) {
    for (const week of weeks) {
      for (let i = 0; i < week.length; i++) {
        if (i % 7 === 2 && week[i].isCurrentMonth) {
          if (local.selectedDateList.includes(week[i].number)) {
            selectDay.push(week[i])
          } else {
            unSelectDay.push(week[i])
            isAllSelected = false
          }
        }
      }
    }
    // 水チェックボックス
  } else if (dayOfWeek === 3) {
    for (const week of weeks) {
      for (let i = 0; i < week.length; i++) {
        if (i % 7 === 3 && week[i].isCurrentMonth) {
          if (local.selectedDateList.includes(week[i].number)) {
            selectDay.push(week[i])
          } else {
            unSelectDay.push(week[i])
            isAllSelected = false
          }
        }
      }
    }
    // 木チェックボックス
  } else if (dayOfWeek === 4) {
    for (const week of weeks) {
      for (let i = 0; i < week.length; i++) {
        if (i % 7 === 4 && week[i].isCurrentMonth) {
          if (local.selectedDateList.includes(week[i].number)) {
            selectDay.push(week[i])
          } else {
            unSelectDay.push(week[i])
            isAllSelected = false
          }
        }
      }
    }
    // 金チェックボックス
  } else if (dayOfWeek === 5) {
    for (const week of weeks) {
      for (let i = 0; i < week.length; i++) {
        if (i % 7 === 5 && week[i].isCurrentMonth) {
          if (local.selectedDateList.includes(week[i].number)) {
            selectDay.push(week[i])
          } else {
            unSelectDay.push(week[i])
            isAllSelected = false
          }
        }
      }
    }
    // 土チェックボックス
  } else if (dayOfWeek === 6) {
    for (const week of weeks) {
      for (let i = 0; i < week.length; i++) {
        if (i % 7 === 6 && week[i].isCurrentMonth) {
          if (local.selectedDateList.includes(week[i].number)) {
            selectDay.push(week[i])
          } else {
            unSelectDay.push(week[i])
            isAllSelected = false
          }
        }
      }
    }
    // 日チェックボックス
  } else if (dayOfWeek === 7) {
    for (const week of weeks) {
      for (let i = 0; i < week.length; i++) {
        if (i % 7 === 0 && week[i].isCurrentMonth) {
          if (local.selectedDateList.includes(week[i].number)) {
            selectDay.push(week[i])
          } else {
            unSelectDay.push(week[i])
            isAllSelected = false
          }
        }
      }
    }
  }
  if (isAllSelected) {
    for (const day of selectDay) {
      selectDate(day)
    }
  } else {
    for (const day of unSelectDay) {
      selectDate(day)
    }
  }
}

let scheduledList = [] as number[]
let realList = [] as number[]

/**
 * getSelectRow
 *
 * @param currentRow -currentRow
 */
function getSelectRow(currentRow: number) {
  const meisaiList = tableData.value.meisaiList[currentRow]
  scheduledList = []
  realList = []
  local.selectedDateList = []
  if (Number(meisaiList.yday01) > 0) {
    scheduledList.push(1)
  }
  if (Number(meisaiList.yday02) > 0) {
    scheduledList.push(2)
  }
  if (Number(meisaiList.yday03) > 0) {
    scheduledList.push(3)
  }
  if (Number(meisaiList.yday04) > 0) {
    scheduledList.push(4)
  }
  if (Number(meisaiList.yday05) > 0) {
    scheduledList.push(5)
  }
  if (Number(meisaiList.yday06) > 0) {
    scheduledList.push(6)
  }
  if (Number(meisaiList.yday07) > 0) {
    scheduledList.push(7)
  }
  if (Number(meisaiList.yday08) > 0) {
    scheduledList.push(8)
  }
  if (Number(meisaiList.yday09) > 0) {
    scheduledList.push(9)
  }
  if (Number(meisaiList.yday10) > 0) {
    scheduledList.push(10)
  }
  if (Number(meisaiList.yday11) > 0) {
    scheduledList.push(11)
  }
  if (Number(meisaiList.yday12) > 0) {
    scheduledList.push(12)
  }
  if (Number(meisaiList.yday13) > 0) {
    scheduledList.push(13)
  }
  if (Number(meisaiList.yday14) > 0) {
    scheduledList.push(14)
  }
  if (Number(meisaiList.yday15) > 0) {
    scheduledList.push(15)
  }
  if (Number(meisaiList.yday16) > 0) {
    scheduledList.push(16)
  }
  if (Number(meisaiList.yday17) > 0) {
    scheduledList.push(17)
  }
  if (Number(meisaiList.yday18) > 0) {
    scheduledList.push(18)
  }
  if (Number(meisaiList.yday19) > 0) {
    scheduledList.push(19)
  }
  if (Number(meisaiList.yday20) > 0) {
    scheduledList.push(20)
  }
  if (Number(meisaiList.yday21) > 0) {
    scheduledList.push(21)
  }
  if (Number(meisaiList.yday22) > 0) {
    scheduledList.push(22)
  }
  if (Number(meisaiList.yday23) > 0) {
    scheduledList.push(23)
  }
  if (Number(meisaiList.yday24) > 0) {
    scheduledList.push(24)
  }
  if (Number(meisaiList.yday25) > 0) {
    scheduledList.push(25)
  }
  if (Number(meisaiList.yday26) > 0) {
    scheduledList.push(26)
  }
  if (Number(meisaiList.yday27) > 0) {
    scheduledList.push(27)
  }
  if (Number(meisaiList.yday28) > 0) {
    scheduledList.push(28)
  }
  if (Number(meisaiList.yday29) > 0) {
    scheduledList.push(29)
  }
  if (Number(meisaiList.yday30) > 0) {
    scheduledList.push(30)
  }
  if (Number(meisaiList.yday31) > 0) {
    scheduledList.push(31)
  }
  if (Number(meisaiList.jday01) > 0) {
    realList.push(1)
  }
  if (Number(meisaiList.jday02) > 0) {
    realList.push(2)
  }
  if (Number(meisaiList.jday03) > 0) {
    realList.push(3)
  }
  if (Number(meisaiList.jday04) > 0) {
    realList.push(4)
  }
  if (Number(meisaiList.jday05) > 0) {
    realList.push(5)
  }
  if (Number(meisaiList.jday06) > 0) {
    realList.push(6)
  }
  if (Number(meisaiList.jday07) > 0) {
    realList.push(7)
  }
  if (Number(meisaiList.jday08) > 0) {
    realList.push(8)
  }
  if (Number(meisaiList.jday09) > 0) {
    realList.push(9)
  }
  if (Number(meisaiList.jday10) > 0) {
    realList.push(10)
  }
  if (Number(meisaiList.jday11) > 0) {
    realList.push(11)
  }
  if (Number(meisaiList.jday12) > 0) {
    realList.push(12)
  }
  if (Number(meisaiList.jday13) > 0) {
    realList.push(13)
  }
  if (Number(meisaiList.jday14) > 0) {
    realList.push(14)
  }
  if (Number(meisaiList.jday15) > 0) {
    realList.push(15)
  }
  if (Number(meisaiList.jday16) > 0) {
    realList.push(16)
  }
  if (Number(meisaiList.jday17) > 0) {
    realList.push(17)
  }
  if (Number(meisaiList.jday18) > 0) {
    realList.push(18)
  }
  if (Number(meisaiList.jday19) > 0) {
    realList.push(19)
  }
  if (Number(meisaiList.jday20) > 0) {
    realList.push(20)
  }
  if (Number(meisaiList.jday21) > 0) {
    realList.push(21)
  }
  if (Number(meisaiList.jday22) > 0) {
    realList.push(22)
  }
  if (Number(meisaiList.jday23) > 0) {
    realList.push(23)
  }
  if (Number(meisaiList.jday24) > 0) {
    realList.push(24)
  }
  if (Number(meisaiList.jday25) > 0) {
    realList.push(25)
  }
  if (Number(meisaiList.jday26) > 0) {
    realList.push(26)
  }
  if (Number(meisaiList.jday27) > 0) {
    realList.push(27)
  }
  if (Number(meisaiList.jday28) > 0) {
    realList.push(28)
  }
  if (Number(meisaiList.jday29) > 0) {
    realList.push(29)
  }
  if (Number(meisaiList.jday30) > 0) {
    realList.push(30)
  }
  if (Number(meisaiList.jday31) > 0) {
    realList.push(31)
  }
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  if (syoriMode.value === Or27016Const.SYORI_MODE_ZERO) {
    for (const selectDay of scheduledList) {
      const date = new Date(year, month, selectDay)
      selectDate({ number: selectDay, date: date, isCurrentMonth: true })
    }
  }
  if (syoriMode.value === Or27016Const.SYORI_MODE_ONE) {
    for (const selectDay of realList) {
      const date = new Date(year, month, selectDay)
      selectDate({ number: selectDay, date: date, isCurrentMonth: true })
    }
  }
}

/**
 * ラジオボタンクリック時の処理
 *
 * @param index - index
 */
// const rowClick = (index: number) => {
//   if (tableData.value.meisaiList[index].dmyDayJ === Or27016Const.DMYDAYJ_ZERO) {
//     return true
//   }
//   return false
// }

/**
 * ラジオボタンクリック時の処理
 *
 * @param index - index
 *
 * @param id - 選択した行のid
 */
const handleRowClick = (index: number, id: string) => {
  selectedRows.value = new Array<boolean>(tableData.value.meisaiList.length).fill(false)
  selectedRows.value[index] = true
  if (!isDoubleClicked.value) {
    radioChange.value = id
  }
  isDoubleClicked.value = false
  getSelectRow(index)
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 *  「確定ボタン」押下
 */
function onClick_Confirm() {
  rtnDataSet()
}

/**
 *  戻り値設定
 */
function rtnDataSet() {
  // 戻り値設定
  const rtnData: Or27016Type = {
    // 返却情報.提供年月 = "9999/99"で設定
    provideYm: Or27016Const.PROVIDE_YM_RTN,
    // 返却情報.利用票明細情報 = 退避情報.利用票明細リストで設定
    meisaiList: tableData.value.meisaiList,
  }
  // 選択情報値戻り
  emit('update:modelValue', rtnData)
  // カレンダー入力画面を閉じる
  close()
}

/**
 * Or21815のイベントを監視
 *
 * @description
 * またOr21815のボタン押下フラグをリセットする。
 */
watch(
  () => Or21815Logic.event.get(or21815.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      // OK:処理終了。
      return
    } else if (newValue.thirdBtnClickFlg) {
      // いいえ：処理終了
      return
    }
  }
)

/**
 * 実icon 表示/非表示
 *
 * @param day -day
 */
const showFruitIcon = (day: number) => {
  if (tableData.value.meisaiList.length >= 0) {
    const selectedIndex = selectedRows.value.findIndex((item) => item)
    const selectedItem = tableData.value.meisaiList[selectedIndex]
    const key = Or27016Const.J_DAY + day.toString().padStart(2, Or27016Const.SYORI_MODE_ZERO)
    const yDay = selectedItem[key as keyof typeof selectedItem]
    if (parseInt(yDay) > 0) {
      return true
    } else {
      return false
    }
  } else {
    return false
  }
}

/**
 * 予icon 表示/非表示
 *
 * @param day -day
 */
const showYoyoIcon = (day: number) => {
  if (tableData.value.meisaiList.length >= 0) {
    const selectedIndex = selectedRows.value.findIndex((item) => item)
    const selectedItem = tableData.value.meisaiList[selectedIndex]
    const key = Or27016Const.Y_DAY + day.toString().padStart(2, Or27016Const.SYORI_MODE_ZERO)
    const yDay = selectedItem[key as keyof typeof selectedItem]
    if (parseInt(yDay) > 0) {
      return true
    } else {
      return false
    }
  } else {
    return false
  }
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row no-gutters>
        <c-v-col class="firstColCssSet">
          <c-v-data-table
            :headers="dateHeader.headers"
            class="table-wrapper"
            :hide-default-footer="true"
            :items="tableDataFilter.meisaiList"
            fixed-header
            hover
            items-per-page="-1"
          >
            <template #item="{ item, index }">
              <tr
                :class="item.dmySel === radioChange ? 'highlight-row' : ''"
                @click="handleRowClick(index, item.id)"
              >
                <!-- 行選択 -->
                <td style="max-width: 40px">
                  <!-- <base-mo00018
                    v-if="item.dmyDayJ !== Or27016Const.DMYDAYJ_ZERO"
                    :model-value="{ modelValue: selectedRows[index] }"
                    :oneway-model-value="localOneway.mo00018OnewayType"
                    class="checkbox-height d-flex"
                    style="padding: 0 0 0 2px !important"
                    @click.stop="rowClick(index)"
                    @change="handleRowClick(index)"
                  ></base-mo00018> -->
                  <base-at-radio
                    v-if="item.dmyDayJ !== Or27016Const.DMYDAYJ_ZERO"
                    v-model="radioChange"
                    name="radio-27016"
                    :value="item.dmySel"
                    radio-label=""
                    readonly
                    @click.prevent="handleRowClick(index, item.id)"
                  >
                  </base-at-radio>
                </td>
                <!-- 事業所 -->
                <td>
                  <div class="d-flex ellipsis align-start h-64">
                    <span class="four white-space"> {{ item.dmyFormalnameKnj }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="350"
                      :text="item.dmyFormalnameKnj"
                      open-delay="200"
                    />
                  </div>
                </td>
                <!-- サービス -->
                <td>
                  <div class="d-flex ellipsis align-start h-64">
                    <span class="four white-space"> {{ item.dmyFormalnameKnj }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="350"
                      :text="item.dmyFormalnameKnj"
                      open-delay="200"
                    />
                  </div>
                </td>
                <!-- 時間帯 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value:
                        item.svStartTime === Or27016Const.START_TIME_99 ||
                        item.svStartTime === Or27016Const.START_TIME_88 ||
                        item.svStartTime === Or27016Const.START_TIME_77
                          ? ''
                          : item.svStartTime + '～' + item.svEndTime,
                    }"
                    style="width: 100%"
                  />
                </td>
                <!-- 回数 -->
                <td class="pl-4">
                  <base-mo01336
                    :oneway-model-value="{
                      value: syoriMode === Or27016Const.SYORI_MODE_ZERO ? item.yTotal : item.jTotal,
                    }"
                    style="width: 100%"
                  />
                </td>
                <!-- 事業所ID(非表示設定項目)  -->
                <td class="td-svJigyoId">
                  {{ item.svJigyoId }}
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
        <c-v-col class="secondColCssSet"></c-v-col>
        <c-v-col class="thirdColCssSet">
          <c-v-row class="firstRowCssSet">
            <c-v-col
              class="d-flex"
              style="padding: 8px !important"
            >
              <!-- 提供年月ラベル -->
              <base-mo00615 :oneway-model-value="offerYmLabel" />
              <!-- 提供年月表示 -->
              <base-mo00615
                style="margin-left: -30px !important"
                :oneway-model-value="provideYmShow"
              />
              <!-- 登録項目ラベル -->
              <div style="width: 32px !important"></div>
              <base-mo00615 :oneway-model-value="insertItemLabel" />
              <base-mo00039
                v-model="syoriMode"
                :oneway-model-value="{
                  // デフォルト値の設定
                  itemLabel: '',
                  showItemLabel: false,
                  hideDetails: true,
                }"
                style="align-content: center; margin-left: -18px"
              >
                <base-at-radio
                  v-for="(item, index) in local.insertItemList"
                  :key="'or27016-' + index"
                  :name="'or27016-radio-' + index"
                  :radio-label="item.label"
                  :value="item.value"
                  :disabled="localOneway.Or27016.scheduled === Or27016Const.SCHEDULED_ONE"
                  @click.stop="clickradio(item.value)"
                  @change="changeradio()"
                />
              </base-mo00039>
            </c-v-col>
          </c-v-row>
          <div class="calendar">
            <table
              v-if="!refreshFlag"
              class="calendarTable"
            >
              <thead>
                <tr>
                  <th class="calendarTableThTd"></th>
                  <th
                    v-for="(day, index) in weekDays"
                    :key="day"
                    class="calendarTableThTd calendarTableTh"
                    :class="{ sunday: index === 0, saturday: index === 6 }"
                  >
                    {{ day }}
                  </th>
                </tr>
                <tr>
                  <!-- 日曜日チェックボックス -->
                  <th
                    class="calendarTableCheckbox"
                    style="justify-items: anchor-center"
                  >
                    <base-at-checkbox
                      v-model="local.dayOfWeek0"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(0)"
                    />
                  </th>
                  <!-- 日曜日チェックボックス -->
                  <th
                    class="calendarTableCheckbox"
                    style="justify-items: anchor-center"
                  >
                    <base-at-checkbox
                      v-model="local.dayOfWeek7"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(7)"
                    />
                  </th>
                  <!-- 月曜日チェックボックス -->
                  <th
                    class="calendarTableCheckbox"
                    style="justify-items: anchor-center"
                  >
                    <base-at-checkbox
                      v-model="local.dayOfWeek1"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(1)"
                    />
                  </th>
                  <!-- 火曜日チェックボックス -->
                  <th
                    class="calendarTableCheckbox"
                    style="justify-items: anchor-center"
                  >
                    <base-at-checkbox
                      v-model="local.dayOfWeek2"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(2)"
                    />
                  </th>
                  <!-- 水曜日チェックボックス -->
                  <th
                    class="calendarTableCheckbox"
                    style="justify-items: anchor-center"
                  >
                    <base-at-checkbox
                      v-model="local.dayOfWeek3"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(3)"
                    />
                  </th>
                  <!-- 木曜日チェックボックス -->
                  <th
                    class="calendarTableCheckbox"
                    style="justify-items: anchor-center"
                  >
                    <base-at-checkbox
                      v-model="local.dayOfWeek4"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(4)"
                    />
                  </th>
                  <!-- 金曜日チェックボックス -->
                  <th
                    class="calendarTableCheckbox"
                    style="justify-items: anchor-center"
                  >
                    <base-at-checkbox
                      v-model="local.dayOfWeek5"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(5)"
                    />
                  </th>
                  <!-- 土曜日チェックボックス -->
                  <th
                    class="calendarTableCheckbox"
                    style="justify-items: anchor-center"
                  >
                    <base-at-checkbox
                      v-model="local.dayOfWeek6"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(6)"
                    />
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(week, index) in calendarWeeks"
                  :key="index"
                >
                  <td class="calendarTableCheckbox calendarTableTh">
                    <base-mo00018
                      :model-value="{ modelValue: local.rowSelList[index] }"
                      :oneway-model-value="localOneway.mo00018OnewayType"
                      class="padding-zero"
                      @change="rowSelected(index)"
                    ></base-mo00018>
                  </td>
                  <td
                    v-for="(day, number) in week"
                    :key="String(day.date)"
                    :class="{
                      'other-month': !day.isCurrentMonth,
                      sundayTd: day.isHoliday || number === 0,
                      saturdayTd: number === 6,
                      selected: local.selectedDateList.includes(day.number) && day.isCurrentMonth,
                    }"
                    class="calendarTableThTd calendarTableTd"
                    @click="selectDate(day)"
                  >
                    <div class="firstDivCssSet">
                      <div class="secondDivCssSet">
                        <span
                          v-if="day.isCurrentMonth"
                          class="firstSpanCssSet"
                          >{{ day.number }}</span
                        >
                      </div>
                    </div>
                    <div class="thirdDivCssSet">
                      <div v-if="day.isCurrentMonth">
                        <div class="fourthDivCssSet">
                          <span
                            v-if="
                              syoriMode === Or27016Const.SYORI_MODE_ZERO &&
                              showFruitIcon(day.number)
                            "
                            class="secondSpanCssSet"
                            >{{ t('label.fruit') }}</span
                          >
                          <span
                            v-if="
                              syoriMode === Or27016Const.SYORI_MODE_ONE && showYoyoIcon(day.number)
                            "
                            class="thirdSpanCssSet"
                            >{{ t('label.yoyo') }}</span
                          >
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div style="margin-top: 25px; margin-bottom: 35px">
            <div>{{ calendarInputNoteFirst }}</div>
            <div>{{ calendarInputNoteSecond }}</div>
            <div>
              {{
                syoriMode === Or27016Const.SYORI_MODE_ZERO
                  ? calendarInputNoteThirdLeft
                  : calendarInputNoteThirdLeft2
              }}
              <base-at-label
                v-if="syoriMode === Or27016Const.SYORI_MODE_ONE"
                class="thirdSpanCssSet"
                style="justify-items: center"
                :value="t('label.yoyo')"
              />
              <base-at-label
                v-if="syoriMode === Or27016Const.SYORI_MODE_ZERO"
                class="secondSpanCssSet"
                :value="t('label.fruit')"
              />
              {{ calendarInputNoteThirdRight }}
            </div>
          </div>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row
        no-gutters
        style="padding-right: 8px"
      >
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          @click="onClick_Confirm"
        >
          <!--ツールチップ表示："設定を確定します。"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21815:有機体:警告ダイアログ -->
  <g-base-or21815
    v-if="showDialog"
    v-bind="or21815"
  />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
.overflowText {
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}
:deep(.v-checkbox .v-checkbox-btn) {
  min-height: 31px !important;
  height: 31px !important;
  margin: auto;
}
:deep(.calendar) {
  font-family: Arial, sans-serif;
  max-width: 100%;
  margin: 0 auto;
}

.calendarTable {
  width: 100%;
  border-collapse: collapse;
}

.calendarTableTh {
  font-size: 19px;
  font-weight: normal;
  text-align: center;
  background-color: #eeeeee;
}

:deep(th.sunday) {
  color: #ff0000 !important;
}

:deep(th.saturday) {
  color: #0000ff !important;
}

:deep(td.sundayTd) {
  color: #ff0000 !important;
}

:deep(td.saturdayTd) {
  color: #0000ff !important;
}

:deep(td.other-month) {
  background-color: #f8f8f8;
}

.calendarTableThTd {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.calendarTableTd {
  cursor: pointer;
  font-weight: bolder;
  height: 75px !important;
  font-size: 20px;
  text-align: left;
  border: 2px #f8f8f8 solid !important;
}

.calendarTableCheckbox {
  background-color: #f8f8f8;
  border: 2px #f8f8f8 solid !important;
}

:deep(td.selected) {
  background-color: #96c8ff;
  color: white;
}

:deep(button) {
  padding: 8px 16px;
  cursor: pointer;
}

:deep(.txt:disabled) {
  background: #ffffff;
}

:deep(.v-table--fixed-header) {
  position: sticky;
  // ジッタ除去
  top: 0px;
  z-index: 2;
}

.table-wrapper .v-table__wrapper td:first-child {
  padding: 0px !important;
}

.firstColCssSet {
  width: 657px !important;
  max-width: 657px !important;
}

.secondColCssSet {
  width: 32px !important;
  max-width: 32px !important;
}

.thirdColCssSet {
  width: 711px !important;
  max-width: 711px !important;
}

.firstRowCssSet {
  margin-top: 2px;
  margin-bottom: 2px;
  height: 60px !important;
  margin-right: -8px !important;
}

.secondRowCssSet {
  margin: -8px !important;
}

.firstDivCssSet {
  display: flex;
}

.secondDivCssSet {
  text-align: center;
  height: 25px !important;
  width: 75px;
}

.thirdDivCssSet {
  text-align: center;
  height: 50px !important;
  width: 75px;
}

.fourthDivCssSet {
  text-align: center;
  font-weight: normal;
  height: 20px;
  max-height: 20px;
  width: 75px;
}

.firstSpanCssSet {
  display: inline-block;
  vertical-align: top;
  width: 25px;
}

.secondSpanCssSet {
  border-radius: 5px;
  color: #ffffff !important;
  font-size: 13px;
  display: inline-block;
  vertical-align: top;
  background-color: #ff0000 !important;
  height: 20px;
  width: 20px;
  text-align: center;
}

.thirdSpanCssSet {
  border-radius: 5px;
  color: #ffffff !important;
  font-size: 13px;
  display: inline-block;
  vertical-align: top;
  background-color: #0000ff !important;
  height: 20px;
  width: 20px;
  text-align: center;
}

.td-svJigyoId {
  display: none;
}
.gridContent {
  display: grid;
  grid-template-columns: repeat(1, 400px);
}
// 選択した行のCSS
.select-row {
  background: #dbeefe;
}
:deep(.padding-zero .v-col) {
  padding: 0px !important;
}
:deep(.checkbox-height .v-checkbox .v-checkbox-btn) {
  min-height: 31px;
  height: 31px;
}
.d-flex {
  padding: 10px 0px 0px !important;
  white-space: pre;
}
.h-64 {
  height: 64px;
}
.ellipsis {
  .four {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-wrap;
  }
  .two {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-wrap;
  }
}
.holidy {
  color: black;
}
</style>
