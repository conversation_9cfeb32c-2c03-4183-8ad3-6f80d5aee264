<script setup lang="ts">
/**
 * Or26850:有機体:日常生活・介護サービス例マスタ
 * GUI00988_日常生活・介護サービス例マスタ
 *
 * @description
 *
 * <AUTHOR>
 */
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0065Const } from '../OrX0065/OrX0065.constants'
import { Or26850Const } from './Or26850.constants'
import type { AsyncFunction, Or26850StateType } from './Or26850.type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Or26850OnewayType } from '~/types/cmn/business/components/Or26850Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { useScreenOneWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useScreenUtils } from '~/utils/useScreenUtils'
import { useScreenStore } from '~/stores/session/screen'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  Or21814EventType,
  Or21814OnewayType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import type {
  Or21813EventType,
  Or21813StateType,
} from '~/components/base-components/organisms/Or21813/Or21813.type'
import type {
  OrX0065OnewayType,
  OrX0065Type,
  Shousai,
} from '~/types/cmn/business/components/OrX0065Type'
import type {
  ShousaiInfoSelectInEntity,
  ShousaiInfoSelectOutEntity,
} from '~/repositories/cmn/entities/ShousaiInfoSelectEntity'
import type { ShousaiInfoUpdateInEntity } from '~/repositories/cmn/entities/ShousaiInfoUpdateEntity'
import type { NavControlJsonType } from '~/types/business/core/DefinitionJsonType'

const { getChildCpBinds, setChildCpBinds } = useScreenUtils()
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or26850OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or26850OnewayType = {
  tabKbn: '1',
  houjinId: '1',
  shisetuId: '1',
  svJigyoId: '1',
  saveAuthority: 'F',
}

const orX0065Tab1 = ref({ uniqueCpId: '' })

const orX0065Tab2 = ref({ uniqueCpId: '' })

const orX0065Tab3 = ref({ uniqueCpId: '' })

const or21814 = ref({ uniqueCpId: '' })

const or21813 = ref({ uniqueCpId: '' })

// ダイアログ表示フラグ
const showInfoDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
const showErrDialog = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const localOneway = reactive({
  or26850: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 情報ダイアログ
  mo00024Oneway: {
    width: '600px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or26850',
      toolbarTitle: t('label.daily-life-and-nursing-care-services-examples-mst'),
      toolbarName: 'Or26850ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // タブ
  mo00043OneWay: {
    tabItems: [
      { id: Or26850Const.PARENT_TAB.TAB_ID_DAILY_TABLE, title: t('label.daily-table') },
      { id: Or26850Const.PARENT_TAB.TAB_ID_CONTENT, title: t('label.content') },
      {
        id: Or26850Const.PARENT_TAB.TAB_ID_SV,
        title: t('label.daily-life-and-nursing-care-services-examples'),
      },
    ],
    minWidth: '100px',
  } as Mo00043OnewayType,
  // タブ
  mo00043ChildOneWay: {
    tabItems: [
      { id: Or26850Const.TAB.TAB_ID_1, title: t('label.daily-life-activities') },
      {
        id: Or26850Const.TAB.TAB_ID_2,
        title: t('label.nursing-care-services'),
      },
      { id: Or26850Const.TAB.TAB_ID_3, title: t('label.acceptance-home-services') },
    ],
    minWidth: '140px',
  } as Mo00043OnewayType,
  // 閉じる
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 保存
  mo00609OneWay: {
    btnLabel: t('btn.save'),
  } as Mo00609OnewayType,
  // タブ：日常生活活動等タブ
  orX0065Tab1OneWay: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
    tabKbn: Or26850Const.TAB.TAB_ID_1,
  } as OrX0065OnewayType,
  // タブ：介護サービス等タブ
  orX0065Tab2OneWay: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
    tabKbn: Or26850Const.TAB.TAB_ID_2,
  } as OrX0065OnewayType,
  // タブ：受託居宅サービスタブ
  orX0065Tab3OneWay: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
    tabKbn: Or26850Const.TAB.TAB_ID_3,
  } as OrX0065OnewayType,
})

const local = reactive({
  mo00043: { id: Or26850Const.PARENT_TAB.TAB_ID_SV } as Mo00043Type,
  mo00043Child: { id: Or26850Const.TAB.TAB_ID_1 } as Mo00043Type,
  orX0065Tab1: {
    editFlg: false,
  } as OrX0065Type,
  orX0065Tab2: {
    editFlg: false,
  } as OrX0065Type,
  orX0065Tab3: {
    editFlg: false,
  } as OrX0065Type,
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or26850Const.DEFAULT.IS_OPEN,
})
// orX0065Tab1 Ref
const orX0065Tab1Ref = ref<{ tableValidation: AsyncFunction }>()
// orX0065Tab2 Ref
const orX0065Tab2Ref = ref<{ tableValidation: AsyncFunction }>()
// orX0065Tab3 Ref
const orX0065Tab3Ref = ref<{ tableValidation: AsyncFunction }>()

// ナビゲーション制御領域のいずれかの編集フラグがON
useSystemCommonsStore().setShowEditDiscardDialog(useScreenStore().isEditNavControl())
const isEdit = computed(() => {
  return useSystemCommonsStore().getShowEditDiscardDialog
})

// TAB
const isNewTab = { newTab: '', oldTab: local.mo00043Child.id }
const isNewParentTab = { newTab: '', oldTab: local.mo00043.id }
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or26850StateType>({
  cpId: Or26850Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or26850Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0065Const.CP_ID(0)]: orX0065Tab1.value,
  [OrX0065Const.CP_ID(1)]: orX0065Tab2.value,
  [OrX0065Const.CP_ID(2)]: orX0065Tab3.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
})
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    await nextTick()
    if (resolvePromise !== undefined && newValue !== undefined) {
      resolvePromise(newValue)
    }
    return
  }
)

/**
 * Or21813のイベントを監視
 *
 * @description
 * またOr21813のボタン押下フラグをリセットする。
 */
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    await nextTick()
    if (errResolvePromise !== undefined && newValue !== undefined) {
      errResolvePromise(newValue)
    }
    return
  }
)

// メニュー切替
watch(
  () => local.mo00043.id,
  async (newValue) => {
    if (await validTable()) {
      local.mo00043.id = Or26850Const.PARENT_TAB.TAB_ID_SV
      return
    }
    if (
      newValue !== Or26850Const.PARENT_TAB.TAB_ID_SV &&
      isNewParentTab.oldTab === Or26850Const.PARENT_TAB.TAB_ID_SV
    ) {
      isNewParentTab.newTab = newValue
      // 変更がない場合、画面データあるかどうかを判定する。
      // タブを設定に切り替え
      if (
        (!isEditTab1.value && isNewTab.oldTab === Or26850Const.TAB.TAB_ID_1) ||
        (!isEditTab2.value && isNewTab.oldTab === Or26850Const.TAB.TAB_ID_2) ||
        (!isEditTab3.value && isNewTab.oldTab === Or26850Const.TAB.TAB_ID_3)
      ) {
        isNewParentTab.oldTab = Or26850Const.PARENT_TAB.TAB_ID_SV
        return
      } else {
        // 画面入力データ変更があるかどうかを判定する
        if (isNewParentTab.oldTab === Or26850Const.PARENT_TAB.TAB_ID_SV) {
          // 処理終了,タブ切替の阻止
          local.mo00043.id = Or26850Const.PARENT_TAB.TAB_ID_SV
          // 変更がある場合、確認ダイアログを表示する。
          const rs = await confirmMsg()
          if (rs.firstBtnClickFlg) {
            if (await save()) {
              isNewParentTab.oldTab = isNewParentTab.newTab
              local.mo00043.id = isNewParentTab.newTab
            } else {
              // 処理終了,タブ切替の阻止
              return
            }
          } else if (rs.secondBtnClickFlg) {
            isNewParentTab.oldTab = isNewParentTab.newTab
            local.mo00043.id = isNewParentTab.newTab
          } else if (rs.thirdBtnClickFlg) {
            return
          }
        } else {
          isNewParentTab.oldTab = Or26850Const.PARENT_TAB.TAB_ID_SV
        }
      }
    }
  }
)

// メニュー切替
watch(
  () => local.mo00043Child.id,
  async (newValue) => {
    if (await validTable()) {
      local.mo00043Child.id = isNewTab.oldTab
      return
    }
    if (isNewTab.oldTab === newValue) {
      return
    }
    isNewTab.newTab = newValue
    // 変更がない場合、画面データあるかどうかを判定する。
    // タブを設定に切り替え
    if (
      (!isEditTab1.value && isNewTab.oldTab === Or26850Const.TAB.TAB_ID_1) ||
      (!isEditTab2.value && isNewTab.oldTab === Or26850Const.TAB.TAB_ID_2) ||
      (!isEditTab3.value && isNewTab.oldTab === Or26850Const.TAB.TAB_ID_3)
    ) {
      isNewTab.oldTab = newValue
    } else {
      // 画面入力データ変更があるかどうかを判定する
      // 処理終了,タブ切替の阻止
      local.mo00043Child.id = isNewTab.oldTab
      // 変更がある場合、確認ダイアログを表示する。
      const rs = await confirmMsg()
      if (rs.firstBtnClickFlg) {
        if (await save()) {
          isNewTab.oldTab = newValue
          local.mo00043Child.id = isNewTab.newTab
        } else {
          // 処理終了,タブ切替の阻止
          return
        }
      } else if (rs.secondBtnClickFlg) {
        isNewTab.oldTab = newValue
        local.mo00043Child.id = isNewTab.newTab
      } else if (rs.thirdBtnClickFlg) {
        return
      }
    }
  }
)

// ダイアログResolve
let resolvePromise: (value: Or21814EventType) => void

/**
 * 確定メッセージを開きます
 *
 * @param uniqueCpId -uniqueCpId
 *
 * @param state -Or21814OnewayType
 */
function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    resolvePromise = resolve
  })
}

// ダイアログResolve
let errResolvePromise: (value: Or21813EventType) => void

/**
 * エラーメッセージを開きます
 *
 * @param uniqueCpId -uniqueCpId
 *
 * @param state -Or21813StateType
 */
function openErrorDialog(uniqueCpId: string, state: Or21813StateType) {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21813EventType>((resolve) => {
    errResolvePromise = resolve
  })
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  // 画面入力データ変更があるかどうかを判定する
  // 変更がない場合、画面を閉じる。
  if (!isEdit.value) {
    setState({ isOpen: false })
  } else {
    const rs = await confirmMsg()
    if (rs.firstBtnClickFlg) {
      if (localOneway.or26850.saveAuthority === 'T') {
        if (await save()) {
          setState({ isOpen: false })
        } else {
          return
        }
      } else {
        setState({ isOpen: false })
      }
    } else if (rs.secondBtnClickFlg) {
      setState({ isOpen: false })
    } else if (rs.thirdBtnClickFlg) {
      return
    }
  }
}

/**
 * メッセージ処理
 *
 * @description
 * 。
 */
async function confirmMsg() {
  let rs: Promise<Or21814EventType>
  // 画面入力データの変更がある、且つ、共通処理の保存権限がない場合
  if (localOneway.or26850.saveAuthority !== 'T') {
    rs = showOr21814MsgTwoBtn(t('message.i-cmn-11140'))
  } else {
    // 画面入力データの変更がある、且つ、共通処理の保存権限がある
    rs = showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
  }
  return rs
}

/**
 * 保存
 */
async function validTable() {
  await nextTick()
  // 画面入力データ変更があるかどうかを判定する
  if (isEdit.value) {
    // 変更がある場合、処理継続
    let valid
    switch (isNewTab.oldTab) {
      case Or26850Const.TAB.TAB_ID_1:
        // 変更ありのデータ行をINPUT情報として、下記の保存APIを実行する
        valid = await orX0065Tab1Ref.value?.tableValidation()
        break
      case Or26850Const.TAB.TAB_ID_2:
        // 変更ありのデータ行をINPUT情報として、下記の保存APIを実行する
        valid = await orX0065Tab2Ref.value?.tableValidation()
        break
      case Or26850Const.TAB.TAB_ID_3:
        // 変更ありのデータ行をINPUT情報として、下記の保存APIを実行する
        valid = await orX0065Tab3Ref.value?.tableValidation()
        break
      default:
        break
    }
    return !valid
  }
  return false
}

/**
 * 保存
 */
async function save() {
  await nextTick()
  // 画面入力データ変更があるかどうかを判定する
  if (isEdit.value) {
    // 変更がある場合、処理継続
    let dataArray: Shousai[] = []
    let valid
    switch (isNewTab.oldTab) {
      case Or26850Const.TAB.TAB_ID_1:
        // 変更ありのデータ行をINPUT情報として、下記の保存APIを実行する
        dataArray = local.orX0065Tab1.shousaiList.filter(
          (data) => (data.svId === '' && data.updateKbn !== UPDATE_KBN.DELETE) || data.svId !== ''
        )
        valid = await orX0065Tab1Ref.value?.tableValidation()
        break
      case Or26850Const.TAB.TAB_ID_2:
        // 変更ありのデータ行をINPUT情報として、下記の保存APIを実行する
        dataArray = local.orX0065Tab2.shousaiList.filter(
          (data) => (data.svId === '' && data.updateKbn !== UPDATE_KBN.DELETE) || data.svId !== ''
        )
        valid = await orX0065Tab2Ref.value?.tableValidation()
        break
      case Or26850Const.TAB.TAB_ID_3:
        // 変更ありのデータ行をINPUT情報として、下記の保存APIを実行する
        dataArray = local.orX0065Tab3.shousaiList.filter(
          (data) => (data.svId === '' && data.updateKbn !== UPDATE_KBN.DELETE) || data.svId !== ''
        )
        valid = await orX0065Tab3Ref.value?.tableValidation()
        break
      default:
        break
    }

    if (!valid) {
      // 画面入力データの変更がある、且つ、変更データに入力エラーがある場合
      await showOr21813Msg(t('message.e-cmn-41710'))
      return false
    }
    const tmpArr: ShousaiInfoUpdateInEntity['shousaiList'] = []
    for (const item of dataArray) {
      tmpArr.push({
        svId: item.svId,
        houjinId: item.houjinId,
        shisetuId: item.shisetuId,
        svJigyoId: item.svJigyoId,
        dataKbn: item.dataKbn,
        svKnj: item.svKnj,
        seq: item.seq,
        delFlg: item.delFlg,
        updateKbn: item.updateKbn!,
        modifiedCnt: item.modifiedCnt,
      })
    }
    const param: ShousaiInfoUpdateInEntity = {
      shousaiList: tmpArr,
    }
    // 保存権限がある
    if (localOneway.or26850.saveAuthority === 'T') {
      // パターン（グループ）タイトル情報保存
      await ScreenRepository.insert('shousaiInfoUpdate', param)
    }
    // パターン（グループ）情報取得IN)
    const paramInit: ShousaiInfoSelectInEntity = {
      // マスタ区分: 引継情報.マスタ区分
      tabKbn: isNewTab.oldTab,
    }
    // パターン（グループ）情報取得
    const ret: ShousaiInfoSelectOutEntity = await ScreenRepository.select(
      'shousaiInfoSelect',
      paramInit
    )
    switch (isNewTab.oldTab) {
      case Or26850Const.TAB.TAB_ID_1: {
        local.orX0065Tab1.saveResultShousaiList = ret.data.shousaiList
        await nextTick()
        const data = getChildCpBinds(props.uniqueCpId, {
          OrX0065: { cpPath: 'OrX0065', twoWayFlg: true },
        })
        setChildCpBinds(props.uniqueCpId, {
          OrX0065: {
            twoWayValue: data.OrX0065.twoWayBind?.value,
          },
        })
        break
      }
      case Or26850Const.TAB.TAB_ID_2: {
        local.orX0065Tab2.saveResultShousaiList = ret.data.shousaiList
        await nextTick()
        const data = getChildCpBinds(props.uniqueCpId, {
          OrX0065_1: { cpPath: 'OrX0065_1', twoWayFlg: true },
        })
        setChildCpBinds(props.uniqueCpId, {
          OrX0065_1: {
            twoWayValue: data.OrX0065_1.twoWayBind?.value,
          },
        })
        break
      }
      case Or26850Const.TAB.TAB_ID_3: {
        local.orX0065Tab3.saveResultShousaiList = ret.data.shousaiList
        await nextTick()
        const data = getChildCpBinds(props.uniqueCpId, {
          OrX0065_2: { cpPath: 'OrX0065_2', twoWayFlg: true },
        })
        setChildCpBinds(props.uniqueCpId, {
          OrX0065_2: {
            twoWayValue: data.OrX0065_2.twoWayBind?.value,
          },
        })
        break
      }
      default:
        break
    }
  }
  // 変更がない場合、処理終了。
  return true
}

/**
 * メッセージの開閉
 *
 * @param infomsg - Message
 */
async function showOr21814MsgThreeBtn(infomsg: string) {
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: infomsg,
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
  return rs
}

/**
 * メッセージの開閉
 *
 * @param infomsg - Message
 */
async function showOr21814MsgTwoBtn(infomsg: string) {
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: infomsg,
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'blank',
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
  return rs
}

/**
 * メッセージの開閉
 *
 * @param errmsg - Message
 */
async function showOr21813Msg(errmsg: string) {
  const rs = await openErrorDialog(or21813.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.error'),
    // ダイアログテキスト
    dialogText: errmsg,
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.ok'),
    secondBtnType: 'blank',
    thirdBtnType: 'blank',
  })
  return rs
}

// 変更されたリスニングのコンポーネントIDリスト
const tab1WatchedComponents = ref<string[]>([orX0065Tab1.value.uniqueCpId])
const isEditTab1 = computed(() => {
  return isEditNavControl(tab1WatchedComponents.value)
})
// 変更されたリスニングのコンポーネントIDリスト
const tab2WatchedComponents = ref<string[]>([orX0065Tab2.value.uniqueCpId])
const isEditTab2 = computed(() => {
  return isEditNavControl(tab2WatchedComponents.value)
})
// 変更されたリスニングのコンポーネントIDリスト
const tab3WatchedComponents = ref<string[]>([orX0065Tab3.value.uniqueCpId])
const isEditTab3 = computed(() => {
  return isEditNavControl(tab3WatchedComponents.value)
})

/**
 * いずれかの編集フラグがONになっているかチェックする
 *
 * @param components - 変更のリスニングが必要なリスト
 *
 * @returns いずれかの編集有無
 */
function isEditNavControl(components: string[]) {
  const screenStore = useScreenStore()
  const screenNavControl = screenStore.getScreenNavControl<NavControlJsonType>()

  // ナビゲーション制御領域を走査
  for (const key in screenNavControl) {
    if (screenNavControl[key] === screenNavControl.components) {
      // ナビゲーション制御領域 - コンポーネント毎の領域を走査
      for (const cpKey in screenNavControl[key]) {
        if (components.includes(cpKey)) {
          if (screenNavControl[key][cpKey].editFlg) {
            // コンポーネント毎の編集フラグがON
            return true
          }
          if (screenNavControl[key][cpKey].items) {
            // ナビゲーション制御領域 - コンポーネント毎の領域にitemsが存在する場合は走査
            for (const itemKey in screenNavControl[key][cpKey].items) {
              if (screenNavControl[key][cpKey].items[itemKey].editFlg) {
                // コンポーネント - itemsの編集フラグがON
                return true
              }
            }
          }
        }
      }
    }
  }

  return false
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
    style="padding: 0px !important"
  >
    <template #cardItem>
      <c-v-row>
        <c-v-col>
          <base-mo00043
            v-model="local.mo00043"
            :oneway-model-value="localOneway.mo00043OneWay"
          ></base-mo00043>
          <c-v-window v-model="local.mo00043.id">
            <c-v-window-item value="daliyTable"> 日課表 </c-v-window-item>
            <c-v-window-item value="content"> 内容 </c-v-window-item>
            <c-v-window-item value="sv">
              <base-mo00043
                v-model="local.mo00043Child"
                :oneway-model-value="localOneway.mo00043ChildOneWay"
              ></base-mo00043>
              <!-- タブ switch -->
              <c-v-window v-model="local.mo00043Child.id">
                <c-v-window-item value="1">
                  <!-- タブ：日常生活活動等 -->
                  <g-custom-or-x-0065
                    ref="orX0065Tab1Ref"
                    v-model="local.orX0065Tab1"
                    v-bind="orX0065Tab1"
                    :oneway-model-value="localOneway.orX0065Tab1OneWay"
                    :parent-unique-cp-id="props.uniqueCpId"
                  ></g-custom-or-x-0065>
                </c-v-window-item>
                <c-v-window-item value="2">
                  <!-- タブ：介護サービス等 -->
                  <g-custom-or-x-0065
                    ref="orX0065Tab2Ref"
                    v-model="local.orX0065Tab2"
                    v-bind="orX0065Tab2"
                    :oneway-model-value="localOneway.orX0065Tab2OneWay"
                    :parent-unique-cp-id="props.uniqueCpId"
                  ></g-custom-or-x-0065>
                </c-v-window-item>
                <c-v-window-item value="3">
                  <!-- タブ：受託居宅サービス -->
                  <g-custom-or-x-0065
                    ref="orX0065Tab3Ref"
                    v-model="local.orX0065Tab3"
                    v-bind="orX0065Tab3"
                    :oneway-model-value="localOneway.orX0065Tab3OneWay"
                    :parent-unique-cp-id="props.uniqueCpId"
                  ></g-custom-or-x-0065>
                </c-v-window-item>
              </c-v-window>
            </c-v-window-item>
          </c-v-window>
        </c-v-col>
      </c-v-row>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.screen-close')"
          />
        </base-mo00611>
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mx-2"
          @click="save"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.confirm-btn')"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showInfoDialog"
    v-bind="or21814"
  />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showErrDialog"
    v-bind="or21813"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
:deep(.v-col) {
  padding: 8px !important;
}
.v-row {
  margin: -8px;
}
</style>
