import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or03249:有機体:［アセスメント（包括）］入浴
 * ［アセスメント（包括）］入浴
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or03249Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or03249', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * タブID
     */
    export const TAB_ID = '3'

    /**
     * メッセージID
     */
    export const MESSAGE_ID = '入浴'

    /**
     * 改定フラグ H21/4
     */
    export const KAITEI_FLG_H21 = '4'

    /**
     * フラグ R3/4
     */
    export const KAITEI_FLG_R34 = '5'

    /**
     * インプット表示フラグ
     */
    export const INPUT_DISPLAY_FLG_LAST = '1'

    /**
     * インプット表示フラグ
     */
    export const INPUT_DISPLAY_FLG_UNIT = '2'

    /**
     * API返却値チェック：1
     */
    export const API_RESULT_CHECKON = '1'

    /**
     * 画面表示モード：複写
     */
    export const SCREEN_DIAPLAY_MODE_COPY = 'copy'

    /**
     * テーブル初期化データリストタイプ：ケアの内容
     */
    export const TABLE_INIT_DATA_LIST_TYPE_CARE_CONTENT = 3

    /**
     * テーブル初期化データリストタイプ：ケア提供場所「入力欄抜く」の場合
     */
    export const TABLE_INIT_DATA_LIST_TYPE_CARE_LOCATION_NO_INPUT = 1

    /**
     * テーブル初期化データリストタイプ：ケア提供場所
     */
    export const TABLE_INIT_DATA_LIST_TYPE_CARE_LOCATION = 2

    /**
     * 各データデフォルト値
     */
    export const DATA_DEFAULT = '0'

    /**
     * チェックボックス:チェックイン値
     */
    export const CHECKBOX_CHECK_IN_VALUE = '1'

    /**
     * チェックボックスチェックオフ値
     */
    export const CHECKBOX_CHECK_OFF_VALUE = '0'
    /**
     * メニュー3名称
     */
    export const MENU3_NAME = '[mnu3][3GK][包括]ｱｾｽﾒﾝﾄ'

    /**
     * システム略称
     */
    export const SYSTEM_ACRONYM = '3GK'

    /**
     * セクション名称
     */
    export const SECTION_NAME = 'ケアチェック表'
  }
}
