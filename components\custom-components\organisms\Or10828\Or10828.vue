<script setup lang="ts">
/**
 * Or10828:有機体:(日課表取込)ダイアログ
 * GUI00981_日課表取込
 *
 * @description
 * 日課表取込画面
 *
 * <AUTHOR>
 */

import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28551Const } from '../Or28551/Or28551.constants'
import { Or28452Const } from '../Or28452/Or28452.constants'
import { Or28452Logic } from '../Or28452/Or28452.logic'
import { Or28453Const } from '../Or28453/Or28453.constants'
import { Or28453Logic } from '../Or28453/Or28453.logic'
import { OrX0070Const } from '../OrX0070/OrX0070.constants'
import { OrX0070Logic } from '../OrX0070/OrX0070.logic'
import { Or10828Const } from './Or10828.constants'
import type { Or10828StateType } from './Or10828.type'
import { useScreenOneWayBind, useSetupChildProps } from '#build/imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type {Or10828Type, Or10828OnewayType} from '~/types/cmn/business/components/Or10828Type'
import type { PlanPeriodListItem, Or28452OneWayType} from '~/types/cmn/business/components/Or28452Type'
import type {HistoryListItem, Or28453OneWayType } from '~/types/cmn/business/components/Or28453Type'
import type {DailyTableListItem, OrX0070OneWayType } from '~/types/cmn/business/components/OrX0070Type'
import type {
  DailyscheduleImportInitSelectInEntity,
  DailyscheduleImportInitSelectOutEntity,
} from '~/repositories/cmn/entities/DailyscheduleImportInitSelectEntity'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { useScreenUtils } from '~/utils/useScreenUtils'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import type { Mo01334Headers ,Mo01334Items} from '~/types/business/components/Mo01334Type'
/**
 * setChildCpBinds
 */
const { setChildCpBinds } = useScreenUtils()

/**
 * useI18n
 */
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or10828Type
  uniqueCpId: string
  onewayModelValue?: Or10828OnewayType
}

/**
 * props
 */
const props = defineProps<Props>()

/**************************************************
 * Emit
 **************************************************/
/**
 * 双方向バインドModelValue
 */
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
/**
 * 双方向バインド
 */
const local = reactive({
  // 本画面
  or10828: {
    ...props.modelValue,
  },
  // 本画面ダイアログ
  mo00024: {
    isOpen: Or28551Const.DEFAULT.IS_OPEN,
  } as Mo00024Type,
  // 計画期間一覧
  or28452: {
    selectedRowId: {
      value: '-1',
      values: []
    }
  },
  // 履歴一覧
  or28453: {
    selectedRowId: {
      value: '-1',
      values: []
    }
  },
  // 日課表明細一覧
  orX0070: {
    selectedRowId: {
      value: '-1',
      values: []
    }
  },
})
/**
 * ローカルOneway
 */
const localOneway = reactive({
  // 本画面
  or10828Oneway: {
    ...props.onewayModelValue,
  },
  or28452OneWay: {
    headers: [],
    items:[]
  } as Or28452OneWayType,
  or28453OneWay: {
    headers: [],
    items:[],
  } as Or28453OneWayType,
  orX0070OneWay: {
    headers: [] as Mo01334Headers[],
    items: [] as Mo01334Items[],
    oraggeItems : [] as Mo01334Items[],
    selectList: [] as CodeType[],
    dailyImportSelected: { modelValue: '' } as Mo00040Type,
  } as OrX0070OneWayType,
  // 本画面ダイアログOneway
  mo00024Oneway: {
    width: '1104px',
    height:'600px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10828',
      toolbarTitle: t('label.daily-schedule-import'),
      toolbarTitleCenteredFlg: false,
      toolbarName: 'Or10828ToolBar',
      showCardActions: true,
    },
  },
  // 閉じるボタン
  footerClosebtnOneway: {
    btnLabel: t('btn.close'),
  },
  // 確定ボタン
  footerOkbtnOneway: {
    btnLabel: t('btn.confirm'),
  },
})

/**
 * バックアップデータ
 */
const originData = {
  // 計画期間一覧
  planPeriodListItems: [] as PlanPeriodListItem[],
  // 履歴一覧
  historyListItems : [] as HistoryListItem[],
  orX0070: {
    items: [] as DailyTableListItem[],
    isAllSelected: false,
    importKbn: '',
    dailyImportSelected: { modelValue: '' } as Mo00040Type,
  },
}
/**
 * 有機体:(課題・目標取込)計画期間選択一覧
 */
const or28452_1 = ref({ uniqueCpId: '' })
/**
 * 有機体:(課題・目標取込)履歴選択一覧
 */
const or28453_1 = ref({ uniqueCpId: '' })
/**
 * 有機体:(日課表取込)日課表明細一覧
 */
const orX0070_1 = ref({ uniqueCpId: '' })
/**
 * 確認ダイアログ
 */
const or21814_1 = ref({ uniqueCpId: '' })
/**
 * 警告ダイアログ
 */
const or21815_1 = ref({ uniqueCpId: '' })

/**
 *計画期間情報選択行データ設定
 */
const selectedId_planPeriod = ref<string | null>(null)
/**
 * 履歴情報選択行データ設定
 */
const selectedId_hisotry = ref<string | null>(null)
/**************************************************
 * Pinia
 **************************************************/
/**
 * OneWayBind領域に関する処理
 */
const { setState } = useScreenOneWayBind<Or10828StateType>({
  cpId: Or10828Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 開閉フラグ
     *
     * @param value - 開閉フラグ
     */
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or10828Const.DEFAULT.IS_OPEN
    },
  },
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or28452Const.CP_ID(1)]: or28452_1.value,
  [Or28453Const.CP_ID(1)]: or28453_1.value,
  [OrX0070Const.CP_ID(1)]: orX0070_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
})
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // コントロール設定
  initContorls()
  // 初期情報取得
  await getInitDataInfo()
  await initCodes()
  setChildCpBindsInfo();

  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10215', [
        t('label.life-after-discharge-from-hospital-intention'),
      ]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })

  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.w-cmn-20791'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
})

/**************************************************
 * ウォッチャー
 **************************************************/
// 画面開閉フラグ
watch(
  () => local.mo00024.isOpen,
  () => {
    setState({ isOpen: local.mo00024.isOpen })
  }
)

/**************************************************
 * 関数
 **************************************************/
/**
 *  初期情報取得
 */
async function getInitDataInfo() {
  local.orX0070.selectedRowId = {
    value: '',
    values: []
  }
  const inputData: DailyscheduleImportInitSelectInEntity = {
    svJigyoId: localOneway.or10828Oneway.dailyscheduleImportType!.jigyoId,
    syubetsuId: localOneway.or10828Oneway.dailyscheduleImportType!.shisetsuId,
    userId: localOneway.or10828Oneway.dailyscheduleImportType!.userId,
    shisetuId: localOneway.or10828Oneway.dailyscheduleImportType!.shubetsuId,
    kikanFlag: localOneway.or10828Oneway.dailyscheduleImportType!.kikanFlag,
  }
  // 初期情報取得
  const resData: DailyscheduleImportInitSelectOutEntity = await ScreenRepository.select(
    'dailyscheduleImportInitSelect',
    inputData
  )
  // 計画期間情報設定
  planPeriodInfoSet(resData)
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 絞込名称の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_IMPORT_DATA },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  localOneway.orX0070OneWay.selectList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_IMPORT_DATA
  )

  localOneway.orX0070OneWay.dailyImportSelected.modelValue = localOneway.orX0070OneWay.selectList[0].value
}
/**
 *  計画期間情報リスト設定
 *
 * @param resData - 計画期間情報
 */
const planPeriodInfoSet = (resData: DailyscheduleImportInitSelectOutEntity) => {
  const dataInfo = resData.data
  if (dataInfo) {
    originData.planPeriodListItems = []
    // 計画期間情報取得
    dataInfo.taishokikanList.forEach((item) => {
      localOneway.or28452OneWay.items.push({
        id: item.sc1Id,
        planPeriod: item.startYmd + Or10828Const.DEFAULT.WAVE_LINE + item.endYmd,
        numberOfWithinThePeriodHistory: item.dmyCnt,
      })
      originData.planPeriodListItems.push({
        periodId: item.sc1Id,
        planPeriod: item.startYmd + Or10828Const.DEFAULT.WAVE_LINE + item.endYmd,
        numberOfWithinThePeriodHistory: item.dmyCnt,
      })
    })

    if (dataInfo.historyList) {
      originData.historyListItems = []
      // 履歴情報バックアップ
      dataInfo.historyList.forEach((item) => {
        originData.historyListItems.push({
          periodId: item.sc1Id,
          histroyId: item.day1Id,
          createDate: item.createYmd,
          createdUser: item.shokuKnj,
          caseNo: item.caseNo,
          revision: item.revision,
        })
      })
    }

    if (dataInfo.nikkahyoumeisaiList) {
      originData.orX0070.items = []
      dataInfo.nikkahyoumeisaiList.forEach((item) => {
        originData.orX0070.items.push({
          timezone: item.startTime + Or10828Const.DEFAULT.WAVE_LINE + item.endTime,
          contents: item.naiyoKnj,
          memo: item.memoKnj,
          offerManagerOccupation: item.tantoKnj,
          kbn: item.dataKbn,
          dailyTableId: item.day1Id,
          id: item.id,
        })
      })
    }
  }

  // 親画面.計画期間IDがある場合、対象行を選択
  if (localOneway.or10828Oneway.dailyscheduleImportType?.sc1Id) {
    localOneway.or28452OneWay.items.forEach((item) => {
      if (item.id === localOneway.or10828Oneway.dailyscheduleImportType?.sc1Id) {
        planPeriodRowClick(item.id)
      }
    })
  } else if (localOneway.or28452OneWay.items && localOneway.or28452OneWay.items.length > 0) {
    // 一行目を選択
    planPeriodRowClick(localOneway.or28452OneWay.items[0].id)
  }
}

/**
 *  計画期間行変更の時の処理
 *
 * @param item - 計画期間行データ
 */
function planPeriodRowClick(item: string) {
  if (selectedId_planPeriod.value !== item) {
    // 計画期間行選択
    selectedId_planPeriod.value = item
    local.or28452.selectedRowId = {
      value: item,
      values: []
    }
    // 履歴情報設定
    histroyInfoSet(item)
  }
}

/**
 * 計画期間のイベントを監視
 */
 watch(
  () => Or28452Logic.data.get(or28452_1.value.uniqueCpId),
   (newValue) => {
    if (newValue && newValue.value !== selectedId_planPeriod.value) {
      planPeriodRowClick(String(newValue.value))
    }
  }
 )

/**
 *  履歴情報設定
 *
 * @param selectId - 選択履歴ID
 */
const histroyInfoSet = (selectId: string) => {
  let selecthistroyId = ''
  if (originData.historyListItems) {
    // 履歴情報取得
    localOneway.or28453OneWay.items = []
    originData.historyListItems.filter((item) => item.periodId === selectId).forEach(item => {
      localOneway.or28453OneWay.items.push({
        id: item.histroyId,
        periodId: item.periodId,
        createDate: item.createDate,
        createdUser: item.createdUser,
        caseNo: item.caseNo,
        revision: item.revision,
      })
    });

    if (localOneway.or28453OneWay.items && localOneway.or28453OneWay.items.length > 0) {
      selecthistroyId = localOneway.or28453OneWay.items[0].id ? localOneway.or28453OneWay.items[0].id : '-1'
    }    // 履歴情報選択明細があり場合、
    selectedId_hisotry.value = selecthistroyId
    local.or28453.selectedRowId = {
      value: selecthistroyId,
      values: []
    }
    // 日課表明細
    dailyTableSet(selecthistroyId)
  }
}

/**
 * 履歴情報のイベントを監視
 */
 watch(
  () => Or28453Logic.data.get(or28453_1.value.uniqueCpId),
  (newValue) => {
    if (newValue && newValue.value !== selectedId_hisotry.value) {
      dailyTableSet(String(newValue.value))
      selectedId_hisotry.value = newValue.value;
    }
  }
 )

/**
 *  日課表明細
 *
 * @param selectId - 選択履歴ID
 */
const dailyTableSet = (selectId: string) => {
  localOneway.orX0070OneWay.items = []
  localOneway.orX0070OneWay.oraggeItems = []
  if (originData.orX0070.items) {
    // 日課表明細
    originData.orX0070.items.filter((item) => item.dailyTableId === selectId).forEach(item => {
      localOneway.orX0070OneWay.items.push({
        timezone: item.timezone,
        contents: item.contents,
        memo: item.memo,
        offerManagerOccupation: item.offerManagerOccupation,
        kbn: item.kbn,
        dailyTableId: item.dailyTableId,
        id: item.id,
      })
      localOneway.orX0070OneWay.oraggeItems.push({
        timezone: item.timezone,
        contents: item.contents,
        memo: item.memo,
        offerManagerOccupation: item.offerManagerOccupation,
        kbn: item.kbn,
        dailyTableId: item.dailyTableId,
        id: item.id,
      })
    });
  }
}

/**
 * setChildCpBindsInfo
 */
function setChildCpBindsInfo() {
  setChildCpBinds(props.uniqueCpId, {
    [Or28452Const.CP_ID(1)]: {
      twoWayValue: {
        value: local.or28452.selectedRowId.value,
        values: local.or28452.selectedRowId.values
      },
    },
    [Or28453Const.CP_ID(1)]: {
      twoWayValue: {
        value: local.or28453.selectedRowId.value,
        values: local.or28453.selectedRowId.values
      },
    },
    [OrX0070Const.CP_ID(1)]: {
      twoWayValue: {
        selectedRowId: local.orX0070.selectedRowId,
        managerMemoImport: { modelValue: false },
      },
    },
  })
}

/**
 *  コントロール初期化
 */
const initContorls = () => {
  //---------------header の定義---------------
    // 期間管理フラグが「1:管理する」
  if (localOneway.or10828Oneway.dailyscheduleImportType!.kikanFlag === '1') {
    // 計画期間一覧
    localOneway.or28452OneWay.headers.splice(
      0,
      localOneway.or28452OneWay.headers.length,
      {
        title: t('label.plan-period'),
        key: 'planPeriod',
        sortable: false,
        minWidth: '250px',
      },
      {
        title: t('label.within-the-period-number-of-history'),
        key: 'numberOfWithinThePeriodHistory',
        sortable: false,
        minWidth: '60px',
      }
    )
  }
  // 履歴一覧
  localOneway.or28453OneWay.headers.splice(
    0,
    localOneway.or28453OneWay.headers.length,
    {
      title: t('label.create-date'),
      key: 'createDate',
      sortable: false,
      minWidth: '120px',
    },
    {
      title: t('label.author'),
      key: 'createdUser',
      sortable: false,
      minWidth: '140px',
    },
    {
      title: t('label.caseNo'),
      key: 'caseNo',
      sortable: false,
      minWidth: '150px',
    },
    {
      title: t('label.revision'),
      key: 'revision',
      sortable: false,
      minWidth: '120px',
    }
  )
  // 日課表明細一覧
  localOneway.orX0070OneWay.headers.splice(
    0,
    localOneway.orX0070OneWay.headers.length,
    {
      title: t('label.time-zone-label'),
      key: 'timezone',
      sortable: false,
      minWidth: '120px',
    },
    {
      title: t('label.content'),
      key: 'contents',
      sortable: false,
      minWidth: '140px',
    },
    {
      title: t('label.memo-label'),
      key: 'memo',
      sortable: false,
      minWidth: '140px',
    },
    {
      title: t('label.offer-manager-occupation'),
      key: 'offerManagerOccupation',
      sortable: false,
      minWidth: '120px',
    }
  )
}


/**
 *  閉じる/Xボタン押下時の処理
 */
function onClick_Close() {
  setState({ isOpen: false })
}

/**
 *  確定ボタン押下時の処理
 */
async function onClick_Ok() {
  local.or10828.items = []
  const data = OrX0070Logic.data.get(orX0070_1.value.uniqueCpId)
  if (data) {
    const managerMemoImportValue = data.managerMemoImport.modelValue
    data.selectedRowId.values.forEach((item) => {
      const selectdata =  originData.orX0070.items.find((selectItem) => selectItem.id === item)
      if (selectdata) {
        let memo = ''
        if (managerMemoImportValue) {
          if (selectdata.offerManagerOccupation) {
            memo = selectdata.offerManagerOccupation + Or10828Const.DEFAULT.R_N
          }
          if (selectdata.memo) {
            memo = memo + selectdata.memo
          }
        } else {
          if (selectdata.memo) {
            memo = selectdata.memo
          }
        }
        local.or10828.items.push({
          /** 時間帯 */
          timezone: selectdata.timezone,
          /** 内容 */
          contents: selectdata.contents ?? selectdata.contents,
          /** メモ */
          memo: memo,
        })
      }
    })
  }

  if (local.or10828.items.length < 1) {
    await openWarnDialog()
    return
  } else {
    // 選択情報値戻り
    emit('update:modelValue', local.or10828)
    //ダイアログ開閉状態を更新する
    setState({ isOpen: false })
  }
}

/**
 * 編集破棄ダイアログ表示
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openWarnDialog(): Promise<string> {
  // 確認ダイアログを開く
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)

        let result = Or10828Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or10828Const.DEFAULT.DIALOG_RESULT_YES
        }
        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet class="view">
        <c-v-row  class="periodHisotry">
          <c-v-col v-if="localOneway.or10828Oneway.dailyscheduleImportType!.kikanFlag === '1'" cols="5">
            <g-custom-or28452
              v-bind="or28452_1"
              v-model="local.or28452"
              :oneway-model-value="localOneway.or28452OneWay"
            >
            </g-custom-or28452>
          </c-v-col>
          <c-v-col class="historyList">
            <g-custom-or28453
              v-bind="or28453_1"
              v-model="local.or28453"
              :oneway-model-value="localOneway.or28453OneWay"
            >
            </g-custom-or28453> </c-v-col
        ></c-v-row>
        <hr class="v-divider" />
        <c-v-row>
          <c-v-col>
            <g-custom-or-x-0070
              v-bind="orX0070_1"
              v-model="local.orX0070"
              class="intentionList"
              :oneway-model-value="localOneway.orX0070OneWay"
            >
            </g-custom-or-x-0070>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>
    <g-base-or21814 v-bind="or21814_1" />
    <g-base-or21815 v-bind="or21815_1" />
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <div class="buttonArea">
        <!-- 閉じる -->
        <base-mo00611
          :oneway-model-value="localOneway.footerClosebtnOneway"
          @click="onClick_Close"
        />
        <!-- 確定ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.footerOkbtnOneway"
          @click="onClick_Ok"
        />
      </div>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
.view {
  width: 1080px;
  display: flex;
  margin: 4px;
  flex-direction: column;

  .periodHisotry {
    display: flex;
    flex-direction: row;

    .planPeriodList {
      height: 146px;
      flex: 0 1 auto;
      max-width: 50%;
      margin-right: 8px;
    }

    .historyList {
      height: 146px;
    }
  }
}

.v-divider {
  margin-top: 8px;
  margin-bottom: 8px;
}

.intentionList {
  height: 260px;
}

.buttonArea {
  button:not(:nth-child(1)) {
    margin-left: 8px;
  }
}
</style>
