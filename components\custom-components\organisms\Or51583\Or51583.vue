<script setup lang="ts">
/**
 * Or51583:有機体:印刷設定
 * GUI00844_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { Or51583Const } from './Or51583.constants'
import type { Or51583StateType } from './Or51583.type'
import { useSetupChildProps, useScreenOneWayBind, useNuxtApp, dateUtils } from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '@/types/business/components/Mo01338Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00020Type, Mo00020OnewayType } from '@/types/business/components/Mo00020Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  UserEntity,
  SelectionHistoryEntity,
  PrtEntity,
  ISelectionTablePrintSettingsInitUpdateInEntity,
  ISelectionTablePrintSettingsInitUpdateOutEntity,
  ISelectionTablePrintSettingsSubjectSelectInEntity,
  ISelectionTablePrintSettingsSubjectSelectOutEntity,
  ISelectionTablePrintSettingsHistorySelectInEntity,
  ISelectionTablePrintSettingsHistorySelectOutEntity,
} from '~/repositories/cmn/entities/SelectionTablePrintSettingsEntity'
import type {
  SysIniInfoEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsPrtNoChangeUpdateEntity'
import type {
  IFreeAssessmentFacePrintSettingsUpdateInEntity,
  IFreeAssessmentFacePrintSettingsUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsUpdateEntity'
import type {
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { CustomClass } from '~/types/CustomClassType'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type {
  OrX0128OnewayType,
  OrX0128Items,
  OrX0128Headers,
} from '~/types/cmn/business/components/OrX0128Type'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import type {
  Or51583Param,
} from '~/components/custom-components/organisms/Or51583/Or51583.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useReportUtils, reportOutputType } from '~/utils/useReportUtils'
import type {
  PrintSetEntity,
  PrintSubjectHistoryEntity,
  ChoPrtEntity,
  IProblemRegionSelectionSheetReportInEntity,
} from '~/repositories/cmn/entities/ProblemRegionSelectionSheetReportEntity'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { useCmnCom } from '@/utils/useCmnCom'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { Or21813StateType } from '~/components/base-components/organisms/Or21813/Or21813.type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import { DIALOG_BTN, SPACE_WAVE, SPACE_SPLIT_COLON } from '~/constants/classification-constants'
import { SYS_RYAKU } from '~/constants/constants'
const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()
const { reportOutput } = useReportUtils()
const { convertDateToSeireki } = dateUtils()
const $log = useNuxtApp().$log as DebugLogPluginInterface

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

// 子コンポーネント用変数
const orX0117 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })

const localOneway = reactive({
  /**
   * 基本設定
   */
  mo01338OneWayBasicSettings: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 帳票タイトル
   */
  mo00615OneWayTypeTitle: {
    itemLabel: t('label.title'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * タイトル表示
   */
  mo00045OneWay: {
    showItemLabel: false,
    width: '296',
    disabled: true,
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo00045OnewayType,
  /**
   * 日付印刷区分
   */
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 指定日
   */
  mo00020OneWay: {
    showItemLabel: false,
    showSelectArrow: false,
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 利用者選択ラベル
   */
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 履歴選択ラベル
   */
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択
   */
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 履歴選択
   */
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 基準日
   */
  mo00615OneWayType: {
    itemLabel: t('label.base-date'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * 基準日: 表用テキストフィールド
   */
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '220px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: Or51583Const.DEFAULT.STR.EMPTY,
  } as OrX0145OnewayType,
  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    disabled: false,
  } as Mo00609OnewayType,
})

/**
 * 親画面の情報
 */
const local = {
  /**
   * 個人情報使用フラグ
   */
  kojinhogoUsedFlg: Or51583Const.DEFAULT.KOJINHOGO_USED_FLG,
  /**
   * 個人情報番号
   */
  sectionAddNo: Or51583Const.DEFAULT.SECTION_ADD_NO,
  /**
   * 親画面.法人ID
   */
  houjinId: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.事業所ID
   */
  svJigyoId: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.施設ID
   */
  shisetuId: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.セクション名
   */
  sectionName: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.利用者ID
   */
  userId: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.事業所名
   */
  svJigyoKnj: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.処理年月日
   */
  processYmd: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.アセスメントID
   */
  assessmentId: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.フォーカス設定用イニシャル
   */
  focusSettingInitial: [] as string[],
  /**
   * 親画面.初期選択状態の担当者カウンタ値
   */
  selectedUserCounter: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 選択された帳票のプロファイル
   */
  profile: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者ID
   */
  selectUserId: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 出力帳票名一覧に選択行番号
   */
  index: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * システムINI情報
   */
  sysIniInfo: {} as SysIniInfoEntity,
  /**
   * 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
   */
  historyNoSelect: false,
  /**
   * 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
   */
  userNoSelect: false,
  /**
   * 帳票番号
   */
  prtNo: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者リスト
   */
  userList: [] as UserEntity[],
  /**
   * 帳票ID
   */
  reportId: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 履歴選択の明細
   */
  orX0128DetList: [] as OrX0128Items[],
  /**
   * 印刷設定情報リスト
   */
  prtList: [] as PrtEntity[],
  /**
   * アセスメントタイプ
   */
  assessmentKindCodeTypes: [] as CodeType[],
}

/**
 * レスポンスパラメータ詳細
 */
const localData: ISelectionTablePrintSettingsInitUpdateOutEntity = {
  data: {},
} as ISelectionTablePrintSettingsInitUpdateOutEntity
/**************************************************
 * 変数定義
 **************************************************/
// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1300px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or51583',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or51583ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'or51583_content',
  } as Mo01344OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or51583Const.DEFAULT.IS_OPEN,
})

/**
 * 出力帳票名一覧
 */
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.ledger'),
      key: 'ledgerName',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 603,
})

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * タイトルテキストフィールドmodelValue
 */
const mo00045TitleType = ref<Mo00045Type>({
  value: '',
} as Mo00045Type)

/**
 * 日付印刷区分
 */
const mo00039Type = ref<string>('')

/**
 * 指定日
 */
const mo00020Type = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 利用者選択
 */
const mo00039OneWayUserSelectType = ref<string>('')

/**
 * 履歴選択
 */
const mo00039OneWayHistorySelectType = ref<string>('')

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

/**
 * 基準日
 */
const mo00020TypeKijunbi = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 指定日非表示/表示フラゲ
 */
const mo00020Flag = ref<boolean>(false)
/**
 * 基準日フラゲ
 */
const kijunbiFlag = ref<boolean>(false)
/**
 * 履歴一覧セクションフラゲ
 */
const mo01334TypeHistoryFlag = ref<boolean>(true)

/**
 * 利用者列幅
 */
const userCols = ref<number>(4)

const orX0128OnewayModel = reactive<OrX0128OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: '1',
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: OrX0128Const.DEFAULT.TANI,
  tableStyle: 'width:515px;height: 512px;',
  headers: [
    {
      title: t('label.selection-date'),
      key: 'capDateYmd',
      minWidth: '120px',
      width: '120px',
      sortable: false,
    },
    {
      title: t('label.author'),
      key: 'shokuinKnj',
      minWidth: '170px',
      width: '170px',
      sortable: false,
    },
    {
      title: t('label.assessment-kind'),
      key: 'capTypeKnj',
      minWidth: '170px',
      width: '170px',
      sortable: false,
    },
  ] as OrX0128Headers[],
  items: [],
})

/**
 * 利用者
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  /**
   * 選択モート
   */
  selectMode: OrX0130Const.DEFAULT.TANI,
  /**
   * テーブルのスタイル
   */
  tableStyle: 'width:206px;height: 467px',
  /**
   * 指定行選択
   */
  userId: Or51583Const.DEFAULT.STR.EMPTY,
  /**
   * 複数選択時、50音の選択数を表示するか
   */
  showKanaSelectionCount: true,
  /**
   * フォーカス設定用イニシャル
   */
  focusSettingInitial: [] as string[],
})

/**
 * 担当ケアマネ選択
 */
const tantoIconBtn = ref<boolean>(false)
/**
 * 印刷設定帳票出力
 */
const orX0117Oneway: OrX0117OnewayType = {
  type: Or51583Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType

/**
 * 初期情報取得フラゲ
 */
const initFlag = ref<boolean>(false)

/**
 * 期間管理フラグ
 */
const kikanFlag = ref<string>(Or51583Const.DEFAULT.STR.EMPTY)
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or51583StateType>({
  cpId: Or51583Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or51583Const.DEFAULT.IS_OPEN
    },
    param: (value) => {
      if (value) {
        local.prtNo = value.prtNo
        local.svJigyoId = value.svJigyoId
        local.houjinId = value.houjinId
        local.shisetuId = value.shisetuId
        local.sectionName = value.sectionName
        local.userId = value.userId
        local.svJigyoKnj = value.svJigyoKnj
        local.processYmd = value.processYmd
        local.assessmentId = value.assessmentId
        local.focusSettingInitial = value.focusSettingInitial
        local.selectedUserCounter = value.selectedUserCounter
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 画面ID
const screenId = 'GUI00844'
// ルーティング
const routing = 'GUI00844/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})

onMounted(async () => {
  // 汎用コード取得API実行
  await initCodes()

  // 画面PDFダウンロードボタン活性非活性設定 => 一時的にコメントアウト
  // await pdfDownloadBtnSetting()

  // 画面ボタン活性非活性設定
  btnItemSetting()

  // 初期プロジェクト設定
  initSetting()

  // 初期情報取得
  await init()

  // 初期選択データ設定
  selectRowDataSetting()
})

// ダイアログ表示フラグ
const showDialogOrX0117 = computed(() => {
  // Or51583のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 初期選択データ設定
 */
const selectRowDataSetting = () => {
  // フォーカス設定用イニシャル設定
  orX0130Oneway.focusSettingInitial = local.focusSettingInitial
  // 初期選択状態の担当者カウンタ値設定
  localOneway.orX0145Oneway.selectedUserCounter = local.selectedUserCounter
  // 親画面.利用者情報リスト件数>0
  if (mo01334Oneway.value.items.length > 0) {
    // 利用者一覧明細に親画面.利用者IDが存在する場合
    if (local.userId) {
      // 利用者IDを対するレコードを選択状態にする
      orX0130Oneway.userId = local.userId
    } else {
      orX0130Oneway.userId = Or51583Const.DEFAULT.STR.EMPTY
    }
  }
  // 利用者一覧明細に親画面.利用者IDが存在しない場合
  else {
    orX0130Oneway.userId = Or51583Const.DEFAULT.STR.EMPTY
  }
}

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // アセスメント種別コード
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    mo00039Type.value = bizukePrintCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of bizukePrintCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWay.items = list
  }

  // アセスメントタイプ
  local.assessmentKindCodeTypes = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )

  // 利用者選択 TAN_MULTIPLE_SELECT_CATEGORY
  const tanMultipleSelectCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  if (tanMultipleSelectCategoryCodeTypes?.length > 0) {
    mo00039OneWayUserSelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    mo00039OneWayHistorySelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of tanMultipleSelectCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayHistorySelectType.items = list
    localOneway.mo00039OneWayUserSelectType.items = list
  }
}

/**
 * 初期プロジェクト設定
 */
const initSetting = () => {
  // 基準日設定(親画面.処理年月日)
  mo00020TypeKijunbi.value.value = local.processYmd
  // 親画面.処理年月日が""の場合
  if (Or51583Const.DEFAULT.STR.EMPTY === local.processYmd) {
    // 担当ケアマネ選択
    tantoIconBtn.value = false
  } else {
    // 担当ケアマネ選択
    tantoIconBtn.value = true
  }
}

/**
 * 初期情報取得
 */
const init = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: ISelectionTablePrintSettingsInitUpdateInEntity = {
    sysCd: systemCommonsStore.getSystemCode,
    sysRyaku: SYS_RYAKU,
    houjinId: local.houjinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    shokuId: systemCommonsStore.getStaffId,
    userId: local.userId,
    sectionName: local.sectionName,
    index: Or51583Const.DEFAULT.INDEX,
    kojinhogoUsedFlg: Or51583Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or51583Const.DEFAULT.SECTION_ADD_NO,
  } as ISelectionTablePrintSettingsInitUpdateInEntity
  const resp: ISelectionTablePrintSettingsInitUpdateOutEntity = await ScreenRepository.update(
    'selectionTablePrintSettingsInitUpdate',
    inputData
  )
  if (resp?.data) {
    // レスポンスパラメータ詳細
    localData.data = { ...resp?.data }

    // 期間管理フラグ
    kikanFlag.value = localData.data.kikanFlg

    const prtList: Mo01334Items[] = []
    for (const item of resp.data.prtList) {
      if (item) {
        prtList.push({
          id: item.prtNo,
          mo01337OnewayLedgerName: {
            value: item.prtTitle,
            unit: Or51583Const.DEFAULT.STR.EMPTY,
          } as Mo01337OnewayType,
          prnDate: item.prnDate === Or51583Const.DEFAULT.STR.TRUE,
          selectable: true,
          profile: item.profile,
          index: item.index,
          prtNo: item.prtNo,
        } as Mo01334Items)
      }
    }
    // 「帳票番号」昇順
    prtList.sort((a, b) => Number(a.id) - Number(b.id))
    mo01334Oneway.value.items = prtList
    initFlag.value = true
    // アセスメント履歴情報を取得する
    getHistoryData(resp.data.selectionHistoryList)
    outputLedgerName(local.prtNo)
  }
}

/**
 * アセスメント履歴一覧データ
 *
 * @param periodHistoryList - アセスメント履歴リスト
 */
const getHistoryData = (periodHistoryList: SelectionHistoryEntity[]) => {
  if (Or51583Const.DEFAULT.KIKAN_FLG_1 === kikanFlag.value) {
    const tempList: string[] = [] as string[]
    let list: OrX0128Items[] = []
    for (const item of periodHistoryList) {
      if (item) {
        const planPeriod =
          t('label.plan-period') +
          SPACE_SPLIT_COLON +
          item.startYmd +
          SPACE_WAVE +
          item.endYmd
        if (!tempList.includes(planPeriod)) {
          const historyList: OrX0128Items[] = []
          for (const data of periodHistoryList) {
            const dataPlanPeriod =
              t('label.plan-period') +
              SPACE_SPLIT_COLON +
              data.startYmd +
              SPACE_WAVE +
              data.endYmd
            if (planPeriod === dataPlanPeriod) {
              historyList.push({
                id: data.raiId,
                sc1Id: data.sc1Id,
                startYmd: data.startYmd,
                endYmd: data.endYmd,
                sel: data.sel,
                raiId: data.raiId,
                userid: data.userId,
                capTypeKnj: getAssessmentKindTypeKnj(data.capType),
                capType: data.capType,
                capDateYmd: data.capDateYmd,
                capShokuId: data.capShokuId,
                shokuinKnj: data.shokuinKnj,
              } as OrX0128Items)
            }
          }
          if (historyList.length > 0) {
            list.push({
              sc1Id: item.sc1Id,
              startYmd: item.startYmd,
              endYmd: item.endYmd,
              isPeriodManagementMergedRow: true,
              planPeriod:
                t('label.plan-period') +
                SPACE_SPLIT_COLON +
                item.startYmd +
                SPACE_WAVE +
                item.endYmd,
              id: Or51583Const.DEFAULT.STR.EMPTY,
            } as OrX0128Items)
            list = list.concat(historyList)
            tempList.push(planPeriod)
          }
        }
      }
    }
    list.forEach((item, index) => {
      item.id = String(++index)
    })
    orX0128OnewayModel.items = list
    // 画面.利用者一覧明細に親画面.履歴IDが存在する場合
    if (local.assessmentId) {
      // 親画面.アセスメントIDを対するレコードを選択状態にする
      orX0128OnewayModel.initSelectId = (
        list.findIndex((item) => item.raiId === local.assessmentId) + 1
      ).toString()

      local.assessmentId = Or51583Const.DEFAULT.STR.EMPTY
    }
  } else {
    const list: OrX0128Items[] = []
    for (const data of periodHistoryList) {
      if (data) {
        list.push({
          id: data.raiId,
          sc1Id: data.sc1Id,
          startYmd: data.startYmd,
          endYmd: data.endYmd,
          sel: data.sel,
          raiId: data.raiId,
          userid: data.userId,
          capType: data.capType,
          capDateYmd: data.capDateYmd,
          capShokuId: data.capShokuId,
          shokuinKnj: data.shokuinKnj,
        } as OrX0128Items)
      }
    }
    orX0128OnewayModel.items = list
    // 履歴一覧明細に親画面.履歴IDが存在する場合
    if (local.assessmentId) {
      // 画面.履歴一覧明細に親画面.履歴IDを対するレコードを選択状態にする
      orX0128OnewayModel.initSelectId = (
        list.findIndex((item) => item.raiId === local.assessmentId) + 1
      ).toString()
    }
  }
}

/**
 * 選定アセスメント種別名取得
 *
 * @param assessmentKindType - 選定アセスメント種別
 */
const getAssessmentKindTypeKnj = (assessmentKindType: string) => {
  let assessmentKindTypeKnj = Or51583Const.DEFAULT.STR.EMPTY
  for (const item of local.assessmentKindCodeTypes) {
    if (item) {
      if (item.value === assessmentKindType) {
        assessmentKindTypeKnj = item.label
      }
    }
  }
  return assessmentKindTypeKnj
}

/**
 * 画面印刷設定内容を保存
 */
const save = async (): Promise<IFreeAssessmentFacePrintSettingsUpdateOutEntity> => {
  // バックエンドAPIから印刷設定情報保存
  const inputData: IFreeAssessmentFacePrintSettingsUpdateInEntity = {
    sysRyaku: SYS_RYAKU,
    sectionName: local.sectionName,
    gsyscd: systemCommonsStore.getSystemCode,
    shokuId: systemCommonsStore.getStaffId,
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    index: local.index,
    sysIniInfo: local.sysIniInfo,
    prtList: localData.data.prtList,
  } as IFreeAssessmentFacePrintSettingsUpdateInEntity

  const resp: IFreeAssessmentFacePrintSettingsUpdateOutEntity = await ScreenRepository.update(
    'freeAssessmentFacePrintSettingsUpdate',
    inputData
  )
  return resp
}

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  await save()
  setState({
    isOpen: false,
    param: {} as Or51583Param,
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const pdfDownload = async () => {
  // 利用者選択方法が「単一」
  if (Or51583Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 履歴選択が「単一」
    if (Or51583Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      orX0117Oneway.type = Or51583Const.DEFAULT.STR.ONE
      // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
      if (local.userNoSelect) {
        // メッセージを表示
        const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11393'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
        })
        // はい
        if (dialogResult === DIALOG_BTN.YES) {
          // 処理終了
          return
        }
      }

      // 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
      if (local.historyNoSelect) {
        // メッセージを表示
        const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11455'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
        })
        // はい
        if (dialogResult === DIALOG_BTN.YES) {
          // 処理終了
          return
        }
      }

      // 履歴情報リストにデータを選択する場合
      if (!local.historyNoSelect) {
        // 印刷ダイアログ画面を開かずに、画面.印刷対象履歴リスト「利用者情報+履歴情報+出力帳票対象」を直接に利用して、帳票側の処理を呼び出す
        await reportOutputPdf()
        return
      }
    }
    // 履歴選択方法が「複数」
    else if (Or51583Const.DEFAULT.HUKUSUU === mo00039OneWayHistorySelectType.value) {
      // 履歴一覧が0件選択場合（※履歴リストが0件、複数件を含む）
      if (local.historyNoSelect) {
        // メッセージを表示
        const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11455'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
        })
        // はい
        if (dialogResult === DIALOG_BTN.YES) {
          // 処理終了
          return
        }
      }
      // 履歴情報リストにデータを選択する場合
      else {
        // 印刷設定情報リストを作成
        createReportOutputData(local.prtNo)
      }
    }
  }

  // 利用者選択方法が「複数」の場合
  if (Or51583Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
    orX0117Oneway.type = Or51583Const.DEFAULT.STR.ONE
    // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
    if (local.userNoSelect) {
      // メッセージを表示
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11393'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if (dialogResult === DIALOG_BTN.YES) {
        // 処理終了
        return
      }
    }
    // 利用者情報リストにデータを選択する場合
    else {
      // 印刷設定情報リストを作成
      await PrintSettingsSubjectSelect()
    }
  }

  // AC019-2と同じ => 画面の印刷設定情報を保存する
  await save()

  // 選択された帳票のプロファイルが””の場合
  if (!local.profile) {
    // メッセージを表示
    const dialogResult = await openErrorDialog(or21813.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // はい
    if (dialogResult === DIALOG_BTN.YES) {
      // 処理終了
      return
    }
  }

  // 「AC020-4-2、AC020-4-3」で取得した印刷用情報リスト＞0件の場合
  if (orX0117Oneway.historyList.length > 0) {
    // OrX0117のダイアログ開閉状態を更新する
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 印刷ダイアログ画面を開
 */
const reportOutputPdf = async () => {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    const reportData: IProblemRegionSelectionSheetReportInEntity = {
      svJigyoKnj: local.svJigyoKnj,
      syscd: systemCommonsStore.getSystemCode,
      printSet: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
      } as PrintSetEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as IProblemRegionSelectionSheetReportInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        //  単一帳票
        if (local.prtNo === item.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or51583Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or51583Const.DEFAULT.STR.EMPTY,
      sc1Id:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].sc1Id as string)
          : Or51583Const.DEFAULT.STR.EMPTY,
      startYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].startYmd as string)
          : Or51583Const.DEFAULT.STR.EMPTY,
      endYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].endYmd as string)
          : Or51583Const.DEFAULT.STR.EMPTY,
      raiId:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].raiId as string)
          : Or51583Const.DEFAULT.STR.EMPTY,
      assType:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].capType as string)
          : Or51583Const.DEFAULT.STR.EMPTY,
      assDateYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].capDateYmd as string)
          : Or51583Const.DEFAULT.STR.EMPTY,
      assShokuId:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].capShokuId as string)
          : Or51583Const.DEFAULT.STR.EMPTY,
      result: Or51583Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    // 帳票出力
    await reportOutput(local.reportId, reportData, reportOutputType.DOWNLOAD)
  } catch (e) {
    $log.debug('帳票の出力に失敗しました。', local.reportId, reportOutputType.DOWNLOAD, e)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * 印刷設定情報リストを作成(利用者選択が「複数」、利用者情報リストにデータを選択する場合)
 */
const PrintSettingsSubjectSelect = async () => {
  // 印刷設定情報リストを作成する
  const inputData: ISelectionTablePrintSettingsSubjectSelectInEntity = {
    prtNo: local.prtNo,
    svJigyoId: local.svJigyoId,
    kijunbi: mo00020TypeKijunbi.value.value ?? Or51583Const.DEFAULT.STR.EMPTY,
    userList: local.userList,
  } as ISelectionTablePrintSettingsSubjectSelectInEntity
  const resp: ISelectionTablePrintSettingsSubjectSelectOutEntity = await ScreenRepository.select(
    'selectionTablePrintSettingsSubjectSelect',
    inputData
  )

  const list: OrX0117History[] = []
  if (resp.data) {
    for (const data of resp.data.printSubjectHistoryList) {
      if (data) {
        // 利用者複数の場合
        if (Or51583Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 印刷設定情報リストを作成
          const reportData: IProblemRegionSelectionSheetReportInEntity = {
            svJigyoKnj: local.svJigyoKnj,
            syscd: systemCommonsStore.getSystemCode,
            printSet: {
              shiTeiKubun: mo00039Type.value,
              shiTeiDate: mo00020Type.value.value
                ? mo00020Type.value.value.split('/').join('-')
                : '',
            } as PrintSetEntity,
            printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
          } as IProblemRegionSelectionSheetReportInEntity

          const choPrtList: ChoPrtEntity[] = []
          for (const item of localData.data.prtList) {
            if (item) {
              //  単一帳票
              if (local.prtNo === item.prtNo) {
                choPrtList.push({
                  shokuId: systemCommonsStore.getStaffId,
                  sysRyaku: systemCommonsStore.getSystemAbbreviation,
                  section: local.sectionName,
                  prtNo: item.prtNo,
                  choPro: item.profile,
                  sectionName: item.defPrtTitle,
                  dwobject: item.dwobject,
                  prtOrient: item.prtOrient,
                  prtSize: item.prtSize,
                  listTitle: item.listTitle,
                  prtTitle: item.prtTitle,
                  mTop: item.mtop,
                  mBottom: item.mbottom,
                  mLeft: item.mleft,
                  mRight: item.mright,
                  ruler: item.ruler,
                  prndate: item.prnDate,
                  prnshoku: item.prnshoku,
                  serialFlg: item.serialFlg,
                  modFlg: item.modFlg,
                  secFlg: item.secFlg,
                  param01: item.param01,
                  param02: item.param02,
                  param03: item.param03,
                  param04: item.param04,
                  param05: item.param05,
                  param06: item.param06,
                  param07: item.param07,
                  param08: item.param08,
                  param09: item.param09,
                  param10: item.param10,
                  serialHeight: item.serialHeight,
                  serialPagelen: item.serialPagelen,
                  hsjId: systemCommonsStore.getHoujinId,
                  param11: item.param11,
                  param12: item.param12,
                  param13: item.param13,
                  param14: item.param14,
                  param15: item.param15,
                  param16: item.param16,
                  param17: item.param17,
                  param18: item.param18,
                  param19: item.param19,
                  param20: item.param20,
                  param21: item.param21,
                  param22: item.param22,
                  param23: item.param23,
                  param24: item.param24,
                  param25: item.param25,
                  param26: item.param26,
                  param27: item.param28,
                  param28: item.param28,
                  param29: item.param29,
                  param30: item.param30,
                  param31: item.param31,
                  param32: item.param32,
                  param33: item.param33,
                  param34: item.param34,
                  param35: item.param35,
                  param36: item.param36,
                  param37: item.param37,
                  param38: item.param38,
                  param39: item.param39,
                  param40: item.param40,
                  param41: item.param41,
                  param42: item.param42,
                  param43: item.param43,
                  param44: item.param44,
                  param45: item.param45,
                  param46: item.param46,
                  param47: item.param47,
                  param48: item.param48,
                  param49: item.param49,
                  param50: item.param50,
                  houjinId: systemCommonsStore.getHoujinId,
                  shisetuId: systemCommonsStore.getShisetuId,
                  svJigyoId: systemCommonsStore.getSvJigyoId,
                  zoomRate: item.zoomRate,
                  modifiedCnt: item.modifiedCnt,
                } as ChoPrtEntity)
              }
            }
          }
          reportData.printSubjectHistoryList.push({
            userId: data.userId,
            userName: data.userName,
            sc1Id: data.sc1Id,
            startYmd: data.startYmd,
            endYmd: data.endYmd,
            raiId: data.raiId,
            assType: data.capType,
            assDateYmd: data.capDateYmd,
            assShokuId: data.capShokuId,
            result: data.result,
            choPrtList: choPrtList,
          } as PrintSubjectHistoryEntity)
          list.push({
            reportId: local.reportId,
            outputType: reportOutputType.DOWNLOAD,
            reportData: reportData,
            userName: data.userName,
            historyDate: data.capDateYmd,
            result: data.result,
          } as OrX0117History)
        }
      }
    }
  }
  orX0117Oneway.historyList = list
}

/**
 * 切替前の印刷設定を保存する
 *
 * @param selectId - 出力帳票ID
 */
const setBeforChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          item.prnDate = mo00039Type.value
          break
        }
      }
    }
  }
}

/**
 * 切替後の印刷設定を画面に設定する
 *
 * @param selectId - 出力帳票ID
 */
const setAfterChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          mo00039Type.value = item.prnDate
          break
        }
      }
    }
  }
}

/**
 * 「出力帳票名」選択
 *
 * @param selectId - 出力帳票ID
 */
const outputLedgerName = (selectId: string) => {
  if (!selectId) {
    selectId = Or51583Const.DEFAULT.SECTION_NO
  }
  let label = Or51583Const.DEFAULT.STR.EMPTY
  for (const item of mo01334Oneway.value.items) {
    if (item) {
      if (selectId === item.id && item.mo01337OnewayLedgerName) {
        const data = item.mo01337OnewayLedgerName as Mo01337OnewayType
        label = data.value
        mo01334Type.value.value = item.id

        // プロファイル
        local.profile = item.profile as string

        // 出力帳票名一覧に選択行番号
        local.index = item.index as string

        // 帳票番号
        local.prtNo = item.prtNo as string

        // 帳票ID
        setReportId(item.id)
        break
      }
    }
  }
  mo00045TitleType.value.value = label

  // 帳票イニシャライズデータを取得する
  void getSectionInitializeData()
}

/**
 * 帳票ID設定
 *
 * @param prtNo - 帳票番号
 */
const setReportId = (prtNo: string) => {
  switch (prtNo) {
    case Or51583Const.DEFAULT.STR.ONE:
      local.reportId = Or51583Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID
      break
    default:
      local.reportId = Or51583Const.DEFAULT.STR.EMPTY
      break
  }
}

/**
 * 印刷設定情報リストを作成
 *
 * @param prtNo - 帳票番号
 */
const createReportOutputData = (prtNo: string) => {
  const list: OrX0117History[] = []
  for (const orX0128DetData of local.orX0128DetList) {
    const reportData: IProblemRegionSelectionSheetReportInEntity = {
      svJigyoKnj: local.svJigyoKnj,
      syscd: systemCommonsStore.getSystemCode,
      printSet: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
      } as PrintSetEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as IProblemRegionSelectionSheetReportInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        //  単一帳票
        if (prtNo === item.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or51583Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or51583Const.DEFAULT.STR.EMPTY,
      sc1Id: orX0128DetData.sc1Id as string,
      startYmd: orX0128DetData.startYmd as string,
      endYmd: orX0128DetData.endYmd as string,
      raiId: orX0128DetData.raiId as string,
      assType: orX0128DetData.capType as string,
      assDateYmd: orX0128DetData.capDateYmd as string,
      assShokuId: orX0128DetData.capShokuId as string,
      result: Or51583Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    list.push({
      reportId: local.reportId,
      outputType: reportOutputType.DOWNLOAD,
      reportData: reportData,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or51583Const.DEFAULT.STR.EMPTY,
      historyDate: orX0128DetData.capDateYmd as string,
      result: Or51583Const.DEFAULT.STR.EMPTY,
    } as OrX0117History)
  }
  orX0117Oneway.historyList = list
}

/**
 * 帳票イニシャライズデータを取得する
 */
const getSectionInitializeData = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity = {
    profile: local.profile,
    gsyscd: systemCommonsStore.getSystemCode,
    shokuId: systemCommonsStore.getStaffId,
    kojinhogoUsedFlg: Or51583Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or51583Const.DEFAULT.SECTION_ADD_NO,
  } as IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity
  const resp: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity =
    await ScreenRepository.update('freeAssessmentFacePrintSettingsPrtNoChangeUpdate', inputData)
  if (resp.data) {
    local.sysIniInfo = resp.data.sysIniInfo
  }
}

/**
 * 画面PDFダウンロードボタン活性非活性設定
 */
const pdfDownloadBtnSetting = async () => {
  // 共通処理の印刷権限チェックを行う
  // 権限有る：活性
  if (await hasPrintAuth(Or51583Const.DEFAULT.PATH_GUI01310)) {
    // PDFダウンロードボタンが非活性
    localOneway.mo00609Oneway.disabled = false
  }
  // 権限無し：非活性
  else {
    // PDFダウンロードボタンが活性
    localOneway.mo00609Oneway.disabled = true
  }
}

/**
 * 印刷設定履歴リスト取得
 */
const getPrintSettingsHistoryList = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: ISelectionTablePrintSettingsHistorySelectInEntity = {
    svJigyoId: local.svJigyoId,
    userId: local.selectUserId,
    kikanFlg: kikanFlag.value,
  } as ISelectionTablePrintSettingsHistorySelectInEntity
  const resp: ISelectionTablePrintSettingsHistorySelectOutEntity = await ScreenRepository.select(
    'selectionTablePrintSettingsHistorySelect',
    inputData
  )
  if (resp.data) {
    // アセスメント履歴一覧データ
    getHistoryData(resp.data.selectionHistoryList)
  }
}

/**
 * 画面ボタン活性非活性設定
 */
const btnItemSetting = () => {
  // 利用者選択方法が「単一」の場合
  if (Or51583Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 基準日を非表示にする
    // 履歴選択を活性表示にする
    kijunbiFlag.value = false
  }
  // 以外の場合
  else {
    // 基準日を活性表示にする
    // 履歴選択を非表示にする
    kijunbiFlag.value = true
  }

  // 履歴一覧セクション
  if (Or51583Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    userCols.value = 4
    mo01334TypeHistoryFlag.value = true
  } else {
    userCols.value = 12
    mo01334TypeHistoryFlag.value = false
  }

  // 担当ケアマネ選択アイコンボタン非活性/活性設定
  const kkjTantoFlg: string =
    cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? Or51583Const.DEFAULT.STR.EMPTY
  // 共通情報.担当ケアマネ設定フラグ > 0、且つ、共通情報.担当者IDが0以外の場合
  if (
    systemCommonsStore.getManagerId !== Or51583Const.DEFAULT.STR.ZERO &&
    parseInt(kkjTantoFlg) > Or51583Const.DEFAULT.NUMBER.ZERO
  ) {
    // 非活性
    localOneway.orX0145Oneway.disabled = true
  }
  // その他場合
  else {
    // 活性
    localOneway.orX0145Oneway.disabled = false
  }
}

/**
 * 担当ケアマネプルダウン
 *
 * @param result - 戻り値
 */
const orx0145UpdateModelValue = (result: OrX0145Type) => {
  if (result) {
    if (result.value) {
      if (!Array.isArray(result.value) && 'chkShokuId' in result.value) {
        // TODO API疎通時に確認
        orX0130Oneway.tantouCareManager = result.value.chkShokuId
      }
    }

    // 画面.利用者選択が単一の場合
    if (Or51583Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
      // 画面.利用者一覧明細の1件目レコードを選択状態にする
      orX0130Oneway.userId = Or51583Const.DEFAULT.STR.EMPTY
    }
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = async (
  uniqueCpId: string,
  state: Or21814OnewayType
): Promise<typeof DIALOG_BTN.YES
          | typeof DIALOG_BTN.NO
          | typeof DIALOG_BTN.CANCEL> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = DIALOG_BTN.YES as typeof DIALOG_BTN.YES
          | typeof DIALOG_BTN.NO
          | typeof DIALOG_BTN.CANCEL

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.NO
        }
        if (event?.thirdBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openErrorDialog = async (
  uniqueCpId: string,
  state: Or21813StateType
): Promise<typeof DIALOG_BTN.YES
          | typeof DIALOG_BTN.NO
          | typeof DIALOG_BTN.CANCEL> => {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(uniqueCpId)

        let result = DIALOG_BTN.CANCEL as typeof DIALOG_BTN.YES
          | typeof DIALOG_BTN.NO
          | typeof DIALOG_BTN.CANCEL

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.NO
        }
        if (event?.thirdBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }

        // エラーダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334Type.value.value,
  (newValue, oldValue) => {
    setBeforChangePrintData(oldValue)
    setAfterChangePrintData(newValue)
    outputLedgerName(newValue)

    // 日付印刷区分が2の場合
    if (Or51583Const.DEFAULT.STR.TWO === mo00039Type.value) {
      // 指定日を活性表示にする。
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする。
      mo00020Flag.value = false
    }
  }
)

/**
 * 「日付印刷区分」ラジオボタン押下
 */
watch(
  () => mo00039Type.value,
  (newValue) => {
    // 「指定日を印刷する」を選択する場合
    if (Or51583Const.DEFAULT.STR.TWO === newValue) {
      // 指定日を活性表示にする
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする
      mo00020Flag.value = false
    }
  }
)

/**
 * 「利用者選択方法」ラジオボタン選択
 */
watch(
  () => mo00039OneWayUserSelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    // 利用者選択方法が「単一」の場合
    if (Or51583Const.DEFAULT.TANI === newValue) {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width: 206px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }

      // 利用者一覧明細に親画面.利用者IDが存在する場合
      if (local.userId) {
        // 利用者IDを対するレコードを選択状態にする
        orX0130Oneway.userId = local.userId
      }
      // 親画面.利用者IDが存在しない場合
      else {
        orX0130Oneway.userId = Or51583Const.DEFAULT.STR.EMPTY
      }
    } else {
      // 復元
      orX0117Oneway.type = Or51583Const.DEFAULT.STR.ONE
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width: 430px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }
    }
  }
)

/**
 * 「履歴選択方法」ラジオボタン押下
 */
watch(
  () => mo00039OneWayHistorySelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    if (OrX0128Logic.event.get(orX0128.value.uniqueCpId)) {
      if (
        OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList &&
        OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList.length > 0
      ) {
        local.historyNoSelect = false
      } else {
        local.historyNoSelect = true
      }
    } else {
      local.historyNoSelect = true
    }

    // 履歴選択方法が「単一」の場合
    if (Or51583Const.DEFAULT.TANI === newValue) {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.TANI
    } else {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.HUKUSUU
      orX0117Oneway.type = Or51583Const.DEFAULT.STR.ZERO
    }
  }
)

/**
 * 「履歴選択」の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()
    if (newValue) {
      if (newValue.historyDetClickFlg) {
        local.orX0128DetList = newValue.orX0128DetList
        if (newValue.orX0128DetList.length > 0) {
          local.historyNoSelect = false
        } else {
          local.historyNoSelect = true
        }
      } else {
        local.historyNoSelect = true
      }
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickFlg) {
      if (newValue.userList.length > 0) {
        local.userNoSelect = false
        local.selectUserId = newValue.userList[0].userId

        local.userList = []
        for (const item of newValue.userList) {
          if (item) {
            local.userList.push({
              userId: item.userId,
              userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
            } as UserEntity)
          }
        }

        // 利用者選択方法が「単一」の場合
        if (Or51583Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
          if (initFlag.value) {
            // アセスメント履歴情報を取得する
            await getPrintSettingsHistoryList()
          }
        }
        // 利用者選択方法が「複数」の場合
        else if (Or51583Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 利用者一覧明細に前回選択された利用者が選択状態になる
          createReportOutputData(local.prtNo)
        }
      } else {
        local.userNoSelect = true
        local.userList = []
      }
    } else {
      local.userNoSelect = true
      local.userList = []
    }
  }
)

/**
 * イベントリスナーの解除
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await close()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        class="or51583_row"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="2"
          class="table-header"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="mo01334Oneway"
            class="list-wrapper"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="3"
          class="content_center"
        >
          <c-v-row
            no-gutter
            class="printerOption customCol or51583_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayBasicSettings"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or51583_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="label_left"
            >
              <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayTypeTitle"></base-mo00615>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or51583_row"
            style="padding-top: 0px"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="or51583-pd-8"
              style="padding-top: 0px !important"
            >
              <base-mo00045
                v-model="mo00045TitleType"
                class="w-100"
                :oneway-model-value="localOneway.mo00045OneWay"
              ></base-mo00045>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or51583_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              style="padding-left: 0px; padding-right: 0px"
            >
              <base-mo00039
                v-model="mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              style="padding-left: 0px; padding-right: 8px"
            >
              <base-mo00020
                v-if="mo00020Flag"
                v-model="mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="7"
          class="content_center"
        >
          <c-v-row
            class="or51583_row"
            no-gutter
            style="align-items: center; height: 90px"
          >
            <c-v-col
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or51583_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  class="or51583-pd-8"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or51583_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00039
                    v-model="mo00039OneWayUserSelectType"
                    :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="kijunbiFlag"
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or51583_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  class="or51583-pd-8"
                >
                  <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayType"> </base-mo00615>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or51583_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00020
                    v-model="mo00020TypeKijunbi"
                    :oneway-model-value="localOneway.mo00020KijunbiOneWay"
                  />
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-else
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or51583_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  class="or51583-pd-8"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or51583_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00039
                    v-model="mo00039OneWayHistorySelectType"
                    :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="tantoIconBtn"
              cols="12"
              sm="4"
              class="or51583-pd-8"
            >
              <!-- 担当ケアマネプルダウン -->
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
                @update:model-value="orx0145UpdateModelValue"
              ></g-custom-or-x-0145>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            class="or51583_row"
            no-gutter
          >
            <c-v-col
              :cols="userCols"
              class="or51583-pd-8"
              style="padding-right: 0px"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode && initFlag"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag && initFlag"
              cols="8"
              class="or51583-pd-8"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0128
                v-if="orX0128OnewayModel.singleFlg"
                v-bind="orX0128"
                :oneway-model-value="orX0128OnewayModel"
              ></g-custom-or-x-0128>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          class="mx-2"
          @click="close"
        >
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          @click="pdfDownload()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21814 v-bind="or21813" />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>
<style>
.or51583_content {
  padding: 0px !important;
}
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';

.or51583_row {
  margin: 0px !important;
}

.table-header {
  padding: 8px;
}

.content_center {
  border-left: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .printerOption {
    background-color: #edf1f7;
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }

  .or51583-pd-8 {
    padding: 8px;
  }
}
</style>
