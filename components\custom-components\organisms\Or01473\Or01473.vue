<script setup lang="ts">
/**
 * Or11017:支援経過確認一覧
 * GUI01259_支援経過確認一覧
 *
 * @description
 * 支援経過確認一覧
 *
 * <AUTHOR> DAO DINH DUONG
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { v4 as uuidv4 } from 'uuid'
import { useI18n } from 'vue-i18n'
import { useSetupChildProps, useScreenTwoWayBind, useScreenStore } from '#imports'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01352OnewayType } from '~/types/business/components/Mo01352Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or27584Type } from '~/types/cmn/business/components/Or27584Type'
import type {
  SupportElapsedRecordSelectInEntity,
  SupportElapsedRecordSelectOutEntity,
} from '~/repositories/cmn/entities/supportElapsedRecordSelectEntity'
import type {
  supportElapsedRecordUpdateInEntity,
  supportElapsedRecordUpdateOutEntity,
} from '~/repositories/cmn/entities/supportElapsedRecordUpdateEntity'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { Or27584Const } from '~/components/custom-components/organisms/Or27584/Or27584.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or27584Logic } from '~/components/custom-components/organisms/Or27584/Or27584.logic'
import { Or01473Const } from '~/components/custom-components/organisms/Or01473/Or01473.constants'
import { Or01473Logic } from '~/components/custom-components/organisms/Or01473/Or01473.logic'
import type { Or27432OnewayType, Or27432Type } from '~/types/cmn/business/components/Or27432Type'
import { Or27432Const } from '~/components/custom-components/organisms/Or27432/Or27432.constants'
import { Or27432Logic } from '~/components/custom-components/organisms/Or27432/Or27432.logic'
import type { Or11017OnewayType } from '~/types/cmn/business/components/Or11017Type'
import type {
  supportElapsedRecord,
  ParamComfirm,
} from '~/components/custom-components/organisms/Or01473/Or01473.type'

interface checkBox {
  modelValue: boolean
}
interface Props extends Or11017OnewayType {
  uniqueCpId: string
  isHasPermission: boolean
}
/**
 * 親コンポーネントから渡されたプロパティを定義
 */
const props = defineProps<Props>()
const systemCommonsStore = useSystemCommonsStore()
/**
 * 国際化関数の取得
 */
const { t } = useI18n()
const screenStore = useScreenStore()
// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => screenStore.getCpNavControl(props.uniqueCpId))
/**
 * 修正ポップアップ（Or21814）用のユニークCP ID
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 * 修正ポップアップ（Or21813）用のユニークCP ID
 */
const or21813 = ref({ uniqueCpId: '' })
const localOneWay = reactive({
  mo01352Oneway: {
    showItemLabel: false,
    hideDetails: true,
    maxlength: '10',
    customClass: new CustomClass({ labelClass: 'ma-1' }),
    showSelectArrow: true,
    width: '140',
  } as Mo01352OnewayType,
  mo00039Oneway: {
    showItemLabel: false,
    hideDetails: true,
    checkOff: false,
  } as Mo00039OnewayType,
  mo00611FilterBtnOneWay: {
    tooltipText: t('絞込み（表示設定）を行います'),
    btnLabel: t('label.filter'),
  } as Mo00611OnewayType,
  memoInputIconBtn: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
  } as Mo00009OnewayType,
})
function addOneMonth(dateStr: string) {
  const [yearStr, monthStr] = dateStr.split('/')
  let year = parseInt(yearStr, 10)
  let month = parseInt(monthStr, 10)
  month += 1
  if (month > 12) {
    month = 1
    year += 1
  }
  const formattedMonth = month.toString().padStart(2, '0')
  return `${year}/${formattedMonth}`
}
const Ymd = ref<Mo00020Type>({
  value: props.ym,
})
const taishoYm = ref<Mo00020Type>({
  value: addOneMonth(props.ym),
})
const { refValue } = useScreenTwoWayBind<supportElapsedRecord[]>({
  cpId: Or01473Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
/**
 *Or27584のID
 */
const or27584 = ref({ uniqueCpId: '', isCheckedMode: false })
/**
 *Or27432のID
 */
const or27432 = ref<Or27432OnewayType>({
  uniqueCpId: '',
  careLevelLock: '0',
  genderLock: '0',
  careManagerLock: '0',
})
const Or27432ModelValue = ref<Or27432Type>({
  writer: { value: '' },
  careLevel: { modelValue: '' },
  gender: '',
  careManager: '',
  plan1: '',
  plan2: '',
  plan3: '',
  plan4: '',
})
useSetupChildProps(props.uniqueCpId, {
  [Or27584Const.CP_ID(0)]: or27584.value,
  [Or27432Const.CP_ID(0)]: or27432.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
})
/**
 * 削除確認ダイアログを表示する
 *
 * @param param
 */
const openPopupComfirm = (param: ParamComfirm) => {
  return new Promise((resolve) => {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        isOpen: true,
        dialogText: param.message,
        dialogTitle: t('label.confirm'),
        firstBtnType: 'normal1',
        firstBtnLabel: param?.firstBtnLabel || t('btn.yes'),
        secondBtnType: param?.secondBtnType || 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnLabel: t('btn.cancel'),
        thirdBtnType: param?.thirdBtnType || 'blank',
      },
    })
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        if (event?.firstBtnClickFlg) {
          if (param.excuteFunction) {
            void param.excuteFunction()
          }
          resolve(true)
        }
        if (event?.secondBtnClickFlg) {
          if (param.excuteFunction1) {
            void param.excuteFunction1()
          }
        }
        if (event?.thirdBtnClickFlg || event?.closeBtnClickFlg) {
          if (param.excuteFunction2) {
            void param.excuteFunction2()
          }
        }
        resolve(false)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
      },
      { once: true }
    )
  })
}
const showDialogOr27584 = computed(() => {
  // Or27584 cks_flg=1 のダイアログ開閉状態
  return Or27584Logic.state.get(or27584.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr27432 = computed(() => {
  return Or27432Logic.state.get(or27432.value.uniqueCpId)?.isOpen ?? false
})
const optionsSI022 = ref([
  { value: '1', label: t('利用者名') },
  { value: '2', label: t('利用者番号') },
  { value: '3', label: t('訪問日') },
])
const optionsSI025 = ref([
  { value: '1', label: t('昇順') },
  { value: '2', label: t('降順') },
])
const SI022 = ref('1')
const SI025 = ref('1')
const headers = ref([
  {
    title: '',
    key: 'checkBox',
    width: '45px',
    sortable: false,
  },
  {
    title: t('label.user-full-name'),
    key: 'Information',
    width: '265px',
    sortable: false,
  },
  {
    title: t('label.recorder'),
    key: 'shokuKnj',
    width: '176px',
    sortable: false,
  },
  {
    title: t('label.last-visit-date'),
    key: 'yymmYmd',
    width: '165px',
    sortable: false,
  },
  {
    title: t('label.weekly-plan-keikakusho', ['(1)']),
    key: 'kkak1Kbn',
    width: '128px',
    sortable: false,
  },
  {
    title: t('label.weekly-plan-keikakusho', ['(2)']),
    key: 'kkak2Kbn',
    width: '128px',
    sortable: false,
  },
  {
    title: t('label.week-plan'),
    key: 'weekKbn',
    width: '128px',
    sortable: false,
  },
  {
    title: t('label.use-slip'),
    key: 'riyoKbn',
    width: '128px',
    sortable: false,
  },
])
function getFirstAndLastDate(yyyyMM: string): { firstDay: string; lastDay: string } {
  const [yearStr, monthStr] = yyyyMM.split('/')
  const year: number = parseInt(yearStr, 10)
  const month: number = parseInt(monthStr, 10)

  const firstDate: Date = new Date(year, month - 1, 1)
  const lastDate: Date = new Date(year, month, 0)

  const format = (date: Date): string => {
    const y = date.getFullYear()
    const m = String(date.getMonth() + 1).padStart(2, '0')
    const d = String(date.getDate()).padStart(2, '0')
    return `${y}/${m}/${d}`
  }

  return {
    firstDay: format(firstDate),
    lastDay: format(lastDate),
  }
}
function isValidDateString(dateStr: string) {
  if (!/^\d{4}\/\d{2}\/\d{2}$/.test(dateStr)) return false
  const parsedDate = new Date(dateStr.replace(/\//g, '-'))
  return !isNaN(parsedDate.getTime())
}
const maxRecord = ref('')
const selectedMap = ref<Record<string, checkBox>>({})
let isIniting = false
watch(
  [Ymd, taishoYm],
  (newVal, oldVal) => {
    const newVal1 = newVal[0].value
    const newVal2 = newVal[1].value
    if (isEdit.value && !isIniting) {
      isIniting = true
      Ymd.value.value = oldVal[0].value
      taishoYm.value.value = oldVal[1].value
      const initnewValue = async () => {
        Ymd.value.value = newVal1
        taishoYm.value.value = newVal2
        await init()
        isIniting = false
      }
      const initAndSave = async () => {
        await onSave()
        initnewValue()
      }
      const param: ParamComfirm = {
        message: t('message.i-cmn-10430'),
        excuteFunction: initAndSave,
        excuteFunction1: initnewValue,
        excuteFunction2: () => (isIniting = false),
        thirdBtnType: 'normal3',
      }
      openPopupComfirm(param)
    } else if (!isEdit.value) {
      init()
      isIniting = false
    }
  },
  {
    deep: true,
  }
)
const init = async () => {
  const paramGetList: SupportElapsedRecordSelectInEntity = {
    svJigyoId: props.svJigyoId ?? systemCommonsStore.getSvJigyoId ?? '',
    userid: props.userid ?? systemCommonsStore.getUserId ?? '',
    symd: getFirstAndLastDate(Ymd.value.value).firstDay,
    eymd: getFirstAndLastDate(Ymd.value.value).lastDay,
    taishoYm: taishoYm.value.value,
  }
  const res: SupportElapsedRecordSelectOutEntity = await ScreenRepository.select(
    'supportElapsedRecordSelect',
    paramGetList
  )
  maxRecord.value = res.data.displayOrderMaxValueInfo.maxValue
  const listUser = systemCommonsStore.getUserSelectUserList(regionKey.value)
  Or01473Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: res.data.supportElapsedRecordList.map((item) => {
      const user = listUser?.find((user) => user.selfId === item.userid)
      const userName = `${user?.nameSei}　${user?.nameMei}`
      selectedMap.value[item.uniqueId] = { modelValue: false }
      return {
        ...item,
        SI043: item.userid,
        SI044: user ? userName : '',
        SI045: user ? t('label.nursing-care-required') + user?.levelOfCareRequired : '',
        SI046: user ? (String(user?.gender) === '2' ? t('label.woman') : t('label.man')) : '',
        isScheduled: item.houmonFlg === '1',
      }
    }),
    isInit: true,
  })
}
onMounted(async () => {
  await init()
})
const itemsToShow = computed(() => {
  let arr = [...refValue.value!]
  if (Or27432ModelValue.value.gender === '0') {
    arr = arr.filter((item) => item.SI046 === t('label.woman'))
  } else if (Or27432ModelValue.value.gender === '1') {
    arr = arr.filter((item) => item.SI046 === t('label.man'))
  }
  const plan1 = Or27432ModelValue.value.plan1
  if (['0', '1', '2'].includes(plan1)) {
    arr = arr.filter((item) => item.kkak1Kbn === plan1)
  }
  const plan2 = Or27432ModelValue.value.plan2
  if (['0', '1', '2'].includes(plan2)) {
    arr = arr.filter((item) => item.kkak2Kbn === plan2)
  }
  const plan3 = Or27432ModelValue.value.plan3
  if (['0', '1', '2'].includes(plan3)) {
    arr = arr.filter((item) => item.weekKbn === plan3)
  }
  const plan4 = Or27432ModelValue.value.plan4
  if (['0', '1', '2'].includes(plan4)) {
    arr = arr.filter((item) => item.riyoKbn === plan4)
  }
  return arr.sort((a, b) => {
    let valA, valB
    if (SI022.value === '1') {
      valA = a.SI044
      valB = b.SI044
      if (valA !== valB) {
        return (SI025.value === '1' ? 1 : -1) * valA.localeCompare(valB, 'ja')
      }
    }
    if (SI022.value === '2') {
      valA = a.SI043
      valB = b.SI043
      if (valA !== valB) {
        return (SI025.value === '1' ? 1 : -1) * (Number(valA) < Number(valB) ? -1 : 1)
      }
    }
    if (SI022.value === '3') {
      if (a.isScheduled) {
        valA = a.yymmYmd
      } else {
        valA = ''
      }
      if (b.isScheduled) {
        valB = b.yymmYmd
      } else {
        valB = ''
      }
      const isValidA = isValidDateString(valA)
      const isValidB = isValidDateString(valB)
      if (isValidA && isValidB && valA !== valB) {
        const dateA = new Date(valA)
        const dateB = new Date(valB)
        return (SI025.value === '1' ? 1 : -1) * (dateA < dateB ? -1 : 1)
      }
      if (!isValidA && isValidB) return 1
      if (isValidA && !isValidB) return -1
    }
    return 0
  })
})
/**
 *  ボタン押下時の処理(Or27584)
 *
 * @param item
 *
 * @param isCheckedMode
 */
function onClickOr27584(item?: Or27584Type, isCheckedMode = false) {
  // Or27584 ダイアログ開閉状態を更新する
  or27584.value.isCheckedMode = isCheckedMode
  selectedRow.value = item
  Or27584Logic.state.set({
    uniqueCpId: or27584.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 *  ボタン押下時の処理(Or27432)
 *
 */
function onClickOr27432() {
  // Or27432 ダイアログ開閉状態を更新する
  Or27432Logic.state.set({
    uniqueCpId: or27432.value.uniqueCpId,
    state: { isOpen: true },
  })
}
const handleShowChecked = (item: supportElapsedRecord, key: string, keyCheckOff: string) => {
  if (!item.isScheduled) return ''
  if (item[key] === '1') {
    return t('label.filled-circle')
  } else if (item[key] === '2') {
    return t('label.circle')
  } else if (item[key] === '0' && item[keyCheckOff] === '1') {
    return t('label.circle')
  }
  return ''
}
const checkAll = ref<Mo00018Type>({ modelValue: false })
/**
 * 選択行かどうか判定
 *
 * @param item - 対象の行
 *
 * @returns true: 選択中, false: 非選択
 */
const isSelected = (item: supportElapsedRecord) => allCheckedItemId.value.includes(item.uniqueId)
const allCheckedItemId = computed(() =>
  refValue
    .value!.filter((item) => selectedMap.value[item.uniqueId]?.modelValue)
    .map((item) => item.uniqueId)
)
const isIndeterminate = computed(
  () => allCheckedItemId.value.length && allCheckedItemId.value.length < refValue.value!.length
)
watch(checkAll, () => {
  if (checkAll.value.modelValue) {
    refValue.value!.forEach((item) => {
      selectedMap.value[item.uniqueId].modelValue = true
    })
  } else {
    refValue.value!.forEach((item) => {
      selectedMap.value[item.uniqueId].modelValue = false
    })
  }
})
watch(allCheckedItemId, () => {
  if (allCheckedItemId.value.length === refValue.value!.length) {
    checkAll.value.modelValue = true
  } else {
    checkAll.value.modelValue = false
  }
})
const handleSelectVisit = (key: string) => {
  const arrSelectedList = refValue.value!.filter((item) =>
    allCheckedItemId.value.includes(item.uniqueId)
  )
  if (key === 'shokuKnj' || key === 'yymmYmd') {
    onClickOr27584(undefined, true)
  } else {
    arrSelectedList.forEach((item) => {
      if (!item.isScheduled) return
      item[key] = '1'
    })
  }
}
const selectedRow = ref<Or27584Type>()
const handleSelectSingleCell = (item: supportElapsedRecord, key: string) => {
  if (item.isScheduled) {
    if (item[key] !== '1') {
      item[key] = '1'
    } else {
      if (key === 'kkak1Kbn') {
        item[key] = item.kkak1KbnPreviousSetingsFlg === '1' ? '2' : '0'
      } else if (key === 'kkak2Kbn') {
        item[key] = item.kkak2KbnPreviousSetingsFlg === '1' ? '2' : '0'
      } else if (key === 'weekKbn') {
        item[key] = item.weekKbnPreviousSetingsFlg === '1' ? '2' : '0'
      } else if (key === 'riyoKbn') {
        item[key] = item.riyoKbnPreviousSetingsFlg === '1' ? '2' : '0'
      }
    }
  } else {
    onClickOr27584(item)
  }
}
const regionKey = ref(Or00248Const.DEFAULT.REGION_KEY)
const update = (val: Or27584Type, isCheckedMode: boolean) => {
  if (isCheckedMode) {
    refValue
      .value!.filter((item) => allCheckedItemId.value.includes(item.uniqueId))
      .forEach((item) => {
        item.kkak1Kbn =
          val.kkak1Kbn === '1' ? '1' : val.kkak1KbnPreviousSetingsFlg === '1' ? '2' : '0'
        item.kkak2Kbn =
          val.kkak2Kbn === '1' ? '1' : val.kkak2KbnPreviousSetingsFlg === '1' ? '2' : '0'
        item.weekKbn = val.weekKbn === '1' ? '1' : val.weekKbnPreviousSetingsFlg === '1' ? '2' : '0'
        item.riyoKbn = val.riyoKbn === '1' ? '1' : val.riyoKbnPreviousSetingsFlg === '1' ? '2' : '0'
        item.shokuKnj = val.shokuKnj
        item.yymmYmd = val.yymmYmd
        item.isScheduled = val.isScheduled
      })
  } else {
    selectedRow.value!.kkak1Kbn = val.kkak1Kbn
    selectedRow.value!.kkak2Kbn = val.kkak2Kbn
    selectedRow.value!.weekKbn = val.weekKbn
    selectedRow.value!.riyoKbn = val.riyoKbn
    selectedRow.value!.shokuKnj = val.shokuKnj
    selectedRow.value!.yymmYmd = val.yymmYmd
    selectedRow.value!.isScheduled = val.isScheduled
  }
}
const onSave = async () => {
  if (!isEdit.value) {
    const param: ParamComfirm = {
      message: t('message.i-cmn-21800'),
      firstBtnLabel: t('OK'),
      secondBtnType: 'blank',
    }
    openPopupComfirm(param)
    return false
  }
  const param: supportElapsedRecordUpdateInEntity = {
    supportElapsedRecordList: refValue.value!.map((item) => ({
      biko_kbn: item.bikoKbn,
      houjin_id: item.houjinId,
      shisetu_id: item.shisetuId,
      yymm_ymd: item.yymmYmd,
      userid: item.userid,
      rec_no: item.recNo,
      time_hh: item.timeHh,
      time_mm: item.timeMm,
      case_cd: item.caseCd,
      case_knj: item.caseKnj,
      staffid: item.staffid,
      case_flg: item.caseFlg,
      moushiokuri_flg: item.moushiokuriFlg,
      base_rec_no: item.baseRecNo,
      system_flg: item.systemFlg,
      shiji_flg: item.shijiFlg,
      kyouyu_biko_kbn: item.kyouyuBikoKbn,
      jyo_flg: item.jyoFlg,
      unique_id: item.uniqueId,
      base_unique_id: item.baseUniqueId,
      time_stmp: item.timeStmp,
      sv_jigyo_id: item.svJigyoId,
      seq_no: item.seqNo,
      end_hh: item.endHh,
      end_mm: item.endMm,
      totaltime: item.totaltime,
      title_knj: item.titleKnj,
      houmon_flg: item.houmonFlg,
      kkak1Kbn: item.kkak1Kbn,
      kkak2Kbn: item.kkak2Kbn,
      weekKbn: item.weekKbn,
      riyoKbn: item.riyoKbn,
      taisho_ym: item.taishoYm,
      tanto_id: item.tantoId,
      cps_keika2_id: item.cpsKeika2Id,
      modify_flg: item.modifyFlg,
      shoku_knj: item.shokuKnj,
      chi_jigyo_id: item.chiJigyoId,
      shurui_cd: item.shuruiCd,
      modified_cnt: item.modifiedCnt,
    })),
  }
  const resData: supportElapsedRecordUpdateOutEntity = await ScreenRepository.update(
    'supportElapsedRecordUpdate',
    param
  )
  if (resData.statusCode === 'success') {
  }
}
defineExpose({
  onSave,
})
</script>
<template>
  <div class="container">
    <c-v-row
      class="py-5 border-bottom"
      no-gutters
    >
      <c-v-row no-gutters>
        <c-v-col cols="auto">
          <base-mo-01338 :oneway-model-value="{ value: t('記録年月') }" />
        </c-v-col>
        <c-v-col cols="auto">
          <base-mo01352
            v-model="Ymd"
            :oneway-model-value="localOneWay.mo01352Oneway"
          />
        </c-v-col>
        <c-v-col cols="auto">
          <base-mo-01338 :oneway-model-value="{ value: t('label.plan-target') }" />
        </c-v-col>
        <c-v-col cols="auto">
          <base-mo01352
            v-model="taishoYm"
            :oneway-model-value="localOneWay.mo01352Oneway"
          />
        </c-v-col>
        <c-v-col cols="auto">
          <base-mo-01338 :oneway-model-value="{ value: t('label.yyyy-mm') }" />
        </c-v-col>
      </c-v-row>
      <div class="flex-container">
        <base-at-icon
          style="cursor: pointer"
          icon="print"
        />
      </div>
    </c-v-row>
    <c-v-row
      class="mt-2"
      no-gutters
    >
      <c-v-col cols="3">
        <div class="flex-container">
          <base-mo00611
            :oneway-model-value="localOneWay.mo00611FilterBtnOneWay"
            @click="onClickOr27432"
          />
        </div>
      </c-v-col>
      <c-v-col cols="5">
        <div class="flex-container">
          {{ t('label.display-order') }}
          <base-mo00039
            v-model="SI022"
            style="margin-right: 0 !important; display: flex; align-items: center"
            :oneway-model-value="localOneWay.mo00039Oneway"
          >
            <base-at-radio
              v-for="item in optionsSI022"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
        </div>
      </c-v-col>
      <c-v-col cols="4">
        <div class="flex-container">
          {{ t('label.ascending') }}/{{ t('label.descending') }}
          <base-mo00039
            v-model="SI025"
            style="margin-right: 0 !important; display: flex; align-items: center"
            :oneway-model-value="localOneWay.mo00039Oneway"
          >
            <base-at-radio
              v-for="item in optionsSI025"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
        </div>
      </c-v-col>
    </c-v-row>
    <c-v-data-table
      :items-per-page="9999"
      fixed-header
      :items="itemsToShow"
      :headers="headers"
      class="table-wrapper overflow-y-auto elevation-1 table-container"
      hide-default-footer
      hover
    >
      <template #headers="props">
        <tr>
          <th
            v-for="header in props.headers[0]"
            :key="header.key"
            :style="`width: ${header.width};`"
          >
            <div v-if="header.key === 'Information'">{{ header.title }}</div>
            <base-mo00018
              v-else-if="header.key === 'checkBox'"
              :class="isIndeterminate ? 'indeterminate' : ''"
              v-model="checkAll"
              :oneway-model-value="{ showItemLabel: false, indeterminate: isIndeterminate }"
            />
            <div
              v-else
              class="flex-start"
            >
              {{ header.title }}
              <base-mo00009
                :oneway-model-value="localOneWay.memoInputIconBtn"
                @click="handleSelectVisit(header.key)"
              />
            </div>
          </th>
        </tr>
      </template>
      <template #item="{ item }">
        <tr :class="{ 'select-row': isSelected(item) }">
          <td>
            <base-mo00018
              v-model="selectedMap[item.uniqueId]"
              :oneway-model-value="{ showItemLabel: false }"
            />
          </td>
          <td>
            <c-v-row no-gutters>
              <c-v-col cols="12">
                <c-v-row
                  class="infomation-user"
                  no-gutters
                  justify="space-between"
                >
                  <div>{{ item.SI043 }}</div>
                  {{ item.SI045 }}
                </c-v-row>
                <c-v-row
                  align="center"
                  no-gutters
                  justify="space-between"
                >
                  {{ item.SI044 }}
                  <div
                    :class="[
                      {
                        'red-style': item.SI046 === t('label.woman'),
                        'blue-style': item.SI046 === t('label.man'),
                      },
                      'infomation-user',
                    ]"
                  >
                    {{ item.SI046 }}
                  </div>
                </c-v-row>
              </c-v-col>
            </c-v-row>
          </td>
          <td @click="onClickOr27584(item)">{{ item.isScheduled ? item.shokuKnj : '' }}</td>
          <td
            :class="item.isScheduled ? '' : 'red-style'"
            @click="onClickOr27584(item)"
          >
            {{ item.isScheduled ? item.yymmYmd : t('label.not-registered') }}
          </td>
          <td
            style="text-align: center"
            @click="handleSelectSingleCell(item, 'kkak1Kbn')"
          >
            {{ handleShowChecked(item, 'kkak1Kbn', 'kkak1KbnPreviousSetingsFlg') }}
          </td>
          <td
            style="text-align: center"
            @click="handleSelectSingleCell(item, 'kkak2Kbn')"
          >
            {{ handleShowChecked(item, 'kkak2Kbn', 'kkak2KbnPreviousSetingsFlg') }}
          </td>
          <td
            style="text-align: center"
            @click="handleSelectSingleCell(item, 'weekKbn')"
          >
            {{ handleShowChecked(item, 'weekKbn', 'weekKbnPreviousSetingsFlg') }}
          </td>
          <td
            style="text-align: center"
            @click="handleSelectSingleCell(item, 'riyoKbn')"
          >
            {{ handleShowChecked(item, 'riyoKbn', 'riyoKbnPreviousSetingsFlg') }}
          </td>
        </tr>
      </template>
    </c-v-data-table>
    <div class="SI065">{{ maxRecord + t('名') }}</div>
    <div class="SI066">{{ t('※●：訪問日にチェックあり　〇：訪問日以前にチェックあり') }}</div>
  </div>
  <g-custom-or-27584
    v-if="showDialogOr27584"
    :model-value="selectedRow"
    v-bind="or27584"
    @update:model-value="update"
  />
  <g-custom-or-27432
    v-if="showDialogOr27432"
    v-model="Or27432ModelValue"
    v-bind="or27432"
  />
  <g-base-or21814 v-bind="or21814" />
  <g-base-or21813 v-bind="or21813" />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/page-data-table.scss';
:deep(.indeterminate) {
  .v-selection-control__input i {
    color: #666666;
  }
}
.container {
  height: 100%;
  position: relative;
  width: 100%;
  padding-bottom: 20px;
  overflow-y: auto;
}
.border-bottom {
  border-bottom: 1px solid #ccc;
}
.flex-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-start {
  display: flex;
  justify-content: start;
  align-items: center;
}
:deep(.table-container) {
  .mr-2 {
    margin-right: 0 !important;
  }
  th:first-child {
    padding: 0;
  }
  td:first-child {
    padding: 0;
  }
  max-height: 74%;
  margin-top: 12px;
  table {
    table-layout: fixed !important;
  }
}
.red-style {
  color: rgb(var(--v-theme-red-700));
}
.infomation-user {
  font-size: 12px;
  &.red-style {
    color: rgb(var(--v-theme-red-700));
  }
  &.blue-style {
    color: rgb(var(--v-theme-blue-700));
  }
}
.SI065 {
  position: absolute;
  bottom: 0;
  color: rgb(var(--v-theme-black-300));
  font-size: 12px;
}
.SI066 {
  position: absolute;
  bottom: 0;
  right: 0;
  color: rgb(var(--v-theme-black-300));
  font-size: 12px;
}

:deep(.table-wrapper table tbody tr td) {
  height: 64px !important;
  max-height: 64px !important;
}
</style>
