<script setup lang="ts">
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or26287Const } from '~/components/custom-components/organisms/Or26287/Or26287.constants'
import { Or26287Logic } from '~/components/custom-components/organisms/Or26287/Or26287.logic'
import type { Or26287OnewayType } from '~/types/cmn/business/components/Or26287Type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 * GUI01151_サービス選択モーダル
 * KMD 靳先念 2025/06/25 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI01151'
// ルーティング
const routing = 'GUI01151/pinia'
// 画面物理名
const screenName = 'GUI01151'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const localOneway = reactive({
  Or26287Oneway: {
    mainParm: '11',
    kinouName: 'PRT',
    yokaiKbn: '1',
    yokaiKbn2: '2',
    termId: '1',
    chsGaibuFlg: '0',
    teiYmdYm: '2025/07/01',
    adw: '1',
    svParm: [
      {
        svtype: '1',
        svJigyoId: '1',
        svkbn: '1',
        adsTsukihi: '1',
        adsKihon: '1',
        adsKasan: '',
      },
    ],
    svtype: '1',
    hokenId: '1',
  } as Or26287OnewayType,
})
/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01151' },
})

/**************************************************
 * Props
 **************************************************/
const Or26287 = ref({ uniqueCpId: '' })
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 自身のPinia領域をセットアップ
const { childCpIds } = useInitialize({
  cpId: 'GUI01151',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or26287Const.CP_ID(0) }],
})
Or26287Logic.initialize(childCpIds.Or26287.uniqueCpId)
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or26287Const.CP_ID(0)]: Or26287.value,
})

/**************************************************
 * Props
 **************************************************/

// ダイアログ表示フラグ
const showDialogOr26287 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or26287Logic.state.get(Or26287.value.uniqueCpId)?.isOpen ?? false
})

/***
 * ボタン押下時の処理
 */
function Or26287OnClick_0() {
  Or26287Logic.state.set({
    uniqueCpId: Or26287.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**************************************************
 * 印刷設定画面
 * KMD 靳先念 2025/07/18 ADD START
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="Or26287OnClick_0()"
        >GUI01151_サービス選択モーダル
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-26287
    v-if="showDialogOr26287"
    v-bind="Or26287"
    :oneway-model-value="localOneway.Or26287Oneway"
    :unique-cp-id="Or26287.uniqueCpId"
    :parent-cp-id="pageComponent.uniqueCpId"
  />
</template>
