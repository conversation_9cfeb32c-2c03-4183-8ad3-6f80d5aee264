<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or32418Const } from '../Or32418/Or32418.constants'
import { Or51773Const } from '../Or51773/Or51773.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import type {
  RirekiInfo,
  InfoCollectionInfoType,
} from '~/types/cmn/business/components/TeX0005Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Or32418OnewayType, Or32418Type } from '~/types/cmn/business/components/Or32418Type'
import type { Or21744StateType } from '~/components/base-components/organisms/Or21744/Or21744.type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo01280OnewayType } from '~/types/business/components/Mo01280Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import { useScreenTwoWayBind, useSetupChildProps } from '#imports'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'

/**
 * Or32418：有機体：GUI00682_［情報収集］画面（10）画面
 *
 * @description
 * ［情報収集］画面（10）
 *
 * <AUTHOR> 李晨昊
 */

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or32418OnewayType
  uniqueCpId: string
  modelValue: Or32418Type
}

const props: Props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
const selectedRow = ref<{ groupIndex: number; subIndex: number } | null>(null)
// 子コンポーネント用変数
const or21815 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(1) })
const contentRef = ref<HTMLDivElement | null>(null)

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
})
const Mo0009OnewayModelValue: Mo00009OnewayType = {
  icon: true,
  btnIcon: 'edit_square',
  prependIcon: 'edit_square',
  density: 'compact',
}

const defaultOnewayModelValue: Or32418OnewayType = {
  periodManageFlag: '0',
  rirekiInfo: {} as RirekiInfo,
  deleteFlg: false,
}

const localOneway = reactive({
  or32418Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // アセスメント表タイトル
  mo01338Oneway: {
    value: '',
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: 'contentTitle',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 全て[〇]コンポーネント
  mo00611OnewayAllRound: {
    btnLabel: t('btn.all-round'),
    width: '90px',
  },
  // 全て[×]コンポーネント
  mo00611OnewayAllWrong: {
    btnLabel: t('btn.all-wrong'),
    width: '90px',
  },
  // 全解除コンポーネント
  mo00611OnewayCancelAll: {
    btnLabel: t('btn.full-release'),
    width: '90px',
  },

  //情報収集画面（1）リスト.第３階層書式１が「1」または「2」の場合、半角52
  mo01280oneway_one: {
    // デフォルト値の設定
    maxLength: 4000,
    rows: '3',
  } as Mo01280OnewayType,
  // ******Visioラジオグループセクション******
  cnsiderOneway: {
    customClass: new CustomClass({ outerClass: 'radio-style' }),
    showItemLabel: false,
    itemLabel: '',
    name: 'body',
    inline: false,
    items: [],
  } as Mo00039OnewayType,
  or51775OnewayTypeOther: {
    //タイトル
    title: '',
    //画面ID
    screenId: '',
    //分類ID
    bunruiId: '',
    //大分類ＣＤ
    t1Cd: '',
    //中分類ＣＤ
    t2Cd: '',
    //小分類ＣＤ
    t3Cd: '',
    //テーブル名
    tableName: '',
    //カラム名
    columnName: '',
    //アセスメント方式
    assessmentMethod: '',
    //文章内容
    inputContents: '',
    //利用者ID
    userId: '',
    //モード
    mode: '',
  } as Or51775OnewayType,
  or21744Oneway: {
    width: '100px',
    minWidth: '100px',
    disabled: false,
  } as Or21744StateType,
})

/**
 *  ラジオボタン初期化
 */
function initCodes() {
  localOneway.cnsiderOneway.items?.push({
    label: t('label.circle'),
    value: Or32418Const.DEFAULT.RIGHTSELECTED,
  })
  localOneway.cnsiderOneway.items?.push({
    label: t('label.wrong'),
    value: Or32418Const.DEFAULT.WRONGSELECTED,
  })
}

/**************************************************
 * Pinia
 **************************************************/
const { refValue: sortList } = useScreenTwoWayBind<Record<string, InfoCollectionInfoType[]>>({
  cpId: Or32418Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
sortList.value = { ...sortList.value }

/**************************************************
 * Emit
 **************************************************/

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  // 初期情報取得
  void init()
})

/**
 * 初期化
 */
const init = () => {
  // ラジオボタン初期化
  void initCodes()

  // 階層1タイトルラベル(情報収集画面詳細情報の「第1階層ID」の値、情報収集画面詳細情報の「第1階層名称」の値の組合文字列)
  if (sortList.value?.['1'] !== undefined) {
    localOneway.mo01338Oneway.value =
      sortList.value['1'][0].level1Id + ' ' + sortList.value['1'][0].level1Knj
  }
}

/**
 * 全て[〇]ボタン押下
 *
 */
function onAllSelect1Click() {
  Object.entries(sortList.value!).map((group) => {
    group[1].map((item) => {
      if (item.shosiki1Flg !== Or32418Const.DEFAULT.FORMAT_THREE) {
        item.kentoFlg = Or32418Const.DEFAULT.RIGHTSELECTED
      }
    })
  })
}

/**
 * 全て[×]ボタン押下
 *
 */
function onAllSelect2Click() {
  Object.entries(sortList.value!).map((group) => {
    group[1].map((item) => {
      if (item.shosiki1Flg !== Or32418Const.DEFAULT.FORMAT_THREE) {
        item.kentoFlg = Or32418Const.DEFAULT.WRONGSELECTED
      }
    })
  })
}

/**
 * 全解除ボタン押下
 *
 */
function onNoSelectClick() {
  Object.entries(sortList.value!).map((group) => {
    group[1].map((item) => {
      if (item.shosiki1Flg !== Or32418Const.DEFAULT.FORMAT_THREE) {
        item.kentoFlg = Or32418Const.DEFAULT.UNSELECTED
      }
    })
  })
}

//その他1課題t入力
const or51775Other = ref({ modelValue: '' })

// 選択した行の第１階層Index
const selectedItemIndex = ref<string>('')

// 選択した行の第２階層Index
const selectedItemSubIndex = ref<number>(-1)

/**
 * 「ケアマネ入力支援アイコンボタン」押下onClickOther
 *
 * @param title - id
 *
 * @param index - 第１階層Index
 *
 * @param subIndex - 第２階層Index
 */
function onClickOther(title: string, index: string, subIndex: number) {
  // その他1の入力支援ポップアップを開く
  or51775Other.value.modelValue = ''

  selectedItemIndex.value = index
  selectedItemSubIndex.value = subIndex

  // タイトルを「その他1」または「その他2」に設定
  localOneway.or51775OnewayTypeOther.title = title

  // 入力支援ポップアップを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or51773Const.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or51773Const.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  // メモ１の場合
  if (sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].memo1Knj) {
    sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].memo1Knj.value =
      setOrAppendValue(
        sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].memo1Knj.value ?? '',
        data
      )
  }

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}
/**
 * 行を選択したときの処理
 *
 * @param groupIndex - 子行
 *
 * @param subIndex - 子行
 */
function onSelectRow(groupIndex: number, subIndex: number) {
  if (selectedRow.value?.groupIndex === groupIndex && selectedRow.value?.subIndex === subIndex) {
    selectedRow.value = null
  } else {
    selectedRow.value = { groupIndex, subIndex }
  }
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * スクロールバーのスクロール
 *
 * @param event - WheelEvent
 */
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()

  const delta = Math.sign(event.deltaY)

  const contentElement = contentRef.value
  if (!contentElement) return

  const currentScroll = contentElement.scrollTop
  const maxScroll = contentElement.scrollHeight - contentElement.clientHeight

  let newScroll = currentScroll + delta * 50

  if (newScroll < 0) newScroll = 0
  if (newScroll > maxScroll) newScroll = maxScroll

  if (newScroll !== currentScroll) {
    contentElement.scrollTo({
      top: newScroll,
      behavior: 'auto',
    })
  }
}

watch(
  () => sortList.value,
  () => {
    // 階層1タイトルラベル(情報収集画面詳細情報の「第1階層ID」の値、情報収集画面詳細情報の「第1階層名称」の値の組合文字列)
    if (sortList.value?.['1'] !== undefined) {
      localOneway.mo01338Oneway.value =
        sortList.value['1'][0].level1Id + ' ' + sortList.value['1'][0].level1Knj
    }
  },
  { deep: true }
)
</script>
<template>
  <c-v-row
    no-gutters
    style="padding-top: 8px; background-color: white"
  >
    <c-v-col
      cols="12"
      class="h-100 px-2"
    >
      <!-- 3ボタン -->
      <c-v-row
        v-if="!props.onewayModelValue.hiddenAction"
        no-gutters
        class="top-button"
      >
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAllRound"
          class="mx-1"
          @click="onAllSelect1Click"
        />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAllWrong"
          class="mx-1"
          @click="onAllSelect2Click"
        />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayCancelAll"
          class="mx-1"
          @click="onNoSelectClick"
        />
      </c-v-row>
      <!-- タイトル -->
      <c-v-row no-gutters>
        <c-v-col class="h-100 px-2">
          <!-- 期間管理フラグが「1:管理する」、かつ、計画期間が登録されていない場合、非表示 -->
          <c-v-row
            no-gutters
            class="or32418-title-row"
          >
            <base-mo01338
              v-if="!props.onewayModelValue.deleteFlg"
              :oneway-model-value="localOneway.mo01338Oneway"
              class="back-unset"
            ></base-mo01338>
          </c-v-row>
          <!-- テーブル固定へーた -->
          <c-v-row
            no-gutters
            class="row-tl-border"
            style="width: calc(100% - 15px)"
          >
            <!-- No -->
            <c-v-col
              cols="auto"
              class="col-br-border tbl-title-bg col1"
              ><label class="col-pl">{{ t('label.no') }}</label></c-v-col
            >
            <!-- 情報項目 -->
            <c-v-col class="col-br-border tbl-title-bg col2"
              ><label class="col-pl">{{ t('label.info-collection-info-item') }}</label></c-v-col
            >
            <!-- 計画書様式が【居宅】、改定区分が【初版】の場合 -->
            <!-- 検討 -->
            <c-v-col
              v-if="
                localOneway.or32418Oneway.rirekiInfo &&
                localOneway.or32418Oneway.rirekiInfo.kaiteiKbn ===
                  Or32418Const.DEFAULT.KAITEIKBN_ONE
              "
              cols="auto"
              class="col-br-border tbl-title-bg col4"
              ><label class="col-pl">{{ t('label.info-collection-consider') }}</label></c-v-col
            >
            <!-- 具体的状況 -->
            <c-v-col class="col-br-border tbl-title-bg col3 width-464"
              ><label class="col-pl">{{
                t('label.info-collection-concrete-situation')
              }}</label></c-v-col
            >
            <!-- 検討 -->
            <c-v-col
              v-if="
                localOneway.or32418Oneway.rirekiInfo &&
                localOneway.or32418Oneway.rirekiInfo.kaiteiKbn ===
                  Or32418Const.DEFAULT.KAITEIKBN_ZERO
              "
              cols="auto"
              class="col-br-border tbl-title-bg col4"
              ><label class="col-pl">{{ t('label.info-collection-consider') }}</label></c-v-col
            >
          </c-v-row>
          <!-- 一覧 -->
          <div
            ref="contentRef"
            class="or32418-main-div"
          >
            <template
              v-for="(group, index) in sortList!"
              :key="index"
            >
              <div
                v-if="!props.onewayModelValue.deleteFlg"
                no-gutters
                class="row-l-border"
              >
                <c-v-row no-gutters>
                  <c-v-col
                    cols="12"
                    class="col-br-border tbl-title-bg"
                  >
                    <base-mo01337
                      :oneway-model-value="{
                        value: group[0].level2Knj,
                        valueFontWeight: 'bold',
                      }"
                    />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  v-for="(item, subIndex) in group"
                  :key="subIndex"
                  no-gutters
                  :class="{
                    'select-row':
                      selectedRow?.groupIndex === index && selectedRow?.subIndex === subIndex,
                  }"
                  class="or32418-row"
                  @click="onSelectRow(index, subIndex)"
                >
                  <!-- No -->
                  <c-v-col
                    v-show="item.shosiki1Flg !== Or32418Const.DEFAULT.FORMAT_THREE"
                    cols="auto"
                    class="col-br-border align-items-center col1 text-bg"
                    style="justify-content: center"
                    ><base-mo01336
                      :oneway-model-value="{
                        value: item.koumokuNo,
                        valueFontWeight: 'bold',
                      }"
                    />
                  </c-v-col>

                  <!-- 情報項目 -->
                  <c-v-col
                    v-show="item.shosiki1Flg === Or32418Const.DEFAULT.FORMAT_ONE"
                    class="col-br-border align-items-center col2 text-bg"
                    style="justify-content: center"
                  >
                    <!-- 情報収集画面（1）リスト.第３階層書式１が「1」の場合、フォント：太字 -->
                    <base-mo01337
                      style="background: inherit"
                      :oneway-model-value="{
                        value: item.level3Knj,
                        valueFontWeight: 'bold',
                      }"
                    />
                  </c-v-col>
                  <c-v-col
                    v-show="item.shosiki1Flg === Or32418Const.DEFAULT.FORMAT_THREE"
                    cols="2"
                    class="col-br-border align-items-center text-bg"
                  >
                    <!-- 情報収集画面（1）リスト.第３階層書式１が「1」の場合、フォント：太字 -->
                    <base-mo01337
                      style="background: inherit"
                      :oneway-model-value="{
                        value: item.level3Knj,
                      }"
                    />
                  </c-v-col>
                  <!--検討1ラジオボタン-->
                  <!--改訂区分が「1：H31/1」の場合、非表示-->
                  <c-v-col
                    v-show="
                      localOneway.or32418Oneway.rirekiInfo &&
                      localOneway.or32418Oneway.rirekiInfo.kaiteiKbn ===
                        Or32418Const.DEFAULT.KAITEIKBN_ONE &&
                      item.shosiki1Flg !== Or32418Const.DEFAULT.FORMAT_THREE
                    "
                    cols="auto"
                    class="col-br-border align-items-center col4"
                    ><div class="radio1">
                      <base-mo00039
                        v-model="item.kentoFlg"
                        style="background: inherit"
                        :oneway-model-value="localOneway.cnsiderOneway"
                      /></div
                  ></c-v-col>
                  <!-- 具体的状況 -->
                  <c-v-col class="col-br-border col3">
                    <div style="display: flex">
                      <base-mo01280
                        v-model="item.memo1Knj"
                        :oneway-model-value="localOneway.mo01280oneway_one"
                      />
                      <c-v-divider
                        vertical
                        class="mr-1 mt-1 mb-1"
                      />
                      <base-mo00009
                        :oneway-model-value="Mo0009OnewayModelValue"
                        variant="flat"
                        density="compact"
                        style="margin-top: 18px; background: inherit"
                        @click="onClickOther(item.level3Knj, index, subIndex)"
                      /></div
                  ></c-v-col>

                  <!--検討2ラジオボタン-->
                  <!--・改訂区分が「0：初版」の場合、非表示-->
                  <c-v-col
                    v-show="
                      localOneway.or32418Oneway.rirekiInfo &&
                      localOneway.or32418Oneway.rirekiInfo.kaiteiKbn ===
                        Or32418Const.DEFAULT.KAITEIKBN_ZERO &&
                      item.shosiki1Flg !== Or32418Const.DEFAULT.FORMAT_THREE
                    "
                    cols="auto"
                    class="col-br-border align-items-center col4"
                    ><div class="radio1">
                      <base-mo00039
                        v-model="item.kentoFlg"
                        :oneway-model-value="localOneway.cnsiderOneway"
                      /></div
                  ></c-v-col>
                </c-v-row>
              </div>
            </template>
          </div>
        </c-v-col>
      </c-v-row>
    </c-v-col>
  </c-v-row>
  <div
    v-if="props.onewayModelValue.copyFlg === true"
    class="overlay"
    @wheel="handleWheel"
  ></div>
  <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Other"
    :oneway-model-value="localOneway.or51775OnewayTypeOther"
    @confirm="handleOr51775Confirm"
  ></g-custom-or-51775>
</template>

<style lang="scss" scoped>
@use '@/styles/base-data-table-list.scss';

:deep(.ml-4) {
  margin-left: 0px !important;
}
.select-row {
  background: rgb(var(--v-theme-blue-100));
}
.container {
  overflow-y: auto;
  max-height: 600px;

  :has(.contentTitle) {
    font-size: 18px !important;
  }

  :has(.contentItem) {
    :deep(.item-label) {
      color: #800000;
    }
  }

  .item-col-position {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .title {
    background: #ffffff;
    padding-left: 8px;
  }
}

.container {
  height: 142px;
  overflow-y: auto;
}

.container2 {
  height: 142px;
  overflow-y: auto;
}

.container3 {
  overflow-y: auto;
  height: 550px;
  padding-bottom: 4px;
}

.container4 {
  height: 350px;
  overflow-y: auto;
}

.container5 {
  height: 350px;
  overflow-y: auto;
}

:deep(.v-selection-control-group--inline) {
  flex-direction: column !important;
}

:deep(.v-selection-control--density-default) {
  --v-selection-control-size: 36px !important;
}

.buttonActive {
  background: #dbeefe;
  color: white;
  border-color: black;

  :deep(.v-btn__content > span) {
    color: rgb(var(--v-theme-black-500)) !important;
  }
}

.radio1 {
  display: flex;
  :deep(.v-selection-control-group > div) {
    margin-bottom: -6px;
    margin-top: -8px;
  }
}
.title-back {
  background-color: unset;
}
.title-weight {
  font-weight: bold;
}
.contentTitle {
  margin-bottom: 18px !important;
}

.top-unset {
  margin-top: unset;
  padding-top: unset;
}
.back-unset {
  background: transparent;
}
.title-no {
  cursor: default;
  width: 52px;
  position: sticky;
  top: 0;
  z-index: 2;
  text-align: center !important;
  font-weight: 400 !important;
  height: 50px !important;
}
.title-info-item {
  cursor: default;
  position: sticky;
  top: 0;
  z-index: 2;
  text-align: center !important;
  font-weight: 400 !important;
}
.title-situation {
  cursor: default;
  width: 466px;
  position: sticky;
  top: 0;
  z-index: 2;
  text-align: center !important;
  font-weight: 400 !important;
}
.title-consider {
  cursor: default;
  width: 63px;
  position: sticky;
  top: 0;
  z-index: 2;
  text-align: center !important;
  font-weight: 400 !important;
}
.padding-zero {
  padding: 0;
}
.cursor-def {
  cursor: default;
}
.content-no {
  text-align: center;
  width: 54px;
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.width-464 {
  width: 464px;
}
.width-62 {
  width: 63px;
}
.disp-flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.content-lev3knj {
  text-align: center;
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.width-760 {
  width: 760px;
}
.width-84 {
  width: 84.5%;
}

.row-tl-border {
  border-top: 1px rgb(var(--v-theme-black-200)) solid;
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

.row-l-border {
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-br-border {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-pl {
  padding-left: 4px !important;
}

.tbl-title-bg {
  background-color: rgb(var(--v-theme-black-100));
}

.top-button {
  padding-bottom: 8px;
  border-bottom: 1px solid rgb(224, 224, 224);
}

.font-bold {
  font-weight: bold;
}

.align-items-center {
  display: flex;
  align-items: center;
}

.radio-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.or32418-main-div {
  overflow-y: auto;
  max-height: 540px;
  scrollbar-gutter: stable;
  position: relative;
  z-index: 999;
}

.or32418-row {
  height: 68px;
}

.or32418-title-row {
  height: 21px;
}

:deep(.col1) {
  width: 54px;
  flex-shrink: 0;
}

:deep(.col2) {
  flex-grow: 2;
}

:deep(.col3) {
  flex-grow: 3;
}

:deep(.col4) {
  width: 64px;
  flex-shrink: 0;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 1000;
  pointer-events: auto;
  cursor: not-allowed;
}
.text-bg {
  background-color: rgb(242, 242, 242);
}
.full-width-field {
  padding: 0 3px !important;
}
</style>
