<script setup lang="ts">
/**
 * Or10029:履歴選択］画面 評価表
 * GUI01239_［履歴選択］画面 評価表
 *
 * @description
 * GUI01239_［履歴選択］画面 評価表
 *
 * <AUTHOR>
 */

import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  watch,
} from '#imports'
import { Or10029Const } from '~/components/custom-components/organisms/Or10029/Or10029.constants'
import { Or10029Logic } from '~/components/custom-components/organisms/Or10029/Or10029.logic'
import type { EvaluationTableDataItem } from '~/components/custom-components/organisms/Or10029/Or10029.type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * 画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01239'
// ルーティング
const routing = 'GUI01239/pinia'
// 画面物理名
const screenName = 'GUI01239'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

// 有機体or10029ユーニックID
const or10029 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01239' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  // Or10029Logic.initialize(or10029.value.uniqueCpId)
}

// 子コンポーネントのユニークIDを設定する
or10029.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01239',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or10029Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or10029Const.CP_ID(1)]: or10029.value,
})

/**************************************************
 * Props
 **************************************************/

// ダイアログ表示フラグ
const showDialogOr10029 = computed(() => {
  // Or10029のダイアログ開閉状態
  return Or10029Logic.state.get(or10029.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or10029Data = {
  //計画期間ID
  sc1Id: '1',
  // 事業者ID
  svJigyoId: '1',
  // 利用者ID
  userId: '1',
  // 履歴ID
  cmoni1Id: '1',
}

const or10029Init = ref<EvaluationTableDataItem>({
  cmoni1Id: '',
})
watch(or10029Init, () => {
  console.log(or10029Init.value)
})

/**
 *  ボタン押下時の処理
 *
 * 期間管理フラグ
 */
function or10029OnClick() {
  // Or10029のダイアログ開閉状態を更新する
  Or10029Logic.state.set({
    uniqueCpId: or10029.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters><c-v-col>GUI01239_［履歴選択］画面 評価表確認用ページ</c-v-col></c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >GUI01239_［履歴選択］画面 評価表確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>

  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or10029OnClick()"
        >GUI01239_［履歴選択］画面 評価表
      </v-btn>
      <g-custom-or10029
        v-if="showDialogOr10029"
        v-bind="or10029"
        v-model="or10029Init"
        :oneway-model-value="or10029Data"
      />
    </c-v-col>
  </c-v-row>
</template>
