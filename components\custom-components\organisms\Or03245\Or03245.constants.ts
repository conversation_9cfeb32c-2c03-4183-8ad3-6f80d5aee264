import { getSequencedCpId } from '#imports'
/**
 * GUI00834_［アセスメント（包括）］洗面画面
 *
 * @description
 *
 * ［アセスメント（包括）］洗面画面
 *
 * 画面ID_ GUI00834
 *
 * <AUTHOR>
 */

export namespace Or03245Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or03245', seq)

  export namespace DEFAULT {
    /**
     * 画面ID
     */
    export const TAB_ID = '4'

    /**
     * 画面ID
     */
    export const TAB_SUB_ID = 34

    /**
     * インプット表示フラグ
     */
    export const INPUT_DISPLAY_FLG_LAST = '1'

    /**
     * インプット表示フラグ
     */
    export const INPUT_DISPLAY_FLG_UNIT = '2'

    /**
     * API返却値チェック：1
     */
    export const API_RESULT_CHECKON = '1'

    /**
     * 画面表示モード：複写
     */
    export const SCREEN_DIAPLAY_MODE_COPY = 'copy'

    /**
     * テーブル初期化データリストタイプ：ケアの内容
     */
    export const TABLE_INIT_DATA_LIST_TYPE_CARE_CONTENT = 3

    /**
     * テーブル初期化データリストタイプ：ケア提供場所「入力欄抜く」の場合
     */
    export const TABLE_INIT_DATA_LIST_TYPE_CARE_LOCATION_NO_INPUT = 1

    /**
     * テーブル初期化データリストタイプ：ケア提供場所
     */
    export const TABLE_INIT_DATA_LIST_TYPE_CARE_LOCATION = 2

    /**
     * 各データデフォルト値
     */
    export const DATA_DEFAULT = '0'

    /**
     * メニュー3名称
     */
    export const MENU3_NAME = '[mnu3][3GK][包括]ｱｾｽﾒﾝﾄ'

    /**
     * システム略称
     */
    export const SYSTEM_ACRONYM = '3GK'

    /**
     * セクション名称
     */
    export const SECTION_NAME = 'ケアチェック表'
  }
}
