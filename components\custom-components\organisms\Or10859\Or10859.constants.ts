import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or10859:有機体:モーダル（表示順変更アセスメントモーダル）
 * 静的データ
 */
export namespace Or10859Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or10859', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
  }
}
