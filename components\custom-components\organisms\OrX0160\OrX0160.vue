<script setup lang="ts">
/**
 * OrX0160:有機体:入力補助付き期間入力
 *
 * @description
 * 入力補助付きセレクト
 *
 * <AUTHOR>
 */
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or25957Const } from '../Or25957/Or25957.constants'
import { Or25957Logic } from '../Or25957/Or25957.logic'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { useSetupChildProps } from '#imports'
import type { OrX0160OnewayType, OrX0160Type } from '~/types/cmn/business/components/OrX0160Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue?: OrX0160OnewayType
  modelValue: OrX0160Type
  uniqueCpId: string
}
const props = defineProps<Props>()

const defaultOnewayModelValue: OrX0160OnewayType = {
  itemLabel: '',
  isRequired: false,
  isVerticalLabel: true,
  showItemLabel: false,
  customClass: new CustomClass({ outerClass: 'mr-2', labelClass: 'ma-2' }),
  width: '',
  hint: '',
  hintTooltipText: '',
  itemLabelFontWeight: 'normal',
  hideDetails: 'auto',
  showEditBtnFlg: true,
} as OrX0160OnewayType

const defaultModelValue: OrX0160Type = {
  startKikan: '',
  endKikan: '',
  mo01343: {
    value: '',
    endValue: '',
  } as Mo01343Type,
} as OrX0160Type
/**************************************************
 * 変数定義
 **************************************************/
const localOneway = reactive({
  orX0160Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
    customClass: {
      ...defaultOnewayModelValue.customClass,
      ...props.onewayModelValue?.customClass,
    } as CustomClass,
  } as OrX0160OnewayType,
  mo01338Oneway: {
    value: t('label.wave-dash'),
  } as Mo01338OnewayType,
})

const local = reactive({
  orX0160: {
    ...defaultModelValue,
    ...props.modelValue,
  } as OrX0160Type,
})
// 期間開始日付
const or25957Component0 = ref({ uniqueCpId: Or25957Const.CP_ID(0) })
// 期間終了日付
const or25957Component1 = ref({ uniqueCpId: Or25957Const.CP_ID(1) })
const emit = defineEmits(['update:modelValue', 'onClickEditBtn'])
// 内部更新による変更かをマークするフラグ
const isInternalUpdate = ref(false)
/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or25957Const.CP_ID(0)]: or25957Component0.value,
  [Or25957Const.CP_ID(1)]: or25957Component1.value,
})

/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.orX0160Oneway = {
      ...defaultOnewayModelValue,
      ...newValue,
      customClass: {
        ...defaultOnewayModelValue.customClass,
        ...newValue?.customClass,
      } as CustomClass,
    }
  },
  { deep: true }
)
watch(
  () => props.modelValue,
  (newValue) => {
    // 内部更新によるトリガーの場合は処理をスキップ
    if (isInternalUpdate.value) {
      isInternalUpdate.value = false
      return
    }
    // 実際に値が変更された場合のみ更新処理を実行
    if (JSON.stringify(newValue) !== JSON.stringify(local.orX0160)) {
      local.orX0160 = {
        ...defaultModelValue,
        ...newValue,
      }
      updateOr25957()
    }
  },
  { deep: true, immediate: true }
)
/**
 * 期間開始日付変更イベント
 */
watch(
  () => Or25957Logic.data.get(or25957Component0.value.uniqueCpId),
  (newVal) => {
    const newStart = newVal?.value ?? ''
    if (local.orX0160.startKikan !== newStart) {
      isInternalUpdate.value = true
      local.orX0160.startKikan = newStart
      emit('update:modelValue', { ...local.orX0160 })
    }
  }
)
/**
 * 期間終了日付変更イベント
 */
watch(
  () => Or25957Logic.data.get(or25957Component1.value.uniqueCpId),
  (newVal) => {
    const newEnd = newVal?.value ?? ''
    if (local.orX0160.endKikan !== newEnd) {
      isInternalUpdate.value = true
      local.orX0160.endKikan = newEnd
      emit('update:modelValue', { ...local.orX0160 })
    }
  }
)
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  updateOr25957()
})

/**
 * Or25957Logicデータを更新する共通メソッド
 */
function updateOr25957() {
  Or25957Logic.data.set({
    uniqueCpId: or25957Component0.value.uniqueCpId,
    value: {
      value: local.orX0160.startKikan,
    },
  })
  Or25957Logic.data.set({
    uniqueCpId: or25957Component1.value.uniqueCpId,
    value: {
      value: local.orX0160.endKikan,
    },
  })
}
</script>
<template>
  <c-v-sheet
    :class="localOneway.orX0160Oneway.customClass?.outerClass"
    :style="localOneway.orX0160Oneway.customClass?.outerStyle"
  >
    <!-- 縦型ラベル -->
    <c-v-row
      v-if="localOneway.orX0160Oneway.isVerticalLabel && localOneway.orX0160Oneway.showItemLabel"
      no-gutters
      :class="localOneway.orX0160Oneway.customClass?.labelClass"
      :style="localOneway.orX0160Oneway.customClass?.labelStyle"
    >
      <!-- 項目名あり -->
      <c-v-col
        v-if="localOneway.orX0160Oneway.itemLabel"
        class="align-self-center"
        cols="auto"
      >
        <base-at-label
          class="item-label"
          :value="localOneway.orX0160Oneway.itemLabel"
          :font-weight="localOneway.orX0160Oneway.itemLabelFontWeight"
        />
      </c-v-col>
      <!-- 項目名なし -->
      <c-v-col
        v-else
        cols="auto"
      >
        <base-at-label
          class="item-label"
          value="&emsp;"
        />
      </c-v-col>
      <!-- 必須バッチ -->
      <c-v-col
        v-if="localOneway.orX0160Oneway.isRequired"
        class="align-self-center"
        cols="auto"
      >
        <base-mo00037 class="pl-1" />
      </c-v-col>
      <!-- ヒント -->
      <c-v-col
        v-if="localOneway.orX0160Oneway.hintTooltipText !== ''"
        class="align-self-center"
        cols="auto"
      >
        <c-v-tooltip :text="localOneway.orX0160Oneway.hintTooltipText">
          <template #activator="{ props: activatorProps }">
            <base-at-icon
              v-bind="activatorProps"
              icon="help fill"
              color="subText"
              size="18"
            />
          </template>
        </c-v-tooltip>
      </c-v-col>
      <slot name="content" />
    </c-v-row>
    <c-v-row
      no-gutters
      :class="localOneway.orX0160Oneway.customClass?.itemClass"
      :style="localOneway.orX0160Oneway.customClass?.itemStyle"
    >
      <!-- 横型ラベル:項目名あり -->
      <c-v-col
        v-if="
          !localOneway.orX0160Oneway.isVerticalLabel &&
          localOneway.orX0160Oneway.showItemLabel &&
          localOneway.orX0160Oneway.itemLabel
        "
        class="pt-1"
        :class="localOneway.orX0160Oneway.customClass?.labelClass"
        :style="localOneway.orX0160Oneway.customClass?.labelStyle"
        cols="auto"
      >
        <base-at-label
          class="label-center"
          :value="localOneway.orX0160Oneway.itemLabel"
        />
      </c-v-col>
      <!-- 横型ラベル:項目名なし -->
      <c-v-col
        v-else-if="
          !localOneway.orX0160Oneway.isVerticalLabel &&
          localOneway.orX0160Oneway.showItemLabel &&
          !localOneway.orX0160Oneway.itemLabel
        "
        cols="auto"
      >
        <base-at-label
          class="item-label"
          value="&emsp;"
        />
      </c-v-col>
      <!-- 横型:必須バッチ -->
      <c-v-col
        v-if="
          !localOneway.orX0160Oneway.isVerticalLabel &&
          localOneway.orX0160Oneway.showItemLabel &&
          localOneway.orX0160Oneway.isRequired
        "
        class="pt-2"
        cols="auto"
      >
        <base-mo00037 class="pl-1" />
      </c-v-col>
      <!-- 横型:ヒント -->
      <c-v-col
        v-if="
          !localOneway.orX0160Oneway.isVerticalLabel &&
          localOneway.orX0160Oneway.showItemLabel &&
          localOneway.orX0160Oneway.hintTooltipText !== ''
        "
        class="pt-2"
        cols="auto"
      >
        <c-v-tooltip :text="localOneway.orX0160Oneway.hintTooltipText">
          <template #activator="{ props: activatorProps }">
            <base-at-icon
              v-bind="activatorProps"
              icon="help fill"
              color="subText"
              size="18"
            />
          </template>
        </c-v-tooltip>
      </c-v-col>
      <c-v-col>
        <!-- 期間選択ルートコンテナ -->
        <div class="d-flex kikan-range-container">
          <div class="d-flex ga-2">
            <!-- 期間開始日付 -->
            <div class="d-flex kikan-start-container">
              <!-- 入力補助ボタン -->
              <div class="input-support">
                <g-base-or-12002
                  v-if="localOneway.orX0160Oneway.showEditBtnFlg"
                  :class="['icon-edit-btn', localOneway.orX0160Oneway.editBtnClass]"
                  :disabled="localOneway.orX0160Oneway.disabled"
                  @click="$emit('onClickEditBtn', $event)"
                >
                </g-base-or-12002>
              </div>
              <g-custom-or-25957
                v-bind="or25957Component0"
                :disabled="localOneway.orX0160Oneway.disabled"
                :class="[
                  'kikan-start-input',
                  localOneway.orX0160Oneway.startKikanClass,
                  { 'kikan-start-input-full-radius': !localOneway.orX0160Oneway.showEditBtnFlg },
                ]"
              ></g-custom-or-25957>
            </div>
            <!-- ~ -->
            <div>
              <base-mo01338
                :oneway-model-value="localOneway.mo01338Oneway"
                class="wave-dash"
              ></base-mo01338>
            </div>
            <!-- 期間終了日付 -->
            <div class="kikan-end-container">
              <g-custom-or-25957
                v-bind="or25957Component1"
                :disabled="localOneway.orX0160Oneway.disabled"
                :class="['kikan-start-input', localOneway.orX0160Oneway.endKikanClass]"
                class="kikan-end-input"
              ></g-custom-or-25957>
            </div>
          </div>
        </div>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
</template>
<style scoped lang="scss">
$border-radius: 4px;
$input-support-border-radius: 3px;
$icon-edit-btn-width: 36px;
$border-color: #cdcdcd;
// 期間選択ルートコンテナ
.kikan-range-container {
  background: #fff;
  // 期間開始日付、期間終了日付スタイル
  .kikan-start-container,
  .kikan-end-container {
    border: 1px solid #c9cdd2;
    border-radius: $border-radius;
  }
  .kikan-start-container {
    .input-support {
      height: 100%;
      // 入力補助ボタンのスタイル
      .icon-edit-btn {
        background: rgb(var(--v-theme-black-100));
        color: #869fca;
        width: $icon-edit-btn-width;
        height: 100%;
        border-top-left-radius: $input-support-border-radius !important;
        border-end-start-radius: $input-support-border-radius !important;
        border-top-right-radius: 0px !important;
        border-end-end-radius: 0px !important;
        border-right: 1px solid $border-color;
      }
    }

    // 入力補助ボタンが存在する場合の左側境界線の角丸除去
    :deep(.v-field) {
      border-top-left-radius: 0px !important;
      border-end-start-radius: 0px !important;
    }

    :deep(.kikan-start-input) {
      border-top-right-radius: $input-support-border-radius !important;
      border-end-end-radius: $input-support-border-radius !important;
    }
    // オリジナルコンポーネントのフォーカス状態時のボーダーシャドウを除去
    :deep(.v-field--focused) {
      box-shadow: none !important;
    }
    // 入力補助ボタンがない場合の左側の角丸
    .kikan-start-input-full-radius {
      border-top-left-radius: $border-radius !important;
      border-end-start-radius: $border-radius !important;
    }
  }
  // ルートコンテナ配下のオリジナルコンポーネントがフォーカス状態時、選択効果をルートコンテナに転移
  .kikan-start-container:has(.v-field--focused),
  .kikan-end-container:has(.v-field--focused) {
    box-shadow: 0 0 5px rgb(var(--v-theme-key)) !important;
    color: rgb(var(--v-theme-key));
    border-color: rgb(var(--v-theme-key));
  }
  .kikan-end-container {
    // 期間終了日付
    .kikan-end-input {
      border-radius: $border-radius !important;
    }
  }
}
.kikan-end-container .kikan-end-input {
  height: 100% !important;
}
.wave-dash {
  margin-right: 0px !important;
  background: transparent;
}
:deep(.wave-dash > .align-center) {
  margin-left: 0px !important;
}
// オリジナルコンポーネントの外枠（ボーダー）を除去
:deep(.v-field__outline__start) {
  border: none !important;
}
:deep(.v-field__outline__end) {
  border: none !important;
}
</style>
