<script setup lang="ts">
/**
 * Or35041:有機体:印刷設定モーダル
 * GUI01215_［印刷設定］画面
 *
 * @description
 * ［印刷設定］画面
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import type { Or35041StateType, Or35041TwoWayData, simList } from './Or35041.type'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenStore,
  useScreenTwoWayBind,
} from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or35041OnewayType, Or35041Type } from '~/types/cmn/business/components/Or35041Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or35041Const } from '~/components/custom-components/organisms/Or35041/Or35041.constants'

import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Headers,
} from '~/types/business/components/Mo01334Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  PrintSettingsScreenInitialInfoSelectGUI01215OutEntity,
  PrintSettingsScreenInitialInfoSelectGUI01215InEntity,
  choPrtList,
} from '~/repositories/cmn/entities/printSettingsScreenInitialInfoSelectGUI01215'
import type {
  LedgerInitializeDataComSelectOutEntity,
  LedgerInitializeDataComSelectInEntity,
} from '~/repositories/cmn/entities/ledgerInitializeDataComSelect'
import type {
  PrintSettingsSimulationInfoSelectOutEntity,
  PrintSettingsSimulationInfoSelectInEntity,
} from '~/repositories/cmn/entities/printSettingsSimulationInfoSelect'
import type { PrintSettingsInfoComUpdateInEntity } from '~/repositories/cmn/entities/printSettingsInfoComUpdate'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useReportUtils, reportOutputType } from '~/utils/useReportUtils'
import type { Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import { Or26315Logic } from '~/components/custom-components/organisms/Or26315/Or26315.logic'
import { Or26315Const } from '~/components/custom-components/organisms/Or26315/Or26315.constants'
import type { Or26315OneWayType } from '~/types/cmn/business/components/Or26315Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { SimulationRiteiReportEntity } from '~/repositories/cmn/entities/SimulationRiteiReportEntity'

const { t } = useI18n()

/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or35041Type
  onewayModelValue: Or35041OnewayType
  uniqueCpId: string
  parentCpId: string
}
const props = defineProps<Props>()

// 引継情報を取得する

const or21814 = ref({ uniqueCpId: '' })
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
const { reportOutput } = useReportUtils()

const or26315 = ref({ uniqueCpId: '' })

// プロファイル
const choPro = ref('')

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or26315Const.CP_ID(0)]: or26315.value,
})

// 帳票イニシャライズデータを取得する
const reportInitData = ref([
  {
    // 氏名伏字印刷
    prtName: '',
    // 文書番号印刷
    prtBng: '',
    // 個人情報印刷
    prtKojin: '',
  },
])

// ダイアログ表示フラグ
const showDialogOr26315 = computed(() => {
  // Or26315のダイアログ開閉状態
  return Or26315Logic.state.get(or26315.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * 変数定義
 **************************************************/
const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(props.uniqueCpId)
})

/**
 * 要介護度リスト
 */
let yokaiList: {
  /**
   * 要介護度
   */
  yokaiKnj: string
  /**
   * 要介護状態区分
   */
  yokaiKbn: string
  /**
   * 表示順
   */
  sort: string
  /**
   * 作成日
   */
  createYmd: string
}[]

// ローカル双方向bind
const local = reactive({
  // シミュレーション雛形一覧情報
  mo01334: {
    value: props.onewayModelValue.shienId,
    values: [],
  },
  or35041: {
    ...props.modelValue,
  } as Or35041Type,
  mo00040: { modelValue: '' } as Mo00040Type,
  /**
   * 利用者ID
   */
  userId: '',
  /**
   * アセスメントID
   */
  assessmentId: '',
  textInput: {
    value: '',
  } as Mo00045Type,
  mo00039Type: '',
  mo00020Type: {
    value: '',
  } as Mo00020Type,
  mo00020TypeKijunbi: {
    value: '',
  } as Mo00020Type,
  titleInput: {
    value: '',
  } as Mo00045Type,
  /** 帳票ID */
  reportId: '',
})

const localOneway = reactive({
  mo01334Oneway: {
    /** 初期値：ヘッダー情報 */
    height: '455px',
    headers: [
      // 雛形選択
      {
        title: '', // ヘッダーに表示される名称
        key: 'dmySel',
        sortable: false,
        width: '70px',
      },
      // 要介護度
      {
        title: t('label.yokai-knj'), // ヘッダーに表示される名称
        key: 'yokaiKbn',
        sortable: true,
      },
      // タイトル
      {
        title: t('label.title'), // ヘッダーに表示される名称
        key: 'simTitleKnj',
        sortable: true,
      },
    ] as Mo01334Headers[],
    showPaginationBottomFlg: false,
    showPaginationTopFlg: false,
    items: [],
  } as Mo01334OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo00020OneWay: {
    showItemLabel: true,
    width: '132px',
  } as Mo00020OnewayType,
  Or35041: {
    ...props.onewayModelValue,
  },
  mo01338OneWayTitle: {
    value: t('label.print-settings-title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayDateTitle: {
    value: t('label.label-for-date'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OnewayTitleInput: {
    value: '',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00009OnewayType: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  // 「印刷設定」ダイアログ
  mo00024Oneway: {
    width: 'auto',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.print-set'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  },
  // チェックボックス
  mo00018OnewayType: {
    showItemLabel: false,
    checkboxLabel: '',
    isVerticalLabel: true,
    hideDetails: true,
    outerDivClass: '',
  } as Mo00018OnewayType,
  mo00611OneWayCopy: {
    btnLabel: t('btn.option'),
    disabled: false,
  } as Mo00611OnewayType,
})

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or35041Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/

const { setState } = useScreenOneWayBind<Or35041StateType>({
  cpId: Or35041Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or35041Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or26315Data: Or26315OneWayType = {
  //システムコード
  sysCd: systemCommonsStore.getSystemCode ?? '',
  //職員ID
  shokuId: systemCommonsStore.getStaffId ?? '',
  //法人ID
  houjinId: systemCommonsStore.getHoujinId ?? '',
  //施設ID
  shisetuId: systemCommonsStore.getShisetuId ?? '',
  //事業所ID
  svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
  //印刷日
  printDate: '',
  //選択No
  selectNo: '1',
  //事業者コード
  svJigyoshaCd: '',
}

/**
 * 出力帳票名一覧
 */
const mo01334OnewayReport = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'defPrtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})
/**
 * 出力帳票名一覧
 */
const mo01334TypeReport = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 変更データを取得する
 *
 * @returns 出力帳票印刷情報リスト
 */
async function getDataTable() {
  const choPrtList = mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => {
    if (id === mo01334TypeReport.value.value) {
      return {
        ...rest,
        prtTitle: localOneway.mo01338OnewayTitleInput.value,
        prndate: local.mo00039Type,
      }
    }
    return {
      ...rest,
    }
  }) as choPrtList[]

  refValue.value = { choPrtList: choPrtList }

  await nextTick()

  return choPrtList
}

/**
 * 確認ダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21814MsgOneBtn() {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11426'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
const { refValue } = useScreenTwoWayBind<Or35041TwoWayData>({
  cpId: Or35041Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
onMounted(async () => {
  //システム日付
  local.mo00020Type.value = systemCommonsStore.getSystemDate!
  //提供年月
  local.mo00020TypeKijunbi.value = localOneway.Or35041.teiYm
  await getPrintSettingList()
  await initCodes()
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [{ mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY }]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  //日付印刷区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
}

/**
 * 印刷設定画面初期情報を取得する
 */
async function getPrintSettingList() {
  const inputData: PrintSettingsScreenInitialInfoSelectGUI01215InEntity = {
    sysCd: localOneway.Or35041.sysCd,
    sysRyaku: Or35041Const.DEFAULT.SYS_RYAKU,
    kinounameKnj: Or35041Const.DEFAULT.KINOU_NAMEKNJ,
    houjinId: localOneway.Or35041.houjinId,
    shisetuId: localOneway.Or35041.shisetuId,
    svJigyoId: localOneway.Or35041.svJigyoId,
    shokuId: localOneway.Or35041.shokuId,
    sectionName: localOneway.Or35041.sectionName,
    choIndex: localOneway.Or35041.choIndex,
    shienId: localOneway.Or35041.shienId,
    teiYm: localOneway.Or35041.teiYm,
  }

  // バックエンドAPIから初期情報取得
  const ret: PrintSettingsScreenInitialInfoSelectGUI01215OutEntity = await ScreenRepository.select(
    'printSettingsScreenInitialInfoSelectGUI01215',
    inputData
  )

  yokaiList = ret.data.yokaiList

  const mo01334OnewayList: Mo01334Items[] = []
  for (const item of ret.data.choPrtList) {
    if (item) {
      mo01334OnewayList.push({
        id: item.prtNo,
        mo01337OnewayReport: {
          value: item.prtTitle,
          unit: '',
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
    }
  }
  mo01334OnewayReport.value.items = mo01334OnewayList

  if (parseInt(localOneway.Or35041.choIndex) > 1 && ret.data.choPrtList.length > 0) {
    mo01334TypeReport.value.value = ret.data.choPrtList[0].prtNo
  } else {
    mo01334TypeReport.value.value = String(ret.data.choPrtList.length)
  }

  refValue.value = { choPrtList: ret.data.choPrtList }

  useScreenStore().setCpTwoWay({
    cpId: Or35041Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })

  //シミュレーション雛形一覧
  if (ret.data.simList.length > 0) {
    for (const item of ret.data.simList) {
      let yokaiKnj = ''
      for (const e of yokaiList) {
        if (parseInt(localOneway.Or35041.userId) > 0) {
          if (item.yokaiKbn === e.yokaiKbn) {
            yokaiKnj = e.yokaiKnj
            const tmpItem1 = {
              id: item.shienId,
              dmySel:
                item.userid === localOneway.Or35041.userId
                  ? { modelValue: true }
                  : { modelValue: false },
              userid: item.userid,
              yokaiKbn: yokaiKnj,
              simTitleKnj: item.simTitleKnj,
              shienId: item.shienId,
              yymmYm: item.yymmYm,
              yymmD: item.yymmD,
              createYmd: item.createYmd,
            }
            localOneway.mo01334Oneway.items.push(tmpItem1)
          }
        } else {
          if (item.yokaiKbn === e.yokaiKbn) {
            yokaiKnj = e.yokaiKnj
            const tmpItem1 = {
              id: item.shienId,
              dmySel:
                item.dmySel === Or35041Const.DEFAULT.ONE
                  ? { modelValue: true }
                  : { modelValue: false },
              userid: item.userid,
              yokaiKbn: yokaiKnj,
              simTitleKnj: item.simTitleKnj,
              shienId: item.shienId,
              yymmYm: item.yymmYm,
              yymmD: item.yymmD,
              createYmd: item.createYmd,
            }
            localOneway.mo01334Oneway.items.push(tmpItem1)
          }
        }
      }
    }
  }
  local.mo01334.value = Or35041Const.DEFAULT.ONE
  reportInitData.value = ret.data.iniDataObject
}

/**
 * 帳票イニシャライズデータを取得する。
 */
async function getReportInfoDataList() {
  const inputData: LedgerInitializeDataComSelectInEntity = {
    sysCd: localOneway.Or35041.sysCd,
    kinounameKnj: Or35041Const.DEFAULT.KINOU_NAMEKNJ,
    shokuId: localOneway.Or35041.shokuId,
    sectionKnj: choPro.value,
    kojinhogoFlg: Or35041Const.DEFAULT.ZERO,
    sectionAddNo: Or35041Const.DEFAULT.ZERO,
  }
  // バックエンドAPIから初期情報取得
  const ret: LedgerInitializeDataComSelectOutEntity = await ScreenRepository.select(
    'ledgerInitializeDataComSelect',
    inputData
  )
  reportInitData.value = ret.data.iniDataObject
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await close()
    }
  }
)

/**
 * 帳票選択切替
 */
watch(
  () => mo01334TypeReport.value.value,
  async (newValue, oldValue) => {
    if (oldValue) {
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (oldValue === item.id) {
            // 画面.出力帳票一覧明細に切替前選択される行.帳票タイトル = 画面.帳票タイトル
            if (localOneway.mo01338OnewayTitleInput.value)
              item.prtTitle = localOneway.mo01338OnewayTitleInput.value
            // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
            item.prndate = local.mo00039Type
          }
        }
      }
    }
    for (const item of mo01334OnewayReport.value.items) {
      if (item) {
        if (newValue === item.id) {
          // 帳票ID設定
          if (mo01334TypeReport.value.value === '1') {
            local.reportId = 'riyohyoBetsuhyoReport'
          }
          if (mo01334TypeReport.value.value === '2') {
            local.reportId = 'simulationRiteiReport'
          }
          if (mo01334TypeReport.value.value === '3') {
            local.reportId = 'useAnnexedTable251Report'
          }
          // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
          localOneway.mo01338OnewayTitleInput.value = item?.prtTitle as string
          // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
          local.mo00039Type = item?.prndate as string

          choPro.value = item?.choPro as string

          await getReportInfoDataList()
        }
      }
    }
  }
)

/**
 *  提供年月-前へアイコンクリック 提供年月選択アイコンクリック 提供年月-次へアイコンクリック
 */
watch(
  () => local.mo00020TypeKijunbi.value,
  async (newValue) => {
    if (newValue) {
      const inputData: PrintSettingsSimulationInfoSelectInEntity = {
        shienId: localOneway.Or35041.shienId,
        teiYm: local.mo00020TypeKijunbi.value,
      }

      // バックエンドAPIから初期情報取得
      const ret: PrintSettingsSimulationInfoSelectOutEntity = await ScreenRepository.select(
        'printSettingsSimulationInfoSelect',
        inputData
      )

      localOneway.mo01334Oneway.items = []

      //シミュレーション雛形一覧
      if (ret.data.simList.length > 0) {
        for (const item of ret.data.simList) {
          let yokaiKnj = ''
          for (const e of yokaiList) {
            if (parseInt(localOneway.Or35041.userId) > 0) {
              if (item.yokaiKbn === e.yokaiKbn) {
                yokaiKnj = e.yokaiKnj
                const tmpItem1 = {
                  id: item.shienId,
                  dmySel:
                    item.userid === localOneway.Or35041.userId
                      ? { modelValue: true }
                      : { modelValue: false },
                  userid: item.userid,
                  yokaiKbn: yokaiKnj,
                  simTitleKnj: item.simTitleKnj,
                  shienId: item.shienId,
                  yymmYm: item.yymmYm,
                  yymmD: item.yymmD,
                  createYmd: item.createYmd,
                }
                localOneway.mo01334Oneway.items.push(tmpItem1)
              }
            } else {
              if (item.yokaiKbn === e.yokaiKbn) {
                yokaiKnj = e.yokaiKnj
                const tmpItem1 = {
                  id: item.shienId,
                  dmySel:
                    item.dmySel === Or35041Const.DEFAULT.ONE
                      ? { modelValue: true }
                      : { modelValue: false },
                  userid: item.userid,
                  yokaiKbn: yokaiKnj,
                  simTitleKnj: item.simTitleKnj,
                  shienId: item.shienId,
                  yymmYm: item.yymmYm,
                  yymmD: item.yymmD,
                  createYmd: item.createYmd,
                }
                localOneway.mo01334Oneway.items.push(tmpItem1)
              }
            }
          }
        }
      }
    }
  }
)
/**
 * 「閉じるボタン」押下
 */
async function close() {
  const choPrtList = await getDataTable()
  if (isEdit.value) await savePrintSettingInfo(choPrtList)
  setState({ isOpen: false })
}

/**
 * 「オプション」ボタン押下
 */
function open() {
  or26315Data.printDate = local.mo00020Type.value
  // Or26315のダイアログ開閉状態を更新する
  Or26315Logic.state.set({
    uniqueCpId: or26315.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「印刷」ボタン押下
 */
async function print() {
  //画面.シミュレーション雛形一覧に雛形選択の選択が無い場合
  let flg = false
  for (const item of localOneway.mo01334Oneway.items) {
    const newValue = item as simList
    if (newValue.dmySel.modelValue) {
      flg = true
      break
    }
  }
  if (!flg) {
    await showOr21814MsgOneBtn()
    return
  }

  //印刷設定情報保存
  const choPrtList = await getDataTable()

  void downloadPdf()

  if (isEdit.value) await savePrintSettingInfo(choPrtList)
  setState({ isOpen: false })
}

/**
 * pdf download
 */
const downloadPdf = async () => {
  for (const item of localOneway.mo01334Oneway.items) {
    const newValue = item as unknown as simList
    if (newValue.dmySel.modelValue) {
      const inputData: SimulationRiteiReportEntity = {
        printSet: {
          shiTeiKubun: local.mo00039Type,
          shiTeiDate: local.mo00020Type.value,
        },
        sim: {
          dmySel: String(newValue.dmySel.modelValue),
          userid: newValue.userid,
          yokaiKbn: newValue.yokaiKbn,
          simTitleKnj: newValue.simTitleKnj,
          shienId: newValue.shienId,
          yymmYm: newValue.yymmYm,
          yymmD: newValue.yymmD,
          createYmd: newValue.createYmd,
        },
        useridList: {
          userid: '0',
          yymmd: newValue.yymmD,
          shienid: newValue.shienId,
        },
        yyyyMm: localOneway.Or35041.teiYm,
        syscd: localOneway.Or35041.sysCd,
        shokuinId: localOneway.Or35041.shokuId,
        svJigyoId: localOneway.Or35041.svJigyoId,
        appYmd: systemCommonsStore.getSystemDate!,
        printOption: '',
      } as SimulationRiteiReportEntity

      // 帳票出力
      await reportOutput(local.reportId, inputData, reportOutputType.DOWNLOAD)
    }
  }
}

/**
 * 印刷設定情報を保存する
 *
 * @param choPrtList - 出力帳票印刷情報リスト
 */
async function savePrintSettingInfo(choPrtList: choPrtList[]) {
  const inputData: PrintSettingsInfoComUpdateInEntity = {
    sysCd: localOneway.Or35041.sysCd,
    sysRyaku: Or35041Const.DEFAULT.SYS_RYAKU,
    kinounameKnj: Or35041Const.DEFAULT.KINOU_NAMEKNJ,
    houjinId: localOneway.Or35041.houjinId,
    shisetuId: localOneway.Or35041.shisetuId,
    svJigyoId: localOneway.Or35041.svJigyoId,
    shokuId: localOneway.Or35041.shokuId,
    choPro: choPro.value,
    kojinhogoFlg: Or35041Const.DEFAULT.ZERO,
    sectionAddNo: Or35041Const.DEFAULT.ZERO,
    iniDataObject: { ...reportInitData.value },
    choPrtList: choPrtList,
    sectionName: localOneway.Or35041.sectionName,
  }

  // バックエンドAPIから初期情報取得
  await ScreenRepository.update('printSettingsInfoComUpdate', inputData)
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or35041_screen"
      >
        <c-v-col
          cols="12"
          sm="3"
          class="pa-0 pt-2 px-2 or35041_border_right"
        >
          <!-- 帳票 -->
          <base-mo-01334
            v-model="mo01334TypeReport"
            :oneway-model-value="mo01334OnewayReport"
            class="list-wrapper"
          >
            <!-- 帳票ラベル  -->
            <template #[`item.defPrtTitle`]="{ item }">
              <base-mo01337 :oneway-model-value="item.mo01337OnewayReport" />
            </template>
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="9"
          class="pa-0 pt-2 content_center"
        >
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="or35041_row flex-center"
          >
            <c-v-col
              cols="auto"
              class="pa-2"
            >
              <!-- タイトル  -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="auto"
              class="pa-2 pl-0"
            >
              <!-- タイトル利用票·別表  -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OnewayTitleInput" />
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or35041_row"
            style="height: 133px"
          >
            <c-v-col
              cols="12"
              sm="7"
              class="pa-2"
            >
              <!-- 印刷日付選択ラジオボタングループ -->
              <base-mo01338
                v-if="
                  localOneway.mo01338OnewayTitleInput.value ===
                  t('label.using-slip-other-table-warning-message')
                "
                :oneway-model-value="localOneway.mo01338OneWayDateTitle"
              ></base-mo01338>
              <base-mo00039
                v-else
                v-model="local.mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              id="test"
              cols="12"
              sm="5"
              class="pa-2"
            >
              <!-- 印刷日付ラベル -->
              <base-mo00020
                v-if="
                  localOneway.mo01338OnewayTitleInput.value ===
                  t('label.using-slip-other-table-warning-message')
                "
                v-model="local.mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
              <base-mo00020
                v-else
                v-show="local.mo00039Type == '2'"
                v-model="local.mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or35041_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2 d-flex"
            >
              <!-- 提供年月 -->
              <c-v-row
                cols="12"
                sm="12"
                class="or35041_row"
              >
                <base-mo00020
                  v-model="local.mo00020TypeKijunbi"
                  :oneway-model-value="localOneway.mo00020KijunbiOneWay"
                />
              </c-v-row>
              <c-v-row
                cols="12"
                sm="12"
                class="or35041_row"
              >
                <!--オプションボタン-->
                <base-mo00611
                  class="mr-2"
                  :oneway-model-value="localOneway.mo00611OneWayCopy"
                  @click="open()"
                >
                  <!--ツールチップ表示："印刷オプション画面を開けます"-->
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :max-width="600"
                    :text="t('tooltip.printing-options')"
                    open-delay="200"
                  />
                </base-mo00611>
              </c-v-row>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pl-2 pt-0 pr-2"
            >
              <!-- シミュレーション雛形一覧 -->
              <base-mo-01334
                v-model="local.mo01334"
                :oneway-model-value="localOneway.mo01334Oneway"
                class="list-wrapper"
                hide-default-footer
              >
                <!-- 項目1 -->
                <template #[`item.dmySel`]="{ item }">
                  <!-- 分子：表用チェックフィールド -->
                  <base-mo00018
                    v-model="item.dmySel"
                    :oneway-model-value="localOneway.mo00018OnewayType"
                  ></base-mo00018>
                </template>
                <!-- 項目2 -->
                <template #[`item.yokaiKbn.value`]="{ item }">
                  <!-- 分子：表用テキストフィールド -->
                  <base-mo01337 :oneway-model-value="item.yokaiKbn"></base-mo01337>
                </template>
                <!-- 項目3 -->
                <template #[`item.simTitleKnj.value`]="{ item }">
                  <!-- 分子：表用テキストフィールド -->
                  <base-mo01337 :oneway-model-value="item.simTitleKnj" />
                </template>
                <!-- ページングを非表示 -->
                <template #bottom />
              </base-mo-01334>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          @click="print()"
        >
          <!--ツールチップ表示："PDFをダウンロードします"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.pdf-download')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
  <!-- GUI01157_印刷オプションを起動する -->
  <g-custom-or-26315
    v-if="showDialogOr26315"
    v-bind="or26315"
    :oneway-model-value="or26315Data"
  />
</template>

<style scoped lang="scss">
.or35041_screen {
  margin: -8px !important;
}

.or35041_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or35041_row {
  margin: 0px !important;
}

.content_center {
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.customCol {
  margin-left: 0px;
  margin-right: 0px;
}
</style>
