import type {
  Or10004OneList,
  Or10004<PERSON>aram,
  Or10004ThreeList,
  Or10004TwoList,
  SeqList,
} from '../Or10004/Or10004.type'
import type { Mo01280Type } from '~/types/business/components/Mo01280Type'

/**
 * Or11122:有機体:((計画ﾓﾆﾀﾘﾝｸﾞ)表示順変更モニタリング記録表)タブ
 * Gui01230_表示順変更モニタリング記録表
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or11122StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
  /**
   * パラメータ
   */
  param: Or10004Param
}

/**
 * OneWayType
 */
export interface Or11122OneWayType {
  /**
   * タブ初期表示
   */
  styleFlg: string

  /**
   * タブ切り替え
   */
  tabFlg: string
  /**
   * 表示順リスト
   */
  seqList?: SeqList
  /**
   * 番号区分
   */
  noFlg?: string
  /**
   * 長期目標区分
   */
  longTermFlg?: string
}
/**
 * Or11122Type
 */
export interface Or11122Type {
  /**
   * 計画ﾓﾆﾀﾘﾝｸﾞ画面の一覧
   */
  list1: Or10004OneList[]
  /**
   * 計画ﾓﾆﾀﾘﾝｸﾞ画面の一覧
   */
  list2: Or10004TwoList[]
  /**
   * 計画ﾓﾆﾀﾘﾝｸﾞ画面の一覧
   */
  list3: Or10004ThreeList[]
  /**
   * データ行の選択値
   */
  selectRowId: string
}

/**
 * TableData
 */
export interface Or11122TableData {
  /**
   * id
   */
  id: string
  /**
   * 表示順
   */
  seq: Mo01280Type
  /**
   * 目標
   */
  mokuhyoKnj: string

  /**
   * 対象期間
   */
  kikanKnj: string

  /**
   * サービスの実施状況
   */
  jyoukyouKnj: string

  /**
   * 目標達成状況（担当者評価）
   */
  tantoHyoukaCd: string

  /**
   * その理由（担当者評価）
   */
  tantoHyoukaKnj: string

  /**
   * 目標達成状況（利用者評価）
   */
  userHyoukaCd: string

  /**
   * その理由（利用者評価）
   */
  userHyoukaKnj: string

  /**
   * 今後の対応
   */
  taiouCd: string

  /**
   * その理由
   */
  taiouKnj: string
}

/**
 * Or10004List
 */
export interface Or11122TableTwoData {
  /**
   * id
   */
  id: string
  /**
   * seq
   */
  seq: Mo01280Type
  /**
   * 課題
   */
  kadaiKnj: string

  /**
   * 長期目標
   */
  choukiKnj: string

  /**
   * 短期目標
   */
  tankiKnj: string

  /**
   * サービス内容
   */
  kaigoKnj: string

  /**
   * 課題番号
   */
  kadaiNo: string

  /**
   * 介護番号
   */
  servNo: string

  /**
   * 実行の確認ＣＤ
   */
  kakuninCd: string

  /**
   * 実行の確認
   */
  kakuninKnj: string

  /**
   * 実行の方法ＣＤ
   */
  houhouCd: string

  /**
   * 実行の方法
   */
  houhouKnj: string

  /**
   * 確認期日
   */
  kakuninYmd: string

  /**
   * 本人の意見ＣＤ
   */
  ikenHonCd: string

  /**
   * 本人の意見
   */
  ikenHonKnj: string

  /**
   * 家族の意見ＣＤ
   */
  ikenKazCd: string

  /**
   * 家族の意見
   */
  ikenKazKnj: string

  /**
   * ニーズ充足度ＣＤ
   */
  jusokuCd: string

  /**
   * ニーズ充足度
   */
  jusokuKnj: string

  /**
   * 今後の対応ＣＤ
   */
  taiouCd: string

  /**
   * 今後の対応
   */
  taiouKnj: string
}
/**
 * Or10004List
 */
export interface Or11122TableThreeData {
  /**
   * id
   */
  id: string
  /**
   * seq
   */
  seq: Mo01280Type
  /** カスタマイズデータ */
  [key: string]:
    | {
        /** 文章 */
        koumokuKnj: string
        /** コード */
        koumokuCod: string
        /** 日付 */
        koumokuYmd: string
      }
    | { value: string }
    | string
}
