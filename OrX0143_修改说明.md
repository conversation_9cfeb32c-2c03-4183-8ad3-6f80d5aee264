# OrX0143 履歴一覧情報 - 選択状態記憶機能の実装

## 概要
OrX0143コンポーネントに、singleFlg（単一/複数フラグ）の切り替え時に各モードの選択状態を記憶する機能を追加しました。

## 実装内容

### 1. 新しい状態変数の追加
```typescript
// 単一モード（singleFlg='0'）と複数モード（singleFlg='1'）の選択状態を分けて保存
const taniModeSelectedRows = ref<boolean[]>([])      // 単一モードの行選択状態
const hukusuuModeSelectedRows = ref<boolean[]>([])   // 複数モードの行選択状態
const taniModeSelectAll = ref<boolean>(false)        // 単一モードの全選択状態
const hukusuuModeSelectAll = ref<boolean>(false)     // 複数モードの全選択状態
```

### 2. 辅助函数の追加
- `restoreSelectionForCurrentMode()`: 現在のモードに応じて選択状態を復元
- `saveSelectionForCurrentMode()`: 現在のモードの選択状態を保存

### 3. 主要な修正点

#### singleFlg監視処理の改善
```typescript
watch(
  () => localOneway.orX0143.singleFlg,
  (newValue, oldValue) => {
    if (newValue && oldValue !== undefined) {
      // 切り替え前のモードの選択状態を保存
      saveSelectionForCurrentMode()
      
      // 配列サイズを調整（データが変更された場合に備えて）
      // ...
      
      // 新しいモードの選択状態を復元
      restoreSelectionForCurrentMode()
    }
  }
)
```

#### イベントハンドラーの修正
- `handleRowClick()`: 行クリック時に選択状態を保存
- `checkedChange()`: チェックボックス変更時に選択状態を保存
- `toggleAllRows()`: 全選択時に選択状態を保存

#### データ更新処理の改善
- `historyEdit()`: データ更新時に既存の選択状態を保持
- `init()`: 初期化時に適切な選択状態を設定

## 動作仕様

### 単一モード（singleFlg='0'）
- 1行のみ選択可能
- 選択状態は`taniModeSelectedRows`に保存
- 他のモードに切り替えても選択状態を記憶

### 複数モード（singleFlg='1'）
- 複数行選択可能
- 選択状態は`hukusuuModeSelectedRows`に保存
- 全選択機能あり
- 他のモードに切り替えても選択状態を記憶

### モード切り替え時の動作
1. 現在のモードの選択状態を保存
2. データ長の変更に対応（配列サイズ調整）
3. 新しいモードの過去の選択状態を復元

## 使用例

```typescript
// 初期状態: 単一モードで1行目を選択
// singleFlg = '0', taniModeSelectedRows = [true, false, false, ...]

// 複数モードに切り替え
// singleFlg = '1', hukusuuModeSelectedRows = [false, false, false, ...]

// 複数モードで2,3行目を選択
// hukusuuModeSelectedRows = [false, true, true, false, ...]

// 単一モードに戻る
// singleFlg = '0', 1行目が再び選択された状態に復元
// taniModeSelectedRows = [true, false, false, ...]

// 再び複数モードに切り替え
// singleFlg = '1', 2,3行目が選択された状態に復元
// hukusuuModeSelectedRows = [false, true, true, false, ...]
```

## 注意事項
- データが更新された場合、配列サイズが自動調整されます
- 既存の選択状態は可能な限り保持されます
- 初期化時は単一モードで最初の有効行が選択されます
