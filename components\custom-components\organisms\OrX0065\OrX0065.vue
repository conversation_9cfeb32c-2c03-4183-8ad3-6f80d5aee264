<script setup lang="ts">
/**
 * OrX0065:有機体:パターン日常生活・介護サービス例マスタ
 * GUI00996_日常生活・介護サービス例マスタ
 *
 * @description
 * 日常生活・介護サービス例マスタタブ：タイトル
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { VForm } from 'vuetify/components'
import { OrX0065Const } from './OrX0065.constants'
import type { TableData } from './OrX0065.type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type {
  OrX0065OnewayType,
  OrX0065Type,
  Shousai,
} from '~/types/cmn/business/components/OrX0065Type'

import type { OrX0018OnewayType } from '~/types/cmn/business/components/OrX0018Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { useScreenUtils } from '~/utils/useScreenUtils'
import { useScreenTwoWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  Or21814EventType,
  Or21814OnewayType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { useValidation } from '~/utils/useValidation'
import type {
  ShousaiInfoSelectInEntity,
  ShousaiInfoSelectOutEntity,
} from '~/repositories/cmn/entities/ShousaiInfoSelectEntity'
import type {
  Or21815EventType,
  Or21815StateType,
} from '~/components/base-components/organisms/Or21815/Or21815.type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
const { setChildCpBinds } = useScreenUtils()

const { t } = useI18n()

const validation = useValidation()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: OrX0065OnewayType
  modelValue: OrX0065Type
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()

const defaultOnewayModelValue: OrX0065OnewayType = {
  tabKbn: '1',
  houjinId: '1',
  shisetuId: '1',
  svJigyoId: '1',
  saveAuthority: 'F',
}
const defaultModelValue: OrX0065Type = {
  editFlg: false,
  shousaiList: [],
  initFlg: false,
}

const or21814delBtn = ref({ uniqueCpId: '' })

const or21815delBtn = ref({ uniqueCpId: '' })

// ダイアログ表示フラグ
const showInfoDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814delBtn.value.uniqueCpId)?.isOpen ?? false
})

const showWarnDialog = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815delBtn.value.uniqueCpId)?.isOpen ?? false
})

const localOneWay = reactive({
  orX0065: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 新規
  mo00611OneWay: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
  } as Mo00611OnewayType,
  // 行複写
  mo00611CopyOneWay: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    disabled: true,
  } as Mo00611OnewayType,
  // 行削除
  mo01265OneWay: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    disabled: true,
  } as Mo01265OnewayType,
  // 表示順: 表用テキストフィールド
  orX0018SeqInputOneWay: {
    maxLength: '3',
    rules: [validation.integer, validation.required, validation.minValue(1)],
  } as OrX0018OnewayType,
  // 項目名: 表用テキストフィールド
  orX0018ItemNameInputOneWay: {
    maxLength: '28',
    rules: [validation.required],
  } as OrX0018OnewayType,
})

const local = reactive({
  orX0065: {
    ...defaultModelValue,
    ...props.modelValue,
  },
})
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<TableData[]>({
  cpId: OrX0065Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = []
/**************************************************
 * 変数定義
 **************************************************/
// 選択した行のindex
const selectedItemIndex = ref<number>(-1)
// テーブルヘッダ
const headers = [
  { title: t('label.id'), key: 'svId', width: '100px', sortable: false },
  { title: t('label.item-name'), key: 'svKnj', sortable: false, required: true },
  { title: t('label.display-order'), key: 'seq', width: '140px', sortable: false },
  { title: t('label.stop'), key: 'delFlg', align: 'center', width: '140px', sortable: false },
]
// "*"
const required: string = OrX0065Const.DEFAULT.REQUIRED
// 説明
const description: string = t('label.system-common-save')
// 元のテーブルデータ
const orgTableData = ref<string>('')
const tableForm = ref<VForm>()

const tableDataFilter = computed(() => {
  const titleList = refValue.value
  return titleList!.filter((i: TableData) => i.updateKbn !== UPDATE_KBN.DELETE)
})
const refs = ref<Record<string, HTMLInputElement>>({})
const setRef = (el: HTMLInputElement | null, tableIndex: string) => {
  if (el) {
    refs.value[tableIndex] = el
  }
}
// ポスト最小幅
const columnMinWidth = ref<number[]>([120, 213, 125, 123])
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**************************************************
 * コンポーネント固有処理
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814delBtn.value,
  [Or21815Const.CP_ID(0)]: or21815delBtn.value,
})

onMounted(async () => {
  await init()
})

/**
 * 日常生活・介護サービス例マスタ情報取得
 */
async function init() {
  // 日常生活・介護サービス例マスタ情報取得(IN)
  const param: ShousaiInfoSelectInEntity = {
    // タブ区分: タブ区分
    tabKbn: localOneWay.orX0065.tabKbn,
  }
  // 日常生活・介護サービス例マスタ情報取得
  const ret: ShousaiInfoSelectOutEntity = await ScreenRepository.select('shousaiInfoSelect', param)
  // 戻り値はテーブルデータとして処理されます
  let tmpArr = []
  tmpArr = []
  for (const item of ret.data.shousaiList) {
    tmpArr.push({
      svId: item.svId,
      houjinId: item.houjinId,
      shisetuId: item.shisetuId,
      svJigyoId: item.svJigyoId,
      dataKbn: item.dataKbn,
      svKnj: { value: item.svKnj },
      seq: { value: item.seq },
      delFlg: { values: [item.delFlg] },
      modifiedCnt: item.modifiedCnt,
      tableIndex: tmpArr.length,
      updateKbn: UPDATE_KBN.NONE,
    })
  }

  // 元のテーブルデータの設定
  orgTableData.value = JSON.stringify(refValue.value)
  // GUI00017
  switch (localOneWay.orX0065.tabKbn) {
    case '1':
      setChildCpBinds(props.parentUniqueCpId, {
        OrX0065: {
          twoWayValue: tmpArr,
        },
      })
      break
    case '2':
      setChildCpBinds(props.parentUniqueCpId, {
        OrX0065_1: {
          twoWayValue: tmpArr,
        },
      })
      break
    case '3':
      setChildCpBinds(props.parentUniqueCpId, {
        OrX0065_2: {
          twoWayValue: tmpArr,
        },
      })
      break
    default:
      break
  }

  // 画面.パターンタイトル一覧の1行目を選択する。
  selectRow(0)
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
  // 行削除活性
  localOneWay.mo01265OneWay.disabled = false
  // 行複写活性
  localOneWay.mo00611CopyOneWay.disabled = false
}

/**
 * 「新規」押下
 */
async function createRow() {
  let lastSeq = 0
  if (refValue.value!.length > 0) {
    for (const data of refValue.value!) {
      if (data.updateKbn !== UPDATE_KBN.DELETE) {
        lastSeq = Math.max(lastSeq, Number(data.seq.value))
      }
    }
  }
  // 表示順最大値＜999の場合
  if (lastSeq < 999) {
    // 表示順最大値＋1を設定
    lastSeq += 1
  } else {
    // 上記以外の場合、999を設定
    lastSeq = 999
  }
  // 週間計画パターンのタイトル一覧の最終に新しい行を追加する。
  const data = {
    // サービス例ID
    svId: '',
    //法人ID
    houjinId: localOneWay.orX0065.houjinId,
    // 施設ID
    shisetuId: localOneWay.orX0065.shisetuId,
    // 事業者ID
    svJigyoId: localOneWay.orX0065.svJigyoId,
    // 区分
    dataKbn: localOneWay.orX0065.tabKbn,
    // 日常生活等・介護サービス例
    svKnj: { value: '' },
    // 表示順
    seq: { value: String(lastSeq) },
    // 削除フラグ・中止フラグ
    delFlg: { values: ['0'] },
    // 更新回数
    modifiedCnt: '',
    // テーブルINDEX(行固有ID)
    tableIndex: refValue.value!.length,
    // 更新区分
    updateKbn: UPDATE_KBN.CREATE,
  }
  refValue.value!.push(data)
  await nextTick()
  const lastInput = refs.value[`input-${data.tableIndex}`]
  if (lastInput) {
    lastInput.focus()
  }
}

/**
 * 行複写
 */
async function onCloneItem() {
  if (selectedItemIndex.value !== -1) {
    await nextTick()
    const copydata = refValue.value![selectedItemIndex.value]
    const copyIndex = selectedItemIndex.value + 1
    const data = {
      // サービス例ID
      svId: '',
      //法人ID
      houjinId: localOneWay.orX0065.houjinId,
      // 施設ID
      shisetuId: localOneWay.orX0065.shisetuId,
      // 事業者ID
      svJigyoId: localOneWay.orX0065.svJigyoId,
      // 区分
      dataKbn: localOneWay.orX0065.tabKbn,
      // 日常生活等・介護サービス例
      svKnj: copydata.svKnj,
      // 表示順
      seq: copydata.seq,
      // 削除フラグ・中止フラグ
      delFlg: { values: [copydata.delFlg.values.length > 0 ? copydata.delFlg.values[0] : '0'] },
      // 更新回数
      modifiedCnt: '',
      // テーブルINDEX(行固有ID)
      tableIndex: copyIndex,
      // 更新区分
      updateKbn: UPDATE_KBN.CREATE,
    }
    refValue.value!.splice(copyIndex, 0, data)
    refValue.value!.forEach((item: { tableIndex: number }, index) => {
      item.tableIndex = index
    })
    await nextTick()
    const lastInput = refs.value[`input-${data.tableIndex}`]
    if (lastInput) {
      lastInput.focus()
    }
  }
}

/**
 * 行削除ボタン押下
 */
async function deleteRowMsg() {
  await nextTick()
  if (selectedItemIndex.value !== -1) {
    const selectData = refValue.value![selectedItemIndex.value]
    if (selectData.svJigyoId === OrX0065Const.DEFAULT.SV_JIGYO_ID) {
      // 以下のメッセージを表示
      await showOr21814Msg(t('message.i-cmn-10588'))
    } else {
      // 以下のメッセージを表示
      const rs = showOr21815Msg(t('message.w-cmn-20803'))
      if ((await rs).secondBtnClickFlg) {
        deleteRow()
        await nextTick()
        selectedItemIndex.value = -1
      }
    }
  }
}

/**
 * 行削除
 */
function deleteRow() {
  if (selectedItemIndex.value !== null) {
    refValue.value!.forEach((item: { tableIndex: number; updateKbn: string }) => {
      if (item.tableIndex === selectedItemIndex.value) {
        item.updateKbn = UPDATE_KBN.DELETE
        // 行削除非活性
        localOneWay.mo01265OneWay.disabled = true
        // 行複写非活性
        localOneWay.mo00611CopyOneWay.disabled = true
      }
    })
    refValue.value = refValue.value!.filter(
      (i: TableData) => i.updateKbn !== UPDATE_KBN.DELETE || i.svId !== ''
    )
  }
}

/**
 * 行削除の開閉
 *
 * @param infomsg - Message
 */
async function showOr21814Msg(infomsg: string) {
  const rs = await openConfirmDialog(or21814delBtn.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: infomsg,
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.ok'),
    secondBtnType: 'blank',
    thirdBtnType: 'blank',
  })
  return rs
}

/**
 * メッセージの開閉
 *
 * @param warnmsg - Message
 */
async function showOr21815Msg(warnmsg: string) {
  const rs = await openWarnDialog(or21815delBtn.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.warning'),
    // ダイアログテキスト
    dialogText: warnmsg,
    firstBtnType: 'normal3',
    firstBtnLabel: t('btn.no'),
    secondBtnType: 'normal1',
    secondBtnLabel: t('btn.yes'),
    thirdBtnType: 'blank',
  })
  return rs
}

// ダイアログResolve
let resolvePromise: (value: Or21814EventType) => void

function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    resolvePromise = resolve
  })
}

// ダイアログResolve
let warnResolvePromise: (value: Or21815EventType) => void

/**
 * 警告メッセージを開きます
 *
 * @param uniqueCpId -uniqueCpId
 *
 * @param state -Or21815StateType
 */
function openWarnDialog(uniqueCpId: string, state: Or21815StateType) {
  Or21815Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21815EventType>((resolve) => {
    warnResolvePromise = resolve
  })
}

/**
 * 検証
 */
async function tableValidation() {
  await nextTick()
  return (await tableForm.value!.validate()).valid
}
/**
 * コンテンツの更新
 */
function onUpdate() {
  refValue.value!.forEach((item: TableData) => {
    if (item.tableIndex === selectedItemIndex.value && item.updateKbn !== UPDATE_KBN.CREATE) {
      item.updateKbn = UPDATE_KBN.UPDATE
      selectedItemIndex.value = -1
      // 行削除非活性
      localOneWay.mo01265OneWay.disabled = true
      // 行複写非活性
      localOneWay.mo00611CopyOneWay.disabled = false
    }
  })
}

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814delBtn.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    await nextTick()
    if (resolvePromise !== undefined && newValue !== undefined) {
      resolvePromise(newValue)
    }
    return
  }
)

/**
 * Or21815のイベントを監視
 *
 * @description
 * またOr21815のボタン押下フラグをリセットする。
 */
watch(
  () => Or21815Logic.event.get(or21815delBtn.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    await nextTick()
    if (warnResolvePromise !== undefined && newValue !== undefined) {
      warnResolvePromise(newValue)
    }
    return
  }
)

watch(
  () => refValue.value,
  () => {
    local.orX0065.editFlg = orgTableData.value !== JSON.stringify(refValue.value)
    // タイトルのリスト
    const shousaiList: Shousai[] = []
    for (const shousai of refValue.value!) {
      shousaiList.push({
        ...shousai,
        svKnj: shousai.svKnj.value,
        seq: shousai.seq.value,
        delFlg: shousai.delFlg.values.length > 0 ? shousai.delFlg.values[0] : '0',
      })
    }
    local.orX0065.shousaiList = shousaiList
    emit('update:modelValue', local.orX0065)
  },
  { deep: true }
)

/**
 * 表データの設定
 */
watch(
  () => local.orX0065.saveResultShousaiList,
  () => {
    if (Array.isArray(local.orX0065.saveResultShousaiList)) {
      const shousaiList = []
      for (const item of local.orX0065.saveResultShousaiList) {
        shousaiList.push({
          // サービス例ID
          svId: item.svId,
          // 法人ID
          houjinId: item.houjinId,
          // 施設ID
          shisetuId: item.shisetuId,
          // 事業者ID
          svJigyoId: item.svJigyoId,
          // 区分
          dataKbn: item.dataKbn,
          // 日常生活等・介護サービス例
          svKnj: { value: item.svKnj },
          // 表示順
          seq: { value: item.seq },
          // 削除フラグ・中止フラグ
          delFlg: { values: [item.delFlg] },
          //更新回数
          modifiedCnt: item.modifiedCnt,
          // テーブルINDEX(行固有ID)
          tableIndex: shousaiList.length,
          // 更新区分
          updateKbn: UPDATE_KBN.NONE,
        })
      }
      refValue.value = shousaiList
      // 元のテーブルデータの設定
      orgTableData.value = JSON.stringify(refValue.value)
    }
  }
)
/**
 * 初期表示フラグを傍受する
 */
watch(
  () => local.orX0065.initFlg,
  async (newVal) => {
    if (newVal) {
      await init()
      local.orX0065.initFlg = false
      emit('update:modelValue', local.orX0065)
    }
  }
)

watch(
  () => props.onewayModelValue.tabKbn,
  async () => {
    localOneWay.orX0065.tabKbn = props.onewayModelValue.tabKbn
    await init()
  }
)

defineExpose({
  tableValidation,
})
</script>

<template>
  <div class="title-container">
    <c-v-row no-gutters>
      <!-- 新規ボタン -->
      <base-mo00611
        v-bind="localOneWay.mo00611OneWay"
        class="ml-2"
        @click="createRow"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.add-row')"
        />
      </base-mo00611>
      <!-- 複写ボタン -->
      <base-mo00611
        v-bind="localOneWay.mo00611CopyOneWay"
        class="mx-2"
        @click="onCloneItem"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.care-plan2-cpyline-btn')"
        ></c-v-tooltip>
      </base-mo00611>
      <!-- 削除ボタン -->
      <base-mo01265
        v-bind="localOneWay.mo01265OneWay"
        @click="deleteRowMsg"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="t('tooltip.display-order-delete-success')"
        />
      </base-mo01265>
    </c-v-row>
    <!-- パターン一覧 -->
    <c-v-form ref="tableForm">
      <c-v-data-table
        v-resizable-grid="{ columnWidths: columnMinWidth }"
        fixed-header
        :headers="headers"
        :items="tableDataFilter"
        class="table-wrapper mt-4"
        height="300px"
        width="600px"
        hide-default-footer
        :items-per-page="-1"
      >
        <!-- ヘッダ Part -->
        <template #headers>
          <tr>
            <!-- ID -->
            <th class="width-20">{{ t('label.id') }}</th>
            <!-- *項目名 -->
            <th class="width-200">
              <span style="color: red">{{ required }}</span
              >{{ t('label.item-name') }}
            </th>
            <!-- *表示順 -->
            <th class="width-100">
              <span style="color: red">{{ required }}</span
              >{{ t('label.display-order') }}
            </th>
            <!-- 中止 -->
            <th
              class="width-20"
              style="text-align: center"
            >
              {{ t('label.stop') }}
            </th>
          </tr>
        </template>
        <!-- 一覧 -->
        <template #item="{ item, index }">
          <tr
            :class="{ 'select-row': selectedItemIndex === item.tableIndex }"
            @click="selectRow(item.tableIndex)"
          >
            <!-- ID: 右寄せ -->
            <td>
              {{ item.svId }}
            </td>
            <!-- 項目名: テキストフィールド -->
            <td
              v-if="tableDataFilter[index].svJigyoId === OrX0065Const.DEFAULT.SV_JIGYO_ID"
              class="sv-jigyo-color"
            >
              {{ tableDataFilter[index].svKnj.value }}
            </td>
            <td v-else>
              <g-custom-or-x-0018
                v-model="tableDataFilter[index].svKnj"
                :re-ref="(el: HTMLInputElement | null) => setRef(el, `input-${item.tableIndex}`)"
                :oneway-model-value="localOneWay.orX0018ItemNameInputOneWay"
                @change="onUpdate"
              ></g-custom-or-x-0018>
            </td>
            <!-- 表示順: テキストフィールド -->
            <td>
              <g-custom-or-x-0018
                v-model="tableDataFilter[index].seq"
                :oneway-model-value="localOneWay.orX0018SeqInputOneWay"
                :reverse="true"
                @change="onUpdate"
              ></g-custom-or-x-0018>
            </td>
            <!-- 削除フラグ・中止フラグ -->
            <td class="text-align-center">
              <!-- 分子：チェックボックス -->
              <base-mo01332
                v-model="item.delFlg"
                style="border: 0 !important"
                :oneway-model-value="{
                  items: [
                    {
                      label: '',
                      value: '1',
                    },
                  ],
                }"
                @change="onUpdate"
              />
            </td>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-form>
  </div>
  <!-- 説明: ※全共通（※システム共通で保存） -->
  <div class="body-text-s">
    {{ description }}
  </div>
  <!-- 選択行削除確認ダイアログ -->
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showInfoDialog"
    v-bind="or21814delBtn"
  />
  <!-- Or21815:有機体:警告ダイアログ -->
  <g-base-or21815
    v-if="showWarnDialog"
    v-bind="or21815delBtn"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
:deep(.v-table) {
  padding: 0px !important;
}
:deep(.v-table td) {
  max-height: 32px;
  line-height: 1;
}
:deep(.v-checkbox .v-checkbox-btn) {
  min-height: 32px !important;
  height: 32px !important;
}

// タイトルのCSS
.title-container {
  margin-top: 15px;
}
//
.text-padding {
  padding: 0px !important;
}
// 右寄せのCSS
.text-align-right {
  text-align: right;
}
.text-align-left {
  text-align: left;
}
// テキストを横方向中央揃え
.text-align-center {
  text-align: center;
}
// 選択した行のCSS
.select-row {
  background: #dbeefe;
}
// 事業者ID=0の場合、背景色:#FFE8DD
.sv-jigyo-color {
  background: #ffe8dd;
}
:deep(.v-table--fixed-header) {
  position: sticky;
  // ジッタ除去
  top: -0.5px;
  z-index: 2;
}
// 幅: 150
.width-150 {
  width: 150px;
}
// 幅: 200
.width-200 {
  width: 200px;
}
// 幅: 190
.width-190 {
  width: 190px;
}
// 幅: 100
.width-100 {
  width: 100px;
}
// 幅: 100
.width-20 {
  width: 20px;
}
</style>
