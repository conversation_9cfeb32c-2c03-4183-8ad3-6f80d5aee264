<script setup lang="ts">
/**
 * Or28676:有機体:月間・年間表複写POP画面
 * GUI00941_月間・年間表複写POP画面
 *
 * @description
 * 月間・年間表複写POP画面の処理
 *
 * <AUTHOR>
 */
import { onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0077Const } from '../../template/OrX0077/OrX0077.constants'
import { OrX0077Logic } from '../../template/OrX0077/OrX0077.logic'
import { OrX0098Const } from '../OrX0098/OrX0098.constants'
import { Or28331Const } from '../Or28331/Or28331.constants'
import { Or28331Logic } from '../Or28331/Or28331.logic'
import { Or28675Const } from '../Or28675/Or28675.constants'
import { Or28675Logic } from '../Or28675/Or28675.logic'
import { Or15845Const } from '../Or15845/Or15845.constants'
import { Or15846Const } from '../Or15846/Or15846.constants'
import { Or27263Const } from '../Or27263/Or27263.constants'
import type { Or28676ScreenType } from './Or28676.type'
import { Or28676Const } from './Or28676.constants'
import { useScreenOneWayBind, useSetupChildProps, useScreenUtils } from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  Or28676OnewayType,
  Or28676StateType,
} from '~/types/cmn/business/components/Or28676Type'
import type { OrX0077OnewayType } from '~/types/cmn/business/components/OrX0077Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type {
  Or21814OnewayType,
  Or21814EventType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import type {
  IKikanInfo,
  IRirekiInfo,
  MonthlyYearlyTableCopyInfoSelectOutEntity,
} from '~/repositories/cmn/entities/MonthlyYearlyTableCopyInfoSelectEntity'
import type { ImplementationPlan2CopySelectInEntity } from '~/repositories/cmn/entities/ImplementationPlan2CopySelectEntity'
import type { Or15845Type } from '~/types/cmn/business/components/Or15845Type'
import type { Mo00046Type } from '~/types/business/components/Mo00046Type'

const { t } = useI18n()
const { setChildCpBinds } = useScreenUtils()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or28676OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

// デフォルトデータ変数
const defaultComponents = {
  // 月間・年間表_履歴対象 Or15845
  or15845: {
    mm: { value: '' },
    naiyoKnj: { value: '' },
    mokutekiKnj: { value: '' },
    seq: '',
    id: '',
    modifiedCnt: '',
  } as Or15845Type,
  // 視点入力支援 Or15846
  or15846: { value: '' } as Mo00046Type,
}

// データ変数
const localComponents = reactive({
  // 月間・年間表
  or15845: [
    {
      ...defaultComponents.or15845,
    } as Or15845Type,
  ],
  // 年間行事
  or15846: {
    ...defaultComponents.or15846,
  } as Mo00046Type,
})

// ローカルデータ変数
const localOneway = reactive({
  or28676Oneway: {
    ...props.onewayModelValue,
  } as Or28676OnewayType,
  // 複写テンプレート
  orX0077Oneway: {
    // 複写ダイアログ
    mo00024Oneway: {
      height: 'auto',
      maxWidth: '1430px',
      persistent: true,
      showCloseBtn: true,
      mo01344Oneway: {
        name: 'Or28676',
        toolbarTitle: t('label.monthly-yearly-table-duplicate'),
        toolbarName: 'Or28676ToolBar',
        // ツールバータイトルの左寄せ
        toolbarTitleCenteredFlg: false,
        showCardActions: true,
      },
    } as Mo00024OnewayType,
  } as OrX0077OnewayType,

  // 閉じるボタン設置
  mo00611Oneway: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,

  // 確定ボタン設置
  mo00609Oneway: {
    btnLabel: t('btn.confirm'),
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
})

// ローカル変数
const local = reactive({
  or28676: {
    kikanList: [] as IKikanInfo[],
    rirekiList: [] as IRirekiInfo[],
  } as Or28676ScreenType,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or28676StateType>({
  cpId: Or28676Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      OrX0077Logic.state.set({
        uniqueCpId: orX0077_1.value.uniqueCpId,
        state: { isOpen: value },
      })
    },
  },
})

const or21814_1 = ref({ uniqueCpId: '' })
const or28331_1 = ref({ uniqueCpId: '' })
const or28675_1 = ref({ uniqueCpId: '' })
const orX0077_1 = ref({ uniqueCpId: '' })
const orX0098_1 = ref({ uniqueCpId: '' })
const or15845_1 = ref({ uniqueCpId: '' })
const or15846_1 = ref({ uniqueCpId: '' })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or28331Const.CP_ID(1)]: or28331_1.value,
  [Or28675Const.CP_ID(1)]: or28675_1.value,
  [OrX0077Const.CP_ID(1)]: orX0077_1.value,
  [OrX0098Const.CP_ID(1)]: orX0098_1.value,
  [Or15845Const.CP_ID(1)]: or15845_1.value,
  [Or15846Const.CP_ID(1)]: or15846_1.value,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 操作区分
  local.or28676.operaFlg = Or28676Const.ACTION_TYPE.INIT
  // 選択用の利用者ＩＤ
  local.or28676.userId = props.onewayModelValue.userId
  // 初期情報取得
  await getInitDataInfo()
})
onUnmounted(() => {
  setChildCpBinds(props.uniqueCpId, {
    // 計画期間情報一覧
    [Or28331Const.CP_ID(1)]: {
      twoWayValue: {
        sc1Id: '',
        svJigyoId: '',
        isRowChange: false,
      },
      oneWayState: {
        kikanList: [],
      },
    },
    // 履歴情報一覧
    [Or28675Const.CP_ID(1)]: {
      twoWayValue: {
        rirekiId: '',
        isRowChange: false,
      },
      oneWayState: {
        rirekiList: [],
        kikanFlg: '',
      },
    },
    // 月間・年間表
    [Or15845Const.CP_ID(1)]: {
      twoWayValue: localComponents.or15845,
      oneWayState: { isCopyMode: true },
    },
    // 年間行事
    [Or15846Const.CP_ID(1)]: {
      twoWayValue: { value: localComponents.or15846.value ?? '' },
      oneWayState: { isCopyMode: true },
    },
  })
})
/**
 * 初期情報取得
 */
const getInitDataInfo = async () => {
  const inputData: ImplementationPlan2CopySelectInEntity = {
    // 施設ID
    operaFlg: local.or28676.operaFlg,
    // 期間管理フラグ
    kikanFlg: props.onewayModelValue.kikanFlg,
    // 施設ID
    shisetuId: props.onewayModelValue.shisetuId,
    // 利用者ID
    userId: local.or28676.userId,
    // 種別ID
    syubetuId: props.onewayModelValue.syubetuId,
    // 適用事業所ＩＤ
    svJigyoIdList: props.onewayModelValue.svJigyoIdList,
    // 計画期間ID
    sc1Id: local.or28676.sc1Id,
    // 事業者ID
    svJigyoId: local.or28676.svJigyoId,
    // 履歴ＩＤ
    rirekiId: local.or28676.rirekiId,
  }
  // 複写初期情報取得
  const ret: MonthlyYearlyTableCopyInfoSelectOutEntity = await ScreenRepository.select(
    'monthlyYearlyTableCopyInfoSelect',
    inputData
  )

  // 履歴情報
  const sortedRirekiList = [...ret.data.rirekiList].sort((a, b) => {
    // 表示順：作成日 降順
    if (a.createYmd > b.createYmd) return -1
    if (a.createYmd < b.createYmd) return 1

    // 履歴ＩＤ
    return b.rirekiId.localeCompare(a.rirekiId)
  })
  // 月間情報
  const sortedDataList = [...ret.data.dataList].sort((a, b) => {
    // 表示順 昇順
    return a.seq.localeCompare(b.seq)
  })
  // 計画期間情報
  const sortedKikanList = [...ret.data.kikanList].sort((a, b) => {
    // 表示順：開始日 降順
    if (a.startYmd > b.startYmd) return -1
    if (a.startYmd < b.startYmd) return 1

    // 終了日 降順
    if (a.endYmd > b.endYmd) return -1
    if (a.endYmd < b.endYmd) return 1

    // 計画期間ＩＤ 降順
    return b.sc1Id.localeCompare(a.sc1Id)
  })
  //操作区分が0:初期化の場合
  if (local.or28676.operaFlg === Or28676Const.ACTION_TYPE.INIT) {
    // 表示用「計画期間」リスト
    local.or28676.kikanList = sortedKikanList
    // 表示用「履歴情報」リスト
    local.or28676.rirekiList = sortedRirekiList
    // 表示用「月間情報」リスト
    local.or28676.dataList = sortedDataList

    // 選択用の計画期間ＩＤ
    local.or28676.sc1Id = local.or28676.kikanList.at(0)?.sc1Id ?? ''
    // 選択用の事業所ＩＤ
    local.or28676.svJigyoId = local.or28676.kikanList.at(0)?.svJigyoId ?? ''
    // 選択用の履歴ＩＤ
    local.or28676.rirekiId =
      sortedRirekiList.find((x) => x.sc1Id === sortedKikanList.at(0)?.sc1Id)?.rirekiId ?? ''
  } else if (local.or28676.operaFlg === Or28676Const.ACTION_TYPE.KIKAN) {
    // 操作区分が1:期間選択の場合
    // 表示用「履歴情報」リスト
    local.or28676.rirekiList = sortedRirekiList
    // 表示用「月間情報」リスト
    local.or28676.dataList = sortedDataList
    // 選択用の履歴ＩＤ
    local.or28676.rirekiId =
      sortedRirekiList.find((x) => x.sc1Id === local.or28676.sc1Id)?.rirekiId ?? ''
  } else if (local.or28676.operaFlg === Or28676Const.ACTION_TYPE.RIREKI) {
    // 操作区分が2:履歴選択の場合
    // 表示用「月間情報」リスト
    local.or28676.dataList = sortedDataList
  }
  localComponents.or15845 = local.or28676.dataList.map((item) => ({
    mm: { value: item.mm, unit: Or27263Const.UNIT_MONTH },
    naiyoKnj: { value: item.naiyoKnj },
    mokutekiKnj: { value: item.mokutekiKnj },
    seq: item.seq,
    id: '',
    modifiedCnt: '',
  }))
  localComponents.or15846 = {
    value:
      local.or28676.rirekiList.find((x) => x.rirekiId === local.or28676.rirekiId)?.sogoShitenKnj ??
      '',
  }
  setChildCpBinds(props.uniqueCpId, {
    // 計画期間情報一覧
    [Or28331Const.CP_ID(1)]: {
      twoWayValue: {
        sc1Id: local.or28676.sc1Id,
        svJigyoId: local.or28676.svJigyoId,
        isRowChange: false,
      },
      oneWayState: {
        kikanList: local.or28676.kikanList,
      },
    },
    // 履歴情報一覧
    [Or28675Const.CP_ID(1)]: {
      twoWayValue: {
        rirekiId: local.or28676.rirekiId,
        isRowChange: false,
      },
      oneWayState: {
        rirekiList: local.or28676.rirekiList,
        kikanFlg: props.onewayModelValue.kikanFlg,
      },
    },
    // 月間・年間表
    [Or15845Const.CP_ID(1)]: {
      twoWayValue: localComponents.or15845,
      oneWayState: { isCopyMode: true },
    },
    // 年間行事
    [Or15846Const.CP_ID(1)]: {
      twoWayValue: { value: localComponents.or15846.value ?? '' },
      oneWayState: { isCopyMode: true },
    },
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openInfoDialog(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })
        resolve(event)
      },
      { once: true }
    )
  })
}

/**
 * 「確定」ボタン押下
 */
const onClickConfirm = async () => {
  const data = Or28675Logic.data.get(or28675_1.value.uniqueCpId)
  //「画面.履歴情報一覧」の行を選択されていない場合
  if (!data?.rirekiId) {
    await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.info'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11289'),
      firstBtnType: 'blank',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.ok'),
      thirdBtnType: 'blank',
      iconName: 'info',
      iconColor: 'rgb(var(--v-theme-blue-700))',
      iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
    })
    // OK：処理終了。
    return
  }
  //「画面.履歴情報一覧」の行を選択しているの場合
  const dialogResult = await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10193', [t('label.monthly-yearly-table')]),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
  if (dialogResult?.firstBtnClickFlg === true) {
    // 選択された履歴IDを設定
    // 選択情報値戻り
    emit('update:modelValue', { rirekiId: data.rirekiId })
    close()
  }
}

/**
 * 閉じるボタン押下時
 */
const close = (): void => {
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => OrX0077Logic.state.get(orX0077_1.value.uniqueCpId),
  (newValue) => {
    if (!newValue?.isOpen) {
      close()
    }
  }
)

/**
 * 「計画期間一覧」の行をクリック
 */
watch(
  () => Or28331Logic.data.get(or28331_1.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.isRowChange) {
      //操作区分
      local.or28676.operaFlg = Or28676Const.ACTION_TYPE.KIKAN
      //選択用の計画期間ＩＤ
      local.or28676.sc1Id = newValue.sc1Id
      //選択用の事業所ＩＤ
      local.or28676.svJigyoId = newValue.svJigyoId
      await getInitDataInfo()
    }
  }
)

/**
 * 「履歴情報一覧」の行をクリック
 */
watch(
  () => Or28675Logic.data.get(or28675_1.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.isRowChange) {
      // 操作区分
      local.or28676.operaFlg = Or28676Const.ACTION_TYPE.RIREKI
      // 選択用の履歴ＩＤ
      local.or28676.rirekiId = newValue.rirekiId
      // 総合支援視点
      localComponents.or15846 = {
        value:
          local.or28676.rirekiList.find((x) => x.rirekiId === local.or28676.rirekiId)
            ?.sogoShitenKnj ?? '',
      }
      await getInitDataInfo()
    }
  }
)

/**
 * 利用者選択
 *
 * @param userSelfId - 利用者ID
 */
async function onChangeUserSelect(userSelfId: string) {
  //操作区分
  local.or28676.operaFlg = Or28676Const.ACTION_TYPE.INIT
  //選択用の計画期間ＩＤ
  local.or28676.sc1Id = ''
  //選択用の事業所ＩＤ
  local.or28676.svJigyoId = ''
  //選択用の履歴ＩＤ
  local.or28676.rirekiId = ''
  //選択用の利用者ＩＤ
  local.or28676.userId = userSelfId
  await getInitDataInfo()
}
</script>

<template>
  <!-- 複写テンプレート -->
  <g-custom-or-x-0077
    v-bind="orX0077_1"
    :oneway-model-value="localOneway.orX0077Oneway"
    @on-change-user-select="onChangeUserSelect"
  >
    <template #filter>
      <c-v-row class="pb-2">
        <c-v-col
          v-if="props.onewayModelValue.kikanFlg === '1'"
          cols="6"
        >
          <!--（記録共通期間（月間・年間表））計画期間情報一覧-->
          <g-custom-or28331 v-bind="or28331_1" />
        </c-v-col>
        <c-v-col cols="auto">
          <!--(月間・年間表）履歴情報一覧-->
          <g-custom-or28675 v-bind="or28675_1" />
        </c-v-col>
      </c-v-row>
    </template>
    <!-- 複写body -->
    <template #copyMain>
      <div class="copy-main-class">
        <c-v-row
          class="flex-1-1 h-100 no-margin"
          style="min-height: 0"
        >
          <c-v-col
            cols="auto"
            class="py-0"
          >
            <!-- 月間行事 -->
            <g-custom-or-15845
              v-bind="or15845_1"
              :unique-cp-id="or15845_1.uniqueCpId"
            ></g-custom-or-15845>
          </c-v-col>
          <c-v-col
            cols="auto"
            class="py-0"
          >
            <!-- 年間行事 -->
            <g-custom-or-15846
              v-bind="or15846_1"
              :unique-cp-id="or15846_1.uniqueCpId"
            ></g-custom-or-15846>
          </c-v-col>
        </c-v-row>
      </div>
    </template>
    <!-- フッターボタン -->
    <template #footer>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611Oneway"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="onClickConfirm()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </g-custom-or-x-0077>

  <!-- 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
.copy-main-class {
  height: 555px;
  width: 1123px;
  overflow-y: auto;
  overflow-x: auto;

  :deep(.v-col) {
    padding: 0 !important;
  }
  :deep(.title-container) {
    margin-top: 0 !important;
    margin-left: 8px !important;
  }
}
.no-margin {
  margin: 8px 0 0 0 !important;
}
.v-row {
  margin-left: 0 !important;
  margin-right: 0 !important;
}
:deep(.title-container) {
  margin-left: 0 !important;
}
:deep(.v-field__field) {
  height: 655px;
}
:deep(.section-header) {
  :deep(.v-sheet) {
    margin-right: 0 !important;
  }
}
:deep(td) {
  height: 66px !important;
}
:deep(td textarea) {
  height: 60px !important;
  min-height: 60px !important;
  resize: vertical;
}
</style>
