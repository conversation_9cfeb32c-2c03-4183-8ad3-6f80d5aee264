<script setup lang="ts">
import { computed, ref } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or28974Const } from '~/components/custom-components/organisms/Or28974/Or28974.constants'
import { Or28974Logic } from '~/components/custom-components/organisms/Or28974/Or28974.logic'
import type { Or28974Type, Or28974OnewayType } from '~/types/cmn/business/components/Or28974Type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**
 * GUI01283_印刷設定
 *
 * @description
 * 「印刷設定」画面を表示
 *
 * <AUTHOR> 劉顕康
 */

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01283'
// ルーティング
const routing = 'GUI01283/pinia'
// 画面物理名
const screenName = 'GUI01283'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or28974 = ref({ uniqueCpId: Or28974Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01283' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or28974Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or28974.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01283',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or28974Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or28974Const.CP_ID(1)]: or28974.value,
})

// ダイアログ表示フラグ
const showDialogOr28974 = computed(() => {
  // Or28974のダイアログ開閉状態
  return Or28974Logic.state.get(or28974.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or28974)
 */
function onClickOr28974() {
  // 処理年月日
  or28974Data.processYmd = '2025/06/19'
  // 担当ケアマネ設定フラグ
  or28974Data.careManagerSettingFlg = '0'

  // Or28974のダイアログ開閉状態を更新する
  Or28974Logic.state.set({
    uniqueCpId: or28974.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理(Or28974)
 */
function onClickOr28974p1() {
  // 処理年月日
  or28974Data.processYmd = ''

  // Or28974のダイアログ開閉状態を更新する
  Or28974Logic.state.set({
    uniqueCpId: or28974.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理(Or28974)
 */
function onClickOr28974p2() {
  // 処理年月日
  or28974Data.processYmd = '2025/06/19'
  // 担当ケアマネ設定フラグ
  or28974Data.careManagerSettingFlg = '1'

  // Or28974のダイアログ開閉状態を更新する
  Or28974Logic.state.set({
    uniqueCpId: or28974.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or28974Type = ref<Or28974Type>({
  // 処理フラグ
  processFlg: '0',
})

const or28974Data: Or28974OnewayType = {
  /** システムコード */
  systemCode: '01',
  /** システム略称 */
  systemNameShort: '3GK',
  /** 計画期間管理フラグ */
  planPeriodManagementFlg: '1',
  /** 法人ID */
  corporationId: '1',
  /** 施設ID */
  facilityId: '9',
  /** 事業者ID */
  officeId: '10',
  /** 利用者ID */
  userId: '1',
  /** 職員ID */
  staffId: '1',
  /** 担当ケアマネ設定フラグ */
  careManagerSettingFlg: '0',
  /** 担当者ID */
  tantoshaId: '302',
  /** システム年月 */
  systemYm: '2025/06',
  /** システム年月日 */
  systemYmd: '2025/06/20',
  /** セクション名 */
  sectionName: '認定調査票(H24/4～)',
  /** 選択帳票番号 */
  selectedLedgerNo: '1',
  /** 処理年月日 */
  processYmd: '2025/06/19',
  /** 50音行番号 */
  gojuuonRowNo: '3',
  /** 50音母音 */
  gojuuonKana: ['き'],
  /** 履歴ID */
  historyId: '2',
  /** 利用者情報リスト */
  userInfoList: [],
  /** アセスメントID */
  assessmentId: '5',
  /** 適用事業所IDリスト */
  applicableOfficeIdList: ['201', '202'],
  /** 事業所名 */
  jigyosha: '事業所名',
  /** 基準日 */
  stGendateYmd: '2025/07/25',
  /** 実施日 */
  appYmd: '2025/07/24',
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr28974"
        >GUI01283_印刷設定
      </v-btn>
      <g-custom-or-28974
        v-if="showDialogOr28974"
        v-bind="or28974"
        v-model="or28974Type"
        :oneway-model-value="or28974Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr28974p1"
        >GUI01283_印刷設定 親画面.処理年月日が""の場合
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr28974p2"
        >GUI01283_印刷設定 親画面.処理年月日が""以外、親画面.担当ケアマネ設定フラグ >
        0、かつ、親画面.担当者IDが0以外の場合
      </v-btn>
    </c-v-col>
  </c-v-row>
</template>
