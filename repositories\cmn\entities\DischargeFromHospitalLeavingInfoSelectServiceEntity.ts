import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * Or01305:有機体:API定義書_APINo(1078)_計画期間変更
 * GUI01305_API定義書_APINo(1078)_計画期間変更
 *
 * @description
 * API定義書_APINo(1078)_計画期間変更 エンティティ
 *
 * <AUTHOR>
 */
/** 計画期間変更 入力エンティティ */
export interface DischargeFromHospitalLeavingInfoPeriodSelectInEntity extends InWebEntity {
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userid: string
  /** 種別ID */
  syubetsuId: string
  /** 施設ID */
  shisetuId: string
  /** 期間ID */
  sc1Id: string
  /** 計画期間移動方式 */
  sc1MoveMode: string
}

/**
 * 計画期間変更 出力エンティティ
 */
export interface DischargeFromHospitalLeavingInfoPeriodSelectOutEntity extends OutWebEntity {
  /** data */
  data: {
    /** 計画期間情報 */
    planPeriodInfo: PlanPeriodInfo
    /** 履歴情報 */
    historyInfo: HistoryInfo1
    /** 権限期間情報 */
    kikanKanriFlg: string // 期間管理フラグ
  }
}

/**
 * 医療情報取得 入力エンティティ
 */
export interface DischargeFromHospitalLeavingInfoMedicalInfoSelectInEntity extends InWebEntity {
  /**
   *情報区分
   */
  jouhouKbn?: string
}

/**
 * 医療情報取得 出力エンティティ
 */
export interface DischargeFromHospitalLeavingInfoMedicalInfoSelectOutEntity extends OutWebEntity {
  /**
   * 医療機関情報リスト
   */
  comMscHospList: ComMscHospList[]
  /**
   * 診療科情報リスト
   */
  comMscHospKaList?: ComMscHospKaList[]
  /**
   * 医師情報リスト
   */
  comMscDoctoList: DoctorInfo[]
}

/**
 * 医療機関情報リスト
 */
interface ComMscHospList {
  /**
   * 医療機関コード - 必填
   */
  hospCd: string
  /**
   * 医療機関名
   */
  hospKnj?: string
}

/**
 * 診療科情報
 */
interface ComMscHospKaList {
  /**
   * 診療科コード - 必填
   */
  hospKaCd: string
  /**
   * 医療機関名
   */
  hospKnj?: string
}

/**
 * 医師情報リスト
 */
interface DoctorInfo {
  /**
   * 医師コード - 必填
   */
  hospDrCd: string
  /**
   * 医療機関名
   */
  hospKnj?: string
}

/**
 * 計画期間情報
 */
export interface PlanPeriodInfo {
  /** 期間ID */
  sc1Id: string
  /** 期間総件数 */
  periodCnt?: string
  /** 期間番号 */
  periodNo?: string
  /** 開始日 */
  startYmd?: string
  /** 終了日 */
  endYmd?: string
  /** 更新回数 */
  modifiedCnt?: string
}

/**
 * 履歴情報_1
 */
export interface HistoryInfo1 {
  /** 履歴ID */
  recId: string
  /** 計画期間ID */
  sc1Id: string
  /** 作成者ID */
  chkShokuId?: string
  /** 作成者名 */
  chkShokuNum?: string
  /** 作成日 */
  createYmd?: string
  /** 履歴総件数 */
  krirekiCnt?: string
  /** 履歴番号 */
  krirekiNo?: string
  /** 更新回数 */
  modifiedCnt?: string
}

/**
 *
 * @description
 * API定義書_APINo(1079)_履歴変更 エンティティ
 *
 * <AUTHOR>
 */
/** _履歴変更 入力エンティティ */
export interface DischargeFromHospitalLeavingInfoHistorySelectInEntity extends InWebEntity {
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userid: string
  /** 履歴ID */
  recId: string
  /** 期間ID */
  sc1Id: string
  /** 履歴移動方式 */
  kikanFlag: string
}

/** _履歴変更 出力エンティティ */
export interface DischargeFromHospitalLeavingInfoHistorySelectOutEntity extends OutWebEntity {
  /**
   *_履歴変更 出力エンティティ
   */
  data: {
    /** 履歴情報 */
    historyInfo: HistoryInfo1
  }
}

/**
 *
 * @description
 * API定義書_APINo(1080)_初期情報取得 エンティティ
 *
 * <AUTHOR>
 */
/** _初期情報取得 入力エンティティ */
export interface DischargeFromHospitalLeavingInfoInitInfoSelectInEntity extends InWebEntity {
  /** 履歴ID */
  recId: string
  /** 職員ID */
  shokuinId: string
  /** 適用事業所IDリスト */
  svJigyoList: string[]
  /** システムコード */
  gsysCd: string
}

/** _初期情報取得 出力エンティティ */
export interface DischargeFromHospitalLeavingInfoInitInfoSelectOutEntity extends OutWebEntity {
  /**
   *_初期情報取得 出力エンティティ
   */
  data: DischargeFromHospitalLeavingInfoInitInfo
}

/**
 * 初期情報取得
 */
export interface DischargeFromHospitalLeavingInfoInitInfo {
  /** 退院退所情報記録書情報 */
  cpnTucTaiKirokuData: CpnTucTaiKirokuData
  /** 閲覧権限情報 */
  comMscYokaigo: ComMscYokaigoInfo
  /** 要介護状態情報リスト */
  comMscYokaigoList: ComMscYokaigoInfoList[]
}

/**
 * 退院退所情報記録書情報
 */
export interface CpnTucTaiKirokuData {
  /** 履歴ID */
  recId?: string
  /** 要区分変更 */
  certifiedChange?: string
  /** 認定状況_要介護度 */
  certifiedStatusYokaigo?: string
  /** 認定状況_申請中 */
  certifiedStatusApplication?: string
  /** 認定状況_なし */
  certifiedStatusUnapplied?: string
  /** 要介護度 */
  certifiedYokaiKbn?: string
  /** 入院日 */
  admissionYmd?: string
  /** 退院予定日 */
  dischargeYmd?: string
  /** 入院原因疾患 */
  causeDiseaseKnj?: string
  /** 入院先_施設名 */
  facilityKnj?: string
  /** 入院先_棟 */
  facilityWardKnj?: string
  /** 入院先_室 */
  facilityRoomKnj?: string
  /** 今後の医学管理_医療機関名 */
  medicHospKnj?: string
  /** 今後の医学管理_方法_通院 */
  medicOutVisit?: string
  /** 今後の医学管理_方法_訪問診療 */
  medicVisit?: string
  /** 現在治療中の疾患①_疾患名 */
  disease1SickKnj?: string
  /** 現在治療中の疾患①_安定状況 */
  disease1Status?: string
  /** 現在治療中の疾患②_疾患名 */
  disease2SickKnj?: string
  /** 現在治療中の疾患②_安定状況 */
  disease2Status?: string
  /** 現在治療中の疾患③_疾患名 */
  disease3SickKnj?: string
  /** 現在治療中の疾患③_安定状況 */
  disease3Status?: string
  /** 移動手段_自立 */
  move1?: string
  /** 移動手段_杖 */
  move2?: string
  /** 移動手段_歩行器 */
  move3?: string
  /** 移動手段_車いす */
  move4?: string
  /** 移動手段_その他 */
  move5?: string
  /** 移動手段_その他_備考 */
  move5Knj?: string
  /** 排泄方法_トイレ */
  toilet1?: string
  /** 排泄方法_ポータブル */
  toilet2?: string
  /** 排泄方法_おむつ */
  toilet3?: string
  /** 排泄方法_カテーテルパウチ */
  toilet4?: string
  /** 排泄方法_カテーテルパウチ_備考 */
  toilet4Knj?: string
  /** 入浴方法_自立 */
  bath1?: string
  /** 入浴方法_シャワー浴 */
  bath2?: string
  /** 入浴方法_一般浴 */
  bath3?: string
  /** 入浴方法_機械浴 */
  bath4?: string
  /** 入浴方法_行わず */
  bath5?: string
  /** 食事形態_普通 */
  meal1?: string
  /** 食事形態_経管栄養 */
  meal2?: string
  /** 食事形態_その他 */
  meal3?: string
  /** 食事形態_その他_備考 */
  meal3Knj?: string
  /** UDF等の食形態区分 */
  mealUdfKnj?: string
  /** 嚥下機能_咽有無 */
  coughKbn?: string
  /** 嚥下機能_咽頻度 */
  coughStatus?: string
  /** 義歯_有無 */
  dentureKbn?: string
  /** 義歯_範囲 */
  dentureStatus?: string
  /** 義歯_入院中の使用 */
  dentureUse?: string
  /** 口腔清潔 */
  oralCleanliness?: string
  /** 口腔ケア */
  oralCare?: string
  /** 睡眠_状況 */
  sleepStatus?: string
  /** 睡眠_状況_備考 */
  sleepStatusKnj?: string
  /** 睡眠_眠剤使用 */
  sleepDrug?: string
  /** 認知精神_認知機能低下 */
  bpsd1?: string
  /** 認知精神_せん妄 */
  bpsd2?: string
  /** 認知精神_徘徊 */
  bpsd3?: string
  /** 認知精神_焦燥不穏 */
  bpsd4?: string
  /** 認知精神_攻撃性 */
  bpsd5?: string
  /** 認知精神_その他 */
  bpsd6?: string
  /** 認知精神_その他_備考 */
  bpsd6Knj?: string
  /** 本人への病名告知 */
  personAnnouncement?: string
  /** 本人_病気障害後遺症等の受け止め方 */
  personAcceptKnj?: string
  /** 本人_退院後の生活に関する意向 */
  personIntentionKnj?: string
  /** 家族_病気障害後遺症等の受け止め方 */
  familyAcceptKnj?: string
  /** 家族_退院後の生活に関する意向 */
  familyIntentionKnj?: string
  /** 医療処置の内容_なし */
  treatmentNone?: string
  /** 医療処置の内容_点滴 */
  treatment1?: string
  /** 医療処置の内容_酸素療法 */
  treatment2?: string
  /** 医療処置の内容_喀痰吸引 */
  treatment3?: string
  /** 医療処置の内容_気管切開 */
  treatment4?: string
  /** 医療処置の内容_胃ろう */
  treatment5?: string
  /** 医療処置の内容_経鼻栄養 */
  treatment6?: string
  /** 医療処置の内容_経腸栄養 */
  treatment7?: string
  /** 医療処置の内容_褥瘡 */
  treatment8?: string
  /** 医療処置の内容_尿道カテーテル */
  treatment9?: string
  /** 医療処置の内容_尿路ストーマ */
  treatment10?: string
  /** 医療処置の内容_消化管ストーマ */
  treatment11?: string
  /** 医療処置の内容_痛みコントロール */
  treatment12?: string
  /** 医療処置の内容_排便コントロール */
  treatment13?: string
  /** 医療処置の内容_自己注射 */
  treatment14?: string
  /** 医療処置の内容_自己注射_備考 */
  treatment14Knj?: string
  /** 医療処置の内容_その他 */
  treatment15?: string
  /** 医療処置の内容_その他_備考 */
  treatment15Knj?: string
  /** 看護の視点_なし */
  nurseNone?: string
  /** 看護の視点_血圧 */
  nurse1?: string
  /** 看護の視点_水分制限 */
  nurse2?: string
  /** 看護の視点_食事制限 */
  nurse3?: string
  /** 看護の視点_食形態 */
  nurse4?: string
  /** 看護の視点_嚥下 */
  nurse5?: string
  /** 看護の視点_口腔ケア */
  nurse6?: string
  /** 看護の視点_清潔ケア */
  nurse7?: string
  /** 看護の視点_血糖コントロール */
  nurse8?: string
  /** 看護の視点_排泄 */
  nurse9?: string
  /** 看護の視点_皮膚状態 */
  nurse10?: string
  /** 看護の視点_睡眠 */
  nurse11?: string
  /** 看護の視点_認知機能精神面 */
  nurse12?: string
  /** 看護の視点_服薬指導 */
  nurse13?: string
  /** 看護の視点_療養上の指導 */
  nurse14?: string
  /** 看護の視点_ターミナル */
  nurse15?: string
  /** 看護の視点_その他 */
  nurse16?: string
  /** 看護の視点_その他_備考 */
  nurse16Knj?: string
  /** リハビリの視点_なし */
  rehabNone?: string
  /** リハビリの視点_本人指導 */
  rehab1?: string
  /** リハビリの視点_家族指導 */
  rehab2?: string
  /** リハビリの視点_関節可動域練習 */
  rehab3?: string
  /** リハビリの視点_筋力増強練習 */
  rehab4?: string
  /** リハビリの視点_バランス練習 */
  rehab5?: string
  /** リハビリの視点_麻痺筋緊張改善練習 */
  rehab6?: string
  /** リハビリの視点_起居／立位等基本動作練習 */
  rehab7?: string
  /** リハビリの視点_摂食嚥下訓練 */
  rehab8?: string
  /** リハビリの視点_言語訓練 */
  rehab9?: string
  /** リハビリの視点_ADL練習 */
  rehab10?: string
  /** リハビリの視点_IADL練習 */
  rehab11?: string
  /** リハビリの視点_疼痛管理 */
  rehab12?: string
  /** リハビリの視点_更生装具福祉用具等管理 */
  rehab13?: string
  /** リハビリの視点_運動耐容能練習 */
  rehab14?: string
  /** リハビリの視点_地域活動支援 */
  rehab15?: string
  /** リハビリの視点_社会参加支援 */
  rehab16?: string
  /** リハビリの視点_就労支援 */
  rehab17?: string
  /** リハビリの視点_その他 */
  rehab18?: string
  /** リハビリの視点_その他_備考 */
  rehab18Knj?: string
  /** 禁忌事項_有無 */
  contraindication?: string
  /** 禁忌事項_内容 */
  contraindicationKnj?: string
  /** 症状病状の予後予測 */
  prognosisKnj?: string
  /** 退院に際しての日常生活の阻害要因 */
  obstructiveFactorKnj?: string
  /** 在宅復帰のために整えなければならない要件 */
  returnHomeKnj?: string
  /** 聞き取り①_日付 */
  listen1Ymd?: string
  /** 聞き取り①_職種氏名 */
  listen1StaffKnj?: string
  /** 聞き取り①_会議出席 */
  listen1Conference?: string
  /** 聞き取り②_日付 */
  listen2Ymd?: string
  /** 聞き取り②_職種氏名 */
  listen2StaffKnj?: string
  /** 聞き取り②_会議出席 */
  listen2Conference?: string
  /** 聞き取り③_日付 */
  listen3Ymd?: string
  /** 聞き取り③_職種氏名 */
  listen3StaffKnj?: string
  /** 聞き取り③_会議出席 */
  listen3Conference?: string
  /** 更新回数 */
  modifiedCnt?: string
  [key: string]: string | undefined | null
}

/**
 * 閲覧権限情報
 */
export interface ComMscYokaigoInfo {
  /** 介護保険画面 */
  kaigoHokenGamen?: string
  /** ｻｰﾋﾞｽ画面 */
  serviceGamen?: string
  /** 既往歴画面 */
  kiouRirekiGamen?: string
}

/**
 * 要介護状態情報リスト
 */
export interface ComMscYokaigoInfoList {
  /** 要介護状態区分 */
  yokaiKbn?: string
  /** 要介護度 */
  yokaiKnj?: string
}

/**
 *
 * @description
 * API定義書_APINo(1081)_新規情報取得 エンティティ
 *
 * <AUTHOR>
 */
/** _新規情報取得 入力エンティティ */
export interface DischargeFromHospitalLeavingInfoNewInfoSelectInEntity extends InWebEntity {
  /** 職員ID */
  shokuinId: string
  /** システムコード */
  gsysCd: string
  /** 適用事業所IDリスト */
  svJigyoIdList: string[]
  /** 利用者ID */
  userId: string
  /** 事業者ID */
  defSvJigyoId: string
  /** 基準日 */
  appYmd: string
}

/** _新規情報取得 出力エンティティ */
export interface DischargeFromHospitalLeavingInfoNewInfoSelectOutEntity extends OutWebEntity {
  /**
   *_新規情報取得 出力エンティティ
   */
  data: {
    /** 新規情報 */
    newInfo: NewInfo
  }
}

/**
 * 新規情報
 */
export interface NewInfo {
  /** 認定状況_要介護度 */
  certifiedStatusYokaigo: string
  /** 認定状況_申請中 */
  certifiedStatusApplication: string
  /** 認定状況_なし */
  certifiedStatusUnapplied: string
  /** 要介護度 */
  certifiedYokaiKbn: string
  /** 入院日 */
  admissionYmd: string
  /** 入院先_施設名 */
  facilityKnj: string
  /** 移動手段_自立 */
  move1: string
  /** 移動手段_杖 */
  move2: string
  /** 移動手段_歩行器 */
  move3: string
  /** 移動手段_車いす */
  move4: string
  /** 移動手段_その他 */
  move5: string
  /** 排泄方法_ポータブル */
  toilet2: string
  /** 排泄方法_おむつ */
  toilet3: string
  /** 入浴方法_自立 */
  bath1: string
  /** 食事形態_普通 */
  meal1: string
  /** 食事形態_経管栄養 */
  meal2: string
  /** 食事形態_その他 */
  meal3: string
  /** 食事形態_その他_備考 */
  meal3Knj: string
  /** UDF等の食形態区分 */
  mealUdfKnj: string
  /** 嚥下機能_咽有無 */
  coughKbn: string
  /** 嚥下機能_咽頻度 */
  coughStatus: string
  /** 義歯_有無 */
  dentureKbn: string
  /** 義歯_範囲 */
  dentureStatus: string
  /** 口腔清潔 */
  oralCleanliness: string
  /** 睡眠_状況 */
  sleepStatus: string
  /** 睡眠_状況_備考 */
  sleepStatusKnj: string
  /** 睡眠_眠剤使用 */
  sleepDrug: string
  /** 認知精神_せん妄 */
  bpsd2: string
  /** 認知精神_徘徊 */
  bpsd3: string
  /** 認知精神_焦燥・不穏 */
  bpsd4: string
  /** 認知精神_攻撃性 */
  bpsd5: string
  /** 認知精神_その他 */
  bpsd6: string
  /** 認知精神_その他_備考 */
  bpsd6Knj: string
  /** 本人_退院後の生活に関する意向 */
  personIntentionKnj: string
  /** 家族_退院後の生活に関する意向 */
  familyIntentionKnj: string
  /** 医療処置の内容_なし */
  treatmentNone: string
  /** 医療処置の内容_点滴 */
  treatment1: string
  /** 医療処置の内容_酸素療法 */
  treatment2: string
  /** 医療処置の内容_喀痰吸引 */
  treatment3: string
  /** 医療処置の内容_気管切開 */
  treatment4: string
  /** 医療処置の内容_胃ろう */
  treatment5: string
  /** 医療処置の内容_経鼻栄養 */
  treatment6: string
  /** 医療処置の内容_経腸栄養 */
  treatment7: string
  /** 医療処置の内容_褥瘡 */
  treatment8: string
  /** 医療処置の内容_尿道カテーテル */
  treatment9: string
  /** 医療処置の内容_尿路ストーマ */
  treatment10: string
  /** 医療処置の内容_消化管ストーマ */
  treatment11: string
  /** 医療処置の内容_痛みコントロール */
  treatment12: string
  /** 医療処置の内容_排便コントロール */
  treatment13: string
  /** 医療処置の内容_自己注射 */
  treatment14: string
  /** 医療処置の内容_自己注射_備考 */
  treatment14Knj: string
  /** 医療処置の内容_その他 */
  treatment15: string
  /** 医療処置の内容_その他_備考 */
  treatment15Knj: string
  [key: string]: string | undefined | null
}

/**
 *
 * @description
 * API定義書_APINo(1083)_データ保存 エンティティ
 *
 * <AUTHOR>
 */
/** _新規情報取得 入力エンティティ*/
export interface DischargeFromHospitalLeavingInfoUpdateInEntity extends InWebEntity {
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userId: string
  /** 計画期間ID */
  sc1Id: string
  /** 記入日 */
  createYmd: string
  /** 作成者 */
  chkShokuId: string
  /** 種別ID */
  syubetsuId: string
  /** 履歴ID */
  recId: string
  /** 履歴更新区分 */
  historyUpdateKbn: string
  /** 更新回数_履歴 */
  modifiedCnt: string
  /** 更新区分 */
  updateKbn: string
  /** 退院・退所情報記録書情報 */
  cpnTucTaiKirokuData: CpnTucTaiKirokuData
}

/** _新規情報取得 出力エンティティ*/
export interface DischargeFromHospitalLeavingInfoUpdateOutEntity extends OutWebEntity {
  /**
   *_新規情報取得 出力エンティティ
   */
  data: {
    /** 提供ID	 */
    cc1Id: string
    /** 期間ID */
    sc1Id: string
  }
}

/** 退院・退所情報記録書情報 */
export interface CpnTucTaiKirokuData {
  /** 要区分変更 */
  certifiedChange?: string
  /** 更新回数 */
  modifiedCnt?: string
  /** 認定状況_要介護度 */
  certifiedStatusYokaigo?: string
  /** 認定状況_申請中 */
  certifiedStatusApplication?: string
  /** 認定状況_なし */
  certifiedStatusUnapplied?: string
  /** 要介護度 */
  certifiedYokaiKbn?: string
  /** 入院日 */
  admissionYmd?: string
  /** 退院予定日 */
  dischargeYmd?: string
  /** 入院原因疾患 */
  causeDiseaseKnj?: string
  /** 入院先_施設名 */
  facilityKnj?: string
  /** 入院先_棟 */
  facilityWardKnj?: string
  /** 入院先_室 */
  facilityRoomKnj?: string
  /** 今後の医学管理_医療機関名 */
  medicHospKnj?: string
  /** 今後の医学管理_方法_通院 */
  medicOutVisit?: string
  /** 今後の医学管理_方法_訪問診療 */
  medicVisit?: string
  /** 現在治療中の疾患①_疾患名 */
  disease1SickKnj?: string
  /** 現在治療中の疾患①_安定状況 */
  disease1Status?: string
  /** 現在治療中の疾患②_疾患名 */
  disease2SickKnj?: string
  /** 現在治療中の疾患②_安定状況 */
  disease2Status?: string
  /** 現在治療中の疾患③_疾患名 */
  disease3SickKnj?: string
  /** 現在治療中の疾患③_安定状況 */
  disease3Status?: string
  /** 移動手段_自立 */
  move1?: string
  /** 移動手段_杖 */
  move2?: string
  /** 移動手段_歩行器 */
  move3?: string
  /** 移動手段_車いす */
  move4?: string
  /** 移動手段_その他 */
  move5?: string
  /** 移動手段_その他_備考 */
  move5Knj?: string
  /** 排泄方法_トイレ */
  toilet1?: string
  /** 排泄方法_ポータブル */
  toilet2?: string
  /** 排泄方法_おむつ */
  toilet3?: string
  /** 排泄方法_カテーテル・パウチ */
  toilet4?: string
  /** 排泄方法_カテーテル・パウチ_備考 */
  toilet4Knj?: string
  /** 入浴方法_自立 */
  bath1?: string
  /** 入浴方法_シャワー浴 */
  bath2?: string
  /** 入浴方法_一般浴 */
  bath3?: string
  /** 入浴方法_機械浴 */
  bath4?: string
  /** 入浴方法_行わず */
  bath5?: string
  /** 食事形態_普通 */
  meal1?: string
  /** 食事形態_経管栄養 */
  meal2?: string
  /** 食事形態_その他 */
  meal3?: string
  /** 食事形態_その他_備考 */
  meal3Knj?: string
  /** UDF等の食形態区分 */
  mealUdfKnj?: string
  /** 嚥下機能_咽有無 */
  coughKbn?: string
  /** 嚥下機能_咽頻度 */
  coughStatus?: string
  /** 義歯_有無 */
  dentureKbn?: string
  /** 義歯_範囲 */
  dentureStatus?: string
  /** 義歯_入院中の使用 */
  dentureUse?: string
  /** 口腔清潔 */
  oralCleanliness?: string
  /** 口腔ケア */
  oralCare?: string
  /** 睡眠_状況 */
  sleepStatus?: string
  /** 睡眠_状況_備考 */
  sleepStatusKnj?: string
  /** 睡眠_眠剤使用 */
  sleepDrug?: string
  /** 認知精神_認知機能低下 */
  bpsd1?: string
  /** 認知精神_せん妄 */
  bpsd2?: string
  /** 認知精神_徘徊 */
  bpsd3?: string
  /** 認知精神_焦燥・不穏 */
  bpsd4?: string
  /** 認知精神_攻撃性 */
  bpsd5?: string
  /** 認知精神_その他 */
  bpsd6?: string
  /** 認知精神_その他_備考 */
  bpsd6Knj?: string
  /** 本人への病名告知 */
  personAnnouncement?: string
  /** 本人_病気障害後遺症等の受け止め方 */
  personAcceptKnj?: string
  /** 本人_退院後の生活に関する意向 */
  personIntentionKnj?: string
  /** 家族_病気障害後遺症等の受け止め方 */
  familyAcceptKnj?: string
  /** 家族_退院後の生活に関する意向 */
  familyIntentionKnj?: string
  /** 医療処置の内容_なし */
  treatmentNone?: string
  /** 医療処置の内容_点滴 */
  treatment1?: string
  /** 医療処置の内容_酸素療法 */
  treatment2?: string
  /** 医療処置の内容_喀痰吸引 */
  treatment3?: string
  /** 医療処置の内容_気管切開 */
  treatment4?: string
  /** 医療処置の内容_胃ろう */
  treatment5?: string
  /** 医療処置の内容_経鼻栄養 */
  treatment6?: string
  /** 医療処置の内容_経腸栄養 */
  treatment7?: string
  /** 医療処置の内容_褥瘡 */
  treatment8?: string
  /** 医療処置の内容_尿道カテーテル */
  treatment9?: string
  /** 医療処置の内容_尿路ストーマ */
  treatment10?: string
  /** 医療処置の内容_消化管ストーマ */
  treatment11?: string
  /** 医療処置の内容_痛みコントロール */
  treatment12?: string
  /** 医療処置の内容_排便コントロール */
  treatment13?: string
  /** 医療処置の内容_自己注射 */
  treatment14?: string
  /** 医療処置の内容_自己注射_備考 */
  treatment14Knj?: string
  /** 医療処置の内容_その他 */
  treatment15?: string
  /** 医療処置の内容_その他_備考 */
  treatment15Knj?: string
  /** 看護の視点_なし */
  nurseNone?: string
  /** 看護の視点_血圧 */
  nurse1?: string
  /** 看護の視点_水分制限 */
  nurse2?: string
  /** 看護の視点_食事制限 */
  nurse3?: string
  /** 看護の視点_食形態 */
  nurse4?: string
  /** 看護の視点_嚥下 */
  nurse5?: string
  /** 看護の視点_口腔ケア */
  nurse6?: string
  /** 看護の視点_清潔ケア */
  nurse7?: string
  /** 看護の視点_血糖コントロール */
  nurse8?: string
  /** 看護の視点_排泄 */
  nurse9?: string
  /** 看護の視点_皮膚状態 */
  nurse10?: string
  /** 看護の視点_睡眠 */
  nurse11?: string
  /** 看護の視点_認知機能・精神面 */
  nurse12?: string
  /** 看護の視点_服薬指導 */
  nurse13?: string
  /** 看護の視点_療養上の指導 */
  nurse14?: string
  /** 看護の視点_ターミナル */
  nurse15?: string
  /** 看護の視点_その他 */
  nurse16?: string
  /** 看護の視点_その他_備考 */
  nurse16Knj?: string
  /** リハビリの視点_なし */
  rehabNone?: string
  /** リハビリの視点_本人指導 */
  rehab1?: string
  /** リハビリの視点_家族指導 */
  rehab2?: string
  /** リハビリの視点_関節可動域練習 */
  rehab3?: string
  /** リハビリの視点_筋力増強練習 */
  rehab4?: string
  /** リハビリの視点_バランス練習 */
  rehab5?: string
  /** リハビリの視点_麻痺・筋緊張改善練習 */
  rehab6?: string
  /** リハビリの視点_起居／立位等基本動作練習 */
  rehab7?: string
  /** リハビリの視点_摂食・嚥下訓練 */
  rehab8?: string
  /** リハビリの視点_言語訓練 */
  rehab9?: string
  /** リハビリの視点_ADL練習 */
  rehab10?: string
  /** リハビリの視点_IADL練習 */
  rehab11?: string
  /** リハビリの視点_疼痛管理 */
  rehab12?: string
  /** リハビリの視点_更生装具・福祉用具等管理 */
  rehab13?: string
  /** リハビリの視点_運動耐容能練習 */
  rehab14?: string
  /** リハビリの視点_地域活動支援 */
  rehab15?: string
  /** リハビリの視点_社会参加支援 */
  rehab16?: string
  /** リハビリの視点_就労支援 */
  rehab17?: string
  /** リハビリの視点_その他 */
  rehab18?: string
  /** リハビリの視点_その他_備考 */
  rehab18Knj?: string
  /** 禁忌事項_有無 */
  contraindication?: string
  /** 禁忌事項_内容 */
  contraindicationKnj?: string
  /** 症状病状の予後予測 */
  prognosisKnj?: string
  /** 退院に際しての日常生活の阻害要因 */
  obstructiveFactorKnj?: string
  /** 在宅復帰のために整えなければならない要件 */
  returnHomeKnj?: string
  /** 聞き取り①_日付 */
  listen1Ymd?: string
  /** 聞き取り①_職種氏名 */
  listen1StaffKnj?: string
  /** 聞き取り①_会議出席 */
  listen1Conference?: string
  /** 聞き取り②_日付 */
  listen2Ymd?: string
  /** 聞き取り②_職種氏名 */
  listen2StaffKnj?: string
  /** 聞き取り②_会議出席 */
  listen2Conference?: string
  /** 聞き取り③_日付 */
  listen3Ymd?: string
  /** 聞き取り③_職種氏名 */
  listen3StaffKnj?: string
  /** 聞き取り③_会議出席 */
  listen3Conference?: string
  [key: string]: string | undefined | null
}
