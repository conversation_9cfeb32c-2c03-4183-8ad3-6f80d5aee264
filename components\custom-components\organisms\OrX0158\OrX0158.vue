<script setup lang="ts">
/**
 * OrX0157:有機体:入力補助付きセレクト
 *
 * @description
 * 入力補助付きセレクト
 *
 * <AUTHOR>
 */
import { reactive, watch } from 'vue'
import type { OrX0158OnewayType, OrX0158Type } from '~/types/cmn/business/components/OrX0158Type'
import { CustomClass } from '~/types/CustomClassType'

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: OrX0158Type
  onewayModelValue?: OrX0158OnewayType
}
const props = defineProps<Props>()

const defaultOnewayModelValue: OrX0158OnewayType = {
  name: '',
  itemLabel: '',
  isRequired: false,
  showItemLabel: true,
  /** カスタムクラス */
  customClass: new CustomClass({ outerClass: 'mr-2', labelClass: 'ma-2' }),
  width: undefined,
  hideDetails: 'auto',
}
/**************************************************
 * 変数
 **************************************************/
const local = reactive({
  orX0158: {
    ...props.modelValue,
  },
})
const localOneway = reactive({
  orX0158Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
    customClass: {
      ...defaultOnewayModelValue?.customClass,
      ...props.onewayModelValue?.customClass,
    } as CustomClass,
  },
})
/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => props.modelValue,
  (newValue) => {
    local.orX0158 = {
      ...newValue,
    }
  },
  { deep: true }
)
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.orX0158Oneway = {
      ...defaultOnewayModelValue,
      ...newValue,
      customClass: {
        ...defaultOnewayModelValue?.customClass,
        ...newValue?.customClass,
      } as CustomClass,
    }
  },
  { deep: true }
)
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue', 'onClickEditBtn'])
watch(
  () => local.orX0158.modelValue,
  () => {
    emit('update:modelValue', local.orX0158)
  }
)
</script>

<template>
  <c-v-sheet
    :class="localOneway.orX0158Oneway.customClass?.outerClass"
    :style="localOneway.orX0158Oneway.customClass?.outerStyle"
    :width="localOneway.orX0158Oneway.width"
  >
    <c-v-row
      v-if="localOneway.orX0158Oneway.showItemLabel"
      no-gutters
      :class="localOneway.orX0158Oneway.customClass?.labelClass"
      :style="localOneway.orX0158Oneway.customClass?.labelStyle"
      class="align-start"
    >
      <c-v-col cols="auto">
        <!-- 項目名 -->
        <base-at-label
          v-if="localOneway.orX0158Oneway.itemLabel"
          :value="localOneway.orX0158Oneway.itemLabel"
          :font-weight="localOneway.orX0158Oneway.itemLabelFontWeight"
        />
        <!-- 項目名（空文字） -->
        <base-at-label v-else />
      </c-v-col>
      <c-v-col v-if="localOneway.orX0158Oneway.isRequired">
        <!-- 必須バッジ -->
        <base-mo00037 />
      </c-v-col>
      <slot />
    </c-v-row>
    <c-v-row
      no-gutters
      :class="localOneway.orX0158Oneway.customClass?.itemClass"
      :style="localOneway.orX0158Oneway.customClass?.itemStyle"
    >
      <c-v-col>
        <!-- セレクトボックス -->
        <div class="selectbox-container d-flex">
          <!-- 原子：セレクトボックス -->
          <base-at-select
            v-model="local.orX0158.modelValue"
            v-bind="{ ...$attrs, ...localOneway.orX0158Oneway }"
            :class="{
              'at-selectbox': true,
              'at-selectbox-full-radius': !localOneway.orX0158Oneway.showEditBtnFlg,
            }"
          >
            <template
              v-for="(slot, slotName) in $slots"
              #[slotName]="data"
            >
              <slot
                :name="slotName"
                v-bind="data"
              />
            </template>
            <template #prepend>
              <g-base-or-12002
                v-if="localOneway.orX0158Oneway.showEditBtnFlg"
                :readonly="localOneway.orX0158Oneway.readonly"
                :disabled="localOneway.orX0158Oneway.disabled"
                :class="['icon-edit-btn', localOneway.orX0158Oneway.editBtnClass]"
                @click="$emit('onClickEditBtn', $event)"
              >
              </g-base-or-12002>
            </template>
          </base-at-select>
        </div>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
</template>
<style scoped lang="scss">
$border-radius: 4px;
$icon-border-radius: 3px;
$icon-edit-btn-width: 36px;
$border-color: #cdcdcd;
$icon-color: #869fca;
// ドロップダウン（プルダウン）の外側コンテナのスタイル
.selectbox-container {
  border-radius: $border-radius;
  .at-selectbox {
    background: #fff;
    border-top-right-radius: $border-radius !important;
    border-end-end-radius: $border-radius !important;
  }
  // 入力補助ボタンがない場合の左側の角丸
  .at-selectbox-full-radius {
    border-top-left-radius: $border-radius !important;
    border-end-start-radius: $border-radius !important;
  }
  // 入力補助ボタンが存在する場合の左側境界線の角丸除去
  :deep(.v-field) {
    border-top-left-radius: 0px !important;
    border-end-start-radius: 0px !important;
  }
  :deep(.v-field) {
    padding-right: 0;
    border: 1px solid $border-color;
  }
  :deep(.v-field.v-field--focused) {
    border-color: rgb(var(--v-theme-key));
  }
  :deep(.v-field.v-field--focused.v-field--error) {
    border-color: rgb(var(--v-theme-error));
  }
}
// 入力補助ボタンのスタイル
.icon-edit-btn {
  border: 1px solid $border-color;
  background: rgb(var(--v-theme-black-100));
  color: $icon-color;
  width: $icon-edit-btn-width;
  height: 100%;
  border-top-left-radius: $icon-border-radius !important;
  border-end-start-radius: $icon-border-radius !important;
  border-top-right-radius: 0px !important;
  border-end-end-radius: 0px !important;
  border-right: none;
}
:deep(.v-field__outline__start) {
  border: none !important;
  opacity: unset !important;
}
:deep(.v-field__outline__end) {
  border: none !important;
  opacity: unset !important;
}
:deep(.v-input__prepend) {
  margin-inline-end: unset !important;
}
</style>
