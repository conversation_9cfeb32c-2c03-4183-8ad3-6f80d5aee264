<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { Or27943Const } from './Or27943.constants'
import type { Or27943StateType } from './Or27943.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import {
  computed,
  onMounted,
  reactive,
  ref,
  useScreenOneWayBind,
  useSetupChildProps,
  watch,
} from '#imports'
import type { Or27943Type, Or27943OnewayType } from '~/types/cmn/business/components/Or27943Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type {
  YokodasiServiceUnitReconfigureExecutionProcessUpdateInEntity,
  YokodasiServiceUnitReconfigureExecutionProcessUpdateOutEntity,
} from '~/repositories/cmn/entities/YokodasiServiceUnitReconfigureExecutionProcessUpdateEntity'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import type { Mo01352OnewayType } from '~/types/business/components/Mo01352Type'

/**
 * Or27943:横出しサービス単位再設定モーダル
 * GUI01171_横出しサービス単位再設定
 *
 * @description
 *［横出しサービス単位再設定］画面では、横出しサービス単位再設定画面を表示します。
 *
 * <AUTHOR> 劉顕康
 */

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or27943Type
  onewayModelValue: Or27943OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or27943OnewayType = {
  // システム年月
  systemDate: '',
  // 職員ID
  staffId: '',
  // システムコード
  systemCode: '',
  // 適用事業所IDリスト
  applicableOfficeIdList: [],
}

const localOneway = reactive({
  or27943: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo01352Oneway: {
    textFieldwidth: '115px',
    disabled: false,
    width: '150px',
  } as Mo01352OnewayType,
  // 実行ボタン
  mo00609OneWay: {
    btnLabel: t('btn.execution'),
    // disabled: !(await hasRegistAuth()),
    disabled: (await hasRegistAuth()),
  } as Mo00609OnewayType,
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
})

//［横出しサービス単位再設定］画面
const mo00024Oneway = reactive({
  width: '590px',
  height: '246px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or27943',
    toolbarTitle: t('label.horizontal-service-unit-reconfigure'),
    toolbarName: 'Or27943ToolBar',
    // ツールバータイトルの左寄せ
    toolbarTitleCenteredFlg: false,
    showToolbar: true,
    showCardActions: true,
    scrollable: false,
  },
}) as Mo00024OnewayType

const local = reactive({
  // 処理年月Start
  mo00020Startmodel: {
    value: '',
  } as Mo00020Type,
  // 処理年月End
  mo00020Endmodel: {
    value: '',
  } as Mo00020Type,
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27943Const.DEFAULT.IS_OPEN,
})

// Or21813
const or21813 = ref({ uniqueCpId: '' })
// Or21814
const or21814 = ref({ uniqueCpId: '' })
// Or21815
const or21815 = ref({ uniqueCpId: '' })

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27943StateType>({
  cpId: Or27943Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or27943Const.DEFAULT.IS_OPEN
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(1)]: or21813.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  // 親画面データを画面に設定
  // 対象開始年月
  local.mo00020Startmodel.value = localOneway.or27943.systemDate
  // 対象終了年月
  local.mo00020Endmodel.value = localOneway.or27943.systemDate
})

/**
 * エラーメッセージを表示する
 *
 * @param errorMsg - メッセージ内容
 */
function showOr21813Msg(errorMsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: errorMsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

// Or21813戻り値の確認
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.thirdBtnClickFlg) {
      return
    }
  }
)

/**
 * 確認メッセージを表示する
 *
 * @param errorMsg - メッセージ内容
 */
function showOr21814Msg(errorMsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogTitle: t('label.confirm'),
      dialogText: errorMsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

// Or21814戻り値の確認
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue.thirdBtnClickFlg) {
      return
    }
  }
)

/**
 * 実行処理
 */
async function doExecution() {
  const inputData: YokodasiServiceUnitReconfigureExecutionProcessUpdateInEntity = {
    // 事業所IDリスト
    svJigyoIdList: localOneway.or27943.applicableOfficeIdList,
    // 開始年月
    fromYm: local.mo00020Startmodel.value,
    // 終了年月
    toYm: local.mo00020Endmodel.value,
  }

  const ret: BaseResponseBody<YokodasiServiceUnitReconfigureExecutionProcessUpdateOutEntity> =
    await ScreenRepository.update('yokodasiServiceUnitReconfigureExecutionProcessUpdate', inputData)

  // 処理結果 <> 1 の場合
  if (ret.data?.result !== Or27943Const.PROCESS_RESULT_SUCCESS) {
    showOr21813Msg(t('message.e-cmn-40434'))
    return
  }

  // 処理結果 = 1 の場合
  showOr21814Msg(t('message.i-cmn-11407', [ret.data.riyouhyouUpdatecount, ret.data.simUpdatecount]))
}

/**
 * 警告メッセージを表示する
 *
 * @param errorMsg - メッセージ内容
 */
function showOr21815Msg(errorMsg: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      dialogTitle: t('label.warning'),
      dialogText: errorMsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

// Or21815戻り値の確認
watch(
  () => Or21815Logic.event.get(or21815.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.secondBtnClickFlg) {
      // 横出しサービス単位再設定実行処理を行う
      await doExecution()
      return
    }
    if (newValue.thirdBtnClickFlg) {
      return
    }
  }
)

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * 実行ボタンクリック
 */
function executionBtnClick() {
  // 対象開始年月 > 対象終了年月の場合
  if (local.mo00020Startmodel.value > local.mo00020Endmodel.value) {
    showOr21814Msg(t('message.i-cmn-10396'))
    return
  }

  // 対象開始年月 <= 対象終了年月の場合
  showOr21815Msg(t('message.w-cmn-20826'))
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row class="pl-2">
        <!-- 機能説明文ラベル -->
        <base-mo01338
          :oneway-model-value="{
            value: t('label.horizontal-service-unit-reset-feature-description'),
            customClass: { labelStyle: 'display: none;' },
          }"
        >
        </base-mo01338>
      </c-v-row>
      <c-v-row style="align-items: center">
        <!-- 対象年月ラベル -->
        <base-mo01338
          :oneway-model-value="{
            value: t('label.subject-year-and-month'),
            customClass: {
              outerClass: 'ml-2',
              labelStyle: 'display: none;',
            },
          }"
        >
        </base-mo01338>
        <!-- 対象開始年月 -->
        <base-mo01352
          v-model="local.mo00020Startmodel"
          style="align-items: center"
          :oneway-model-value="localOneway.mo01352Oneway"
        />
        <!-- ～ラベル -->
        <base-mo01338
          :oneway-model-value="{
            value: '~',
            customClass: {
              itemStyle: 'font-size: x-large;',
            },
          }"
          class="mr-2"
        >
        </base-mo01338>
        <!-- 対象終了年月 -->
        <base-mo01352
          v-model="local.mo00020Endmodel"
          style="align-items: center"
          :oneway-model-value="localOneway.mo01352Oneway"
        />
        <c-v-col cols="2"></c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row
        no-gutters
        style="padding-right: 8px; padding-bottom: -8px"
      >
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 実行ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          @click="executionBtnClick"
        >
          <!--ツールチップ表示："設定を確定します。"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.execution-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
    <!-- エラーダイアログを表示する。 -->
    <g-base-or21813
      v-if="showDialogOr21813"
      v-bind="or21813"
    ></g-base-or21813>
    <!-- 確認ダイアログを表示する。 -->
    <g-base-or21814
      v-if="showDialogOr21814"
      v-bind="or21814"
    ></g-base-or21814>
    <!-- ワーニングダイアログを表示する。 -->
    <g-base-or21815
      v-if="showDialogOr21815"
      v-bind="or21815"
    ></g-base-or21815>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
</style>
