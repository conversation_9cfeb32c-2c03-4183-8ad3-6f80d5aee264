<script setup lang="ts">
/**
 * Or03244:有機体:ページタイトル
 * ［アセスメント（包括）］画面 心理
 *
 * @description
 * ページタイトルを表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { cloneDeep } from 'lodash'
import { TeX0008Logic } from '../../template/TeX0008/TeX0008.logic'
import { Or53105Logic } from '../Or53105/Or53105.logic'
import type { CareLabelType, CareLocationLabelType, Or34069Type } from '../Or34069/Or34069.type'
import type {
  ConcreteCareItemType,
  ConcreteContentType,
  OrX00096OnewayType,
  OrX0096Type,
} from '../OrX0096/OrX0096.type'
import { Or34069Const } from '../Or34069/Or34069.constants'
import { OrX0096Const } from '../OrX0096/OrX0096.constants'
import { Or53105Const } from '../Or53105/Or53105.constants'
import type { TeX0008StateType } from '../../template/TeX0008/TeX0008.type'
import { Or59423Logic } from '../Or59423/Or59423.logic'
import { Or59423Const } from '../Or59423/Or59423.constants'
import { TeX0008Const } from '../../template/TeX0008/TeX0008.constants'
import { Or03244Const } from './Or03244.constants'
import { Or03244Logic } from './Or03244.logic'
import type { Or03244OnewayType } from './Or34244.type'
import type { TeX0008Type } from '~/types/cmn/business/components/TeX0008Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  AssessmentComprehensivePsychologyInitSelectInEntity,
  AssessmentComprehensivePsychologyInitSelectOutEntity,
  AssessmentComprehensivePsychologyUpdateInEntity,
  AssessmentComprehensivePsychologyUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentComprehensivePsychologyInitEntity'
import type { Or51105OnewayType, Or51105Type } from '~/types/cmn/business/components/Or51105Type'
import { useSetupChildProps, useScreenUtils, useScreenStore, useSystemCommonsStore } from '#imports'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { ResBodyStatusCode } from '~/constants/api-constants'
import type {
  assessmentComprehensiveQuestionInEntity,
  assessmentComprehensiveQuestionOutWebEntity,
} from '~/repositories/cmn/entities/assessmentComprehensiveQuestionSelect'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { Or34069OnewayType } from '~/types/cmn/business/components/Or34069Type'
const { t } = useI18n()
const { getChildCpBinds } = useScreenUtils()
/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  onewayModelValue: Or03244OnewayType
  parentUniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
/**
 * ロードフラグ
 */
const isLoading = ref(false)

/**
 * コンポネントのRef
 */
const componentRef = ref<HTMLDivElement | null>(null)

/**
 * 保存用テーブルデータ
 */
const tableData = ref<Or34069Type>({} as Or34069Type)

/**
 * 画面更新区分
 */
const screenUpdateKbn = ref(UPDATE_KBN.DELETE)

/**
 * 共通情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()

/**
 * データの更新回数
 */
let updateNum = ''

/**
 * API返却値の番号リストを一時保存する
 */
let dotNumberList: assessmentComprehensiveQuestionOutWebEntity['problemDotSolutionEtcInfo']['problemDotNumberInfoList'] =
  []

/**
 * 画面複写表示モード
 */
let screenFromDuplicate = false

/** デフォルトTwoway */
const local = reactive({
  tableHeader: {},
  commonInfo: {} as TeX0008Type,
  orX0096: {
    listSection: props.onewayModelValue.questionList ?? [],
  } as OrX0096Type,
  or51105: {
    kigoImiList: [],
  } as Or51105Type,
  or34069: {
    /** '7.心理・社会面等に関するケア' */
    title: t('label.psychology-care-7-title'),
    careItems: [
      {
        // 相談・助言
        title: t('label.consultation-heed-title-1'),
        showMode: '2',
        careLabel: [
          {
            label: t('label.consultation-label-1'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-2'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-3'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-4'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-5'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        // 書類作成
        title: t('label.consultation-heed-title-3'),
        showMode: '2',
        careLabel: [
          {
            label: t('label.consultation-label-6'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        // 問題行動への対応
        title: t('label.consultation-heed-title-4'),
        showMode: '0',
        careLabel: [
          {
            label: t('label.consultation-label-7'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-8'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-9'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-10'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        // 余暇活動
        title: t('label.consultation-heed-title-5'),
        showMode: '0',
        careLabel: [
          {
            label: t('label.consultation-label-11'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-12'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        // 環境整備
        title: t('label.consultation-heed-title-6'),
        showMode: '2',
        careLabel: [
          {
            label: t('label.consultation-label-13'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-14'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-15'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-16'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-17'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-18'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-19'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-20'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-21'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-22'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        // 買物
        title: t('label.consultation-heed-title-7'),
        showMode: '0',
        careLabel: [
          {
            label: t('label.consultation-label-23'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.consultation-label-24'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        // 金銭管理
        title: t('label.consultation-heed-title-8'),
        showMode: '0',
        careLabel: [
          {
            label: t('label.consultation-label-25'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        // 移送
        title: t('label.consultation-heed-title-9'),
        showMode: '0',
        careLabel: [
          {
            label: t('label.consultation-label-26'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
    ],
    careLocationItems: [
      {
        // コミュニケーション等用具
        title: t('label.consultation-heed-title-10'),
        showMode: '0',
        careLocationLabel: [
          {
            locationValue: { modelValue: '' },
            label: t('label.consultation-label-27'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.consultation-label-28'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.consultation-label-29'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.consultation-label-30'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.consultation-label-31'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.consultation-label-32'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.consultation-label-33'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.consultation-label-34'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '32',
            },
          },
        ],
      },
    ],
  } as Or34069Type,
})

/** デフォルトOneway */

/** One-way */
const localOneway = reactive({
  or34069Oneway: {
    showTableBodyFlg: true,
  } as Or34069OnewayType,
  orX0096Oneway: {
    showInputFlg: false,
    tableDisplayFlg: true,
    b1Cd: Or03244Const.DEFAULT.TAB_ID,
  } as OrX00096OnewayType,
  or51105Oneway: {
    sc1Id: '',
    cc1Id: '',
  } as Or51105OnewayType,
})

const or34069 = ref({ uniqueCpId: '', showTableBodyFlg: true })
const or53105 = ref({ uniqueCpId: '' })
const orX0096 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or21814 = ref({ uniqueCpId: '' })

/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or34069Const.CP_ID(6)]: or34069.value,
  [Or53105Const.CP_ID(0)]: or53105.value,
  [OrX0096Const.CP_ID(1)]: orX0096.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
})

/**************************************************
 * 算出プロパティ
 **************************************************/
/**
 * 記号選択画面ダイアログ表示フラグ
 */
const showDialogOr53105CksFlg = computed(() => {
  // Or53105 cks_flg=1 のダイアログ開閉状態
  return Or53105Logic.state.get(or53105.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 確認ダイアログ表示フラグ
 */
const showOr21814DialogFlg = computed(() => {
  // ダイアログの開閉フラグ
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 画面データ変更フラグ
 */
const _isEdit = computed(() => {
  const isEditByUniqueCpIds = useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
  return isEditByUniqueCpIds
})
/**************************************************
 * 関数
 **************************************************/
/**
 * コントロール初期化
 */
const initControl = () => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      firstBtnLabel: t('btn.yes'),
      firstBtnType: 'normal1',
      secondBtnLabel: t('btn.no'),
      secondBtnType: 'destroy1',
      thirdBtnLabel: t('btn.close'),
      thirdBtnType: 'normal3',
      dialogTitle: t('label.confirm'),
    },
  })
  if (props.onewayModelValue.screenMode === Or03244Const.DEFAULT.SCREEN_DIAPLAY_MODE_COPY) {
    if (componentRef.value) {
      disableTab(componentRef.value)
    }
  }
}

/**
 * tabを制御
 *
 * @param component - コンポーネント
 */
const disableTab = (component: HTMLElement) => {
  const focusableSelectors = [
    'a[href]',
    'area[href]',
    'input',
    'select',
    'textarea',
    'button',
    'iframe',
    '[tabindex]',
    '[contenteditable]',
  ]

  const focusables = component.querySelectorAll(focusableSelectors.join(','))
  focusables.forEach((el) => {
    el.setAttribute('tabindex', '-1')
  })
}

/**
 * 共通情報取得
 */
function getCommonInfo() {
  const commonInfo = TeX0008Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo = commonInfo
    screenUpdateKbn.value = commonInfo.updateKbn ?? ''
    // 更新区分クリア
    if (commonInfo.updateKbn === UPDATE_KBN.DELETE) {
      localOneway.orX0096Oneway.tableDisplayFlg = false
    } else {
      localOneway.orX0096Oneway.tableDisplayFlg = true
    }
  }
}

/**
 * 複写モード共通情報取得
 */
const getDuplicateCommonInfo = () => {
  const commonInfo = Or59423Logic.data.get(props.parentUniqueCpId + Or59423Const.CP_ID(0))
  if (commonInfo) {
    local.commonInfo = {
      ninteiFormF: commonInfo.duplicateInfo?.ninteiFormF,
      activeTabId: commonInfo.duplicateInfo?.activeTabId,
      jigyoId: commonInfo.duplicateInfo?.jigyoId,
      houjinId: commonInfo.duplicateInfo?.houjinId,
      shisetuId: commonInfo.duplicateInfo?.shisetuId,
      userId: commonInfo.duplicateInfo?.userId,
      syubetsuId: commonInfo.duplicateInfo?.syubetsuId,
      createYmd: commonInfo.duplicateInfo?.createYmd,
      historyUpdateKbn: commonInfo.duplicateInfo?.historyUpdateKbn,
      historyModifiedCnt: commonInfo.duplicateInfo?.historyModifiedCnt,
      sc1Id: commonInfo.duplicateInfo?.sc1Id,
      recId: commonInfo.duplicateInfo?.recId,
      cc1Id: commonInfo.duplicateInfo?.cc1Id,
      createUserId: commonInfo.duplicateInfo?.createUserId,
      svJigyoId: commonInfo.duplicateInfo?.svJigyoId,
      listSection: commonInfo.duplicateInfo?.listSection,
      modifiedCnt: commonInfo.duplicateInfo?.modifiedCnt,
      updateKbn: commonInfo.duplicateInfo?.updateKbn,
      thingsToKeepInMindContentsInfo: commonInfo.duplicateInfo?.thingsToKeepInMindContentsInfo,
      planPeriodFlg: commonInfo.duplicateInfo?.planPeriodFlg,
    }
  }
}

/**
 *  コントロール初期化
 */
async function reload() {
  // 全表示フラグをtrueにする
  localOneway.or34069Oneway.showTableBodyFlg = true
  localOneway.orX0096Oneway.tableDisplayFlg = true
  // 更新区分クリア
  screenUpdateKbn.value = UPDATE_KBN.NONE
  isLoading.value = true
  // 画面初期情報取得
  await getInitDataInfo()
  // 問題点初期情報取得
  await getProblemDotSolutionEtcInfoData()
  isLoading.value = false
}

/**
 * 親画面のstateを変更する
 *
 * @param state - state
 */
const setTeX0008State = (state: TeX0008StateType) => {
  TeX0008Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

/**
 * 複写情報取得
 */
const getDuplicateDataInfo = async () => {
  const inputData: AssessmentComprehensivePsychologyInitSelectInEntity = {
    cc1Id: local.commonInfo.duplicateCareCheckId ?? '',
    sc1Id: local.commonInfo.duplicatePlanId ?? '',
  }
  // 初期情報取得APIを呼び出す
  const resData: AssessmentComprehensivePsychologyInitSelectOutEntity =
    await ScreenRepository.select('assessmentComprehensivePsychologyInitSelect', inputData)
  // 返却値チェック
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    processInfoData(resData, false)
  }
}

/**
 * 複写情報取得「問題点情報」
 */
const getDuplicateProblemDotSolutionEtcInfoData = async () => {
  try {
    const inputData: assessmentComprehensiveQuestionInEntity = {
      cc1Id: local.commonInfo?.duplicateCareCheckId ?? '',
      sc1Id: local.commonInfo?.duplicatePlanId ?? '',
      // タブID：「7：心理」
      typeId: Or03244Const.DEFAULT.TAB_ID,
    }

    const resData: BaseResponseBody<assessmentComprehensiveQuestionOutWebEntity> =
      await ScreenRepository.select('problemDotSolutionEtcInfoSelect', inputData)
    if (resData.data) {
      processDuplicateData(resData.data.problemDotSolutionEtcInfo)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 *  画面初期情報取得
 */
async function getInitDataInfo() {
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentComprehensivePsychologyInitSelectInEntity = {
    /** ケアチェックID */
    cc1Id: local.commonInfo.cc1Id ?? '',
    /** 計画期間ID */
    sc1Id: local.commonInfo.sc1Id ?? '',
  }

  const resData: AssessmentComprehensivePsychologyInitSelectOutEntity =
    await ScreenRepository.select('assessmentComprehensivePsychologyInitSelect', inputData)
  // 画面情報を設定
  if (resData) {
    // API返却値処理
    processInfoData(resData, true)
    // 更新回数の取得
    updateNum = resData.data.subInfoPsychology.modifiedCnt ?? ''
  }
}

/**
 * API返却値処理
 *
 * @param resData - API返却値
 *
 * @param updateFlg - 更新回数フラグ
 */
const processInfoData = (
  resData: AssessmentComprehensivePsychologyInitSelectOutEntity,
  updateFlg: boolean
) => {
  if (!resData.data) return
  // 複写モードの場合、返却値を保存する
  if (props.onewayModelValue.screenMode === TeX0008Const.DEFAULT.MODE_COPY) {
    Or03244Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        comprehensiveQuestionInfo: props.onewayModelValue.comprehensiveQuestionInfo,
        result: resData,
      },
    })
  }

  // セレクト選択肢設定
  const { careOfferLocationInfoList, scheduleInfoList, familyInfoList, offerInfoList } =
    resData.data.markInfo
  localOneway.or34069Oneway.careOfferMarkMeaning = offerInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.careFamilyMarkMeaning = familyInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.carePlanMarkMeaning = scheduleInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.careLocationMarkMeaning = careOfferLocationInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })

  // 複数回の更新による無限再帰を防ぐために、データをディープコピーし、以下の処理は別データに移管する
  tableData.value = cloneDeep(local.or34069)
  const {
    cn011,
    cn012,
    cn013,
    cn021,
    cn022,
    cn023,
    cn031,
    cn032,
    cn033,
    cn041,
    cn042,
    cn043,
    cn051,
    cn052,
    cn053,
    cn061,
    cn062,
    cn063,
    cn071,
    cn072,
    cn073,
    cn081,
    cn082,
    cn083,
    cn091,
    cn092,
    cn093,
    cn101,
    cn102,
    cn103,
    cn111,
    cn112,
    cn113,
    cn121,
    cn122,
    cn123,
    cn131,
    cn132,
    cn133,
    cn141,
    cn142,
    cn143,
    cn151,
    cn152,
    cn153,
    cn161,
    cn162,
    cn163,
    cn171,
    cn172,
    cn173,
    cn181,
    cn182,
    cn183,
    cn191,
    cn192,
    cn193,
    cn201,
    cn202,
    cn203,
    cn211,
    cn212,
    cn213,
    cn221,
    cn222,
    cn223,
    cn231,
    cn232,
    cn233,
    cn241,
    cn242,
    cn243,
    cn251,
    cn252,
    cn253,
    cn261,
    cn262,
    cn263,
    cb011,
    cb021,
    cb031,
    cb041,
    cb051,
    cb061,
    cb071,
    cb072Knj,
    cb081,
    cb082Knj,
    cb091,
    cb092Knj,
    cb101,
    cb102Knj,
    cb111,
    cb112Knj,
    cb121,
    cb122Knj,
    cb131,
    cb132Knj,
    cb141,
    cb142Knj,
    cb151,
    cb152Knj,
    cb161,
    cb162Knj,
    cb171,
    cb172Knj,
    cb181,
    cb182Knj,
    cb191,
    cb192Knj,
    cb201,
    cb202Knj,
    cb211,
    cb212Knj,
    cb221,
    cb222Knj,
    cb231,
    cb232Knj,
    cb241,
    cb242Knj,
    cb251,
    cb252Knj,
    cb261,
    cb262Knj,
    modifiedCnt,
  } = resData.data.subInfoPsychology
  // 相談・助言の内容
  initTableData([cn011, cn012, cn013], tableData.value.careItems[0].careLabel[0])
  initTableData([cn021, cn022, cn023], tableData.value.careItems[0].careLabel[1])
  initTableData([cn031, cn032, cn033], tableData.value.careItems[0].careLabel[2])
  initTableData([cn041, cn042, cn043], tableData.value.careItems[0].careLabel[3])
  initTableData([cn051, cn052, cn053], tableData.value.careItems[0].careLabel[4])
  // 書類作成の内容
  initTableData([cn061, cn062, cn063], tableData.value.careItems[1].careLabel[0])
  // 問題行動への対応の内容
  initTableData([cn071, cn072, cn073], tableData.value.careItems[2].careLabel[0])
  initTableData([cn081, cn082, cn083], tableData.value.careItems[2].careLabel[1])
  initTableData([cn091, cn092, cn093], tableData.value.careItems[2].careLabel[2])
  initTableData([cn101, cn102, cn103], tableData.value.careItems[2].careLabel[3])
  // 余暇活動の内容
  initTableData([cn111, cn112, cn113], tableData.value.careItems[3].careLabel[0])
  initTableData([cn121, cn122, cn123], tableData.value.careItems[3].careLabel[1])
  // 環境整備の内容
  initTableData([cn131, cn132, cn133], tableData.value.careItems[4].careLabel[0])
  initTableData([cn141, cn142, cn143], tableData.value.careItems[4].careLabel[1])
  initTableData([cn151, cn152, cn153], tableData.value.careItems[4].careLabel[2])
  initTableData([cn161, cn162, cn163], tableData.value.careItems[4].careLabel[3])
  initTableData([cn171, cn172, cn173], tableData.value.careItems[4].careLabel[4])
  initTableData([cn181, cn182, cn183], tableData.value.careItems[4].careLabel[5])
  initTableData([cn191, cn192, cn193], tableData.value.careItems[4].careLabel[6])
  initTableData([cn201, cn202, cn203], tableData.value.careItems[4].careLabel[7])
  initTableData([cn211, cn212, cn213], tableData.value.careItems[4].careLabel[8])
  initTableData([cn221, cn222, cn223], tableData.value.careItems[4].careLabel[9])
  // 買物の内容
  initTableData([cn231, cn232, cn233], tableData.value.careItems[5].careLabel[0])
  initTableData([cn241, cn242, cn243], tableData.value.careItems[5].careLabel[1])
  // 金銭管理の内容
  initTableData([cn251, cn252, cn253], tableData.value.careItems[6].careLabel[0])
  // 移送の内容
  initTableData([cn261, cn262, cn263], tableData.value.careItems[7].careLabel[0])

  // コミュニケーション等用具の内容
  initTableData([cb011], tableData.value.careLocationItems[0].careLocationLabel[0])
  initTableData([cb021], tableData.value.careLocationItems[0].careLocationLabel[1])
  initTableData([cb031], tableData.value.careLocationItems[0].careLocationLabel[2])
  initTableData([cb041], tableData.value.careLocationItems[0].careLocationLabel[3])
  initTableData([cb051], tableData.value.careLocationItems[0].careLocationLabel[4])
  initTableData([cb061], tableData.value.careLocationItems[0].careLocationLabel[5])
  initTableData([cb071, cb072Knj], tableData.value.careLocationItems[0].careLocationLabel[6])
  initTableData([cb081, cb082Knj], tableData.value.careLocationItems[0].careLocationLabel[7])
  initTableData([cb091, cb092Knj], tableData.value.careLocationItems[0].careLocationLabel[8])
  initTableData([cb101, cb102Knj], tableData.value.careLocationItems[0].careLocationLabel[9])
  initTableData([cb111, cb112Knj], tableData.value.careLocationItems[0].careLocationLabel[10])
  initTableData([cb121, cb122Knj], tableData.value.careLocationItems[0].careLocationLabel[11])
  initTableData([cb131, cb132Knj], tableData.value.careLocationItems[0].careLocationLabel[12])
  initTableData([cb141, cb142Knj], tableData.value.careLocationItems[0].careLocationLabel[13])
  initTableData([cb151, cb152Knj], tableData.value.careLocationItems[0].careLocationLabel[14])
  initTableData([cb161, cb162Knj], tableData.value.careLocationItems[0].careLocationLabel[15])
  initTableData([cb171, cb172Knj], tableData.value.careLocationItems[0].careLocationLabel[16])
  initTableData([cb181, cb182Knj], tableData.value.careLocationItems[0].careLocationLabel[17])
  initTableData([cb191, cb192Knj], tableData.value.careLocationItems[0].careLocationLabel[18])
  initTableData([cb201, cb202Knj], tableData.value.careLocationItems[0].careLocationLabel[19])
  initTableData([cb211, cb212Knj], tableData.value.careLocationItems[0].careLocationLabel[20])
  initTableData([cb221, cb222Knj], tableData.value.careLocationItems[0].careLocationLabel[21])
  initTableData([cb231, cb232Knj], tableData.value.careLocationItems[0].careLocationLabel[22])
  initTableData([cb241, cb242Knj], tableData.value.careLocationItems[0].careLocationLabel[23])
  initTableData([cb251, cb252Knj], tableData.value.careLocationItems[0].careLocationLabel[24])
  initTableData([cb261, cb262Knj], tableData.value.careLocationItems[0].careLocationLabel[25])

  // 処理済みのデータを画面に表示する
  local.or34069 = tableData.value
  // 複写再発火の場合は、サブ情報の更新回数は上書きしない
  if (updateFlg) {
    updateNum = modifiedCnt ?? ''
  } else {
    // 複写発火の場合は、画面の更新区分を `U` にする
    screenUpdateKbn.value = UPDATE_KBN.UPDATE
  }
}

/**
 * 初期情報データより、テーブルデータを設定する
 *
 * @param tableData - 初期化APIから取得したデータ
 *
 * @param items - テーブルデータ
 */
function initTableData<Label extends CareLabelType | CareLocationLabelType>(
  tableData: (string | undefined)[],
  items: Label
) {
  const getDefaultValue = (index: number, defaultValue = '') => tableData[index] ?? defaultValue
  if (tableData.length === Or03244Const.DEFAULT.TABLE_INIT_DATA_LIST_TYPE_CARE_CONTENT) {
    if ('offerValue' in items && 'familyValue' in items && 'planValue' in items) {
      items.offerValue = { modelValue: getDefaultValue(0) }
      items.familyValue = { modelValue: getDefaultValue(1) }
      items.planValue = { modelValue: getDefaultValue(2) }
    }
  } else if (
    tableData.length === Or03244Const.DEFAULT.TABLE_INIT_DATA_LIST_TYPE_CARE_LOCATION_NO_INPUT
  ) {
    if ('locationValue' in items) {
      items.locationValue = { modelValue: getDefaultValue(0) }
    }
  } else if (tableData.length === Or03244Const.DEFAULT.TABLE_INIT_DATA_LIST_TYPE_CARE_LOCATION) {
    if ('locationValue' in items && 'inputContent' in items) {
      items.locationValue = { modelValue: getDefaultValue(0) }
      items.inputContent = { value: getDefaultValue(1) }
    }
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */

const setShowDialog = async (paramDialogText: string): Promise<'yes' | 'no'> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        let result: 'yes' | 'no' = 'yes'
        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 保存処理
 */
const _userSave = async () => {
  // 保存前のチェック
  if (!_isEdit.value && screenUpdateKbn.value === UPDATE_KBN.NONE && !screenFromDuplicate) {
    const result = await setShowDialog(t('message.i-cmn-10430'))
    if (result === 'no') return
  }
  // 共通情報.e-文書法対象機能の電子ファイル保存設定区分が「true：適用する」の場合 Todo
  if (!systemCommonsStore) return
  try {
    isLoading.value = true
    // 子コンポーネントからデータを一括取得する
    const childrenTableCpBinds = getChildCpBinds(props.uniqueCpId, {
      [Or34069Const.CP_ID(6)]: { cpPath: Or34069Const.CP_ID(6), twoWayFlg: true },
      [OrX0096Const.CP_ID(1)]: { cpPath: OrX0096Const.CP_ID(1), twoWayFlg: true },
    })
    // ケアの内容を取得する
    const careItems = (childrenTableCpBinds[Or34069Const.CP_ID(6)].twoWayBind?.value as Or34069Type)
      .careItems
    // ケアの提供場所を取得する
    const careLocationItems = (
      childrenTableCpBinds[Or34069Const.CP_ID(6)].twoWayBind?.value as Or34069Type
    ).careLocationItems
    // 要介護者などの健康上や生活上の問題点及び解決すべき課題等を取得する
    const careRecipientHealthAndLifeIssues = childrenTableCpBinds[OrX0096Const.CP_ID(1)].twoWayBind
      ?.value as OrX0096Type
    const inputData: AssessmentComprehensivePsychologyUpdateInEntity = {
      cc1Id: createRequest(local.commonInfo.cc1Id),
      sc1Id: local.commonInfo.sc1Id ?? '0',
      houjinId: local.commonInfo.houjinId,
      shisetuId: local.commonInfo.shisetuId ?? '',
      svJigyoId: local.commonInfo.svJigyoId ?? '',
      userId: local.commonInfo.userId ?? '',
      syubetsuId: local.commonInfo.syubetsuId ?? '2',
      createYmd: local.commonInfo.createYmd ?? '',
      updateKbn: getRequestScreenUpdataKbn(false),
      historyUpdateKbn: getRequestScreenUpdataKbn(true),
      historyModifiedCnt: createRequest(local.commonInfo.historyModifiedCnt),
      shokuId: '1',
      typeId: Or03244Const.DEFAULT.TAB_ID,
    }
    inputData.cc1Id = createRequest(local.commonInfo.cc1Id)
    inputData.historyModifiedCnt = createRequest(local.commonInfo.historyModifiedCnt)
    const subInfoPsychology: AssessmentComprehensivePsychologyUpdateInEntity['subInfoPsychology'] =
      {
        // 相談・助言
        cn011: careItems[0].careLabel[0].offerValue.modelValue,
        cn012: careItems[0].careLabel[0].familyValue.modelValue,
        cn013: careItems[0].careLabel[0].planValue.modelValue,
        cn021: careItems[0].careLabel[1].offerValue.modelValue,
        cn022: careItems[0].careLabel[1].familyValue.modelValue,
        cn023: careItems[0].careLabel[1].planValue.modelValue,
        cn031: careItems[0].careLabel[2].offerValue.modelValue,
        cn032: careItems[0].careLabel[2].familyValue.modelValue,
        cn033: careItems[0].careLabel[2].planValue.modelValue,
        cn041: careItems[0].careLabel[3].offerValue.modelValue,
        cn042: careItems[0].careLabel[3].familyValue.modelValue,
        cn043: careItems[0].careLabel[3].planValue.modelValue,
        cn051: careItems[0].careLabel[4].offerValue.modelValue,
        cn052: careItems[0].careLabel[4].familyValue.modelValue,
        cn053: careItems[0].careLabel[4].planValue.modelValue,
        // 書類作成
        cn061: careItems[1].careLabel[0].offerValue.modelValue,
        cn062: careItems[1].careLabel[0].familyValue.modelValue,
        cn063: careItems[1].careLabel[0].planValue.modelValue,
        // 問題行動への対応
        cn071: careItems[2].careLabel[0].offerValue.modelValue,
        cn072: careItems[2].careLabel[0].familyValue.modelValue,
        cn073: careItems[2].careLabel[0].planValue.modelValue,
        cn081: careItems[2].careLabel[1].offerValue.modelValue,
        cn082: careItems[2].careLabel[1].familyValue.modelValue,
        cn083: careItems[2].careLabel[1].planValue.modelValue,
        cn091: careItems[2].careLabel[2].offerValue.modelValue,
        cn092: careItems[2].careLabel[2].familyValue.modelValue,
        cn093: careItems[2].careLabel[2].planValue.modelValue,
        cn101: careItems[2].careLabel[3].offerValue.modelValue,
        cn102: careItems[2].careLabel[3].familyValue.modelValue,
        cn103: careItems[2].careLabel[3].planValue.modelValue,
        // 余暇活動
        cn111: careItems[4].careLabel[0].offerValue.modelValue,
        cn112: careItems[4].careLabel[0].familyValue.modelValue,
        cn113: careItems[4].careLabel[0].planValue.modelValue,
        cn121: careItems[4].careLabel[1].offerValue.modelValue,
        cn122: careItems[4].careLabel[1].familyValue.modelValue,
        cn123: careItems[4].careLabel[1].planValue.modelValue,
        // 環境整備
        cn131: careItems[4].careLabel[0].offerValue.modelValue,
        cn132: careItems[4].careLabel[0].familyValue.modelValue,
        cn133: careItems[4].careLabel[0].planValue.modelValue,
        cn141: careItems[4].careLabel[1].offerValue.modelValue,
        cn142: careItems[4].careLabel[1].familyValue.modelValue,
        cn143: careItems[4].careLabel[1].planValue.modelValue,
        cn151: careItems[4].careLabel[2].offerValue.modelValue,
        cn152: careItems[4].careLabel[2].familyValue.modelValue,
        cn153: careItems[4].careLabel[2].planValue.modelValue,
        cn161: careItems[4].careLabel[3].offerValue.modelValue,
        cn162: careItems[4].careLabel[3].familyValue.modelValue,
        cn163: careItems[4].careLabel[3].planValue.modelValue,
        cn171: careItems[4].careLabel[4].offerValue.modelValue,
        cn172: careItems[4].careLabel[4].familyValue.modelValue,
        cn173: careItems[4].careLabel[4].planValue.modelValue,
        cn181: careItems[4].careLabel[5].offerValue.modelValue,
        cn182: careItems[4].careLabel[5].familyValue.modelValue,
        cn183: careItems[4].careLabel[5].planValue.modelValue,
        cn191: careItems[4].careLabel[6].offerValue.modelValue,
        cn192: careItems[4].careLabel[6].familyValue.modelValue,
        cn193: careItems[4].careLabel[6].planValue.modelValue,
        cn201: careItems[4].careLabel[7].offerValue.modelValue,
        cn202: careItems[4].careLabel[7].familyValue.modelValue,
        cn203: careItems[4].careLabel[7].planValue.modelValue,
        cn211: careItems[4].careLabel[8].offerValue.modelValue,
        cn212: careItems[4].careLabel[8].familyValue.modelValue,
        cn213: careItems[4].careLabel[8].planValue.modelValue,
        cn221: careItems[4].careLabel[9].offerValue.modelValue,
        cn222: careItems[4].careLabel[9].familyValue.modelValue,
        cn223: careItems[4].careLabel[9].planValue.modelValue,
        // 買物
        cn231: careItems[5].careLabel[0].offerValue.modelValue,
        cn232: careItems[5].careLabel[0].familyValue.modelValue,
        cn233: careItems[5].careLabel[0].planValue.modelValue,
        cn241: careItems[5].careLabel[1].offerValue.modelValue,
        cn242: careItems[5].careLabel[1].familyValue.modelValue,
        cn243: careItems[5].careLabel[1].planValue.modelValue,
        // 金銭管理
        cn251: careItems[6].careLabel[0].offerValue.modelValue,
        cn252: careItems[6].careLabel[0].familyValue.modelValue,
        cn253: careItems[6].careLabel[0].planValue.modelValue,
        // 移送
        cn261: careItems[7].careLabel[0].offerValue.modelValue,
        cn262: careItems[7].careLabel[0].familyValue.modelValue,
        cn263: careItems[7].careLabel[0].planValue.modelValue,

        // コミュニケーション等用具
        cb011: careLocationItems[0].careLocationLabel[0].locationValue.modelValue,
        cb021: careLocationItems[0].careLocationLabel[1].locationValue.modelValue,
        cb031: careLocationItems[0].careLocationLabel[2].locationValue.modelValue,
        cb041: careLocationItems[0].careLocationLabel[3].locationValue.modelValue,
        cb051: careLocationItems[0].careLocationLabel[4].locationValue.modelValue,
        cb061: careLocationItems[0].careLocationLabel[5].locationValue.modelValue,
        cb071: careLocationItems[0].careLocationLabel[6].locationValue.modelValue,
        cb072Knj: careLocationItems[0].careLocationLabel[6].inputContent?.value,
        cb081: careLocationItems[0].careLocationLabel[7].locationValue.modelValue,
        cb082Knj: careLocationItems[0].careLocationLabel[7].inputContent?.value,
        cb091: careLocationItems[0].careLocationLabel[8].locationValue.modelValue,
        cb092Knj: careLocationItems[0].careLocationLabel[8].inputContent?.value,
        cb101: careLocationItems[0].careLocationLabel[9].locationValue.modelValue,
        cb102Knj: careLocationItems[0].careLocationLabel[9].inputContent?.value,
        cb111: careLocationItems[0].careLocationLabel[10].locationValue.modelValue,
        cb112Knj: careLocationItems[0].careLocationLabel[10].inputContent?.value,
        cb121: careLocationItems[0].careLocationLabel[11].locationValue.modelValue,
        cb122Knj: careLocationItems[0].careLocationLabel[11].inputContent?.value,
        cb131: careLocationItems[0].careLocationLabel[12].locationValue.modelValue,
        cb132Knj: careLocationItems[0].careLocationLabel[12].inputContent?.value,
        cb141: careLocationItems[0].careLocationLabel[13].locationValue.modelValue,
        cb142Knj: careLocationItems[0].careLocationLabel[13].inputContent?.value,
        cb151: careLocationItems[0].careLocationLabel[14].locationValue.modelValue,
        cb152Knj: careLocationItems[0].careLocationLabel[14].inputContent?.value,
        cb161: careLocationItems[0].careLocationLabel[15].locationValue.modelValue,
        cb162Knj: careLocationItems[0].careLocationLabel[15].inputContent?.value,
        cb171: careLocationItems[0].careLocationLabel[16].locationValue.modelValue,
        cb172Knj: careLocationItems[0].careLocationLabel[16].inputContent?.value,
        cb181: careLocationItems[0].careLocationLabel[17].locationValue.modelValue,
        cb182Knj: careLocationItems[0].careLocationLabel[17].inputContent?.value,
        cb191: careLocationItems[0].careLocationLabel[18].locationValue.modelValue,
        cb192Knj: careLocationItems[0].careLocationLabel[18].inputContent?.value,
        cb201: careLocationItems[0].careLocationLabel[19].locationValue.modelValue,
        cb202Knj: careLocationItems[0].careLocationLabel[19].inputContent?.value,
        cb211: careLocationItems[0].careLocationLabel[20].locationValue.modelValue,
        cb212Knj: careLocationItems[0].careLocationLabel[20].inputContent?.value,
        cb221: careLocationItems[0].careLocationLabel[21].locationValue.modelValue,
        cb222Knj: careLocationItems[0].careLocationLabel[21].inputContent?.value,
        cb231: careLocationItems[0].careLocationLabel[22].locationValue.modelValue,
        cb232Knj: careLocationItems[0].careLocationLabel[22].inputContent?.value,
        cb241: careLocationItems[0].careLocationLabel[23].locationValue.modelValue,
        cb242Knj: careLocationItems[0].careLocationLabel[23].inputContent?.value,
        cb251: careLocationItems[0].careLocationLabel[24].locationValue.modelValue,
        cb252Knj: careLocationItems[0].careLocationLabel[24].inputContent?.value,
        cb261: careLocationItems[0].careLocationLabel[25].locationValue.modelValue,
        cb262Knj: careLocationItems[0].careLocationLabel[25].inputContent?.value,

        modifiedCnt: updateNum === '' ? '0' : updateNum,
      }
    inputData.subInfoPsychology = subInfoPsychology

    // 問題点や解決すべき課題等々情報保存前処理
    const problemInfoList = processProblemDotLsit(careRecipientHealthAndLifeIssues)

    // 問題点番号リスト
    if (screenUpdateKbn.value !== UPDATE_KBN.CREATE) {
      inputData.problemDotNumberInfoList = dotNumberList
    } else {
      inputData.problemDotNumberInfoList = undefined
    }
    // 具体的内容リスト
    inputData.concreteContentsInfoList = problemInfoList.concreteContentsInfoList
    // 対応するケア項目リスト
    inputData.careItemList = problemInfoList.careItemList
    // 問題点解決情報リスト
    inputData.problemDotSolutionInfoList = problemInfoList.issuseList

    const resData: AssessmentComprehensivePsychologyUpdateOutEntity = await ScreenRepository.update(
      'assessmentComprehensivePsychologyUpdate',
      inputData
    )
    if (resData.data) {
      // 保存処理返却値を親画面Piniaに設定する
      TeX0008Logic.data.set({
        uniqueCpId: props.parentUniqueCpId,
        value: {
          ...local.commonInfo,
          childrenScreenApiResult: {
            sc1Id: resData.data.sc1Id,
            cc1Id: resData.data.cc1Id,
          },
        },
      })
      setTeX0008State({ saveCompletedState: true })
      // 更新区分クリア
      screenUpdateKbn.value = UPDATE_KBN.NONE
      screenFromDuplicate = false
    }
  } finally {
    isLoading.value = false
  }
}

/**
 * 課題データ処理
 *
 * @param data -課題データ
 */
function processIssuesData(
  data: assessmentComprehensiveQuestionOutWebEntity['problemDotSolutionEtcInfo']
) {
  localOneway.orX0096Oneway.officeId = local.commonInfo.svJigyoId ?? ''
  // 本画面のデータを絞り出す
  // 対応するケア項目リスト
  const concreteCareItemList: ConcreteCareItemType[] = data.careItemList
    .filter((item) => item.b1Cd === Or03244Const.DEFAULT.TAB_ID)
    .map((item) => {
      return {
        key: item.cc33Id,
        content: item.memoKnj,
        dmyCc32Id: item.dmyCc32Id,
        modifiedCnt: item.modifiedCnt,
        cc33Id: item.cc33Id,
        b1Cd: item.b1Cd,
        cc32Id: item.cc32Id,
        seq: item.seq,
        ci2Id: item.ci2Id,
        updateKbn: '',
      }
    })
  local.orX0096.concreteCareItemList = concreteCareItemList
  // 具体的内容
  const concreteContentList: ConcreteContentType[] = data.concreteContentsInfoList
    .filter((item) => item.b1Cd === Or03244Const.DEFAULT.TAB_ID)
    .map((item): ConcreteContentType => {
      return {
        key: item.cc32Id,
        correspondenceKeys: [],
        content: item.memoKnj,
        cc32Id: item.cc32Id,
        modifiedCnt: item.modifiedCnt,
        juni: item.juni,
        b1Cd: item.b1Cd,
        seq: item.seq,
        cc32Type: item.cc32Type,
        ci1Id: item.ci1Id,
        dmyB4Cd: item.dmyB4Cd,
        b4Cd: item.b4Cd,
        number: '',
        updateKbn: '',
      }
    })
  // 番号リストを作成
  const numberList = data.problemDotNumberInfoList
    .filter((item) => item.b1Cd === Or03244Const.DEFAULT.TAB_ID)
    .map((item) => {
      return {
        cc32Id: item.cc32Id,
        b4Cd: item.b4Cd,
        seq: item.seq,
        modifiedCnt: item.modifiedCnt1,
        cc34Id: item.cc34Id,
        b1Cd: item.b1Cd,
        modifiedCnt1: item.modifiedCnt1,
      }
    })
  if (concreteContentList.length > 0) {
    concreteContentList.forEach((item) => {
      const findedItem = numberList.filter((sItem) => sItem.cc32Id === item.cc32Id)
      if (findedItem) {
        item.correspondenceKeys = findedItem
        item.number = findedItem.map((item) => setCircleNumber(parseInt(item.b4Cd) - 75)).join('')
      }
    })
  }

  local.orX0096.concreteContentList = concreteContentList
  /**
   * 問題点リスト
   */
  const questionList = data.problemDotSolutionInfoList.filter(
    (item) => item.b1Cd === Or03244Const.DEFAULT.TAB_ID
  )
  local.orX0096.listSection = local.orX0096.listSection.map((item) => {
    const findedItem = questionList.find((sItem) => sItem.b4Cd === item.b4cd)
    if (findedItem) {
      return {
        ...item,
        cc31Id: findedItem?.cc31Id ?? '',
        isPlanningFlg: findedItem.f1 === Or03244Const.DEFAULT.API_RESULT_CHECKON,
        isHaveIssuesFlg: findedItem.f2 === Or03244Const.DEFAULT.API_RESULT_CHECKON,
        modifiedCnt: findedItem?.modifiedCnt,
      }
    } else {
      return {
        ...item,
        isPlanningFlg: false,
        isHaveIssuesFlg: false,
        cc31Id: '',
        modifiedCnt: Or03244Const.DEFAULT.DATA_DEFAULT,
      }
    }
  })
  // API返却値の番号リストを一時保存する
  dotNumberList = data.problemDotNumberInfoList
  localOneway.orX0096Oneway.maxCount = parseInt(data.cc32IdMax)
}

/**
 * 問題点初期情報取得
 */
async function getProblemDotSolutionEtcInfoData() {
  const inputData: assessmentComprehensiveQuestionInEntity = {
    cc1Id: local.commonInfo.cc1Id,
    sc1Id: local.commonInfo.sc1Id,
    typeId: Or03244Const.DEFAULT.TAB_ID,
  }

  const resData: BaseResponseBody<assessmentComprehensiveQuestionOutWebEntity> =
    await ScreenRepository.select('problemDotSolutionEtcInfoSelect', inputData)

  if (resData.data) {
    // 課題データ処理
    processIssuesData(resData.data.problemDotSolutionEtcInfo)
  }
}

/**
 * 番号設定関数
 *
 * @param index - 変換前のインデックス
 */
function setCircleNumber(index: number) {
  const circleNumbers = [
    '①',
    '②',
    '③',
    '④',
    '⑤',
    '⑥',
    '⑦',
    '⑧',
    '⑨',
    '⑩',
    '⑪',
    '⑫',
    '⑬',
    '⑭',
    '⑮',
    '⑯',
    '⑰',
    '⑱',
    '⑲',
    '⑳',
  ]
  if (index >= 1 && index <= 20) {
    return circleNumbers[index - 1]
  }
}

/**
 * 新規処理
 */
const createNew = () => {
  // 更新区分を新規にする
  screenUpdateKbn.value = UPDATE_KBN.CREATE
  local.or34069.careItems.forEach((item) => {
    item.careLabel.forEach((sItem) => {
      sItem.planValue = { modelValue: '' }
      sItem.offerValue = { modelValue: '' }
      sItem.familyValue = { modelValue: '' }
    })
  })
  local.or34069.careLocationItems.forEach((item) => {
    item.careLocationLabel.forEach((sItem) => {
      sItem.locationValue = { modelValue: '' }
      if (
        sItem.label === '' ||
        sItem.inputShowMode.appendInput === Or03244Const.DEFAULT.INPUT_DISPLAY_FLG_LAST ||
        sItem.inputShowMode.appendInput === Or03244Const.DEFAULT.INPUT_DISPLAY_FLG_UNIT
      ) {
        sItem.inputContent = {
          value: '',
        }
      }
    })
  })
  // 各ラベルをクリア
  const newQuestionList = local.orX0096.listSection.map((item) => {
    return {
      ...item,
      isHaveIssuesFlg: false,
      isPlanningFlg: false,
    }
  })
  // 要介護課題等々クリア
  local.orX0096 = {
    listSection: newQuestionList,
    concreteCareItemList: [],
    concreteContentList: [],
  }
}

/**
 * APIリクエストパラメータ作成
 *
 * @param request - リクエスト
 */
const createRequest = (request: string | undefined) => {
  if (!request) return Or03244Const.DEFAULT.DATA_DEFAULT
  if (request === '') {
    return Or03244Const.DEFAULT.DATA_DEFAULT
  }
  if (screenUpdateKbn.value === UPDATE_KBN.CREATE) {
    return Or03244Const.DEFAULT.DATA_DEFAULT
  }
  return request
}

/**
 * 更新回数チェック
 *
 * @param isHistory - 履歴更新区分
 */
const getRequestScreenUpdataKbn = (isHistory: boolean) => {
  // 更新回数が不存在する場合、更新区分を新規にする
  if (isHistory) {
    return local.commonInfo.historyUpdateKbn === UPDATE_KBN.NONE
      ? UPDATE_KBN.UPDATE
      : (local.commonInfo.historyUpdateKbn ?? '')
  } else {
    if (updateNum === '' && screenUpdateKbn.value === UPDATE_KBN.NONE) return UPDATE_KBN.CREATE
    return screenUpdateKbn.value === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : screenUpdateKbn.value
  }
}

/**
 * 問題点や解決すべき課題等々情報保存前処理
 *
 * @param careRecipientHealthAndLifeIssues - 課題情報
 */
const processProblemDotLsit = (careRecipientHealthAndLifeIssues: OrX0096Type) => {
  return {
    issuseList: createProblemDotList(careRecipientHealthAndLifeIssues.listSection),
    concreteContentsInfoList: createConcreteContentsInfoList(
      careRecipientHealthAndLifeIssues.concreteContentList
    ),
    careItemList: createCareList(careRecipientHealthAndLifeIssues.concreteCareItemList),
  }
}

/**
 * 問題点解決情報リスト作成関数
 *
 * @param listSection - 課題情報
 */
const createProblemDotList = (listSection: OrX0096Type['listSection']) => {
  return listSection.map((item) => {
    return {
      cc31Id: item.cc31Id,
      b1Cd: Or03244Const.DEFAULT.TAB_ID,
      b4Cd: item.b4cd,
      f1: item.isPlanningFlg
        ? Or03244Const.DEFAULT.CHECKBOX_CHECK_IN_VALUE
        : Or03244Const.DEFAULT.CHECKBOX_CHECK_OFF_VALUE,
      f2: item.isHaveIssuesFlg
        ? Or03244Const.DEFAULT.CHECKBOX_CHECK_IN_VALUE
        : Or03244Const.DEFAULT.CHECKBOX_CHECK_OFF_VALUE,
      modifiedCnt: item.modifiedCnt,
    }
  })
}

/**
 * 具体的内容作成関数
 *
 * @param concreteContentList - 具体的内容情報
 */
const createConcreteContentsInfoList = (
  concreteContentList: OrX0096Type['concreteContentList']
) => {
  // 具体内容情報リスト
  let concreteContentsInfoList: AssessmentComprehensivePsychologyUpdateInEntity['concreteContentsInfoList'] =
    []
  concreteContentsInfoList = concreteContentList?.map((item, index) => {
    return {
      // 更新区分チェック
      cc32Id:
        item.updateKbn === UPDATE_KBN.CREATE ? Or03244Const.DEFAULT.DATA_DEFAULT : item.cc32Id,
      // 更新区分チェック、新規の場合は、画面記録IDを設定する
      cc32IdRecord: item.updateKbn === UPDATE_KBN.CREATE ? item.cc32Id : '',
      b1Cd: Or03244Const.DEFAULT.TAB_ID,
      memoKnj: item.content,
      seq: (index + 1).toString(),
      juni: item.juni,
      cc32Type: item.cc32Type,
      ci1Id: item.ci1Id,
      dmyB4Cd: item.correspondenceKeys.map((item) => {
        return {
          b4Cd: item.b4Cd,
        }
      }),
      updateKbn: item.updateKbn === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : item.updateKbn,
      modifiedCnt: item.modifiedCnt,
    }
  })
  return concreteContentsInfoList
}

/**
 * 対応するケア項目作成関数
 *
 * @param concreteCareItemList - 対応するケア項目情報
 */
const createCareList = (concreteCareItemList: OrX0096Type['concreteCareItemList']) => {
  let careItemList: AssessmentComprehensivePsychologyUpdateInEntity['careItemList'] = []
  careItemList = concreteCareItemList?.map((item, index) => {
    return {
      // 該当情報が新規の場合、画面に表示したIDを削除する
      cc33Id:
        item.updateKbn === UPDATE_KBN.CREATE ? Or03244Const.DEFAULT.DATA_DEFAULT : item.cc33Id,
      b1Cd: Or03244Const.DEFAULT.TAB_ID,
      memoKnj: item.content,
      cc32Id: item.cc32Id,
      seq: (index + 1).toString(),
      ci2Id: item.ci2Id,
      dmyCc32Id: item.dmyCc32Id,
      updateKbn: item.updateKbn === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : item.updateKbn,
      modifiedCnt: item.modifiedCnt,
    }
  })
  return careItemList
}

/**
 * 複写データ作成関数
 *
 * @param comprehensiveQuestionInfo - 問題点解決等情報
 */
const processDuplicateData = (
  comprehensiveQuestionInfo: assessmentComprehensiveQuestionOutWebEntity['problemDotSolutionEtcInfo']
) => {
  const problemInfoList = cloneDeep(local.orX0096)
  let maxCount = localOneway.orX0096Oneway.maxCount
  local.orX0096.listSection.forEach((item) => {
    // ID以外、他のデータを画面に設定する
    const findedItem = comprehensiveQuestionInfo.problemDotSolutionInfoList.find(
      (listSectionItem) => listSectionItem.b4Cd === item.b4Cd
    )
    if (findedItem) {
      item.isPlanningFlg = findedItem.f1 === Or03244Const.DEFAULT.API_RESULT_CHECKON
      item.isHaveIssuesFlg = findedItem.f2 === Or03244Const.DEFAULT.API_RESULT_CHECKON
    }
  })
  // 元データを削除する
  local.orX0096.concreteContentList?.forEach((item) => {
    item.updateKbn = UPDATE_KBN.DELETE
  })
  // 取得したデータ画面に表示する
  const concreteContensList = comprehensiveQuestionInfo.concreteContentsInfoList.map(
    (item): ConcreteContentType => {
      maxCount++
      return {
        key: item.cc32Id,
        correspondenceKeys: [],
        content: item.memoKnj,
        // ID再設定
        cc32Id: maxCount.toString(),
        modifiedCnt: Or03244Const.DEFAULT.DATA_DEFAULT,
        juni: item.juni,
        b1Cd: item.b1Cd,
        seq: item.seq,
        cc32Type: item.cc32Type,
        ci1Id: item.ci1Id,
        dmyB4Cd: item.dmyB4Cd,
        b4Cd: item.b4Cd,
        number: '',
        updateKbn: UPDATE_KBN.CREATE,
      }
    }
  )
  // 番号リストを作成
  const numberList = comprehensiveQuestionInfo.problemDotNumberInfoList.map((item) => {
    return {
      cc32Id: item.cc32Id,
      b4Cd: item.b4Cd,
      seq: item.seq,
      modifiedCnt: item.modifiedCnt1,
      cc34Id: item.cc34Id,
      b1Cd: item.b1Cd,
      modifiedCnt1: Or03244Const.DEFAULT.DATA_DEFAULT,
    }
  })
  // 番号リスト対応するCc32Id再設定
  numberList.forEach((item) => {
    // 対応するIDのデータを探す
    const findedItem = concreteContensList.find((sItem) => sItem.key === item.cc32Id)
    if (findedItem) {
      item.cc32Id = findedItem.cc32Id
    }
  })
  // 対応するケア項目リスト作成
  const concreteCareList = comprehensiveQuestionInfo.careItemList.map(
    (item, index): ConcreteCareItemType => {
      return {
        key: item.cc32Id,
        dmyCc32Id: item.dmyCc32Id,
        cc33Id: item.cc33Id,
        content: item.memoKnj,
        b1Cd: item.b1Cd,
        cc32Id: item.cc32Id,
        updateKbn: UPDATE_KBN.CREATE,
        modifiedCnt: Or03244Const.DEFAULT.DATA_DEFAULT,
        seq: (index + 1).toString(),
        ci2Id: item.ci2Id,
      }
    }
  )
  const updatedConcreteCareList = concreteCareList
    .map((item) => {
      const findedItem = concreteContensList.find((sItem) => sItem.key === item.cc32Id)
      if (findedItem) {
        return {
          ...item,
          cc32Id: findedItem.cc32Id,
          dmyCc32Id: findedItem.cc32Id,
        }
      } else {
        // 対応するアイテムが見つからない場合はnullを返す
        return null
      }
    })
    .filter((item) => item !== null) // nullをフィルタリングして最終的なリストを作成
  if (concreteContensList.length > 0) {
    concreteContensList.forEach((item) => {
      const findedItem = numberList.filter((sItem) => sItem.cc32Id === item.cc32Id)
      if (findedItem) {
        item.correspondenceKeys = findedItem
        item.number = findedItem.map((item) => setCircleNumber(parseInt(item.b4Cd))).join('')
      }
    })
  }
  local.orX0096.concreteContentList?.push(...concreteContensList)
  local.orX0096.concreteCareItemList?.push(...updatedConcreteCareList)
  localOneway.orX0096Oneway.maxCount = maxCount
}

/**
 * 削除処理を行う
 */
const userDelete = () => {
  // 更新区分を D に設定する
  screenUpdateKbn.value = UPDATE_KBN.DELETE
  // テーブルボディーを非表示にする
  localOneway.or34069Oneway.showTableBodyFlg = false
  localOneway.orX0096Oneway.tableDisplayFlg = false
}

/**************************************************
 * ライフサイクル
 **************************************************/
onMounted(() => {
  initControl()
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0008Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    // 画面共通情報を取得
    getCommonInfo()
    if (newValue === undefined) {
      return
    }
    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or03244Const.DEFAULT.TAB_ID) return

    // 再表示の場合
    if (newValue.isRefresh) {
      // 画面情報再取得
      await reload()
    }

    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      await _userSave()
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      createNew()
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
      userDelete()
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      isLoading.value = true
      // 複写情報取得
      await getDuplicateDataInfo()
      // 問題点初期情報取得
      await getDuplicateProblemDotSolutionEtcInfoData()
      screenFromDuplicate = true
      isLoading.value = false
      return
    }
  },
  { deep: true, immediate: true }
)

/**
 * 複写モードイベント監視
 */
watch(
  () => Or59423Logic.event.get(props.parentUniqueCpId + Or59423Const.CP_ID(0)),
  async (newValue) => {
    if (!newValue) return

    // 複写モード共通情報取得
    getDuplicateCommonInfo()

    // 本画面ではない場合、処理終了
    if (local.commonInfo.activeTabId !== Or03244Const.DEFAULT.TAB_ID) {
      return
    }

    if (newValue.reloadEvent) {
      isLoading.value = true
      await getInitDataInfo()
      // 問題点初期情報取得
      await getProblemDotSolutionEtcInfoData()
      // 全処理済み、タブ変更を禁止する
      if (props.onewayModelValue.screenMode === TeX0008Const.DEFAULT.MODE_COPY) {
        if (componentRef.value) {
          disableTab(componentRef.value)
        }
      }
      isLoading.value = false
    }
  }
)
</script>

<template>
  <div
    ref="componentRef"
    class="or03244Wrapper"
  >
    <v-overlay
      :model-value="isLoading"
      :persistent="false"
      class="align-center justify-center"
      ><v-progress-circular
        indeterminate
        color="primary"
      ></v-progress-circular
    ></v-overlay>
    <c-v-row no-gutters>
      <g-custom-or34069
        v-bind="or34069"
        :oneway-model-value="localOneway.or34069Oneway"
        :model-value="local.or34069"
      />
      <div
        class="w-100 mt-2 mb-2"
        style="height: 1px; background-color: #cccccc"
      ></div>
      <g-custom-or-x0096
        v-bind="orX0096"
        :oneway-model-value="localOneway.orX0096Oneway"
        :model-value="local.orX0096"
      />
    </c-v-row>
    <!-- 記号選択ダイヤログ -->
    <g-custom-or-53105
      v-if="showDialogOr53105CksFlg"
      v-bind="or53105"
    />
    <!-- 確認ダイヤログ -->
    <g-base-or-21814
      v-if="showOr21814DialogFlg"
      v-bind="or21814"
    />
  </div>
</template>

<style scoped lang="scss">
.or03244Wrapper {
  .pageTitle {
    border: 1px rgb(var(--v-theme-black-200)) solid;
    background-color: rgb(var(--v-theme-black-100)) !important;
  }
  .titilLabel {
    font-weight: bold;
    font-size: 16px;
    padding: 8px;
  }
}
</style>
