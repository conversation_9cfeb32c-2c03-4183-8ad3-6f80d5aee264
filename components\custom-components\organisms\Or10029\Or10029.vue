<script setup lang="ts">
/**
 * Or10029:履歴選択］画面 評価表
 * GUI01239_［履歴選択］画面 評価表
 *
 * @description
 * ［履歴選択］画面 評価表
 *
 * <AUTHOR>
 */

import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch } from 'vue'
import type { EvaluationTableDataItem, Or10029StateType } from './Or10029.type'
import { Or10029Const } from './Or10029.constants'
import { useScreenOneWayBind } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Or10029OnewayType,Or10029Type } from '~/types/cmn/business/components/Or10029Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  HistorySelectEvaluationInitInEntity,
  HistorySelectEvaluationInitOutEntity,
} from '~/repositories/cmn/entities/EvaluationTableHistoryInitInfoSelectEntity'
import type { Mo01334OnewayType } from '~/types/business/components/Mo01334Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or10029Type
  onewayModelValue: Or10029OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const defaultOnewayModelValue = {
  defaultData: {
    //計画期間ID
    sc1Id: '',
    // 事業者ID
    svJigyoId: '0',
    // 利用者ID
    userId: '',
    // 履歴ID
    cmoni1Id: '1',
  },
}

const localOneway = reactive({
  or10029: {
    ...defaultOnewayModelValue.defaultData,
    ...props.onewayModelValue,
  },
  // 評価表ダイアログ
  mo00024Oneway: {
    width: '325px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.history-select'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  evaluationMo01334: {
    // 評価表データテーブルのヘッダー
    headers: [
      // 作成日
      {
        title: t('label.create-date'),
        key: 'createDate',
        minWidth: '110px',
        sortable: false,
      },
      // 作成者
      {
        title: t('label.author'),
        key: 'author',
        minWidth: '184px',
        sortable: false,
      },
    ],
    height: 294,
    items: [
      {
        id: '',
        cmoni1Id: '',
        createYmd: '',
        shokuName: '',
      },
    ],
  } as Mo01334OnewayType,
})

// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
}

// 確定ボタン設置
const mo00609Oneway: Mo00609OnewayType = {
  btnLabel: t('btn.confirm'),
  width: '90px',
  tooltipText: t('tooltip.confirm-btn'),
}

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10029Const.DEFAULT.IS_OPEN,
})

// 評価表データ設定
const evaluationSelectedItem = ref<Or10029Type>()

const local = reactive({
  // 履歴選択情報
  mo01334: {
    value: '',
    values: [],
  },
})


/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10029StateType>({
  cpId: Or10029Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10029Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: HistorySelectEvaluationInitInEntity = {
    sc1Id: localOneway.or10029.sc1Id,
    svJigyoId: localOneway.or10029.svJigyoId,
    userId: localOneway.or10029.userId,
  }

  const resData: HistorySelectEvaluationInitOutEntity = await ScreenRepository.select(
    'evaluationTableHistoryInitInfoSelect',
    inputData
  )
  localOneway.evaluationMo01334.items = []
  if (resData.data.cpnTucCmoni1List) {
    const dataList = resData.data.cpnTucCmoni1List
    // データ情報設定
    localOneway.evaluationMo01334.items = dataList.map((item) => {
      return {
        ...item,
        id: item.cmoni1Id,
      }
    })
    // 親画面.履歴IDが存在判断
    evaluationSelectedItem.value =
      dataList.find((item) => item.cmoni1Id === localOneway.or10029.cmoni1Id) ?? dataList[0]
  }
}

/**
 * 評価表情報選択行
 *
 * @param item - 評価表情報選択行
 */
const clickEvaluationSelectRow = (item: EvaluationTableDataItem) => {
  evaluationSelectedItem.value = item
}

/**
 * 評価表情報の行をダブルクリックで選択
 *
 * @param item - 評価表情報選択行
 */
const doubleClickEvaluationSelectRow = (item: EvaluationTableDataItem) => {
  evaluationSelectedItem.value = item
  onConfirmBtn()
}

/**
 * 評価表情報選択行設定様式
 *
 * @param item - 評価表情報選択行
 */
const evaluationIsSelected = (item: { cmoni1Id: null }) =>
  evaluationSelectedItem.value?.cmoni1Id === item.cmoni1Id

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = () => {
  // 評価表データがない場合、操作なし
  if (!evaluationSelectedItem.value) return
  // 選択情報値戻り
  const resData={cmoni1Id:evaluationSelectedItem.value.cmoni1Id} as Or10029Type
  emit('update:modelValue',resData)
  onClickCloseBtn()
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            class="table-header"
          >
            <base-mo01334
              v-model="local.mo01334"
              class="list-wrapper"
              hide-default-footer
              :oneway-model-value="localOneway.evaluationMo01334"
            >
              <template #item="{ item }">
                <tr
                  :class="{ 'selected-row': evaluationIsSelected(item) }"
                  @click="clickEvaluationSelectRow(item)"
                  @dblclick="doubleClickEvaluationSelectRow(item)"
                >
                  <td>
                    <!-- 作成日 -->
                    <span>{{ item.createYmd }}</span>
                  </td>
                  <td>
                    <!-- 作成者 -->
                    <span>{{ item.shokuName }}</span>
                  </td>
                </tr>
              </template>
            </base-mo01334>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row
        no-gutters
        class="text-right"
      >
        <c-v-col>
          <c-v-spacer />
          <!-- 閉じるボタン -->
          <base-mo00611
            :oneway-model-value="mo00611Oneway"
            class="mr-2"
            @click="onClickCloseBtn"
          >
          </base-mo00611>
          <!-- 確定ボタン-->
          <base-mo00609
            :oneway-model-value="mo00609Oneway"
            @click="onConfirmBtn"
          >
          </base-mo00609>
        </c-v-col>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
</style>
