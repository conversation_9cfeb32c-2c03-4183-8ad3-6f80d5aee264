/**
 * アセスメント(パッケージプラン)モーダル
 *
 * @description
 * アセスメント(パッケージプラン)モーダル
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { AssessmentPackageplanInitSelectInEntity } from '~/repositories/cmn/entities/AssessmentPackageplanEntity'
/**
 * アセスメント(パッケージプラン)
 *
 * @description
 * アセスメント(パッケージプラン)初期情報データを返却する。
 * dataName："assessmentPackageplanInitSelect"
 */
export function handler(inEntity: AssessmentPackageplanInitSelectInEntity) {
  let result = {}
  if (inEntity.cstId === '0' && inEntity.assId === '2') {
    result = defaultData.res1
  } else if (inEntity.cstId === '1' && inEntity.assId === '2') {
    result = defaultData.res2
  }

  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...result,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
