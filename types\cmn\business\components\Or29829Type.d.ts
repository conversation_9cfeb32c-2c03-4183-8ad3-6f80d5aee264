import type { Mo01384Type } from '~/types/business/components/Mo01384Type'
import type { Mo01274Type } from '~/types/business/components/Mo01274Type'
/**
 * Or29829：有機体：アセスメント(インターライ)画面
 * 単方向バインドのデータ構造
 */
export interface Or29829OnewayType {
  /**
   * 期間管理フラグ「0:管理しない 1:管理する」
   */
  periodManageFlag: '0' | '1'

  /**
   * 削除フラグ
   */
  deleteFlg: boolean

  /**
   *copyParentId
   */
  copyParentId?: string
  /**
   *複数flg
   */
  copyFlg?: boolean
  /**
   *ボタン非表示フラグ
   */
  hiddenAction?: boolean
}

/**
 * 双方向バインドModelValue
 */
export interface Or29829Type {
  /**
   * 服薬状況一覧
   */
  takingMedicationType: TakingMedicationType[]
}

/**
 * TakingMedicationType
 */
export interface TakingMedicationType {
  /**
   * 薬剤ID
   */
  yakuzaiId: string
  /**
   * 薬剤名
   */
  drugKnj: Mo01384Type

  /**
   * 量
   */
  ryouKnj: Mo01274Type

  /**
   * 効能
   */
  kounouKnj: Mo01274Type

  /**
   * 連番
   */
  ass3Id: string

  /**
   * ヘッダID
   */
  ass1Id: string

  /**
   * 表示順
   */
  sort: string

  /**
   * 更新回数
   */
  modifiedCnt: string
}
