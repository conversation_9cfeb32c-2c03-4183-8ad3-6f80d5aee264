<script setup lang="ts">
/**
 * GUI01003_印刷設定
 *
 * @description
 * GUI01003_印刷設定POP画面
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or29115Const } from '~/components/custom-components/organisms/Or29115/Or29115.constants'
import { Or29115Logic } from '~/components/custom-components/organisms/Or29115/Or29115.logic'
import type { Plan1InitMasterInfo } from '~/repositories/cmn/entities/Plan1SelectEntity'
import type { PrintOnewayEntity, userList } from '~/repositories/cmn/entities/PrintSelectEntity'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01003'
// ルーティング
const routing = 'GUI01003/pinia'
// 画面物理名
const screenName = 'GUI01003'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

//印刷設定モーダル
const or29115 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01003' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
or29115.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'GUI01003',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or29115Const.CP_ID(0) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or29115Const.CP_ID(0)]: or29115.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or29115Logic.initialize(init.childCpIds[Or29115Const.CP_ID(0)].uniqueCpId)
}
/**************************************************
 * Props
 **************************************************/

// ダイアログ表示フラグ
const showDialogOr29115 = computed(() => {
  // Or29115のダイアログ開閉状態
  return Or29115Logic.state.get(or29115.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const localOneway = reactive({
  or29115Oneway: {
    houjinId: '1',
    shisetuId: '1',
    svJigyoId: '1',
    jigyoKnj: '事業所名TEST',
    svJigyoIdList: ['1'],
    shokuId: '1',
    userId: '1',
    startDate: systemCommonsStore.getStartDate,
    endDate: systemCommonsStore.getEndDate,
    sysYmd: systemCommonsStore.getSystemDate,
    systemCode: '71101',
    userList: [] as userList[],
    initMasterObj: {
      shosikiFlg: '1',
      cksFlg: '1',
      cks1TantoFlg: '1',
      cks1YokaiFlg: '1',
      cks1PrtSizeFlg: '1',
      shoninFlg: '1',
    } as Plan1InitMasterInfo,
    tantoShokuId: '0',
    rirekiId: '6',
    kikanFlg: '1',
  } as PrintOnewayEntity<Plan1InitMasterInfo>,
})

/**
 *  ボタン押下時の処理
 *
 * @param cksFlg - 計画書様式
 *
 * @param sectionName - セクション名
 */
function or29115OnClick(cksFlg: string, sectionName: string) {
  localOneway.or29115Oneway.initMasterObj.cksFlg = cksFlg
  localOneway.or29115Oneway.sectionName = sectionName
  // Or29115のダイアログ開閉状態を更新する
  Or29115Logic.state.set({
    uniqueCpId: or29115.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or29115OnClick('1', '施設サービス計画書（１）')"
        >GUI01003_印刷設定（計画書様式が「2:居宅」以外）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or29115OnClick('2', '居宅サービス計画書（１）')"
        >GUI01003_印刷設定（計画書様式が「2:居宅」の場合）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-29115
    v-if="showDialogOr29115"
    v-bind="or29115"
    :oneway-model-value="localOneway.or29115Oneway"
  />
</template>
