/**
 * Or27607:(阻害要因マスタ)阻害要因マスタ入力
 * GUI00913_阻害要因マスタ
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> DAO DINH DUONG
 */

/**
 * 親画面からGUI00672_困難度マスタモーダル
 */
import type { Mo01278Type } from '~/types/business/components/Mo01278Type'
import type { Mo01274Type } from '~/types/business/components/Mo01274Type'

/**
 * Or27607のOneWayバインド用の状態インターフェース
 */
export interface Or27607StateType {
  /**
   * 保存実行フラグ
   */
  isSave: boolean
}

/**
 * 困難度マスタ
 */
export interface TableData {
  /**
   * 困難度マスタ
   */
  sogaiyouinList: Sogaiyouin[]
}
interface Sogaiyouin {
  uniqueId: string,
  /**
   * 要因コード
   */
  youinCd: { modelValue: Mo01278Type }
   /**
   * 阻害要因
   */
  youinKnj: { modelValue: Mo01274Type }
  /**
   * 施設ID
   */
  shisetuId?: string
  /**
   * 施設ID
   */
  svJigyoId?: string
  /**
   * 法人ID
   */
  houjinId?: string
  /**
   * 表示順
   */
  sort: { modelValue: Mo01278Type }
  /**
   * 更新回数
   */
  modifiedCnt: number
  /**
   * 更新区分（任意）
   */
  updateKbn?: string
  /**
   * 表示フラグ
   */
  visible: boolean
}
export interface OderVTable {
  key: string
  order: string
}