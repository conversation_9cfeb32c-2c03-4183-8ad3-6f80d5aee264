<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import {
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { OrX0160Const } from '~/components/custom-components/organisms/OrX0160/OrX0160.constants'
import { OrX0160Logic } from '~/components/custom-components/organisms/OrX0160/OrX0160.logic'
import type { OrX0160OnewayType, OrX0160Type } from '~/types/cmn/business/components/OrX0160Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
const { t } = useI18n()
/**************************************************
 * 変数定義
 **************************************************/
// 画面ID
const screenId = 'sampleOrX0160KikanInput'
// ルーティング
const routing = 'sampleOrX0160KikanInput'
// 画面物理名
const screenName = 'サンプル_OrX0160_入力補助付き期間入力'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const orX0160 = ref({ uniqueCpId: OrX0160Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'sampleOrX0160KikanInput' },
})
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'sampleOrX0160KikanInput',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or21814Const.CP_ID(0) }, { cpId: OrX0160Const.CP_ID(0) }],
})
// 子コンポーネントのセットアップ
Or21814Logic.initialize(init.childCpIds.Or21814.uniqueCpId)
OrX0160Logic.initialize(init.childCpIds.OrX0160.uniqueCpId)
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
  [OrX0160Const.CP_ID(0)]: orX0160.value,
})

const orX0160Oneway = reactive<OrX0160OnewayType>({
  showItemLabel: true,
  itemLabel: 'テストラベル',
  isRequired: true,
  itemLabelFontWeight: 'bold',
  showEditBtnFlg: true,
})
// 入力支援付き期間入力 disabled
const orX0160DisabledOneway = reactive<OrX0160OnewayType>({
  showItemLabel: true,
  itemLabel: 'テストラベル',
  isRequired: true,
  itemLabelFontWeight: 'bold',
  showEditBtnFlg: true,
  disabled: true,
})
// 入力支援付き期間入力 カスタイムスタイル
const orX0160CustomStyleOneway = reactive<OrX0160OnewayType>({
  showItemLabel: true,
  itemLabel: 'テストラベル',
  isRequired: true,
  itemLabelFontWeight: 'bold',
  showEditBtnFlg: true,
  editBtnClass: 'special-btn',
  startKikanClass: 'color-blue',
})

const orX0160Type = ref<OrX0160Type>({
  startKikan: '2025/01/01',
  endKikan: '2025/12/31',
})

/**
 * 入力補助ボタンクリック時の処理
 */
function onClickEditBtn() {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogText: 'テスト',
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
}
</script>
<template>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/care-manager/mockup-sample/picture-book-list"
        >前のページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- 入力支援付き期間入力 -->
  <div class="d-flex ma-2 flex-row">
    <g-custom-or-x-0160
      v-bind="orX0160"
      v-model="orX0160Type"
      :oneway-model-value="orX0160Oneway"
      @on-click-edit-btn="onClickEditBtn"
    ></g-custom-or-x-0160>
    <div>
      <div>期間開始日: {{ orX0160Type.startKikan }}</div>
      <div>期間終了日: {{ orX0160Type.endKikan }}</div>
    </div>
  </div>
  <!-- 入力支援付き期間入力 disabled -->
  <div class="d-flex ma-2">disabled</div>
  <div class="d-flex ma-2">
    <g-custom-or-x-0160
      v-bind="orX0160"
      v-model="orX0160Type"
      :oneway-model-value="orX0160DisabledOneway"
      @on-click-edit-btn="onClickEditBtn"
    ></g-custom-or-x-0160>
  </div>
  <!-- 入力支援付き期間入力 カスタイムスタイル -->
  <div class="d-flex ma-2">カスタイムスタイル</div>
  <div class="d-flex ma-2">
    <g-custom-or-x-0160
      v-bind="orX0160"
      v-model="orX0160Type"
      :oneway-model-value="orX0160CustomStyleOneway"
      @on-click-edit-btn="onClickEditBtn"
    ></g-custom-or-x-0160>
  </div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>
<style lang="scss" scoped>
:deep(.special-btn) {
  background: #ebf2fd !important;
  color: red !important;
}
:deep(.color-blue) {
  background: #2e75b6 !important;
}
</style>
