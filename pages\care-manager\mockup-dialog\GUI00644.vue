<script setup lang="ts">
/**
 * GUI00644_［年金手帳選択］画面
 *
 * @description
 * ［年金手帳選択］画面
 *
 * <AUTHOR> DAO DINH DUONG
 */
import _ from 'lodash'
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or28409Const } from '~/components/custom-components/organisms/Or28409/Or28409.constants'
import { Or28409Logic } from '~/components/custom-components/organisms/Or28409/Or28409.logic'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP［年金手帳選択］画面
 * KMD DAO DINH DUONG 2025/05/07 ADD START
 **************************************************/
/**
 *画面ID
 */
const screenId = 'GUI00644'
/**
 *ルーティング
 */
const routing = 'GUI00644/pinia'
/**
 *画面物理名
 */
const screenName = 'GUI00644'

/**
 *画面状態管理用操作変数
 */
const screenStore = useScreenStore()
/**
 *or28409
 */
const or28409 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
/**
 *piniaの画面領域を初期化する
 */
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00644' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
/**
 *これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
 */
const pageComponent = screenStore.screen().supplement.pageComponent
const systemCommonsStore = useSystemCommonsStore()
/**
 *自身のPinia領域をセットアップ
 */
useInitialize({
  cpId: 'GUI00644',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or28409Const.CP_ID(1) }],
})
/**
 * 表示状態を計算して返す算出プロパティ。
 */
const isShow = computed(() => {
  return Or28409Logic.state.get(or28409.value.uniqueCpId)?.isOpen ?? false
})
// 自身のPinia領域をセットアップ
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or28409Const.CP_ID(1)]: or28409.value,
})
Or28409Logic.initialize(or28409.value.uniqueCpId)

/**
 *  ボタン押下時の処理(Or28409)
 */
function onClickOr28409() {
  // Or28409 ダイアログ開閉状態を更新する
  Or28409Logic.state.set({
    uniqueCpId: or28409.value.uniqueCpId,
    state: { param: { userId: systemCommonsStore.getUserId ?? '' }, isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- GUI00644_困難度マスタ DAO DINH DUONG 2025/05/08 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr28409"
      >
        GUI00644_［年金手帳選択］画面
      </v-btn>
      <g-custom-or-28409
        v-if="isShow"
        v-bind="or28409"
      />
    </c-v-col>
  </c-v-row>
  <!-- GUI00644_［年金手帳選択］画面 DAO DINH DUONG 2025/05/10 ADD END-->
</template>
