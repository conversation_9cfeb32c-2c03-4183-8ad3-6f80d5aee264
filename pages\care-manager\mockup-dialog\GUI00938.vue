<script lang="ts" setup>
/**
 * Or10453:処理ロジック
 * GUI00938_印刷設定
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> DAO VAN DUONG
 */
import _ from 'lodash'
import {
  computed,
  definePageMeta,
  ref,
  useScreenStore,
  useSetupChildProps,
  useInitialize,
  nextTick
} from '#imports'
import type { Or10453OnewayType } from '~/types/cmn/business/components/Or10453Type'
import { Or10453Logic } from '~/components/custom-components/organisms/Or10453/Or10453.logic'
import { Or10453Const } from '~/components/custom-components/organisms/Or10453/Or10453.constants'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

const screenId = 'GUI00938'
const routing = 'GUI00938/pinia'
const screenName = 'GUI00938'
const screenStore = useScreenStore()
const Or10453 = ref({ uniqueCpId: '' })
const Or10453OnewayModel: Or10453OnewayType = {
  kikanFlg: '1',
  userId: '2',
  sectionName: '1',
  choIndex: '1',
  historyId: '3',
  careManagerInChargeSettingsFlag: 2,
  tantoShokuId: '0',
  svJigyoId: '1',
  svJigyoIdList: ['1'],
  kinounameKnj: 'PRT',
  kojinhogoFlg: '1',
  sectionAddNo: '1',
  local: ''
}

screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00938' },
})

const pageComponent = screenStore.screen().supplement.pageComponent
Or10453.value.uniqueCpId = pageComponent.uniqueCpId

/**
 * 自身のPinia領域をセットアップ
 * - 現在の画面のコンポーネントIDとユニークコンポーネントIDを設定
 * - 子コンポーネントの情報を登録
 */
const { childCpIds } = useInitialize({
  cpId: 'GUI00938', // 現在の画面のコンポーネントID
  uniqueCpId: pageComponent.uniqueCpId, // ユニークコンポーネントID
  childCps: [{ cpId: Or10453Const.CP_ID(0) }], // 子コンポーネントの情報
})

Or10453Logic.initialize(childCpIds.Or10453.uniqueCpId)

/**
 * 子コンポーネントのプロパティを設定
 * - 子コンポーネントのユニークIDを設定
 */
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or10453Const.CP_ID(0)]: Or10453.value,
})

const isShowDialogOr10453 = computed(() => {
  return Or10453Logic.state.get(Or10453.value.uniqueCpId)?.isOpen ?? false
})
async function onClickOr10453(kikanFlg = '1') {
  Or10453OnewayModel.kikanFlg = kikanFlg
  await nextTick()
  Or10453Logic.state.set({
    uniqueCpId: Or10453.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters>
    <c-v-col> ケアマネモックアップ開発ダイヤログ画面確認用ページ </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- GUI00938_印刷設定 KMD DAO VAN DUONG 2025/05/30 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        class="block"
        variant="plain"
        @click="onClickOr10453('1')"
      >
        GUI00938_印刷設定 kikanFlag === '1'
      </v-btn>
      <v-btn
        class="block"
        variant="plain"
        @click="onClickOr10453('2')"
      >
        GUI00938_印刷設定 kikanFlag === '2'
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-10453
    v-if="isShowDialogOr10453"
    v-bind="Or10453"
    :oneway-model-value="Or10453OnewayModel"
  />
  <!-- GUI00938_印刷設定 KMD DAO VAN DUONG 2025/05/30 ADD END-->
</template>
<style lang="scss" scoped>
  .block {
    display: block;
  }
</style>
