<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or11122Const } from '../Or11122/Or11122.constants'
import type { Or11122OneWayType } from '../Or11122/Or11122.type'
import { Or11122Logic } from '../Or11122/Or11122.logic'
import { Or10004Const } from './Or10004.constants'
import type {
  Or10004CopyType,
  Or10004OneList,
  Or10004OneWayType,
  Or10004Param,
  Or10004StateType,
  Or10004ThreeList,
  Or10004TwoList,
  Or10004Type,
  SeqList,
} from './Or10004.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01344OnewayType } from '~/types/business/components/Mo01344Type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type {
  MonitoringDisplayOrderSettingSelectInEntity,
  MonitoringDisplayOrderSettingSelectOutEntity,
} from '~/repositories/cmn/entities/MonitoringDisplayOrderSettingSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

/**
 * Or10004:((計画ﾓﾆﾀﾘﾝｸﾞ)表示順変更モニタリング記録表)ダイアログ
 * Gui01230_表示順変更モニタリング記録表
 *
 * @description
 * ((計画ﾓﾆﾀﾘﾝｸﾞ)表示順変更モニタリング記録表)ダイアログ
 *
 * <AUTHOR>
 */

/************************************************
 * Props
 ************************************************/

interface Props {
  uniqueCpId: string
  modelValue: Or10004Type
  onewayModelValue: Or10004OneWayType
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

const or11122_1 = ref({ uniqueCpId: Or11122Const.CP_ID(0) })
const or11122_2 = ref({ uniqueCpId: Or11122Const.CP_ID(1) })

const isInit = ref(false)


const mo00024 = ref<Mo00024Type>({
  isOpen: Or10004Const.DEFAULT.IS_OPEN,
})

const local = reactive({
  // タブ
  mo00043: {
    id: '',
  } as Mo00043Type,
  or10004: {
    ...props.modelValue,
  },
  or10004Copy: {
    list1: [] as Or10004OneList[],
    list2: [] as Or10004TwoList[],
    list3: [] as Or10004ThreeList[],
    selectRowId: '1',
  } as Or10004CopyType,
})

const defaultOneway = reactive({
  // タブ
  mo00043OnewayType: {
    tabItems: [],
    minWidth: '58px',
  } as Mo00043OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609SaveOneway: {
    btnLabel: t('btn.confirm'),
    disabled: false,
  } as Mo00609OnewayType,
  // タブ
  or11122OneWay: {
    styleFlg: '',
    tabFlg: Or10004Const.DEFAULT.TAB_ID_1,
  } as Or11122OneWayType,
})

const localOneWay = reactive({
  or10004: {
    ...props.onewayModelValue,
  },
  seqList: {
    columnCount: '',
    headList: [],
    jikouList: [],
    kakuninList: [],
    ikenList: [],
    jusokuList: [],
    taiouList: [],
  } as SeqList,
})

const styleFlg = computed(() => {
  if (localOneWay.or10004.youshikiId !== Or10004Const.DEFAULT.YOUSHIKI_ID_0) {
    return Or10004Const.DEFAULT.CUSTOM_STYLE
  } else {
    if (localOneWay.or10004.carePlanFlg !== Or10004Const.DEFAULT.CARE_PLAN_FLG_5) {
      return Or10004Const.DEFAULT.FIXED_STYLE
    } else {
      return Or10004Const.DEFAULT.FIXED_STYLE_PACK
    }
  }
})

// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1300px',
  height:styleFlg.value === Or10004Const.DEFAULT.FIXED_STYLE_PACK ? '595px' : styleFlg.value === Or10004Const.DEFAULT.FIXED_STYLE ? '558px' : '523px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or10004',
    toolbarTitle: t('label.display-order-modified-monitoring-record-table'),
    toolbarName: 'Or10004ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'pa-0',
  } as Mo01344OnewayType,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * ライフサイクルフック
 **************************************************/

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11122Const.CP_ID(0)]: or11122_1.value,
  [Or11122Const.CP_ID(1)]: or11122_2.value,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10004StateType>({
  cpId: Or10004Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10004Const.DEFAULT.IS_OPEN
    },
  },
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  init()
})

/**
 * 初期化
 */
const init = () => {
  defaultOneway.mo00043OnewayType.tabItems = [
    {
      id: Or10004Const.DEFAULT.TAB_ID_1,
      title: t('label.display-order-modified'),
      tooltipText: t('tooltip.display-order-change'),
      tooltipLocation: 'bottom',
    },
    {
      id: Or10004Const.DEFAULT.TAB_ID_2,
      title: t('label.display-order-move'),
      tooltipText: t('tooltip.display-order-movement'),
      tooltipLocation: 'bottom',
    },
  ]
  defaultOneway.or11122OneWay.styleFlg = styleFlg.value
  defaultOneway.or11122OneWay.tabFlg = Or10004Const.DEFAULT.TAB_ID_1
  defaultOneway.or11122OneWay.longTermFlg = localOneWay.or10004.longTermFlg
  defaultOneway.or11122OneWay.noFlg = localOneWay.or10004.noFlg
  if (styleFlg.value === Or10004Const.DEFAULT.FIXED_STYLE_PACK) {
    const list1: Or10004OneList[] = localOneWay.or10004.list as Or10004OneList[]
    local.or10004Copy.list1 = cloneDeep(list1)
    isInit.value = true
  } else if (styleFlg.value === Or10004Const.DEFAULT.FIXED_STYLE) {
    const list2: Or10004TwoList[] = localOneWay.or10004.list as Or10004TwoList[]
    local.or10004Copy.list2 = cloneDeep(list2)
  } else {
    const list3: Or10004ThreeList[] = localOneWay.or10004.list as Or10004ThreeList[]
    local.or10004Copy.list3 = cloneDeep(list3)
  }
}

/**
 * 表示順変更モニタリング記録表モーダルの初期情報（表示順リスト）を取得
 */
const getData = async () => {
  // 表示順変更モニタリング記録表モーダルの初期情報（表示順リスト）を取得する
  const inputData: MonitoringDisplayOrderSettingSelectInEntity = {
    free1Id: localOneWay.or10004.free1Id!,
    sys3ryaku: 'CPN',
  }
  // '表示順変更モニタリング記録表モーダルの初期情報（表示順リスト）を取得する
  const res: MonitoringDisplayOrderSettingSelectOutEntity = await ScreenRepository.select(
    'monitoringDisplayOrderSettingSelect',
    inputData
  )

  localOneWay.seqList = res.data
  isInit.value = true

  getTabsData()
}

/**
 * リストを親画面に返す
 */
const confirm = () => {
  sort()
  if (styleFlg.value === Or10004Const.DEFAULT.FIXED_STYLE_PACK) {
    emit('update:modelValue', local.or10004Copy.list1)
  } else if (styleFlg.value === Or10004Const.DEFAULT.FIXED_STYLE) {
    emit('update:modelValue', local.or10004Copy.list2)
  } else {
    emit('update:modelValue', local.or10004Copy.list3)
  }
  close()
}

/**
 * 表示順の更新
 */
const sort = () => {
  const key: keyof Or10004CopyType = `list${styleFlg.value}`
  local.or10004Copy[key].sort((a, b) => {
    if (a.seq === '' || parseInt(a.seq) <= 0) return 1
    if (b.seq === '' || parseInt(b.seq) <= 0) return -1
    const numA = parseInt(a.seq)
    const numB = parseInt(b.seq)
    return numA - numB
  })
  if (local.or10004Copy[key][0].seq === '' || local.or10004Copy[key][0].seq !== '1') {
    local.or10004Copy[key].forEach((item, index) => {
      item.seq = ++index + ''
    })
  } else {
    local.or10004Copy[key].forEach((item, index) => {
      if (item.seq === '') {
        if (index === 0) {
          item.seq = '1'
          return
        }
        item.seq = Number(local.or10004Copy[key][index - 1].seq) + 1 + ''
      } else {
        if (index === 0) {
          return
        }
        if (Number(item.seq) - 1 + '' === local.or10004Copy[key][index - 1].seq) {
          return
        }
        item.seq = Number(local.or10004Copy[key][index - 1].seq) + 1 + ''
      }
    })
  }
}

/**
 * 伝達するデータを更新する
 *
 * @param val - データ
 */
const onUpdate = (val: Or10004CopyType) => {
  local.or10004Copy = val
}

/**
 * 画面最新情報を取得する
 */
const getTabsData = () => {
  // Or11122のダイアログ状態を更新する
  Or11122Logic.state.set({
    uniqueCpId: local.mo00043.id === Or10004Const.DEFAULT.TAB_ID_1 ? or11122_1.value.uniqueCpId : or11122_2.value.uniqueCpId,
    state: {
      param: {
        executeFlag: 'getData',
        seqList: localOneWay.seqList,
      } as Or10004Param,
    },
  })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    mo00024.value.isOpen = true
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue) {
      void close()
    }
  }
)

/**
 * タブ切り替え
 *
 * @description
 * タブ切り替え
 */
watch(
  () => local.mo00043.id,
  async (newVal) => {
    defaultOneway.or11122OneWay.styleFlg = styleFlg.value
    defaultOneway.or11122OneWay.tabFlg = newVal
    sort()
    if (styleFlg.value === Or10004Const.DEFAULT.FIXED_STYLE_PACK) {
      mo00024Oneway.value.width = '1200px'
      getTabsData()
    } else {
      if (!isInit.value) {
        await getData()
      } else {
        getTabsData()
      }
    }
  }
)

/**
 * 画面が閉じます
 */
/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
const close = () => {
  // 画面を閉じる。
  setState({ isOpen: false })
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043"
        :oneway-model-value="defaultOneway.mo00043OnewayType"
        class="ml-2 mr-2"
      >
      </base-mo00043>
      <c-v-window v-model="local.mo00043.id">
        <c-v-window-item :value="Or10004Const.DEFAULT.TAB_ID_1">
          <g-custom-or11122
            v-if="isInit"
            v-model="local.or10004Copy"
            v-bind="or11122_1"
            :oneway-model-value="defaultOneway.or11122OneWay"
            @update:model-value="onUpdate"
        /></c-v-window-item>
        <c-v-window-item :value="Or10004Const.DEFAULT.TAB_ID_2">
          <g-custom-or11122
            v-if="isInit"
            v-model="local.or10004Copy"
            v-bind="or11122_2"
            :oneway-model-value="defaultOneway.or11122OneWay"
            @update:model-value="onUpdate"
          />
        </c-v-window-item>
      </c-v-window>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="defaultOneway.mo00611CloseBtnOneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 保存ボタン Mo00611 -->
        <base-mo00609
          v-bind="defaultOneway.mo00609SaveOneway"
          class="mx-2"
          @click="confirm"
        >
          <!--ツールチップ表示："保存します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss"></style>
