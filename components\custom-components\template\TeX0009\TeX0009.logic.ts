import { OrT0001Const } from '../../organisms/OrT0001/OrT0001.constants'
import { OrT0003Const } from '../../organisms/OrT0003/Ort0003.constants'
import { Or27562Logic } from '../../organisms/Or27562/Or27562.logic'
import { Or27562Const } from '../../organisms/Or27562/Or27562.constants'
import { Or61746Const } from '../../organisms/Or61746/Or61746.constants'
import { Or61746Logic } from '../../organisms/Or61746/Or61746.logic'
import { OrX0008Const } from '../../organisms/OrX0008/OrX0008.constants'
import { OrX0009Const } from '../../organisms/OrX0009/OrX0009.constants'
import { OrX0008Logic } from '../../organisms/OrX0008/OrX0008.logic'
import { OrX0009Logic } from '../../organisms/OrX0009/OrX0009.logic'
import { Or13844Const } from '../../organisms/Or13844/Or13844.constants'
import { OrX0115Logic } from '../../organisms/OrX0115/OrX0115.logic'
import { OrX0115Const } from '../../organisms/OrX0115/OrX0115.constants'
import { Or13844Logic } from '../../organisms/Or13844/Or13844.logic'
import { Or61587Const } from '../../organisms/Or61587/Or61587.constants'
import { Or61587Logic } from '../../organisms/Or61587/Or61587.logic'
import { Or61588Const } from '../../organisms/Or61588/Or61588.constants'
import { Or61588Logic } from '../../organisms/Or61588/Or61588.logic'
import { TeX0009Const } from './TeX0009.constants'
import type { TeX0009EventType, TeX0009StateType } from './TeX0009.type'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'

import {
  useEventStatusAccessor,
  useInitialize,
  useTwoWayBindAccessor,
} from '~/composables/useComponentLogic'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'

import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'

/**
 * TeX0009:GUI00816_アセスメント(パッケージプラン)
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace TeX0009Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: TeX0009Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or00248Const.CP_ID(0) },
        { cpId: OrT0001Const.CP_ID(0) },
        { cpId: OrT0003Const.CP_ID(0) },
        { cpId: Or41179Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or11871Const.CP_ID },
        { cpId: Or21814Const.CP_ID(2) },
        { cpId: Or27562Const.CP_ID(0) },
        { cpId: Or61746Const.CP_ID(0) },
        { cpId: Or13844Const.CP_ID(0) },
        { cpId: OrX0008Const.CP_ID(1) },
        { cpId: OrX0009Const.CP_ID(1) },
        { cpId: OrX0115Const.CP_ID(1) },
        { cpId: Or61587Const.CP_ID(1) },
        { cpId: Or61587Const.CP_ID(2) },
        { cpId: Or61588Const.CP_ID(1) },
        { cpId: Or61588Const.CP_ID(2) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or00248Logic.initialize(childCpIds[Or00248Const.CP_ID(0)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(2)].uniqueCpId)
    Or11871Logic.initialize(childCpIds[Or11871Const.CP_ID].uniqueCpId)
    Or27562Logic.initialize(childCpIds[Or27562Const.CP_ID(0)].uniqueCpId)
    Or61746Logic.initialize(childCpIds[Or61746Const.CP_ID(0)].uniqueCpId)
    Or41179Logic.initialize(childCpIds[Or41179Const.CP_ID(1)].uniqueCpId)
    Or13844Logic.initialize(childCpIds[Or13844Const.CP_ID(0)].uniqueCpId)
    OrX0008Logic.initialize(childCpIds[OrX0008Const.CP_ID(1)].uniqueCpId)
    OrX0009Logic.initialize(childCpIds[OrX0009Const.CP_ID(1)].uniqueCpId)
    OrX0115Logic.initialize(childCpIds[OrX0115Const.CP_ID(1)].uniqueCpId)
    Or61587Logic.initialize(childCpIds[Or61587Const.CP_ID(1)].uniqueCpId)
    Or61587Logic.initialize(childCpIds[Or61587Const.CP_ID(2)].uniqueCpId)
    Or61588Logic.initialize(childCpIds[Or61588Const.CP_ID(1)].uniqueCpId)
    Or61588Logic.initialize(childCpIds[Or61588Const.CP_ID(2)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<TeX0009StateType>(TeX0009Const.CP_ID(0))

  /**
   * 自身のEventStatus領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const event = useEventStatusAccessor<TeX0009EventType>(TeX0009Const.CP_ID(0))
}
