<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or52063Const } from '../Or52063/Or52063.constants'
import { Or52063Logic } from '../Or52063/Or52063.logic'
import { Or09997Logic } from '../Or09997/Or09997.logic'
import { Or09997Const } from '../Or09997/Or09997.constants'
import { Or61587Const } from './Or61587.constants'
import type { DataTableListItem, issuesDataTableListItem } from './Or61587.type'
import { useScreenTwoWayBind, useSetupChildProps } from '#imports'
import { Or21735Const } from '~/components/base-components/organisms/Or21735/Or21735.constants'
import { Or21736Const } from '~/components/base-components/organisms/Or21736/Or21736.constants'
import { Or21737Const } from '~/components/base-components/organisms/Or21737/Or21737.constants'
import { Or21738Const } from '~/components/base-components/organisms/Or21738/Or21738.constants'
import type { Mo01354OnewayType } from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Or61587OnewayType } from '~/types/cmn/business/components/Or61587Type'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type {
  FixedDataList,
  Or52063OneWayType,
  Or52063Type,
} from '~/types/cmn/business/components/Or52063Type'
import type {
  Or09997OnewayType,
  Or09997Type,
  Title,
} from '~/types/cmn/business/components/Or09997Type'

/**
 * Or61587:GUI00816_アセスメント(パッケージプラン)
 *
 * @description
 * アセスメント総括
 *
 * <AUTHOR>
 */

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue?: Or61587OnewayType
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

const or21735 = ref({ uniqueCpId: '' })
const or21736 = ref({ uniqueCpId: '' })
const or21737 = ref({ uniqueCpId: '' })
const or21738 = ref({ uniqueCpId: '' })
const or52063 = ref({ uniqueCpId: '' })
const or09997 = ref({ uniqueCpId: '' })

/**
 * ロカールoneway
 */
const localOneway = reactive({
  importBtnMo00609Oneway: {
    btnLabel: t('btn.import'),
  } as Mo00609OnewayType,
  displayOrderMo00609Oneway: {
    btnLabel: t('btn.display-order'),
  } as Mo00609OnewayType,
  filterImportBtnMo00609Oneway: {
    btnLabel: t('btn.filter-import'),
  } as Mo00609OnewayType,
  displayOrderMo0009Oneway: {
    btnIcon: 'edit_square',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  },
  caseMo00609Oneway: {
    btnLabel: t('label.case-import'),
  } as Mo00609OnewayType,
  caseMo0009Oneway: {
    btnIcon: 'edit_square',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  },
  foucsRowChangeOneway: {
    btnIcon: 'chevron_left',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  },
  mo01354Oneway: {
    headers: [
      /** 領域 */
      {
        title: t('label.region'),
        key: 'ryoikiKbn',
        minWidth: '100px',
        sortable: false,
      },
      {
        title: t('label.planner-or-care-manager-judgment'),
        sortable: false,
        children: [
          {
            /** 具体的状況 */
            title: t('label.specific-situation'),
            key: 'gutaitekiKnj',
            sortable: false,
          },
          {
            /** その原因や要因 */
            title: t('label.causes-and-factors'),
            key: 'geninKnj',
            sortable: false,
          },
          {
            /** 影響している領域 */
            title: t('label.affected-area'),
            key: 'eikyouKnj',
            sortable: false,
          },
          {
            /** 今後の見通し */
            key: 'mitoushiKnj',
            title: t('label.future-outlook'),
            sortable: false,
          },
        ],
      },
      {
        /** 入所者の意向・受けとめ方 */
        key: 'ikouKnj',
        title: t('label.resident-preference-method-acceptance'),
        sortable: false,
      },
    ],
    height: '100%',
    useDefaultHeader: false,
    density: 'default',
    showSelect: true,
    selectStrategy: 'all',
  } as Mo01354OnewayType,
  issuesTableMo01354Oneway: {
    headers: [
      /** 課題 */
      {
        title: t('label.issues'),
        key: 'kadaiKnj',
        sortable: false,
      },
      {
        /** 目標 */
        key: 'mokuhyoKnj',
        title: t('label.goal'),
        sortable: false,
      },
    ],
    height: '100%',
    density: 'default',
  } as Mo01354OnewayType,
  ryoikiKbnOptions: {
    items: [] as object[],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo01282OnewayType,
  editIcon: {
    btnIcon: 'edit_square',
    name: 'editIcon',
    density: 'compact',
  } as Mo00009OnewayType,
  mo00045Oneway: {
    maxLength: '4000',
    isEditCamma: false,
    showItemLabel: false,
  },
  or52063OneWayType: {
    styleId: '',
    headers: [],
  } as Or52063OneWayType,
  or09997Data: {
    sortList: [],
  } as Or09997OnewayType,
})

/**
 * ロカール
 */
const local = reactive({
  mo01354TabelData: {
    values: {
      selectedRowId: '-1',
      selectedRowIds: [],
      items: [] as DataTableListItem[],
    },
  },
  issuesTableMo01354TabelData: {
    values: {
      selectedRowId: '-1',
      selectedRowIds: [],
      items: [] as issuesDataTableListItem[],
    },
  },
  /** Or52063:GUI00824_表示順変更アセスメント */
  or52063Type: {
    fixedDataList: [],
    customizeDataList: [],
  } as Or52063Type,
  or09997Type: {
    sortList: [],
  } as Or09997Type,
})
// ダイアログ表示フラグ
const showDialogOr52063 = computed(() => {
  // Or52063のダイアログ開閉状態
  return Or52063Logic.state.get(or52063.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr09997 = computed(() => {
  // Or09997のダイアログ開閉状態
  return Or09997Logic.state.get(or09997.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or21735Const.CP_ID(1)]: or21735.value,
  [Or21736Const.CP_ID(1)]: or21736.value,
  [Or21737Const.CP_ID(1)]: or21737.value,
  [Or21738Const.CP_ID(1)]: or21737.value,
  [Or52063Const.CP_ID(1)]: or52063.value,
  [Or09997Const.CP_ID(1)]: or09997.value,
})

const { refValue } = useScreenTwoWayBind<Or61587OnewayType>({
  cpId: Or61587Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**
 * ケース取り込み画面表示
 */
function setShowCaseImport() {
  //   if (!authorityManagementInfo.value.isCanView) {
  //     setShowDialog(t('message.i-cmn-10423'))
  //   }
}

watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (!newValue) return
    local.mo01354TabelData.values.items = newValue.cpnTucSypAss3InfoList.map((item) => {
      return {
        ...item,
        ryoikiKbn: { modelValue: item.ryoikiKbn },
        gutaitekiKnj: { value: item.gutaitekiKnj },
        geninKnj: { value: item.geninKnj },
        eikyouKnj: { value: item.eikyouKnj },
        mitoushiKnj: { value: item.mitoushiKnj },
        ikouKnj: { value: item.ikouKnj },
      } as DataTableListItem
    })
    local.issuesTableMo01354TabelData.values.items = newValue.cpnTucSypAss4InfoList.map((item) => {
      return {
        ...item,
        kadaiKnj: { value: item.kadaiKnj },
        mokuhyoKnj: { value: item.mokuhyoKnj },
      } as issuesDataTableListItem
    })
  },
  { deep: true, immediate: true }
)
/**
 * 「表示順ボタン」押下
 *
 * @description
 * GUI00824 ［表示順変更アセスメント］画面をポップアップで起動する。
 */
const onOpenSortModal = () => {
  localOneway.or52063OneWayType = {
    styleId: '0',
    headers: [],
  }
  local.or52063Type.fixedDataList = local.mo01354TabelData.values.items.map((item) => {
    return {
      ...item,
      displayOrder: {
        value: item.seqNo,
      },
      // 領域をA, B, C...の形式に変換
      area: item.ryoikiKbn.modelValue,
      specificSituation: item.gutaitekiKnj.value,
      cause: item.geninKnj.value,
      futureOutlook: item.mitoushiKnj.value,
      methodAcceptance: item.ikouKnj.value,
      affectedAreas: item.eikyouKnj.value,
    } as FixedDataList
  })
  console.log(or52063.value.uniqueCpId, '123')
  Or52063Logic.state.set({
    uniqueCpId: or52063.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「表示順ボタン」押下
 *
 * @description
 * GUI00825 ［表示順変更アセスメント］画面をポップアップで起動する。
 */
const onOpenSortGui00825Modal = () => {
  localOneway.or09997Data.sortList = local.issuesTableMo01354TabelData.values.items.map((item) => {
    return {
      ...item,
      issues: item.kadaiKnj.value,
      goal: item.mokuhyoKnj.value,
    } as Title
  })
  Or09997Logic.state.set({
    uniqueCpId: or09997.value.uniqueCpId,
    state: { isOpen: true },
  })
}

watch(
  () => local.or09997Type,
  (newVal) => {
    local.issuesTableMo01354TabelData.values.items = (
      newVal.sortList as (issuesDataTableListItem & Title)[]
    ).map((item) => {
      return {
        ...item,
        kadaiKnj: item.kadaiKnj,
        mokuhyoKnj: item.mokuhyoKnj,
      } as issuesDataTableListItem
    })
  }
)

watch(
  () => local.or52063Type,
  (newVal) => {
    local.mo01354TabelData.values.items = (
      newVal.fixedDataList as (DataTableListItem & FixedDataList)[]
    ).map((item) => {
      return {
        /** ID */
        id: item.id,
        /** アセスメント履歴ID */
        assId: item.assId,
        /** 領域区分 */
        ryoikiKbn: item.ryoikiKbn,
        /** 具体的状況 */
        gutaitekiKnj: item.gutaitekiKnj,
        /** その原因や要因 */
        geninKnj: item.geninKnj,
        /** 影响领域的领域 */
        eikyouKnj: item.eikyouKnj,
        /** 今後の見通し */
        mitoushiKnj: item.mitoushiKnj,
        /** 利用者の意向受け止め方 */
        ikouKnj: item.ikouKnj,
        /** 表示顺 */
        seqNo: item.displayOrder.value,
        /** 利用者ID */
        userId: item.userId,
        /** 更新回数 */
        modifiedCnt: item.modifiedCnt,
      } as DataTableListItem
    })
  },
  {
    deep: true,
  }
)
</script>

<template>
  <c-v-row no-gutters>
    <c-v-col class="heading-text-m pa-2">
      {{ t('アセスメントの総括') }}
    </c-v-col>
  </c-v-row>
  <!-- アセスメント総括ボタン組 -->
  <c-v-row
    no-gutters
    class="px-2 pb-2"
  >
    <!-- アクションボタン組 -->
    <c-v-col cols="8 d-flex">
      <!-- 行追加ボタン -->
      <div>
        <g-base-or-21735 v-bind="or21735"></g-base-or-21735>
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.add-row')"
        />
      </div>
      <!-- 行挿入ボタン -->
      <div class="ml-2">
        <g-base-or-21736 v-bind="or21736"></g-base-or-21736>
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.insert-row')"
        />
      </div>
      <!-- 行複写ボタン: Or21737 -->
      <div class="ml-2">
        <g-base-or-21737 v-bind="or21737"></g-base-or-21737>
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.duplicate-row')"
        />
      </div>
      <!-- 行削除ボタン: Or21738 -->
      <div class="ml-2">
        <g-base-or-21738 v-bind="or21738"></g-base-or-21738>
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.delete-row')"
        ></c-v-tooltip>
      </div>
      <c-v-divider
        vertical
        class="divider mx-2"
      />
      <!-- 取込 -->
      <div class="mr-2">
        <base-mo00611
          :oneway-model-value="localOneway.importBtnMo00609Oneway"
          @click="onOpenSortModal"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="t('tooltip.import')"
            open-delay="200"
          />
        </base-mo00611>
      </div>
      <!-- ケース取込エリア -->
      <div>
        <base-mo00611
          :oneway-model-value="localOneway.caseMo00609Oneway"
          @click="setShowCaseImport"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('まとめ情報を表示します')"
            open-delay="200"
          />
        </base-mo00611>
      </div>
    </c-v-col>
    <c-v-col
      cols="4"
      class="d-flex justify-end align-center"
    >
      <!-- 絞込みボタン -->
      <base-mo00611
        :oneway-model-value="localOneway.filterImportBtnMo00609Oneway"
        class="mr-2"
        @click="onOpenSortModal"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="t('データを絞り込みます')"
          open-delay="200"
        />
      </base-mo00611>
      <!-- 表示順変更ボタン -->
      <base-mo00611
        :oneway-model-value="localOneway.displayOrderMo00609Oneway"
        @click="onOpenSortModal"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="t('tooltip.display-order')"
          open-delay="200"
        />
      </base-mo00611>
      <!--件数ラベル  -->
      <div class="ml-2">
        {{ local.mo01354TabelData.values.items.length + t('label.item') }}
      </div>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col
      cols="12"
      class="table-header px-2"
    >
      <base-mo-01354
        v-model="local.mo01354TabelData"
        hide-default-footer
        :oneway-model-value="localOneway.mo01354Oneway"
        class="list-wrapper"
      >
        <!--領域区分  -->
        <template #[`item.ryoikiKbn`]="{ item }">
          <base-mo-01282
            v-model="item.ryoikiKbn"
            :oneway-model-value="localOneway.ryoikiKbnOptions"
          />
        </template>
        <!--具体的状況  -->
        <template #[`item.gutaitekiKnj`]="{ item }">
          <div class="d-flex align-center h-100">
            <div class="px-4">
              <base-mo00009 :oneway-model-value="localOneway.editIcon" />
            </div>
            <div class="overflowText h-100 flex-grow-1">
              <!-- 内容 -->
              <base-mo01280 v-model="item.gutaitekiKnj" />
            </div>
          </div>
        </template>
        <!--その原因や要因  -->
        <template #[`item.geninKnj`]="{ item }">
          <div class="d-flex align-center h-100">
            <div class="px-4">
              <base-mo00009 :oneway-model-value="localOneway.editIcon" />
            </div>
            <div class="overflowText h-100 flex-grow-1">
              <!-- 内容 -->
              <base-mo01280 v-model="item.geninKnj" />
            </div>
          </div>
        </template>
        <!--影響している領域  -->
        <template #[`item.eikyouKnj`]="{ item }">
          <div class="d-flex align-center h-100">
            <div class="px-4">
              <base-mo00009 :oneway-model-value="localOneway.editIcon" />
            </div>
            <div class="overflowText h-100 flex-grow-1">
              <!-- 内容 -->
              <base-mo01280 v-model="item.eikyouKnj" />
            </div>
          </div>
        </template>
        <!--今後の見通し  -->
        <template #[`item.mitoushiKnj`]="{ item }">
          <div class="d-flex align-center h-100">
            <div class="px-4">
              <base-mo00009 :oneway-model-value="localOneway.editIcon" />
            </div>
            <div class="overflowText h-100 flex-grow-1">
              <!-- 内容 -->
              <base-mo01280 v-model="item.mitoushiKnj" />
            </div>
          </div>
        </template>
        <!--入所者の意向・受けとめ方  -->
        <template #[`item.ikouKnj`]="{ item }">
          <div class="d-flex align-center h-100">
            <div class="px-4">
              <base-mo00009 :oneway-model-value="localOneway.editIcon" />
            </div>
            <div class="overflowText h-100 flex-grow-1">
              <!-- 内容 -->
              <base-mo01280 v-model="item.ikouKnj" />
            </div>
          </div>
        </template>
      </base-mo-01354>
    </c-v-col>
  </c-v-row>
  <c-v-row
    no-gutters
    class="mx-2 mt-2"
  >
    <c-v-col class="d-flex">
      <div>
        <g-base-or-21735 v-bind="or21735" />
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.add-row')"
        />
      </div>
      <!-- 行削除ボタン: Or21738 -->
      <div class="ml-2">
        <g-base-or-21738 v-bind="or21738"></g-base-or-21738>
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.delete-row')"
        ></c-v-tooltip>
      </div>
    </c-v-col>
    <c-v-col
      cols="4"
      class="d-flex justify-end align-center"
    >
      <!-- 表示順変更ボタン -->
      <base-mo00611
        :oneway-model-value="localOneway.displayOrderMo00609Oneway"
        @click="onOpenSortGui00825Modal"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="t('tooltip.display-order')"
          open-delay="200"
        />
      </base-mo00611>
      <!--件数ラベル  -->
      <div class="ml-2">
        {{ local.issuesTableMo01354TabelData.values.items.length + t('label.item') }}
      </div>
    </c-v-col>
  </c-v-row>
  <c-v-row
    no-gutters
    class="pa-2"
  >
    <c-v-col class="table-header">
      <base-mo-01354
        v-model="local.issuesTableMo01354TabelData"
        hide-default-footer
        :oneway-model-value="localOneway.issuesTableMo01354Oneway"
        class="list-wrapper"
      >
        <!--課題  -->
        <template #[`item.kadaiKnj`]="{ item }">
          <div class="d-flex align-center h-100">
            <div class="px-4">
              <base-mo00009 :oneway-model-value="localOneway.editIcon" />
            </div>
            <div class="overflowText h-100 flex-grow-1">
              <!-- 内容 -->
              <base-mo01280 v-model="item.kadaiKnj" />
            </div>
          </div>
        </template>
        <!--目標  -->
        <template #[`item.mokuhyoKnj`]="{ item }">
          <div class="d-flex align-center h-100">
            <div class="px-4">
              <base-mo00009 :oneway-model-value="localOneway.editIcon" />
            </div>
            <div class="overflowText h-100 flex-grow-1">
              <!-- 内容 -->
              <base-mo01280 v-model="item.mokuhyoKnj" />
            </div>
          </div>
        </template>
      </base-mo-01354>
    </c-v-col>
  </c-v-row>
  <!-- GUI00824_表示順変更アセスメント -->
  <g-custom-or-52063
    v-if="showDialogOr52063"
    v-bind="or52063"
    v-model="local.or52063Type"
    :oneway-model-value="localOneway.or52063OneWayType"
  />
  <!-- GUI00825_表示順変更アセスメント -->
  <g-custom-or-09997
    v-if="showDialogOr09997"
    v-bind="or09997"
    v-model="local.or09997Type"
    :oneway-model-value="localOneway.or09997Data"
  />
</template>

<style lang="scss" scoped>
@use '@/styles/cmn/mo-data-table.scss';
:deep(.v-table__wrapper table tr:not(:first-child) th:last-child) {
  border-right: 1px solid rgba(var(--v-theme-black-200)) !important;
}
.table-header :deep(.v-table__wrapper tr th) {
  background-color: rgb(var(--v-theme-blue-200)) !important;
}
.table-header :deep(.v-table__wrapper tr td:not(:first-child)) {
  padding: 1px !important;
}
.divider {
  height: 36px;
}
.background-transparent {
  background-color: transparent;
}
</style>
