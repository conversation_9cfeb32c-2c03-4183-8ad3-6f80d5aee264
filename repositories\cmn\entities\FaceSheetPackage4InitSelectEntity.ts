import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
/**
 * ［フェースシート（パッケージプラン）］④画面
 *
 * @description
 * ［フェースシート（パッケージプラン）］④画面 API用エンティティ
 *
 * <AUTHOR> - DAO DINH DUONG
 */
export interface FaceSheetPackage4InitSelectEntity extends InWebEntity {
  /**
   * 履歴ID
   */
  faceId: string
  /**
   * 事業者ID
   */
  jid: string
  /**
   * 計画期間ID
   */
  sc1: string
  /**
   * 利用者ID
   */
  uid: string
  /**
   * 改訂フラグ
   */
  kaiteiFlg: string
}

/**
 * ［フェースシート（パッケージプラン）］④画面
 */
export interface FaceSheetPackage4InitSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    kaigoHokenshaList: ItemList[]
    zokuList: ItemList[]
    faceSheet3InfoSelDmyYoshien: FaceSheet3InfoSelDmyYoshien
    cpnTucSypFace1: CpnTucSypFace1
  }
}
/**
 * Item
 */
export interface CpnTucSypFace1 {
  /**
   * フェースシート履歴ＩＤ
   */
  faceId: string

  /**
   * 計画期間ID
   */
  sc1Id: string

  /**
   * 法人ID
   */
  houjinId: string

  /**
   * 施設ID
   */
  shisetuId: string

  /**
   * 事業者ID
   */
  svJigyoId: string

  /**
   * 利用者ＩＤ
   */
  userid: string

  /**
   * 作成日
   */
  createYmd: string

  /**
   * 作成者
   */
  shokuId: string

  /**
   * ケース番号
   */
  caseNo: string

  /**
   * 変更回数
   */
  henkoKaisu: string

  /**
   * フェースシート①作成
   */
  face1Flg: string

  /**
   * フェースシート②作成
   */
  face2Flg: string

  /**
   * フェースシート③作成
   */
  face3Flg: string

  /**
   * 改訂フラグ
   */
  kaiteiFlg: string

  /**
   * 初回作成日
   */
  shokaiYmd: string

  /**
   * フェースシート④作成
   */
  face4Flg: string

  /**
   * 更新回数
   */
  modifiedCnt: string
}

/**
 * Item
 */
export interface FaceSheet3InfoSelDmyYoshien {
  /**
   * アセスメントID
   */
  dmyIryoTokuYmd: string
  /**
   * アセスメントID
   */
  dmyKHokenTokuYmd: string
  /**
   * アセスメントID
   */
  dmyNinStartYmd: string
  /**
   * アセスメントID
   */
  dmyNinEndYmd: string
  /**
  /**
   * アセスメントID
   */
  faceId: string
  /**
   * 介護保険の有無
   */
  kHokenUmu: string
  /**
   * 介護保険者
   */
  kHokenCd: string
  /**
   * 被保険者番号
   */
  hHokenNo: string
  /**
   * 介護保険における住所特例手続き
   */
  kHokenTokuKbn: string
  /**
   * 介護保険における住所特例手続き完了年月日
   */
  kHokenTokuYmd: string
  /**
   * 介護認定の有無
   */
  ninteiUmu: string
  /**
   * 要介護状態区分
   */
  yokaiKbn: string
  /**
   * 認定有効期間（開始）
   */
  ninStartYmd: string
  /**
   * 認定有効期間（終了）
   */
  ninEndYmd: string
  /**
   * 入所前のサービス利用状況
   */
  nyushomaeKnj: string
  /**
   * 障害手帳の有無
   */
  techoUmu0: string
  /**
   *身障手帳有り無し
   */
  techoUmu1: string
  /**
   *手帳の障害名等
   */
  techoShogaiKnj: string
  /**
   *療育手帳有り無し
   */
  techoUmu2: string
  /**
   *手帳の障害名等2
   */
  techoShogai2Knj: string
  /**
   *精神手帳有り無し
   */
  techoUmu3: string
  /**
   *手帳の障害名等3
   */
  techoShogai3Knj: string
  /**
   *障害程度区分の認定
   */
  shougaiNinteiUmu: string
  /**
   *障害程度区分
   */
  teidoKbn: string
  /**
   *障害備考
   */
  shogaiKnj: string
  /**
   *障害手帳の有効期間(開始日)
   */
  techoSYmd: string
  /**
   *障害手帳の有効期間(終了日)
   */
  techoEYmd: string
  /**
   *継続的援護の必要性
   */
  keizokuKnj: string
  /**
   *措置費障害者加算対象者の検討の要否
   */
  sotiKentoKbn: string
  /**
   *契約能力
   */
  keiyakuKbn: string
  /**
   *契約能力特記事項
   */
  keiyakuTokkiKnj: string
  /**
   *権利擁護制度の活用の必要性の検討
   */
  yogoKentoKbn: string
  /**
   *権利擁護制度の活用の必要性の検討（必要な場合の対応）
   */
  kenriHituyoKnj: string
  /**
   *地域福祉権利擁護事業有無
   */
  yogoUmu: string
  /**
   *地域福祉権利擁護事業担当
   */
  yogoTantoKnj: string
  /**
   *成年後見制度の活用の必要性の検討
   */
  skoukenKentoKbn: string
  /**
   *成年後見制度の活用の必要性の検討（必要な場合の対応）
   */
  seinenHituyoKnj: string
  /**
   *後見人等有無
   */
  kokenUmu: string
  /**
   *後見人
   */
  kokenKnj: string
  /**
   *補佐人
   */
  hosaKnj: string
  /**
   *補助人
   */
  hojoKnj: string
  /**
   *年金（有無）
   */
  nenkinUmu: string
  /**
   *年金
   */
  nenkinKnj: string
  /**
   *年金額
   */
  nenkinGaku: string
  /**
   *収入その他（有無）
   */
  syunyuSonotaUmu: string
  /**
   *収入その他
   */
  syunyuSonotaKnj: string
  /**
   *収入なし
   */
  syunyuMu: string
  /**
   *収入その他額
   */
  syunyuSonotaGaku: string
  /**
   *所得段階
   */
  shotokuDankaiKnj: string
  /**
   *経済的自立評価
   */
  keizaiKbn: string
  /**
   *入所前課税状況
   */
  kazeijokyoKnj: string
  /**
   *入所後費用徴収額階層入所者
   */
  hiyogakuKaisouNyusho: string
  /**
   *入所後費用徴収額入所者
   */
  hiyogakuNyusho: string
  /**
   *入所後費用徴収額扶養義務者
   */
  hiyogakuHuyo: string
  /**
   *入所後費用徴収額階層扶養義務者
   */
  hiyogakuKaisouHuyo: string
  /**
   *資産負債の状況
   */
  shisanKnj: string
  /**
   *高額介護サービス費上限度額
   */
  kougakuGendogaku: string
  /**
   *生活保護法による医療扶助の有無
   */
  seihoIryohojoUmu: string
  /**
   *生活保護実施機関名
   */
  seihoJisshiKnj: string
  /**
   *扶助名
   */
  hujoKnj: string
  /**
   *医療扶助単給
   */
  iryohujoUmu: string
  /**
   *葬祭の実施の必要性
   */
  sosaiJisshiKnj: string
  /**
   *相続人有無
   */
  sozokuUmu: string
  /**
   *相続人1
   */
  sozoku1Knj: string
  /**
   *相続人1続柄
   */
  sozoku1ZokuCd: string
  /**
   *相続人2
   */
  sozoku2Knj: string
  /**
   *相続人2続柄
   */
  sozoku2ZokuCd: string
  /**
   *収入等経済的基盤の状況メモ
   */
  syunyuMemoKnj: string
  /**
   *利用者ID
   */
  userid: string
  /**
   *dmyTechoSYmd
   */
  dmyTechoSYmd: string
  /**
   *dmyTechoEYmd
   */
  dmyTechoEYmd: string
  /**
   *dmyYoshien
   */
  dmyYoshien: string
  /**
   *dmyYokaigo
   */
  dmyYokaigo: string
  /**
   *更新回数
   */
  modifiedCnt: string
}

/**
 * アセスメント(インターライ)画面保存入力エンティティ
 */
export interface AssessmentFacesheetNoteBook4UpdateEntity
  extends InWebEntity,
    Record<string, unknown> {}

/**
 * アセスメントマスタ保存出力エンティティ
 */
export interface AssessmentFacesheetNoteBook4UpdateOutEntity extends OutWebEntity {
  /** data */
  data: object
}

/**
 * 介護保険者リスト
 */
interface ItemList {
  /** コードID */
  codeId: string
  /** コード名称 */
  codeName: string
}
