import type { CustomClass } from '~/types/CustomClassType'

/**
 * OrX0160：有機体：入力補助付き期間入力
 *
 * 単方向バインドのデータ構造
 *
 * <AUTHOR>
 */
export interface OrX0160OnewayType {
  /**
   * 入力支援ボタン表示・非表示フラグ
   */
  showEditBtnFlg?: boolean
  /**
   * コンポーネント名
   */
  name?: string | undefined

  /**
   * 項目名ラベル
   */
  itemLabel?: string | undefined

  /**
   * 項目名ラベルフォント
   */
  itemLabelFontWeight?: string

  /**
   * 必須バッジの表示フラグ（デフォルト：false）
   * true：表示 false：非表示
   */
  isRequired?: boolean

  /**
   * 項目名ラベルの領域を表示（デフォルト：true）
   * true：表示する false：表示しない
   */
  showItemLabel?: boolean
  /**
   * 無効フラグ
   */
  disabled?: boolean

  /**
   * カスタムクラス
   */
  customClass?: CustomClass

  /**
   * 入力補助ボタンクラス
   */
  editBtnClass?: string

  /**
   * 期間開始日付クラス
   */
  startKikanClass?: string

  /**
   * 期間終了日付クラス
   */
  endKikanClass?: string

  /**
   * ラベルの表示位置
   */
  isVerticalLabel?: boolean

  /**
   * ヒントツールチップテキスト
   */
  hintTooltipText?: string

  /**
   * 詳細非表示
   */
  hideDetails?: string
}
/**
 * OrX0160：有機体：入力補助付き期間入力
 * 双方向バインドのデータ構造
 */
export interface OrX0160Type {
  /** 期間開始日付 */
  startKikan: string
  /** 期間終了日付 */
  endKikan: string
}
