<script setup lang="ts">
/**
 * Or27347:有機体:課題番号変更モーダル
 * GUI01097_課題番号変更
 *
 * @description
 *［課題番号変更］画面では、課題番号を変更する。
 *［課題番号変更］画面は、［ケアマネ］→［予防計画］→［予防計画書］→［総合的課題と目標］画面などで［課題番号］をクリックすると表示する。
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27347Const } from './Or27347.constants'
import type { Or27347StateType, TableData } from './Or27347.type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  Or27347Type,
  Or27347OnewayType,
  IssuesRtnList,
} from '~/types/cmn/business/components/Or27347Type'
import type { Mo01278Type, Mo01278OnewayType } from '~/types/business/components/Mo01278Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or27347Type
  onewayModelValue: Or27347OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
const selectedItemIndex = ref<number>(-1)
const localOneway = reactive({
  Or27347: {
    ...props.onewayModelValue,
  },
  // 情報ダイアログ
  mo00024Oneway: {
    width: '1000px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or27347',
      toolbarTitle: t('label.issues-number-upd'),
      toolbarName: 'Or27347ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo01265OnewayModelValue: {
    btnLabel: t('btn.issues-number-del'),
  } as Mo01265OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27347StateType>({
  cpId: Or27347Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or27347Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

// 課題一覧リスト
const tableData = ref<TableData[]>([])
// ポスト最小幅
const columnMinWidth = ref<number[]>([217, 182, 182, 193, 184])
/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27347Const.DEFAULT.IS_OPEN,
})

const or21814 = ref({ uniqueCpId: '' })

// ダイアログ表示フラグ
const showDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
})

// 課題一覧ヘッダ(A4横2枚、A4横3枚の場合)
const issuesNumUpdTableHeaders = [
  {
    title: t('label.issues-number'),
    key: 'issuesNum',
    align: 'start',
    sortable: false,
    required: true,
  },
  {
    title: t('label.comprehensive-issues'),
    key: 'comprehensiveIs',
    align: 'start',
    sortable: false,
  },
  {
    title: t('label.issues-goal-Proposal'),
    key: 'issGoalProposal',
    align: 'start',
    sortable: false,
  },
  {
    title: t('label.specific-intention'),
    key: 'specificIntention',
    align: 'start',
    sortable: false,
  },
  {
    title: t('label.goal'),
    key: 'goal',
    align: 'start',
    sortable: false,
  },
]
// 課題一覧ヘッダ(A3横1枚の場合)
const issuesNumUpdTableHeadersAThree = [
  {
    title: t('label.issues-number'),
    key: 'issuesNum',
    align: 'start',
    sortable: false,
    required: true,
  },
  {
    title: t('label.comprehensive-issues'),
    key: 'comprehensiveIs',
    align: 'start',
    sortable: false,
  },
  {
    title: t('label.issues-goal-Proposal-a-three'),
    key: 'issGoalProposal',
    align: 'start',
    sortable: false,
  },
  {
    title: t('label.specific-intention'),
    key: 'specificIntention',
    align: 'start',
    sortable: false,
  },
  {
    title: t('label.goal'),
    key: 'goal',
    align: 'start',
    sortable: false,
  },
]

//"*"
const required: string = Or27347Const.DEFAULT.REQUIRED
//title高さです
const tableHeight = ref('')

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  init()
})

/**
 * AC001_初期情報取得
 */
function init() {
  // A4横2枚、A4横3枚の場合
  if (localOneway.Or27347.processType === 'true') {
    tableHeight.value = '180px'
  } else {
    // 3横1枚の場合
    tableHeight.value = '210px'
  }
  // 「課題一覧リスト」タブ初期表示
  // AC001-2取得した課題一覧リストデータを一覧に設定する。
  if (props.onewayModelValue.issuesList && props.onewayModelValue.issuesList.length > 0) {
    const tempArray = props.onewayModelValue.issuesList
    for (const data of tempArray) {
      const item = {
        week1Id: data.week1Id,
        parentIssuesNum: data.issuesNum,
        issuesNum: {
          modelValue: {
            value: data.issuesNum + '',
          } as Mo01278Type,
          onewayModelValue: {
            max: 99,
            min: -9,
          } as Mo01278OnewayType,
        },
        comprehensiveIs: {
          onewayModelValue: {
            value: data.comprehensiveIs,
            unit: '',
          } as Mo01337OnewayType,
        },
        issGoalProposal: {
          onewayModelValue: {
            value: data.issGoalProposal,
            unit: '',
          } as Mo01337OnewayType,
        },
        specificIntention: {
          onewayModelValue: {
            value: data.specificIntention,
            unit: '',
          } as Mo01337OnewayType,
        },
        goal: {
          onewayModelValue: {
            value: data.goal,
            unit: '',
          } as Mo01337OnewayType,
        },
        copy: data.issuesNum + '',
      } as TableData
      tableData.value.push(item)
    }
  }
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * ポップアップウィンドウで選択確認ダイアログを表示し
 *
 * @param errormsg - Message
 */
function showOr21814Msg(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.ok'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.thirdBtnClickFlg) {
      // 注意ダイアログの確認ボタン押下時
      return
    }
  }
)
/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
}
/**
 * 数値入力イベント
 *
 *@param reSetIndex - reSetIndex
 */
async function onInputNumber(reSetIndex: number) {
  // 数値以外の文字を削除
  const value = tableData.value[reSetIndex].issuesNum.modelValue.value
  tableData.value[reSetIndex].issuesNum.modelValue.value = value.replace(/[^0-9]/g, '')
  await nextTick()
}

/**
 * AC003_「課題番号」セルマウスダウン
 *
 * @param reSetIndex - reSetIndex
 */
function sortReSetProc(reSetIndex: number) {
  const sortVal = {
    maxIssuesNum: 0,
  }
  for (const item of tableData.value) {
    if (
      item &&
      item.issuesNum.modelValue.value !== '' &&
      Number(item.issuesNum.modelValue.value) > sortVal.maxIssuesNum
    ) {
      sortVal.maxIssuesNum = Number(item.issuesNum.modelValue.value)
    }
  }
  if (tableData.value[reSetIndex].issuesNum.modelValue.value === '') {
    tableData.value[reSetIndex].issuesNum.modelValue.value = String(sortVal.maxIssuesNum + 1)
  }
}

/**
 * AC004_「課題番号削除」ボタン押下
 */
function sortDeleteClick() {
  // 課題一覧リスト内の「課題番号」列の内容をクリアする
  if (tableData.value && tableData.value.length > 0) {
    for (const item of tableData.value) {
      item.issuesNum.modelValue.value = ''
    }
  }
}

/**
 * AC005_「確定」ボタン押下
 */
function confirm() {
  let isError = 'false'
  const respData: Or27347Type = {
    issuesList: [],
  }
  const checklist: string | string[] = []
  if (tableData.value && tableData.value.length > 0) {
    const tempList = new Array<IssuesRtnList>()
    for (const item of tableData.value) {
      if (checklist.includes(item.issuesNum.modelValue.value)) {
        showOr21814Msg(t('message.i-cmn-11264'))
        isError = 'true'
        break
      } else {
        checklist.push(item.issuesNum.modelValue.value)
      }
      const data: IssuesRtnList = {
        week1Id: item.week1Id,
        issuesNum: item.parentIssuesNum,
        setIssuesNum: Number(item.issuesNum.modelValue.value),
      }
      tempList.push(data)
    }
    tempList.sort((a, b) => a.issuesNum - b.issuesNum)
    respData.issuesList = tempList
  }
  if (isError === 'false') {
    // 設定した課題番号は呼び出す元画面の一覧をソートし、再表示する。
    emit('update:modelValue', respData)
    // 本画面を閉じ、親画面に返却する。
    close()
  }
}

/**
 * AC006_「閉じるボタン」押下
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo01265
        :oneway-model-value="localOneway.mo01265OnewayModelValue"
        color="#ff0000"
        label-color="#ff0000"
        style="margin-left: 900px; margin-bottom: -5px; margin-top: 2px"
        @click="sortDeleteClick"
      >
      </base-mo01265>
      <c-v-col>
        <c-v-data-table
          v-resizable-grid="{ columnWidths: columnMinWidth }"
          :headers="
            localOneway.Or27347.processType === 'true'
              ? issuesNumUpdTableHeaders
              : issuesNumUpdTableHeadersAThree
          "
          class="table-wrapper"
          hide-default-footer
          :items="tableData"
          fixed-header
          hover
          :height="tableHeight"
          :items-per-page="-1"
        >
          <!-- ヘッダ Part -->
          <template #headers>
            <tr>
              <th>
                <!-- 課題番号 -->
                <div style="display: flex">
                  <div style="color: red">{{ required }}</div>
                  <span>{{ t('label.issues-number-2') }}</span>
                </div>
              </th>
              <th>
                <!-- 総合的課題 -->
                {{ t('label.comprehensive-issues') }}
              </th>
              <th v-if="localOneway.Or27347.processType === 'true'">
                <!-- 課題に対する目標と具体策の提案 -->
                {{ t('label.issues-goal-Proposal') }}
              </th>
              <th v-if="localOneway.Or27347.processType === 'false'">
                <!-- 課題に対する目標と具体策の提案 -->
                {{ t('label.issues-goal-Proposal-a-three') }}
              </th>
              <th>
                <!-- 具体策についての意向 本人・家族 -->
                {{ t('label.specific-intention') }}
              </th>
              <th>
                <!-- 目標 -->
                {{ t('label.goal') }}
              </th>
            </tr>
          </template>
          <template #item="{ item, index }">
            <tr
              :class="{ 'select-row': selectedItemIndex === index }"
              @click="selectRow(index)"
            >
              <!-- 課題番号 -->
              <td>
                <base-mo01278
                  v-model="tableData[index].issuesNum.modelValue"
                  :oneway-model-value="item.issuesNum.onewayModelValue"
                  @click.stop="sortReSetProc(index)"
                  @input="onInputNumber(index)"
                >
                </base-mo01278>
                <!-- 総合的課題 -->
              </td>
              <td>
                <span class="overflowText"> {{ item.comprehensiveIs.onewayModelValue.value }}</span>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :max-width="350"
                  :text="item.comprehensiveIs.onewayModelValue.value"
                  open-delay="200"
                />
              </td>
              <!-- 課題に対する目標と具体策の提案 -->
              <td>
                <span class="overflowText"> {{ item.issGoalProposal.onewayModelValue.value }}</span>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :max-width="350"
                  :text="item.issGoalProposal.onewayModelValue.value"
                  open-delay="200"
                />
              </td>
              <!-- 具体策についての意向 本人・家族 -->
              <td>
                <span class="overflowText">
                  {{ item.specificIntention.onewayModelValue.value }}</span
                >
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :max-width="350"
                  :text="item.specificIntention.onewayModelValue.value"
                  open-delay="200"
                />
              </td>
              <!-- 目標 -->
              <td>
                <span class="overflowText"> {{ item.goal.onewayModelValue.value }}</span>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :max-width="350"
                  :text="item.goal.onewayModelValue.value"
                  open-delay="200"
                />
              </td>
            </tr>
          </template>
        </c-v-data-table>
      </c-v-col>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          v-bind="localOneway.mo00609ConfirmOneway"
          class="mx-2"
          @click="confirm"
        >
          <!--ツールチップ表示："設定を確定します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialog"
    v-bind="or21814"
  />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
.overflowText {
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}
:deep(.txt:disabled) {
  background: #ffffff;
}
</style>
