import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or52060:有機体:印刷設定モーダル（画面/特殊コンポーネント）
 * GUI00842_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
export namespace Or52060Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or52060', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * セクション番号
     */
    export const SECTION_NO = 'ALL'
    /**
     * 個人情報使用フラグ
     */
    export const KOJINHOGO_USED_FLG = '0'
    /**
     * 個人情報番号
     */
    export const SECTION_ADD_NO = '0'
    /**
     * インデックス
     */
    export const INDEX = '0'
    /**
     * 単一
     */
    export const TANI = '0'
    /**
     * 複数
     */
    export const HUKUSUU = '1'
    /**
     * 期間管理する
     */
    export const KIKAN_FLG_MANAGE = '1'
    /**
     * 期間管理しない
     */
    export const KIKAN_FLG_MANAGE_NONE = '0'
    /**
     * チェックボックスの選択状態 '0':チェックオフ
     */
    export const CHECKBOX_STATUS_CHECK_OFF = '0'
    /**
     * チェックボックスの選択状態 '1':チェックイン
     */
    export const CHECKBOX_STATUS_CHECK_IN = '1'
    /**
     * メニュー２名称:[mnu2][3GK][包括]ｱｾｽﾒﾝﾄ
     */
    export const NM_MNU2_3GK_INCLUDE_ASSESSMENT = '[mnu2][3GK][包括]ｱｾｽﾒﾝﾄ'
    /**
     * メニュー３名称:[mnu3][3GK][包括]ｱｾｽﾒﾝﾄ
     */
    export const NM_MNU3_3GK_INCLUDE_ASSESSMENT = '[mnu3][3GK][包括]ｱｾｽﾒﾝﾄ'
    /**
     * 出力帳票ID: ケアチェック表１
     */
    export const CARE_CHECK_TABLE_1_REPORE_ID = 'CareCheckTable1Report'
    /**
     * 出力帳票ID: ケアチェック表２
     */
    export const CARE_CHECK_TABLE_2_REPORE_ID = 'CareCheckTable2Report'
    /**
     * 出力帳票ID: ケアチェック表３
     */
    export const CARE_CHECK_TABLE_3_REPORE_ID = 'CareCheckTable3Report'
    /**
     * 出力帳票ID: ケアチェック表４
     */
    export const CARE_CHECK_TABLE_4_REPORE_ID = 'CareCheckTable4Report'
    /**
     * 出力帳票ID: ケアチェック表５
     */
    export const CARE_CHECK_TABLE_5_REPORE_ID = 'CareCheckTable5Report'
    /**
     * 出力帳票ID: ケアチェック表６
     */
    export const CARE_CHECK_TABLE_6_REPORE_ID = 'CareCheckTable6Report'
    /**
     * 出力帳票ID: ケアチェック表７
     */
    export const CARE_CHECK_TABLE_7_REPORE_ID = 'CareCheckTable7Report'
    /**
     * 出力帳票ID: ケアチェック表(全て)
     */
    export const CARE_CHECK_TABLE_REPORE_ALL_ID = 'CareCheckTableALLReport'
    /**
     * 出力帳票ID: 具体的内容と対応するケア項目一覧
     */
    export const CARE_ITEM_LIST_REPORT_ID = 'CareItemListReport'
    /**
     * 出力帳票ID: ケアチェック要約表
     */
    export const CARE_CHECK_YOYAKU_TABLE_REPORE_ID = 'CareCheckYoyakuTableReport'
    /**
     * スペース
     */
    export const SAPCE = ' '
    /**
     * 固定文字セット
     */
    export namespace STR {
      /**
       * 空文字
       */
      export const EMPTY = ''
      /**
       * 数字: 0
       */
      export const ZERO = '0'
      /**
       * 数字: 1
       */
      export const ONE = '1'
      /**
       * 数字: 2
       */
      export const TWO = '2'
    }
  }
}
