<script setup lang="ts">
/**
 * Or60075:［退院・退所情報記録書］画面テンプレート
 *
 * @description
 * GUI01305_［退院・退所情報記録書］画面
 *
 * <AUTHOR>
 */

import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { OrD2002Const } from '../OrD2002/OrD2002.constants'
import { Or28992Const } from '../Or28992/Or28992.constants'
import { OrX0008Logic } from '../OrX0008/OrX0008.logic'
import { OrX0008Const } from '../OrX0008/OrX0008.constants'
import { Or17392Const } from '../Or17392/Or17392.constants'
import { Or17393Const } from '../Or17393/Or17393.constants'
import { OrX0009Logic } from '../OrX0009/OrX0009.logic'
import { OrX0001Const } from '../OrX0001/OrX0001.constants'
import { Or27562Logic } from '../Or27562/Or27562.logic'
import { Or27562Const } from '../Or27562/Or27562.constants'
import { Or26257Const } from '../Or26257/Or26257.constants'
import { OrX0009Const } from '../OrX0009/OrX0009.constants'
import { Or17391Logic } from '../Or17391/Or17391.logic'
import { Or17391Const } from '../Or17391/Or17391.constants'
import { OrX0007Logic } from '../OrX0007/OrX0007.logic'
import { OrX0007Const } from '../OrX0007/OrX0007.constants'
import { Or06146Const } from '../Or06146/Or06146.constants'
import { Or60075Logic } from './Or60075.logic'
import type { Or60075StateType } from './Or60075.type'
import { Or60075Const } from './Or60075.constants'
import {
  hasPrintAuth,
  useJigyoList,
  useNuxtApp,
  useScreenInitFlg,
  useScreenOneWayBind,
  useScreenStore,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00043OnewayType } from '~/types/business/components/Mo00043Type'
import type { Mo00028Type } from '~/types/business/components/Mo00028Type'
import type { Or28992OnewayType } from '~/types/cmn/business/components/Or28992Type'
import type { Or60075OnewayType, Or60075Type } from '~/types/cmn/business/components/Or60075Type'
import type {} from '~/repositories/cmn/entities/CpnTucGdlComInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import type { OrX0001OnewayType, OrX0001Type } from '~/types/cmn/business/components/OrX0001Type'

import { useUserListInfo } from '~/utils/useUserListInfo'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or27562OnewayType } from '~/types/cmn/business/components/Or27562Type'
import type { Or17391OnewayType } from '~/types/cmn/business/components/Or17391Type'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'

import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type {
  HistoryInfo1,
  HospitalizationTimeInfoOfferHistorySelectInEntity,
  HospitalizationTimeInfoOfferHistorySelectOutEntity,
  HospitalizationTimeInfoOfferInitInfo,
  HospitalizationTimeInfoOfferPeriodSelectOutEntity,
} from '~/repositories/cmn/entities/HospitalizationTimeInfoOfferPeriodSelectServiceEntity'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { OrX0007OnewayType, OrX0007Type } from '~/types/cmn/business/components/OrX0007Type'
import type {
  OrX0008OnewayType,
  OrX0008Type,
  RirekiInfo,
} from '~/types/cmn/business/components/OrX0008Type'
import type { OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'

const $log = useNuxtApp().$log as DebugLogPluginInterface

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or60075OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
// 利用者一覧変更監視
const { syscomUserSelectWatchFunc } = useUserListInfo()
// 事業所変更監視
const { jigyoListWatch } = useJigyoList()

const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const or17391 = ref({
  uniqueCpId: '',
  parentUniqueCpId: props.uniqueCpId,
  or00249UniqueCpId: '',
  or00094UniqueCpId: '',
}) // GUI00807_アセスメント複写
const or27562 = ref({ uniqueCpId: '' }) // GUI00626_アセスメントマスタ
const or41179 = ref({ uniqueCpId: '' }) // 事業所選択
const orX0009 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' }) // 確認ダイアログ(yes, no, cancel)
const or21814_2 = ref({ uniqueCpId: '' }) // 確認ダイアログ(yes)
const or21814_3 = ref({ uniqueCpId: '' }) // 確認ダイアログ(yes,no)
const orHeadLine = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const or00094 = ref({ uniqueCpId: '' })
const orD2002 = ref({ uniqueCpId: '' })
const or28992 = ref({
  uniqueCpId: Or28992Const.CP_ID(0),
  parentUniqueCpId: props.uniqueCpId,
})

const orX0007 = ref({ uniqueCpId: OrX0007Const.CP_ID(0) })
const orX0008 = ref({ uniqueCpId: OrX0008Const.CP_ID(0) })
const orX0001 = ref({ uniqueCpId: '' })
const or26257 = ref({ uniqueCpId: '' })
const or06146 = ref({
  uniqueCpId: Or06146Const.CP_ID(0),
  parentUniqueCpId: props.uniqueCpId,
})

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})

// ローカルTwoway
const local = reactive({
  orX0007: {
    PlanTargetPeriodUpdateFlg: '0',
    planTargetPeriodId: '1',
  } as OrX0007Type,
  orX0008: {
    /**
     *計画書ID（履歴Id）
     */
    createId: '0',
    /**
     *履歴更新フラグ
     */
    createUpateFlg: '0',
  } as OrX0008Type,
  periodInfo: {},
  listDataGroup: {} as Record<string, CodeType[]>,
  // 作成日
  createDate: {
    value: '',
    mo01343: {
      value: '',
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  mo00028: {
    panel: ref(1),
  } as Mo00028Type,
  // 削除ダイアログ
  orX0001Type: {
    deleteSyubetsu: '',
  } as OrX0001Type,
  copyDisabled: false,
})

// 権限フラグ
const isPermissionViewAuth = ref(true)

// 画面情報の取得中かどうか
const isLoading = ref(false)

// 画面初期化完了フラグ
let isInitCompleted = false

// 選択中利用者ID
const userId = ref('')

// // 変更タブIDを保持
// const tabIdBk = ref('')

// 期間管理フラグ
const plannningPeriodManageFlg = ref(Or60075Const.DEFAULT.PLANNING_PERIOD_MANAGE)

// 履歴更新回数
const historyModifiedCnt = ref('0')

// 履歴表示フラグ
const isHistoryShow = ref(true)

// 画面情報領域のsupplement内のisInitの取得およびonMountedにfalseへ更新する処理を登録
const isInit = useScreenInitFlg()

// 画面処理パターン
const clickMode = ref('')

// 複写コンポネントキー
const copyComponentKey = ref('')

// 複写コンポネントキー
const deleteKbn = ref(Or60075Const.DEFAULT.DELETE_PROCESS_KBN_NORMAL)

// 事業所IDバックアップ
const jigyoId = ref('')

// 履歴IDバックアップ
const teikyouIdOld = ref('')

// 入力作成日バックアップ
const createDateOld = ref('')

// ローカルOneway
const localOneway = reactive({
  // 計画対象期間選択
  orX0007Oneway: {
    kindId: systemCommonsStore.getSyubetu,
  } as OrX0007OnewayType,
  Or60075Oneway: {
    /** 続柄マスタ情報 */
    relationshipMasterInfo: [],
    /** 介護保険情報リスト */
    nursingCareInsuranceInfoList: [],
    /** 担当情報リスト */
    tantoInfoList: [],
    /** 事業所情報リスト */
    jigyoInfoList: [],
    /**  親族関係者情報リスト */
    kinshipInfoList: [],
    confirmationInfo: {},
  } as HospitalizationTimeInfoOfferInitInfo,
  // 作成者選択
  orX0009Oneway: {
    showId: '',
    // モード
    mode: Or60075Const.DEFAULT.SELECT_MODE_12,
    isDisabled: false,
  } as OrX0009OnewayType,

  // アセスメント複写
  or17391Oneway: {
    periodManagementFlg: '',
  } as Or17391OnewayType,
  // アセスメントマスタ
  or27562Oneway: {
    /** 事業者ID */
    svJigyoId: '',
    /** 分類1 */
    bunrui1Id: '2',
    /** 分類2 */
    bunrui2Id: '12',
    /** 施設ID */
    shisetuId: '',
    /** まとめフラグ */
    summaryFlag: '',
  } as Or27562OnewayType,
  // 計画対象期間フラグ
  plainningPeriodManageFlg: false,

  // 期间選択
  periodSelectOneway: {
    /** 期間ID  */
    sc1Id: '',
  },
  // 履歴選択
  orX0008Oneway: {
    createData: {} as RirekiInfo,
  } as OrX0008OnewayType,
  // 履歴選択
  hisotrySelectOneway: {
    /** 履歴情報 */
    historyInfo: {
      /** 提供ID  */
      teikyouId: '',
      /** 期間ID  */
      sc1Id: '',
      /** 記入日  */
      createYmd: '',
      /** 職員ID  */
      chkShokuId: '',
      /** 履歴総件数  */
      krirekiCnt: '',
      /** 履歴番号  */
      krirekiNo: '',
      /** 担当ケアマネID  */
      tantoShokuId: '',
      /** ケアマネジャー氏名  */
      tantoShokuKnj: '',
      /** 居宅介護支援事業所ID  */
      shienJigyoId: '',
      /** 事業所名  */
      jigyoKnj: '',
      /** 更新回数  */
      modifiedCnt: '',
    } as HistoryInfo1,
    pageBtnAutoDisabled: true,
  },

  // 記入日
  createDateOneway: {
    itemLabel: t('label.entry-date'),
    isRequired: true,
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass d-flex flex-row',
      labelClass: 'ma-1',
    }),
  } as Mo00020OnewayType,

  // 「退院・退所情報記録書」コンテンツ情報
  or06146Oneway: {},
  // タブ
  mo00043OnewayType: {
    tabItems: [],
  } as Mo00043OnewayType,
  or17392Oneway: {
    mode: Or60075Const.DEFAULT.MODE_NORMAL,
  } as Or28992OnewayType,
  or17393Oneway: {
    mode: Or60075Const.DEFAULT.MODE_NORMAL,
  } as Or28992OnewayType,
  mo01338Oneway: {
    value: t('label.record-document-not'),
    valueFontWeight: 'blod',
    customClass: { outerStyle: 'background: rgb(var(--v-theme-background))' } as CustomClass,
  } as Mo01338OnewayType,
  // 削除ダイアログ
  orX0001Oneway: {
    createYmd: '',
    kinouKnj: '',
    selectTabName: '',
    startTabName: '',
    endTabName: '',
  } as OrX0001OnewayType,
})

// アセスメント複写ダイアログ表示フラグ
// const showDialogOr17391 = computed(() => {
//   return Or17391Logic.state.get(or17391.value.uniqueCpId)?.isOpen ?? false
// })

// アセスメントマスタダイアログ表示フラグ
// const showDialogOr27562 = computed(() => {
//   // Or27562のダイアログ開閉状態
//   return Or27562Logic.state.get(or27562.value.uniqueCpId)?.isOpen ?? false
// })

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // ブラウザの「戻る」「進む」ボタンからの遷移時に以前の値を復元したい画面の場合は
  // 画面情報領域のsupplement内のisInitで処理を分岐させて初回表示時のみ画面データの取得を行なう
  // ※値の復元が不要な画面では分岐は不要
  isInitCompleted = false
  if (isInit) {
    // コントロール設定
    await initControls()

    reload()
  }
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or60075StateType>({
  cpId: Or17391Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    // 保存完了処理
    isSaveCompleted: (value) => {
      if (value) {
        // 更新後の計画対象期間ID、履歴IDを再設定
        if (localOneway.periodSelectOneway.sc1Id) {
          localOneway.periodSelectOneway.sc1Id =
            Or60075Logic.data.get(or28992.value.uniqueCpId)?.updateData?.sc1Id ?? ''
        }
        if (localOneway.orX0008Oneway.createData) {
          localOneway.orX0008Oneway.createData.createId =
            Or60075Logic.data.get(or28992.value.uniqueCpId)?.updateData?.teikyouId ?? ''
        }

        // タブ情報再取得
        void reload()
        // 保存完了フラグをリセット
        setState({ isSaveCompleted: false })
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(0)]: or00248.value,
  [Or11871Const.CP_ID]: or11871.value,
  // [Or17391Const.CP_ID(1)]: or17391.value,
  [Or27562Const.CP_ID(0)]: or27562.value, // マスタ他設定画面
  [Or41179Const.CP_ID(0)]: or41179.value, // Or41179:有機体:事業所選択プルダウン
  [OrX0009Const.CP_ID(0)]: orX0009.value, // OrX0009：有機体：作成者（特殊コンポーネント）
  [OrD2002Const.CP_ID(0)]: orD2002.value,
  [Or26257Const.CP_ID(0)]: or26257.value, // Or26257:有機体:職員検索モーダル
  [Or21814Const.CP_ID(1)]: or21814_1.value, // 確認ダイアログ(yes, no, cancel)
  [Or21814Const.CP_ID(2)]: or21814_2.value, // 確認ダイアログ(yes)
  [Or21814Const.CP_ID(3)]: or21814_3.value, // 確認ダイアログ(yes, no)
  [OrX0001Const.CP_ID(0)]: orX0001.value, // GUI04471_［削除確認］画面
  [OrX0007Const.CP_ID(0)]: orX0007.value, // OrX0007：有機体：計画対象期間選択（特殊コンポーネント）
  [OrX0008Const.CP_ID(0)]: orX0008.value, // OrX0008：有機体：履歴選択（特殊コンポーネント）
  [Or06146Const.CP_ID(0)]: or06146.value, // 「退院退所情報記録書」明細情報
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(0)]: or00249.value,
  [Or00094Const.CP_ID(0)]: or00094.value,
})

// ★利用者一覧詳細表示(Or00248)の「領域キー」を設定
Or00248Logic.state.set({
  uniqueCpId: or00248.value.uniqueCpId,
  state: {
    regionKey: props.uniqueCpId, // 「領域キー」を設定
  },
})

systemCommonsStore.setUserSelectZenkaiSvJigyoIdList([''])
systemCommonsStore.setUserSelectSelfId('')

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackUserChange, props.uniqueCpId)

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 新しいデータの値をチェックし、全falseの場合は処理を中断する
    const isAllFalseFlg = Object.values(newValue).every((item) => item === false)
    if (isAllFalseFlg) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      $log.debug('お気に入り')
      setOr11871Event({ favoriteEventFlg: false })
      return
    }
    if (newValue.saveEventFlg) {
      $log.debug('保存')
      // 保存ボタンが押下された場合、保存処理を実行する
      setOr11871Event({ saveEventFlg: false })

      // 削除区分を設定
      deleteKbn.value = Or60075Const.DEFAULT.DELETE_PROCESS_KBN_NORMAL

      setEvent({ saveEventFlg: true })
      return
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      $log.debug('新規')
      setOr11871Event({ createEventFlg: false })

      clickMode.value = 'createNew'
      await createNew()
      return
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
      $log.debug('印刷')
      setOr11871Event({ printEventFlg: false })
      return
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      $log.debug('マスタ他')
      // アセスメントマスタのダイアログを開く
      Or27562Logic.state.set({
        uniqueCpId: or27562.value.uniqueCpId,
        state: { isOpen: true },
      })
      setOr11871Event({ masterEventFlg: false })
      return
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
      clickMode.value = 'delete'
      await _delete()
      setOr11871Event({ deleteEventFlg: false })
      return
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      $log.debug('複写')
      _copy()
      return
    }
  }
)

/**************************************************
 * 関数
 **************************************************/
/**
 *  コントロール初期化
 */
const initControls = async () => {
  isPermissionViewAuth.value = true
  // 子コンポーネントに対して初期設定を行う
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      screenTitleLabel: t('label.discharge-from-hospital-leaving-info'),
      tooltipTextSaveBtn: t('tooltip.save'),
      tooltipTextCreateBtn: t('tooltip.care-plan2-new-btn'),
      tooltipTextPrintBtn: t('tooltip.care-plan2-print-setting-btn'),
      tooltipTextOptionMenuBtn: t('tooltip.regular-master-icon'),
      showFavorite: true,
      showViewSelect: false,
      viewSelectItems: [],
      showSaveBtn: true,
      disabledSaveBtn: !isPermissionViewAuth.value, //共通処理の保存権限チェックを行う
      disabledPrintBtn: await hasPrintAuth(Or60075Const.DEFAULT.LINK_AUTH), //共通処理の印刷権限チェックを行う
      showCreateBtn: true,
      showCreateMenuCopy: false,
      showPrintBtn: true,
      showMasterBtn: true,
      showOptionMenuBtn: true,
      showOptionMenuDelete: true,
    },
  })
  clickMode.value = ''

  local.createDate.value =
    systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
  createDateOld.value = local.createDate.value

  // 事業所選択リストを初期化
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
    },
  })

  // 確認ダイアログを初期化(yes, no, cancel)
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを初期化(yes)
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
    },
  })

  // 確認ダイアログを初期化(yes, no)
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
    },
  })

  localOneway.mo00043OnewayType.tabItems = [
    {
      id: Or17392Const.DEFAULT.TAB_ID,
      title: t('label.outer'),
    },
    {
      id: Or17393Const.DEFAULT.TAB_ID,
      title: t('label.inner'),
    },
  ]
}

/**
 * Or13844イベント発火
 *
 * @param event - イベント
 */
function setOr11871Event(event: Record<string, boolean>) {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 * イベント発火
 *
 * @param event - イベント
 */
function setEvent(event: Record<string, boolean>) {
  // 複写の場合
  let copyData: object = {}
  if (event.copyEventFlg === true) {
    const or17391Data = Or17391Logic.data.get(or17391.value.uniqueCpId)
    copyData = or17391Data?.resData?.tabData ?? {}
  }

  // 画面共通情報を設定
  setCommonInfo({
    /** 履歴更新回数 */
    historyModifiedCnt: historyModifiedCnt.value,
    /** 事業所ID */
    jigyoId: systemCommonsStore.getSvJigyoId,
    /** 計画対象期間ID */
    sc1Id: localOneway.periodSelectOneway.sc1Id,
    /** アセスメントID */
    teikyouId: localOneway.orX0008Oneway.createData.createId,
    /** 作成者ID */
    createUserId: localOneway.orX0009Oneway.createData?.staffId?.toString() ?? '',
    /** 作成日 */
    createYmd: local.createDate.value,
    /** 複写データ */
    copyData: copyData,
    /** 複写データ */
    deleteKbn: deleteKbn.value,
  })

  Or60075Logic.event.set({
    uniqueCpId: props.uniqueCpId,
    events: {
      /** 再表示発火フラグ */
      isRefresh: event.isRefresh,
      /** 作成日変更フラグ */
      isCreateDateChanged: event.isCreateDateChanged,
      /** お気に入りイベント発火フラグ */
      favoriteEventFlg: event.favoriteEventFlg,
      /** 保存イベント発火フラグ */
      saveEventFlg: event.saveEventFlg,
      // /** 保存のみイベント発火フラグ */
      // saveOnlyEventFlg: event.saveOnlyEventFlg,
      /** 新規イベント発火フラグ */
      createEventFlg: event.createEventFlg,
      /** 複写イベント発火フラグ */
      copyEventFlg: event.copyEventFlg,
      // /** 保存のみイベント発火フラグ */
      // tabChangeSaveEventFlg: event.tabChangeSaveEventFlg,
      /** 印刷イベント発火フラグ */
      printEventFlg: event.printEventFlg,
      /** マスタ他イベント発火フラグ */
      masterEventFlg: event.masterEventFlg,
      /** 削除イベント発火フラグ */
      deleteEventFlg: event.deleteEventFlg,
    },
  })
}

/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 変更後の利用者番号
 */
function callbackUserChange(newSelfId: string) {
  if (newSelfId !== '' && userId.value === '') {
    // 利用者変更処理
    void userChange(newSelfId)
    return
  }

  // 変更がない場合、スキップ
  if (newSelfId === userId.value) {
    return
  }

  // 利用者変更処理
  void userChange(newSelfId)
}

/**
 *  画面初期情報再取得
 */
function reload() {
  // 計画対象期間がない場合、リロードしない
  if (
    plannningPeriodManageFlg.value === Or60075Const.DEFAULT.PLANNING_PERIOD_MANAGE &&
    (localOneway.periodSelectOneway.sc1Id === undefined ||
      localOneway.periodSelectOneway.sc1Id === '')
  ) {
    return
  }

  // 作成者選択アイコンボタンを活性
  localOneway.orX0009Oneway.isDisabled = false
  // 作成日を活性
  localOneway.createDateOneway.disabled = false
  // 削除区分を設定
  deleteKbn.value = Or60075Const.DEFAULT.DELETE_PROCESS_KBN_NORMAL

  // 画面共通情報を設定
  setCommonInfo({
    /** 履歴更新回数 */
    historyModifiedCnt: historyModifiedCnt.value,
    /** 事業所ID */
    jigyoId: systemCommonsStore.getSvJigyoId,
    /** 利用者ID */
    userId: userId.value,
    /** 計画対象期間ID */
    sc1Id: localOneway.periodSelectOneway.sc1Id,
    /** teikyouIdID */
    teikyouId: localOneway.orX0008Oneway.createData.createId,
    /** 作成者ID */
    createUserId: localOneway.orX0009Oneway.createData?.staffId?.toString() ?? '',
    /** 作成日 */
    createYmd: local.createDate.value,
  })

  // 画面情報再取得
  setEvent({ isRefresh: true })
}

/**
 *  画面共通情報を設定
 *
 * @param data - 設定情報
 */
function setCommonInfo(data: Or60075Type) {
  const existedData = Or60075Logic.data.get(props.uniqueCpId)
  const newData = {
    /** 履歴更新回数 */
    historyModifiedCnt: historyModifiedCnt.value,
    /** 選択中タブ */
    activeTabId: data.activeTabId ?? existedData?.activeTabId,
    /** 事業所ID */
    jigyoId: data.jigyoId ?? existedData?.jigyoId,
    /** 計画期間ID */
    sc1Id: data.sc1Id ?? existedData?.sc1Id,
    /** アセスメントID */
    teikyouId: data.teikyouId ?? existedData?.teikyouId,
    /** 作成者ID */
    createUserId: data.createUserId ?? existedData?.createUserId,
    /** 履歴作成日 */
    createYmd: data.createYmd ?? existedData?.createYmd,
    /** 複写データ */
    copyData: data.copyData ?? existedData?.copyData,
    /** 削除区分 */
    deleteKbn: deleteKbn.value ?? existedData?.deleteKbn,
    /** 介護保険権限フラグ */
    nursingCareInsuranceAuthorityFlag:
      data.nursingCareInsuranceAuthorityFlag ?? existedData?.nursingCareInsuranceAuthorityFlag,
    /** 親族関係者権限フラグ */
    kinshipAuthorityFlag: data.kinshipAuthorityFlag ?? existedData?.kinshipAuthorityFlag,
    /** 既往歴権限フラグ */
    pastMedicalHistoryAuthorityFlag:
      data.pastMedicalHistoryAuthorityFlag ?? existedData?.pastMedicalHistoryAuthorityFlag,
  } as Or60075Type
}

/**
 *  計画対象期間チェンジ設定
 *
 * @param sc1Id - 計画対象期間ID
 *
 * @param pageFlag - 期間処理区分
 */
async function getPlanningPeriodInfo(sc1Id: string, pageFlag: string) {
  try {
    // // バックエンドAPIから初期情報取得
    // const inputData: HospitalizationTimeInfoOfferPeriodSelectInEntity = {
    //   /** 事業所ID */
    //   svJigyoId: jigyoId.value,
    //   /** システムコード */
    //   gsysCd: systemCommonsStore.getSystemCode ?? '',
    //   /** 利用者ID */
    //   userid: userId.value,
    //   /** 種別ID */
    //   syubetsuId: systemCommonsStore.getSyubetu ?? '',
    //   /** 施設ID */
    //   shisetuId: systemCommonsStore.getShisetuId ?? '',
    //   /** 期間ID  */
    //   sc1Id: sc1Id ? sc1Id : '',
    //   // /** 計画期間ページ区分  */
    //   // pageFlag: pageFlag ?? '0',
    // }
    const inputData = {}
    const resData: HospitalizationTimeInfoOfferPeriodSelectOutEntity =
      await ScreenRepository.select('hospitalizationTimeInfoOfferPeriodSelect', inputData)
    /** =============画面情報を設定============= */
    if (resData?.data) {
      local.periodInfo = {
        ...resData.data,
        /** 計画期間情報 */
        planPeriodInfo: undefined,
        /** 履歴情報 */
        historyInfo: undefined,
      }
      // 画面共通情報を設定
      setCommonInfo({
        // 介護保険権限
        nursingCareInsuranceAuthorityFlag: resData.data.nursingCareInsuranceAuthorityFlag,
        // 親族関係者権限フラグ
        kinshipAuthorityFlag: resData.data.kinshipAuthorityFlag,
        // 既往歴権限フラグ
        pastMedicalHistoryAuthorityFlag: resData.data.pastMedicalHistoryAuthorityFlag,
      })
      /** ------------計画対象期間を設定------------ */
      // 計画期間管理フラグ設定
      plannningPeriodManageFlg.value = resData.data.kikanKanriFlg

      // 計画対象期間管理フラグが「1:管理する」場合、計画対象期間情報を設定
      if (
        plannningPeriodManageFlg.value === Or60075Const.DEFAULT.PLANNING_PERIOD_MANAGE &&
        resData?.data?.planPeriodInfo?.sc1Id &&
        resData.data.planPeriodInfo.periodCnt !== '0'
      ) {
        localOneway.periodSelectOneway.sc1Id = resData.data.planPeriodInfo.sc1Id
        localOneway.orX0007Oneway.planTargetPeriodData = {
          planTargetPeriodId: Number(resData.data.planPeriodInfo.sc1Id),
          planTargetPeriod:
            resData.data.planPeriodInfo.startYmd + '～' + resData.data.planPeriodInfo.endYmd,
          currentIndex: Number(resData.data.planPeriodInfo.periodNo),
          totalCount: Number(resData.data.planPeriodInfo.periodCnt),
        }
        isHistoryShow.value = true
      } else if (
        plannningPeriodManageFlg.value === Or60075Const.DEFAULT.PLANNING_PERIOD_NO_MANAGE &&
        resData?.data?.planPeriodInfo?.sc1Id &&
        resData.data.planPeriodInfo.periodCnt !== '0'
      ) {
        localOneway.periodSelectOneway.sc1Id = resData.data.planPeriodInfo.sc1Id ?? ''
        localOneway.orX0007Oneway.planTargetPeriodData = {
          planTargetPeriodId: 0,
          planTargetPeriod: '',
          currentIndex: 0,
          totalCount: 0,
        }
        isHistoryShow.value = true
      } else {
        localOneway.periodSelectOneway.sc1Id = resData.data.planPeriodInfo.sc1Id ?? ''
        localOneway.orX0007Oneway.planTargetPeriodData = {
          planTargetPeriodId: Number(resData.data.planPeriodInfo.sc1Id ?? 0),
          planTargetPeriod: '',
          currentIndex: 0,
          totalCount: 0,
        }
        isHistoryShow.value = false
      }
      /** ------------履歴情報を設定------------ */
      // 表示中計画対象期間の履歴情報取得
      if (resData?.data?.historyInfo.teikyouId) {
        localOneway.orX0008Oneway = {
          createData: {
            /** 提供ID */
            createId: resData.data.historyInfo.teikyouId,
            /** 作成日 */
            createDate: resData.data.historyInfo.createYmd ?? '',
            /** 職員ID */
            staffId: resData.data.historyInfo.chkShokuId ?? '',
            /** 作成者名（職員名） */
            staffName: resData.data.historyInfo.chkShokuName ?? '',
            /** 履歴番号 */
            currentIndex: Number(resData.data.historyInfo.krirekiNo ?? 1),
            /** 履歴総件数 */
            totalCount: Number(resData.data.historyInfo.krirekiCnt ?? 1),
          },
          /**
           *事業所ID
           */
          officeId: resData.data.historyInfo.shienJigyoId ?? '',
          /**
           *画面ID
           */
          screenID: Or60075Const.DEFAULT.GUI,
          /**
           *利用者ID
           */
          useId: userId.value,
          /**
           *計画対象期間ID
           */
          sc1Id: resData.data.historyInfo.sc1Id ?? '',
          /**
           *計画書１ID
           */
          plan1Id: '',
          /**
           * モード
           */
          mode: '',
        }
        // 履歴更新回数
        historyModifiedCnt.value = resData.data.historyInfo.modifiedCnt ?? ''
        // 作成者を設定
        localOneway.orX0009Oneway.createData = {
          staffId: parseInt(resData.data.historyInfo.chkShokuId ?? '0'),
          staffName: resData.data.historyInfo.chkShokuName ?? '',
        } as OrX0009OnewayType['createData']
        // 作成日を設定
        local.createDate.value = resData.data.historyInfo.createYmd ?? ''
        createDateOld.value = local.createDate.value
      } else {
        localOneway.orX0008Oneway = {
          createData: {
            /** 提供ID */
            createId: resData.data.historyInfo.teikyouId ?? '',
            /** 作成日 */
            createDate: resData.data.historyInfo.createYmd ?? '',
            /** 職員ID */
            staffId: resData.data.historyInfo.chkShokuId ?? '',
            /** 作成者名（職員名） */
            staffName: resData.data.historyInfo.chkShokuName ?? '',
            /** 履歴番号 */
            currentIndex: 1,
            /** 履歴総件数 */
            totalCount: 1,
          },
          /**
           *事業所ID
           */
          officeId: resData.data.historyInfo.shienJigyoId ?? '',
          /**
           *画面ID
           */
          screenID: Or60075Const.DEFAULT.GUI,
          /**
           *利用者ID
           */
          useId: userId.value,
          /**
           *計画対象期間ID
           */
          sc1Id: resData.data.historyInfo.sc1Id ?? '',
          /**
           *計画書１ID
           */
          plan1Id: '',
          /**
           * モード
           */
          mode: '',
        }
        // 履歴更新回数
        historyModifiedCnt.value = ''

        // 作成者を設定
        localOneway.orX0009Oneway.createData = {
          staffId: 0,
          staffName: '',
        } as OrX0009OnewayType['createData']
        // 作成日を設定
        local.createDate.value =
          systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
        createDateOld.value = local.createDate.value
      }
    } else {
      localOneway.orX0007Oneway.planTargetPeriodData = {
        planTargetPeriodId: 0,
        planTargetPeriod: '',
        currentIndex: 0,
        totalCount: 0,
      }
      isHistoryShow.value = false
    }
  } catch (e: unknown) {
    console.log(e)
  }
}

/**
 *  履歴情報を取得
 *
 * @param teikyouId - 履歴ID
 *
 * @param kikanFlag - 履歴処理区分
 */
async function getHistoryInfo(teikyouId: string, kikanFlag: string) {
  try {
    // バックエンドAPIから初期情報取得
    const inputData: HospitalizationTimeInfoOfferHistorySelectInEntity = {
      /** 事業所ID */
      svJigyoId: jigyoId.value,
      /** 利用者ID */
      userid: userId.value,
      /** 提供ID  */
      cc1Id: teikyouId ? teikyouId : '',
      /** 計画期間ID */
      sc1Id: localOneway.periodSelectOneway.sc1Id ?? '',
      /** 履歴変更区分 */
      kikanFlag: kikanFlag ?? '0',
    }
    const resData: HospitalizationTimeInfoOfferHistorySelectOutEntity =
      await ScreenRepository.select('hospitalizationTimeInfoOfferHistorySelect', inputData)

    // 画面情報を設定
    if (resData?.data) {
      if (resData?.data?.historyInfo?.teikyouId) {
        // 履歴IDをバックアップ
        teikyouIdOld.value = resData?.data?.historyInfo.teikyouId

        // 表示中計画対象期間の履歴情報取得
        localOneway.orX0008Oneway = {
          createData: {
            /** 提供ID */
            createId: resData.data.historyInfo.teikyouId,
            /** 作成日 */
            createDate: resData.data.historyInfo.createYmd ?? '',
            /** 職員ID */
            staffId: resData.data.historyInfo.chkShokuId ?? '',
            /** 作成者名（職員名） */
            staffName: resData.data.historyInfo.chkShokuName ?? '',
            /** 履歴番号 */
            currentIndex: Number(resData.data.historyInfo.krirekiNo ?? 1),
            /** 履歴総件数 */
            totalCount: Number(resData.data.historyInfo.krirekiCnt ?? 1),
          },
          /**
           *事業所ID
           */
          officeId: resData.data.historyInfo.shienJigyoId ?? '',
          /**
           *画面ID
           */
          screenID: Or60075Const.DEFAULT.GUI,
          /**
           *利用者ID
           */
          useId: userId.value,
          /**
           *計画対象期間ID
           */
          sc1Id: resData.data.historyInfo.sc1Id ?? '',
          /**
           *計画書１ID
           */
          plan1Id: '',
          /**
           * モード
           */
          mode: '',
        }
        // 履歴更新回数
        historyModifiedCnt.value = resData.data.historyInfo.modifiedCnt ?? ''

        // 作成者を設定
        localOneway.orX0009Oneway.createData = {
          staffId: parseInt(resData.data.historyInfo.chkShokuId ?? '0'),
          staffName: resData.data.historyInfo.chkShokuName ?? '',
        } as OrX0009OnewayType['createData']
        // 作成日を設定
        local.createDate.value = resData.data.historyInfo.createYmd ?? ''
        createDateOld.value = local.createDate.value
      } else {
        localOneway.orX0008Oneway = {
          createData: {
            /** 提供ID */
            createId: '',
            /** 作成日 */
            createDate: resData.data.historyInfo.createYmd ?? '',
            /** 職員ID */
            staffId: resData.data.historyInfo.chkShokuId ?? '',
            /** 作成者名（職員名） */
            staffName: resData.data.historyInfo.chkShokuName ?? '',
            /** 履歴番号 */
            currentIndex: Number(resData.data.historyInfo.krirekiNo ?? 1),
            /** 履歴総件数 */
            totalCount: Number(resData.data.historyInfo.krirekiCnt ?? 1),
          },
          /**
           *事業所ID
           */
          officeId: resData.data.historyInfo.shienJigyoId ?? '',
          /**
           *画面ID
           */
          screenID: Or60075Const.DEFAULT.GUI,
          /**
           *利用者ID
           */
          useId: userId.value,
          /**
           *計画対象期間ID
           */
          sc1Id: resData.data.historyInfo.sc1Id ?? '',
          /**
           *計画書１ID
           */
          plan1Id: '',
          /**
           * モード
           */
          mode: '',
        }

        // 履歴更新回数
        historyModifiedCnt.value = ''

        // 作成者を設定
        localOneway.orX0009Oneway.createData = {
          staffId: 0,
          staffName: '',
        } as OrX0009OnewayType['createData']
        // 作成日を設定
        local.createDate.value =
          systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
        createDateOld.value = local.createDate.value
      }
    }
  } catch (e: unknown) {
    console.log(e)
  }
}

/**
 *  データ変更チェック
 */
async function checkChange(): Promise<number> {
  let result = 0
  // 画面変更チェック
  if (isEdit.value) {
    if (!isPermissionViewAuth.value) {
      // 画面入力変更あり、かつ保存権限がない場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog2(t('message.w-com-10006'))
      switch (dialogResult) {
        case 'yes': {
          // 編集を破棄して処理を続く
          result = 1
        }
      }
    } else {
      // 画面入力変更あり、かつ保存権限がある場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
      switch (dialogResult) {
        case 'yes': {
          // 編集を保存して処理を続く
          result = 2
          break
        }
        case 'no':
          // 編集を破棄して処理を続く
          result = 1
          break
        case 'cancel':
          // キャンセル選択時は何もしない
          result = 3
          break
      }
    }
  } else {
    // 変更なしの場合は何もしないまま処理を続く
    result = 0
  }
  return result
}

/**
 * 利用者変更処理
 *
 * @param newUserId - 新しい利用者ID
 */
async function userChange(newUserId: string) {
  isLoading.value = true

  // 初期化の場合、チェックなしで処理を続行
  if (isInitCompleted === false) {
    userId.value = newUserId
    // 初期情報を取得
    await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
    // 画面情報再取得
    void reload()

    isInitCompleted = true
    isLoading.value = false
    return
  }

  // 画面変更チェック
  switch (await checkChange()) {
    case 0: {
      // 画面入力変更なし
      userId.value = newUserId
      systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)
      // 初期情報を取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 画面情報再取得
      void reload()

      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      userId.value = newUserId
      systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)
      // 初期情報を取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 画面情報再取得
      void reload()
      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存して処理を続行
      userId.value = newUserId
      systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)
      // 初期情報を取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 保存
      setEvent({ saveEventFlg: true })
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }

  isLoading.value = false
}

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (jigyoId.value === '') {
    jigyoId.value = newJigyoId
    return
  }

  // 事業所変更がない場合、スキップ
  if (newJigyoId === jigyoId.value) {
    return
  }

  // まず変更前の事業所を保持
  void setJigyo(jigyoId.value)

  // 事業所変更処理
  void jigyoChange(newJigyoId)
}

/**
 * 事業所を選択
 *
 * @param jigyoId - 事業所ID
 */
async function setJigyo(jigyoId: string) {
  await nextTick()

  Or41179Logic.data.set({
    uniqueCpId: or41179.value.uniqueCpId,
    value: {
      modelValue: jigyoId,
    },
  })
}

/**
 * 事業所変更処理
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function jigyoChange(newJigyoId: string) {
  // 画面変更チェック
  switch (await checkChange()) {
    case 0: {
      // 画面入力変更なし
      jigyoId.value = newJigyoId
      // 変更後の事業所に設定
      await setJigyo(newJigyoId)
      // 画面情報再取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 画面情報再取得
      void reload()

      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      jigyoId.value = newJigyoId
      // 変更後の事業所に設定
      await setJigyo(newJigyoId)

      // 画面情報再取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 画面情報再取得
      void reload()

      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存して処理を続行
      jigyoId.value = newJigyoId
      // 変更後の事業所に設定
      await setJigyo(newJigyoId)

      // 画面情報再取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // はい選択時は入力内容を保存する
      setEvent({ saveEventFlg: true })
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog1(paramDialogText: string): Promise<'yes' | 'no' | 'cancel'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no' | 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog2(paramDialogText: string) {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise(() => {
    watch(
      () => Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen,
      () => {
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_2.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
async function openConfirmDialog3(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_3.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_3.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_3.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
/**
 * 複写ボタンクリック
 *
 */
function copyBtnClick() {
  // 保存ボタンが押下された場合、保存処理を実行する
  setOr11871Event({ copyEventFlg: true })
}

/**
 * 新規処理
 *
 */
async function createNew() {
  if (plannningPeriodManageFlg.value === Or60075Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
    if (localOneway.periodSelectOneway.sc1Id) {
      if (localOneway.orX0008Oneway.createData.createId) {
        // 画面変更チェック
        if (isEdit.value) {
          // 画面入力変更がある場合
          // 確認ダイアログ表示
          const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
          switch (dialogResult) {
            case 'yes': {
              // はい選択時は入力内容を保存する
              setEvent({ saveEventFlg: true })

              // 履歴件数 + 1
              void historyNew()
              break
            }
            case 'no': {
              // いいえ選択時は編集内容を破棄するので何もしない
              // 履歴件数 + 1
              void historyNew()
              break
            }
            case 'cancel':
              // キャンセル
              break
          }

          return
        } else {
          // 履歴件数 + 1
          void historyNew()
        }
      } else {
        // 計画期間情報がある、当該画面データが保存されない場合
        // 確認ダイアログ表示
        await openConfirmDialog2(t('message.i-cmn-11265', [t('label.hospitalization-info')]))

        return
      }
    } else {
      // 計画期間情報がない場合
      // 確認ダイアログ表示
      await openConfirmDialog2(t('message.i-cmn-11300'))

      return
    }
  }
}

/**
 * 複写処理
 *
 */
function _copy() {
  // 複写画面パラメータを設定
  or17391.value.or00249UniqueCpId = or00249.value.uniqueCpId
  or17391.value.or00094UniqueCpId = or00094.value.uniqueCpId
  localOneway.or17391Oneway.periodManagementFlg = plannningPeriodManageFlg.value
  copyComponentKey.value = Date.now().toString()
  const tabItems = [] as {
    /** id */
    id: string
    /** id */
    name: string
  }[]
  localOneway.mo00043OnewayType.tabItems.forEach((item) => {
    tabItems.push({
      id: item.id,
      name: item.title,
    })
  })
  localOneway.or17391Oneway = {
    /** 期間管理フラグ */
    periodManagementFlg: plannningPeriodManageFlg.value,
    /** タブリスト */
    tabItems: tabItems,
    /** 事業所IDリスト*/
    svJigyoIdList: Array.from(systemCommonsStore.getSvJigyoIdList) ?? [],
    /** 種別ID */
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    /** 利用者ID */
    userId: userId.value,
    /** 基準日 */
    createYmd: systemCommonsStore.getSystemDate ?? '',
    /** 施設ID */
    shisetuId: systemCommonsStore.getShisetuId ?? '',
  }
  // 複写画面を開く
  Or17391Logic.state.set({
    uniqueCpId: or17391.value.uniqueCpId,
    state: { isOpen: true },
  })
  setOr11871Event({ copyEventFlg: false })
}

/**
 * 履歴を更新
 *
 */
async function historyNew() {
  // 履歴件数 + 1
  let rirekiCnt: number = localOneway.orX0008Oneway.createData?.totalCount
    ? localOneway.orX0008Oneway.createData?.totalCount
    : 0
  rirekiCnt = rirekiCnt + 1
  localOneway.orX0008Oneway.createData = {
    /** 提供ID */
    createId: '',
    /** 作成日 */
    createDate: '',
    /** 職員ID */
    staffId: '',
    /** 作成者名（職員名） */
    staffName: '',
    /** 履歴番号 */
    currentIndex: rirekiCnt,
    /** 履歴総件数 */
    totalCount: rirekiCnt,
  }
  setEvent({ createEventFlg: true })
}

/**
 * 削除処理
 *
 */
function _delete() {
  // メッセージ 11326
  // 現在表示している[{0}］の{1}データを削除します。よろしいですか？\r\n現在表示している画面のみ削除する。\r\n※{2}画面を削除します。\r\n表示している画面を履歴ごと削除する。\r\n※{3}までの全ての項目を削除します。
  // 削除確認ダイアログを初期化(i.cmn.11326)
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11326', [
        local.createDate.value,
        t('label.hospitalization-info'),
      ]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_3.value.uniqueCpId)?.isOpen,
      async () => {
        const event = Or21814Logic.event.get(or21814_3.value.uniqueCpId)

        let result = Or60075Const.DEFAULT.DIALOG_RESULT_CANCEL
        if (event?.firstBtnClickFlg) {
          result = Or60075Const.DEFAULT.DIALOG_RESULT_YES
          // 削除実施後、作成者選択アイコンボタンを非活性
          localOneway.orX0009Oneway.isDisabled = true
          // 削除実施後、作成日を非活性
          localOneway.createDateOneway.disabled = true
          //共通処理の复写権限チェックを行う
          local.copyDisabled = true
          Or11871Logic.state.set({
            uniqueCpId: or11871.value.uniqueCpId,
            state: {
              disabledCreateBtn: true, //共通処理の新规権限チェックを行う
              disabledPrintBtn: true, //共通処理の印刷権限チェックを行う
              disabledOptionMenuDelete: true, //共通処理の削除権限チェックを行う
            },
          })
          setEvent({ deleteEventFlg: true })
        }
        if (event?.secondBtnClickFlg) {
          result = Or60075Const.DEFAULT.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_3.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 作成日変更処理
 *
 * @param mo00020 - 作成日
 */
async function createDateChange(mo00020: Mo00020Type) {
  if (mo00020.value === createDateOld.value) {
    // 作成日が変更されていない場合は何もしない
    return
  }

  if (createDateOld.value === '' || mo00020.value === '') {
    // 履歴に作成日がない、または入力されない場合は何もしない
    return
  }

  // H21/4改訂版（改訂フラグ＝4）AND  作成日が2018年3月31日以前（平成30年区分=0）AND 入力した作成日>= '2018/04/01'の場合
  if (
    parseInt(createDateOld.value.replaceAll('/', '')) < 20180331 &&
    parseInt(mo00020.value.replaceAll('/', '')) >= 20180401
  ) {
    // 確認ダイアログ表示
    const dialogResult = await openConfirmDialog3(t('message.i-cmn-11324'))
    switch (dialogResult) {
      case 'yes': {
        createDateOld.value = mo00020.value
        // はい選択時は入力内容を保存する
        if (isEdit.value) {
          setEvent({ saveEventFlg: true })
        }

        // 作成日変更処理
        setEvent({ isCreateDateChanged: true })
        break
      }
      case 'no': {
        // いいえ選択時入力前の値に戻る
        mo00020.value = createDateOld.value
        break
      }
    }
  }

  // H21/４改訂版（改訂フラグ＝4）AND  作成日が2018年4月1日以降（平成30年区分=1）AND 入力した作成日< '2018/04/01'の場合
  if (
    parseInt(createDateOld.value.replaceAll('/', '')) >= 20180401 &&
    parseInt(mo00020.value.replaceAll('/', '')) < 20180401
  ) {
    // 確認ダイアログ表示
    const dialogResult = await openConfirmDialog3(t('message.i-cmn-11324'))
    switch (dialogResult) {
      case 'yes': {
        createDateOld.value = mo00020.value
        // はい選択時は入力内容を保存する
        if (isEdit.value) {
          setEvent({ saveEventFlg: true })
        }

        // 作成日変更処理
        setEvent({ isCreateDateChanged: true })
        break
      }
      case 'no': {
        // いいえ選択時入力前の値に戻る
        mo00020.value = createDateOld.value
        break
      }
    }
  }
}

function handleSave() {
  setEvent({ saveEventFlg: true })
}

/**
 * 削除ダイアログ選択変更処理
 *
 * @param orX0001Value - 戻り値
 */
function deleteDialogChange(orX0001Value: OrX0001Type) {
  if ('0' !== orX0001Value.deleteSyubetsu) {
    // 確定ボタン押下の場合
    deleteKbn.value = orX0001Value.deleteSyubetsu
    // 削除実施後、作成者選択アイコンボタンを非活性
    localOneway.orX0009Oneway.isDisabled = true
    // 削除実施後、作成日を非活性
    localOneway.createDateOneway.disabled = true
  } else {
    // キャンセル
    // 何もしない
  }
}

/**
 * AC014_「計画対象期間-前へアイコンボタン」押下
 * AC015_「計画対象期間-次へアイコンボタン」押下
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    const { currentIndex, totalCount } = localOneway.orX0007Oneway.planTargetPeriodData
    const sc1Id =
      newValue.planTargetPeriodId !== '0'
        ? newValue.planTargetPeriodId
        : (localOneway.periodSelectOneway.sc1Id ?? '')
    if (newValue?.PlanTargetPeriodUpdateFlg === '0') {
      await getPlanningPeriodInfo(sc1Id, Or60075Const.DEFAULT.PERIOD_KBN_OPEN)
      // 画面情報再取得
      void reload()
    } else if (newValue?.PlanTargetPeriodUpdateFlg === '1') {
      if (currentIndex === 1) {
        await openConfirmDialog2(t('message.i-cmn-11262'))
        // 処理終了
        return
      }
      await getPlanningPeriodInfo(sc1Id, Or60075Const.DEFAULT.PERIOD_KBN_PREV)
      // 画面情報再取得
      void reload()
    } else if (newValue?.PlanTargetPeriodUpdateFlg === '2') {
      if (currentIndex === totalCount) {
        await openConfirmDialog2(t('message.i-cmn-11263'))
        // 処理終了
        return
      }
      await getPlanningPeriodInfo(sc1Id, Or60075Const.DEFAULT.PERIOD_KBN_NEXT)
      // 画面情報再取得
      void reload()
    }
  },
  { deep: true }
)

watch(
  () => OrX0008Logic.data.get(orX0008.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    const { currentIndex, totalCount } = localOneway.orX0008Oneway.createData
    const { createId, createUpateFlg } = newValue
    const teikyouId =
      createId !== '' ? createId : (localOneway.orX0008Oneway.createData.createId ?? '')
    if (createUpateFlg === '0') {
      // 画面入力変更あり、編集を保存して処理を続行
      // 履歴情報を再取得
      await getHistoryInfo(teikyouId, Or60075Const.DEFAULT.HISTORY_KBN_OPEN)
      // 画面情報再取得
      void reload()
    } else if (createUpateFlg === '1') {
      if (currentIndex === 1) {
        // 処理終了
        return
      }
      await getHistoryInfo(teikyouId, Or60075Const.DEFAULT.HISTORY_KBN_PREV)
      // 画面情報再取得
      void reload()
    } else if (createUpateFlg === '2') {
      if (currentIndex === totalCount) {
        // 処理終了
        return
      }
      await getHistoryInfo(teikyouId, Or60075Const.DEFAULT.HISTORY_KBN_NEXT)
      // 画面情報再取得
      void reload()
    }
  },
  { deep: true }
)
/**
 * 作成者変更監視
 */
watch(
  () => OrX0009Logic.data.get(orX0009.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // ・親情報を設定 ※親情報が、すべてのタブ間で共有する
      // 親情報.作成者ID = 画面.作成者ID
      setCommonInfo({ createUserId: newValue.staffId })
    }
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <!-- Or11871：有機体：画面メニューエリア -->
  <!-- ローディング -->
  <v-overlay
    :model-value="isLoading"
    :persistent="false"
    class="align-center justify-center"
    ><v-progress-circular
      indeterminate
      color="primary"
    ></v-progress-circular
  ></v-overlay>
  <c-v-sheet class="view d-flex flex-column h-100">
    <!-- 操作ボタンエリア -->
    <g-base-or11871 v-bind="or11871">
      <template #createItems>
        <c-v-list-item
          :title="t('btn.copy')"
          prepend-icon="file_copy"
          :disabled="local.copyDisabled"
          @click="copyBtnClick"
        />
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="t('tooltip.care-plan2-copy-btn')"
        ></c-v-tooltip>
      </template>
    </g-base-or11871>

    <c-v-row
      no-gutters
      class="main-Content d-flex flex-0-1 h-100 overflow-hidden"
    >
      <!-- ナビゲーションエリア -->
      <c-v-col cols="2">
        <!-- 利用者選択一覧 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>
      <!-- コンテンツエリア -->
      <c-v-col class="main-right d-flex flex-column overflow-hidden h-100 ml-2">
        <!-- 上段 -->
        <c-v-row no-gutters>
          <c-v-col>
            <c-v-row no-gutters>
              <!-- 事業所選択画面 -->
              <g-base-or-41179 v-bind="or41179" />
            </c-v-row>
            <c-v-row no-gutters>
              <c-v-col
                v-show="plannningPeriodManageFlg === Or60075Const.DEFAULT.PLANNING_PERIOD_MANAGE"
                cols="auto mr-4 bg-box"
              >
                <!-- 計画対象期間 -->
                <g-custom-orX0007
                  v-bind="orX0007"
                  v-model="local.orX0007"
                  :is-edit="isEdit"
                  :oneway-model-value="localOneway.orX0007Oneway"
                  :parent-method="handleSave"
                />
              </c-v-col>
              <c-v-col
                v-show="isHistoryShow"
                cols="auto mr-4 bg-box"
              >
                <!-- 履歴 -->
                <g-custom-orX0008
                  v-bind="orX0008"
                  v-model="local.orX0008"
                  :is-edit="isEdit"
                  :oneway-model-value="localOneway.orX0008Oneway"
                  :unique-cp-id="orX0008.uniqueCpId"
                  :parent-method="handleSave"
                />
              </c-v-col>
              <c-v-col
                v-show="isHistoryShow"
                cols="auto mr-4 bg-box"
              >
                <!-- 作成者 -->
                <g-custom-orX0009
                  v-bind="orX0009"
                  :oneway-model-value="localOneway.orX0009Oneway"
                />
              </c-v-col>
              <c-v-col
                v-show="isHistoryShow"
                cols="auto mr-4"
              >
                <!-- 記入日 -->
                <base-mo00020
                  v-model="local.createDate"
                  :oneway-model-value="localOneway.createDateOneway"
                  @update:model-value="createDateChange"
                />
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <!-- 中段 -->
        <c-v-row
          no-gutters
          class="middleContent flex-1-1 h-100 overflow-y-auto"
        >
          <g-custom-or-06146
            v-bind="or06146"
            :oneway-model-value="localOneway.or06146Oneway"
          />
        </c-v-row>
        <!-- 下段 -->
        <c-v-row no-gutters>
          <c-v-col class="pa-2">
            <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway" />
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>

    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_1" />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_2" />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_3" />
    <!--  削除確認画面 -->
    <g-custom-or-x-0001
      v-bind="orX0001"
      v-model="local.orX0001Type"
      :oneway-model-value="localOneway.orX0001Oneway"
      @update:model-value="deleteDialogChange"
    ></g-custom-or-x-0001>
  </c-v-sheet>
</template>

<style scoped lang="scss">
.view {
  background-color: transparent;
}

:deep(.v-window__container) {
  height: 100%;
}

.main-Content {
  .main-left {
    max-width: 20%;
  }

  .main-right {
    .middleContent {
      min-height: 0;

      .v-window {
        width: 100%;
      }
    }
  }
}

.createDateOuterClass {
  width: auto !important;
  background: rgb(var(--v-theme-background));

  :deep(.must-badge) {
    margin-left: unset;
  }

  :deep(.v-input__details) {
    display: none;
  }
}
.bg-box {
  display: flex;
  align-items: center;
  :deep(.v-sheet) {
    background-color: rgb(var(--v-theme-background)) !important;
  }
}
</style>
