<script setup lang="ts">
/**
 * Or10444:処理ロジック
 * GUI01131_［印刷設定］画面
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> DAO VAN DUONG
 */
import { computed, onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10444Const } from '~/components/custom-components/organisms/Or10444/Or10444.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CustomClass } from '@/types/CustomClassType'
import { Or10016Logic } from '~/components/custom-components/organisms/Or10016/Or10016.logic'
import { Or10016Const } from '~/components/custom-components/organisms/Or10016/Or10016.constants'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import { reportOutputType } from '~/utils/useReportUtils'
import {
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
  useReportUtils
} from '#imports'

import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or10444OnewayType } from '~/types/cmn/business/components/Or10444Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Or10016OnewayType } from '~/types/cmn/business/components/Or10016Type'
import type { UserEntity } from '~/repositories/cmn/entities/LeavingInfoRecordPrintSettingsEntity'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01334Items, Mo01334OnewayType } from '~/types/business/components/Mo01334Type'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type {
  OrX0128OnewayType,
  OrX0128Items,
  OrX0128Headers,
} from '~/types/cmn/business/components/OrX0128Type'
import type {
  Or10444TwoWayData,
  TyppeCustomClass,
  ChoPrtListMaping,
  Attendance1List,
  Attendance2List,
  KazokuInfo,
  PrintSubjectHistoryList191,
  PrintSubjectHistoryList198
} from '~/components/custom-components/organisms/Or10444/Or10444.type'
import type {
  PrintSettingsScreenInitialInfoSelectGUI1131InEntity,
  PrintSettingsScreenInitialInfoSelectGUI1131OutEntity,
  MeetingMinutesHeaderInfoSelectGUI01131InEntity,
  MeetingMinutesHeaderInfoSelectGUI01131OutEntity,
  PrintSettingsInfoUpdateGUI1131InEntity,
  LedgerInitializeDataSelectGUI1131InEntity,
  LedgerInitializeDataSelectGUI1131OutEntity,
  ChoPrtList,
  IniDataObject,
  KghTucKrkKaigi1List
} from '~/repositories/cmn/entities/PrintSettingsScreenGUI1131Entity'

const { t } = useI18n()

const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or10444OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()
/**
 * or10016Data
 */
const or10016Data: Or10016OnewayType = {
  houjinId: systemCommonsStore.getHoujinId ?? '',
  shisetsuId: systemCommonsStore.getShisetuId ?? '',
  shokuinId: systemCommonsStore.getStaffId ?? '',
  systemCode: systemCommonsStore.getSystemCode ?? '',
  jigyoshoId: '',
  loginNumber: '',
  loginUserType: '',
  emrLinkFlag: '',
  reportSectionNumber: '3GKU0P092P001',
  assessment: '4',
  conferenceFlag: true,
}
const or21813 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })
const or10016 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })
const mo00610Oneway = ref<Mo00610OnewayType>({
  btnLabel: t('label.seal-column'),
  width: '100px',
  disabled: false,
  prependIcon: '',
  appendIcon: '',
})
const userCols = ref(6)
const iniDataObject = ref<IniDataObject>({
  prtName: '',
  prtBng: '',
  prtKojin: '',
})
const orX0117Oneway: OrX0117OnewayType = {
  type: Or10444Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType

const mo00024 = ref<Mo00024Type>({
  isOpen: Or10444Const.DEFAULT.IS_OPEN,
})
const { reportOutput } = useReportUtils()

const mo01334OnewayReport = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'prtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})

const mo01334TypeReport = ref({
  value: '',
  values: [] as IniDataObject[],
})
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

const local = reactive({
  createYmd: {
    value: '',
  } as Mo00020Type,
  mo00018PrintDocumentNumber: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintDescription: {
    modelValue: false,
  } as Mo00018Type,
  mo00039TypeUserSelectType: '',
  mo00039TypeHistorySelectType: '',
  mo00020TypeKijunbi: {
    value: '2024/11/11',
  } as Mo00020Type,
  height: 1,
  userId: Or10444Const.DEFAULT.EMPTY,
  userList: [] as UserEntity[],
  orX0128DetList: [] as OrX0128Items[],
})
const localOneway = reactive({
  Or10444: {
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '1300px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10444',
      toolbarTitle: t('label.print-set'),
      toolbarName: 'Or10444ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.print'),
  } as Mo00609OnewayType,
  mo01338OneWayTitle: {
    value: t('label.title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  prndateOneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  param07Option: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  param08Option: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo00020OneWay: {
    showItemLabel: false,
    width: '132px',
  } as Mo00020OnewayType,
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  param03OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00045OnewayTitleInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
  } as Mo00045OnewayType,
  param04Oneway: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '50',
    maxLength: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1' }),
  } as Mo00045OnewayType,
  param050OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param049OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-the-level-of-care-required'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param047OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-in-portrait-orientation'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param045OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printing-patron-family-attendance'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param05OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.confirm-form-copying'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintAuthor: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-author'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintDescription: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-description'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      labelStyle: 'display: none',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      labelStyle: 'display: none',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayBaseDate: {
    value: t('label.base-date'),
    customClass: {
      labelStyle: 'display: none',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo01338OneWayCareManagerInCharge: {
    value: t('label.care-manager-in-charge'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayCareManagerInChargeLabel: {
    value: t('label.saburo-honobono'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo00018OneWayPrintDocumentNumber: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-bunsyokanri'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: false,
    multiple: false,
    selectedUserCounter: '1',
  } as OrX0145OnewayType,
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
})
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width: 250px',
  userId: Or10444Const.DEFAULT.EMPTY,
  showKanaSelectionCount: true,
})
const orX0128OnewayModel = reactive<OrX0128OnewayType>({
  kikanFlg: localOneway.Or10444.kikanFlg ?? '',
  singleFlg: OrX0128Const.DEFAULT.TANI,
  headers: [
    { title: t('label.consultation-date'), key: 'createYmd', minWidth: '150px', sortable: false },
    { title: t('label.shoku_knj'), key: 'chkShoku', sortable: false },
  ] as OrX0128Headers[],
  items: [],
  initSelectId: Or10444Const.DEFAULT.EMPTY,
})

/**************************************************
 * Pinia
 **************************************************/
/**
 * Piniaストアの設定
 * - ダイアログの開閉状態を管理
 * - uniqueCpIdを使用して一意の状態を識別
 */
const { setState } = useScreenOneWayBind({
  cpId: Or10444Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or10444Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/* 子コンポーネントのユニークIDを設定する */
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [Or10016Const.CP_ID(0)]: or10016.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

const { refValue } = useScreenTwoWayBind<Or10444TwoWayData>({
  cpId: Or10444Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(props.uniqueCpId)
})
const showDialogOr10016 = computed(() => {
  return Or10016Logic.state.get(or10016.value.uniqueCpId)?.isOpen ?? false
})
const param048OneWay = computed(() => {
  return {
    disabled: refValue.value?.choPrtList[mo01334TypeReport.value.value]?.param050.modelValue,
    readonly: refValue.value?.choPrtList[mo01334TypeReport.value.value]?.param047.modelValue,
  }
})
const param046OneWay = computed(() => {
  return {
    name: '',
    itemLabel: '',
    checkboxLabel: '出席者の空欄を印刷する',
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: refValue.value?.choPrtList[mo01334TypeReport.value.value]?.param050.modelValue || !refValue.value?.choPrtList[mo01334TypeReport.value.value]?.param047.modelValue ? true : false,
    color: 'key',
  }
})

watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)
/* AC003_帳票選択切替 */
watch(
  () => mo01334TypeReport.value.value,
  async () => {
    localOneway.orX0145Oneway.selectedUserCounter = refValue.value.choPrtList[mo01334TypeReport.value.value]?.param06 ?? ''
    await ledgerInitializeDataSelect()
  }
)
watch(
  () => local.mo00039TypeUserSelectType,
  async (newValue) => {
    orX0130Oneway.selectMode = newValue
    const userList = OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList || []
    local.userList = []
    if (userList.length) {
      for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
        local.userList.push({
          userId: item.userId,
          userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
        } as UserEntity)
      }
    }
    if (newValue === Or10444Const.DEFAULT.TANI) {
      userCols.value = 6
      orX0130Oneway.tableStyle = 'width: 250px'
      orX0117Oneway.type = Or10444Const.DEFAULT.TWO
      await meetingMinutesHeaderInfoSelectGUI01131()
      return
    }
    userCols.value = 12
    orX0130Oneway.tableStyle = 'width: 430px'
    orX0117Oneway.type = Or10444Const.DEFAULT.ONE
    orX0130Oneway.userId = localOneway.Or10444.userId || Or10444Const.DEFAULT.EMPTY
    checkDisabledOaram050OneWay()
  }
)
watch(
  () => local.mo00039TypeHistorySelectType,
  (newValue) => {
    orX0128OnewayModel.singleFlg = newValue
    checkDisabledOaram050OneWay()
  }
)
/* AC004_日付印刷選択 */
watch(
  () => !refValue.value?.choPrtList[mo01334TypeReport.value.value]?.prndate,
  async () => {
    await checkTitleInput()
  }
)

watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    local.userList = newValue.userList ?? []
    if (local.mo00039TypeUserSelectType === Or10444Const.DEFAULT.HUKUSUU) return
    await nextTick()
    await meetingMinutesHeaderInfoSelectGUI01131()
  }
)
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    if (!newValue.historyDetClickFlg) return
    local.orX0128DetList = newValue.orX0128DetList
  }
)
onMounted(async () => {
  await initCodes()
  await printSettingsScreenInitialInfoSelectGUI1131()
  orX0130Oneway.userId = localOneway.Or10444.userId || Or10444Const.DEFAULT.EMPTY
})
function checkDisabledOaram050OneWay () {
  if (local.mo00039TypeUserSelectType === '0' && local.mo00039TypeHistorySelectType === '0') {
    localOneway.param050OneWay.disabled = false
    return
  }
  localOneway.param050OneWay.disabled = true
}

async function initCodes() {
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CREATED_DATE_PRINT_TYPE },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.PRINT_LEVEL_OF_CARE_REQUIRED },
  ]

  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  localOneway.prndateOneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  localOneway.param08Option.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CREATED_DATE_PRINT_TYPE
  )
  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  localOneway.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  localOneway.param07Option.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.PRINT_LEVEL_OF_CARE_REQUIRED
  )

  local.mo00039TypeUserSelectType = '0'
  local.mo00039TypeHistorySelectType = '0'
}
/* AC001_初期表示 */
async function printSettingsScreenInitialInfoSelectGUI1131() {
  const params: PrintSettingsScreenInitialInfoSelectGUI1131InEntity = {
    sysCd: systemCommonsStore.getSystemCode ?? '',
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    shokuId: systemCommonsStore.getStaffId ?? '',
    appYmd: systemCommonsStore.getProcessDate ?? '',
    kinounameKnj: localOneway.Or10444.kinounameKnj ?? '',
    kikanFlg: localOneway.Or10444.kikanFlg ?? '',
    userId: localOneway.Or10444.userId ?? '',
    tantoId: localOneway.Or10444.careManagerInChargeSettingsFlag ?? '',
    sectionName: localOneway.Or10444.sectionName ?? '',
    choIndex: localOneway.Or10444.choIndex ?? '',
    kojinhogoFlg: localOneway.Or10444.kojinhogoFlg ?? '',
    sectionAddNo: localOneway.Or10444.sectionAddNo ?? '',
    local: localOneway.Or10444.local ?? '',
    kaigiFlg: localOneway.Or10444.kaigiFlg ?? '',
    shosikiFlg: localOneway.Or10444.shosikiFlg ?? '',
    gamenKbn: localOneway.Or10444.gamenKbn ?? '',
  }

  const res: PrintSettingsScreenInitialInfoSelectGUI1131OutEntity = await ScreenRepository.select(
    'printSettingsScreenInitialInfoSelectGUI1131',
    params
  )
  const choPrtList: ChoPrtList[] = res.data?.choPrtList ?? []
  mo01334OnewayReport.value.items = choPrtList.map((item) => {
    return {
      id: item.prtNo,
      mo01337OnewayReport: {
        value: item.prtTitle,
        unit: '',
      } as Mo01337OnewayType,
      ...item,
    } as Mo01334Items
  })
  if (choPrtList.length > 0) mo01334TypeReport.value.value = choPrtList[0].prtNo ?? ''
  mappingRefValue(choPrtList)
  await nextTick()
  localOneway.orX0145Oneway.selectedUserCounter = refValue.value.choPrtList[mo01334TypeReport.value.value]?.param06 ?? ''
}
function mappingRefValue(choPrtList:ChoPrtList[] = [], reverse = false) {
  if (!refValue.value) return
  if (reverse) {
    refValue.value.choPrtList[mo01334TypeReport.value.value].param06 = `${orX0145Type.value.value.value}`
    const choPrtListMap: ChoPrtListMaping[] = Object.values(refValue.value.choPrtList) ?? []
    const array = choPrtListMap.map((e: ChoPrtListMaping) => {
    return {
      ...e,
      prtTitle: e.prtTitle.value,
      param02: e.param02.value,
      param03: e.param03.modelValue ? 'TRUE' : 'FALSE',
      param04: e.param04.value,
      param05: e.param05.modelValue ? 'TRUE' : 'FALSE',
      param045: e.param045.modelValue ? 'TRUE' : 'FALSE',
      param046: e.param046.modelValue ? 'TRUE' : 'FALSE',
      param047: e.param047.modelValue ? 'TRUE' : 'FALSE',
      param048: e.param048.modelValue ? 'TRUE' : 'FALSE',
      param049: e.param049.modelValue ? 'TRUE' : 'FALSE',
      param050: e.param050.modelValue ? 'TRUE' : 'FALSE',
    }
  }) ?? []
    return array
  }
  const output = choPrtList.reduce((obj: Record<string, ChoPrtList>, item: ChoPrtList) => {
    obj[item.prtNo] = {
      ...item,
      prtTitle: { value: item.prtTitle },
      param02: { value: hasData(item.param02) ? item.param02 : systemCommonsStore.getSystemDate },
      param03: { modelValue: item.param03 === 'TRUE' ? true : false },
      param04: { value: item.param04 },
      param05: { modelValue: item.param05 === 'TRUE' ? true : false },
      param07: hasData(item.param07) ? item.param07 : localOneway.param07Option.items.length ? localOneway.param07Option.items[0].value : '',
      param045: { modelValue: item.param045 === 'TRUE' ? true : false },
      param046: { modelValue: item.param046 === 'TRUE' ? true : false },
      param047: { modelValue: item.param047 === 'TRUE' ? true : false },
      param048: { modelValue: item.param048 === 'TRUE' ? true : false },
      param049: { modelValue: item.param049 === 'TRUE' ? true : false },
      param050: { modelValue: item.param050 === 'TRUE' ? true : false }
    }
    return obj
  }, {})
  refValue.value = { choPrtList: output }
  useScreenStore().setCpTwoWay({
    cpId: Or10444Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}
/* AC007_利用者単複数選択 */
async function meetingMinutesHeaderInfoSelectGUI01131() {
  const userId = local.userList[0]?.userId || ''
  const params: MeetingMinutesHeaderInfoSelectGUI01131InEntity = {
    kikanFlg: localOneway.Or10444.kikanFlg ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    userId: userId,
    gamenKbn: localOneway.Or10444.gamenKbn ?? ''
  }
  const res: MeetingMinutesHeaderInfoSelectGUI01131OutEntity = await ScreenRepository.select(
    'meetingMinutesHeaderInfoSelectGUI01131',
    params
  )
  mappingDataTableKikanRireki(res.data.kghTucKrkKaigi1List)
}
function mappingDataTableKikanRireki(rirekiList: KghTucKrkKaigi1List[]) {
  orX0128OnewayModel.items = []
  const tempList: string[] = [] as string[]
  local.createYmd.value = rirekiList[0]?.createYmd ?? ''
  orX0128OnewayModel.items = []
  rirekiList.forEach((item, index) => {
    const obj = {...item}
    const planPeriod =
      t('label.plan-period') +
      Or10444Const.DEFAULT.SPLIT_COLON +
      item.startYmd +
      Or10444Const.DEFAULT.SPLIT_TILDE +
      item.endYmd
    if (!tempList.includes(planPeriod)) {
      tempList.push(planPeriod)
      obj.planPeriod = planPeriod
      obj.isPeriodManagementMergedRow = true
      orX0128OnewayModel.items.push(obj)
      const objMap = {id: `${index + 1}`, ...obj, isPeriodManagementMergedRow: false}
      delete objMap.planPeriod
      orX0128OnewayModel.items.push(objMap)
      return
    }
    obj.id = `${index + 1}`
    obj.isPeriodManagementMergedRow = false
    orX0128OnewayModel.items.push(obj)
  })
}
async function printSettingsInfoUpdateGUI1131() {
  const choPrtList = mappingRefValue([], true) ?? []
  const params: PrintSettingsInfoUpdateGUI1131InEntity = {
    sysCd: systemCommonsStore.getSystemCode ?? '',
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    shokuId: systemCommonsStore.getStaffId ?? '',
    kinounameKnj: localOneway.Or10444.kinounameKnj ?? '',
    choPro: refValue.value?.choPrtList[mo01334TypeReport.value.value]?.choPro ?? '',
    kojinhogoFlg: localOneway.Or10444.kojinhogoFlg ?? '',
    sectionAddNo: localOneway.Or10444.sectionAddNo ?? '',
    choPrtList: choPrtList ?? [],
    iniDataObject: iniDataObject.value
  }
  await ScreenRepository.update('printSettingsInfoUpdateGUI1131', params)
}

async function showOr21813MsgOneBtn() {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

async function showOr21815MsgOneBtn() {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      dialogTitle: t('label.caution'),
      iconName: 'warning',
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
/* AC005_「指定日」変更 */
/* AC006_指定日選択 */
async function checkTitleInput() {
  if (refValue.value?.choPrtList[mo01334TypeReport.value.value]?.prtTitle.value) return
  const dialogResult = await showOr21815MsgOneBtn()
  if (dialogResult !== 'yes') return
  const item: ChoPrtList = mo01334OnewayReport.value.items.find(e => e.id === mo01334TypeReport.value.value)
  if (!item || !refValue.value.choPrtList[mo01334TypeReport.value.value]) return
  refValue.value.choPrtList[mo01334TypeReport.value.value].prtTitle.value = item.prtTitle
}
/* AC002_「×ボタン」押下 */
/* AC018_「閉じるボタン」押下 */
async function close() {
  await checkTitleInput()

  if (isEdit.value) await printSettingsInfoUpdateGUI1131()

  setState({ isOpen: false })
}
/* AC019_「印刷」ボタン押下 */
async function handlePrint() {
  await checkTitleInput()
  if (!refValue.value.choPrtList[mo01334TypeReport.value.value].choPro) await showOr21813MsgOneBtn()
  if (!local.userList.length) return
  // if (isEdit.value) await printSettingsInfoUpdateGUI1131()
  /* 利用者選択 */
  if (local.mo00039TypeUserSelectType === Or10444Const.DEFAULT.HUKUSUU) {
    for (const user of local.userList) {
      await handleCallApiReport(user)
    }
    return
  }
  /* 利用者選択 */
  const user = local.userList[0] || null
  for (const item of local.orX0128DetList) {
    await handleCallApiReport(user, item)
  }
  setState({ isOpen: false })
}
async function handleCallApiReport(user: UserEntity, item: OrX0128Items | null = null) {
  const mode = localOneway.Or10444.mode ?? ''
  if (!mode) return
  if (mode === Or10444Const.DEFAULT.ONE) {
    await handleServiceManagerMeetingMainPoint(user, item)
    return
  }
  if (mode === Or10444Const.DEFAULT.TWO) {
    await handleServiceManagerMeetingMainPointU00911(user, item)
    return
  }
  await handleServiceManagerMeetingMainPointU00912(user, item)
}
async function handleServiceManagerMeetingMainPoint(user: UserEntity, item: OrX0128Items | null = null) {
  const choPrtList = refValue.value?.choPrtList[mo01334TypeReport.value.value] ?? null
  if (!choPrtList) return
  const params = {
    st_title: choPrtList.prtTitle.value ?? '',
    st_sakudate: '',
    st_sakudate_flg: '',
    st_yokaigo: '',
    st_yokaigoFlg: '',
    kazokuSyutusekiFlg: '',
    kessekiRiyuu: '',
    kessekiRiyuuFlg: '',
    st_kaisuu: '',
    kaigi1Id: '',
    sheetNo: '',
    attendance1List: [] as Attendance1List[],
    attendance2List: [] as Attendance2List[],
    kazokuInfo: [] as KazokuInfo[],
    printSet: {
      shiTeiKubun: '',
      shiTeiDate: '',
    },
    printOption: {
      emptyFlg: '',
      keishoFlg: '',
      keishoKnj: '',
      jigyoshaKnj: '',
      shokuName: '',
      inkanPrintFlag: '',
    },
    printSubjectHistoryList: [] as PrintSubjectHistoryList191[]
  }
  params.printSubjectHistoryList.push({
    userId: `${user.id}`,
    userName: `${user.name1Knj}${user.name2Knj}`,
    startYmd: item ? `${item.startYmd}` : '',
    endYmd: item ? `${item.endYmd}` : '',
  })
  await reportOutput(Or10444Const.SERVICE_MANAGER_MEETING_MAIN_POINT, params, reportOutputType.DOWNLOAD)
}
async function handleServiceManagerMeetingMainPointU00911(user: UserEntity, item: OrX0128Items | null = null) {
  const choPrtList = refValue.value?.choPrtList[mo01334TypeReport.value.value] ?? null
  if (!choPrtList) return
  const params = {
    st_title: choPrtList.prtTitle.value ?? '',
    st_sakudate: '',
    st_sakudateKbn: '',
    st_yokaigo: '',
    st_yokaigoKbn: '',
    jigyousha: '',
    kaigi1Id: '',
    printSet: {
      shiTeiKubun: '',
      shiTeiDate: '',
    },
    printOption: {
      emptyFlg: '',
      kinyuAssType: '',
      keishoFlg: '',
      keishoKnj: '',
      colorFlg: '',
    },
    printSubjectHistoryList: [] as PrintSubjectHistoryList198[]
  }
  params.printSubjectHistoryList.push({
    userId: `${user.id}`,
    userName: `${user.name1Knj}${user.name2Knj}`,
    shokuName: '',
    sc1Id: item ? `${item.sc1Id}` : '',
    startYmd: item ? `${item.startYmd}` : '',
    endYmd: item ? `${item.endYmd}` : '',
    raiId: '',
    assType: '',
    assDateYmd: '',
    assShokuId: '',
    result: '',
  })
  await reportOutput(Or10444Const.SERVICE_MANAGER_MEETING_MAIN_POINT_U00911, params, reportOutputType.DOWNLOAD)
}
async function handleServiceManagerMeetingMainPointU00912(user: UserEntity, item: OrX0128Items | null = null){
  const choPrtList = refValue.value?.choPrtList[mo01334TypeReport.value.value] ?? null
  if (!choPrtList) return
  const params = {
    st_title: choPrtList.prtTitle.value ?? '',
    st_sakudate: '',
    st_sakudateKbn: '',
    st_yokaigo: '',
    st_yokaigoKbn: '',
    jigyousha: '',
    kaigi1Id: '',
    printSet: {
      shiTeiKubun: '',
      shiTeiDate: '',
    },
    printOption: {
      emptyFlg: '',
      kinyuAssType: '',
      keishoFlg: '',
      keishoKnj: '',
      colorFlg: '',
    },
    printSubjectHistoryList: [] as PrintSubjectHistoryList198[]
  }
  params.printSubjectHistoryList.push({
    userId: `${user.id}`,
    userName: `${user.name1Knj}${user.name2Knj}`,
    shokuName: '',
    sc1Id: item ? `${item.sc1Id}` : '',
    startYmd: item ? `${item.startYmd}` : '',
    endYmd: item ? `${item.endYmd}` : '',
    raiId: '',
    assType: '',
    assDateYmd: '',
    assShokuId: '',
    result: '',
  })
  await reportOutput(Or10444Const.SERVICE_MANAGER_MEETING_MAIN_POINT_U00912, params, reportOutputType.DOWNLOAD)
}
async function ledgerInitializeDataSelect() {
  const params: LedgerInitializeDataSelectGUI1131InEntity = {
    sysCd: systemCommonsStore.getSystemCode ?? '',
    shokuId: systemCommonsStore.getStaffId ?? '',
    sectionKnj: refValue.value?.choPrtList[mo01334TypeReport.value.value]?.choPro ?? '',
    kinounameKnj: localOneway.Or10444.kinounameKnj ?? '',
    kojinhogoFlg: localOneway.Or10444.kojinhogoFlg ?? '',
    sectionAddNo: localOneway.Or10444.sectionAddNo ?? '',
  }
  const res: LedgerInitializeDataSelectGUI1131OutEntity = await ScreenRepository.select(
    'ledgerInitializeDataSelectGUI1131',
    params
  )
  iniDataObject.value = res.data?.iniDataObject ?? {
    prtName: '',
    prtBng: '',
    prtKojin: '',
  }
}
function onInputNumber(event: KeyboardEvent) {
  if (['-', 'e', '+'].includes(event.key)) {
    event.preventDefault()
  }
  if (local.height >= 1) return
  local.height = 1
}
/* AC020_印鑑欄 */
function handleOpenModalGUI01110() {
  Or10016Logic.state.set({
    uniqueCpId: or10016.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function hasData(data: unknown) {
  if (data !== '' && data !== null && data !== undefined) return true
  else return false
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or10444_screen"
      >
        <c-v-col
          cols="12"
          sm="2"
          class="pa-0 pt-2 pl-2 box-left"
        >
          <base-mo-01334
            v-model="mo01334TypeReport"
            :oneway-model-value="mo01334OnewayReport"
            class="list-wrapper"
          >
            <!-- 帳票 -->
            <template #[`item.prtTitle`]="{ item }">
              <base-mo01337 :oneway-model-value="item.mo01337OnewayReport" />
            </template>
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          v-if="refValue.choPrtList[mo01334TypeReport.value]"
          cols="12"
          sm="4"
          class="pa-0 pt-2 content_center box-center"
        >
          <c-v-row
            no-gutter
            class="or10444_row flex-center"
          >
            <!-- 印鑑欄 -->
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo00610
                :oneway-model-value="mo00610Oneway"
                @click="handleOpenModalGUI01110()"
              />
            </c-v-col>
            <!-- タイトル -->
            <c-v-col
              cols="12"
              sm="3"
              class="pa-0 pl-2"
            >
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle" />
            </c-v-col>
            <c-v-col
              cols="12"
              sm="9"
              class="pa-0"
            >
              <base-mo00045
                v-model="refValue.choPrtList[mo01334TypeReport.value].prtTitle"
                :oneway-model-value="localOneway.mo00045OnewayTitleInput"
                @keyup.enter="checkTitleInput"
              />
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 flex justify-right"
            >
              <!-- 文書番号を印字する -->
              <base-mo00018
                v-model="local.mo00018PrintDocumentNumber"
                :oneway-model-value="localOneway.mo00018OneWayPrintDocumentNumber"
              />
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0 mt-2 mb-2" />
          <c-v-row
            no-gutter
            class="customCol or10444_row"
          >
            <c-v-col
              cols="7"
              class="pa-0"
            >
              <!-- 印刷日付選択ラジオボタングループ -->
              <base-mo00039
                v-model="refValue.choPrtList[mo01334TypeReport.value].prndate"
                :oneway-model-value="localOneway.prndateOneWay"
              />
            </c-v-col>
            <c-v-col
              cols="5"
              class="pa-0"
            >
              <!-- 印刷日付ラベル -->
              <base-mo00020
                v-if="refValue.choPrtList[mo01334TypeReport.value].prndate === '2'"
                v-model="refValue.choPrtList[mo01334TypeReport.value].param02"
                :oneway-model-value="localOneway.prndateOneWay"
                @mousedown="checkTitleInput"
              />
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0 mt-2 mb-2" />
          <c-v-row
            no-gutter
            class="customCol or10444_row"
          >
            <c-v-col
              cols="12"
              sm="8"
              class="pa-0"
            >
              <!-- 作成年月日印刷区分 -->
              <base-mo00039
                v-model="refValue.choPrtList[mo01334TypeReport.value].param08"
                :oneway-model-value="localOneway.param08Option"
              />
            </c-v-col>
            <c-v-col
              cols="12"
              sm="4"
              class="pa-0"
            >
              <!-- 印刷日付ラベル -->
              <base-mo01338
                v-if="refValue.choPrtList[mo01334TypeReport.value].param08 === '1'"
                :oneway-model-value="local.createYmd"
                style="background-color: transparent"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="printerOption"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pt-0 pb-0"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or10444_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 flex flex-row align-center"
            >
              <!-- 敬称を変更するチェックボックス -->
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param03"
                :oneway-model-value="localOneway.param03OneWay"
              />
              <!-- 敬称テキストボックス -->
              (<base-mo00045
                v-model="refValue.choPrtList[mo01334TypeReport.value].param04"
                :oneway-model-value="localOneway.param04Oneway"
                :disabled="!refValue.choPrtList[mo01334TypeReport.value].param03.modelValue"
              />)
            </c-v-col>
            <!-- 記入用シートを印刷する -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param050"
                :oneway-model-value="localOneway.param050OneWay"
              />
            </c-v-col>
            <!-- 要介護度を印刷する -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param049"
                :oneway-model-value="localOneway.param049OneWay"
              />
            </c-v-col>
            <!-- 印刷枠の高さを自動調整する -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <div class="flex flex-row align-center">
                <v-checkbox
                  :model-value="refValue.choPrtList[mo01334TypeReport.value].param048.modelValue"
                  name=""
                  :label="t('label.printing-frame-height-auto-resize')"
                  :indeterminate="false"
                  :hide-details="true"
                  :disabled="param048OneWay.disabled"
                  :readonly="param048OneWay.readonly"
                  color="key"
                />
                <span>(最小</span>
                <span>
                  <base-at-text-field
                    v-model="local.height"
                    :disabled="refValue.choPrtList[mo01334TypeReport.value].param050.modelValue"
                    type="number"
                    maxlength="3"
                    hide-details="auto"
                    style="width: 80px"
                    @change="onInputNumber"
                    @keydown="onInputNumber"
                  />
                </span>
                <span>日行)</span>
              </div>
              <div class="ml-8 text-sm mb-2">
                ※出席者行を追加している場合はチェックを入れてください
              </div>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or10444_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pt-0 pb-0"
            >
              <b>{{ t('label.print-nursing-care-required') }}</b>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pt-0 pb-0"
            >
              <base-at-select
                v-model="refValue.choPrtList[mo01334TypeReport.value].param07"
                class="select-w mt-2"
                :items="localOneway.param07Option.items"
                item-title="label"
                item-value="value"
                :class="{ active: false }"
                :hide-details="true"
              />
            </c-v-col>
            <!-- 縦型で印刷する -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param047"
                :oneway-model-value="localOneway.param047OneWay"
              />
            </c-v-col>
            <!-- 出席者の空欄を印刷する -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param046"
                :oneway-model-value="param046OneWay"
              />
            </c-v-col>
            <!-- 利用者·家族の出席を印刷する -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param045"
                :oneway-model-value="localOneway.param045OneWay"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          v-if="refValue.choPrtList[mo01334TypeReport.value]"
          cols="12"
          sm="6"
          class="pa-0 box-right"
        >
          <c-v-row
            class="or10444_row pa-2"
            no-gutter
            style="align-items: center"
          >
            <c-v-col
              cols="4"
              class="pa-0"
            >
              <!-- 利用者選択 -->
              <base-mo01338
                class="mt-2 pl-2"
                :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                style="background-color: transparent"
              />
              <!-- 利用者選択ラジオボタングループ -->
              <base-mo00039
                v-model="local.mo00039TypeUserSelectType"
                :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                @click="meetingMinutesHeaderInfoSelectGUI01131()"
              />
            </c-v-col>
            <c-v-col
              v-if="local.mo00039TypeUserSelectType === '0'"
              cols="4"
              class="pa-0"
            >
              <!-- 履歴選択 -->
              <base-mo01338
                class="mt-2 pl-2"
                :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                style="background-color: transparent"
              />
              <!-- 履歴選択 -->
              <base-mo00039
                v-model="local.mo00039TypeHistorySelectType"
                :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
              />
            </c-v-col>
            <c-v-col
              v-if="local.mo00039TypeUserSelectType === '1'"
              cols="4"
              class="pa-0"
            >
              <!-- 履歴選択ラベル -->
              <base-mo01338
                class="mt-2 pl-2"
                :oneway-model-value="localOneway.mo01338OneWayBaseDate"
                style="background-color: transparent"
              />
              <base-mo00020
                v-model="local.mo00020TypeKijunbi"
                :oneway-model-value="localOneway.mo00020KijunbiOneWay"
              />
            </c-v-col>
            <c-v-col
              cols="4"
              class="pa-0"
            >
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            class="or10444_row"
            no-gutter
          >
            <c-v-col
              class="overflow-auto"
              :cols="userCols"
            >
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              />
            </c-v-col>
            <c-v-col
              v-if="local.mo00039TypeUserSelectType === '0'"
              cols="6"
              class="py-0 pr-0 pl-0 overflow-auto"
            >
              <g-custom-or-x-0128
                v-if="orX0128OnewayModel.singleFlg"
                v-bind="orX0128"
                :oneway-model-value="orX0128OnewayModel"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        />
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="handlePrint()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813 v-bind="or21813" />
  <g-base-or21815 v-bind="or21815" />
  <g-custom-or-10016
    v-if="showDialogOr10016"
    v-bind="or10016"
    :oneway-model-value="or10016Data"
  />
</template>
<style lang="scss">
  .or10444_row {
    table {
      th {
        padding: 0px!important;
        .v-data-table-header__content {
          padding: 0px 5px!important;
        }
      }
      td {
        padding: 0px!important;
        .v-col {
          padding: 0px 5px!important;
        }
      }
    }
  }
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';
.or10444_screen {
  margin: -8px !important;
}
.box-left, .box-center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}
.or10444_row {
  margin: 0px !important;
}
.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;
  .label_left {
    padding-right: 0px;
  }
  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }
  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
     margin: 0 0 4px 0 !important;
  }
  :deep(.label-area-style) {
    display: none;
  }
  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}
.flex-center {
  display: flex;
  align-items: center;
}
.flex {
  display: flex;
}
.flex-row {
  flex-direction: row;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.justify-right {
  justify-content: end;
}
.align-center {
  align-items: center;
}
.flex-1 {
  flex: 1;
}
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
.btn-mo00610 {
  outline: none;
  background-color: rgb(var(--v-theme-black-100));
  border-color: rgb(var(--v-theme-black-100));
  color: black !important;
}
.overflow-auto {
  overflow: auto;
}
</style>
