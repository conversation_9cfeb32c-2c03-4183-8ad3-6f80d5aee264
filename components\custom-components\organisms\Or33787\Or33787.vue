<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { OrX0130Const } from '../OrX0130/OrX0130.constants'
import { OrX0130Logic } from '../OrX0130/OrX0130.logic'
import { Or33787Const } from '../Or33787/Or33787.constants'
import { Or26261Const } from '../Or26261/Or26261.constants'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import type { DataRow, Or33787StateType } from './Or33787.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import {
  computed,
  onMounted,
  reactive,
  ref,
  useScreenOneWayBind,
  useSetupChildProps,
  watch,
} from '#imports'
import type { Or33787Type, Or33787OnewayType } from '~/types/cmn/business/components/Or33787Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { Mo01334OnewayType, Mo01334Type } from '~/types/business/components/Mo01334Type'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  LedgerInitData,
  PrintSettingInitialInfoSelectInEntity,
  PrintSettingInitialInfoSelectOutEntity,
  TantoInfo,
} from '~/repositories/cmn/entities/PrintSettingInitialInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { PrintSettingsInfoComUpdateInEntity } from '~/repositories/cmn/entities/printSettingsInfoComUpdate'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { BenefitStatusListReportEntity } from '~/repositories/cmn/entities/BenefitStatusListReportEntity'
import { reportOutputType, useReportUtils } from '~/utils/useReportUtils'

/**
 * Or33787:（給付状況）印刷設定モーダル
 * GUI01184_印刷設定
 *
 * @description
 *［印刷設定］画面では、印刷設定画面を表示します。
 *
 * <AUTHOR> 劉顕康
 */

const { t } = useI18n()
const { reportOutput } = useReportUtils()
// システム共有情報ストア
// const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or33787Type
  onewayModelValue: Or33787OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or33787OnewayType = {
  /** デフォルト値 */
  defaultValue: '2',
  /** システムコード */
  systemCode: '',
  /** 法人ID */
  corporationId: '',
  /** 施設ID */
  facilityId: '',
  /** 事業者ID */
  officeId: '',
  /** 利用者ID */
  userId: '',
  /** 職員ID */
  staffId: '',
  /** 担当ケアマネID */
  careManaId: '',
  /** システム日付 */
  systemYmd: '',
  /** セクション名 */
  sectionName: '',
  /** 選択帳票番号 */
  selectedLedgerNo: '',
  /** 50音行番号 */
  gojuuonRowNo: '',
  /** 50音母音 */
  gojuuonKana: [''],
  /** 適用事業所IDリスト */
  applicableOfficeIdList: [],
  /** 利用票有無 */
  riyouhyoUmu: '0',
}

const localOneway = reactive({
  or33787: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // コードマスタ
  codeMasterValues: [] as CodeType[],
  mo00039OnewayDate: {
    name: 'date',
    items: [],
    showItemLabel: false,
    hideDetails: true,
    inline: false,
  } as Mo00039OnewayType,
  mo00039OnewayOrder: {
    name: 'order',
    items: [],
    showItemLabel: false,
    hideDetails: true,
    inline: false,
  } as Mo00039OnewayType,
  mo00040OnewayOffice: { showItemLabel: false, items: [], width: '470px' } as Mo00040OnewayType,
  mo00045OnewayTitle: {
    showItemLabel: true,
    itemLabel: t('label.title'),
    readonly: false,
    maxLength: '50',
    customClass: {
      outerClass: 'mr-0',
      outerStyle: 'width: -webkit-fill-available;',
      itemStyle: 'margin-top: -16px',
    },
  } as Mo00045OnewayType,
  /** 担当ケアマネ表示ラベル */
  mo00615OnewayCare: {
    itemLabel: '',
  } as Mo00615OnewayType,
  // PDFダウンロードボタン
  mo00609OneWay: {
    btnLabel: t('btn.pdf-download'),
    disabled: !(await hasPrintAuth()),
  } as Mo00609OnewayType,
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
  } as OrX0145OnewayType,
})

const local = reactive({
  tantoList: [] as TantoInfo[],
  iniDataList: [] as LedgerInitData[],
  mo00039DateModel: '0',
  mo00039OrderModel: '0',
  /** 帳票タイトル */
  mo00045ModelTitle: {
    value: '',
  },
  mo00040ModelOffice: {
    modelValue: '0',
  },
  mo00020modelShitei: {
    value: '',
  } as Mo00020Type,
  /** 担当ケアマネid */
  tantoId: '',
  mo00018modelCreater: {
    modelValue: false,
  } as Mo00018Type,
  asYmd: '',
})

//［項目選択］画面
const mo00024Oneway = reactive({
  width: '1400px',
  maxWidth: '1400px',
  height: '760px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or33787',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or33787ToolBar',
    // ツールバータイトルの左寄せ
    toolbarTitleCenteredFlg: false,
    showToolbar: true,
    showCardActions: true,
    scrollable: false,
    cardTextClass: 'or33787-nopadding-content',
  },
}) as Mo00024OnewayType

/**
 * OrX0130 oneway
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.HUKUSUU,
  tableStyle: 'width: 529px',
  focusSettingInitial: localOneway.or33787.gojuuonKana,
})

/*** 指定日 */
const mo00020OnewayShitei = ref<Mo00020OnewayType>({
  isRequired: false,
  showItemLabel: false,
  hideDetails: true,
  maxlength: '10',
  width: '130px',
  showSelectArrow: false,
  customClass: new CustomClass({
    outerClass: 'ml-15',
  }),
  mo01343Oneway: {
    selectMode: '0',
    closedDayDisplayOffice: '',
    rangeSelectDefaultPeriod: 0,
  },
})

/**
 * 出力帳票名一覧
 */
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'defPrtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 680,
})

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or33787Const.DEFAULT.IS_OPEN,
})

/** 「給付状況一覧（個別）」が選択された場合、true */
const isKobetu = computed(
  () =>
    selectedRowData.value !== undefined &&
    selectedRowData.value.defPrtTitle === Or33787Const.OUTPUT_LEDGER_NAME_INDIVIDUAL
)

// 子コンポーネント用変数
const orX0130 = ref({ uniqueCpId: '' })
// Or21813
const or21813 = ref({ uniqueCpId: '' })
// Or21814
const or21814 = ref({ uniqueCpId: '' })
// Or21815
const or21815 = ref({ uniqueCpId: '' })
// Or26261
const or26261 = ref({ uniqueCpId: '' })

const orx0145 = ref({ uniqueCpId: '' })
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or33787StateType>({
  cpId: Or33787Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or33787Const.DEFAULT.IS_OPEN
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0130Const.CP_ID(1)]: orX0130.value,
  [Or21813Const.CP_ID(1)]: or21813.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or26261Const.CP_ID(1)]: or26261.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await initCodes()
  await init()
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 性別区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
    // 印刷順序(印刷設定)
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRINT_ORDER },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分ラジオ
  localOneway.mo00039OnewayDate.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )

  // 印刷順序ラジオ
  localOneway.mo00039OnewayOrder.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PRINT_ORDER
  )
}

/**
 * 計画転送一覧初期情報取得
 */
async function init() {
  // リクエストパラメータ
  const inputData: PrintSettingInitialInfoSelectInEntity = {
    /** システムコード */
    sysCd: localOneway.or33787.systemCode,
    /** システム略称 */
    sysRyaku: Or33787Const.SYSTEM_NAME_SHORT,
    /** 機能名 */
    kinounameKnj: Or33787Const.FUNCTION_NAME_PRINT,
    /** 法人ID */
    houjinId: localOneway.or33787.corporationId,
    /** 施設ID */
    shisetuId: localOneway.or33787.facilityId,
    /** 事業者ID */
    svJigyoId: localOneway.or33787.officeId,
    /** 職員ID */
    shokuId: localOneway.or33787.staffId,
    /** セクション名 */
    sectionName: localOneway.or33787.sectionName,
    /** 選択帳票番号 */
    choIndex: localOneway.or33787.selectedLedgerNo,
    /** 適用事業所ＩＤリスト */
    jigyoId: localOneway.or33787.applicableOfficeIdList,
  }

  // 初期情報取得
  const ret: PrintSettingInitialInfoSelectOutEntity = await ScreenRepository.select(
    'printSettingInitialInfoSelect',
    inputData
  )

  // システム日付
  local.asYmd = ret.data.sysYmd
  // 担当ケアマネリスト
  local.tantoList = ret.data.tantoList
  // 帳票INIデータリスト
  local.iniDataList = ret.data.iniDataList ?? []

  // 対象事業所セレクトフィールド
  ret.data.svJigyoList.forEach((item) => {
    if (item.svJigyoId === localOneway.or33787.officeId) {
      local.mo00040ModelOffice.modelValue = localOneway.or33787.officeId
    }
    localOneway.mo00040OnewayOffice.items?.push({
      value: item.svJigyoId,
      title: item.jigyoRyakuKnj,
    })
  })

  // 出力帳票一覧の設定
  ret.data.choPrtList.forEach((item, index) => {
    const rowData = { id: String(index + 1), ...item } as DataRow
    mo01334Oneway.value.items.push(rowData)
  })

  // 指定日
  local.mo00020modelShitei.value = ret.data.sysYmd

  // 担当ケアマネ
  local.tantoId = localOneway.or33787.careManaId
  localOneway.mo00615OnewayCare.itemLabel = careManaName.value

  // 計画作成者を印刷するチェックボックス
  local.mo00018modelCreater.modelValue = Boolean(Number(localOneway.or33787.riyouhyoUmu))

  // 親画面.デフォルト値 > 1（個別画面から起動）の場合
  if (localOneway.or33787.defaultValue > '1') {
    // 画面.出力帳票一覧明細セクションの帳票：給付状況一覧(個別)を選択する。
    mo01334Type.value.value =
      mo01334Oneway.value.items.find(
        (item) => (item as DataRow).defPrtTitle === Or33787Const.OUTPUT_LEDGER_NAME_INDIVIDUAL
      )?.id ?? ''
  } else {
    mo01334Type.value.value = '1'
  }
}

/**
 * ケアマネIDに対応する職員名、担当ケアマネリストから取得
 */
const careManaName = computed(() => {
  const tanto = local.tantoList.find((item) => item.chkShokuId === local.tantoId)
  return tanto === undefined ? '' : tanto.shokuin1Knj + ' ' + tanto.shokuin2Knj
})

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334Type.value.value,
  (newValue, oldValue) => {
    saveRowStatus(oldValue)

    // 帳票タイトル
    local.mo00045ModelTitle.value = selectedRowData.value.prtTitle
    // 日付印刷区分
    local.mo00039DateModel = selectedRowData.value.prndate
  }
)

/**
 * 選択行のデータ
 */
const selectedRowData = computed(() => {
  return mo01334Oneway.value.items.find((item) => item.id === mo01334Type.value.value) as DataRow
})

/**
 * 出力帳票一覧の選択行変更時、画面の入力内容を行のデータとして保存する
 *
 * @param oldId - 変更前のID
 */
function saveRowStatus(oldId: string) {
  if (oldId === '') {
    return
  }

  const oldRow = mo01334Oneway.value.items.find((item) => item.id === oldId) as DataRow
  // 帳票タイトルが空白以外の場合
  if (local.mo00045ModelTitle.value.trim().length !== 0) {
    // 行の帳票タイトル = 画面.帳票タイトル
    oldRow.prtTitle = local.mo00045ModelTitle.value
  }
  // 行の日付表示有無 = 画面.日付印刷区分
  oldRow.prndate = local.mo00039DateModel
}

/**
 * 日付印刷区分ラジオ変更
 */
watch(
  () => local.mo00039DateModel,
  () => {
    // 画面.帳票タイトルの入力変更がある
    if (local.mo00045ModelTitle.value !== selectedRowData.value.prtTitle) {
      checkTitleValue()
    }
  }
)

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  // 保存処理
  await savePrintSettingInfo()

  setState({ isOpen: false })
}

/**
 * PDFダウンロードボタンクリック
 */
async function pdfDownloadBtnClick() {
  // 印刷設定情報を保存する
  const result = await savePrintSettingInfo()

  if (result) {
    // 帳票出力処理
    await downloadPdf()
  }
}

/**
 * pdf download
 */
const downloadPdf = async () => {
  const userList = OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList

  const useridList: string[] = []
  userList?.forEach((item) => useridList.push(item.userId))

  const inputData: BenefitStatusListReportEntity = {
    /** 印刷設定 */
    printSet: {
      shiTeiKubun: local.mo00039DateModel,
      shiTeiDate: local.mo00020modelShitei.value,
      sortFlag: local.mo00039OrderModel,
      tantoKeamane: local.tantoId,
      riyouhyouCheck: local.mo00018modelCreater ? '1' : '0',
    },
    /** 適用事務所IDリスト */
    jigyoIdList: localOneway.or33787.applicableOfficeIdList,
    /** 提供年月 */
    asYymm: localOneway.or33787.systemYmd,
    /** システム日付 */
    asYmd: local.asYmd,
    /** 支援事業者ID */
    shienId: localOneway.or33787.officeId,
    /** 利用者IDリスト */
    useridList: useridList,
  }

  // 帳票出力
  await reportOutput(
    isKobetu.value ? Or33787Const.CHOUHYOU_ID_KOBETU : Or33787Const.CHOUHYOU_ID,
    inputData,
    reportOutputType.DOWNLOAD
  )
}

/**
 * 画面の印刷設定情報を保存する
 */
async function savePrintSettingInfo() {
  // 画面.帳票タイトルの入力変更がある
  if (local.mo00045ModelTitle.value !== selectedRowData.value.prtTitle) {
    checkTitleValue()
  }

  const userList = OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList
  // 選択した画面.出力帳票名="給付状況一覧（個別）" がつ 利用者一覧が0件選択の場合
  if (isKobetu.value && (userList === undefined || userList.length === 0)) {
    showOr21814Msg(t('message.i-cmn-11393'))
    return false
  }

  // 画面.出力帳票一覧明細に選択行.日付表示有無 = 画面.日付印刷区分
  selectedRowData.value.prndate = local.mo00039DateModel

  // 画面の印刷設定情報を保存する
  const inputData: PrintSettingsInfoComUpdateInEntity = {
    // システムコード
    sysCd: localOneway.or33787.systemCode,
    // システム略称
    sysRyaku: Or33787Const.SYSTEM_NAME_SHORT,
    // 機能名
    kinounameKnj: Or33787Const.FUNCTION_NAME_PRINT,
    // 法人ID
    houjinId: localOneway.or33787.corporationId,
    // 施設ID
    shisetuId: localOneway.or33787.facilityId,
    // 事業者ID
    svJigyoId: localOneway.or33787.officeId,
    // 職員ID
    shokuId: localOneway.or33787.staffId,
    // プロファイル
    choPro: selectedRowData.value.choPro,
    // 個人情報表示フラグ
    kojinhogoFlg: Or33787Const.PERSONAL_INFO_SHOW_FLG_0,
    // 個人情報表示値
    sectionAddNo: Or33787Const.PERSONAL_INFO_SHOW_VALUE_0,
    // 出力帳票印刷情報リスト
    choPrtList: [selectedRowData.value],
    // 帳票INIデータオブジェクト
    iniDataObject: [...local.iniDataList],
    sectionName: '',
  }

  await ScreenRepository.update('printSettingsInfoComUpdate', inputData)

  return true
}

// Or21813の戻り値
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.thirdBtnClickFlg) {
      return
    }
  }
)

/**
 * 確認メッセージを表示する
 *
 * @param errorMsg - メッセージ内容
 */
function showOr21814Msg(errorMsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogTitle: t('label.confirm'),
      dialogText: errorMsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.yes'),
    },
  })
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

// Or21814の戻り値
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.thirdBtnClickFlg) {
      return
    }
  }
)

/**
 * 警告メッセージを表示する
 *
 * @param errorMsg - メッセージ内容
 */
function showOr21815Msg(errorMsg: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      dialogTitle: t('label.warning'),
      dialogText: errorMsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

// Or21815の戻り値
watch(
  () => Or21815Logic.event.get(or21815.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.thirdBtnClickFlg) {
      return
    }
  }
)

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await close()
    }
  }
)

/**
 * Enterキー押下
 */
function keyDownEnter() {
  checkTitleValue()
}

/**
 * 帳票タイトルのチェック
 */
function checkTitleValue() {
  // 画面.帳票タイトルが入力していない場合
  if (local.mo00045ModelTitle.value.trim() === '') {
    showOr21815Msg(t('message.w-cmn-20845'))
    // 画面.出力帳票一覧明細に選択されている行.帳票タイトルの値を入力欄に再設定する。
    local.mo00045ModelTitle.value = selectedRowData.value.prtTitle
    return
  } else {
    // 画面.帳票タイトルが空白以外の場合
    // 出力帳票一覧明細に選択されている行.帳票タイトル = 画面.帳票タイトル
    selectedRowData.value.prtTitle = local.mo00045ModelTitle.value
  }
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        class="no-margin"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="2"
          class="np-border-right"
          style="padding: 8px !important; height: 657px"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="mo01334Oneway"
            class="list-wrapper"
          >
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="5"
          class="np-border-right"
          style="padding-left: 8px !important; padding-right: 8px !important"
        >
          <c-v-row
            class="has-border-bottom"
            style="height: 83px"
          >
            <!-- 帳票タイトル -->
            <base-mo00045
              v-model="local.mo00045ModelTitle"
              :oneway-model-value="localOneway.mo00045OnewayTitle"
              @keydown.enter="keyDownEnter"
            >
            </base-mo00045>
          </c-v-row>
          <c-v-row
            class="has-border-bottom no-pl"
            style="height: 134px"
          >
            <!-- 日付印刷区分 -->
            <base-mo00039
              v-model="local.mo00039DateModel"
              :oneway-model-value="localOneway.mo00039OnewayDate"
            >
            </base-mo00039>
            <!-- 指定日 -->
            <base-mo00020
              v-show="local.mo00039DateModel == Or33787Const.DATE_PRINT_CATEGORY_2"
              v-model="local.mo00020modelShitei"
              :oneway-model-value="mo00020OnewayShitei"
            >
            </base-mo00020>
          </c-v-row>
          <c-v-col
            v-show="!isKobetu"
            style="padding-bottom: 0px !important"
          >
            <c-v-row style="height: 42px">
              <!-- 印刷順序ラベル -->
              <base-mo01338
                :oneway-model-value="{
                  value: t('label.print-options'),
                  valueFontWeight: 'bolder',
                  customClass: {
                    labelStyle: 'display: none',
                  },
                }"
                style="
                  width: 39%;
                  border: 1px rgb(var(--v-theme-black-200)) solid;
                  background-color: #e2ecf5;
                "
              >
              </base-mo01338>
            </c-v-row>
            <c-v-row
              class="has-border-bottom"
              style="
                height: 94px;
                width: 40%;
                margin-top: 8px;
                border: 1px rgb(var(--v-theme-black-200)) solid;
                border-top: none;
              "
            >
              <!-- 印刷順序ラジオ -->
              <base-mo00039
                v-model="local.mo00039OrderModel"
                :oneway-model-value="localOneway.mo00039OnewayOrder"
              >
              </base-mo00039>
            </c-v-row>
          </c-v-col>
          <c-v-col v-show="isKobetu">
            <c-v-row style="height: 42px">
              <!-- 事業所選択ラベル -->
              <base-mo01338
                :oneway-model-value="{
                  value: t('label.office-select'),
                  valueFontWeight: 'bolder',
                  customClass: {
                    labelStyle: 'display: none',
                  },
                }"
                style="
                  width: 100%;
                  border: 1px rgb(var(--v-theme-black-200)) solid;
                  background-color: #e2ecf5;
                "
              >
              </base-mo01338>
            </c-v-row>
            <c-v-row
              class="border-row"
              style="height: 100px"
            >
              <c-v-row class="content-row">
                <!-- 対象事業所ラベル -->
                <base-mo01338
                  :oneway-model-value="{
                    value: t('label.subject-office'),
                    customClass: {
                      labelStyle: 'display: none',
                    },
                  }"
                  class="no-pr"
                >
                </base-mo01338>
                <!-- 対象事業所セレクトフィールド -->
                <base-mo00040
                  v-model="local.mo00040ModelOffice"
                  :oneway-model-value="localOneway.mo00040OnewayOffice"
                >
                </base-mo00040>
              </c-v-row>
              <c-v-row class="content-row">
                <custom-at00014
                  :oneway-model-value="{
                    value: t('label.print-from-selected-users'),
                  }"
                >
                </custom-at00014>
              </c-v-row>
            </c-v-row>
          </c-v-col>
          <c-v-col v-show="!isKobetu">
            <c-v-row style="height: 42px">
              <!-- 絞込条件ラベル -->
              <base-mo01338
                :oneway-model-value="{
                  value: t('label.filter-conditions'),
                  valueFontWeight: 'bolder',
                  customClass: {
                    labelStyle: 'display: none',
                  },
                }"
                style="
                  width: 100%;
                  border: 1px rgb(var(--v-theme-black-200)) solid;
                  background-color: #e2ecf5;
                "
              >
              </base-mo01338>
            </c-v-row>
            <c-v-row
              class="border-row"
              style="height: 100px"
            >
              <c-v-row class="content-row x-0145">
                <!-- 担当ケアマネラベル -->
                <!-- <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.care-manager-in-charge'),
                  }"
                >
                </base-mo00615> -->
                <!-- 担当ケアマネ入力支援アイコンボタン -->
                <!-- <base-mo00009
                  :oneway-model-value="mo00009Oneway"
                  @click="careManagerClick"
                >
                  <g-custom-or-26261
                    v-if="showDialogOr26261"
                    v-bind="or26261"
                    v-model="local.or26261Type"
                    :oneway-model-value="or26261Oneway"
                  />
                </base-mo00009> -->
                <g-custom-or-x-0145
                  v-bind="orx0145"
                  v-model="orX0145Type"
                  :oneway-model-value="localOneway.orX0145Oneway"
                ></g-custom-or-x-0145>
                <!-- 担当ケアマネ表示ラベル -->
                <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayCare"> </base-mo00615>
              </c-v-row>
              <c-v-row style="padding-left: 8px">
                <!-- 利用票有無ラベル -->
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.has-using-slip-or-not'),
                  }"
                >
                </base-mo00615>
                <!-- 計画作成者を印刷するチェックボックス -->
                <base-mo00018
                  v-model="local.mo00018modelCreater"
                  :oneway-model-value="{
                    checkboxLabel: t('label.print-plan-creater'),
                    isVerticalLabel: false,
                    customClass: { outerClass: 'ml-2' },
                  }"
                ></base-mo00018>
              </c-v-row>
            </c-v-row>
          </c-v-col>
        </c-v-col>
        <c-v-col
          v-show="isKobetu"
          cols="12"
          sm="5"
          style="padding: 0px 8px 0px 0px"
        >
          <g-custom-or-x-0130
            v-bind="orX0130"
            :oneway-model-value="orX0130Oneway"
          >
          </g-custom-or-x-0130>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row
        no-gutters
        style="padding-right: 8px; padding-bottom: -8px"
      >
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- PDFダウンロードボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          @click="pdfDownloadBtnClick"
        >
          <!--ツールチップ表示："PDFをダウンロードします"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.pdf-download')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- エラーダイアログを表示する。 -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  ></g-base-or21813>
  <!-- 確認ダイアログを表示する。 -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  ></g-base-or21814>
  <!-- ワーニングダイアログを表示する。 -->
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  ></g-base-or21815>
</template>
<style>
.or33787-nopadding-content {
  padding: 0px !important;
}
</style>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';

:deep(.v-data-table__th) {
  min-width: 70px;
}

.has-border-bottom {
  margin-bottom: 8px;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
}

.content-row {
  width: 100%;
  padding-left: 8px;
  align-items: center;
}

.border-row {
  padding-top: 8px;
  border: 1px rgb(var(--v-theme-black-200)) solid;
  align-items: center;
}

.no-margin {
  margin: 0px !important;
}

.np-border-right {
  padding: 0px !important;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

:deep(.no-pl .v-col) {
  padding-left: 0 !important;
}

:deep(.no-pr .v-col) {
  padding-right: 0 !important;
}

:deep(td:not(:first-child) .v-row--no-gutters > .v-col) {
  padding-top: 0 !important;
  padding-left: 0 !important;
  padding-bottom: 0 !important;
}

:deep(.v-table--density-compact .row-height) {
  height: 31px !important;
}
:deep(.list-wrapper .v-table__wrapper) {
  height: 580px !important;
}
.x-0145 .v-theme--mainTheme {
  display: flex;
}
</style>
