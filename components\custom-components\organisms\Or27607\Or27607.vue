<script setup lang="ts">
/**
 * Or27607:(阻害要因マスタ)阻害要因マスタ入力
 * GUI00913_阻害要因マスタ
 *
 * @description
 * (阻害要因マスタ)阻害要因マスタ入力
 *
 * <AUTHOR> DAO DINH DUONG
 */
import { computed, nextTick, onMounted, reactive, ref, watch, toRaw } from 'vue'
import { cloneDeep, isEqual, sortBy, isNil, some } from 'lodash'
import { useI18n } from 'vue-i18n'
import type { TableData, Sogaiyouin, OderVTable } from './Or27607.type'
import { Or27607Const } from './Or27607.constants'
import {
  useSetupChildProps,
  useSystemCommonsStore,
  useScreenTwoWayBind,
  useScreenStore,
} from '#imports'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01278OnewayType } from '~/types/business/components/Mo01278Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { Or27607OnewayType } from '~/types/cmn/business/components/Or27607Type'
import type { Mo01274OnewayType } from '~/types/business/components/Mo01274Type'
import { useValidation } from '~/utils/useValidation'
import type {
  InhibitoryFactorsMasterSelectInEntity,
  InhibitoryFactorsMasterSelectOutEntity,
} from '~/repositories/cmn/entities/inhibitoryFactorsMasterSelectEntity'
import type {
  InhibitoryFactorsMasterUpdateInEntity,
  InhibitoryFactorsMasterUpdateOutEntity,
} from '~/repositories/cmn/entities/inhibitoryFactorsMasterUpdateEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or27607Logic } from './Or27607.logic'
import { v4 as uuidv4 } from 'uuid'

interface Props {
  uniqueCpId: string
  onewayModelValue: Or27607OnewayType
}
const systemCommonsStore = useSystemCommonsStore()
/**
 * 親コンポーネントから渡されたプロパティを定義
 */
const props = defineProps<Props>()
const screenStore = useScreenStore()
/**
 * 国際化関数の取得
 */
const { t } = useI18n()

/**
 * 入力バリデーションロジック
 */
const validation = useValidation()

/**
 * 修正ポップアップ（Or21814）用のユニークCP ID
 */
const or21814ModifiledPopup = ref({ uniqueCpId: '' })

/**
 * 権限ポップアップ（Or21814）用のユニークCP ID
 */
const or21814PermissionPopup = ref({ uniqueCpId: '' })

/**
 * 削除確認ポップアップ（Or21814）用のユニークCP ID
 */
const or21814ConfirmDelete = ref({ uniqueCpId: '' })

/**
 * 保存不可ポップアップ（Or21814）用のユニークCP ID
 */
const or21814CantSave = ref({ uniqueCpId: '' })

/**
 * Or21813モーダル用のユニークCP ID
 */
const or21813 = ref({ uniqueCpId: '' })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814ModifiledPopup.value, // 確認ダイアログ
  [Or21814Const.CP_ID(1)]: or21814PermissionPopup.value, // 確認ダイアログ
  [Or21814Const.CP_ID(2)]: or21814ConfirmDelete.value,
  [Or21814Const.CP_ID(3)]: or21814CantSave.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
})
const { refValue } = useScreenTwoWayBind<TableData>({
  cpId: Or27607Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
/**
 *OneWayバインド用のローカル状態
 */
const localOneway = reactive({
  mo00611AddBtnOneway: {
    tooltipText: t('tooltip.care-plan2-newline-btn'),
    btnLabel: t('label.add-row'),
    width: '90px',
  } as Mo00611OnewayType,
  mo00611CopyBtnOneway: {
    tooltipText: t('tooltip.care-plan2-cpyline-btn'),
    btnLabel: t('btn.duplicate-row'),
    width: '90px',
  } as Mo00611OnewayType,
  mo00611DeleteBtnOneway: {
    tooltipText: t('tooltip.care-plan2-deleteline-btn'),
    btnLabel: t('label.delete-row'),
    width: '90px',
    color: 'red',
    labelColor: 'red',
  } as Mo00611OnewayType,
  konnanKnjOneway: {
    class: 'youinKnjInputText',
    rules: [validation.required],
    maxlength: '84',
  } as Mo01274OnewayType,
  mo01278Oneway: {
    rules: [validation.required],
    min: -999,
    max: 9999,
  } as Mo01278OnewayType,
})

/**
 *無効な入力項目が存在するか
 */
const hasInvalidField = computed(() =>
  some(
    refValue.value!.sogaiyouinList.filter((item) => item.visible),
    (item) => {
      const konnanCdVal = item.youinCd?.modelValue?.value
      const konnanKnjVal = item.youinKnj?.modelValue?.value
      const sortVal = item.sort?.modelValue?.value
      return (
        isNil(konnanCdVal) ||
        konnanCdVal.trim() === (Or27607Const.DEFAULT.EMPTY as string) ||
        isNil(konnanKnjVal) ||
        konnanKnjVal.trim() === Or27607Const.DEFAULT.EMPTY ||
        isNil(sortVal) ||
        sortVal.trim() === Or27607Const.DEFAULT.EMPTY
      )
    }
  )
)
/**
 * 調査票一覧情報取得(IN)
 */
async function init() {
  const paramGetList: InhibitoryFactorsMasterSelectInEntity = {
    //事業所ID
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
  }

  const res: InhibitoryFactorsMasterSelectOutEntity = await ScreenRepository.select(
    'inhibitoryFactorsMasterSelect',
    paramGetList
  )
  screenStore.setCpTwoWay({
    cpId: Or27607Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: {
      sogaiyouinList: res.data.sogaiyouinList.map((item) => ({
        uniqueId: uuidv4(),
        modifiedCnt: item.modifiedCnt,
        houjinId: item.houjinId,
        svJigyoId: item.svJigyoId,
        shisetuId: item.shisetuId,
        youinCd: { modelValue: { value: String(item.youinCd) } },
        youinKnj: { modelValue: { value: item.youinKnj ?? '' } },
        sort: { modelValue: { value: String(item.sort) } },
        visible: true,
      })),
    },
    isInit: true,
  })
  await nextTick()
  dataBeforeChange.value = Or27607Logic.data.get(props.uniqueCpId)?.sogaiyouinList
  selectedItem.value = refValue.value!.sogaiyouinList[0].uniqueId
  
}
onMounted(async () => {
  await init()
})
/**
 * 変更前の困難度データ
 */
const dataBeforeChange = ref<Sogaiyouin[]>()

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return screenStore.isEditNavControl()
})
/**
 * 画面を閉じる処理（確認ダイアログあり）
 *
 * @returns 'nextStep'：閉じる / 'keep'：そのまま
 */
const changeTabOrCloseModal = () => {
  return new Promise((resolve) => {
    if (!props.onewayModelValue.isHasPermission && isEdit.value) {
      Or21814Logic.state.set({
        uniqueCpId: or21814PermissionPopup.value.uniqueCpId,
        state: {
          // ダイアログタイトル
          isOpen: true,
          dialogText: t('message.i-cmn-10006'),
          dialogTitle: t('label.confirm'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'destroy1',
          secondBtnLabel: t('btn.no'),
        },
      })
      watch(
        () => Or21814Logic.state.get(or21814PermissionPopup.value.uniqueCpId)?.isOpen,
        () => {
          const event = Or21814Logic.event.get(or21814PermissionPopup.value.uniqueCpId)
          if (event?.firstBtnClickFlg) {
            resolve(Or27607Const.DEFAULT.NEXTSTEP)
          }
          if (event?.secondBtnClickFlg) {
            resolve(Or27607Const.DEFAULT.KEEP)
          }
          // 確認ダイアログのフラグをOFF
          Or21814Logic.event.set({
            uniqueCpId: or21814PermissionPopup.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
            },
          })
        },
        { once: true }
      )
      return
    }
    if (!isEdit.value) {
      resolve(Or27607Const.DEFAULT.NEXTSTEP)
    } else {
      Or21814Logic.state.set({
        uniqueCpId: or21814ModifiledPopup.value.uniqueCpId,
        state: {
          // ダイアログタイトル
          isOpen: true,
          dialogText: t('message.i-cmn-10430'),
          dialogTitle: t('label.confirm'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'destroy1',
          secondBtnLabel: t('btn.no'),
          thirdBtnLabel: t('btn.cancel'),
          thirdBtnType: 'normal3',
        },
      })
      watch(
        () => Or21814Logic.state.get(or21814ModifiledPopup.value.uniqueCpId)?.isOpen,
        async () => {
          const event = Or21814Logic.event.get(or21814ModifiledPopup.value.uniqueCpId)
          if (event?.firstBtnClickFlg) {
            const isSucces = await onSave()
            if (isSucces) {
              resolve(Or27607Const.DEFAULT.NEXTSTEP)
            }
          }
          if (event?.secondBtnClickFlg) {
            resolve(Or27607Const.DEFAULT.NEXTSTEP)
          }
          if (event?.thirdBtnClickFlg) {
            resolve(Or27607Const.DEFAULT.KEEP)
          }
          // 確認ダイアログのフラグをOFF
          Or21814Logic.event.set({
            uniqueCpId: or21814ModifiledPopup.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              thirdBtnClickFlg: false,
            },
          })
        },
        { once: true }
      )
    }
  })
}
/**
 * 削除確認ダイアログを表示する
 */
const openPopupComfirmDelete = () => {
  Or21814Logic.state.set({
    uniqueCpId: or21814ConfirmDelete.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      isOpen: true,
      dialogText: t('message.i-cmn-10764'),
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
    },
  })
  watch(
    () => Or21814Logic.state.get(or21814ConfirmDelete.value.uniqueCpId)?.isOpen,
    () => {
      const event = Or21814Logic.event.get(or21814ConfirmDelete.value.uniqueCpId)
      if (event?.firstBtnClickFlg) {
        void deleteYouin()
      }
      // 確認ダイアログのフラグをOFF
      Or21814Logic.event.set({
        uniqueCpId: or21814ConfirmDelete.value.uniqueCpId,
        events: {
          firstBtnClickFlg: false,
          secondBtnClickFlg: false,
        },
      })
    },
    { once: true }
  )
}
/**
 * Or21813ダイアログの表示状態
 */
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})
/**
 * 保存処理
 *
 * @returns  非同期の保存処理を行います。
 */
const onSave = async () => {
  if (hasInvalidField.value) {
    Or21813Logic.state.set({
      uniqueCpId: or21813.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        isOpen: true,
        dialogText: t('message.e-cmn-41708'),
        dialogTitle: t('label.confirm'),
        firstBtnType: 'normal1',
        secondBtnType: 'blank',
        firstBtnLabel: t('OK'),
      },
    })
    return false
  }
  if (!isEdit.value) {
    Or21814Logic.state.set({
      uniqueCpId: or21814CantSave.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        isOpen: true,
        dialogText: t('message.i-cmn-21800'),
        dialogTitle: t('label.confirm'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('OK'),
      },
    })
  } else {
    dataBeforeChange.value?.forEach((item) => {
      const itemInlist = refValue
        .value!.sogaiyouinList.filter((curentList) => curentList.visible)
        .find((curentList) => curentList.youinCd.modelValue.value === item.youinCd.modelValue.value)
      if (!itemInlist) return
      const isChange = !isEqual(cloneDeep(item), cloneDeep(itemInlist))
      if (isChange) {
        itemInlist.updateKbn = Or27607Const.DEFAULT.UPDATE
      }
    })
    const inputData: InhibitoryFactorsMasterUpdateInEntity = {
      sogaiyouinList: refValue
        .value!.sogaiyouinList.filter((item) => item.updateKbn)
        .map((item) => ({
          updateKbn: item.updateKbn ?? '',
          houjinId: item.houjinId,
          shisetuId: item.shisetuId,
          svJigyoId: item.svJigyoId,
          youinCd: Number(item.youinCd.modelValue.value),
          youinKnj: item.youinKnj.modelValue.value,
          sort: Number(item.sort.modelValue.value),
          modifiedCnt: item.modifiedCnt,
        })),
    }
    const resData: InhibitoryFactorsMasterUpdateOutEntity = await ScreenRepository.update(
      'inhibitoryFactorsMasterUpdate',
      inputData
    )
    if (
      resData.statusCode === Or27607Const.DEFAULT.SUCCESS ||
      resData.statusCode === Or27607Const.DEFAULT.SUCCESS_CODE
    ) {
      await init()
      return true
    } else {
      return false
    }
  }
}
/**
 * 選択中の項目
 */
const selectedItem = ref<string | undefined>()

/**
 * 行選択処理
 *
 * @param item - 対象の困難度
 */
function selectRow(item: Sogaiyouin) {
  selectedItem.value = item.uniqueId
}

/**
 * 選択行かどうか判定
 *
 * @param item - 対象の行
 *
 * @returns true: 選択中, false: 非選択
 */
const isSelected = (item: Sogaiyouin) =>
  selectedItem.value === item.uniqueId

/**
 *テーブルヘッダ
 */
const headers = [
  {
    title: t('label.item-cd-1'),
    key: 'youinCd',
    sortable: true,
  },
  {
    title: t('label.inhibitory-factor'),
    key: 'youinKnj',
    sortable: true,
  },
  {
    title: t('label.display-order'),
    key: Or27607Const.DEFAULT.SORT,
    sortable: false,
    required: true,
  },
]

/**
 *仮想テーブルのソート順リスト
 */
const sortByVTable = ref<OderVTable[]>([])
/**
 * 仮想テーブルのデータ
 */
const tableDataShow = computed(() => {
  if (!sortByVTable.value.length) {
    return refValue.value!.sogaiyouinList
  } else {
    return [...refValue.value!.sogaiyouinList].sort((a, b) => {
      let valA, valB
      if (sortByVTable.value[0].key === Or27607Const.DEFAULT.YOUINKNJ) {
        valA = a.youinKnj.modelValue.value
        valB = b.youinKnj.modelValue.value
        if (valA !== valB) {
          return (
            (sortByVTable.value[0].order === Or27607Const.DEFAULT.ASC ? -1 : 1) *
            valA.localeCompare(valB, 'ja')
          )
        }
      }
      if (sortByVTable.value[0].key === Or27607Const.DEFAULT.YOUINCD) {
        valA = a.youinCd.modelValue.value
        valB = b.youinCd.modelValue.value
        if (valA !== valB) {
          return (
            (sortByVTable.value[0].order === Or27607Const.DEFAULT.ASC ? -1 : 1) *
            (valA < valB ? -1 : 1)
          )
        }
      }
      if (sortByVTable.value[0].key === Or27607Const.DEFAULT.SORT) {
        valA = a.sort.modelValue.value
        valB = b.sort.modelValue.value
        if (valA !== valB) {
          return (
            (sortByVTable.value[0].order === Or27607Const.DEFAULT.ASC ? -1 : 1) *
            (Number(valA) < Number(valB) ? -1 : 1)
          )
        }
      }
      return 0
    })
  }
})
watch(
  () => sortByVTable.value,
  async () => {
    await nextTick()
    selectedItem.value = tableDataShow.value.find((item) => item.visible)?.uniqueId
  }
)
/**
 *新しい困難度データを追加する
 */
const addNewYouin = async () => {
  const largestSort = Math.max(0,
    ...refValue.value!.sogaiyouinList.map((item) => Number(item.sort.modelValue.value))
  )
  const largestCd = Math.max(0,
    ...refValue.value!.sogaiyouinList.map((item) => Number(item.youinCd.modelValue.value))
  )
  const uniqueId = uuidv4()
  refValue.value!.sogaiyouinList.push({
    uniqueId,
    youinCd: { modelValue: { value: `${largestCd + 1}` } },
    youinKnj: { modelValue: { value: '' } },
    sort: {
      modelValue: {
        value: `${largestSort !== Or27607Const.DEFAULT.MAX_LARGEST_SORT ? largestSort + 1 : largestSort}`,
      },
    },
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: systemCommonsStore.getShisetuId,
    svJigyoId: systemCommonsStore.getSvJigyoId,
    modifiedCnt: 1,
    updateKbn: Or27607Const.DEFAULT.CREATE,
    visible: Or27607Const.DEFAULT.TRUE,
  })
  selectedItem.value = uniqueId
  await nextTick()
  const inputText = document.querySelectorAll<HTMLDivElement>('.youinKnjInputText')
  const index = refValue.value!.sogaiyouinList.filter((item) => item.visible).length - 1
  inputText[index]?.querySelector<HTMLInputElement>('input')?.focus()
}
/**
 * 選択中の困難度データを削除（非表示＆更新区分設定）
 */
const deleteYouin = async () => {
  const index = refValue.value!.sogaiyouinList.findIndex(
    (item) => item.uniqueId === selectedItem.value
  )
  const visibleIndex = tableDataShow.value
    .filter((item) => item.visible)
    .findIndex((item) => item.uniqueId === selectedItem.value)
  refValue.value!.sogaiyouinList[index].visible = Or27607Const.DEFAULT.FALSE
  refValue.value!.sogaiyouinList[index].updateKbn = Or27607Const.DEFAULT.D
  selectedItem.value = refValue.value!.sogaiyouinList.filter((item) => item.visible)[
    visibleIndex === Or27607Const.DEFAULT.FIRST_INDEX ? visibleIndex : visibleIndex - 1
  ].uniqueId
  await nextTick()
  const inputText = document.querySelectorAll<HTMLDivElement>('.youinKnjInputText')
  inputText[visibleIndex === Or27607Const.DEFAULT.FIRST_INDEX ? visibleIndex : visibleIndex - 1]
    ?.querySelector<HTMLInputElement>('input')
    ?.focus()
}
/**
 *選択中の困難度データを複製して追加
 */
const copyYouin = async () => {
  const konnando = refValue.value!.sogaiyouinList.find(
    (item) => item.uniqueId === selectedItem.value
  )
  const largestCd = Math.max(0,
    ...refValue.value!.sogaiyouinList.map((item) => Number(item.youinCd.modelValue.value))
  )
  const uniqueId = uuidv4()
  const newKonnando = {
    ...cloneDeep(konnando),
    uniqueId,
    youinCd: { modelValue: { value: String(largestCd + 1) } },
    updateKbn: Or27607Const.DEFAULT.CREATE,
    visible: Or27607Const.DEFAULT.TRUE,
  } as Sogaiyouin
  refValue.value!.sogaiyouinList.push(newKonnando)
  await nextTick()
  selectedItem.value = uniqueId
  const inputText = document.querySelectorAll<HTMLDivElement>('.youinKnjInputText')
  inputText[refValue.value!.sogaiyouinList.filter((item) => item.visible).length - 1]
    ?.querySelector<HTMLInputElement>('input')
    ?.focus()
}
// ポスト最小幅
const columnMinWidth = ref<number[]>([132, 268, 132])
const sortByParam = ref<any>({
  youinCd: {
    sortType: '',
  },
  youinKnj: {
    sortType: '',
  },
})
const handleSort = (key: string) => {
  const current = sortByParam.value[key].sortType
  sortByParam.value[key].sortType = current === '' ? 'asc' : current === 'asc' ? 'desc' : ''
  for (const k in sortByParam.value) {
    if (k !== key) {
      sortByParam.value[k].sortType = ''
    }
  }
  if (sortByParam.value[key].sortType) {
    sortByVTable.value = [{ order: sortByParam.value[key].sortType, key }]
  } else {
    sortByVTable.value = []
  }
}
const resetData = () => {
  screenStore.setCpTwoWay({
    cpId: Or27607Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: {
      sogaiyouinList: [],
    },
    isInit: true,
  })
}
defineExpose({
  changeTabOrCloseModal,
  onSave,
  init,
  resetData,
})
</script>
<template>
  <div class="pt-2 h-100 w-100">
    <c-v-row
      class="pt-2"
      no-gutters
    >
      <base-mo00611
        :oneway-model-value="localOneway.mo00611AddBtnOneway"
        class="mx-1"
        prepend-icon="add"
        @click="addNewYouin"
      />
      <base-mo00611
        :oneway-model-value="{
          ...localOneway.mo00611CopyBtnOneway,
          disabled: !refValue!.sogaiyouinList.filter((item) => item.visible).length,
        }"
        class="mx-1"
        prepend-icon="file_copy"
        @click="copyYouin"
      />
      <base-mo00611
        :oneway-model-value="{
          ...localOneway.mo00611DeleteBtnOneway,
          disabled: !refValue!.sogaiyouinList.filter((item) => item.visible).length,
        }"
        class="mx-1"
        prepend-icon="delete"
        @click="openPopupComfirmDelete"
      />
    </c-v-row>
    <c-v-data-table
      v-resizable-grid="{ columnWidths: columnMinWidth }"
      fixed-header
      :headers="headers"
      height="520px"
      class="table-wrapper elevation-1 table-area"
      :items="tableDataShow"
      :items-per-page="-1"
      hide-default-footer
      hover
    >
      <template #headers="{ columns }">
        <tr>
          <th
            :style="column.sortable ? 'cursor: pointer' : ''"
            v-for="column in columns"
          >
            <c-v-row
              no-gutters
              :class="column.required ? 'red-dot' : ''"
              align="center"
              @click.stop="handleSort(column.key)"
            >
              {{ column.title }}

              <v-icon v-if="column.sortable && sortByParam[column.key].sortType">{{
                sortByParam[column.key].sortType === 'asc' ? 'arrow_upward' : 'arrow_downward'
              }}</v-icon>
              <v-icon
                v-else-if="column.sortable"
                class="arrow-up"
                >arrow_upward</v-icon
              >
            </c-v-row>
          </th>
        </tr>
      </template>
      <template #item="{ item }">
        <tr
          v-if="item.visible"
          :class="{ 'select-row': isSelected(item) }"
          @click="selectRow(item)"
        >
          <td style="text-align: right">
            <base-mo01278
              v-model="item.youinCd.modelValue"
              :oneway-model-value="localOneway.mo01278Oneway"
              :disabled="true"
            >
            </base-mo01278>
          </td>
          <td>
            <g-custom-or-x-0018
              v-model="item.youinKnj.modelValue"
              :oneway-model-value="localOneway.konnanKnjOneway"
            />
          </td>
          <td style="text-align: right">
            <base-mo01278
              v-model="item.sort.modelValue"
              :oneway-model-value="localOneway.mo01278Oneway"
            ></base-mo01278>
          </td>
        </tr>
      </template>
    </c-v-data-table>
  </div>
  <div class="SI020">{{ t('label.save-by-office-unit') }}</div>
  <g-base-or21814 v-bind="or21814ModifiledPopup" />
  <g-base-or21814 v-bind="or21814PermissionPopup" />
  <g-base-or21814 v-bind="or21814ConfirmDelete" />
  <g-base-or21814 v-bind="or21814CantSave" />
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
</template>
<style lang="scss" scoped>
// @use '@/styles/cmn/page-data-table.scss';
@use '@/styles/cmn/dialog-data-table.scss';
.v-row {
  margin: 0 !important;
}

:deep(.table-area) {
  table {
    border-collapse: collapse;
  }
  margin-top: 16px;
  margin-bottom: 32px;
  td {
    padding: 0 !important;
  }
  th {
    cursor: default;
    &:hover .arrow-up {
      display: block;
    }
    .arrow-up {
      color: #898484;
      display: none;
    }
  }
  td:nth-child(2) {
    input {
      padding: 0 16px !important;
    }
  }
  .select-row {
    input:disabled {
      background-color: rgb(var(--v-theme-black-200));
    }
  }
  input {
    width: 100%;
  }
  .red-dot::before {
    content: ' *';
    font-size: 18px;
    color: rgb(var(--v-theme-red-700));
  }
}
.SI020 {
  position: absolute;
  bottom: 0;
  color: rgb(var(--v-theme-black-200));
  font-size: 12px;
}
</style>
