/**
 * Or29976:有機体:印刷設定モーダル
 * GUI00986_［印刷設定］画面
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */

/**
 * 単方向バインドのデータ構造
 */
export interface Or29976OnewayType {
  /**
   * 週間表履歴リスト
   */
  rirekiList: rirekiList[]
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 事業所ID */
  svJigyoId: string
  /** 事業所名 */
  svJigyoName: string
  /**
   * 利用者リスト
   */
  userList: userList[]
  /**
   * 計画期間管理フラグ
   */
  kikanFlg: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * セクション名
   */
  sectionName: string
  /**
   * 選択帳票番号
   */
  choIndex: string
  /**
   * システムコード
   */
  sysCd: string
  /**
   * システム略称
   */
  sysRyaku: string
  /**
   * 職員ID
   */
  shokuId: string
  /**
   * 担当者ID
   */
  managerId: string
  /**
   * 担当ケアマネ設定フラグ
   */
  careManagerInChargeSettingsFlag: number
  /**
   * 処理年月日
   */
  processDate: string
  /**
   * 週間表ID
   */
  week1Id: string
  /**
   * フォーカス設定用イニシャル
   */
  focusSettingInitial?: string[]
}

/**
 * 利用者リスト
 */
export interface userList {
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 利用者名
   */
  nameKnj: string
  /**
   * 利用者番号
   */
  userNumber: string
  /**
   * 性別
   */
  sex: string
}

/**
 * 週間表履歴リスト
 */
export interface rirekiList {
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
  /**
   * 履歴リスト
   */
  historyList: {
    /**
     * 週間表ID
     */
    week1Id: string
    /**
     * 法人ID
     */
    houjinId: string
    /**
     * 施設ID
     */
    shisetuId: string
    /**
     * 事業者ID
     */
    svJigyoId: string
    /**
     * 利用者ID
     */
    userId: string
    /**
     * 作成日
     */
    createYmd: string
    /**
     * 職員ID
     */
    shokuId: string
    /**
     * ケース番号
     */
    caseNo: string
    /**
     * 処理年月
     */
    tougaiYm: string
    /**
     * 有効期間ID
     */
    termid: string
    /**
     * 改訂
     */
    kaiteiFlg: string
    /**
     * 週単位以外ｻｰﾋﾞｽ
     */
    wIgaiKnj: string
  }[]
}
