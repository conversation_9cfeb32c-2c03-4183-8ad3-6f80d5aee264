/**
 * TeX0008_「アセスメント」（包括）画面共通情報モック
 * GUI00834_「アセスメント」（包括）共通画面
 *
 * <AUTHOR>
 */

import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type {
  assessmentComprehensiveQuestionInEntity,
  assessmentComprehensiveQuestionOutWebEntity,
} from '~/repositories/cmn/entities/assessmentComprehensiveQuestionSelect'

/**
 * GUI00834_「アセスメント」（包括）画面共通情報モック
 *
 * @description
 * GUI00834_「アセスメント」（包括）画面共通情報モック画面に表示されるデータを返却する。
 * dataName："problemDotSolutionEtcInfoSelect"
 */
export function handler(inEntity: assessmentComprehensiveQuestionInEntity) {
  const responceJson: BaseResponseBody<assessmentComprehensiveQuestionOutWebEntity> = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
