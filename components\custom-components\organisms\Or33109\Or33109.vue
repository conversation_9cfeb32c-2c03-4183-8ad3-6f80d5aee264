<script setup lang="ts">
/**
 * Or33109：有機体：GUI00676_［情報収集］画面（4）画面
 *
 * @description
 * ［情報収集］画面（4）
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51773Const } from '../Or51773/Or51773.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or33109Const } from './Or33109.constants'
import type { Or33109StateType } from './Or33109.Type'
import { useScreenOneWayBind, useScreenTwoWayBind, useSetupChildProps } from '#imports'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
import type { Or10412OnewayType } from '~/types/cmn/business/components/Or10412Type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type {
  OrCpGroupDefinitionInputFormDeleteDialogType,
  OrCpGroupDefinitionInputFormDeleteDialogOnewayType,
} from '~/types/cmn/business/generator-components/OrCpGroupDefinitionInputFormDeleteDialog'
import type {
  HistoryInfo,
  PlanPeriodInfo,
  RirekiInfo,
  InfoCollectionInfoType,
} from '~/types/cmn/business/components/TeX0005Type'
import type { Or33109OnewayType, Or33109Type } from '~/types/cmn/business/components/Or33109Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Or21744StateType } from '~/components/base-components/organisms/Or21744/Or21744.type'
import type { Mo01280OnewayType } from '~/types/business/components/Mo01280Type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or33109OnewayType
  uniqueCpId: string
  modelValue: Or33109Type
}

const props: Props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/

const { t } = useI18n()

// 子コンポーネント用変数
const or21815 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(0) })
const contentRef = ref<HTMLDivElement | null>(null)

const Mo0009OnewayModelValue: Mo00009OnewayType = {
  icon: true,
  btnIcon: 'edit_square',
  prependIcon: 'edit_square',
  density: 'compact',
}

const defaultOnewayModelValue: Or33109OnewayType = {
  periodManageFlag: '0',
  rirekiInfo: {} as RirekiInfo,
  deleteFlg: false,
}

const localOneway = reactive({
  or33109Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // アセスメント表タイトル
  mo01338Oneway: {
    value: '',
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: 'contentTitle',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // GUI00787 ［メモ入力］画面の単方向バインドModelValue
  or10412Oneway: {
    selectItemNo: '',
    userId: '',
    fontColor: '',
    historyTable: '',
    historyTableColum: '',
    meMoContent: '',
    textSize: '',
    flag: '',
  } as Or10412OnewayType,
  // メッセージ「i-cmn-11267」
  msgDialogOneway11267: {
    msg: t('message.i-cmn-11267'),
    btnNoDisplay: false,
    mo00024Oneway: {
      class: 'mr-1',
      name: '',
      width: '360px',
    } as Mo00024OnewayType,
  } as OrCpGroupDefinitionInputFormDeleteDialogOnewayType,
  // メッセージ「i-cmn-11266」
  msgDialogOneway11266: {
    msg: t('message.i-cmn-11266'),
    btnNoDisplay: false,
    mo00024Oneway: {
      class: 'mr-1',
      name: '',
      width: '360px',
    } as Mo00024OnewayType,
  } as OrCpGroupDefinitionInputFormDeleteDialogOnewayType,
  // メッセージ「e-cmn-41717」
  msgDialogOneway41717: {
    msg: t('message.e-cmn-41717'),
    btnNoDisplay: false,
    mo00024Oneway: {
      class: 'mr-1',
      name: '',
      width: '360px',
    } as Mo00024OnewayType,
  } as OrCpGroupDefinitionInputFormDeleteDialogOnewayType,
  // 全て[〇]コンポーネント
  mo00611OnewayAllRound: {
    btnLabel: t('btn.all-round'),
    width: '90px',
  },
  // 全て[×]コンポーネント
  mo00611OnewayAllWrong: {
    btnLabel: t('btn.all-wrong'),
    width: '90px',
  },
  // 全解除コンポーネント
  mo00611OnewayCancelAll: {
    btnLabel: t('btn.full-release'),
    width: '90px',
  },

  mo01280Oneway: {
    // デフォルト値の設定
    maxLength: 4000,
    rows: '3',
    class: 'area23',
  } as Mo01280OnewayType,
  // ******Visioラジオグループセクション******
  cnsiderOneway: {
    customClass: new CustomClass({ outerClass: 'radio-style' }),
    showItemLabel: false,
    itemLabel: '',
    name: 'body',
    inline: false,
    items: [],
  } as Mo00039OnewayType,
  or21744Oneway: {
    width: '100px',
    minWidth: '100px',
    disabled: false,
  } as Or21744StateType,
  or51775OnewayTypeOther: {
    title: '',
    classificationID: '2',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
})

const local = reactive({
  officeId: '',
  planPeriodId: '',
  historyId: '',
  mstKbn: '1',
  or33109: {
    ...props.modelValue,
  } as Or33109Type,
  // 履歴情報
  historyInfo: {} as HistoryInfo,
  // 計画期間情報
  planPeriodInfo: {} as PlanPeriodInfo,
  // 期間管理フラグ
  kikanKanriFlg: '',
  // 利用者ID
  userId: '',
  // 基準日
  kijunbiYmd: '',
  // ソート後の画面データ
  sortList: {} as Record<string, Record<string, InfoCollectionInfoType[]>>,
})

const respInfo = {
  // 期間管理フラグ
  kikanKanriFlg: '',
  // 計画期間情報
  planPeriodInfo: {
    sc1Id: 0,
    periodNo: 0,
    periodCnt: 0,
    startYmd: '',
    endYmd: '',
  } as PlanPeriodInfo,
  // 履歴情報
  historyInfo: {
    createId: 0,
    sc1Id: '',
    raiId: '',
    assType: '',
    capType: '',
    plnType: '',
    shokuinId: '',
    assDateYmd: '',
    krirekiNo: 0,
    krirekiCnt: 0,
  } as HistoryInfo,
}

// メッセージ「i-cmn-11267」
const msgDialog11267 = ref<OrCpGroupDefinitionInputFormDeleteDialogType>({
  mo00024: {
    isOpen: false,
  } as Mo00024Type,
  emitType: 'clickYes',
})

// メッセージ「i-cmn-11266」
const msgDialog11266 = ref<OrCpGroupDefinitionInputFormDeleteDialogType>({
  mo00024: {
    isOpen: false,
  } as Mo00024Type,
  emitType: 'clickYes',
})

/**
 *  ラジオボタン初期化
 */
function initCodes() {
  localOneway.cnsiderOneway.items?.push({
    label: t('label.circle'),
    value: Or33109Const.DEFAULT.RIGHTSELECTED,
  })
  localOneway.cnsiderOneway.items?.push({
    label: t('label.wrong'),
    value: Or33109Const.DEFAULT.WRONGSELECTED,
  })
}

/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or33109StateType>({
  cpId: Or33109Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      if (value) {
        local.kikanKanriFlg = value.kikanKanriFlg
        local.historyInfo = value.historyInfo
        local.planPeriodInfo = value.planPeriodInfo
        local.userId = value.userId
        local.kijunbiYmd = value.kijunbiYmd
      }
      switch (value?.executeFlag) {
        // 保存
        case Or33109Const.DEFAULT.SAVE:
          void save()
          break
        // 新規
        case Or33109Const.DEFAULT.ADD:
          void add()
          break
        // 複写
        case Or33109Const.DEFAULT.COPY:
          copy()
          break
        // 削除
        case Or33109Const.DEFAULT.DELETE:
          del()
          break
        // データ再取得
        case Or33109Const.DEFAULT.GETDATA:
          break
        default:
          break
      }
    },
  },
})

const { refValue } = useScreenTwoWayBind<Record<string, InfoCollectionInfoType[]>>({
  cpId: Or33109Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * ウォッチャー
 **************************************************/

/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(() => {
  // 初期情報取得
  init()
})

/**
 * 初期化
 */
const init = () => {
  // ラジオボタン初期化
  void initCodes()
  // 情報収集画面の情報を取得する。
  groupByLevel2IdAndKoumokuNo({ ...refValue.value })
}

/**
 * 「新規ボタン」押下
 */
const add = async () => {
  // アセスメント(インターライ)画面履歴の最新情報を取得
}

/**
 * 「保存ボタン」押下
 */
const save = () => {
  // アセスメント(インターライ)画面履歴の最新情報を取得

  // 取得したインターライ方式履歴情報<>NULL
  if (respInfo.historyInfo !== null) {
    // 選定アセスメント種別 > 0
    if (parseInt(respInfo.historyInfo.capType) > 0) {
      // 検討アセスメント種別 > 0
      if (parseInt(respInfo.historyInfo.plnType) > 0) {
        msgDialog11267.value.mo00024.isOpen = true
      }
      // 検討アセスメント種別 <= 0
      if (parseInt(respInfo.historyInfo.plnType) <= 0) {
        msgDialog11266.value.mo00024.isOpen = true
      }
    }
  }
}

/**
 * 「複写ボタン」押下
 */
const copy = () => {
  // 複写処理は、tab1～tab14、「その他」および「服薬状況」タブにおいて実装されています。
}

/**
 * 「削除」押下
 */
const del = () => {
  // 削除処理は、tab1～tab14、「その他」および「服薬状況」タブにおいて実装されています。
}

// 書式2のデータをグループ化する
function groupByLevel2IdAndKoumokuNo(infoList: Record<string, InfoCollectionInfoType[]>) {
  const retList = Object.values(infoList).flat()
  // 階層1タイトルラベル(情報収集画面詳細情報の「第1階層ID」の値、情報収集画面詳細情報の「第1階層名称」の値の組合文字列)
  if (retList !== null && retList.length > 0) {
    localOneway.mo01338Oneway.value = retList[0].level1Id + ' ' + retList[0].level1Knj
    local.sortList = retList.reduce(
      (acc, current) => {
        const level2Key = current.level2Id
        const koumokuKey = Number(current.koumokuNo)

        if (!acc[level2Key]) {
          acc[level2Key] = {} as Record<string, InfoCollectionInfoType[]>
        }
        if (!acc[level2Key][koumokuKey]) {
          acc[level2Key][koumokuKey] = []
        }
        acc[level2Key][koumokuKey].push(current)
        return acc
      },
      {} as Record<string, Record<string, InfoCollectionInfoType[]>>
    )
  }
}

function mo01280Keyup(
  index1: string,
  index2: string,
  index3: number,
  maxLength: number,
  event: KeyboardEvent
) {
  const width = getStringWidth(local.sortList[index1][index2][index3].memo2Knj.value)
  if (
    event.key === 'Enter' &&
    local.sortList[index1][index2][index3].memo2Knj.value.split('\n').length - 1 >= 3
  ) {
    local.sortList[index1][index2][index3].memo2Knj.value = local.sortList[index1][index2][
      index3
    ].memo2Knj.value.substring(0, local.sortList[index1][index2][index3].memo2Knj.value.length - 1)
  }
  if (width > maxLength) {
    local.sortList[index1][index2][index3].memo2Knj.value = local.sortList[index1][index2][
      index3
    ].memo2Knj.value.substring(0, maxLength)
  }
}

function getStringWidth(str: string): number {
  let width = 0
  for (const char of str) {
    const code = char.charCodeAt(0)
    if (
      (code >= 0x3000 && code <= 0x303f) ||
      (code >= 0xff00 && code <= 0xffef) ||
      (code >= 0x4e00 && code <= 0x9fff)
    ) {
      width += 2
    } else {
      width += 1
    }
  }
  return width
}
function getFirstLevel2Knj(group: Record<string, InfoCollectionInfoType[]>) {
  const firstKey = Object.keys(group)[0]
  const firstArray = group[firstKey]

  if (firstArray && firstArray.length > 0) {
    return firstArray[0].level2Knj
  }
  return ''
}

/**
 * 全て[〇]ボタン押下
 *
 */
function onAllSelect1Click() {
  for (const level2Id in local.sortList) {
    for (const koumokuNo in local.sortList[level2Id]) {
      local.sortList[level2Id][koumokuNo].forEach((item) => {
        item.kentoFlg = Or33109Const.DEFAULT.RIGHTSELECTED
      })
    }
  }
}

/**
 * 全て[×]ボタン押下
 *
 */
function onAllSelect2Click() {
  for (const level2Id in local.sortList) {
    for (const koumokuNo in local.sortList[level2Id]) {
      local.sortList[level2Id][koumokuNo].forEach((item) => {
        item.kentoFlg = Or33109Const.DEFAULT.WRONGSELECTED
      })
    }
  }
}

/**
 * 全解除ボタン押下
 *
 */
function onNoSelectClick() {
  for (const level2Id in local.sortList) {
    for (const koumokuNo in local.sortList[level2Id]) {
      local.sortList[level2Id][koumokuNo].forEach((item) => {
        item.kentoFlg = Or33109Const.DEFAULT.UNSELECTED
      })
    }
  }
}

// 選択した行の第１階層Index
const selectedItemIndex = ref<string>('')

// 選択した行の第２階層Index
const selectedItemSubIndex = ref<number>(-1)

// 選択した行の番号Index
const selectedItemKoumokuIndex = ref<number>(-1)

// 選択した行のメモ１ORメモ２
const selectedItemType = ref<number>(-1)
/**
 * 「ケアマネ入力支援アイコンボタン」押下onClickOther
 *
 * @param title - id
 *
 * @param index - 第１階層Index
 *
 * @param subIndex - 第２階層Index
 *
 * @param koumokuIndex - 番号Index
 *
 * @param type - メモ１ORメモ２
 */
function onClickOther(
  title: string,
  index: string,
  subIndex: string,
  koumokuIndex: number,
  type: number
) {
  // その他1の入力支援ポップアップを開く
  or51775Other.value.modelValue = ''

  selectedItemIndex.value = index
  selectedItemSubIndex.value = Number(subIndex)
  selectedItemKoumokuIndex.value = koumokuIndex
  selectedItemType.value = type

  // タイトルを「その他1」または「その他2」に設定
  localOneway.or51775OnewayTypeOther.title = title

  // 入力支援ポップアップを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

//課題t入力
const or51775Other = ref({ modelValue: '' })

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or51773Const.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or51773Const.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  // メモ１の場合
  if (selectedItemType.value === 1) {
    if (
      local.sortList[selectedItemIndex.value][selectedItemSubIndex.value][
        selectedItemKoumokuIndex.value
      ].memo1Knj
    ) {
      local.sortList[selectedItemIndex.value][selectedItemSubIndex.value][
        selectedItemKoumokuIndex.value
      ].memo1Knj.value = setOrAppendValue(
        local.sortList[selectedItemIndex.value][selectedItemSubIndex.value][
          selectedItemKoumokuIndex.value
        ].memo1Knj.value ?? '',
        data
      )
    }
  } else {
    if (
      local.sortList[selectedItemIndex.value][selectedItemSubIndex.value][
        selectedItemKoumokuIndex.value
      ].memo2Knj
    ) {
      local.sortList[selectedItemIndex.value][selectedItemSubIndex.value][
        selectedItemKoumokuIndex.value
      ].memo2Knj.value = setOrAppendValue(
        local.sortList[selectedItemIndex.value][selectedItemSubIndex.value][
          selectedItemKoumokuIndex.value
        ].memo2Knj.value ?? '',
        data
      )
    }
  }
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * スクロールバーのスクロール
 *
 * @param event - WheelEvent
 */
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()

  const delta = Math.sign(event.deltaY)

  const contentElement = contentRef.value
  if (!contentElement) return

  const currentScroll = contentElement.scrollTop
  const maxScroll = contentElement.scrollHeight - contentElement.clientHeight

  let newScroll = currentScroll + delta * 50

  if (newScroll < 0) newScroll = 0
  if (newScroll > maxScroll) newScroll = maxScroll

  if (newScroll !== currentScroll) {
    contentElement.scrollTo({
      top: newScroll,
      behavior: 'auto',
    })
  }
}

watch(
  () => local.or33109,
  () => {
    emit('update:modelValue', local.or33109)
  },
  { deep: true }
)
watch(
  () => refValue.value,
  () => {
    groupByLevel2IdAndKoumokuNo({ ...refValue.value })
  },
  { deep: true }
)
</script>
<template>
  <c-v-row
    v-show="
      props.onewayModelValue.periodManageFlag !== Or33109Const.PLANNING_PERIOD_NO_MANAGE ||
      props.onewayModelValue.copyFlg === true
    "
    no-gutters
    style="padding-top: 8px; background-color: white"
  >
    <c-v-col
      cols="12"
      class="h-100 px-2"
    >
      <!-- 3ボタン -->
      <c-v-row
        v-if="!props.onewayModelValue.hiddenAction"
        no-gutters
        class="top-button"
      >
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAllRound"
          class="mx-1"
          @click="onAllSelect1Click"
        />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAllWrong"
          class="mx-1"
          @click="onAllSelect2Click"
        />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayCancelAll"
          class="mx-1"
          @click="onNoSelectClick"
        />
      </c-v-row>
      <!-- タイトル -->
      <c-v-row no-gutters>
        <c-v-col class="h-100 px-2">
          <!-- 期間管理フラグが「1:管理する」、かつ、計画期間が登録されていない場合、非表示 -->
          <c-v-row
            no-gutters
            class="or33109-title-row"
          >
            <base-mo01338
              v-if="!props.onewayModelValue.deleteFlg"
              :oneway-model-value="localOneway.mo01338Oneway"
              class="back-unset"
            ></base-mo01338>
          </c-v-row>
          <!-- テーブル固定へーた -->
          <c-v-row
            no-gutters
            class="row-tl-border"
            style="width: calc(100% - 15px)"
          >
            <!-- No -->
            <c-v-col
              cols="auto"
              class="col-br-border tbl-title-bg col1"
              ><label class="col-pl">{{ t('label.no') }}</label></c-v-col
            >
            <!-- 情報項目 -->
            <c-v-col class="col-br-border tbl-title-bg col2"
              ><label class="col-pl">{{ t('label.info-collection-info-item') }}</label></c-v-col
            >
            <!-- 計画書様式が【居宅】、改定区分が【初版】の場合 -->
            <!-- 検討 -->
            <c-v-col
              v-if="
                localOneway.or33109Oneway.rirekiInfo &&
                localOneway.or33109Oneway.rirekiInfo.kaiteiKbn !== Or33109Const.KAITEI_KBN_1
              "
              cols="auto"
              class="col-br-border tbl-title-bg col4"
              ><label class="col-pl">{{ t('label.info-collection-consider') }}</label></c-v-col
            >
            <!-- 具体的状況 -->
            <c-v-col class="col-br-border tbl-title-bg col3 width-464"
              ><label class="col-pl">{{
                t('label.info-collection-concrete-situation')
              }}</label></c-v-col
            >
            <!-- 検討 -->
            <c-v-col
              v-if="
                localOneway.or33109Oneway.rirekiInfo &&
                localOneway.or33109Oneway.rirekiInfo.kaiteiKbn !== Or33109Const.KAITEI_KBN_0
              "
              cols="auto"
              class="col-br-border tbl-title-bg col4"
              ><label class="col-pl">{{ t('label.info-collection-consider') }}</label></c-v-col
            >
          </c-v-row>
          <!-- 一覧 -->
          <div
            ref="contentRef"
            class="or33109-main-div"
          >
            <template
              v-for="(group, index) in local.sortList!"
              :key="index"
            >
              <div
                v-if="!props.onewayModelValue.deleteFlg"
                no-gutters
                class="row-l-border"
              >
                <c-v-row no-gutters>
                  <c-v-col
                    cols="12"
                    class="col-br-border tbl-title-bg"
                  >
                    <base-mo01337
                      :oneway-model-value="{
                        value: getFirstLevel2Knj(group),
                        valueFontWeight: 'bold',
                      }"
                    />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  v-for="(item, subIndex) in group"
                  :key="subIndex"
                  no-gutters
                  class="or33109-row"
                >
                  <!-- No -->
                  <c-v-col
                    v-show="item[0].shosiki1Flg !== Or33109Const.DEFAULT.FORMAT_THREE"
                    cols="auto"
                    class="col-br-border align-items-center col1 text-bg"
                    style="justify-content: center"
                    ><base-mo01336
                      :oneway-model-value="{
                        value: item[0].koumokuNo,
                        valueFontWeight: item[0].shosiki1Flg === Or33109Const.DEFAULT.FORMAT_ONE ? 'bold' : 'unset',
                      }"
                    />
                  </c-v-col>
                  <div class="body-content">
                  <div
                      v-for="(koumokuItem, koumokuIndex) in item"
                      :key="koumokuIndex"
                      class="body-item">
                  <!-- 情報項目 -->
                  <c-v-col
                    v-show="koumokuItem.shosiki1Flg !== Or33109Const.DEFAULT.FORMAT_THREE"
                    class="col-br-border align-items-center col2 text-bg"
                  >
                    <!-- 情報収集画面（1）リスト.第３階層書式１が「1」の場合、フォント：太字 -->
                    <base-mo01337
                      :oneway-model-value="{
                        value: koumokuItem.level3Knj,
                        valueFontWeight: koumokuItem.shosiki1Flg === Or33109Const.DEFAULT.FORMAT_ONE ? 'bold' : 'unset',
                      }"
                    />
                  </c-v-col>
                  <c-v-col
                    v-show="koumokuItem.shosiki1Flg === Or33109Const.DEFAULT.FORMAT_THREE"
                    cols="2"
                    class="col-br-border align-items-center text-bg"
                  >
                    <base-mo01337
                      :oneway-model-value="{
                        value: koumokuItem.level3Knj,
                      }"
                    />
                  </c-v-col>
                  <!--検討1ラジオボタン-->
                  <!--改訂区分が「1：H31/1」の場合、非表示-->
                  <c-v-col
                    v-show="
                      localOneway.or33109Oneway.rirekiInfo &&
                      localOneway.or33109Oneway.rirekiInfo.kaiteiKbn ===
                        Or33109Const.KAITEI_KBN_0 &&
                      koumokuItem.shosiki1Flg !== Or33109Const.DEFAULT.FORMAT_THREE
                    "
                    cols="auto"
                    class="col-br-border align-items-center col4"
                    ><div class="radio1">
                      <base-mo00039
                        v-model="koumokuItem.kentoFlg"
                        :oneway-model-value="localOneway.cnsiderOneway"
                      /></div
                  ></c-v-col>
                  <!-- 具体的状況 -->
                  <c-v-col class="col-br-border col3">
                    <div style="display: flex">
                      <base-mo01280
                        v-model="koumokuItem.memo1Knj"
                        :oneway-model-value="localOneway.mo01280Oneway"
                        @keyup="
                                    mo01280Keyup(
                                      index,
                                      subIndex,
                                      koumokuIndex,
                                      koumokuItem.shosiki1Flg !== Or33109Const.DEFAULT.FORMAT_THREE?Or33109Const.textarea_maxlength_50:Or33109Const.textarea_maxlength_87,
                                      $event
                                    )
                                  "
                      />
                      <c-v-divider
                        vertical
                        class="mr-1 mt-1 mb-1"
                      />
                      <base-mo00009
                        :oneway-model-value="Mo0009OnewayModelValue"
                        variant="flat"
                        density="compact"
                        style="margin-top: 18px"
                        @click="onClickOther(koumokuItem.level3Knj, index, subIndex,koumokuIndex,1)"
                      /></div
                  ></c-v-col>

                  <!--検討2ラジオボタン-->
                  <!--・改訂区分が「0：初版」の場合、非表示-->
                  <c-v-col
                    v-show="
                      localOneway.or33109Oneway.rirekiInfo &&
                      localOneway.or33109Oneway.rirekiInfo.kaiteiKbn ===
                        Or33109Const.KAITEI_KBN_1 &&
                      koumokuItem.shosiki1Flg !== Or33109Const.DEFAULT.FORMAT_THREE
                    "
                    cols="auto"
                    class="col-br-border align-items-center col4"
                    ><div class="radio1">
                      <base-mo00039
                        v-model="koumokuItem.kentoFlg"
                        :oneway-model-value="localOneway.cnsiderOneway"
                      /></div
                  ></c-v-col>
                  </div>
                  </div>
                </c-v-row>
              </div>
            </template>
          </div>
        </c-v-col>
      </c-v-row>
    </c-v-col>
  </c-v-row>
  <div
    v-if="props.onewayModelValue.copyFlg === true"
    class="overlay"
    @wheel="handleWheel"
  ></div>
  <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Other"
    :oneway-model-value="localOneway.or51775OnewayTypeOther"
    @confirm="handleOr51775Confirm"
  ></g-custom-or-51775>
</template>

<style lang="scss" scoped>
@use '@/styles/base-data-table-list.scss';

:deep(.ml-4) {
  margin-left: 0px !important;
}

.container {
  overflow-y: auto;
  max-height: 600px;

  :has(.contentTitle) {
    font-size: 18px !important;
  }

  :has(.contentItem) {
    :deep(.item-label) {
      color: #800000;
    }
  }

  .item-col-position {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .title {
    background: #ffffff;
    padding-left: 8px;
  }
}

.container {
  height: 142px;
  overflow-y: auto;
}

.container2 {
  height: 142px;
  overflow-y: auto;
}

.container3 {
  overflow-y: auto;
  height: 550px;
  padding-bottom: 4px;
}

.container4 {
  height: 350px;
  overflow-y: auto;
}

.container5 {
  height: 350px;
  overflow-y: auto;
}

:deep(.v-selection-control-group--inline) {
  flex-direction: column !important;
}

:deep(.v-selection-control--density-default) {
  --v-selection-control-size: 36px !important;
}

.buttonActive {
  background: #dbeefe;
  color: white;
  border-color: black;

  :deep(.v-btn__content > span) {
    color: rgb(var(--v-theme-black-500)) !important;
  }
}

.radio1 {
  display: flex;
  :deep(.v-selection-control-group > div) {
    margin-bottom: -6px;
    margin-top: -8px;
  }
}
.title-back {
  background-color: unset;
}
.title-weight {
  font-weight: bold;
}
.contentTitle {
  margin-bottom: 18px !important;
}

.top-unset {
  margin-top: unset;
  padding-top: unset;
}
.back-unset {
  background: transparent;
}
.title-no {
  cursor: default;
  width: 52px;
  position: sticky;
  top: 0;
  z-index: 2;
  text-align: center !important;
  font-weight: 400 !important;
  height: 50px !important;
}
.title-info-item {
  cursor: default;
  position: sticky;
  top: 0;
  z-index: 2;
  text-align: center !important;
  font-weight: 400 !important;
}
.title-situation {
  cursor: default;
  width: 466px;
  position: sticky;
  top: 0;
  z-index: 2;
  text-align: center !important;
  font-weight: 400 !important;
}
.title-consider {
  cursor: default;
  width: 63px;
  position: sticky;
  top: 0;
  z-index: 2;
  text-align: center !important;
  font-weight: 400 !important;
}
.padding-zero {
  padding: 0;
}
.cursor-def {
  cursor: default;
}
.content-no {
  text-align: center;
  width: 54px;
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.width-464 {
  width: 464px;
}
.width-62 {
  width: 63px;
}
.disp-flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.content-lev3knj {
  text-align: center;
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.width-760 {
  width: 760px;
}
.width-84 {
  width: 84.5%;
}

.row-tl-border {
  border-top: 1px rgb(var(--v-theme-black-200)) solid;
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

.row-l-border {
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-br-border {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-pl {
  padding-left: 4px !important;
}

.tbl-title-bg {
  background-color: rgb(var(--v-theme-black-100));
}

.top-button {
  padding-bottom: 8px;
  border-bottom: 1px solid rgb(224, 224, 224);
}

.font-bold {
  font-weight: bold;
}

.align-items-center {
  display: flex;
  align-items: center;
}

.radio-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.or33109-main-div {
  overflow-y: auto;
  max-height: 540px;
  scrollbar-gutter: stable;
  position: relative;
  z-index: 999;
}

.or33109-row {
  flex-wrap: nowrap;
}

.or33109-title-row {
  height: 21px;
}

:deep(.col1) {
  width: 54px;
  flex-shrink: 0;
}

:deep(.col2) {
  flex-grow: 2;
  padding: 0 !important;
}

:deep(.col3) {
  flex-grow: 3;
  padding: 0 !important;
}

:deep(.col4) {
  width: 64px;
  flex-shrink: 0;
  padding: 0 !important;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 1000;
  pointer-events: auto;
  cursor: not-allowed;
}
.body-content{
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
.body-item{
  display: flex;
  width: 100%;
  height: 68px;
}
.text-bg {
  background-color: rgb(242, 242, 242);
}
.full-width-field {
  padding: 0 3px !important;
}
</style>
