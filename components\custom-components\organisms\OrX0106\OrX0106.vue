<script setup lang="ts">
/**
 * OrX0106:動的フォームコンポーネント
 *
 * @description
 * 動的フォームコンポーネント
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, toRefs, watch, watchEffect } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27339Logic } from '../Or27339/Or27339.logic'
import { Or27339Const } from '../Or27339/Or27339.constants'
import type { OrX0106Type } from './OrX0106.type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { RyoikiMediumBunruiListType } from '~/repositories/cmn/entities/AssessmentPackageplanEntity'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import { useSetupChildProps } from '#imports'
import type {
  BedriddenDegreeDementiaDegreeSelectScreenType,
  Or27339Type,
} from '~/types/cmn/business/components/Or27339Type'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: OrX0106Type
  uniqueCpId: string
}

const props = defineProps<Props>()

// 応答性を維持します
const { onewayModelValue } = toRefs(props)
watchEffect(() => console.log(onewayModelValue))

const or27339 = ref({ uniqueCpId: '' })

const local = reactive({
  recordDate: {
    value: '',
    mo01343: {
      value: '',
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  or27339Type: {
    bedriddenDegreeDementiaDegreeSelectScreenList: [],
  } as Or27339Type,
})

const localOneway = {
  // 障害者老人日常生活自立度ラベル
  handycapElderlyIndependenceLevelMo01338Oneway: {
    /** 値 */
    value: '障害者老人日常生活自立',
    customClass: new CustomClass({
      outerClass: 'background-color mb-2',
    }),
  } as Mo01338OnewayType,
  // 認知症老人日常生活自立度ラベル
  dementiaElderlyIndependenceLevelMo01338Oneway: {
    /** 値 */
    value: '認知症老人日常生活自立度',
    customClass: new CustomClass({
      outerClass: 'background-color mb-2',
    }),
  } as Mo01338OnewayType,
  // 自立度取込ボタン
  importantBtnOneway: {
    btnLabel: '自立度取込',
  } as Mo00611OnewayType,
  //障害者老人日常生活自立 selectOptions
  handycapMo00040Oneway: {
    showItemLabel: false,
    width: '100%',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  //認知症老人日常生活自立 selectOptions
  or27339Data: {
    svJigyoId: '',
    shisetuId: '',
    userId: '',
    houjinId: '',
    sc1Id: '-1',
    certificationFlg: '',
    kinScreenKbn: '4',
  } as BedriddenDegreeDementiaDegreeSelectScreenType,
  dementiaMo00040Oneway: {
    showItemLabel: false,
    width: '100%',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  evaluationMo00040Oneway: {
    showItemLabel: false,
    width: '100%',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
    customClass: new CustomClass({
      outerClass: 'noRigthMargin',
    }),
  } as Mo00040OnewayType,
  residentPreferenceMo00040Oneway: {
    showItemLabel: false,
    width: '100%',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
    customClass: new CustomClass({
      outerClass: 'noRigthMargin',
    }),
  } as Mo00040OnewayType,
  residentPreferencemo00045OnewayType: {
    showItemLabel: false,
    isVerticalLabel: true,
    customClass: new CustomClass({
      outerClass: 'noRigthMargin',
    }),
    maxLength: '10',
  } as Mo00045OnewayType,
  priorityMo00040Oneway: {
    showItemLabel: false,
    width: '100%',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
    customClass: new CustomClass({
      outerClass: 'noRigthMargin',
    }),
  } as Mo00040OnewayType,
  evaluationIcon: {
    btnIcon: 'edit_square',
    name: 'evaluationIcon',
    density: 'compact',
  } as Mo00009OnewayType,
  residentPreferenceIcon: {
    btnIcon: 'edit_square',
    name: 'evaluationIcon',
    density: 'compact',
  } as Mo00009OnewayType,
  priorityIcon: {
    btnIcon: 'edit_square',
    name: 'evaluationIcon',
    density: 'compact',
  } as Mo00009OnewayType,
  projectNameMo01338Oneway: {
    itemLabel: t('label.project-name'),
    customClass: new CustomClass({
      outerClass: 'background-color-blue py-2 ',
      itemClass: 'w-0 h-0',
    }),
  } as Mo01338OnewayType,
  judgingResultMo01338Oneway: {
    itemLabel: t('label.judgment-result'),
    customClass: new CustomClass({
      outerClass: 'background-color-blue py-2',
      itemClass: 'w-0 h-0',
    }),
  } as Mo01338OnewayType,
  evaluationMo01338Oneway: {
    itemLabel: t('label.evaluation'),
    customClass: new CustomClass({
      outerClass: 'background-color-blue font-color-blue text-center  py-2 ',
      itemClass: 'w-0 h-0',
    }),
  } as Mo01338OnewayType,
  residentPreferenceMo01338Oneway: {
    itemLabel: t('label.resident-preference'),
    customClass: new CustomClass({
      outerClass: 'background-color-blue font-color-blue text-center  py-2',
      itemClass: 'w-0 h-0',
    }),
  } as Mo01338OnewayType,
  priorityMo01338Oneway: {
    itemLabel: t('label.assessment-area-selection-screen-header-yusenKbn'),
    customClass: new CustomClass({
      outerClass: 'background-color-blue text-center font-color-blue py-2',
      itemClass: 'w-0 h-0',
    }),
  } as Mo01338OnewayType,
  mo00020Oneway: {
    showItemLabel: false,
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass',
      labelClass: 'ma-1',
    }),
    width: '150px',
  } as Mo00020OnewayType,
  articleSupportedBtn: {
    btnIcon: 'edit_square',
    name: 'articleSupportedBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  btnTooltip: {
    articleSupportedBtn: t('tooltip.assessment-home-6-1-servey-ledger-import'),
    medicineSupportedBtn: '薬',
  },
  //薬
  medicineBtnLabelOneway: {
    value: '薬',
    customClass: new CustomClass({
      outerClass: 'background-color',
      labelClass: 'd-none',
    }),
  },

  iconTooltip: {
    evaluationIcon: t('１１１'),
    residentPreferenceIcon: '',
    priorityIcon: '',
  },
}

// ダイアログ表示フラグ
const showDialogOr27339 = computed(() => {
  // Or27339のダイアログ開閉状態
  return Or27339Logic.state.get(or27339.value.uniqueCpId)?.isOpen ?? false
})

// 自立度情報表示フラグ
const showIndependenceLevelFlg = computed(
  () => onewayModelValue.value.id === '1' && onewayModelValue.value.fieldId === '1'
)

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or27339Const.CP_ID(0)]: or27339.value,
})

/**
 *  動的なオフセット値を取得する
 *
 * @param item -アセスメント中分類情報
 */
const getDynamicMargin = (item: RyoikiMediumBunruiListType) => {
  if (!item.children) return
  const count =
    item.koumokuKnj.includes('身長') || item.koumokuKnj.includes('服薬状況')
      ? item.children.length - 2
      : item.children.length - 1
  return item.children.length > 1 ? (count + 1) * 45 + 'px' : ''
}

/**
 *  文章入力支援選択ボタンクリック
 */
const articleSupportedBtnClick = () => {}

/**
 *  自立度取込ボタンクリック
 *
 * @description GUI00810 ［寝たきり度・認知症度選択画面］画面をポップアップで起動する。
 */
const onImportant = () => {
  // Or27339のダイアログ開閉状態を更新する
  Or27339Logic.state.set({
    uniqueCpId: or27339.value.uniqueCpId,
    state: { isOpen: true },
  })
}

watch(
  () => local.or27339Type,
  () => {
    console.log(local.or27339Type)
  }
)
</script>

<template>
  <c-v-sheet class="background-color pa-2">
    <!-- 自立度情報  -->
    <!-- 領域A-分類aに固定表示内容、領域A-分類a以外に表示しない -->
    <c-v-row
      v-if="showIndependenceLevelFlg"
      class="mb-2"
      no-gutters
    >
      <c-v-col cols="auto mr-2">
        <div>
          <base-mo01338
            :oneway-model-value="localOneway.handycapElderlyIndependenceLevelMo01338Oneway"
          />
          <base-mo00040 :oneway-model-value="localOneway.handycapMo00040Oneway" />
        </div>
      </c-v-col>
      <c-v-col cols="auto">
        <div>
          <base-mo01338
            :oneway-model-value="localOneway.dementiaElderlyIndependenceLevelMo01338Oneway"
          />
          <base-mo00040 :oneway-model-value="localOneway.dementiaMo00040Oneway" />
        </div>
      </c-v-col>
      <c-v-col cols="auto ml-2 d-flex align-end">
        <base-mo00611
          :oneway-model-value="localOneway.importantBtnOneway"
          @click="onImportant"
        />
      </c-v-col>
      <!-- ジャンプボタンSlot  -->
      <c-v-col class="text-right">
        <slot name="jumpButton" />
      </c-v-col>
    </c-v-row>
    <c-v-row
      v-else
      class="pb-2"
      no-gutters
    >
      <!-- ジャンプボタンSlot  -->
      <c-v-col class="text-right">
        <slot name="jumpButton" />
      </c-v-col>
    </c-v-row>
    <div
      no-gutters
      class="border-all"
    >
      <!-- 固定タイトル -->
      <c-v-row
        no-gutters
        class="border-bottom background-color-blue"
      >
        <!-- 項目タイトル -->
        <c-v-col
          cols="2"
          class="d-flex justify-center border-right"
        >
          <base-mo01338 :oneway-model-value="localOneway.projectNameMo01338Oneway" />
        </c-v-col>
        <!-- 判断結果 -->
        <c-v-col
          cols="6"
          class="d-flex justify-center border-right"
        >
          <base-mo01338 :oneway-model-value="localOneway.judgingResultMo01338Oneway" />
        </c-v-col>
        <c-v-col cols="4 d-flex justify-center ">
          <!--評価 -->
          <div class="border-right w-30 d-flex justify-center align-center">
            <base-mo01338 :oneway-model-value="localOneway.evaluationMo01338Oneway" />
            <base-mo00009 :oneway-model-value="localOneway.evaluationIcon">
              <c-v-tooltip
                activator="parent"
                location="top"
                :text="localOneway.iconTooltip.evaluationIcon"
              />
            </base-mo00009>
          </div>
          <!--入所者の意向 -->
          <div class="border-right w-40 d-flex justify-center align-center">
            <base-mo01338 :oneway-model-value="localOneway.residentPreferenceMo01338Oneway" />
            <base-mo00009 :oneway-model-value="localOneway.residentPreferenceIcon">
              <c-v-tooltip
                activator="parent"
                location="top"
                :text="localOneway.iconTooltip.residentPreferenceIcon"
              />
            </base-mo00009>
          </div>

          <!--優先度 -->
          <div class="border-right w-30 d-flex justify-center align-center">
            <base-mo01338 :oneway-model-value="localOneway.priorityMo01338Oneway" />
            <base-mo00009 :oneway-model-value="localOneway.priorityIcon">
              <c-v-tooltip
                activator="parent"
                location="top"
                :text="localOneway.iconTooltip.priorityIcon"
              />
            </base-mo00009>
          </div>
        </c-v-col>
      </c-v-row>
      <!-- 項目一覧エリア -->
      <c-v-row
        v-for="(item, index) in onewayModelValue.projectsList"
        :key="index"
        no-gutters
        :class="[index === onewayModelValue.projectsList.length - 1 ? '' : 'border-bottom']"
      >
        <!-- 1列目 -->
        <c-v-col
          cols="2"
          class="border-right"
        >
          <c-v-row no-gutters>
            <c-v-col
              v-if="item.content?.length > 1"
              cols="2"
              class="border-right"
              :style="{
                height: item?.rowHeightSpeed * 45 + 'px',
              }"
            >
              <base-mo01337 :oneway-model-value="{ value: item.mainTitle }" />
            </c-v-col>
            <c-v-col>
              <c-v-row
                v-for="(subItem, subKey) in item.content"
                :key="subKey"
                no-gutters
                :style="{
                  marginTop: subKey && getDynamicMargin(subItem),
                }"
                :class="[!subKey ? '' : 'border-top']"
              >
                <c-v-col
                  cols="12"
                  class="text-left pa-2 row-height align-stretch"
                >
                  <base-mo01337
                    :oneway-model-value="{
                      value: !!subItem?.koumoku2Knj ? subItem?.koumoku2Knj : subItem?.koumokuKnj,
                    }"
                  />
                </c-v-col>
                <c-v-col
                  v-if="subItem.koumokuKnj.indexOf('身長') !== -1"
                  class="text-right pa-2"
                  cols="12"
                >
                  <base-mo00020
                    v-model="local.recordDate"
                    :oneway-model-value="localOneway.mo00020Oneway"
                  />
                </c-v-col>
                <c-v-col
                  v-if="subItem.koumokuKnj.indexOf('服薬状況') !== -1"
                  class="d-flex align-center justify-end pa-2"
                  cols="12"
                >
                  <base-mo01338 :oneway-model-value="localOneway.medicineBtnLabelOneway" />
                  <base-mo00009
                    :oneway-model-value="localOneway.articleSupportedBtn"
                    @click="articleSupportedBtnClick"
                  >
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :text="localOneway.btnTooltip.medicineSupportedBtn"
                    />
                  </base-mo00009>
                </c-v-col>
              </c-v-row>
              <!-- <c-v-row no-gutters>
                <c-v-col
                  cols="12"
                  class="text-right pa-2 row-height"
                  :style="{
                    marginTop: getDynamicMargin(item),
                  }"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.articleSupportedBtn"
                    @click="articleSupportedBtnClick"
                  >
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :text="localOneway.btnTooltip.articleSupportedBtn"
                    />
                  </base-mo00009>
                </c-v-col>
              </c-v-row> -->
            </c-v-col>

            <!-- <c-v-col>
              <c-v-row no-gutters>
                <c-v-col
                  cols="12"
                  class="text-left pa-2 row-height align-stretch"
                >
                  <base-mo01337
                    :oneway-model-value="{
                      value: !!item.koumoku2Knj ? item.koumoku2Knj : item.koumokuKnj,
                    }"
                  />
                </c-v-col>
                <c-v-col
                  v-if="item.koumokuKnj?.indexOf('身長') !== -1"
                  class="text-right row-height pa-2"
                  cols="12"
                >
                  <base-mo00020
                    v-model="local.recordDate"
                    :oneway-model-value="localOneway.mo00020Oneway"
                  />
                </c-v-col>
                <c-v-col
                  v-if="item.koumokuKnj?.indexOf('服薬状況') !== -1"
                  class="d-flex align-center justify-end row-height pa-2"
                  cols="12"
                >
                  <base-mo01338 :oneway-model-value="localOneway.medicineBtnLabelOneway" />
                  <base-mo00009
                    :oneway-model-value="localOneway.articleSupportedBtn"
                    @click="articleSupportedBtnClick"
                  >
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :text="localOneway.btnTooltip.medicineSupportedBtn"
                    />
                  </base-mo00009>
                </c-v-col>
              </c-v-row>
            </c-v-col> -->
          </c-v-row>
        </c-v-col>
        <!-- 2列目 -->
        <c-v-col
          cols="6"
          class="border-right"
        >
          <template
            v-for="(item_1, key_1) in item.content"
            :key="key_1"
          >
            <c-v-row
              v-for="(it, itKey) in item_1.children"
              :key="itKey"
              no-gutters
              class="align-center pa-2 row-height"
            >
              <c-v-col
                v-for="(el, elKey) in it.customComponents"
                :key="elKey"
                cols="auto "
                :class="{ 'flex-grow-1': Number(el.type) === 3 }"
              >
                <template v-if="Number(el.type) === 3">
                  <base-mo00045
                    v-model="el.value"
                    :oneway-model-value="{
                      maxLength: '22',
                      class: 'text-filed',
                      minWidth: '80px',
                      showItemLabel: false,
                    }"
                  />
                </template>
                <template v-if="Number(el.type) === 2">
                  <base-mo00018
                    v-model="el.value"
                    :oneway-model-value="{
                      checkboxLabel: el.label,
                      showItemLabel: false,
                    }"
                  />
                </template>
                <template v-if="Number(el.type) === 1">
                  <base-mo01337
                    :oneway-model-value="{
                      value: el.label,
                    }"
                  />
                </template>
              </c-v-col>
              <!-- <c-v-col
              v-for="(el, key) in 10"
              :key="key"
              cols="auto"
            >
              <div v-if="Number(subItem['chk' + el + 'Kbn']) === 3">
                <base-mo00045
                  :oneway-model-value="{
                    maxLength: '22',
                    class: 'text-filed',
                    width: '100px',
                    showItemLabel: false,
                  }"
                />
              </div>
              <div v-if="Number(subItem['chk' + el + 'Kbn']) === 2">
                <base-mo00018
                  :oneway-model-value="{
                    checkboxLabel: subItem['chk' + el + 'Knj'],
                    showItemLabel: false,
                  }"
                />
              </div>
              <div v-if="Number(subItem['chk' + el + 'Kbn']) === 1">
                <base-mo01337
                  :oneway-model-value="{
                    value: subItem['chk' + el + 'Knj'],
                  }"
                />
              </div>
            </c-v-col> -->
            </c-v-row>
            <!-- 固定行 -->
            <c-v-row no-gutters>
              <c-v-col
                cols="12"
                class="pa-2 row-height"
              >
                <!-- <base-mo00045
                class="text-filed"
                :oneway-model-value="{
                  maxLength: '22',

                  showItemLabel: false,
                }"
              >
                <template #append> 666</template>
              </base-mo00045> -->
                <base-at-text-field
                  class="custom_input"
                  prepend-icon="edit_square"
                  :hide-details="true"
                  @click:prepend="articleSupportedBtnClick"
                >
                </base-at-text-field>
              </c-v-col>
            </c-v-row>
          </template>
        </c-v-col>
        <c-v-col
          cols="4"
          class="d-flex"
        >
          <div class="border-right pa-2 d-flex align-center w-30">
            <!-- 3列目 -->
            <base-mo00040 :oneway-model-value="localOneway.evaluationMo00040Oneway" />
          </div>
          <div class="border-right pa-2 d-flex flex-column justify-space-between w-40">
            <!-- 4列目 -->
            <base-mo00040 :oneway-model-value="localOneway.residentPreferenceMo00040Oneway" />
            <base-mo00045 :oneway-model-value="localOneway.residentPreferencemo00045OnewayType" />
          </div>
          <div class="border-right pa-2 w-30 d-flex align-center">
            <!-- 5列目 -->
            <base-mo00040 :oneway-model-value="localOneway.priorityMo00040Oneway" />
          </div>
        </c-v-col>
      </c-v-row>
    </div>
    <g-custom-or-27339
      v-if="showDialogOr27339"
      v-bind="or27339"
      v-model="local.or27339Type"
      :oneway-model-value="localOneway.or27339Data"
    />
  </c-v-sheet>
</template>

<style scoped lang="scss">
.border-all {
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.border-right {
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.border-bottom {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.border-top {
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.row-height {
  height: 45px !important;
  overflow: hidden;
}
.background-color-blue {
  background-color: rgb(var(--v-theme-blue-200)) !important;
}

.main_formContent {
  overflow-x: auto;
}
:deep(.v-field__field) input {
  min-height: 30px !important;
  height: 30px !important;
  padding: 4px !important;
}
.createDateOuterClass {
  width: auto !important;
  background: rgb(var(--v-theme-background));

  :deep(.must-badge) {
    margin-left: unset;
  }
  :deep(.text-date-area-style) {
    justify-content: right;
  }
  :deep(.v-input__details) {
    display: none;
  }
}
.noRigthMargin :deep(.v-sheet.mr-2) {
  margin: 0 !important;
}

.w-30 {
  width: 30% !important;
}
.w-40 {
  width: 40% !important;
}
.text-filed :deep(.v-field__field) {
  width: 1000px !important;
}

.font-color-blue :deep(.v-row:nth-child(1) .item-label:nth-child(1)) {
  color: rgb(var(--v-theme-blue-800)) !important;
  border-bottom: 1px solid rgb(var(--v-theme-blue-800));
  cursor: pointer;
}
.custom_input :deep(.v-input__prepend) {
  // border: 1px solid red;
  margin-right: 0;
  padding: 0 4px;
  border-radius: 4px;
  background-color: rgb(var(--v-theme-blue-100)) !important;
  color: black;
}
</style>
