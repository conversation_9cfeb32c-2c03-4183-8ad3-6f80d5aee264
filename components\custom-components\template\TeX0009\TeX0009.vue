<script setup lang="ts">
/**
 * TeX0009:GUI00816_アセスメント(パッケージプラン)
 *
 * @description
 * アセスメント(パッケージプラン)
 *
 * <AUTHOR>
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { Or13850Const } from '../../organisms/Or13850/Or13850.constants'
import { OrT0001Const } from '../../organisms/OrT0001/OrT0001.constants'
import type { OrT0001OneWayType, OrT0001Type } from '../../organisms/OrT0001/OrT0001.type'
import { OrT0003Const } from '../../organisms/OrT0003/Ort0003.constants'
import { Or13872Const } from '../../organisms/Or13872/Or13872.constants'
import type { OrT0003OneWayType, OrT0003Type } from '../../organisms/OrT0003/OrT0003.type'
import { Or27562Const } from '../../organisms/Or27562/Or27562.constants'
import { OrX0106Const } from '../../organisms/OrX0106/OrX0106.constants'
import type { Mo00018Type } from '../../../../types/business/components/Mo00018Type'
import type { Or61746OnewayType } from '../../organisms/Or61746/Or61746.type'
import { Or61746Logic } from '../../organisms/Or61746/Or61746.logic'
import { Or61746Const } from '../../organisms/Or61746/Or61746.constants'
import { Or13844Const } from '../../organisms/Or13844/Or13844.constants'
import { Or13844Logic } from '../../organisms/Or13844/Or13844.logic'
import { OrX0115Const } from '../../organisms/OrX0115/OrX0115.constants'
import { OrX0115Logic } from '../../organisms/OrX0115/OrX0115.logic'
import { OrT0003Logic } from '../../organisms/OrT0003/OrT0003.logic'
import { Or13872Logic } from '../../organisms/Or13872/Or13872.logic'
import { Or61587Const } from '../../organisms/Or61587/Or61587.constants'
import { Or61588Const } from '../../organisms/Or61588/Or61588.constants'
import type {
  ClassificationTabsListType,
  CommonInfoType,
  CustomComponentsType,
  OrX0106Component,
  OrX0106ComponentsListType,
} from './TeX0009.type'
import { TeX0009Logic } from './TeX0009.logic'
import { TeX0009Const } from './TeX0009.constants'
import { useUserListInfo } from '~/utils/useUserListInfo'
import {
  useScreenUtils,
  useJigyoList,
  useScreenInitFlg,
  useScreenStore,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import type { Or13844OnewayType } from '~/types/cmn/business/components/Or13844Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'

import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'

import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'

import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

import type { Or27562OnewayType } from '~/types/cmn/business/components/Or27562Type'
import type {
  AssessmentPackageplanHistoryChangeSelectInEntity,
  AssessmentPackageplanHistoryChangeSelectOutEntity,
  AssessmentPackageplanInitCommonSelectInEntity,
  AssessmentPackageplanInitCommonSelectOutEntity,
  AssessmentPackageplanInitSelectInEntity,
  AssessmentPackageplanInitSelectOutEntity,
  AssessmentPackageplanPeriodChangeSelectInEntity,
  AssessmentPackageplanPeriodChangeSelectOutEntity,
  CpnTucSypAss2InfoListType,
  CpnTucSypAss3InfoListType,
  CpnTucSypAss4InfoListType,
  RyoikiMediumBunruiListType,
} from '~/repositories/cmn/entities/AssessmentPackageplanEntity'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { ResBodyStatusCode } from '~/constants/api-constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import { DIALOG_BTN, SPACE_WAVE } from '~/constants/classification-constants'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import type { Or13872OnewayType } from '~/types/cmn/business/components/Or13872Type'
import type { Or13850OnewayType } from '~/types/cmn/business/components/Or13850Type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

const { setChildCpBinds } = useScreenUtils()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

// 利用者一覧変更監視
const { syscomUserSelectWatchFunc } = useUserListInfo()

// 事業所選択変更監視
const { jigyoListWatch } = useJigyoList()

const or11871 = ref({ uniqueCpId: Or11871Const.CP_ID })
const or00248 = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const orT0001 = ref({ uniqueCpId: '' })
const or13872 = ref({ uniqueCpId: Or13872Const.CP_ID(0) })
const or61746 = ref({ uniqueCpId: '' })
const or61587_1 = ref({ uniqueCpId: '' })
const or61587_2 = ref({ uniqueCpId: '' })
const or61588_1 = ref({ uniqueCpId: '' })
const or61588_2 = ref({ uniqueCpId: '' })

const orT0003 = ref({ uniqueCpId: '' })
// 確認ダイアログ(yes, no, cancel)
const or21814_1 = ref({ uniqueCpId: '' })
// 確認ダイアログ(yes)
const or21814_2 = ref({ uniqueCpId: '' })

// GUI00626_アセスメントマスタ
const or27562 = ref({ uniqueCpId: '' })

// 事業所選択
const or41179 = ref({ uniqueCpId: '' })
// 職員検索
const or13850 = ref({ uniqueCpId: Or13850Const.CP_ID(0) })

// 有機体: 計画対象期間入力
const or13844 = ref({ uniqueCpId: '' })

// 有機体:作成者
const orX0115 = ref({ uniqueCpId: '' })

const orX0106Str = 'orX0106_'

// 動的フォームList
const orX0106Components = reactive<Record<string, OrX0106Component>>({})

// 選択中利用者ID
const userId = ref('')

// 作成日バックアップ値
const createDateBk = ref('')

// 入力作成日バックアップ
const createDateOld = ref('')

// 初回作成日バックアップ値
// const initCreateDateBk = ref('')

// 期間管理フラグ
const plannningPeriodManageFlg = ref('1')

// 履歴表示フラグ
const isHistoryShow = ref(true)

// 画面情報領域のsupplement内のisInitの取得およびonMountedにfalseへ更新する処理を登録
const isInit = useScreenInitFlg()

// 履歴更新回数
const historyModifiedCnt = ref('0')

// 複写コンポネントキー
const copyComponentKey = ref('')

// 画面保存区分
const screenSaveKbn = ref('')

// 一時保存事業所
const temporarySaveOfficeModelvalue = ref<string | undefined>('')

/**
 * 共通情報
 */
const commonInfo = ref<CommonInfoType>({
  sc1Id: '',
  houjinId: '',
  shisetuId: '',
  svJigyoId: '',
  userId: '',
  createYmd: '',
  shokuId: '',
})

// ローカルTwoway
const local = reactive({
  orT0003: {} as OrT0003Type,
  orT0001: {
    officeDataList: [],
    termid: 0,
  } as OrT0001Type,
  // 作成日
  createDate: {
    value: '',
    mo01343: {
      value: '',
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  // 初回作成日
  initCreateDate: {
    value: '',
    mo01343: {
      value: '',
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  filedMo00043: {
    id: '',
  } as Mo00043Type,
  classificationMo00043: {
    id: '',
  } as Mo00043Type,
  summaryTabMo00043: {
    id: '',
  } as Mo00043Type,
  caseSeq: { value: '' } as Mo00045Type,
})

const summaryTab = {
  title: '総括',
  id: '-1',
}

// ローカルOneway
const localOneway = reactive({
  // 事業所選択
  orT0001Oneway: {
    officeDataList: [],
    termid: 0,
  } as OrT0001OneWayType,
  // アセスメント複写
  or61746Oneway: {
    periodManagementFlg: '',
  } as Or61746OnewayType,
  // 計画管理
  planningPeriodSelectOneway: {
    plainningPeriodManageFlg: '',
    pageBtnAutoDisabled: false,
    labelAutoDisabled: false,
    showLabelMode: false,
  } as Or13844OnewayType,
  orX0115Oneway: {
    kindId: systemCommonsStore.getSyubetu,
    sc1Id: '',
  } as OrX0115OnewayType,
  // 歴史
  hisotrySelectOneway: {
    /** 履歴情報 */
    historyInfo: {
      /** アセスメントID */
      rirekiId: '',
      /** 履歴番号 */
      krirekiNo: '',
      /** 履歴総件数 */
      krirekiCnt: '',
    },
    showLabelMode: false,
    pageBtnAutoDisabled: true,
  } as Or13872OnewayType,
  // 作成者選択
  authorSelectOneway: {
    itemTitle: t('label.author'),
    /** アイコンボタン活性化フラグ */
    iconBtnDisabled: false,
    id: '',
    name: '',
    showLabelMode: false,
  } as Or13850OnewayType,
  orT0003Oneway: {
    createDataList: [],
    termid: 0,
  } as OrT0003OneWayType,
  // 作成日
  createDateOneway: {
    itemLabel: t('label.create-date'),
    isRequired: true,
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass d-flex flex-row',
    }),
  } as Mo00020OnewayType,
  // 初回作成日
  initCreateDateOneway: {
    itemLabel: t('label.shokai_ymd'),
    isRequired: true,
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass d-flex flex-row',
    }),
  } as Mo00020OnewayType,
  mo00045SeqOnewayType: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: t('label.caseNo'),
    class: 'background-color  ',
    customClass: new CustomClass({
      labelClass: 'mt-2 pb-1',
      outerClass: 'background-transparent',
    }),
    maxLength: '10',
  } as Mo00045OnewayType,
  or27562Oneway: {
    assessmentMaster: {} as {
      // 事業者ID
      jigyoId: string
      // 利用者ID
      userId: string
      // まとめフラグ
      summaryFlag: string
    },
  } as unknown as Or27562OnewayType,
  // 領域エリアTAB
  fieldTabsMo00043OnewayType: {
    tabItems: [],
  } as Mo00043OnewayType,
  // 分類エリアTAB
  classificationTabs: [] as ClassificationTabsListType[],
  // 解決すべき課題と目標セクション
  summaryTabOnewayType: {
    tabItems: [
      {
        id: '1',
        title: t('label.classifica-summary', [
          String.fromCharCode(Number(local.classificationMo00043.id) + 96),
        ]),
      },
      {
        id: '2',
        title: t('label.region-summary', [String.fromCharCode(Number(local.filedMo00043.id) + 64)]),
      },
      { id: '3', title: 'アセスメントの総括' },
    ],
  } as Mo00043OnewayType,
  cpnTucSypAss2Info: {
    id: '',
    assId: '',
    mstId: '',
    chk1Flg: '1',
    chk2Flg: '1',
    chk3Flg: '0',
    chk4Flg: '0',
    chk5Flg: '0',
    chk6Flg: '0',
    chk7Flg: '0',
    chk8Flg: '0',
    chk9Flg: '0',
    chk10Flg: '0',
    memo1Knj: '123',
    memo2Knj: '13',
    memo3Knj: '123',
    memo4Knj: '13',
    hyokaKbn: '0',
    ikouKbn: '0',
    yusenKbn: '-1',
    ikouKnj: '',
    userId: '',
    ryoikiKbn: '',
    seqNo4: '',
    seqNo1: '',
    seqNo3: '',
    memoKnj: '',
    memo5Knj: '',
    bunruiKbn: '',
    koumokuKbn: '',
    seqNo2: '',
    kaiteiFlg: '',
    endFlg: '',
    modifiedCntCpnTucSypAss2: '',
  } as CpnTucSypAss2InfoListType,
  mo00009IconBtnOneway: {
    btnIcon: 'edit_square',
    name: 'button',
    density: 'compact',
  } as Mo00009OnewayType,
  btnTooltip: t('tooltip.assessment-home-6-1-servey-ledger-import'),
  or61587Oneway: {
    //アセスメント総括（課題・目標）情報リスト
    cpnTucSypAss4InfoList: [] as CpnTucSypAss4InfoListType[],
    // アセスメント総括情報リスト
    cpnTucSypAss3InfoList: [] as CpnTucSypAss3InfoListType[],
  },
})

/**************************************************
 * 算出プロパティ
 **************************************************/

// 分類情報リスト
const classificationTabComputed = computed(() => {
  return localOneway.classificationTabs.filter((item) => item.fieldId === local.filedMo00043.id)
})

// 計画対象期間ダイアログ表示フラグ
const showDialogOrX0115 = computed(() => {
  // OrX0115 のダイアログ開閉状態
  return OrX0115Logic.state.get(orX0115.value.uniqueCpId)?.isOpen ?? false
})

// 職員検索ダイアログ表示フラグ
const showDialogOrT0003 = computed(() => {
  // Or28991のダイアログ開閉状態
  return OrT0003Logic.state.get(orT0003.value.uniqueCpId)?.isOpen ?? false
})

const summaryTabOnewayTypeComputed = computed(() => {
  const temData = localOneway.summaryTabOnewayType
  // タイトルの設定
  temData.tabItems[0].title = t('label.classifica-summary', [
    String.fromCharCode(Number(local.classificationMo00043.id) + 96),
  ])
  temData.tabItems[1].title = t('label.region-summary', [
    String.fromCharCode(Number(local.filedMo00043.id) + 64),
  ])
  return temData
})

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => useScreenStore().isEditNavControl())

/**************************************************
 * ライフサイクルフック
 **************************************************/

systemCommonsStore.setUserSelectZenkaiSvJigyoIdList([''])
systemCommonsStore.setUserSelectSelfId('')

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackUserChange)
// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, () => callbackFuncJigyo)

onMounted(async () => {
  // ブラウザの「戻る」「進む」ボタンからの遷移時に以前の値を復元したい画面の場合は
  // 画面情報領域のsupplement内のisInitで処理を分岐させて初回表示時のみ画面データの取得を行なう
  // ※値の復元が不要な画面では分岐は不要
  if (isInit) {
    // コントロール設定
    initContorls()
  }
  await getInitCommonInfo()
  await getDataInfo()
  onJump()
})
/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(0)]: or00248.value,
  [Or11871Const.CP_ID]: or11871.value,
  [OrT0001Const.CP_ID(0)]: orT0001.value,
  [OrT0003Const.CP_ID(0)]: orT0003.value,
  [Or13872Const.CP_ID(0)]: or13872.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
  [Or27562Const.CP_ID(0)]: or27562.value,
  [Or61746Const.CP_ID(0)]: or61746.value,
  [Or13850Const.CP_ID(1)]: or13850.value,
  [Or41179Const.CP_ID(0)]: or41179.value,
  [Or13844Const.CP_ID(1)]: or13844.value,
  [OrX0115Const.CP_ID(1)]: orX0115.value,
  [Or61587Const.CP_ID(1)]: or61587_1.value,
  [Or61587Const.CP_ID(2)]: or61587_2.value,
  [Or61588Const.CP_ID(1)]: or61588_1.value,
  [Or61588Const.CP_ID(2)]: or61588_2.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [Or00249Const.CP_ID(0)]: or00249.value,
})

// 子コンポーネントに対して初期設定を行う
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('アセスメント(パッケージプラン)'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: true,
    showCreateBtn: true,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: true,
    showOptionMenuBtn: true,
    showOptionMenuDelete: true,
  },
})

/**************************************************
 * 関数
 **************************************************/

/**
 * 新規処理
 *
 */
async function createNew() {}

/**
 * 削除処理
 *
 */
function _delete() {
  // メッセージを表示 i.cmn.11348
  // let tabName = ''
  // // メッセージ 11348
  // // 現在表示している[{0}］の{1}データを削除します。よろしいですか？\r\n現在表示している画面のみ削除する。\r\n※{2}画面を削除します。\r\n表示している画面を履歴ごと削除する。\r\n※{3}までの全ての項目を削除します。
  // const msg11348 = t('message.i-cmn-11348', [
  //   local.createDate.value,
  //   t('label.assessment'),
  //   tabName,
  //   '1~7' + t('label.schedule-k'),
  // ])
}

/**
 *  初期情報取得【共通処理】
 */
async function getInitCommonInfo() {
  const inputData: AssessmentPackageplanInitCommonSelectInEntity = {
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    userid: userId.value,
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    // kikanFlg: plannningPeriodManageFlg.value,
  }

  const resData: AssessmentPackageplanInitCommonSelectOutEntity = await ScreenRepository.select(
    'assessmentPackageplanInitCommonSelect',
    inputData
  )
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    const { planPeriodInfo, historyInfo, kikanFlg } = resData.data
    // ------------計画対象期間を設定------------
    // 計画対象期間管理フラグが「1:管理する」場合、計画対象期間情報を設定
    plannningPeriodManageFlg.value = kikanFlg
    if (kikanFlg === TeX0009Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
      if (planPeriodInfo?.sc1Id) {
        localOneway.planningPeriodSelectOneway.plainningPeriodManageFlg = kikanFlg
        localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
          id: planPeriodInfo.sc1Id,
          period: planPeriodInfo.startYmd + SPACE_WAVE + planPeriodInfo.endYmd,
          periodNo: planPeriodInfo.periodNo ?? '0',
          periodCnt: planPeriodInfo.periodCnt ?? '0',
        }
        isHistoryShow.value = true
      } else {
        localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
          id: '',
          period: '',
          periodNo: '0',
          periodCnt: '0',
        }
        isHistoryShow.value = false
      }
    } else {
      localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
        id: '0',
        period: '',
        periodNo: '',
        periodCnt: '',
      }
      isHistoryShow.value = true
    }

    /** ------------履歴情報を設定------------ */

    if (historyInfo?.assId) {
      localOneway.hisotrySelectOneway.historyInfo = {
        /** アセスメントID */
        rirekiId: historyInfo.assId,
        /** 履歴番号 */
        krirekiNo: historyInfo.krirekiNo ?? '1',
        /** 履歴総件数 */
        krirekiCnt: historyInfo.krirekiCnt ?? '1',
      }
      // 履歴更新回数
      historyModifiedCnt.value = historyInfo.modifiedCnt
      // 作成日設定
      local.createDate.value = historyInfo.createYmd ?? ''
      createDateOld.value = local.createDate.value
      // 作成者設定
      localOneway.authorSelectOneway.id = historyInfo.shokuId
      localOneway.authorSelectOneway.name = historyInfo.shokuinName

      // 初回作成日
      local.initCreateDate.value = historyInfo.shokaiYmd ?? ''
      // ケース番号設定
      local.caseSeq = { value: historyInfo.caseNo ?? '' }

      // 共通情報設定
      setCommonInfo({
        sc1Id: planPeriodInfo.sc1Id,
        createYmd: historyInfo.createYmd,
        shokuId: historyInfo.shokuId,
        assId: historyInfo.assId,
      })
    }
  }
}

/**
 *  画面初期情報取得
 */
async function getDataInfo() {
  // 初期共通情報
  //画面初期情報
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentPackageplanInitSelectInEntity = {
    // アセスメントカスタマイズID
    cstId: '1',
    // アセスメント履歴ＩＤ
    assId: '2',
  }
  const resData: AssessmentPackageplanInitSelectOutEntity = await ScreenRepository.select(
    'assessmentPackageplanInitSelect',
    inputData
  )
  const {
    ryoikiMasterList,
    ryoikiBunruiList,
    ryoikiMediumBunruiList,
    cpnTucSypAssMatomeInfoList,
    cpnTucSypAss2InfoList,
    cpnTucSypAss3InfoList,
    cpnTucSypAss4InfoList,
  } = resData.data

  if (!ryoikiMasterList?.length) {
    return
  }
  // 領域エリアTAB取得処理
  localOneway.fieldTabsMo00043OnewayType.tabItems = [
    ...ryoikiMasterList.map((item) => {
      return {
        id: item.ryoikiKbn,
        title: item.ryoikiRyakuKnj,
      }
    }),
    summaryTab,
  ]
  // デフォルトで最初のIDを取得
  local.filedMo00043.id = ryoikiMasterList?.[0]?.ryoikiKbn
  // 分類エリアTAB取得処理
  localOneway.classificationTabs = ryoikiBunruiList?.map((item, index) => {
    // 動的にサブコンポーネントのユニークID（uniqueCpId）を生成する
    orX0106Components[orX0106Str + index] = { uniqueCpId: OrX0106Const.CP_ID(index) }
    const temData = ryoikiMediumBunruiList.filter(
      (subItem) =>
        String(subItem.ryoikiKbn) === item.ryoikiKbn && String(subItem.bunruiKbn) === item.bunruiKbn
    )
    const formValuesTem: CpnTucSypAss2InfoListType[] = temData.map((item) => {
      return {
        ...localOneway.cpnTucSypAss2Info,
        assId: item.assId as string,
        mstId: item.mstId,
        ryoikiKbn: item.ryoikiKbn,
        bunruiKbn: item.bunruiKbn,
        koumokuKbn: item.koumokuKbn,
        ass1SeqNo: item.ass1SeqNo,
        ass2SeqNo: item.ass2SeqNo,
        ass3SeqNp: item.ass3SeqNo,
        ass4SeqNp: item.ass4SeqNo,
        endFlg: item.endFlg,
      }
    })
    return {
      id: item.bunruiKbn,
      title: item.bunruiRyakuKnj,
      fieldId: item.ryoikiKbn,
      // 対応する中分類のリスト処理

      projectsList: groupByEndFlg(
        processArray(
          temData,
          formValuesTem.map((item) => {
            return (
              cpnTucSypAss2InfoList.find(
                (el) =>
                  el.ryoikiKbn === item.ryoikiKbn &&
                  el.bunruiKbn === item.bunruiKbn &&
                  item.mstId === el.mstId
              ) ?? item
            )
          })
        )
      ),
      // 対応する中分類フォーム値処理です
    }
  })
  // 初期表示でA領域の最初の分類タブ
  local.classificationMo00043.id =
    ryoikiBunruiList.find(
      (item) => item.ryoikiKbn === local.filedMo00043.id && item.ass2SeqNo === '1'
    )?.bunruiKbn ?? ''

  //  アセスメントまとめ情報リスト
  const temData = cpnTucSypAssMatomeInfoList.map((item) => {
    return {
      ...item,
      titile: '',
      matomeKnj: { value: item.matomeKnj },
      mitoshiKnj: { value: item.mitoshiKnj },
    }
  })
  setChildCpBinds(props.uniqueCpId, {
    [Or61588Const.CP_ID(1)]: {
      twoWayValue: {
        or61588ModelValue: temData
          .filter((item) => item.bunruiKbn !== '0')
          .map((el) => {
            return {
              ...el,
              title:
                localOneway.classificationTabs.find((subItem) => {
                  return el.bunruiKbn === subItem.id
                })?.title ?? '',
            }
          }),
      },
    },
    [Or61588Const.CP_ID(2)]: {
      twoWayValue: {
        or61588ModelValue: temData
          .filter((item) => item.bunruiKbn === '0')
          .map((el) => {
            return {
              ...el,
              title:
                localOneway.fieldTabsMo00043OnewayType.tabItems.find((subItem) => {
                  return el.ryoikiKbn === subItem.id
                })?.title ?? '',
            }
          }),
      },
    },
  })

  // localOneway.or61588Oneway.cpnTucSypAssMatomeInfoList = cpnTucSypAssMatomeInfoList
  // アセスメント総括情報リスト
  localOneway.or61587Oneway.cpnTucSypAss3InfoList = cpnTucSypAss3InfoList
  //アセスメント総括（課題・目標）情報リスト
  localOneway.or61587Oneway.cpnTucSypAss4InfoList = cpnTucSypAss4InfoList
}

/**
 *  カスタムフォーム配列の処理
 *
 * @param array - ページ区分
 *
 * @param formValues -formValues
 */
function processArray(
  array: RyoikiMediumBunruiListType[],
  formValues: CpnTucSypAss2InfoListType[]
) {
  const result: RyoikiMediumBunruiListType[] = []
  const map = new Map<string, RyoikiMediumBunruiListType[]>()

  // まず、配列を走査してマッピングテーブルを作成する
  array.forEach((item, index) => {
    const key = item.koumokuKbn
    const customComponents: CustomComponentsType[] = []
    let memoCount = 1
    let checkCount = 1
    const formItemValue = formValues[index]

    for (let i = 1; i <= 10; i++) {
      const chkKbn = item[`chk${i}Kbn`] as string
      const chkKnj = item[`chk${i}Knj`] as string
      if (Number(chkKbn) === 0) continue
      let name = ''
      let value: Mo00018Type | Mo00045Type = { value: '' }

      switch (Number(chkKbn)) {
        case 1:
          name = ''
          break
        case 2:
          name = `chk${checkCount}Flg`
          checkCount++
          value = { modelValue: formItemValue[name] === '0' ? false : true }
          break
        case 3:
          name = `memo${memoCount}Knj`
          memoCount++
          value = { value: formItemValue[name] }
          break
      }
      customComponents.push({
        type: chkKbn,
        label: chkKnj,
        name,
        value,
      })
    }

    if (!map.has(key)) {
      map.set(key, [])
    }
    map.get(key)?.push({ ...item, customComponents })
  })

  // 再度マッピングテーブルを走査して結果の
  map.forEach((group) => {
    const parent = group.find((item: RyoikiMediumBunruiListType) => !Number(item.endFlg4))
    if (parent) {
      parent.children = [parent, ...group.filter((item) => Number(item.endFlg4))]
      result.push(parent)
    } else {
      const firstItem = group[0]
      if (firstItem) {
        firstItem.children = [firstItem, ...group.slice(1)]
        result.push(firstItem)
      }
    }
  })

  return result
}

function groupByEndFlg(items: RyoikiMediumBunruiListType[]) {
  const result: OrX0106ComponentsListType[] = []
  let tempGroup: RyoikiMediumBunruiListType[] = []
  for (const item of items) {
    if (Number(item.endFlg) === 0) {
      // まず親項目でないものを一時的に保存する
      tempGroup.push(item)
    } else if (Number(item.endFlg) === 1) {
      // 親項目に出会った
      if (!tempGroup.length) {
        // 以前の子項目がなく、自己が親項目であり、子項目がない
        result.push({
          mainTitle: item.koumokuKnj,
          content: [item],
          rowHeightSpeed: item?.children?.length ?? 0 + 1,
        })
      } else {
        // 以前の子項目があり、itemは親項目で、子項目はtempGroupです
        const temArray = [item, ...tempGroup]
        const count = temArray.reduce((sum, obj) => {
          return sum + (obj?.children?.length ?? 0) + 1
        }, 0)
        result.push({
          mainTitle: item.koumokuKnj,
          rowHeightSpeed: count,
          content: temArray,
        })
        // 一時的なグループをクリアして、次のグループ分けを開始する
        tempGroup = []
      }
    }
  }

  return result
}

/**
 * Or13844イベント発火
 *
 * @param event - イベント
 */
function setOr11871Event(event: Record<string, boolean>) {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId !== '') {
    const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
    const jigyoInfo = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)
    if (jigyoInfo) {
      if (commonInfo.value.svJigyoId !== jigyoInfo.svJigyoId) {
        await officeChange(jigyoInfo)
      }
    }
  }
}

/**
 * 事業所変更処理
 *
 * @param jigyoInfo - 事業所詳細
 */
async function officeChange(jigyoInfo: {
  houjinId: string
  shisetuId: string
  svJigyoId: string
  svJigyoCd: string
  jigyoRyakuKnj: string
}) {
  // データ変更チェック
  if (isEdit.value) {
    // 変更あるの場合
    const result = await getOr21814_2DialogResult(t('message.i-cmn-10430'))

    if (result === 'no') {
      Or41179Logic.data.set({
        uniqueCpId: or41179.value.uniqueCpId,
        value: {
          modelValue: temporarySaveOfficeModelvalue.value,
        } as Mo00040Type,
      })
    } else if (result === 'yes') {
      setCommonInfo({
        houjinId: jigyoInfo?.houjinId,
        shisetuId: jigyoInfo?.shisetuId,
        svJigyoId: jigyoInfo?.svJigyoId,
      })
      // // 保存処理を行う
      // // await userSave()
      // // 画面情報再取得
      // // 期間ID：0
      // localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriodId = 0
      // // ヘーダID ＝ 0
      // localOneway.hisotrySelectOneway.screenID = '0'
      // // AC001を実行する。
      // // await reload()
    }
  } else {
    setCommonInfo({
      houjinId: jigyoInfo?.houjinId,
      shisetuId: jigyoInfo?.shisetuId,
      svJigyoId: jigyoInfo?.svJigyoId,
    })

    // // 期間ID：0
    // localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriodId = 0
    // // ヘーダID ＝ 0
    // localOneway.hisotrySelectOneway.screenID = '0'
  }
}

/**
 * Or21814_2ダイアログを呼び出す、返却値を取得する
 *
 * @param dialogText -ダイアログ内容
 */
function getOr21814_2DialogResult(dialogText: string): Promise<'yes' | 'no' | 'cancel' | ''> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: dialogText,
    },
  })
  // 閉じるタイミングで監視を実行
  return new Promise((resolve) => {
    let result: 'yes' | 'no' | 'cancel' | '' = ''
    watch(
      () => Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen,
      (newValue) => {
        if (!newValue) {
          const event = Or21814Logic.event.get(or21814_2.value.uniqueCpId)
          if (event?.firstBtnClickFlg) {
            result = 'yes'
          }
          if (event?.secondBtnClickFlg) {
            result = 'no'
          }
          if (event?.thirdBtnClickFlg) {
            result = 'cancel'
          }
          if (event?.closeBtnClickFlg) {
            result = 'cancel'
          }
          Or21814Logic.event.set({
            uniqueCpId: or21814_2.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              closeBtnClickFlg: false,
            },
          })
          resolve(result)
        }
      },
      { once: true }
    )
  })
}

/**
 *  データ変更チェック
 */
// function checkChange(): boolean {
//   return true
// }

/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 変更後の利用者番号
 */
function callbackUserChange(newSelfId: string) {
  if (newSelfId !== '') {
    userId.value = newSelfId
  }
}

/**
 *  コントロール初期化
 */
const initContorls = () => {
  local.createDate.value =
    systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
  createDateBk.value = local.createDate.value

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })

  if (systemCommonsStore.getUserSelectSelfId) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
    // 画面情報設定
    setCommonInfo({ userId: systemCommonsStore.getUserSelectSelfId() })
  }

  TeX0009Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      /**
       * 事業所ID
       */
      jigyoId: systemCommonsStore.getSvJigyoId,
      /**
       * 計画対象期間ID
       */
      sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id,
      /**
       * アセスメントID
       */
      gdlId: localOneway.hisotrySelectOneway.historyInfo?.rirekiId ?? '',
      /**
       * 作成者ID
       */
      createUserId: localOneway.authorSelectOneway.id,
      /**
       * 作成日
       */
      createYmd: local.createDate.value,
    },
  })
}

/**
 * 共通情報設定
 *
 * @param info - 共通情報
 */
function setCommonInfo(info: CommonInfoType) {
  const exitData = { ...commonInfo.value }
  commonInfo.value = {
    sc1Id: info.sc1Id ?? exitData.sc1Id,
    houjinId: info.houjinId ?? exitData.houjinId,
    shisetuId: info.shisetuId ?? exitData.shisetuId,
    svJigyoId: info.svJigyoId ?? exitData.svJigyoId,
    userId: info.userId ?? exitData.userId,
    createYmd: info.createYmd ?? exitData.createYmd,
    shokuId: info.shokuId ?? exitData.shokuId,
    cstId: info.cstId ?? exitData.cstId,
    assId: info.assId ?? exitData.assId,
  }
}

/**
 * 複写ボタンクリック
 *
 */
function copyBtnClick() {
  // 複写画面パラメータを設定
  copyComponentKey.value = Date.now().toString()

  localOneway.or61746Oneway = {
    /** 期間管理フラグ */
    periodManagementFlg: '1', //  plannningPeriodManageFlg.value
    /** 領域区分タブリスト */
    fieldTabs: localOneway.fieldTabsMo00043OnewayType.tabItems.filter(
      (el) => el.id === local.filedMo00043.id
    ),
    /** 分類タブリスト */
    classificationTabs: localOneway.classificationTabs.filter(
      (item) => item.fieldId === local.filedMo00043.id
    ),
    /** 選択中タブ */
    activeTabId: local.classificationMo00043.id,
    /** 事業所ID */
    jigyoId: commonInfo.value.svJigyoId,
    /** 計画対象期間ID */
    sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id,
    /** アセスメントID */
    gdlId: commonInfo.value.assId,
    /** 作成者ID */
    createUserId: commonInfo.value.shokuId,
    /** 作成日 */
    createYmd: local.createDate.value,
  }
  // 複写画面を開く
  Or61746Logic.state.set({
    uniqueCpId: or61746.value.uniqueCpId,
    state: { isOpen: true },
  })
  setOr11871Event({ copyEventFlg: false })
}

/**
 * 計画対象期間選択開くボタンクリック
 *
 */
async function planningPeriodSelectClick() {
  //画面入力データに変更がある場合
  if (isEdit.value) {
    const result = await getOr21814_2DialogResult(t('message.i-cmn-10430'))
    switch (result) {
      //はい：AC003(保存処理)を実行し、処理続き
      case DIALOG_BTN.YES:
        // onSave('')
        return
      //いいえ：処理続き
      case DIALOG_BTN.NO:
        break
      //キャンセル：処理終了
      default:
        break
    }
  }
  // 計画対象期間選択画面を開く
  OrX0115Logic.state.set({
    uniqueCpId: orX0115.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 計画期間変更情報取得
 *
 * @param pageFlg - ページ区分
 */
async function getChangePlanPeroidInfo(pageFlg: string) {
  const inputData: AssessmentPackageplanPeriodChangeSelectInEntity = {
    /**
     * 事業者ID
     */
    svJigyoId: commonInfo.value.svJigyoId ?? '',
    /**
     * 利用者ID
     */
    userid: userId.value,
    /**
     * 種別ID
     */
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    /**
     * 施設ID
     */
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    /**
     * 期間ID
     */
    sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo!.id,
    /**
     * 期間処理区分
     */
    pageFlag: pageFlg,
  }
  const resData: AssessmentPackageplanPeriodChangeSelectOutEntity = await ScreenRepository.select(
    'assessmentPackageplanPeriodChangeSelect',
    inputData
  )
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    const { planPeriodInfo, historyInfo, kikanFlg } = resData.data
    // ------------計画対象期間を設定------------
    // 計画対象期間管理フラグが「1:管理する」場合、計画対象期間情報を設定
    if (plannningPeriodManageFlg.value === TeX0009Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
      if (planPeriodInfo?.sc1Id) {
        localOneway.planningPeriodSelectOneway.plainningPeriodManageFlg = kikanFlg
        localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
          id: planPeriodInfo.sc1Id,
          period: planPeriodInfo.startYmd + SPACE_WAVE + planPeriodInfo.endYmd,
          periodNo: planPeriodInfo.periodNo ?? '0',
          periodCnt: planPeriodInfo.periodCnt ?? '0',
        }
        isHistoryShow.value = true
      } else {
        localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
          id: '',
          period: '',
          periodNo: '1',
          periodCnt: '1',
        }
      }
    }

    if (historyInfo?.assId) {
      // 歴史情報設定
      localOneway.hisotrySelectOneway.historyInfo = {
        /** アセスメントID */
        rirekiId: resData.data.historyInfo.assId,
        /** 履歴番号 */
        krirekiNo: resData.data.historyInfo.krirekiNo ?? '1',
        /** 履歴総件数 */
        krirekiCnt: resData.data.historyInfo.krirekiCnt ?? '1',
      }
      // 履歴更新回数
      historyModifiedCnt.value = resData.data.historyInfo.modifiedCnt

      // 作成日設定
      local.createDate.value = historyInfo.createYmd
      createDateOld.value = local.createDate.value
      // 作成者設定
      localOneway.authorSelectOneway.id = historyInfo.shokuId
      localOneway.authorSelectOneway.name = historyInfo.shokuinName

      // 初回作成日
      local.initCreateDate.value = historyInfo.shokaiYmd ?? ''
      // ケース番号設定
      local.caseSeq = { value: historyInfo.caseNo ?? '' }
    }

    // 共通情報設定
    setCommonInfo({
      sc1Id: planPeriodInfo.sc1Id,
      createYmd: historyInfo.createYmd,
      shokuId: historyInfo.shokuId,
    })
  }
}

/**
 * 歴史変更情報取得
 *
 * @param pageFlg - ページ区分
 */
async function getChangeHistoryInfo(pageFlg: string) {
  const inputData: AssessmentPackageplanHistoryChangeSelectInEntity = {
    /**
     * 事業者ID
     */
    svJigyoId: commonInfo.value.svJigyoId ?? '',
    /**
     * 利用者ID
     */
    userid: userId.value,
    /**
     * 期間ID
     */
    sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id ?? '',
    /**
     * アセスメント履歴ID
     */
    assId: commonInfo.value.assId ?? '',
    /**
     * 履歴変更区分
     */
    kikanFlag: pageFlg,
  }
  const resData: AssessmentPackageplanHistoryChangeSelectOutEntity = await ScreenRepository.select(
    'assessmentPackageplanHistoryChangeSelect',
    inputData
  )
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    // 歴史情報設定
    const { historyInfo } = resData.data
    localOneway.hisotrySelectOneway.historyInfo = {
      /** アセスメントID */
      rirekiId: historyInfo.assId,
      /** 履歴番号 */
      krirekiNo: historyInfo.krirekiNo ?? '1',
      /** 履歴総件数 */
      krirekiCnt: historyInfo.krirekiCnt ?? '1',
    }

    // 履歴更新回数
    historyModifiedCnt.value = historyInfo.modifiedCnt
    // 作成日設定
    local.createDate.value = historyInfo.createYmd ?? ''
    createDateOld.value = local.createDate.value
    // 作成者設定
    localOneway.authorSelectOneway.id = historyInfo.shokuId
    localOneway.authorSelectOneway.name = historyInfo.shokuinName
    // 共通情報設定
    setCommonInfo({
      sc1Id: historyInfo.sc1Id,
      assId: historyInfo.assId,
      createYmd: historyInfo.createYmd,
      shokuId: historyInfo.shokuId,
    })
  }
}

/**
 * 履歴選択開くボタンクリック
 *
 */
async function historySelectClick() {
  // // 履歴選択画面パラメータを作成
  // localOneway.or28326Oneway = {
  //   /**事業者ID*/
  //   svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
  //   /**計画期間ID*/
  //   sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id ?? '',
  //   /**利用者ID*/
  //   userId: userId.value,
  //   /**履歴ID*/
  //   ks21Id: localOneway.hisotrySelectOneway.historyInfo?.gdlId ?? '',
  // }
  // // 画面変更チェック
  // switch (await checkChange()) {
  //   case 0: {
  //     // 画面入力変更なし
  //     // 履歴選択画面を開く
  //     Or28326Logic.state.set({
  //       uniqueCpId: or28326.value.uniqueCpId,
  //       state: { isOpen: true },
  //     })
  //     break
  //   }
  //   case 1: {
  //     // 画面入力変更あり、編集を破棄して処理を続行
  //     // 履歴選択画面を開く
  //     Or28326Logic.state.set({
  //       uniqueCpId: or28326.value.uniqueCpId,
  //       state: { isOpen: true },
  //     })
  //     break
  //   }
  //   case 2: {
  //     // 画面入力変更あり、編集を保存
  //     // 履歴選択画面を開く
  //     Or28326Logic.state.set({
  //       uniqueCpId: or28326.value.uniqueCpId,
  //       state: { isOpen: true },
  //     })
  //     // 保存
  //     setEvent({ saveOnlyEventFlg: true })
  //     break
  //   }
  //   case 3: {
  //     // キャンセル、何もしない
  //     break
  //   }
  // }
}

/**
 * 作成者選択画面クリック
 *
 */
function authorSelectClick() {
  // 選択画面を開く
  OrT0003Logic.state.set({
    uniqueCpId: orT0003.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 作成日変更処理
 *
 * @param mo00020 - 作成日
 */
async function createDateChange(mo00020: Mo00020Type) {
  //
}

/**
 * ダイアログを呼び出す
 *
 * @param dialogText - ダイアログ内容
 */
function setShowDialog(dialogText: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: dialogText,
    },
  })
}

/**
 * Or13844イベント発火
 *
 * @param event - イベント
 */
function setOr13844Event(event: Record<string, boolean>) {
  Or13844Logic.event.set({
    uniqueCpId: or13844.value.uniqueCpId,
    events: event,
  })
}

/**
 * Or13872イベント発火
 *
 * @param event - イベント
 */
function setOr13872Event(event: Record<string, boolean>) {
  Or13844Logic.event.set({
    uniqueCpId: or13872.value.uniqueCpId,
    events: event,
  })
}

const summyTabWindow = ref(null)

function onJump() {
  if (summyTabWindow.value) {
    ;(summyTabWindow.value as HTMLElement).scrollIntoView({ behavior: 'smooth' })
  }
}

/**************************************************
 * ウォッチャー
 **************************************************/

/**
 * 計画対象期間履歴チェンジワッチャー
 */
watch(
  () => Or13844Logic.event.get(or13844.value.uniqueCpId),
  async (newValue) => {
    if (!newValue?.preBtnClickFlg && !newValue?.nextBtnClickFlg) return
    setOr13844Event({ preBtnClickFlg: false, nextBtnClickFlg: false })
    //画面入力データに変更がある場合
    if (isEdit.value) {
      const result = await getOr21814_2DialogResult(t('message.i-cmn-10430'))
      switch (result) {
        //はい：AC003(保存処理)を実行し、処理続き
        case DIALOG_BTN.YES:
          return
        //いいえ：処理続き
        case DIALOG_BTN.NO:
          break
        default:
          break
      }
    }
    // 「計画対象期間-前へアイコンボタン」押下
    if (newValue?.preBtnClickFlg) {
      // 1件目の期間データが表示されている状態
      if (localOneway.planningPeriodSelectOneway.planningPeriodInfo?.periodNo === '1') {
        setShowDialog(t('message.i-cmn-11262'))
        return
      }
      // 計画期間変更情報取得
      await getChangePlanPeroidInfo(TeX0009Const.DEFAULT.PERIOD_KBN_PREV)
    }
    // 「計画対象期間-次へアイコンボタン」押下
    if (newValue?.nextBtnClickFlg) {
      // 最終件目の期間データが表示されている状態
      if (
        localOneway.planningPeriodSelectOneway.planningPeriodInfo?.periodNo ===
        localOneway.planningPeriodSelectOneway.planningPeriodInfo?.periodCnt
      ) {
        setShowDialog(t('message.i-cmn-11263'))
        return
      }
      // 計画期間変更情報取得
      await getChangePlanPeroidInfo(TeX0009Const.DEFAULT.PERIOD_KBN_NEXT)
    }
  },
  { deep: true }
)

/**
 * 履歴チェンジワッチャー
 */
watch(
  () => Or13872Logic.event.get(or13872.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.preBtnClickFlg === false && newValue.nextBtnClickFlg === false) {
      return
    }

    setOr13872Event({ preBtnClickFlg: false, nextBtnClickFlg: false })

    // データ変更チェック
    if (isEdit.value) {
      const result = await getOr21814_2DialogResult(t('message.i-cmn-10430'))
      if (result === DIALOG_BTN.YES) {
        // await onSave()
      }
      if (result === DIALOG_BTN.NO) {
        //
      }
    }
    // 「履歴-前へアイコンボタン」押下
    if (newValue?.preBtnClickFlg) {
      //1件目の履歴データが表示されている状態
      if (localOneway.hisotrySelectOneway.historyInfo?.krirekiNo === '1') {
        //処理終了にする。
        return
      }
      await getChangeHistoryInfo(TeX0009Const.DEFAULT.HISTORY_KBN_PREV)
    }
    // 「履歴-次へアイコンボタン」押下
    if (newValue?.preBtnClickFlg) {
      // 最終件目の履歴データが表示されている状態
      if (
        localOneway.hisotrySelectOneway.historyInfo?.krirekiNo ===
        localOneway.hisotrySelectOneway.historyInfo?.krirekiCnt
      ) {
        //処理終了にする。
        return
      }
      // 履歴変更情報取得
      await getChangeHistoryInfo(TeX0009Const.DEFAULT.HISTORY_KBN_NEXT)
    }
  },
  { deep: true }
)

watch(
  () => Or41179Logic.data.get(or41179.value.uniqueCpId)?.modelValue,
  (_, oldVal) => {
    // oldValue`を一時変数に保存できます
    temporarySaveOfficeModelvalue.value = oldVal
  }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (!newValue) return

    // 新しいデータの値をチェックし、全falseの場合は処理を中断する
    const isAllFalseFlg = Object.values(newValue).every((item) => !item)
    if (isAllFalseFlg) return

    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する

      setOr11871Event({ favoriteEventFlg: false })
      return
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      setOr11871Event({ saveEventFlg: false })

      return
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      setOr11871Event({ createEventFlg: false })

      screenSaveKbn.value = TeX0009Const.DEFAULT.UPDATE_KBN_C
      await createNew()
      return
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
      setOr11871Event({ printEventFlg: false })
      return
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する

      setOr11871Event({ masterEventFlg: false })
      return
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
      screenSaveKbn.value = TeX0009Const.DEFAULT.UPDATE_KBN_D
      _delete()
      setOr11871Event({ deleteEventFlg: false })
      return
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      setOr11871Event({ copyEventFlg: false })
      return
    }
  }
)

/**
 * タブ変更監視
 */
watch(
  () => local.filedMo00043.id,
  (newValue) => {
    if (!newValue) return
    // 分類IDを1に設定する
    local.classificationMo00043.id = '1'
  }
)
</script>

<template>
  <c-v-sheet class="view d-flex flex-column h-100">
    <!-- 操作ボタンエリア -->
    <div class="action-sticky">
      <g-base-or11871 v-bind="or11871">
        <template #createItems>
          <c-v-list-item
            :title="t('btn.copy')"
            prepend-icon="file_copy"
            @click="copyBtnClick"
          />
        </template>
      </g-base-or11871>
    </div>
    <c-v-row
      no-gutters
      class="main-Content d-flex flex-0-1 h-100 overflow-y-auto"
    >
      <!-- ナビゲーションエリア -->
      <c-v-col
        cols="auto"
        class="hidden-scroll h-100"
      >
        <!-- 利用者選択一覧 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>
      <!-- コンテンツエリア -->
      <c-v-col class="main-right d-flex flex-column overflow-hidden h-100">
        <!-- 上段 -->
        <c-v-row
          no-gutters
          class="d-flex align-center mb-2"
        >
          <c-v-col cols="auto mr-2">
            <!-- 事業所 -->
            <g-base-or41179
              class="custom_Or41179"
              v-bind="or41179"
            />
          </c-v-col>
          <!-- 計画管理 -->
          <c-v-col
            v-if="plannningPeriodManageFlg === '1'"
            cols="auto mr-2"
          >
            <!-- 計画対象期間 -->
            <g-custom-or13844
              v-bind="or13844"
              :oneway-model-value="localOneway.planningPeriodSelectOneway"
              @open-btn-click="planningPeriodSelectClick"
            />
          </c-v-col>
          <!-- 歴史 -->
          <c-v-col
            v-show="isHistoryShow"
            cols="auto "
          >
            <g-custom-or13872
              v-bind="or13872"
              :oneway-model-value="localOneway.hisotrySelectOneway"
              @open-btn-click="historySelectClick"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row
          no-gutters
          class="d-flex flex-between align-center"
        >
          <c-v-col cols="auto">
            <c-v-row
              no-gutters
              class="d-flex align-center"
            >
              <!-- 作成日 -->
              <c-v-col cols="auto mr-2">
                <base-mo00020
                  v-model="local.createDate"
                  :oneway-model-value="localOneway.createDateOneway"
                  @update:model-value="createDateChange"
                />
              </c-v-col>
              <!-- 作成者 -->
              <c-v-col cols="auto mr-2">
                <g-custom-or13850
                  v-bind="or13850"
                  :oneway-model-value="localOneway.authorSelectOneway"
                  @open-btnclick="authorSelectClick"
                />
              </c-v-col>
              <c-v-col cosl="auto mr-2">
                <base-mo00045
                  v-model="local.caseSeq"
                  :oneway-model-value="localOneway.mo00045SeqOnewayType"
                />
              </c-v-col>
              <!-- 初回作成日 -->
              <c-v-col cols="auto ml-2">
                <base-mo00020
                  v-model="local.initCreateDate"
                  :oneway-model-value="localOneway.initCreateDateOneway"
                />
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mt-2"
        >
          <c-v-col>
            <base-mo00043
              v-model="local.filedMo00043"
              :oneway-model-value="localOneway.fieldTabsMo00043OnewayType"
            >
            </base-mo00043>
          </c-v-col>
        </c-v-row>
        <c-v-row
          v-show="local.filedMo00043.id !== '-1'"
          no-gutters
          class="mt-2"
        >
          <c-v-col>
            <base-mo00043
              v-model="local.classificationMo00043"
              :oneway-model-value="{ tabItems: classificationTabComputed }"
            >
            </base-mo00043>
          </c-v-col>
        </c-v-row>

        <!-- 中段 -->
        <c-v-row
          no-gutters
          class="middleContent flex-1-1 h-100"
        >
          <c-v-window
            v-show="local.filedMo00043.id !== '-1'"
            id="tabWindow"
            v-model="local.classificationMo00043.id"
            class="h-100"
          >
            <!-- タブ -->
            <c-v-window-item
              v-for="(item, index) in classificationTabComputed"
              :key="index"
              :value="item.id"
            >
              <g-custom-or-x-0106
                v-bind="orX0106Components[orX0106Str + index]"
                :oneway-model-value="item"
              >
                <!-- ジャンプボタン -->
                <template #jumpButton>
                  <div
                    class="cursor-pointer"
                    @click="onJump"
                  >
                    {{ t('まとめ·総括 ↓') }}
                  </div>
                </template>
              </g-custom-or-x-0106>
            </c-v-window-item>
          </c-v-window>
          <!--総括まとめ一覧  -->
          <div v-show="local.filedMo00043.id === '-1'">
            <g-custom-or-61587 v-bind="or61587_1" />
          </div>
        </c-v-row>
        <!-- 下段 -->
        <div v-show="local.filedMo00043.id !== '-1'">
          <c-v-row
            no-gutters
            class="mt-2"
          >
            <c-v-col>
              <base-mo00043
                v-model="local.summaryTabMo00043"
                :oneway-model-value="summaryTabOnewayTypeComputed"
              >
              </base-mo00043>
            </c-v-col>
          </c-v-row>
          <c-v-row no-gutters>
            <c-v-col>
              <div ref="summyTabWindow">
                <c-v-window
                  v-model="local.summaryTabMo00043.id"
                  class="h-100"
                >
                  <!-- タブ -->
                  <c-v-window-item value="1">
                    <!--分類[x]のまとめ -->
                    <g-custom-or-61588
                      v-bind="or61588_1"
                      :oneway-model-value="{
                        showType: '1',
                        classificationTabId: local.classificationMo00043.id,
                        filedTabId: local.filedMo00043.id,
                      }"
                    />
                  </c-v-window-item>
                  <c-v-window-item value="2">
                    <!--領域[X]のまとめ-->
                    <g-custom-or-61588
                      v-bind="or61588_2"
                      :oneway-model-value="{
                        showType: '0',
                        classificationTabId: local.classificationMo00043.id,
                        filedTabId: local.filedMo00043.id,
                      }"
                    />
                  </c-v-window-item>
                  <c-v-window-item value="3">
                    <g-custom-or-61587
                      v-bind="or61587_2"
                      :oneway-model-value="localOneway.or61587Oneway"
                    />
                  </c-v-window-item>
                </c-v-window>
              </div>
            </c-v-col>
          </c-v-row>
        </div>
        <c-v-row no-gutters>
          <c-v-col>
            <g-base-or00051 />
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>

    <!-- 事業所選択画面 -->
    <g-custom-orT-0001
      v-bind="orT0001"
      v-model="local.orT0001"
      :oneway-model-value="localOneway.orT0001Oneway"
    />

    <!-- 計画対象期間選択画面 -->
    <g-custom-or-x-0115
      v-if="showDialogOrX0115"
      v-bind="orX0115"
      :oneway-model-value="localOneway.orX0115Oneway"
    />
    <!-- 職員検索画面 -->
    <g-custom-orT-0003
      v-if="showDialogOrT0003"
      v-bind="orT0003"
      v-model="local.orT0003"
      :oneway-model-value="localOneway.orT0003Oneway"
    />

    <g-custom-or-61746
      v-bind="or61746"
      :key="copyComponentKey"
      :oneway-model-value="localOneway.or61746Oneway"
      :parent-unique-cp-id="props.uniqueCpId"
    />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_1" />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_2" />
  </c-v-sheet>
</template>

<style scoped lang="scss">
.view {
  background-color: transparent;
  height: max-content !important;
}
.action-sticky {
  position: sticky;
  top: 48px;
  z-index: 999;
  background: rgb(var(--v-theme-background));
  width: 100%;
}
:deep(.v-window__container) {
  height: 100%;
}

.main-Content {
  height: max-content;
  background: rgb(var(--v-theme-background));
  .main-left {
    max-width: 20%;
  }
  .main-right {
    .middleContent {
      min-height: 0;

      .v-window {
        width: 100%;
      }
    }
  }
}

.createDateOuterClass {
  width: 200px !important;
  background: rgb(var(--v-theme-background));

  :deep(.must-badge) {
    margin-left: unset;
  }

  :deep(.v-input__details) {
    display: none;
  }
}

.background-transparent {
  background-color: transparent !important;
}

// 事業所コンポーネントの上下構造を設定する
.custom_Or41179 {
  display: block;
}

// 日付選択ツールの上下構造スタイル設定
:deep(.text-date-area-style) {
  align-items: end;
}
:deep(.text-date-area-style .v-col:nth-child(3)) {
  display: block !important;
  width: 100% !important;
  margin-top: 4px;
}
// 作成者コンポーネントの上下構造スタイル設定
.officeSelect {
  width: 200px !important;
}
</style>
