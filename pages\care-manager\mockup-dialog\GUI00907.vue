<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  watch,
} from '#imports'
import { Or28571Const } from '~/components/custom-components/organisms/Or28571/Or28571.constants'
import { Or28571Logic } from '~/components/custom-components/organisms/Or28571/Or28571.logic'
import type { HistorySelectTableDataItem } from '~/components/custom-components/organisms/Or28571/Or28571.type'
import type { HistorySelectInfoType } from '~/types/cmn/business/components/Or28571Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * 画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00907'
// ルーティング
const routing = 'GUI00907/pinia'
// 画面物理名
const screenName = 'GUI00907'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

// 有機体or28571ユーニックID
const or28571 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00907' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  // Or28571Logic.initialize(or28571.value.uniqueCpId)
}

// 子コンポーネントのユニークIDを設定する
or28571.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00907',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or28571Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or28571Const.CP_ID(1)]: or28571.value,
})

/**************************************************
 * Props
 **************************************************/

// ダイアログ表示フラグ
const showDialogOr28571 = computed(() => {
  // Or28571のダイアログ開閉状態
  return Or28571Logic.state.get(or28571.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or28571Data: HistorySelectInfoType = {
  // 事業者ID
  svJigyoId: '1',
  // 利用者ID
  userId: '1',
  // 計画期間ID
  sc1Id: '31',
  // 履歴ID
  assId: '',
}

const or28571Type = ref<HistorySelectTableDataItem>({
  createYmd: '',
  memoContent: '',
  titleKnj: '',
  assId: '',
})
watch(or28571Type, () => {
  console.log(or28571Type.value)
})

/**
 *  ボタン押下時の処理
 *
 * 期間管理フラグ
 */
function or28571OnClick() {
  // Or28571のダイアログ開閉状態を更新する
  Or28571Logic.state.set({
    uniqueCpId: or28571.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>GUI00907_［履歴選択］画面 課題立案画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >GUI00907_［履歴選択］画面 課題立案画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ CD 2025/02/27 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or28571OnClick()"
        >GUI00907_［履歴選択］画面 課題立案画面
      </v-btn>
      <g-custom-or28571
        v-if="showDialogOr28571"
        v-bind="or28571"
        v-model="or28571Type"
        :oneway-model-value="or28571Data"
      />
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ CD 2025/02/27 ADD END-->
</template>
