import { cloneDeep } from 'lodash'
import { Or33216Const } from './Or33216.constants'
import { useTwoWayBindAccessor, useInitialize } from '~/composables/useComponentLogic'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or28389Logic } from '~/components/custom-components/organisms/Or28389/Or28389.logic'
import { Or28389Const } from '~/components/custom-components/organisms/Or28389/Or28389.constants'
import { Or26257Logic } from '~/components/custom-components/organisms/Or26257/Or26257.logic'
import { Or26257Const } from '~/components/custom-components/organisms/Or26257/Or26257.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or33216OnewayType, Or33216Type } from '~/types/cmn/business/components/Or33216Type'
import type { FaceSheetPackage1InitSelectDataEntity } from '~/repositories/cmn/entities/FaceSheetPackage1InitSelectEntity'

/**
 * Or33216:有機体：GUI00635_［フェースシート（パッケージプラン）］①画面
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or33216Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize<object>({
      cpId: Or33216Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or51775Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or28389Const.CP_ID(1) },
        { cpId: Or26257Const.CP_ID(1) },
      ],
      // TwoWayBind領域初期化用の初期値
      initTwoWayValue: cloneDeep(Or33216Const.DEFAULT.TWO_WAY),
    })

    Or51775Logic.initialize(childCpIds[Or51775Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or28389Logic.initialize(childCpIds[Or28389Const.CP_ID(1)].uniqueCpId)
    Or26257Logic.initialize(childCpIds[Or26257Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<Or33216Type>(Or33216Const.CP_ID(0))

  /**
   * サーバーの戻り値を画面のOneWayBindの型に変換
   *
   * @param facesheet1 - サーバーの戻り値
   *
   * @returns 画面のOneWayBind
   */
  export const convertApiEntityToOneWayValue = (
    facesheet1: FaceSheetPackage1InitSelectDataEntity
  ): Or33216OnewayType => {
    const oneWayValue: Or33216OnewayType = cloneDeep(Or33216Const.DEFAULT.ONE_WAY)

    facesheet1.zokugaraList?.forEach((zokugara) => {
      oneWayValue.zokugaraList.push({
        value: zokugara.zcode,
        title: zokugara.zokugaraKnj,
      })
    })
    oneWayValue.modifiedCnt = facesheet1.face1List?.[0]?.modifiedCnt ?? ''

    return oneWayValue
  }

  /**
   * サーバーの戻り値を画面のTwoWayBindの型に変換
   *
   * @param facesheet1 - サーバーの戻り値
   *
   * @returns 画面のTwoWayBind
   */
  export const convertApiEntityToTwoWayValue = (
    facesheet1: FaceSheetPackage1InitSelectDataEntity
  ): Or33216Type => {
    const faceSheet1Data = cloneDeep(Or33216Const.DEFAULT.TWO_WAY)
    /**
     * 前住所
     */
    faceSheet1Data.zenAddressKnj.value = facesheet1.face1List?.[0]?.zenAddressKnj ?? ''
    /**
     * 入所年月日
     */
    faceSheet1Data.nyushoYmd.value = facesheet1.face1List?.[0]?.nyushoYmd ?? ''
    /**
     * 措置の実施機関
     */
    faceSheet1Data.kikanNameKnj.value = facesheet1.face1List?.[0]?.kikanNameKnj ?? ''
    /**
     * 福祉事務所（町・村）
     */
    faceSheet1Data.kikanKukuKbn = facesheet1.face1List?.[0]?.kikanKukuKbn ?? ''
    /**
     * 担当CW ID
     */
    faceSheet1Data.tantoCwId = facesheet1.face1List?.[0]?.tantoCwId ?? ''
    /**
     * 担当CW
     */
    faceSheet1Data.tantoCw = facesheet1.face1List?.[0]?.tantoCwName ?? ''
    /**
     * 家族氏名1
     */
    faceSheet1Data.kazoku1Knj.value = facesheet1.face1List?.[0]?.kazoku1Knj ?? ''
    /**
     * 身元引受人フラグ1
     */
    faceSheet1Data.mimoto1F.modelValue =
      facesheet1.face1List?.[0]?.mimoto1F === Or33216Const.MIMOTO_FLAG_TRUE
    /**
     * 家族続柄1
     */
    faceSheet1Data.kazokuZoku1Cd.modelValue = facesheet1.face1List?.[0]?.kazokuZoku1Cd ?? ''
    /**
     * 家族住所1
     */
    faceSheet1Data.kzokuAddr1Knj.value = facesheet1.face1List?.[0]?.kzokuAddr1Knj ?? ''
    /**
     * 家族連絡先1
     */
    faceSheet1Data.kazokuRenraku1.value = facesheet1.face1List?.[0]?.kazokuRenraku1 ?? ''
    /**
     * 家族順位1
     */
    faceSheet1Data.kazokuYusen1.mo00045.value = facesheet1.face1List?.[0]?.kazokuYusen1 ?? ''
    /**
     * 家族氏名2
     */
    faceSheet1Data.kazoku2Knj.value = facesheet1.face1List?.[0]?.kazoku2Knj ?? ''
    /**
     * 身元引受人フラグ2
     */
    faceSheet1Data.mimoto2F.modelValue =
      facesheet1.face1List?.[0]?.mimoto2F === Or33216Const.MIMOTO_FLAG_TRUE
    /**
     * 家族続柄2
     */
    faceSheet1Data.kazokuZoku2Cd.modelValue = facesheet1.face1List?.[0]?.kazokuZoku2Cd ?? ''
    /**
     * 家族住所2
     */
    faceSheet1Data.kzokuAddr2Knj.value = facesheet1.face1List?.[0]?.kzokuAddr2Knj ?? ''
    /**
     * 家族連絡先2
     */
    faceSheet1Data.kazokuRenraku2.value = facesheet1.face1List?.[0]?.kazokuRenraku2 ?? ''
    /**
     * 家族順位2
     */
    faceSheet1Data.kazokuYusen2.mo00045.value = facesheet1.face1List?.[0]?.kazokuYusen2 ?? ''
    /**
     * 家族氏名3
     */
    faceSheet1Data.kazoku3Knj.value = facesheet1.face1List?.[0]?.kazoku3Knj ?? ''
    /**
     * 身元引受人フラグ3
     */
    faceSheet1Data.mimoto3F.modelValue =
      facesheet1.face1List?.[0]?.mimoto3F === Or33216Const.MIMOTO_FLAG_TRUE
    /**
     * 家族続柄3
     */
    faceSheet1Data.kazokuZoku3Cd.modelValue = facesheet1.face1List?.[0]?.kazokuZoku3Cd ?? ''
    /**
     * 家族住所3
     */
    faceSheet1Data.kzokuAddr3Knj.value = facesheet1.face1List?.[0]?.kzokuAddr3Knj ?? ''
    /**
     * 家族連絡先3
     */
    faceSheet1Data.kazokuRenraku3.value = facesheet1.face1List?.[0]?.kazokuRenraku3 ?? ''
    /**
     * 家族順位3
     */
    faceSheet1Data.kazokuYusen3.mo00045.value = facesheet1.face1List?.[0]?.kazokuYusen3 ?? ''
    /**
     * 家族氏名4
     */
    faceSheet1Data.kazoku4Knj.value = facesheet1.face1List?.[0]?.kazoku4Knj ?? ''
    /**
     * 身元引受人フラグ4
     */
    faceSheet1Data.mimoto4F.modelValue =
      facesheet1.face1List?.[0]?.mimoto4F === Or33216Const.MIMOTO_FLAG_TRUE
    /**
     * 家族続柄4
     */
    faceSheet1Data.kazokuZoku4Cd.modelValue = facesheet1.face1List?.[0]?.kazokuZoku4Cd ?? ''
    /**
     * 家族住所4
     */
    faceSheet1Data.kzokuAddr4Knj.value = facesheet1.face1List?.[0]?.kzokuAddr4Knj ?? ''
    /**
     * 家族連絡先4
     */
    faceSheet1Data.kazokuRenraku4.value = facesheet1.face1List?.[0]?.kazokuRenraku4 ?? ''
    /**
     * 家族順位4
     */
    faceSheet1Data.kazokuYusen4.mo00045.value = facesheet1.face1List?.[0]?.kazokuYusen4 ?? ''
    /**
     * 家族氏名5
     */
    faceSheet1Data.kazoku5Knj.value = facesheet1.face1List?.[0]?.kazoku5Knj ?? ''
    /**
     * 身元引受人フラグ5
     */
    faceSheet1Data.mimoto5F.modelValue =
      facesheet1.face1List?.[0]?.mimoto5F === Or33216Const.MIMOTO_FLAG_TRUE
    /**
     * 家族続柄5
     */
    faceSheet1Data.kazokuZoku5Cd.modelValue = facesheet1.face1List?.[0]?.kazokuZoku5Cd ?? ''
    /**
     * 家族住所5
     */
    faceSheet1Data.kzokuAddr5Knj.value = facesheet1.face1List?.[0]?.kzokuAddr5Knj ?? ''
    /**
     * 家族連絡先5
     */
    faceSheet1Data.kazokuRenraku5.value = facesheet1.face1List?.[0]?.kazokuRenraku5 ?? ''
    /**
     * 家族順位5
     */
    faceSheet1Data.kazokuYusen5.mo00045.value = facesheet1.face1List?.[0]?.kazokuYusen5 ?? ''
    /**
     * 家族氏名6
     */
    faceSheet1Data.kazoku6Knj.value = facesheet1.face1List?.[0]?.kazoku6Knj ?? ''
    /**
     * 身元引受人フラグ6
     */
    faceSheet1Data.mimoto6F.modelValue =
      facesheet1.face1List?.[0]?.mimoto6F === Or33216Const.MIMOTO_FLAG_TRUE
    /**
     * 家族続柄6
     */
    faceSheet1Data.kazokuZoku6Cd.modelValue = facesheet1.face1List?.[0]?.kazokuZoku6Cd ?? ''
    /**
     * 家族住所6
     */
    faceSheet1Data.kzokuAddr6Knj.value = facesheet1.face1List?.[0]?.kzokuAddr6Knj ?? ''
    /**
     * 家族連絡先6
     */
    faceSheet1Data.kazokuRenraku6.value = facesheet1.face1List?.[0]?.kazokuRenraku6 ?? ''
    /**
     * 家族順位6
     */
    faceSheet1Data.kazokuYusen6.mo00045.value = facesheet1.face1List?.[0]?.kazokuYusen6 ?? ''
    /**
     * 家族氏名7
     */
    faceSheet1Data.kazoku7Knj.value = facesheet1.face1List?.[0]?.kazoku7Knj ?? ''
    /**
     * 身元引受人フラグ7
     */
    faceSheet1Data.mimoto7F.modelValue =
      facesheet1.face1List?.[0]?.mimoto7F === Or33216Const.MIMOTO_FLAG_TRUE
    /**
     * 家族続柄7
     */
    faceSheet1Data.kazokuZoku7Cd.modelValue = facesheet1.face1List?.[0]?.kazokuZoku7Cd ?? ''
    /**
     * 家族住所7
     */
    faceSheet1Data.kzokuAddr7Knj.value = facesheet1.face1List?.[0]?.kzokuAddr7Knj ?? ''
    /**
     * 家族連絡先7
     */
    faceSheet1Data.kazokuRenraku7.value = facesheet1.face1List?.[0]?.kazokuRenraku7 ?? ''
    /**
     * 家族順位7
     */
    faceSheet1Data.kazokuYusen7.mo00045.value = facesheet1.face1List?.[0]?.kazokuYusen7 ?? ''
    /**
     * 家族氏名8
     */
    faceSheet1Data.kazoku8Knj.value = facesheet1.face1List?.[0]?.kazoku8Knj ?? ''
    /**
     * 身元引受人フラグ8
     */
    faceSheet1Data.mimoto8F.modelValue =
      facesheet1.face1List?.[0]?.mimoto8F === Or33216Const.MIMOTO_FLAG_TRUE
    /**
     * 家族続柄8
     */
    faceSheet1Data.kazokuZoku8Cd.modelValue = facesheet1.face1List?.[0]?.kazokuZoku8Cd ?? ''
    /**
     * 家族住所8
     */
    faceSheet1Data.kzokuAddr8Knj.value = facesheet1.face1List?.[0]?.kzokuAddr8Knj ?? ''
    /**
     * 家族連絡先8
     */
    faceSheet1Data.kazokuRenraku8.value = facesheet1.face1List?.[0]?.kazokuRenraku8 ?? ''
    /**
     * 家族順位8
     */
    faceSheet1Data.kazokuYusen8.mo00045.value = facesheet1.face1List?.[0]?.kazokuYusen8 ?? ''
    /**
     * 入所措置の端緒
     */
    faceSheet1Data.nyushoSochiKnj.value = facesheet1.face1List?.[0]?.nyushoSochiKnj ?? ''
    /**
     * 生活歴
     */
    faceSheet1Data.seikaturekiKnj.value = facesheet1.face1List?.[0]?.seikaturekiKnj ?? ''
    /**
     * 住居の状況区分
     */
    faceSheet1Data.jukyoKbn = facesheet1.face1List?.[0]?.jukyoKbn ?? ''
    /**
     * 住居の状況その他
     */
    faceSheet1Data.jukyoSonotaKnj.value = facesheet1.face1List?.[0]?.jukyoSonotaKnj ?? ''
    /**
     * 帰来先の有無区分
     */
    faceSheet1Data.kiraisakiUmu = facesheet1.face1List?.[0]?.kiraisakiUmu ?? ''
    /**
     * 帰来先有
     */
    faceSheet1Data.kiraisakiAriKnj.value = facesheet1.face1List?.[0]?.kiraisakiAriKnj ?? ''

    return faceSheet1Data
  }
}
