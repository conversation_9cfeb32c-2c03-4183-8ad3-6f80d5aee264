<script setup lang="ts">
/**
 * Or27946:有機体:総合事業サービス単位再設定
 * GUI01172_総合事業サービス単位再設定画面
 *
 * @description
 * 総合事業サービス単位再設定
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { Or27946Const } from './Or27946.constants'
import type { Or27946StateType } from './Or27946.type'
import type { Or27946OneWayType } from '~/types/cmn/business/components/Or27946Type'
import { definePageMeta, useScreenOneWayBind } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Or26183Type } from '~/types/cmn/business/components/Or26183Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import { CustomClass } from '~/types/CustomClassType'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  SougouJigyoServiceUnitReconfigureExecutionProcessUpdateInEntity,
  SougouJigyoServiceUnitReconfigureExecutionProcessUpdateOutEntity,
} from '~/repositories/cmn/entities/SougouJigyoServiceUnitReconfigureExecutionProcessUpdateEntity'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Mo01352OnewayType } from '~/types/business/components/Mo01352Type'

definePageMeta({
  layout: 'business-platform-layout',
})

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or27946OneWayType
  uniqueCpId: string
  parentCpId: string
}

const props = defineProps<Props>()
const defaultOnewayModelValue: Or27946OneWayType = {
  /**
   * システム年月
   */
  sysYm: '',
  /**
   * システムコード
   */
  sysCd: '1',
  /**
   * 職員ID
   */
  shokuId: '1',
  /**
   * 適用事業所IDリスト
   */
  applicableOfficeIdList: ['1'],
}

const localOneway = reactive({
  Or27946: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 「総合事業サービス単位再設定」ダイアログ
  mo00024Oneway: {
    width: '600px',
    height: '300px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.comprehensive-project-unit-reset'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  // 確認ボタン
  mo00609Oneway: {
    btnLabel: t('btn.ok'),
  } as Mo00609OnewayType,
  mo00040Oneway: {
    itemLabel: Or27946Const.CP_ID(0),
    showItemLabel: false,
    isRequired: false,
    items: [],
    width: '240px',
  } as Mo00040OnewayType,
  mo01282TermidOneway: [] as CodeType[],
  mo01282DataidOneway: [] as CodeType[],
  // 機能説明文ラベル
  mo01338FunctionDescriptionOneway: {
    value: t('label.function-description'),
  } as Mo01338OnewayType,
  // 対象年月ラベル
  mo00615SubjectYearAndMonthOnewayType: {
    itemLabel: t('label.subject-year-and-month'),
    customClass: new CustomClass({
      outerClass: 'pl-6 mr-0 pr-0',
      itemClass: 'ml-4 align-center',
    }),
  } as Mo00615OnewayType,
  // ～ラベル
  mo00615WavyOnewayType: {
    itemLabel: t('label.wavy'),
    customClass: new CustomClass({
      outerClass: 'pl-2 mr-0 pr-0',
      itemClass: 'ml-0 align-center',
    }),
  } as Mo00615OnewayType,
  mo01338MonthOneway: {
    value: t('label.month-sel'),
    customClass: 'margin-none',
  },
  //<ボタン
  backmo00009Oneway: {
    variant: 'text',
    color: 'black-800',
    btnIcon: 'chevron_left',
  },
  //>ボタン
  forwardmo00009Oneway: {
    variant: 'text',
    color: 'black-800',
    btnIcon: 'chevron_right',
  },
  mo00020Oneway: { showItemLabel: false, customClass: { outerClass: 'disp_with' } },
})
const defaultModelValue: Or26183Type = {
  value: '',
}

const local = reactive({
  Or26183: {
    ...defaultModelValue,
  },
  // 本画面
  Or27946State: {
    selectedTitleValue: '',
  } as Or27946StateType,
  mo00024: {
    isOpen: true,
  },
  mo00020TargetStartYm: {
    value: '',
  } as Mo00020Type,
  mo00020TargetEndYm: {
    value: '',
  } as Mo00020Type,
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00611Oneway1 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
})

const mo00611Oneway2 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.execution'),
  width: '90px',
  tooltipText: t('tooltip.execution-btn'),
  disabled: false,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or27946Const.DEFAULT.IS_OPEN,
})

const mo01352Oneway = ref<Mo01352OnewayType>({
  textFieldwidth: '115px',
  disabled: false,
  width: '150px',
})
// 権限フラグ
const isPermissionViewAuth = ref(false)
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27946StateType>({
  cpId: Or27946Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or27946Const.DEFAULT.IS_OPEN
    },
  },
})

//Or21814_有機体:確認ダイアログ
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const or21815 = ref({ uniqueCpId: Or21815Const.CP_ID(0) })
const or21813 = ref({ uniqueCpId: '' })
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
//警告ウィンドウを表示
const showDialogOr21815 = computed(() => {
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})
// エラー表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * ウォッチャー
 **************************************************/
// 画面開閉フラグ
watch(
  () => local.mo00024.isOpen,
  () => {
    setState({ isOpen: local.mo00024.isOpen })
  }
)
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期処理
  await init()
})
/** 初期処理 */
async function init() {
  isPermissionViewAuth.value = await hasRegistAuth()

  // 権限によって保存ボタンを制御
  if (!isPermissionViewAuth.value) {
    mo00611Oneway2.value.disabled = false
    // mo00611Oneway2.value.disabled = true
  }

  // 対象開始年月
  local.mo00020TargetStartYm.value = localOneway.Or27946.sysYm
  // 対象終了年月
  local.mo00020TargetEndYm.value = localOneway.Or27946.sysYm
}

/**
 * 閉じるボタン押下時
 */
function onClickCloseBtn(): void {
  setState({ isOpen: false })
}
/**
 * 実行ボタン処理
 *
 */
async function doConfirm() {
  // パラメータ
  const param: SougouJigyoServiceUnitReconfigureExecutionProcessUpdateInEntity = {
    // 事業所IDリスト
    svJigyoIdList: localOneway.Or27946.applicableOfficeIdList,
    // 開始年月
    fromYm: local.mo00020TargetStartYm.value,
    // 終了年月
    toYm: local.mo00020TargetEndYm.value,
  }
  // 総合事業サービス単位再設定実行処理を行う。
  const ret: SougouJigyoServiceUnitReconfigureExecutionProcessUpdateOutEntity =
    await ScreenRepository.update('sougouJigyoServiceUnitReconfigureExecutionProcessUpdate', param)

  if (ret.data.result !== Or27946Const.DEFAULT.RESULT_OK) {
    // 処理結果 <> 1の場合、エラーメッセージを表示する。
    // メッセージ内容：単位数更新処理が、異常終了しました。[改行]データは更新されていません。
    showOr21813MsgOneBtn(t('message.e-cmn-40434'))
  } else {
    // 単位数更新処理が、正常終了しました。[改行]利用票(利用票更新件数)件[改行]シミュレーション(シミュレーション更新件数)件
    showOr21814MsgOneBtn_11407(ret.data.riyouhyouUpdatecount, ret.data.simUpdatecount)
  }
}
/**
 * 実行ボタン押下時
 */
function onExecutionBtn(): void {
  if (local.mo00020TargetStartYm.value > local.mo00020TargetEndYm.value) {
    // 対象開始年月 > 対象終了年月の場合、確認ダイアログを表示する。
    // メッセージ内容：終了年月に、開始年月より前の年月が指定されています。
    showOr21814MsgOneBtn(t('message.i-cmn-10396'))
  } else {
    // 指定された期間に対し処理を行います。[改行]実行してよろしいですか？
    showOr21815MsgTwoBtn(t('message.w-cmn-20826'))
  }
}
/**
 * 「実行ボタン」押下
 * 指定された期間に対し処理を行います。[改行]実行してよろしいですか？
 *
 * @param errormsg - errormsg - Message
 */
function showOr21815MsgTwoBtn(errormsg: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      dialogTitle: t('label.warning'),
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * 「実行ボタン」押下
 * 終了年月に、開始年月より前の年月が指定されています。
 *
 * @param errormsg - Message
 */
function showOr21814MsgOneBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm-dialog-title-info'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      thirdBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.ok'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * 「実行ボタン」押下
 * 単位数更新処理が、正常終了しました。[改行]利用票( {0} )件[改行]シミュレーション( {1} )件
 *
 * @param riyouhyouUpdateCount - 利用票更新件数
 *
 * @param simUpdateCount - シミュレーション更新件数
 */
function showOr21814MsgOneBtn_11407(riyouhyouUpdateCount: string, simUpdateCount: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm-dialog-title-info'),
      // ダイアログテキスト,
      dialogText: t('message.i-cmn-11407', [riyouhyouUpdateCount, simUpdateCount]),
      firstBtnType: 'blank',
      thirdBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.ok'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * 「実行ボタン」押下
 * 単位数更新処理が、異常終了しました。[改行]データは更新されていません。
 *
 * @param errormsg - Message
 */
function showOr21813MsgOneBtn(errormsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // エラーダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**************************************************
 * Emit
 **************************************************/
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
/**
 * OrX0020（閉じる処理ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue.firstBtnClickFlg) {
      void doConfirm()
      return
    }
    if (newValue.secondBtnClickFlg) {
      // いいえ：処理終了。
      return
    }
  }
)

/**
 * （実行処理ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue.firstBtnClickFlg) {
      // 処理終了。
      return
    }
  }
)
/**
 * （実行処理ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => Or21815Logic.event.get(or21815.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue.firstBtnClickFlg) {
      void doConfirm()
      return
    }
    if (newValue.secondBtnClickFlg) {
      // 処理終了。
      return
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <!-- 機能説明文ラベル -->
      <base-mo01338 :oneway-model-value="localOneway.mo01338FunctionDescriptionOneway" />
      <c-v-row
        no-gutters
        class="align-center pt-4"
        >
        <!-- 対象年月ラベル -->
        <base-mo00615 :oneway-model-value="localOneway.mo00615SubjectYearAndMonthOnewayType" />
        <c-v-col cols="4">
          <!-- 年月選択 -->
          <base-mo01352
            v-model="local.mo00020TargetStartYm"
            style="align-items: center"
            :oneway-model-value="mo01352Oneway"
          />
        </c-v-col>
        <!-- ～ラベル -->
        <base-mo00615 :oneway-model-value="localOneway.mo00615WavyOnewayType" />

        <c-v-col cols="4">
          <!-- 年月選択 -->
          <base-mo01352
            v-model="local.mo00020TargetEndYm"
            style="align-items: center"
            :oneway-model-value="mo01352Oneway"
          />
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="mo00611Oneway1"
          @click="onClickCloseBtn"
        >
        </base-mo00611>

        <!-- 実行ボタン -->
        <base-mo00609
          :oneway-model-value="mo00611Oneway2"
          class="mx-2"
          @click="onExecutionBtn"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21813:有機体 -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
  <!-- Or21815:有機体 -->
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
tr {
  cursor: pointer;
}

.v-col {
  padding: 8px;
}
:deep(.v-table--fixed-header) {
  position: sticky;
  // ジッタ除去
  top: 0px;
  z-index: 2;
}
.flex-container {
  display: flex;
}
.align-center {
  align-items: center !important;
}
:deep(.disp_with .d-flex) {
  width: 100px !important;
}
</style>
