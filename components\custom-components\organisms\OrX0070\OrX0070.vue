<script setup lang="ts">
/**
 * OrX0070:有機体:(日課表取込)日課表明細一覧
 * GUI00981_日課表取込
 *
 * @description
 * 日課表明細一覧を表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import {  onMounted, reactive, watch } from 'vue'
import { OrX0070Const } from './OrX0070.constants'
import type {
  OrX0070OneWayType,
  OrX0070Type,
} from '~/types/cmn/business/components/OrX0070Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import { useScreenTwoWayBind } from '#imports'
import type { Mo01334OnewayType } from '~/types/business/components/Mo01334Type'

/**
 * useI18n
 */
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  modelValue?: OrX0070Type
  onewayModelValue?: OrX0070OneWayType
}

/**
 * props
 */
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
/**
 * ローカルOneway
 */
const localOneway = reactive({
  orX0070: {
    ...props.modelValue,
  },
  orX0070OneWay: {
    ...props.onewayModelValue,
  } as OrX0070OneWayType,
  mo01334Oneway: {
    headers: [],
    items: [],
    height: '265px',
    showSelect: true,
    selectStrategy: 'all',
    mandatory: false,
  } as Mo01334OnewayType,
  mo00018Oneway: {
    showItemLabel: false,
    hideDetails: true,
    checkboxLabel: t('label.manager-memo-import'),
  } as Mo00018OnewayType,
  // 絞込ボタン
  importbtnOneway: {
    btnLabel: t('btn.filter-import'),
  },
  mo00040Oneway: {
    showItemLabel: false,
    isRequired: false,
    width: '240px',
    items: [],
  } as Mo00040OnewayType,
})
/**************************************************
 * Pinia
 **************************************************/
/**
 * Pinia refValue
 */
const { refValue } = useScreenTwoWayBind<OrX0070Type>({
  cpId: OrX0070Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  localOneway.mo01334Oneway.headers = localOneway.orX0070OneWay.headers
  localOneway.mo01334Oneway.items = localOneway.orX0070OneWay.items
  localOneway.mo00040Oneway.items = localOneway.orX0070OneWay.selectList
})
/**************************************************
 * ウォッチャー
 **************************************************/
 watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue) {
      localOneway.orX0070OneWay = {
        ...newValue,
      }
      localOneway.mo01334Oneway.headers = localOneway.orX0070OneWay.headers
      localOneway.mo01334Oneway.items = localOneway.orX0070OneWay.items
      localOneway.mo00040Oneway.items = []
      for (const item of localOneway.orX0070OneWay.selectList) {
        localOneway.mo00040Oneway.items.push({ value: item.value, title: item.label })
      }
    }
  },
  { deep: true }
)
/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 *  「絞込ボタン」押下
 */
function filterImportClick() {
  if (!localOneway.mo00040Oneway.items?.length) {
    return
  }
  const currentIndex = localOneway.orX0070OneWay.selectList.findIndex(
    (item) => item.value === localOneway.orX0070OneWay.dailyImportSelected.modelValue
  )
  if (currentIndex === -1) {
    localOneway.orX0070OneWay.dailyImportSelected.modelValue =
      localOneway.orX0070OneWay.selectList[0].value
  }
  const nextIndex = (currentIndex + 1) % localOneway.orX0070OneWay.selectList.length
  localOneway.orX0070OneWay.dailyImportSelected.modelValue =
    localOneway.orX0070OneWay.selectList[nextIndex].value
}
/**
 * タイトルのイベントを監視
 */
watch(
  () => localOneway.orX0070OneWay.dailyImportSelected.modelValue,
  (newValue) => {
    if (localOneway.orX0070OneWay.oraggeItems) {
      if (newValue?.toString() === OrX0070Const.DEFAULT.ALL) {
        localOneway.mo01334Oneway.items = localOneway.orX0070OneWay.oraggeItems
      } else {
        localOneway.mo01334Oneway.items = localOneway.orX0070OneWay.oraggeItems.filter(
          (item) => item.kbn === newValue
        )
      }
    }
  }
)
</script>

<template>
  <c-v-row no-gutters>
    <c-v-col cols="auto">
      <base-mo00018
        v-if="refValue"
        v-model="refValue!.managerMemoImport"
        :oneway-model-value="localOneway.mo00018Oneway"
      />
    </c-v-col>
    <c-v-col cols="auto"
      ><base-mo00611
        class="w-auto flex-0-0 mt-0 mr-2"
        :oneway-model-value="localOneway.importbtnOneway"
        @click="filterImportClick"
    /></c-v-col>
    <c-v-col
      cols="auto"
      class="w-auto flex-0-0 mt-0"
    >
      <base-mo00040
        v-model="localOneway.orX0070OneWay.dailyImportSelected"
        :oneway-model-value="localOneway.mo00040Oneway"
        v-bind="{ ...$attrs }"
      />
    </c-v-col>
  </c-v-row>
  <hr class="v-divider" />
  <c-v-row>
    <c-v-col style="margin: 8px !important; padding: 0px !important;" class="table-header table-wrapper">
      <base-mo-01334
        v-if="refValue"
        v-model="refValue!.selectedRowId"
        :oneway-model-value="localOneway.mo01334Oneway"
        width="550px"
        class="list-wrapper"
        :hide-default-footer="true"
      >
    </base-mo-01334>
    </c-v-col>
  </c-v-row>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
.v-divider {
  margin-top: 8px;
  margin-bottom: 8px;
}
</style>
