<script setup lang="ts">
/**
 * Or52786:（総合計画）総合計画
 * GUI00971_総合計画
 *
 * @description
 * 総合計画画面の処理
 *
 * <AUTHOR> DO AI QUOC
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { Or52768Const } from '../Or52768/Or52768.constants'
import { Or52771Const } from '../Or52771/Or52771.constants'
import { Or52769Const } from '../Or52769/Or52769.constants'
import { Or52770Const } from '../Or52770/Or52770.constants'
import { Or52788Const } from '../Or52788/Or52788.constants'
import { Or52772Const } from '../Or52772/Or52772.constants'
import { Or52768Logic } from '../Or52768/Or52768.logic'
import { Or52771Logic } from '../Or52771/Or52771.logic'
import { Or27299Logic } from '../Or27299/Or27299.logic'
import { Or27299Const } from '../Or27299/Or27299.constants'
import { Or28376Logic } from '../Or28376/Or28376.logic'
import { Or52785Const } from '../Or52785/Or52785.constants'
import { OrX0115Const } from '../OrX0115/OrX0115.constants'
import { Or28382Const } from '../Or28382/Or28382.constants'
import { Or28382Logic } from '../Or28382/Or28382.logic'
import { Or52770Logic } from '../Or52770/Or52770.logic'
import { Or52769Logic } from '../Or52769/Or52769.logic'
import { Or52788Logic } from '../Or52788/Or52788.logic'
import { Or52772Logic } from '../Or52772/Or52772.logic'
import { Or52786Const } from './Or52786.constants'
import type { ISougouKakRirekiSelectOut, ISougouKakSelectOutData } from './Or52786.type'
import { OrX0115Logic } from '~/components/custom-components/organisms/OrX0115/OrX0115.logic'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import {
  useCommonProps,
  useScreenInitFlg,
  useScreenStore,
  useScreenTwoWayBind,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import type { OrHeadLineType } from '~/types/business/generator-components/OrHeadLineType'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Or52768OnewayType, Or52768Type } from '~/types/cmn/business/components/Or52768Type'
import type {
  IComprehensivePlanInEntity,
  IComprehensivePlanOutEntity,
  ISougouKakSyousai,
} from '~/repositories/cmn/entities/ComprehensivePlanEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or52769OnewayType, Or52769Type } from '~/types/cmn/business/components/Or52769Type'
import type { Or52770OnewayType } from '~/types/cmn/business/components/Or52770Type'
import type { Or52771OnewayType, Or52771Type } from '~/types/cmn/business/components/Or52771Type'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import type { Or52788OnewayType } from '~/types/cmn/business/components/Or52788Type'
import type { Or52772OnewayType, Or52772Type } from '~/types/cmn/business/components/Or52772Type'
import type { Or52776OnewayType } from '~/types/cmn/business/components/Or52776Type'
import type { Or27299OneWayType } from '~/types/cmn/business/components/Or27299Type'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Or00094TwowayType } from '~/components/base-components/organisms/Or00094/Or00094.type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { NavControlJsonType } from '~/types/business/core/DefinitionJsonType'
import type { Or52785OnewayType, Or52785Type } from '~/types/cmn/business/components/Or52785Type'
import type { ComprehensivePlanInfoType } from '~/types/cmn/business/components/Or52786Type'
import type {
  Or21814EventType,
  Or21814OnewayType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { HistType, Or28382OnewayType } from '~/types/cmn/business/components/Or28382Type'
import type { SougouKakYoKaigodoListSelectOutEntity } from '~/repositories/cmn/entities/SougouKakYoKaigodoListSelectEntity'
import type { PlanTargetPeriodDataType } from '~/types/cmn/PlanTargetPeriodDataType'
import { SPACE_WAVE } from '~/constants/classification-constants'
import type { IComprehensivePlanUpdateInEntity } from '~/repositories/cmn/entities/ComprehensivePlanUpdateEntity'
import type { SougouKakNewCreateSelectInEntity, SougouKakNewCreateSelectOutEntity } from '~/repositories/cmn/entities/SougouKakNewCreateSelectEntity'
import type { SougouKakaTekiyoJigyosyoListInfoSelectOutEntity, SougouKakaTekiyoJigyosyoListInfoSelectInEntity } from '~/repositories/cmn/entities/SougouKakaTekiyoJigyosyoListInfoSelectEntity'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import type { SougouKakKeikakuKikanChangeInfoSelectInEntity, SougouKakKeikakuKikanChangeInfoSelectOutEntity } from '~/repositories/cmn/entities/SougouKakKeikakuKikanChangeInfoSelect'
import type { SougouKakRirekiChangeInfoSelectInEntity, SougouKakRirekiChangeInfoSelectOutEntity } from '~/repositories/cmn/entities/SougouKakRirekiChangeInfoSelect'
import type { Mo01408ItemType } from '~/types/business/components/Mo01408Type'

/**
 * pinia内のコンポーネントの値を一括で設定する
 */
const { setChildCpBinds } = useScreenUtils()
/**
 * useI18n初期化
 */
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
/**
 * 引継情報を取得する
 */
const props = defineProps(useCommonProps())

/**
 * システム共有領域の状態管理
 */
const systemCommonsStore = useSystemCommonsStore()

/**
 * 確認ダイアログのPromise
 */
let or21814ResolvePromise: (value: Or21814EventType) => void

/**
 * 共通情報
 */
const commonInfoData = reactive({
  // 事業者ID
  // svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
  svJigyoId: '1',
  // 利用者ID
  //userId: systemCommonsStore.getUserId ?? '',
  userId: '0000000148', // TODO APIとバックエンドを結合するために使用される
  // 施設ID
  shisetsuId: systemCommonsStore.getShisetuId ?? '',
  // 種別ID
  shubetsuId: systemCommonsStore.getSyubetu ?? '',
  // ログイン情報.職員名
  loginUserName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
  // ログイン情報.職員ID
  loginUserId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
  // システム年月日
  systemDate: systemCommonsStore.getSystemDate ?? '',
  // 法人ID
  houjinId: systemCommonsStore.getHoujinId ?? '',
  // 計画書式
  //planFormat: systemCommonsStore.getAuthzOtherCaremanager?.planFormat ?? '',
  planFormat: '1', //TODO
  // 適用事業所グループID
  tekiyouGroupId: systemCommonsStore.getTekiyouGroupId ?? ''
})

/**
 * OR52785 画面用のリアクティブオブジェクト
 */
const or52785Ref = ref({
  /**
   * OR52785初期処理
   *
   * @param _or52785Oneway - OR52785 の初期データ
   */
  initData: (_or52785Oneway: Or52785OnewayType) => {},
})

/**
 * OneWayバインド用のデフォルトローカルデータ
 */
const defaultOneway = reactive({
  // 事業所

  // 期間データ
  or52768Oneway: {
    planTargetPeriodData: { currentIndex: 0, totalCount: 0 },
    kindId: systemCommonsStore.getSyubetu
  } as Or52768OnewayType,
  // 履歴
  or52771Oneway: { createData: {} as PlanCreateDataType } as Or52771OnewayType,
  // 作成者
  or52769Oneway: {
    createData: {} as PlanCreateDataType,
    disableFlg: false,
  } as Or52769OnewayType,
  // 作成日
  or52770Oneway: { disableFlg: false } as Or52770OnewayType,
  // 初回作成日
  or52788Oneway: { disableFlg: false } as Or52788OnewayType,
  // ｹｰｽ番号
  or52772Oneway: { disableFlg: false } as Or52772OnewayType,
  // 認定結果
  or52776Oneway: {} as Or52776OnewayType,
  // マスタ他
  or27299Oneway: {
    svJigyoId: '1',
    shisetuId: '1',
    svJigyoIdList: [],
  } as Or27299OneWayType,
})

/**
 * OneWayバインド用のローカルデータ
 */
const localOneway = reactive({
  or52768Oneway: {
    ...defaultOneway.or52768Oneway,
  } as Or52768OnewayType,
  or52771Oneway: {
    ...defaultOneway.or52771Oneway,
  } as Or52771OnewayType,
  or52769Oneway: {
    ...defaultOneway.or52769Oneway,
  } as Or52769OnewayType,
  or52770Oneway: {
    ...defaultOneway.or52770Oneway,
  } as Or52770OnewayType,
  or52788Oneway: {
    ...defaultOneway.or52788Oneway,
  } as Or52788OnewayType,
  or52772Oneway: {
    ...defaultOneway.or52772Oneway,
  } as Or52772OnewayType,
  or52776Oneway: {
    ...defaultOneway.or52776Oneway,
  } as Or52776OnewayType,
  or27299Oneway: {
    ...defaultOneway.or27299Oneway,
  } as Or27299OneWayType,
  mo00615Twoway: {
    itemLabelFontWeight: 'bold',
    itemLabel: t('label.policy-support-independence'),
    customClass: new CustomClass({ outerClass: 'mr-2', labelClass: 'my-2' }),
  } as Mo00615OnewayType,
  Or00094Twoway: { selectValueArray: ['全'] } as Or00094TwowayType,
  or52785Oneway: {
    comprehensivePlanData: {} as ComprehensivePlanInfoType,
    certificationResultSelectList: [] as Mo01408ItemType[],
    periodManageFlag: '',
    planTargetPeriodId: '',
    cpyFlg: false,
  } as Or52785OnewayType,
  or28382Oneway: {
    periodManageFlag: '1'
  } as Or28382OnewayType,
  // 対象期間
  orX0115Oneway: {
    kindId: systemCommonsStore.getSyubetu ?? '',
    sc1Id: '',
  } as OrX0115OnewayType,
})

/**
 * デファクトコンポネント変数
 */
const defaultComponents = {
  // 事業所
  or41179: {} as Mo00040Type,
  // 期間データ
  or52768: { PlanTargetPeriodUpdateFlg: '' } as Or52768Type,
  // 履歴
  or52771: {
    createId: '',
    createUpateFlg: '',
  } as Or52771Type,
  // 作成者
  or52769: { staffId: '', staffName: '' } as Or52769Type,
  // 作成日
  or52770: { value: '', mo01343: {} as Mo01343Type } as Mo00020Type,
  // 初回作成日
  or52788: { value: '', mo01343: {} as Mo01343Type } as Mo00020Type,
  // ｹｰｽ番号
  or52772: { value: '' } as Or52772Type,
  or52785: {
    comprehensivePlanData: {
      certificationResult: {
        mo01408CertificationResult: {
          value: '',
        },
      },
      validPeriod: {
        mo00020ValidityPeriodStartDate: {
          value: '',
          mo01343: {} as Mo01343Type,
        },
        mo00020ValidityPeriodEndDate: {
          value: '',
          mo01343: {} as Mo01343Type,
        },
      },
      certificationReviewOpinion: {
        value: '',
      },
      policy1: {
        value: '',
      },
      policy2: {
        value: '',
      },
      policy3: {
        value: '',
      },
      comprehensiveSupportPolicy: {
        value: '',
      },
      descriptionYmd: {
        value: '',
        mo01343: {} as Mo01343Type,
      },
      deleteFlg: false
    },
  } as Or52785Type,
}

/**
 * ロカール変数
 */
const local = reactive({
  or52768: {
    planTargetPeriodId: '',
    PlanTargetPeriodUpdateFlg: ''
  } as Or52768Type,
  // 内容入力指定
  inputType: '',
  or52785: {} as Or52785Type,
  // 期間管理フラグ
  kikanFlg: ''
})

/**
 * ロカールコンポネント変数
 */
const localComponents = reactive({
  or41179: {
    ...defaultComponents.or41179,
  } as Mo00040Type,
  or52768: {
    ...defaultComponents.or52768,
  } as Or52768Type,
  or52771: {
    ...defaultComponents.or52771,
  } as Or52771Type,
  or52769: {
    ...defaultComponents.or52769,
  } as Or52769Type,
  or52770: {
    ...defaultComponents.or52770,
  } as Mo00020Type,
  or52788: {
    ...defaultComponents.or52788,
  } as Mo00020Type,
  or52772: {
    ...defaultComponents.or52772,
  } as Or52772Type,
  or52785: {
    ...defaultComponents.or52785,
  } as Or52785Type,
})

/**
 * 表示フラグ
 */
const localFlg = ref({
  // 計画対象期間表示表示フラグ
  showPlanningPeriodFlg: true,
  // 履歴表示フラグ
  showHistoryFlg: true,
  // 作成者表示フラグ
  showAuthorFlg: true,
  // 作成日表示フラグ
  showCreateDateFlg: true,
  // 総合計画入力フォーム表示フラグ
  showComprehensivePlanFormFlg: true,
})

/**
 * 総合計画の初期情報を取得する。
 */
const sougouKakData = ref<ISougouKakSelectOutData>({
  // 履歴ID/総合計画ID
  kkak1Id: '',
  // 期間管理フラグ
  kikanFlg: '',
  // 計画期間情報
  kikanInfo: null,
  // 履歴情報
  rirekiInfo: null,
  // 更新回数
  modifiedCnt: '',
  // 総合計画詳細情報
  comprehensivePlanData: {
    ...defaultComponents.or52785.comprehensivePlanData,
  } as ComprehensivePlanInfoType,
})

/**
 * or11871子コンポーネント用変数
 */
const or11871 = ref({ uniqueCpId: '' })
/**
 * or00248子コンポーネント用変数
 */
const or00248 = ref({ uniqueCpId: '' })
/**
 * or00249子コンポーネント用変数
 */
const or00249 = ref({ uniqueCpId: '' })
/**
 * orHeadLine子コンポーネント用変数
 */
const orHeadLine = ref({ uniqueCpId: '' })
/**
 * or41179_1子コンポーネント用変数
 */
const or41179_1 = ref({ uniqueCpId: '' })
/**
 * or52768_1子コンポーネント用変数
 */
const or52768_1 = ref({ uniqueCpId: '' })
/**
 * or52771_1子コンポーネント用変数
 */
const or52771_1 = ref({ uniqueCpId: '' })
/**
 * or52769_1子コンポーネント用変数
 */
const or52769_1 = ref({ uniqueCpId: '' })
/**
 * or52770_1子コンポーネント用変数
 */
const or52770_1 = ref({ uniqueCpId: '' })
/**
 * or52788_1子コンポーネント用変数
 */
const or52788_1 = ref({ uniqueCpId: '' })
/**
 * or52772_1子コンポーネント用変数
 */
const or52772_1 = ref({ uniqueCpId: '' })
/**
 * or21814_1子コンポーネント用変数
 */
const or21814_1 = ref({ uniqueCpId: '' })
/**
 * or27299_1子コンポーネント用変数
 */
const or27299_1 = ref({ uniqueCpId: '' })
/**
 * or52785_1子コンポーネント用変数
 */
const or52785_1 = ref({ uniqueCpId: '' })
/**
 * orX0115子コンポーネント用変数
 */
const orX0115 = ref({ uniqueCpId: '' })
/**
 * or28382子コンポーネント用変数
 */
const or28382 = ref({ uniqueCpId: '' })

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 初期化フラグ
 */
const isInit = useScreenInitFlg()
onMounted(async () => {
  officeSelect()

  // 利用者を全選択です。
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: Or52786Const.STR_ALL })
  })

  if (isInit) {
    await initOr52786()
  }
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(1)]: or00248.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or41179Const.CP_ID(1)]: or41179_1.value,
  [Or52768Const.CP_ID(1)]: or52768_1.value,
  [Or52771Const.CP_ID(1)]: or52771_1.value,
  [Or52769Const.CP_ID(1)]: or52769_1.value,
  [Or52770Const.CP_ID(1)]: or52770_1.value,
  [Or52788Const.CP_ID(1)]: or52788_1.value,
  [Or52772Const.CP_ID(1)]: or52772_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or27299Const.CP_ID(1)]: or27299_1.value,
  [Or52785Const.CP_ID(1)]: or52785_1.value,
  [Or28382Const.CP_ID(0)]: or28382.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(1)]: or00249.value,
})

useSetupChildProps(or52768_1.value.uniqueCpId, {
  [OrX0115Const.CP_ID(1)]: orX0115.value,
})

Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.comprehensive-plan'),
    showFavorite: true,
    showViewSelect: false,
    showCreateBtn: true,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showOptionMenuBtn: true,
    viewSelectItems: [],
    showMasterBtn: true,
    showOptionMenuDelete: false,
    showSaveBtn: true,
  },
})

/**
 * 変更されたリスニングのコンポーネントIDリスト
 */
const watchedComponents = ref<string[]>([
  props.uniqueCpId,
  or52769_1.value.uniqueCpId,
  or52770_1.value.uniqueCpId,
  or52772_1.value.uniqueCpId,
  or52788_1.value.uniqueCpId,
  or52785_1.value.uniqueCpId,
])

/**
 * 計画対象期間リスト
 */
const kikanInfoList = ref<PlanTargetPeriodDataType[]>([])
/**
 * 計画対象期間Map
 */
const kikanInfoMap = ref<Record<string, PlanTargetPeriodDataType>>({})

/**
 * 総合計画履歴情報
 */
const rirekiInfoList = ref<ISougouKakRirekiSelectOut[]>([])

/**
 * 総合計画履歴情報Map
 */
const rirekiInfoMap = ref<Record<string, ISougouKakRirekiSelectOut>>({})

/**
 * 操作メニュー
 */
const operationMenu = ref<string>('')

/**************************************************
 * Pinia
 **************************************************/
/**
 * リアクティブデータ
 */
const { refValue } = useScreenTwoWayBind<ISougouKakSelectOutData>({
  cpId: Or52785Const.CP_ID(1),
  uniqueCpId: or52785_1.value.uniqueCpId,
})
refValue.value = {
  // 履歴ID/総合計画ID
  kkak1Id: '',
  // 期間管理フラグ
  kikanFlg: '',
  // 計画期間情報
  kikanInfo: null,
  // 履歴情報
  rirekiInfo: null,
  // 更新回数
  modifiedCnt: '',
  // 総合計画詳細情報
  comprehensivePlanData: {
    ...defaultComponents.or52785.comprehensivePlanData,
  } as ComprehensivePlanInfoType,
}

/**
 * 事業所選択更新の監視
 */
watch(
  () => Or41179Logic.data.get(or41179_1.value.uniqueCpId),
  async (newValue, oldValue) => {
    // Undefinedの時戻す
    if (isUndefined(newValue) || !oldValue?.modelValue) {
      return
    }

    const isEdit = isEditNavControl(watchedComponents.value)
    // 画面データ変更かどうかを判断する
    if (isEdit) {
      const rs = await showMessageICmn10430()
      if (rs.firstBtnClickFlg) {
        await _save()
      } else if (rs.thirdBtnClickFlg) {
        // キャンセル選択時
        return
      }
    }
    // 事業所切替時、選択した事業所の事業所IDを設定する

    // 画面を初期化する
    await initOr52786()
  },
  {
    deep: true,
  }
)

/**
 * 計画期間変更の監視
 */
watch(
  () => Or52768Logic.data.get(or52768_1.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }

    const planID = newValue.planTargetPeriodId
    local.or52768.planTargetPeriodId = planID
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg

    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }

    const isEdit = isEditNavControl(watchedComponents.value)
    // 画面データ変更かどうかを判断する
    if (isEdit) {
      const rs = await showMessageICmn10430()
      if (rs.firstBtnClickFlg) {
        await _save()
      } else if (rs.thirdBtnClickFlg) {
        // キャンセル選択時は複写データの作成を行わずに終了する
        return
      }
    }

    //「期間-選択確認前 アイコンボタン」押下
    if (planUpdateFlg === Or52768Const.UPDATE_CATEGORY_SELECT) {
      localOneway.orX0115Oneway.sc1Id = planID
      // 期間選択画面を呼び出す
      OrX0115Logic.state.set({
        uniqueCpId: orX0115.value.uniqueCpId,
        state: { isOpen: true },
      })
    }
    //「期間-前へ アイコンボタン」押下
    else if (planUpdateFlg === Or52768Const.UPDATE_CATEGORY_PREVIOUS) {
      local.or52768.PlanTargetPeriodUpdateFlg = planUpdateFlg

      // 計画期間変更処理を行う
      await handleChangeKikan(local.or52768.planTargetPeriodId, Or52786Const.KIKAN_PAGE_KBN.BEFORE)
    }
    //「期間-次へ アイコンボタン」押下
    else if (planUpdateFlg === Or52768Const.UPDATE_CATEGORY_NEXT) {
      local.or52768.PlanTargetPeriodUpdateFlg = planUpdateFlg

      // 計画期間変更処理を行う
      await handleChangeKikan(local.or52768.planTargetPeriodId, Or52786Const.KIKAN_PAGE_KBN.AFTER)
    }
  }
)

/**
 * 対象期間ダイアログの期間更新の監視
 */
watch(
  () => OrX0115Logic.event.get(orX0115.value.uniqueCpId),
  async (newValue, oldValue) => {
    if(newValue?.kikanId === oldValue?.kikanId) return
    local.or52768.planTargetPeriodId = newValue?.kikanId ?? ''
      // 更新フラグINIT
      Or52768Logic.data.set({
        uniqueCpId: or52768_1.value.uniqueCpId,
        value: {
          planTargetPeriodId: local.or52768.planTargetPeriodId,
          PlanTargetPeriodUpdateFlg: '',
        },
      })

    // 計画期間変更処理を行う
    await handleChangeKikan(local.or52768.planTargetPeriodId, Or52786Const.KIKAN_PAGE_KBN.FIXED)
  }
)

/**
 * 履歴変更の監視
 */
watch(
  () => Or52771Logic.data.get(or52771_1.value.uniqueCpId),
  async (newValue, oldValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    if (isUndefined(newValue)) {
      return
    }
    const createId = newValue.createId
    const createUpateFlg = newValue.createUpateFlg

    if (createUpateFlg === undefined || createUpateFlg === '') {
      return
    }

    //「履歴-選択確認後 アイコンボタン」を押す時は総合計画の履歴変更処理を行って、処理終了
    if (createUpateFlg === Or52771Const.UPDATE_CATEGORY_CERTIFICATION) {
      // 選択前の履歴から変更がない場合、処理を終了する。
      if(newValue.createId === oldValue?.createId) return
      // 更新フラグINIT
      Or52771Logic.data.set({
        uniqueCpId: or52771_1.value.uniqueCpId,
        value: {
          createId: Or52771Logic.data.get(or52771_1.value.uniqueCpId)!.createId,
          createUpateFlg: '',
        },
      })
      // 履歴変更処理を行う
      await handleChangeRireki(createId, Or52786Const.HISTORY_CHANGE_KBN.SELECTED_ID)
      return
    }

    const isEdit = isEditNavControl(watchedComponents.value)
    // 画面データ変更かどうかを判断する
    if (isEdit) {
      const rs = await showMessageICmn10430()
      if (rs.firstBtnClickFlg) {
        await _save()
      } else if (rs.secondBtnClickFlg) {
        // いいえ選択時は編集内容を破棄するので何もしない
      } else if (rs.thirdBtnClickFlg) {
        // キャンセル選択時は複写データの作成を行わずに終了する
        return
      }
    }

    //「履歴-選択確認前 アイコンボタン」押下
    if (createUpateFlg === Or52771Const.UPDATE_CATEGORY_SELECT) {
      // 履歴-選択画面を呼び出す
      // Or28376:履歴選択］画面 総合計画モーダル開閉状態を更新する
      localOneway.or52771Oneway.officeId = commonInfoData.svJigyoId
      localOneway.or52771Oneway.userId = commonInfoData.userId
      localOneway.or52771Oneway.sc1Id = localComponents.or52768.planTargetPeriodId
      Or28376Logic.state.set({
        uniqueCpId: newValue.or28376UniqueCpId!,
        state: { isOpen: true },
      })
    }
    //「履歴-前へ アイコンボタン」押下
    else if (createUpateFlg === Or52771Const.UPDATE_CATEGORY_PREVIOUS) {
      // 履歴変更処理を行う
      // 操作メニュが新規の場合、最新の履歴IDへ移動
      if(operationMenu.value === Or52786Const.OPERATION_MENU.NEW) {
        const rirekiId = rirekiInfoList.value[0].kkak1Id
        await handleChangeRireki(rirekiId, Or52786Const.HISTORY_CHANGE_KBN.SELECTED_ID)
      } else {
        await handleChangeRireki(createId, Or52786Const.HISTORY_CHANGE_KBN.BEFORE_SELECTED_ID)
      }
    }
    //「履歴-次へ アイコンボタン」押下
    else if (createUpateFlg === Or52771Const.UPDATE_CATEGORY_NEXT) {
      // 履歴変更処理を行う
      await handleChangeRireki(createId, Or52786Const.HISTORY_CHANGE_KBN.AFTER_SELECTED_ID)
    }
  },
  { deep: true }
)

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814_1.value.uniqueCpId),
  async (newValue) => {
    await nextTick()
    if (or21814ResolvePromise !== undefined && newValue !== undefined) {
      or21814ResolvePromise(newValue)
    }
    return
  }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue.favoriteEventFlg) {
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { favoriteEventFlg: false },
      })
    }

    // 保存ボタンを押下時
    if (newValue.saveEventFlg) {
      const isEdit = isEditNavControl(watchedComponents.value)
      if (!isEdit) {
        await showMessageICmn21800()
      } else {
        await _save()
        // 操作メニューが新規の場合
        if(operationMenu.value === Or52786Const.OPERATION_MENU.NEW) {
          // 登録した履歴IDで再初期化
          await handleChangeKikan(sougouKakData.value.kikanInfo!.planTargetPeriodId.toString(), Or52786Const.KIKAN_PAGE_KBN.FIXED)
        } else if (operationMenu.value === Or52786Const.OPERATION_MENU.DELETE) {
          // 前の履歴IDで再初期化
          // TODO
        } else {
          // 選択している履歴IDで再初期化
          await handleChangeRireki(sougouKakData.value.rirekiInfo!.kkak1Id, Or52786Const.HISTORY_CHANGE_KBN.SELECTED_ID)
        }
      }
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { saveEventFlg: false },
      })
    }

    // 新規ボタンを押下時
    if (newValue.createEventFlg) {
      await _create()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { createEventFlg: false },
      })
    }

    // 印刷ボタンを押下時
    if (newValue.printEventFlg) {
      await _print()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { printEventFlg: false },
      })
    }

    if (newValue.masterEventFlg) {
      await _master()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { masterEventFlg: false },
      })
    }
  }
)

/**
 * 総合計画モデル値の監視
 */
watch(
  () => local.or52785.comprehensivePlanData,
  async (newVal) => {
    await nextTick()
    if (JSON.stringify(newVal) !== JSON.stringify(refValue.value?.comprehensivePlanData)) {
      refValue.value!.comprehensivePlanData = newVal
    }
  },
  { deep: true, immediate: true }
)

/**
 * 操作メニュー更新の監視
 */
watch(
  () => operationMenu.value,
  (newValue) => {
    // 操作メニューが削除の場合
    if(newValue === Or52786Const.OPERATION_MENU.DELETE) {
      // 作成日、作成日カレンダーアイコン、作成者表示選択アイコンを表示のみにする。
      localOneway.or52769Oneway.disableFlg = true
      localOneway.or52770Oneway.disableFlg = true
      localOneway.or52772Oneway.disableFlg = true
      localOneway.or52788Oneway.disableFlg = true
    } else {
      // 作成日、作成日カレンダーアイコン、作成者表示選択アイコンを活性にする。
      localOneway.or52769Oneway.disableFlg = false
      localOneway.or52770Oneway.disableFlg = false
      localOneway.or52772Oneway.disableFlg = false
      localOneway.or52788Oneway.disableFlg = false
    }
  }
)

/**
 * 新規作成時処理
 */
async function _create() {
  //「削除」押下時に、新規、複写のボタンは実行無効にする
  if (operationMenu.value === Or52786Const.OPERATION_MENU.DELETE) {
    return
  }

  // 期間管理フラグが「1:管理する」、かつ、計画期間情報.期間総件数 = 0（期間なし）
  if (sougouKakData.value?.kikanFlg === Or52786Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT &&
      (sougouKakData.value?.kikanInfo === null || sougouKakData.value?.kikanInfo === undefined)) {
    // i.cmn.11300メッセージを表示
    await showMessageICmn11300()
  } else {
    const isEdit = isEditNavControl(watchedComponents.value)
    // 画面入力データに変更がある場合、メッセージを表示i.cmn.10430
    if (isEdit) {
      const rs = await showMessageICmn10430()
      if (rs.firstBtnClickFlg) {
        // はいの場合、保存処理
        await _save()
        // 新規空白画面を作成します
        await createEmptyScreen()
      } else if (rs.secondBtnClickFlg) {
        // 新規空白画面を作成します
        await createEmptyScreen()
      } else if (rs.thirdBtnClickFlg) {
        // キャンセル選択時は複写データの作成を行わずに終了する
        return
      }
    } else {
      // 新規作成された総合計画が未登録の状態でボタン押下、メッセージを表示i.cmn.11265
      if (operationMenu.value === Or52786Const.OPERATION_MENU.NEW) {
        await showMessageICmn11265()
      } else {
        await createEmptyScreen()
      }
    }
  }
}

/**
 * 事業所選択初期化処理
 */
function officeSelect() {
  // 事業所選択プルダウンの検索条件設定
  // 検索条件未指定だと、ヘッダーで選択した適用事業所が表示される
  // 検索条件が変更されると、共通処理にて事業所の情報が更新される
  // 更新後やプルダウンの値変更後、変更後の事業所に基づいて処理を行う場合、
  // 関数「 jigyoListWatch 」にコールバック関数を渡す
  if (systemCommonsStore.getUserSelectSelfId()) {
    Or41179Logic.state.set({
      uniqueCpId: or41179_1.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }
}

/**
 * 新しいプランフォームデータの設定
 */
async function createEmptyScreen() {
  // 新規表示情報取得APIを呼び出して新規表示情報を取得する。
  const inParam: SougouKakNewCreateSelectInEntity = {
    svJigyoId: commonInfoData.svJigyoId,
    userId: commonInfoData.userId
  }

  const ret: SougouKakNewCreateSelectOutEntity = await ScreenRepository.select(
    'sougouKakNewCreateSelect',
    inParam
  )

  const retData = ret.data
  // 作成者に共通情報.ログイン担当者をセットする
  localComponents.or52769 = {
    staffName: commonInfoData.loginUserName,
    staffId: commonInfoData.loginUserId,
  }
  localOneway.or52769Oneway.createData = {
    staffName: commonInfoData.loginUserName,
    staffId: Number(commonInfoData.loginUserId),
    createId: null,
    createDate: '',
    currentIndex: 0,
    totalCount: 0,
    ks21Id: '',
  }

  // 履歴ページング ← 総合計画履歴情報の件数+1/総合計画履歴情報の件数+1
  localOneway.or52771Oneway.createData.currentIndex = Number(sougouKakData.value.rirekiInfo?.totalCount) + 1
  localOneway.or52771Oneway.createData.totalCount = Number(sougouKakData.value.rirekiInfo?.totalCount) + 1
  localOneway.or52771Oneway.createData.createId = 0
  // 履歴情報設定
  localComponents.or52771.createId = ''
  localComponents.or52771.createUpateFlg = ''

  // Or52772 ケース番号に空をセットする
  localComponents.or52772.value = ''

  // 認定有効期間（開始）に新規表示情報.台帳認定有効開始日（表示用）をセットする
  localComponents.or52785.comprehensivePlanData.validPeriod.mo00020ValidityPeriodStartDate.value = retData.yukoKaishibiDisp
  // 認定有効期間（終了）に新規表示情報.台帳認定有効終了日（表示用）をセットする
  localComponents.or52785.comprehensivePlanData.validPeriod.mo00020ValidityPeriodEndDate.value = retData.yukoSyuryobiDisp
  // 実施機関の意向や判断などに関する事項に空をセットする
  localComponents.or52785.comprehensivePlanData.certificationReviewOpinion.value = ''
  // 入所者の希望・意向など勘案すべき事項に空をセットする
  localComponents.or52785.comprehensivePlanData.policy1.value = ''
  // 実施機関の意向や判断などに関する事項に空をセットする
  localComponents.or52785.comprehensivePlanData.policy2.value = ''
  // 援助に関する事項に空をセットする
  localComponents.or52785.comprehensivePlanData.policy3.value = ''
  // 総合的な援助の方針に空をセットする
  localComponents.or52785.comprehensivePlanData.comprehensiveSupportPolicy.value = ''
  // 説明年月日に空をセットする
  localComponents.or52785.comprehensivePlanData.descriptionYmd.value = ''

  // 新規表示情報.要介護状態区分が1:調査票の場合
  if(retData.yoKaifoJyotaiKbn === '1') {
    // 認定結果に新規表示情報.認定調査票要介護状態区分をセットする
    localComponents.or52785.comprehensivePlanData.certificationResult.mo01408CertificationResult.value = retData.ninteiCyousahyoYoKaigoJyotaiKbn
  } else if (retData.yoKaifoJyotaiKbn === '2') {
    // 認定結果に新規表示情報.台帳要介護状態区分をセットする
    localComponents.or52785.comprehensivePlanData.certificationResult.mo01408CertificationResult.value = retData.daicyoYoKaigoJyotaiKbn
  }

  // 新規表示情報.初回作成日がNullでない場合
  if(retData.syokaiYmd !== null) {
    // 初回作成日に新規表示情報.初回作成日をセットする
    localComponents.or52788.value = retData.syokaiYmd
  }

  // 新規表示情報.作成日がNullでない場合
  if(retData.createYmd !== null) {
    // 初回作成日に新規表示情報.作成日をセットする
    localComponents.or52770.value = retData.createYmd
  }

  // 上記条件以外の場合(新規表示情報.初回作成日と新規表示情報.作成日がNull)
  if(retData.syokaiYmd === null && retData.createYmd === null) {
    localComponents.or52770.value = commonInfoData.systemDate
  }

  localComponents.or52785.comprehensivePlanData.deleteFlg = false

  sougouKakData.value.comprehensivePlanData = localComponents.or52785.comprehensivePlanData
  sougouKakData.value.rirekiInfo!.kkak1Id = ''
  sougouKakData.value.kkak1Id = ''

  setChildCpBinds(props.uniqueCpId, {
    // 作成日
    [Or52770Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.or52770.value,
        mo01343: {},
      } as Mo00020Type,
    },
    // 作成者
    [Or52769Const.CP_ID(1)]: {
      twoWayValue: localComponents.or52769,
    },
    // 履歴
    [Or52771Const.CP_ID(1)]: {
      twoWayValue: localComponents.or52771,
    },
    // 初回作成日
    [Or52788Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.or52788.value,
        mo01343: {},
      } as Mo00020Type,
    },
    // ｹｰｽ番号
    [Or52772Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.or52772.value
      } as Or52772Type,
    },
  })

  refValue.value = JSON.parse(
    JSON.stringify(sougouKakData.value)
  ) as ISougouKakSelectOutData
  setChildCpBinds(props.uniqueCpId, {
    [Or52785Const.CP_ID(1)]: {
      twoWayValue: JSON.parse(JSON.stringify(sougouKakData.value)),
    },
  })

  // Or52785 総合計画Onewayデータ設定
  localOneway.or52785Oneway.comprehensivePlanData = JSON.parse(
    JSON.stringify(sougouKakData.value.comprehensivePlanData)
  ) as ComprehensivePlanInfoType
  or52785Ref.value.initData(localOneway.or52785Oneway)

  // 操作メニューに1:新規を設定する
  operationMenu.value = Or52786Const.OPERATION_MENU.NEW
}

/**
 * 計画期間変更処理を行う
 *
 * @param kikanId - 期間ID
 *
 * @param pageKbn - 計画期間ページ区分
 */
async function handleChangeKikan(kikanId: string, pageKbn: string) {
  // 期間IDと計画期間ページ区分により、使用済み期間IDを計算する
  let usedKikanId = kikanId
  // 前の計画期間アイコン押の場合
  if(pageKbn === Or52786Const.KIKAN_PAGE_KBN.BEFORE) {
    usedKikanId = kikanInfoList.value[kikanInfoList.value.findIndex(x => x.planTargetPeriodId === Number(kikanId)) + 1].planTargetPeriodId.toString()
  }
  // 次の計画期間アイコン押下の場合
  else if(pageKbn === Or52786Const.KIKAN_PAGE_KBN.AFTER) {
    usedKikanId = kikanInfoList.value[kikanInfoList.value.findIndex(x => x.planTargetPeriodId === Number(kikanId)) - 1].planTargetPeriodId.toString()
  }

  sougouKakData.value.kikanInfo = kikanInfoMap.value[usedKikanId]

  const param: SougouKakKeikakuKikanChangeInfoSelectInEntity = {
    svJigyoId: commonInfoData.svJigyoId,
    userId: commonInfoData.userId,
    shisetuId: commonInfoData.shisetsuId,
    syubetsuId: commonInfoData.shubetsuId,
    sc1Id: usedKikanId,
    kikanFlag: Or52786Const.KIKAN_PAGE_KBN.FIXED
  }
  // 計画期間変条件を取得する
  const ret: SougouKakKeikakuKikanChangeInfoSelectOutEntity = await ScreenRepository.select(
    'sougouKakKeikakuKikanChangeInfoSelect',
    param
  )

  // 画面再表示する
  await setScreenData(ret as IComprehensivePlanOutEntity, true)
}

/**
 * 履歴変更処理を行う
 *
 * @param rirekiId - 履歴ID
 *
 * @param pageFlag - 履歴ページ区分
 */
async function handleChangeRireki(rirekiId: string, pageFlag: string) {
  // 履歴IDIDと履歴ページ区分により、使用済み履歴IDを計算する
  let usedRirekiId = rirekiId
  // 前の計画期間アイコン押の場合
  if(pageFlag === Or52786Const.HISTORY_CHANGE_KBN.BEFORE_SELECTED_ID) {
    usedRirekiId = rirekiInfoList.value[rirekiInfoList.value.findIndex(x => x.kkak1Id === rirekiId) + 1].kkak1Id
  }
  // 次の計画期間アイコン押下の場合
  else if(pageFlag === Or52786Const.HISTORY_CHANGE_KBN.AFTER_SELECTED_ID) {
    usedRirekiId = rirekiInfoList.value[rirekiInfoList.value.findIndex(x => x.kkak1Id === rirekiId) - 1].kkak1Id
  }
  sougouKakData.value.rirekiInfo = {...rirekiInfoMap.value[usedRirekiId]}

  const param: SougouKakRirekiChangeInfoSelectInEntity = {
    sc1Id: sougouKakData.value.kikanFlg === Or52786Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT ?
      localComponents.or52768.planTargetPeriodId : '0',
    kkak1Id: usedRirekiId,
    svJigyoId: commonInfoData.svJigyoId,
    userId: commonInfoData.userId,
    rirekiFlag: Or52786Const.HISTORY_CHANGE_KBN.SELECTED_ID,
  }

  // 履歴IDを取得する
  const ret: SougouKakRirekiChangeInfoSelectOutEntity = await ScreenRepository.select(
    'sougouKakRirekiChangeInfoSelect',
    param
  )

  // 選択した履歴IDによって、再取得した総合計画情報に設定する
  // 画面.履歴ID = 返却情報.履歴ID
  setRirekiData(ret)

  setChildCpBinds(props.uniqueCpId, {
    // 作成日
    [Or52770Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.or52770.value,
        mo01343: {},
      } as Mo00020Type,
    },
    // 作成者
    [Or52769Const.CP_ID(1)]: {
      twoWayValue: localComponents.or52769,
    },
    // 履歴
    [Or52771Const.CP_ID(1)]: {
      twoWayValue: localComponents.or52771,
    },
    // 初回作成日
    [Or52788Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.or52788.value,
        mo01343: {},
      } as Mo00020Type,
    },
    // ｹｰｽ番号
    [Or52772Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.or52772.value
      } as Or52772Type,
    },
  })

  refValue.value = JSON.parse(
    JSON.stringify(sougouKakData.value)
  ) as ISougouKakSelectOutData
  setChildCpBinds(props.uniqueCpId, {
    [Or52785Const.CP_ID(1)]: {
      twoWayValue: JSON.parse(JSON.stringify(sougouKakData.value)),
    },
  })

  localOneway.or52785Oneway.comprehensivePlanData = refValue.value?.comprehensivePlanData ?? ({} as ComprehensivePlanInfoType)
  or52785Ref.value?.initData(localOneway.or52785Oneway)
}

/**
 * 保存時処理
 */
async function _save() {
  // 保存時処理のパラメータを設定する
  const inputData: IComprehensivePlanUpdateInEntity = {
    sokInsertInfo: [
      {
        operationMenu: operationMenu.value,
        sok1Id: sougouKakData.value.kkak1Id,
        sc1Id: sougouKakData.value.kikanInfo!.planTargetPeriodId.toString(),
        houjinId: commonInfoData.houjinId,
        shisetuId: commonInfoData.shisetsuId,
        svJigyoId: commonInfoData.svJigyoId,
        userid: commonInfoData.userId,
        createYmd: Or52770Logic.data.get(or52770_1.value.uniqueCpId)?.value ?? '',
        shokuId: Or52769Logic.data.get(or52769_1.value.uniqueCpId)?.staffId ?? '',
        syokaiYmd: Or52788Logic.data.get(or52788_1.value.uniqueCpId)?.value ?? '',
        caseNo: Or52772Logic.data.get(or52772_1.value.uniqueCpId)?.value ?? '',
        yokaiKbn: local.or52785.comprehensivePlanData.certificationResult.mo01408CertificationResult.value as string,
        ninStartYmd: local.or52785.comprehensivePlanData.validPeriod.mo00020ValidityPeriodStartDate.value,
        ninEndYmd: local.or52785.comprehensivePlanData.validPeriod.mo00020ValidityPeriodEndDate.value,
        hoshin1Knj: local.or52785.comprehensivePlanData.policy1.value ?? '',
        hoshin2Knj: local.or52785.comprehensivePlanData.policy2.value ?? '',
        hoshin3Knj: local.or52785.comprehensivePlanData.policy3.value ?? '',
        hoshin5Knj: local.or52785.comprehensivePlanData.certificationReviewOpinion.value ?? '',
        sogoHoshinKnj: local.or52785.comprehensivePlanData.comprehensiveSupportPolicy.value ?? '',
        setumeiYmd: local.or52785.comprehensivePlanData.descriptionYmd.value ?? '',
        kaiteiFlg: commonInfoData.planFormat,
        modifiedCnt: sougouKakData.value.modifiedCnt
      }
    ]
  }

  await ScreenRepository.update(
    'R4sTucSok1InfoUpdate',
    inputData
  )
}

/**
 * その他の機能画面をポップアップで起動する
 */
async function _master() {
  // TODO 未作成
  // GUI00039_その他の機能画面をポップアップで起動する

  const isEdit = isEditNavControl(watchedComponents.value)
  // 画面データ変更かどうかを判断する
  if (isEdit) {
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      // 保存処理を行い、後続処理へ進む。
      await _save()
    } else if (rs.thirdBtnClickFlg) {
      // キャンセル選択時は複写データの作成を行わずに終了する
      return
    }
  }

  // GUI00970_総合計画マスタをポップアップで起動する。
  showMasterDialog()
}

/**
 * マスタ他のダイアログを表示
 */
function showMasterDialog() {
  localOneway.or27299Oneway.shisetuId = commonInfoData.shisetsuId
  localOneway.or27299Oneway.svJigyoId = commonInfoData.svJigyoId
  // Or10578のダイアログ開閉状態を更新する
  Or27299Logic.state.set({
    uniqueCpId: or27299_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 削除処理
 */
async function _delete() {
  // 計画対象期間≠NULLの場合
  if (sougouKakData.value.kikanInfo !== null) {
    // 以下のメッセージを表示: i.cmn.11326
    const rs = await openConfirmDialog(or21814_1.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11326', [
        Or52770Logic.data.get(or52770_1.value.uniqueCpId)?.value ?? '',
        t('label.comprehensive-plan')
      ]),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンタイプ
      secondBtnType: 'normal3',
      // 第2ボタンラベル
      secondBtnLabel: t('btn.no'),
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    })
    if (rs.firstBtnClickFlg) {
      // (総合計画)メインセクションを非表示にする。
      // 操作メニューに2:削除をセットする
      operationMenu.value = Or52786Const.OPERATION_MENU.DELETE

      // 総合計画入力フォームを非表示
      localFlg.value.showComprehensivePlanFormFlg = false

      refValue.value!.comprehensivePlanData.deleteFlg = true
    }
  }
}

/**
 * 印刷設定
 */
async function _print() {
  const isEdit = isEditNavControl(watchedComponents.value)
  // 画面データ変更かどうかを判断する
  if (isEdit) {
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      // 保存処理を行い、後続処理へ進む。
      await _save()
    } else if (rs.thirdBtnClickFlg) {
      // キャンセル選択時は複写データの作成を行わずに終了する
      return
    }
  }

  // TODO 画面未作成
  //［GUI00976_［印刷設定］画面］を表示する。アセスメント表を印刷できる。
}

/**
 * 複写画面を表示する
 */
function _copy() {
  // [No.GUI00974_総合計画複写]画面を表示する。登録済みのアセスメントの内容を現在表示している画面に複写できる。
  localOneway.or28382Oneway.periodManageFlag = local.kikanFlg

  // Or28382のダイアログ開閉状態を更新する
  Or28382Logic.state.set({
    uniqueCpId: or28382.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ナビゲーション制御領域のいずれかの編集フラグがON
// const isEdit = computed(() => {
//   return useScreenStore().isEditNavControl()
// })

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 初期化処理
 */
const initOr52786 = async () => {
  // 初期化データを取得する
  const [sougouKakRet, sougouKakYoKaigodoListRet, sougouKakaTekiyoJigyosyoListInfoRet] = await Promise.all([
    getSougouKak(),
    getSougouKakYoKaigodoList(),
    getSougouKakaTekiyoJigyosyoListInfo()
  ])

  // 要介護度リスト
  const sougouKakYoKaigodoList = sougouKakYoKaigodoListRet.data
  // 認定結果選択一覧設定
  localOneway.or52785Oneway.certificationResultSelectList = sougouKakYoKaigodoList.yoKaigodoList.map(x => ({
    value: x.yokaiKbn,
    title: x.yokaiKnj
  } as Mo01408ItemType))
  // TODO 適用事業所一覧情報
  const sougouKakaTekiyoJigyosyoListInfo = sougouKakaTekiyoJigyosyoListInfoRet.data

  // 画面初期化のComponents表示フラグ設定、画面初期化のデータ設定
  await setScreenData(sougouKakRet)
}

/**
 * 画面表示のデータを取得
 *
 * @returns - レスポンス
 */
const getSougouKak = async (): Promise<IComprehensivePlanOutEntity> => {
  const inputParam: IComprehensivePlanInEntity = {
    shisetuId: commonInfoData.shisetsuId,
    userId: commonInfoData.userId,
    svJigyoId: commonInfoData.svJigyoId,
    syubetsuId:commonInfoData.shubetsuId,
  }

  const ret: IComprehensivePlanOutEntity = await ScreenRepository.select(
    'sougouKakSelect',
    inputParam
  )

  return ret
}

/**
 * 要介護度リストの初期化を取得する
 *
 * @returns - レスポンス
 */
const getSougouKakYoKaigodoList = async (): Promise<SougouKakYoKaigodoListSelectOutEntity> => {
  const ret: SougouKakYoKaigodoListSelectOutEntity = await ScreenRepository.select(
    'sougouKakYoKaigodoListSelect',
    {}
  )

  return ret
}

/**
 * 適用事業所一覧の初期化を取得する
 *
 * @returns - レスポンス
 */
const getSougouKakaTekiyoJigyosyoListInfo = async (): Promise<SougouKakaTekiyoJigyosyoListInfoSelectOutEntity> => {
  const inParam: SougouKakaTekiyoJigyosyoListInfoSelectInEntity = {
    tekiyoGroupId: commonInfoData.tekiyouGroupId
  }

  const ret: SougouKakaTekiyoJigyosyoListInfoSelectOutEntity = await ScreenRepository.select(
    'sougouKakaTekiyoJigyosyoListInfoSelect',
    inParam
  )

  return ret
}

/**
 * 画面コントロール表示設定
 *
 * @param ret - 初期情報 | 計画期間変更情報
 *
 * @param reInit - 再初期化フラグ
 */
async function setScreenData(ret: IComprehensivePlanOutEntity, reInit = false) {
  const sougouKakRet = ret.data
  // 初期化の場合
  if(!reInit) {
    // 期間管理フラグ設定
    sougouKakData.value.kikanFlg = ret.data.kikanKanriFlg
    // 計画対象期間: 期間管理フラグが「0:管理しない」の場合、非表示
    localFlg.value.showPlanningPeriodFlg = sougouKakData.value.kikanFlg !== Or52786Const.PERIOD_MANAGEMENT_FLG.NO_MANAGEMENT

    if(sougouKakRet.kakTaisyoKikanList?.length){
      let currentIndex = sougouKakRet.kakTaisyoKikanList.length;
      kikanInfoList.value = sougouKakRet.kakTaisyoKikanList
      .map((x) => {
        const result: PlanTargetPeriodDataType = {
          currentIndex: currentIndex,
          planTargetPeriodId: Number(x.sc1Id),
          planTargetPeriod: x.startYmd + SPACE_WAVE + x.endYmd,
          totalCount: sougouKakRet.kakTaisyoKikanList.length,
        };
        currentIndex--;
        return result;
      })

      kikanInfoMap.value = kikanInfoList.value.reduce((acc, item) => {
        acc[item.planTargetPeriodId.toString()] = item
        return acc
      }, {} as Record<string, PlanTargetPeriodDataType>);

      // 計画期間情報を設定する
      sougouKakData.value.kikanInfo = kikanInfoList.value[0]
    } else {
      sougouKakData.value.kikanInfo = null
    }
  }
  // 画面で計画対象期間セクション初期化
  setKikanScreen()

  if(sougouKakRet.sougouKakRirekiInfo.length) {
    let currentIndex = sougouKakRet.sougouKakRirekiInfo.length;
    rirekiInfoList.value = sougouKakRet.sougouKakRirekiInfo
    .map((x) => {
      const result: ISougouKakRirekiSelectOut = {
        ...x,
        currentIndex: currentIndex,
        totalCount: sougouKakRet.sougouKakRirekiInfo.length
      };
      currentIndex--;
      return result;
    })

    rirekiInfoMap.value = rirekiInfoList.value.reduce((acc, item) => {
      acc[item.kkak1Id] = item
      return acc
    }, {} as Record<string, ISougouKakRirekiSelectOut>);

    // 履歴情報を設定する
    sougouKakData.value.rirekiInfo = {...rirekiInfoList.value[0]}
  } else {
    // 履歴情報を設定する
    sougouKakData.value.rirekiInfo = null
  }

  // 画面で履歴セクション初期化
  setRirekiData(ret)

  await nextTick()

  setChildCpBinds(props.uniqueCpId, {
    // 作成日
    [Or52770Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.or52770.value,
        mo01343: {},
      } as Mo00020Type,
    },
    [Or52769Const.CP_ID(1)]: {
      twoWayValue: localComponents.or52769,
    },
    [Or52768Const.CP_ID(1)]: {
      twoWayValue: localComponents.or52768,
    },
    [Or52771Const.CP_ID(1)]: {
      twoWayValue: localComponents.or52771,
    },
    [Or52788Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.or52788.value,
        mo01343: {},
      } as Mo00020Type,
    },
    [Or52772Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.or52772.value
      } as Or52772Type,
    },
  })

  refValue.value = JSON.parse(
    JSON.stringify(sougouKakData.value)
  ) as ISougouKakSelectOutData
  setChildCpBinds(props.uniqueCpId, {
    [Or52785Const.CP_ID(1)]: {
      twoWayValue: JSON.parse(JSON.stringify(sougouKakData.value)),
    },
  })

  localOneway.or52785Oneway.comprehensivePlanData = refValue.value?.comprehensivePlanData ?? ({} as ComprehensivePlanInfoType)
  or52785Ref.value?.initData(localOneway.or52785Oneway)
}

/**
 * 画面で計画対象期間セクション初期化
 */
const setKikanScreen = () => {
  // 計画期間が登録されている場合、計画期間情報.開始日 + " ～ " + 計画期間情報.終了日
  if (
    sougouKakData.value.kikanInfo !== null &&
    sougouKakData.value.kikanInfo !== undefined
  ) {
    localOneway.or52768Oneway.planTargetPeriodData = sougouKakData.value.kikanInfo
    localComponents.or52768.planTargetPeriodId = sougouKakData.value.kikanInfo.planTargetPeriodId.toString()
    localComponents.or52768.PlanTargetPeriodUpdateFlg = ''
  } else {
    localOneway.or52768Oneway.planTargetPeriodData = {
      // 計画対象期間
      planTargetPeriod: '',
      // 計画対象期間期間ID
      planTargetPeriodId: 0,
      // 表示中の番号
      currentIndex: 0,
      // 登録数
      totalCount: 0,
    }
    localComponents.or52768.planTargetPeriodId = ''
    localComponents.or52768.PlanTargetPeriodUpdateFlg = ''
  }

  // 期間管理フラグが「1:管理する」、かつ、計画期間が登録されていない場合
  if (
    sougouKakData.value.kikanFlg === Or52786Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT &&
    !localOneway.or52768Oneway.planTargetPeriodData.planTargetPeriodId
  ) {
    // 履歴非表示
    localFlg.value.showHistoryFlg = false
    // 作成者非表示
    localFlg.value.showAuthorFlg = false
    // 作成日非表示
    localFlg.value.showCreateDateFlg = false
    // チェックシシート入力フォーム非表示
    localFlg.value.showComprehensivePlanFormFlg = false
  }
}

/**
 * 画面で履歴セクション初期化
 *
 * @param ret - レスポンス
 */
const setRirekiData = (ret: IComprehensivePlanOutEntity | SougouKakRirekiChangeInfoSelectOutEntity) => {
  // 総合計画入力フォームを非表示
  localFlg.value.showComprehensivePlanFormFlg = true
  // 操作メニューに更新を設定する
  operationMenu.value = Or52786Const.OPERATION_MENU.UPDATE

  // 履歴情報が存在する場合
  if (
    sougouKakData.value.rirekiInfo !== null &&
    sougouKakData.value.rirekiInfo !== undefined
  ) {
    // 履歴ID設定
    sougouKakData.value.kkak1Id = sougouKakData.value.rirekiInfo.kkak1Id
  } else {
    // 操作メニューに新規を設定する
    operationMenu.value = Or52786Const.OPERATION_MENU.NEW
    sougouKakData.value.kkak1Id = '0'
  }

  if(ret.data.sougouKakSyousaiInfo.length) {
    const sougouKakSyousaiInfo = ret.data.sougouKakSyousaiInfo[0]
    // 更新回数設定
    //sougouKakData.value.modifiedCnt = sougouKakSyousaiInfo.modifiedCnt // TODO

    setcomprehensivePlanData(sougouKakSyousaiInfo)
  } else {
    // 履歴ID/総合計画ID
    sougouKakData.value.kkak1Id = ''
    // 更新回数設定
    sougouKakData.value.modifiedCnt = ''

    setcomprehensivePlanData(null)
  }

  // 履歴
  // 履歴情報が存在する場合
  if(sougouKakData.value.rirekiInfo !== null) {
    localOneway.or52771Oneway.createData.currentIndex = Number(
      sougouKakData.value.rirekiInfo.currentIndex
    )
    localOneway.or52771Oneway.createData.totalCount = Number(
      sougouKakData.value.rirekiInfo.totalCount
    )
    localOneway.or52771Oneway.createData.createId = Number(
      sougouKakData.value.rirekiInfo.kkak1Id
    )
    localOneway.or52771Oneway.createData.staffId = Number(
      sougouKakData.value.rirekiInfo.shokuId
    )

    // 履歴ID
    localComponents.or52771.createId = sougouKakData.value.rirekiInfo.kkak1Id

    // 計画書_履歴情報.職員番号の職員名
    localOneway.or52769Oneway.createData = {
      staffName: sougouKakData.value.rirekiInfo.shokuKnj,
      staffId: Number(sougouKakData.value.rirekiInfo.shokuId),
      createId: null,
      createDate: '',
      currentIndex: 0,
      totalCount: 0,
      ks21Id: '',
    }

    // or52770 計画書_履歴リストデータが存在の場合、計画書_履歴リスト1件目.作成日
    localComponents.or52770.value = sougouKakData.value.rirekiInfo.createYmd

    // or52788 初回作成日を初期表示する
    localComponents.or52788.value = sougouKakData.value.rirekiInfo.syokaiYmd

    // or52772 ｹｰｽ番号を初期表示する
    localComponents.or52772.value = sougouKakData.value.rirekiInfo.caseNo
  } else {
    localOneway.or52771Oneway.createData.currentIndex = 1
    localOneway.or52771Oneway.createData.totalCount = 1
    localOneway.or52771Oneway.createData.createId = 0
    localOneway.or52771Oneway.createData.staffId = 0

    // 履歴ID
    localComponents.or52771.createId = ''

    // 作成者名: 初期表示で履歴データが存在しない場合、ログイン情報.職員名
    localOneway.or52769Oneway.createData = {
      staffName: commonInfoData.loginUserName,
      staffId: 0,
      createId: null,
      createDate: '',
      currentIndex: 0,
      totalCount: 0,
      ks21Id: '',
    }

    // or52770 作成日: 初期表示で履歴データが存在しない場合、共通情報.作成日
    localComponents.or52770.value = systemCommonsStore.getSystemDate ?? ''

    // or52788 初回作成日を初期表示する
    localComponents.or52788.value = ''

    // or52772 ｹｰｽ番号を初期表示する
    localComponents.or52772.value = ''
  }
}

/**
 * 総合計画詳細情報設定
 *
 * @param syousaiInfo - 総合計画詳細情報
 */
const setcomprehensivePlanData = (syousaiInfo: ISougouKakSyousai | null) => {
  const comprehensivePlanInfo = {...defaultComponents.or52785.comprehensivePlanData}
  // or52776 認定結果を初期表示する
  comprehensivePlanInfo.certificationResult.mo01408CertificationResult.value = syousaiInfo?.yokaiKbn ?? ''

  // or52777 有効期間情報を初期表示する
  comprehensivePlanInfo.validPeriod.mo00020ValidityPeriodStartDate.value = syousaiInfo?.ninStartYmd ?? ''
  comprehensivePlanInfo.validPeriod.mo00020ValidityPeriodEndDate.value = syousaiInfo?.ninEndYmd ?? ''

  // Or52778 総合計画詳細情報.介護保険サービスに留意すべき事項
  comprehensivePlanInfo.certificationReviewOpinion.value = syousaiInfo?.hoshin5Knj ?? ''
  // 総合計画詳細情報.社会活動復帰に関する事項が空白でない場合、上記末尾に改行追加後にデータを追加する
  if(syousaiInfo?.hoshin4Knj) {
    comprehensivePlanInfo.certificationReviewOpinion.value =
      comprehensivePlanInfo.certificationReviewOpinion.value + '\n' + (syousaiInfo?.hoshin4Knj ?? '')
  }

  // Or52779 総合計画詳細情報.入所者の希望意向など勘案すべき事項
  comprehensivePlanInfo.policy1.value = syousaiInfo?.hoshin1Knj ?? ''
  // 総合計画詳細情報.達成のための意欲等が空白でない場合、上記末尾に改行追加後にデータを追加する
  if(syousaiInfo?.iyokuKnj) {
    comprehensivePlanInfo.policy1.value = comprehensivePlanInfo.policy1.value + '\n' + (syousaiInfo?.iyokuKnj ?? '')
  }

  // Or52780 総合計画詳細情報.実施機関の意向や判断などに関する事項
  comprehensivePlanInfo.policy2.value = syousaiInfo?.hoshin2Knj ?? ''

  // Or52781 総合計画詳細情報.援助に関する事項
  comprehensivePlanInfo.policy3.value = syousaiInfo?.hoshin3Knj ?? ''

  // Or52782 総合計画詳細情報.総合的な援助の方針
  comprehensivePlanInfo.comprehensiveSupportPolicy.value = syousaiInfo?.sogoHoshinKnj ?? ''

  // Or52775 総合計画詳細情報.説明年月日
  comprehensivePlanInfo.descriptionYmd.value = syousaiInfo?.setumeiYmd ?? ''

  // 総合計画詳細情報設定
  sougouKakData.value.comprehensivePlanData = comprehensivePlanInfo

  // 更新回数設定
  sougouKakData.value.modifiedCnt = syousaiInfo!.modifiedCnt ?? ''
}

/**
 * 複写ボタン押下時
 */
const copyBtnClick = () => {
  _copy()
}

/**
 * 削除ボタン押下時
 */
const deleteBtnClick = async () => {
  await _delete()
}

/**
 * 確認メッセージダイアログ開く(i-cmn-10430)
 *
 * @returns メッセージPromise
 */
async function showMessageICmn10430() {
  const rs = await openConfirmDialog(or21814_1.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンタイプ
    secondBtnType: 'destroy1',
    // 第2ボタンラベル
    secondBtnLabel: t('btn.no'),
    // 第3ボタンタイプ
    thirdBtnType: 'normal3',
    // 第3ボタンラベル
    thirdBtnLabel: t('btn.cancel'),
  })
  return rs
}

/**
 * 確認メッセージダイアログ開く(i-cmn-11300)
 *
 * @returns メッセージPromise
 */
async function showMessageICmn21800() {
  const rs = await openConfirmDialog(or21814_1.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-21800'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
  })
  return rs
}

/**
 * 確認メッセージダイアログ開く(i-cmn-11300)
 *
 * @returns メッセージPromise
 */
async function showMessageICmn11300() {
  const rs = await openConfirmDialog(or21814_1.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11300'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
  })
  return rs
}

/**
 * 確認メッセージダイアログ開く(i-cmn-11265)
 */
async function showMessageICmn11265() {
  await openConfirmDialog(or21814_1.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11265', [t('label.interest-checksheet')]),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンボタンタイプ
    secondBtnType: 'blank',
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  } as Or21814OnewayType)
}

/**
 * 確認ダイアログ開く
 *
 * @param uniqueCpId - uniqueCpId
 *
 * @param state - 確認ダイアログOneWayBind領域
 */
function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    or21814ResolvePromise = resolve
  })
}

/**
 * 複写画面の確定ボタンを押下時の処理
 *
 * @param result - 返却データ
 */
const handleCopyConfirm = (result: HistType[]) => {
  // TODO
}

/**
 * いずれかの編集フラグがONになっているかチェックする
 *
 * @param components - 変更のリスニングが必要なリスト
 *
 * @returns いずれかの編集有無
 */
function isEditNavControl(components: string[]) {
  const screenStore = useScreenStore()
  const screenNavControl = screenStore.getScreenNavControl<NavControlJsonType>()

  // ナビゲーション制御領域を走査
  for (const key in screenNavControl) {
    if (screenNavControl[key] === screenNavControl.components) {
      // ナビゲーション制御領域 - コンポーネント毎の領域を走査
      for (const cpKey in screenNavControl[key]) {
        if (components.includes(cpKey)) {
          if (screenNavControl[key][cpKey].editFlg) {
            // コンポーネント毎の編集フラグがON
            return true
          }
          if (screenNavControl[key][cpKey].items) {
            // ナビゲーション制御領域 - コンポーネント毎の領域にitemsが存在する場合は走査
            for (const itemKey in screenNavControl[key][cpKey].items) {
              if (screenNavControl[key][cpKey].items[itemKey].editFlg) {
                // コンポーネント - itemsの編集フラグがON
                return true
              }
            }
          }
        }
      }
    }
  }

  return false
}

/**
 * 総合計画マスタダイアログ表示フラグ
 */
const showDialogOr27299 = computed(() => {
  // Or27299のダイアログ開閉状態
  return Or27299Logic.state.get(or27299_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr28382 = computed(() => {
  // Or28382のダイアログ開閉状態
  return Or28382Logic.state.get(or28382.value.uniqueCpId)?.isOpen ?? false
})

// OrX0115 ダイアログ表示フラグ
const showDialogOrX0115 = computed(() => {
  // OrX0115 cks_flg=1 のダイアログ開閉状態
  return OrX0115Logic.state.get(orX0115.value.uniqueCpId)?.isOpen ?? false
})

</script>

<template>
  <c-v-sheet class="view">
    <!-- Or11871：有機体：画面メニューエリア -->
    <g-base-or11871 v-bind="or11871">
      <template #createItems>
        <c-v-list-item
          :title="t('btn.copy')"
          prepend-icon="file_copy"
          @click="copyBtnClick"
        />
        <c-v-list-item
          :title="t('btn.delete')"
          prepend-icon="delete"
          @click="deleteBtnClick"
        />
      </template>
    </g-base-or11871>
    <c-v-row
      no-gutters
      class="content-area"
    >
      <c-v-col
        cols="2"
        class="hidden-scroll h-100"
      >
        <!-- （利用者基本）利用者選択の表示 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>

      <c-v-col
        cols="9"
        class="hidden-scroll h-100 px-2"
      >
        <c-v-sheet class="content">
          <c-v-container>
            <c-v-row>
              <c-v-col style="padding: 8px">
                <!-- 事業所 -->
                <g-base-or41179 v-bind="or41179_1" />
              </c-v-col>
            </c-v-row>
            <c-v-row>
              <c-v-col
                cols="auto"
                class="pt-0"
              >
                <c-v-row class="second-row">
                  <c-v-col
                    v-show="localFlg.showPlanningPeriodFlg"
                    cols="auto"
                    style="
                      padding-top: 0px;
                      padding-bottom: 0px;
                      padding-right: 8px;
                      padding-left: 8px;
                    "
                  >
                    <!-- 計画対象期間 -->
                    <g-custom-or52768
                      v-bind="or52768_1"
                      :oneway-model-value="localOneway.or52768Oneway"
                      :unique-cp-id="or52768_1.uniqueCpId"
                    />
                  </c-v-col>
                  <c-v-col
                    v-show="localFlg.showHistoryFlg"
                    cols="auto"
                    style="
                      padding-left: 8px;
                      padding-top: 0px;
                      padding-bottom: 0px;
                      padding-right: 8px;
                    "
                  >
                    <!-- 履歴 -->
                    <g-custom-or52771
                      v-bind="or52771_1"
                      :oneway-model-value="localOneway.or52771Oneway"
                      :unique-cp-id="or52771_1.uniqueCpId"
                    />
                  </c-v-col>
                  <c-v-col
                    v-show="localFlg.showAuthorFlg"
                    cols="auto"
                    style="
                      padding-top: 0px;
                      padding-bottom: 0px;
                      padding-right: 8px;
                      padding-left: 0px;
                    "
                  >
                    <!-- 作成者 -->
                    <g-custom-or52769
                      v-bind="or52769_1"
                      :oneway-model-value="localOneway.or52769Oneway"
                      :unique-cp-id="or52769_1.uniqueCpId"
                    />
                  </c-v-col>
                  <c-v-col
                    v-show="localFlg.showCreateDateFlg"
                    cols="auto"
                    style="
                      padding-top: 0px;
                      padding-bottom: 0px;
                      padding-right: 8px;
                      padding-left: 0px;
                    "
                  >
                    <!-- 作成日 -->
                    <g-custom-or52770
                      v-bind="or52770_1"
                      :oneway-model-value="localOneway.or52770Oneway"
                      :unique-cp-id="or52770_1.uniqueCpId"
                    />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  v-show="localFlg.showHistoryFlg"
                  class="three-row">
                  <c-v-col cols="12">
                    <c-v-row
                      justify="end"
                      align="center"
                    >
                      <c-v-col
                        cols="auto"
                        style="
                          padding-top: 8px;
                          padding-bottom: 0px;
                          padding-right: 8px;
                          padding-left: 0px;
                        "
                      >
                        <!-- ケース番号 -->
                        <g-custom-or52772
                          v-bind="or52772_1"
                          :oneway-model-value="localOneway.or52772Oneway"
                          :unique-cp-id="or52772_1.uniqueCpId"
                        />
                      </c-v-col>
                      <c-v-col
                        cols="auto"
                        style="
                          padding-top: 8px;
                          padding-bottom: 0px;
                          padding-right: 8px;
                          padding-left: 0px;
                        "
                      >
                        <!-- 初回作成日 -->
                        <g-custom-or52788
                          v-bind="or52788_1"
                          :oneway-model-value="localOneway.or52788Oneway"
                          :unique-cp-id="or52788_1.uniqueCpId"
                        />
                      </c-v-col>
                    </c-v-row>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>

            <c-v-divider
              class="divider-class"
              style="margin: 16px; margin-left: 0px; padding-left: 8px"
            />
            <!-- Or52785 (総合計画)メインセクション -->
            <div v-show="localFlg.showComprehensivePlanFormFlg">
              <g-custom-or52785
                v-bind="or52785_1"
                ref="or52785Ref"
                v-model="local.or52785"
              />
            </div>

          </c-v-container>
        </c-v-sheet>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <!-- Or21814 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />

  <!--OrX0115 対象期間-->
  <g-custom-or-x-0115
    v-if="showDialogOrX0115"
    v-bind="orX0115"
    :oneway-model-value="localOneway.orX0115Oneway"
  />

  <!-- 総合計画マスタ -->
  <g-custom-or-27299
    v-if="showDialogOr27299"
    v-bind="or27299_1"
    :oneway-model-value="localOneway.or27299Oneway"
  />

  <!-- 総合計画複写 -->
  <g-custom-or-28382
    v-if="showDialogOr28382"
    v-bind="or28382"
    :oneway-model-value="localOneway.or28382Oneway"
    @on-click-overwrite = handleCopyConfirm
  />
</template>

<style scoped lang="scss">
.divider-class {
  border-width: thin;
  margin: 8px 0px;
}

.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
}

.content {
  padding: 5px 0px;
  overflow-x: auto;
}
.second-row {
  margin-top: 0px;
  align-items: baseline;
}
.three-row {
  margin-top: 8px;
  align-items: baseline;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}

:deep(.v-field__overlay) {
  background-color: white;
}
</style>
