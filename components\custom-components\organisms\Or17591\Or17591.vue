<script setup lang="ts">
/**
 * Or17591:有機体:[優先順位]画面
 * GUI00908_[優先順位]画面
 *
 * @description
 * [優先順位]画面
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch } from 'vue'
import type { Or17591StateType, PriorityOrderInfoType, PriorityOrderType } from './Or17591.type'
import { Or17591Const } from './Or17591.constants'
import { hasRegistAuth, useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Or17591OnewayType, Or17591Type } from '~/types/cmn/business/components/Or17591Type'
import type {
  PriorityOrderSelectInEntity,
  PriorityOrderSelectOutEntity,
  PriorityOrderUpdateInEntity,
  PriorityOrderUpdateOutEntity,
} from '~/repositories/cmn/entities/PriorityOrderSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

import type { Mo01278OnewayType, Mo01278Type } from '~/types/business/components/Mo01278Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CustomClass } from '~/types/CustomClassType'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  Mo01354Headers,
  Mo01354Items,
  Mo01354OnewayType,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { ResizableGridBinding } from '~/plugins/resizableGrid.client'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or17591Type
  onewayModelValue: Or17591OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const localOneway = reactive({
  or17591Oneway: {
    ...props.onewayModelValue,
  },
  // 「優先順位」ダイアログ
  mo00024Oneway: {
    width: '900px',
    height: '455px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.assessment-comprehensive-master-priority'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  // 優先順位情報データテーブルのヘッダー
  mo01354Oneway: {
    height: '276px',
    columnMinWidth: {
      columnWidths: [],
    },
    headers: [] as Mo01354Headers[],
    showPaginationBottomFlg: false,
    showPaginationTopFlg: false,
    density: 'default',
  } as Mo01354OnewayType,
  /** 表示順input Oneway 固定様式 */
  mo01278Oneway: {
    max: 999,
    min: 1,
  } as Mo01278OnewayType,
  mo01265Oneway: {
    // '表示順位削除'
    btnLabel: t('label.display-order-delete'),
    width: '140px',
    prependIcon: 'delete',
  },
  // 注意ラベル
  announcementMessages: {
    value: t('label.announcement'),
    customClass: new CustomClass({
      itemClass: 'announcement-text',
    }),
  } as Mo01337OnewayType,
})

const local = reactive({
  // 優先順位情報
  mo01354: {
    values: {
      selectedRowId: '',
      selectedRowIds: [],
      items: [] as Mo01354Items[],
    },
  },
  or17591: {
    ...props.modelValue,
  } as Or17591Type,
  or17591Data: {} as PriorityOrderInfoType,
  priorityOrderFilterList: [] as PriorityOrderType[],
})
const or21814 = ref({ uniqueCpId: '' }) // 確認ダイアログ
// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
}

// 確定ボタン設置
const mo00609Oneway: Mo00609OnewayType = {
  btnLabel: t('btn.confirm'),
  width: '90px',
  tooltipText: t('tooltip.confirm-btn'),
}

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or17591Const.DEFAULT.IS_OPEN,
})
/**
 * isPermissionRegist
 */
const isPermissionRegist = ref<boolean>(true)
// 優先順位情報選択行データ設定
const historySelectedItem = ref<Mo01354Items | undefined>(undefined)
const isEdit = ref(false)
const freeTInfoData = ref<Mo01354Headers[]>([])
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or17591StateType>({
  cpId: Or17591Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or17591Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value, // 確認ダイアログ
})

onMounted(async () => {
  isPermissionRegist.value = await hasRegistAuth()
  // 初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  isEdit.value = false
  // バックエンドAPIから初期情報取得
  const inputData: PriorityOrderSelectInEntity = {
    ...local.or17591,
  }
  const resData: PriorityOrderSelectOutEntity = await ScreenRepository.select(
    'priorityOrderSelect',
    inputData
  )
  local.or17591Data = resData.data
  const freeTWithList: number[] = []
  if (resData.data.freeTInfo.length) {
    resData.data.freeTInfo.forEach((item, index) => {
      freeTInfoData.value[index] = {
        title: item.freeTText,
        key: `free${index + 1}`,
        sortable: false,
      } as unknown as Mo01354Headers
      freeTWithList.push(Number(item.freeTWith))
    })
  }
  ;(localOneway.mo01354Oneway.columnMinWidth as ResizableGridBinding).columnWidths =
    [95, 172, 172].concat(freeTWithList) ?? []
  localOneway.mo01354Oneway.headers =
    (
      [
        // 優先順位
        {
          title: t('label.assessment-comprehensive-master-priority'), // ヘッダーに表示される名称
          key: 'juni',
          sortable: false,
        },
        // 評価項目
        {
          title: t('label.evaluation-item'), // ヘッダーに表示される名称
          key: 'koumokuKnj',
          sortable: false,
        },
        // 評価細目
        {
          title: t('label.evaluation-detail'), // ヘッダーに表示される名称
          key: 'saimokuKnj',
          sortable: false,
        },
      ] as Mo01354Headers[]
    ).concat(freeTInfoData.value) ?? ([] as Mo01354Headers[])

  if (resData.data.freeKbn === '1') {
    localOneway.mo01354Oneway.headers = localOneway.mo01354Oneway.headers
      .slice(0, 2)
      .concat(localOneway.mo01354Oneway.headers.slice(3))
    ;(localOneway.mo01354Oneway.columnMinWidth as ResizableGridBinding).columnWidths = (
      localOneway.mo01354Oneway.columnMinWidth as ResizableGridBinding
    ).columnWidths
      .slice(0, 2)
      .concat(
        (localOneway.mo01354Oneway.columnMinWidth as ResizableGridBinding).columnWidths.slice(3)
      )
  }

  if (resData.data.priorityOrder.length) {
    resData.data.priorityOrder.forEach((item, index) => {
      item.id = index.toString()
      item.backup = item.juni
      item.sort = {
        value: item.juni,
      }
    })
    // データ情報設定
    ;(local.mo01354.values.items as Mo01354Items[]) = orderTable(
      resData.data.priorityOrder
    ) as Mo01354Items[]
  }

  console.log(localOneway.mo01354Oneway, local.mo01354.values, local.mo01354.values.items)
}

// 情報ダイアログ表示
const handleAuthzKinou = () => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: false,
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11371'),
      firstBtnType: 'blank',
      thirdBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 表示順削除
 *
 */
function clearSort() {
  local.mo01354.values.items.forEach((item) => {
    ;(item?.sort as Mo01278Type).value = ''
  })
}

/**
 * 排序
 *
 * @param sortedList - 表示順設定
 */
function orderTable(sortedList: PriorityOrderType[]) {
  sortedList.sort((a, b) => {
    const aOrder = a.sort && a.sort.value !== '' ? parseInt(a.sort.value, 10) : Infinity
    const bOrder = b.sort && b.sort.value !== '' ? parseInt(b.sort.value, 10) : Infinity
    return aOrder - bOrder
  })
  local.priorityOrderFilterList = sortedList.filter((item) => item.filterKbn !== '1')
  return sortedList.filter((item) => item.filterKbn === '1')
}

/**
 * 表示順設定 タブ変更
 *
 * @param tableItem - テーブルデータ
 */
function setSortOrder(tableItem: PriorityOrderType) {
  // 全空白チェック
  const allBlankFlg = local.mo01354.values.items.every(
    (item) => (item.sort as Mo01278Type).value === ''
  )
  if (allBlankFlg) {
    tableItem.sort!.value = '1'
  } else {
    // 最大値を取得する
    let maxSeq = Math.max(
      ...local.mo01354.values.items
        .map((item) => parseInt((item.sort as Mo01278Type).value))
        .filter((sort) => !isNaN(sort))
    )
    // 最大値チェック
    if (maxSeq < 0) {
      maxSeq = 0
    }
    if (tableItem.sort!.value === '') {
      // 最大値に1を足して新しいseq.valueを設定
      tableItem.sort!.value = (maxSeq + 1).toString()
    }
  }
}

/**
 * 履歴情報選択行
 *
 * @param item - 履歴情報選択行
 */
const clickHistorySelectRow = (item: Mo01354Items) => {
  historySelectedItem.value = item
}

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = async () => {
  if (isPermissionRegist.value) {
    handleAuthzKinou()
    return
  }
  const priorityOrder = [...local.mo01354.values.items, ...local.priorityOrderFilterList]
  priorityOrder.forEach((item) => {
    setSortOrder(item as PriorityOrderType)
    item.juni = (item.sort as Mo01278Type).value
    if (item.backup !== item.juni) {
      isEdit.value = true
    }
  })

  // 変更ありの項目データを保存する
  const inputData: PriorityOrderUpdateInEntity = {
    priorityOrder: priorityOrder as PriorityOrderType[],
    updateKbn: isEdit.value ? Or17591Const.EDIT_FLG : 'C',
  }

  // 画面入力データ変更があるかどうかを判定する
  if (!isEdit.value) {
    // メッセージ内容：
    //「変更されている項目がないため、保存を行うことは出来ません。
    // 項目を入力変更してから、再度保存を行ってください。」
    showOr21814MsgOneBtn(t('message.i-cmn-21800'))
    return
  }
  const resData: PriorityOrderUpdateOutEntity = await ScreenRepository.update(
    'priorityOrderUpdate',
    inputData
  )
    // 選択情報値戻り
    emit('update:modelValue', historySelectedItem.value)
    onClickCloseBtn()
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}

/**
 * 保存ボタン押下
 * 変更されている項目がないため、保存を行うことは出来ません。「改行」項目を入力変更してから、再度保存を行ってください。
 *
 * @param errormsg - Message
 */
function showOr21814MsgOneBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      thirdBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row
          v-if="local.or17591Data.errorKbn !== Or17591Const.ERROR_KUB"
          no-gutters
        >
          <base-mo01265
            :oneway-model-value="localOneway.mo01265Oneway"
            class="mb-2"
            @click="clearSort()"
          />
          <c-v-col cols="12">
            <!-- 優先順位一覧 -->
            <base-mo01354
              v-if="localOneway.mo01354Oneway.headers.length"
              v-model="local.mo01354"
              :oneway-model-value="localOneway.mo01354Oneway"
              hide-default-footer
              class="list-wrapper"
            >
              <!-- 表示順 -->
              <template #[`item.juni`]="{ item }">
                <c-v-col
                  class="data-table-cell"
                  @click="setSortOrder(item)"
                >
                  <base-mo01278
                    v-model="item.sort"
                    :oneway-model-value="localOneway.mo01278Oneway"
                  />
                </c-v-col>
              </template>
              <!-- 評価項目 -->
              <template #[`item.koumokuKnj`]="{ item }">
                <p
                  class="omitted-content"
                  @click="clickHistorySelectRow(item)"
                >
                  {{ item.koumokuKnj }}
                </p>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :text="item.koumokuKnj"
                ></c-v-tooltip>
              </template>
              <!-- 評価細目 -->
              <template #[`item.saimokuKnj`]="{ item }">
                <p
                  class="omitted-content"
                  @click="clickHistorySelectRow(item)"
                >
                  {{ item.saimokuKnj }}
                </p>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :text="item.saimokuKnj"
                ></c-v-tooltip>
              </template>
              <!-- 項目1 -->
              <template #[`item.free1`]="{ item }">
                <p
                  class="omitted-content"
                  @click="clickHistorySelectRow(item)"
                >
                  {{ item.free1 }}
                </p>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :text="item.free1"
                ></c-v-tooltip>
              </template>
              <!-- 項目2 -->
              <template #[`item.free2`]="{ item }">
                <p
                  class="omitted-content"
                  @click="clickHistorySelectRow(item)"
                >
                  {{ item.free2 }}
                </p>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :text="item.free2"
                ></c-v-tooltip>
              </template>
              <!-- 項目3 -->
              <template #[`item.free3`]="{ item }">
                <p
                  class="omitted-content"
                  @click="clickHistorySelectRow(item)"
                >
                  {{ item.free3 }}
                </p>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :text="item.free3"
                ></c-v-tooltip>
              </template>
              <!-- 項目4 -->
              <template #[`item.free4`]="{ item }">
                <p
                  class="omitted-content"
                  @click="clickHistorySelectRow(item)"
                >
                  {{ item.free4 }}
                </p>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :text="item.free4"
                ></c-v-tooltip>
              </template>
            </base-mo01354>
          </c-v-col>
        </c-v-row>
        <c-v-row
          v-else
          class="announcement"
        >
          <base-mo01337 :oneway-model-value="localOneway.announcementMessages"
        /></c-v-row>
      </c-v-sheet>
      <!-- Or21814:有機体:情報ダイアログ -->
      <g-base-or21814 v-bind="or21814" />
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="mo00611Oneway"
          class="mr-2"
          @click="onClickCloseBtn"
        >
        </base-mo00611>
        <!-- 確定ボタン-->
        <base-mo00609
          v-if="
            local.or17591Data.errorKbn !== Or17591Const.ERROR_KUB &&
            local.mo01354.values.items.length
          "
          class="mr-2"
          :oneway-model-value="mo00609Oneway"
          @click="onConfirmBtn"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
:deep(.custom-header-sticky) {
  font-weight: bold !important;
}
// セル内容行数の設定
.omitted-content {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 16px;
}
:deep(td) {
  height: 32px !important;
}
:deep(.full-width-field) {
  width: 72px !important;
}
.announcement {
  display: flex;
  justify-content: center;
  width: 100%;
}
:deep(.announcement-text) {
  label {
    color: #757575 !important;
    font-size: 24px;
    font-weight: bold;
  }
}
.data-table-cell {
  padding: 0 2px !important;
}
</style>
