/**
 * Or28409:(共通)年金手帳選択モーダル
 * GUI00644_［年金手帳選択］画面
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> DAO DINH DUONG
 */
export interface Or28409StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
  /**
   * パラメータ
   */
  param?: {
    /**
     * ユーザーID
     */
    userId: string
  }
}
/**
 * 調査票年金情報のインターフェース。
 */
export interface ToriNenkin {
  /**
   * 交付年月日
   */
  getYmd: string
  /**
   * 金額
   */
  kingaku: string
  /**
   * 手帳番号
   */
  tBango: string
  /**
   * 手帳種別名称
   */
  tKindKnj: string
  /**
   * 受給月１月
   */
  tuki1Flg: string
  /**
   * 受給月2月
   */
  tuki2Flg: string
  /**
   * 受給月3月
   */
  tuki3Flg: string
  /**
   * 受給月4月
   */
  tuki4Flg: string
  /**
   * 受給月5月
   */
  tuki5Flg: string
  /**
   * 受給月6月
   */
  tuki6Flg: string
  /**
   * 受給月7月
   */
  tuki7Flg: string
  /**
   * 受給月8月
   */
  tuki8Flg: string
  /**
   * 受給月9月
   */
  tuki9Flg: string
  /**
   * 受給月１0月
   */
  tuki10Flg: string
  /**
   * 受給月11月
   */
  tuki11Flg: string
  /**
   * 受給月12月
   */
  tuki12Flg: string
}
