import { getSequencedCpId } from '~/utils/useScreenUtils'
/**
 * Or17591:有機体:[優先順位]画面
 * GUI00908_[優先順位]画面
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or17591Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or17591', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
  }

  /**
   * 編集フラグ
   */
  export const EDIT_FLG = 'U'
  /**
   * エラー区分
   */
  export const ERROR_KUB = '1'
}
