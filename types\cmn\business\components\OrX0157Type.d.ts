import type { CustomClass } from '@/types/CustomClassType'
import type { MoInputCommonType } from '~/types/business/components/MoInputCommonType'
/**
 * OrX0157：有機体：入力支援付きテキストフィールド
 *
 * 単方向バインドのデータ構造
 *
 * <AUTHOR>
 */
export interface OrX0157OnewayType {
  /**
   * テキストフィールド入力モード
   */
  inputMode?: 'HalfWidthCharsOnly' | 'NumericOnly' | 'TextOnly'
  /**
   * 入力支援ボタン表示・非表示フラグ
   */
  showEditBtnFlg?: boolean
  /**
   * 入力支援ボタンのクラス
   */
  editBtnClass?: string
  /**
   * 半角文字専用
   */
  halfWidth?: {
    /**
     * 入力支援付きテキストフィールド単方向バインドのテキストフィールドデータ構造
     */
    orX0157InputOneway: OrX0157InputOnewayType
    /**
     * 半角文字専用：
     *カスタムクラス
     */
    customClass?: CustomClass

    /**
     * 半角文字専用：
     * モード
     * 1:全角判定を行いemittypeのフラグを発火する
     * 2:ブラー時に全ての全角文字を半角文字に変換する
     */
    mode?: '1' | '2'

    /**
     * 半角文字専用：
     * 無効フラグ
     * デフォルト:false
     */
    disabled?: boolean
  }
  /**
   * 数値専用
   */
  numeric?: {
    /**
     * 入力支援付きテキストフィールド単方向バインドのテキストフィールドデータ構造
     */
    orX0157InputOneway: OrX0157InputOnewayType
    /**
     * 数値専用:
     * カンマ編集するか
     *
     * @default true
     */
    isEditCamma?: boolean
    /**
     * 数値専用:
     * ピリオド編集するか
     *
     * @default false
     */
    isEditPeriod?: boolean
    /**
     * 数値専用:
     * ピリオドを挿入する桁数
     *
     * @default 2
     */
    periodSortDigit?: number

    /**
     * 数値専用:
     * 最小値
     */
    min?: number

    /**
     * 数値専用:
     * 最大値
     */
    max?: number

    /**
     * 数値専用:
     * スピンボタンの表示フラグ
     *
     * @default false
     */
    showSpinBtn?: boolean

    /**
     * 数値専用:
     * スピンボタンのデフォルト処理無効化フラグ
     *
     * @description
     * 以下の処理を無効化する。独自の処理を行いたい場合に利用。
     * - ボタン押下時の加算、減算
     * - min, maxで指定した値の範囲外にならないよう制御
     *
     * @default false
     */
    disalbeSpinBtnDefaultProcessing?: boolean
  }
  /**
   * テキストフィールド
   */
  text?: {
    /**
     * 入力支援付きテキストフィールド単方向バインドのテキストフィールドデータ構造
     */
    orX0157InputOneway: OrX0157InputOnewayType
  }
}
/**
 * OrX0157:有機体：入力支援付きテキストフィールド
 * 双方向バインドのデータ構造
 */
export interface OrX0157Type {
  /**
   * テキストエリアに表示する値
   */
  value: string
  /**
   * 半角文字専用：
   * EmitType
   */
  emitType?: Mo00030EmitType
}

/**
 * OrX0157:有機体：入力支援付きテキストフィールド
 * 単方向バインドのテキストフィールドデータ構造
 */
export interface OrX0157InputOnewayType extends MoInputCommonType {
  /**
   * コンポーネント名
   */
  name?: string | undefined

  /**
   * 項目名ラベル
   */
  itemLabel?: string | undefined

  /**
   * 項目名ラベルフォント
   */
  itemLabelFontWeight?: string

  /**
   * 必須バッジの表示フラグ（デフォルト：false）
   * true：表示 false：非表示
   */
  isRequired?: boolean

  /**
   * 項目名ラベルの領域を表示（デフォルト：true）
   * true：表示する false：表示しない
   */
  showItemLabel?: boolean

  /**
   * 最大文字数
   */
  maxLength?: string
  /**
   * 無効フラグ
   */
  disabled?: boolean

  /**
   * カスタムクラス
   */
  customClass?: CustomClass

  /**
   * クラス
   */
  class?: string

  /**
   * タイプ
   */
  type?: string

  /**
   * 後アイコン（内側）
   */
  appendInnerIcon?: string

  /**
   * インプットモード
   */
  inputmode?: string

  /**
   * 読み取り専用
   */
  readonly?: boolean

  /**
   * サフィックステキスト
   */
  suffix?: string

  /**
   * ラベルの表示位置
   */
  isVerticalLabel?: boolean

  /**
   * 横幅
   */
  width?: string

  /**
   * カウンター
   */
  counter?: number

  /**
   * ヒントツールチップテキスト
   */
  hintTooltipText?: string

  /**
   * 後ラベル
   */
  appendLabel?: string

  /**
   * ヒントテキスト
   * 表示する場合はhideDetails:falseにする
   */
  hint?: string

  /**
   * 詳細非表示
   */
  hideDetails?: string
}
