<script setup lang="ts">
/**
 * or10939:［履歴選択］画面 計画ﾓﾆﾀﾘﾝｸﾞダイアログ
 * GUI01231_［履歴選択］画面 計画ﾓﾆﾀﾘﾝｸﾞ
 *
 * @description
 *  履歴選択計画ﾓﾆﾀﾘﾝｸﾞダイアログ
 *
 * <AUTHOR>
 */

import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10939Const } from './Or10939.constants'
import type { HistoryInfoListItem, Or10939StateType } from './Or10939.type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type {
  HistorySelectInfoType,
  Or10939Type,
  Or10939OnewayType,
} from '~/types/cmn/business/components/Or10939Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { useScreenOneWayBind } from '#imports'
import type {
  PlanMoniHistoryInfoSelectInEntity,
  PlanMoniHistoryInfoSelectOutEntity,
} from '~/repositories/cmn/entities/PlanMoniHistoryInfoSelectEntity'
import type {
  Mo01334Items,
  Mo01334Headers,
  Mo01334OnewayType,
} from '~/types/business/components/Mo01334Type'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
/**
 * コンポーネントのプロパティ
 */
interface Props {
  modelValue: Or10939Type
  onewayModelValue: Or10939OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const planPeriodMo01334Header_One = [
  // 作成日
  {
    title: t('label.create-date'),
    key: 'createYmd',
    width: '120',
    sortable: false,
  },
  // 作成者
  {
    title: t('label.author'),
    key: 'shokuin',
    width: '170',
    sortable: false,
  },
  // ケース番号
  {
    title: t('label.caseNo'),
    key: 'caseNo',
    width: '110',
    sortable: false,
  },
  // 改訂
  {
    title: t('label.revision'),
    key: 'kaiteiFlg',
    width: '80',
    sortable: false,
  },
  // 種別
  {
    title: t('label.kind'),
    key: 'moniFlg',
    width: '150',
    sortable: false,
  },
  // 様式名
  {
    title: t('label.style-name'),
    key: 'styleName',
    width: '150',
    sortable: false,
  },
] as unknown as Mo01334Headers[]

const planPeriodMo01334Header_Two = [
  // 作成日
  {
    title: t('label.create-date'),
    key: 'createYmd',
    width: '120',
    sortable: false,
  },
  // 作成者
  {
    title: t('label.author'),
    key: 'shokuin',
    width: '170',
    sortable: false,
  },
  // 様式名
  {
    title: t('label.style-name'),
    key: 'styleName',
    width: '150',
    sortable: false,
  },
] as unknown as Mo01334Headers[]

/**
 * OneWayBindのデフォルト値
 */
const defaultOnewayModelValue: Or10939OnewayType = {
  historySelectInfo: {} as HistorySelectInfoType,
}
/**
 * ローカルのOneWayBindデータ
 */
const localOneway = reactive({
  or10939: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '760px',
    height: '382px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'or10939',
      toolbarTitle: t('label.history-select'),
      toolbarName: 'Or10939ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      cardTextClass: 'pa-2',
    },
  } as Mo00024OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,

  // 計画期間一覧
  planPeriodMo01334: {
    // 計画期間データテーブルのヘッダー
    headers: [],
    height: 262,
    items: [] as Mo01334Items[],
  } as Mo01334OnewayType,
})

/**
 * モーダルの状態
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10939Const.DEFAULT.IS_OPEN,
})

// 履歴選択情報選択行データ設定
const historySelectedItem = ref<HistoryInfoListItem | undefined>(undefined)

const local = reactive({
  // 履歴選択情報
  mo01334: {
    value: '',
    values: [],
  },
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10939StateType>({
  cpId: Or10939Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10939Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(async () => {
  localOneway.planPeriodMo01334.headers = []
  if (localOneway.or10939.historySelectInfo.sypFlg === Or10939Const.DEFAULT.SYP_FLG) {
    localOneway.planPeriodMo01334.headers = planPeriodMo01334Header_One
  } else {
    localOneway.planPeriodMo01334.headers = planPeriodMo01334Header_Two
  }
  localOneway.mo00024Oneway.width =
    localOneway.or10939.historySelectInfo.sypFlg === Or10939Const.DEFAULT.SYP_FLG
      ? '811px'
      : '471px'
  // 初期情報取得
  await getInitDataInfo()
})

// 初期化処理
const getInitDataInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: PlanMoniHistoryInfoSelectInEntity = {
    sc1Id: localOneway.or10939.historySelectInfo.sc1Id,
    svJigyoId: localOneway.or10939.historySelectInfo.svJigyoId,
    userId: localOneway.or10939.historySelectInfo.userId,
    sypFlg: localOneway.or10939.historySelectInfo.sypFlg,
  }
  // サーバーからデータを取得
  const resDate: PlanMoniHistoryInfoSelectOutEntity = await ScreenRepository.select(
    'planMoniHistoryInfoSelect',
    inputData
  )

  // データ情報設定
  localOneway.planPeriodMo01334.items = resDate.data.historyInfoSelectList
  // 親画面.履歴IDが存在する場合
  if (localOneway.or10939.historySelectInfo.historyId) {
    historySelectedItem.value = localOneway.planPeriodMo01334.items.find(
      (item) => item.cmoni1Id === localOneway.or10939.historySelectInfo.historyId
    )
    return
  }
  // デフォルトでは第一条を選択します
  historySelectedItem.value = localOneway.planPeriodMo01334.items?.[0] ?? undefined
}
/**
 * 履歴情報選択行
 *
 * @param item - 履歴情報選択行
 */
const clickHistorySelectRow = (item: HistoryInfoListItem) => {
  historySelectedItem.value = item
}

/**
 * 履歴情報の行をダブルクリックで選択
 *
 * @param item - 履歴情報選択行
 */
const doubleClickHistorySelectRow = (item: HistoryInfoListItem) => {
  historySelectedItem.value = item
  onConfirmBtn()
}

/**
 * 履歴情報選択行設定様式
 *
 * @param item - 履歴情報選択行
 */
const historyIsSelected = (item: { cmoni1Id: null }) =>
  historySelectedItem.value?.cmoni1Id === item.cmoni1Id
/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = () => {
  // 履歴データがない場合、操作なし
  if (!historySelectedItem.value) return
  // 選択情報値戻り
  const resData = { cmoni1Id: historySelectedItem.value.cmoni1Id } as Or10939Type
  emit('update:modelValue', resData)
  onClickCloseBtn()
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
    class="mo00024"
  >
    <template #cardItem>
      <c-v-row no-gutters>
        <c-v-col
          v-if="localOneway.or10939.historySelectInfo.sypFlg === Or10939Const.DEFAULT.SYP_FLG"
          class="table-header"
        >
          <base-mo01334
            v-model="local.mo01334"
            class="list-wrapper"
            hide-default-footer
            :oneway-model-value="localOneway.planPeriodMo01334"
          >
            <template #item="{ item }">
              <tr
                style="cursor: default"
                :class="{ 'selected-row': historyIsSelected(item) }"
                @click="clickHistorySelectRow(item)"
                @dblclick="doubleClickHistorySelectRow(item)"
              >
                <td>
                  <span>{{ item.createYmd }}</span>
                </td>
                <td>
                  <c-v-col class="d-flex align-center">
                    <div class="overflowText width-138">{{ item.shokuin }}</div>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.shokuin"
                      open-delay="200"
                    />
                  </c-v-col>
                </td>
                <td class="text-right">
                  <span>{{ item.caseNo }}</span>
                </td>
                <td>
                  <span>{{ item.kaiteiFlg }}</span>
                </td>
                <td>
                  <span>{{ item.moniFlg }}</span>
                </td>
                <td>
                  <c-v-col class="d-flex align-center">
                    <div class="overflowText width-117">{{ item.styleName }}</div>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.styleName"
                      open-delay="200"
                    />
                  </c-v-col>
                </td>
              </tr>
            </template>
          </base-mo01334>
        </c-v-col>
        <c-v-col
          v-if="localOneway.or10939.historySelectInfo.sypFlg === Or10939Const.DEFAULT.SYP_NOFLG"
          class="table-header"
        >
          <base-mo01334
            v-model="local.mo01334"
            class="list-wrapper"
            hide-default-footer
            :oneway-model-value="localOneway.planPeriodMo01334"
          >
            <template #item="{ item }">
              <tr
                style="cursor: default"
                :class="{ 'selected-row': historyIsSelected(item) }"
                @click="clickHistorySelectRow(item)"
                @dblclick="doubleClickHistorySelectRow(item)"
              >
                <td>
                  <span>{{ item.createYmd }}</span>
                </td>
                <td>
                  <c-v-col class="d-flex align-center">
                    <div class="overflowText width-138">{{ item.shokuin }}</div>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.shokuin"
                      open-delay="200"
                    />
                  </c-v-col>
                </td>
                <td>
                  <c-v-col class="d-flex align-center">
                    <div class="overflowText width-117">{{ item.styleName }}</div>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.styleName"
                      open-delay="200"
                    />
                  </c-v-col>
                </td>
              </tr>
            </template>
          </base-mo01334>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          @click="onClickCloseBtn"
        ></base-mo00611>
        <base-mo00609
          :oneway-model-value="localOneway.mo00609ConfirmOneway"
          class="ml-2"
          @click="onConfirmBtn"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';

:deep(.v-card-text) {
  padding: 0;
}

.width-117 {
  max-width: 117px;
  width: 117px !important;
}

.width-138 {
  max-width: 138px;
  width: 138px !important;
}

:deep(.v-col) {
  padding: 0 !important;
}

:deep(.v-table__wrapper tr td) {
  height: 32px !important;
}

.overflowText {
  width: 100%;
  height: 100%;
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
  padding: 0;
}
</style>
