import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * Or32525の計画書（２）計画書（２）情報保存リクエストパラメータエンティティ
 * GUI01014_計画書（２）
 *
 * <AUTHOR>
 */
export interface Careplan2InsertInEntity extends InWebEntity {
  /** 計画対象期間更新区分 */
  updateKbn: string
  /** 計画対象期間ID */
  sc1Id: string
  /** 更新回数 */
  modifiedCnt: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 利用者ID */
  userId: string
  /** 事業者ID */
  svJigyoId: string
  /** 種別ID */
  syubetuId: string
  /** 履歴更新区分 */
  historyUpdateKbn: string
  /** 計画書（２）ID */
  ks21Id: string
  /** 作成日 */
  createYmd: string
  /** 作成者ID */
  shokuId: string
  /** 有効期間ID */
  termid: string
  /** 計画書（２）リスト */
  keikasyo2List: Keikasyo2Insert[]
}

/**
 * Or32525の計画書（２）計画書（２）情報保存レスポンスパラメータエンティティ
 * GUI01014_計画書（２）
 *
 */
export interface Careplan2InsertOutEntity extends OutWebEntity {
  /**
   * data
   */
  data: {
    /** 文章法対象機能の電子ファイル保存フラグ */
    updateSc1IdKbn: string
  }
}
/**
 * 計画書（２）
 */
export interface Keikasyo2Insert {
  /** 計画書（２）更新区分 */
  updateKbn1?: string
  /** カウンター */
  ks22Id?: string
  /** 計画書（２）ID */
  ks21Id?: string
  /** 具体的 */
  gutaitekiKnj?: string
  /** 長期 */
  choukiKnj?: string
  /** 短期 */
  tankiKnj?: string
  /** 介護 */
  kaigoKnj?: string
  /** サービス種 */
  svShuKnj?: string
  /** 頻度 */
  hindoKnj?: string
  /** 期間 */
  kikanKnj?: string
  /** 通番 */
  seq?: string
  /** 課題番号 */
  kadaiNo?: string
  /** 介護番号 */
  kaigoNo?: string
  /** 長期期間 */
  choKikanKnj?: string
  /** 短期期間 */
  tanKikanKnj?: string
  /** 給付対象 */
  hkyuKbn?: string
  /** サービス事業者ＣＤ */
  jigyouId?: string
  /** サービス事業者名 */
  jigyoNameKnj?: string
  /** 給付文字 */
  hkyuKnj?: string
  /** 長期期間開始日 */
  choSYmd?: string
  /** 長期期間終了日 */
  choEYmd?: string
  /** 短期期間開始日 */
  tanSYmd?: string
  /** 短期期間終了日 */
  tanEYmd?: string
  /** 期間開始日 */
  kikanSYmd?: string
  /** 期間終了日 */
  kikanEYmd?: string
  /** 保険サービスリ */
  hokenList?: Hoken[]
  /** 月日指定リスト */
  tukihiList?: Tukihi[]
  /** サービス曜日リスト */
  yobiList?: Yobi[]
  /** 担当者リスト */
  tantoList?: Tanto[]
  /** 更新回数 */
  modifiedCnt?: string
}

/**
 * 保険サービス
 */
export interface Hoken {
  /** 保険サービス更新区分 */
  updateKbn2?: string
  /** カウンター */
  ks211Id?: string
  /** 計画書（２）ID */
  ks21Id?: string
  /** 曜日 */
  youbi?: string
  /** 週単位以外のｻｰﾋﾞｽ区分 */
  igaiKbn?: string
  /** 週単位以外のｻｰﾋﾞｽ（日付指定） */
  igaiDate?: string
  /** 週単位以外のｻｰﾋﾞｽ（曜日指定） */
  igaiWeek?: string
  /** 居宅：開始時間 */
  kaishiJikan?: string
  /** 居宅：終了時間 */
  shuuryouJikan?: string
  /** 居宅：サービス種類 */
  svShuruiCd?: string
  /** 居宅：サービス項目（台帳） */
  svItemCd?: string
  /** 居宅：サービス事業者CD */
  svJigyoId?: string
  /** 居宅：福祉用具貸与単位 */
  tanka?: string
  /** 福祉用具貸与商品コード */
  fygId?: string
  /** 合成識別区分 */
  gouseiSikKbn?: string
  /** 加算フラグ */
  kasanFlg?: string
  /** 親レコード番号 */
  oyaLineNo?: string
  /** 更新回数 */
  modifiedCnt?: string
}

/**
 * 月日指定
 */
export interface Tukihi {
  /**
   *月日指定更新区分
   */
  updateKbn3?: string
  /**
   *カウンター
   */
  ks212Id?: string
  /**
   *計画書（２）ID
   */
  ks21Id?: string
  /**
   *月日指定開始日
   */
  startYmd?: string
  /**
   *月日指定終了日
   */
  endYmd?: string
  /** 更新回数 */
  modifiedCnt?: string
}

/**
 * 曜日
 */
export interface Yobi {
  /**
   *サービス曜日更新区分
   */
  updateKbn4?: string
  /**
   *カウンター
   */
  ks221Id?: string
  /**
   *計画書（２）行データID
   */
  ks22Id?: string
  /**
   *計画書（２）ID
   */
  ks21Id?: string
  /**
   *曜日
   */
  youbi?: string
  /**
   *週単位以外のｻｰﾋﾞｽ区分
   */
  igaiKbn?: string
  /**
   *週単位以外のｻｰﾋﾞｽ（日付指定）
   */
  igaiDate?: string
  /**
   *週単位以外のｻｰﾋﾞｽ（曜日指定）
   */
  igaiWeek?: string
  /**
   *居宅：開始時間
   */
  kaishiJikan?: string
  /**
   *居宅：終了時間
   */
  shuuryouJikan?: string
  /**
   *居宅：サービス種類
   */
  svShuruiCd?: string
  /**
   *居宅：サービス項目（台帳）
   */
  svItemCd?: string
  /**
   *居宅：サービス事業者CD
   */
  svJigyoId?: string
  /**
   *居宅：福祉用具貸与単位
   */
  tanka?: string
  /**
   *福祉用具貸与商品コード
   */
  fygId?: string
  /**
   *合成識別区分
   */
  gouseiSikKbn?: string
  /**
   *加算フラグ
   */
  kasanFlg?: string
  /** 更新回数 */
  modifiedCnt?: string
}
/**
 *担当者
 */
export interface Tanto {
  /**
   * 担当者更新区分
   */
  updateKbn5?: string
  /**
   * カウンター
   */
  ks222Id?: string
  /**
   * 計画書（２）行データID
   */
  ks22Id?: string
  /**
   * 計画書（２）ID
   */
  ks21Id?: string
  /**
   * 施設：職種（担当者）
   */
  shokushuId?: string
  /** 更新回数 */
  modifiedCnt?: string
}
