/**
 *
 * GUI00834_［アセスメント（包括）］画面 心理
 *
 * @description
 * ［アセスメント（包括）］画面 心理
 *
 * <AUTHOR>
 */

import { HttpResponse } from 'msw'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type {
  AssessmentComprehensivePsychologyUpdateInEntity,
  AssessmentComprehensivePsychologyUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentComprehensivePsychologyInitEntity'

/**
 *  GUI00834_［アセスメント（包括）］画面 心理 初期情報保存APIモック
 *
 * @description
 * GUI00834_［［アセスメント（包括）］画面 心理 情報データを保存する。
 * dataName："assessmentComprehensivePsychologyUpdate"
 */
export function handler(inEntity: AssessmentComprehensivePsychologyUpdateInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {},
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
