import type { InWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 印刷設定帳票出力入力エンティティ
 */
export interface ShuuKanReportEntity extends InWebEntity {
  /** サービス事業者ID */
  svJigyoId: string
  /** 法人ID */
  houjinID: string
  /** 施設ID */
  shisetuId: string
  /** 事業所名 */
  svJigyoName: string
  /** 職員ID */
  shokuId: string
  /** 利用者ID */
  userId: string
  /** 帳票セクション番号 */
  sectionNo: string
  /** 週間表ID */
  week1Id: string
  /**
   * 印刷設定
   */
  printSet: {
    /** 指定日 */
    shiTeiDate: string
    /** 指定日印刷区分 */
    shiTeiKubun: string
    /** 印鑑欄を表示するフラグ */
    inkanFlg: string
  }
  /**
   * 印刷オプション
   */
  printOption: {
    /** 敬称変更フラグ */
    keishoFlg: string
    /** 敬称 */
    keisho: string
    /** 記入用シート印刷フラグ */
    emptyFlg: string
    /** 作成者を印刷フラグ */
    sakuNameFlag: string
    /** サービス選択フラグ */
    serviceFlag: string
  }
}
