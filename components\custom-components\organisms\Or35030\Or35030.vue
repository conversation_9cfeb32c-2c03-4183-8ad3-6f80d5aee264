<script setup lang="ts">
/**
 * Or35030:（照会内容）印刷設定モーダル
 * GUI01265_印刷設定
 *
 * @description
 * （照会内容）印刷設定モーダル
 *
 * <AUTHOR> NGUYEN VAN PHONG
 */
import { computed, onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10016Logic } from '../Or10016/Or10016.logic'
import { Or26257Logic } from '../Or26257/Or26257.logic'
import { Or10016Const } from '../Or10016/Or10016.constants'
import { Or26257Const } from '../Or26257/Or26257.constants'
import { Or35030Const } from './Or35030.constants'
import type {
  Or35030TwoWayData,
  UserEntity
} from './Or35030.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or35030OnewayType } from '~/types/cmn/business/components/Or35030Type'
import {
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
} from '#imports'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or26261Const } from '~/components/custom-components/organisms/Or26261/Or26261.constants'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Or10016OnewayType } from '~/types/cmn/business/components/Or10016Type'
import type { Or26257OnewayType, Or26257Type } from '~/types/cmn/business/components/Or26257Type'
import type {
  NewNursingCareElderlySupportElapsedRecordSelectInEntity,
  NewNursingCareElderlySupportElapsedRecordSelectOutEntity,
  sysIniInfo,
  prtList
} from '~/repositories/cmn/entities/NewNursingCareElderlySupportElapsedRecordSelectEntity'
import type {
  IFreeAssessmentFacePrintSettingsUpdateInEntity,
  IFreeAssessmentFacePrintSettingsUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsUpdateEntity'
import type { LedgerInitializeDataComSelectInEntity, LedgerInitializeDataComSelectOutEntity } from '~/repositories/cmn/entities/ledgerInitializeDataComSelect'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'

const { t } = useI18n()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or35030OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const or00094 = ref({ uniqueCpId: '' })

const or21813 = ref({ uniqueCpId: '' })

const or21815 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })

// プロファイル
const choPro = ref('')

const localOneway = reactive({
  Or35030: {
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '1400px',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or35030',
      toolbarTitle: t('label.print-set'),
      toolbarName: 'Or35030ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  mo00610SealColumnBtnOneWay: {
    btnLabel: t('label.seal-column'),
  } as Mo00610OnewayType,
  mo01338OneWayTitle: {
    value: t('label.print-settings-title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo00039OneWayPrintDateCreation: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo00020OneWay: {
    showItemLabel: false,
    width: '132px',
  } as Mo00020OnewayType,
  mo00020OneWayPeriodStartDate: {
    showItemLabel: false,
    width: '132px',
  } as Mo00020OnewayType,
  mo00020OneWayPeriodEndDate: {
    showItemLabel: false,
    width: '132px',
  } as Mo00020OnewayType,
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00018OneWayChangeTitle: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00045OnewayTitleInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
  } as Mo00045OnewayType,
  mo00045OnewayTextInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '50',
    maxLength: '2',
  } as Mo00045OnewayType,
  mo00018OneWayPrintTheForm: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintLevelCare: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-the-level-of-care-required'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00040OnewayFacilityName: {
    name: 'Or35030SelectFacilityName',
    showItemLabel: true,
    itemLabel: t('label.facility-name'),
    itemTitle: 'label',
    itemValue: 'value',
    isRequired: false,
    items: [],
    // customClass: {
    //   outerClass: 'd-flex',
    //   itemStyle: 'width:270px',
    // } as CustomClass,
  } as Mo00040OnewayType,
  mo00040OnewaySelectBusiness: {
    name: 'Or35030SelectBusiness',
    showItemLabel: false,
    itemTitle: 'naiyoKnj',
    itemValue: 'svTeikyoCd',
    isRequired: false,
    width: '240',
    disabled: true,
    items: [],
    // customClass: {
    //   outerClass: 'd-flex',
    //   itemStyle: 'width:270px',
    // } as CustomClass,
  } as Mo00040OnewayType,
  mo01338OnewayAuthorLabel: {
    value: t('label.author'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00009OnewayAuthorButton: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00009OnewayCareManagerSelection: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo01338OneWayAuthorName: {
    value: '',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00018OneWayPrintDuration: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-time-period'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayFilterTypeOffer: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.filter-by-type-of-offer'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo01338OnewayChoiceOne: {
    value: t('label.selected'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00009OnewayChoiceOneButton: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
    disabled: true,
  } as Mo00009OnewayType,
  mo00009OnewayChoiceTwoButton: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
    disabled: true,
  } as Mo00009OnewayType,
  mo00009OnewayChoiceThreeButton: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
    disabled: true,
  } as Mo00009OnewayType,

  mo00018OneWayFilterPersonInCharge: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.filter-by-person-in-charge'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayFilterByRecorder: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.filter-by-recorder'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,

  mo01338OnewayChoiceTwo: {
    value: t('label.selected'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayFilterPersonInChargeData: {
    value: '',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayFilterByRecorderData: {
    value: '',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,

  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,

  mo01338OneWayPriodLabel: {
    value: t('label.period'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayPriodTildeLabel: {
    value: t('label.wavy'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo00018CheckAllOneway: {
    showItemLabel: false,
    hideDetails: true,
    isVerticalLabel: false,
    checkboxLabel: '',
  } as Mo00018OnewayType,
  mo01337OnewayUserItem: {
    customClass: {
      outerStyle: 'background-color: rgba(0, 0, 0, 0);',
      itemClass: 'itemClass',
    } as CustomClass,
  } as Mo01337OnewayType,
  mo01338OnewayFooter: {
    value: '',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none;',
      itemClass: 'mo01338-footer',
      itemStyle: 'padding-left: 8px',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00018OnewayCheckUser: {
    showItemLabel: false,
    hideDetails: true,
    isVerticalLabel: false,
    checkboxLabel: '',
  } as Mo00018OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: {
      outerClass: 'd-flex',
      itemStyle: 'width:250px',
    } as CustomClass,
  } as OrX0145OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or35030Const.DEFAULT.IS_OPEN,
})

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

// 帳票イニシャライズデータを取得する
const reportInitData = ref([
  {
    // 氏名伏字印刷
    prtName: '',
    // 文書番号印刷
    prtBng: '',
    // 個人情報印刷
    prtKojin: '',
  },
])

const local = reactive({
  or35030: {
    ...props.onewayModelValue
  },
   /**
    * システムINI情報
    */
  sysIniInfo: {} as sysIniInfo,

  // 1:作成者, 2:担当者で絞り込む, 3:記録者で絞り込む
  actionClickPopup: '' as string,
  textInput: {
    value: '',
  } as Mo00045Type,
  titleInput: {
    value: '',
  } as Mo00045Type,
  mo00039Type: '',
  mo00039TypePrintDateCreation: '',
  mo00020Type: {
    value: '',
  } as Mo00020Type,
  mo00020TypePeriodStartDate: {
    value: '',
  } as Mo00020Type,
  mo00020TypePeriodEndDate: {
    value: '',
  } as Mo00020Type,
  mo00020TypePrintDateCreation: {
    value: '',
  } as Mo00020Type,
  mo00018TypeChangeTitle: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintDuration: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeFilterTypeOffer: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeFilterPersonInCharge: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeFilterByRecorder: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintLevelCare: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintAuthor: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintDescription: {
    modelValue: false,
  } as Mo00018Type,
  mo00039TypeUserSelectType: '',
  mo00039TypeHistorySelectType: '',
  mo00020TypeKijunbi: {
    value: '2024/11/11',
  } as Mo00020Type,
  mo00040FacilityName: {
    modelValue: '',
  } as Mo00040Type,
  mo00040SelectBusiness: {
    modelValue: '',
  } as Mo00040Type,
  mo00018TypePrintTheYear: {
    modelValue: false,
  } as Mo00018Type,
  minPrintFrameRows: {
    value: '',
  } as Mo00045Type,
   /**
    * 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
    */
  userSelect: false,
  /**
   * 選択利用者ID
   */
  selectUserId: '',
  /**
   * 選択利用者リスト
   */
  userList: [] as UserEntity[],
})

/**
 * 出力帳票名一覧
 */
const mo01334OnewayReport = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'prtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})

/**
 * 利用者
 */

const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width:480px',
})

const orX0130 = ref({ uniqueCpId: '' })
const or10016 = ref({ uniqueCpId: '' })
const or26257 = ref({ uniqueCpId: '' })
/**
 * or26261
 */
const or26261 = ref({ uniqueCpId: '' })

/**
 * or10016Data
 */
const or10016Data: Or10016OnewayType = {
  /**
   * 法人ID
   */
  houjinId: systemCommonsStore.getHoujinId!,
  /**
   * 施設ID
   */
  shisetsuId: systemCommonsStore.getShisetuId!,
  /**
   * 職員ID
   */
  shokuinId: systemCommonsStore.getStaffId!,
  /**
   * システムコード
   */
  systemCode: systemCommonsStore.getSystemCode!,
  /**
   * 事業所ID
   */
  jigyoshoId: '',
  /**
   * ログイン番号
   */
  loginNumber: '',
  /**
   * ログインユーザタイプ
   */
  loginUserType: '',
  /**
   * 電子カルテ連携フラグ
   */
  emrLinkFlag: '',
  /**
   * 帳票セクション番号
   */
  reportSectionNumber: '3GKU0P092P001',

  /**
   * 引続情報.アセスメント
   */
  assessment: '4',
  /**
   * 引続情報.会議禄フラグ
   */
  conferenceFlag: true,
}

/**
 * AC008
 * 作成者
 * 返却情報を画面に設定する：
 * 画面.記録者値カラム=返却情報.職員番号
 * 画面.記録者表示カラム=返却情報.職員名
 */
const or26257Type = ref<Or26257Type>({
  shokuin: {
    shokuin1Knj: '',
    shokuin2Knj: '',
    shokuNumber: '',
  },
} as Or26257Type)

/**
 * AC008
 * 作成者
 */
const or26257Data: Or26257OnewayType = {
  // システム略称
  sysCdKbn: '3GK',
  // アカウント設定
  secAccountAllFlg: '',
  // 適用事業所ＩＤリスト
  svJigyoIdList: [{ svJigyoId: '1' }],
  // 職員ID
  shokuinId: '',
  // システムコード
  gsysCd: '',
  // モード
  selectMode: '',
  // 基準日
  kijunYmd: '',
  // 事業所ID
  defSvJigyoId: '',
  // フィルターフラグ
  filterDwFlg: '',
  // 雇用状態
  koyouState: '',
  // 地域フラグ
  areaFlg: '',
  // 表示名称リスト
  hyoujiColumnList: [{ hyoujiColumn: '1' }, { hyoujiColumn: '2' }, { hyoujiColumn: '3' }],
  // 未設定フラグ
  misetteiFlg: '0',
  // 他職員参照権限
  otherRead: '',
  // 中止フラグ
  refStopFlg: '',
  // 処理フラグ
  syoriFlg: '',
  // メニュー１ID
  menu1Id: '',
  // 件数フラグ
  kensuFlg: '1',
  // 職員IDリスト
  shokuinIdList: [],
}

/**
 * 出力帳票名一覧
 */
const mo01334TypeReport = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**************************************************
 * Pinia
 **************************************************/
/**
 * Piniaストアの設定
 * - ダイアログの開閉状態を管理
 * - uniqueCpIdを使用して一意の状態を識別
 */
const { setState } = useScreenOneWayBind({
  cpId: Or35030Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or35030Const.DEFAULT.IS_OPEN
    },
  },
})

const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(props.uniqueCpId)
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or00094Const.CP_ID(0)]: or00094.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [Or10016Const.CP_ID(0)]: or10016.value,
  [Or26257Const.CP_ID(0)]: or26257.value,
  [Or26261Const.CP_ID(0)]: or26261.value,
})

const { refValue } = useScreenTwoWayBind<Or35030TwoWayData>({
  cpId: Or35030Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

onMounted(async () => {
  local.mo00020Type.value = systemCommonsStore.getSystemDate!

  // 50音ヘッドラインの表示設定ボタンを表示
  Or00094Logic.state.set({
    uniqueCpId: or00094.value.uniqueCpId,
    state: { dispSettingBtnDisplayFlg: true },
  })
  await initCodes()
  await printSettingsScreenInitialInfoSelect()
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr10016 = computed(() => {
  // Or10016のダイアログ開閉状態
  return Or10016Logic.state.get(or10016.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 回数区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分がコード一覧から取得
  // コード区分マスタID：482
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )

  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 印刷する要介護度セレクトボックス
  // 作成年月日印刷区分がコード一覧から取得 : コード区分マスタID： 527
  localOneway.mo00040OnewayFacilityName.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE
  )

  // 初期値
  local.mo00039TypeUserSelectType = '0'
  local.mo00039TypeHistorySelectType = '0'
}

/**
 * 「印鑑欄」ボタン押下
 */
function onClickSealColumn() {
  Or10016Logic.state.set({
    uniqueCpId: or10016.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 印刷設定画面初期情報を取得する
 * dataName: NewNursingCareElderlySupportElapsedRecordSelect
 */
async function printSettingsScreenInitialInfoSelect() {
  const inputData: NewNursingCareElderlySupportElapsedRecordSelectInEntity = {
    // システムコード
    sysCd: systemCommonsStore.getSystemCode ?? '',
    // システム略称
    sysRyaku: '',
    // 機能名
    kinounameKnj: 'PRT',
    // 法人ID
    houjinId: systemCommonsStore.getHoujinId ?? '',
    // 施設ID
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    // 事業者ID
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    // 利用者ID
    userId: systemCommonsStore.getUserId ?? '',
    // 職員ID
    shokuId: '',
    // 担当者ID
    tantoId: '',
    // 処理年月日
    appYmd: '',
    // セクション名
    // 0：管理しない／1：管理する
    sectionName: '0',
    // 選択帳票番号
    choIndex: '',
    // 適用事業所IDリスト
    // 0：主に日誌以外、1：主に日誌系
    svJigyoIds: '0',
    // 計画期間管理フラグ
    kikanFlg: '',
    // 個人情報使用フラグ
    // 0：不使用、1：使用
    kojinhogoUsedFlg: '0',
    // 個人情報表示値
    // 0：主に日誌以外、1：主に日誌系
    sectionAddNo: '0',
    // インデックス
    index: '0',
  }

  // バックエンドAPIから初期情報取得
  const ret: NewNursingCareElderlySupportElapsedRecordSelectOutEntity =
    await ScreenRepository.select('NewNursingCareElderlySupportElapsedRecordSelect', inputData)

  // AC001-2-2: ①帳票タイトル :  出力帳票一覧明細に選択される行.帳票タイトル
  const mo01334OnewayList: Mo01334Items[] = []
  for (const item of ret.data.prtList) {
    if (item) {
      mo01334OnewayList.push({
        id: item.prtNo,
        mo01337OnewayReport: {
          value: item.prtTitle,
          unit: '',
          customClass: {
            itemStyle: 'margin-left: -8px;',
            outerStyle: 'background-color: rgba(0, 0, 0, 0);',
          }
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
    }
  }

  mo01334OnewayReport.value.items = mo01334OnewayList

  if (localOneway.Or35030.sectionName) {
    mo01334TypeReport.value.value = localOneway.Or35030.sectionName
  } else {
    mo01334TypeReport.value.value = ret.data.prtList[0].prtNo
  }

  refValue.value = { choPrtList: ret.data.prtList }

  useScreenStore().setCpTwoWay({
    cpId: Or35030Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })

  // 提供種別セレクトボックス
  localOneway.mo00040OnewaySelectBusiness.items = ret.data.tekiyouList

  local.sysIniInfo = ret.data.sysIniInfo
}

/**
 * 「×ボタン」押下
 *
 * @param choPrtList - 出力帳票印刷情報リスト
 */
async function printSettingsInfoUpdate(choPrtList: prtList[]) {
  // バックエンドAPIから印刷設定情報保存
  const inputData: IFreeAssessmentFacePrintSettingsUpdateInEntity = {
    sysRyaku: '3gk',
    sectionName: local.or35030.sectionName,
    gsyscd: systemCommonsStore.getSystemCode,
    shokuId: systemCommonsStore.getStaffId,
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: '',
    svJigyoId: '',
    index: '',
    sysIniInfo: local.sysIniInfo,
    prtList: choPrtList,
  } as IFreeAssessmentFacePrintSettingsUpdateInEntity

  const resp: IFreeAssessmentFacePrintSettingsUpdateOutEntity = await ScreenRepository.update(
    'freeAssessmentFacePrintSettingsUpdate',
    inputData
  )
  return resp
}

/**
 * 帳票イニシャライズデータを取得する。
 */
async function getReportInfoDataList() {
  const inputData: LedgerInitializeDataComSelectInEntity = {
    sysCd:  systemCommonsStore.getSystemCode ?? '',
    kinounameKnj: 'PRT',
    shokuId: systemCommonsStore.getStaffId!,
    sectionKnj: choPro.value,
    kojinhogoFlg: '0',
    sectionAddNo: '0',
  }
  // バックエンドAPIから初期情報取得
  const ret: LedgerInitializeDataComSelectOutEntity = await ScreenRepository.select(
    'ledgerInitializeDataComSelect',
    inputData
  )
  reportInitData.value = ret.data.iniDataObject
}

/**
 * エラーダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21813MsgOneBtn() {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21815MsgOneBtn() {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 帳票タイトルが入力していない場合
 */
async function checkTitleInput() {
  if (local.titleInput.value) return true
  const dialogResult = await showOr21815MsgOneBtn()
  switch (dialogResult) {
    case 'yes': {
      let label = ''
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (mo01334TypeReport.value.value === item.id && item.mo01337OnewayReport) {
            const data = item.mo01337OnewayReport as Mo01337OnewayType
            label = data.value
          }
        }
      }
      local.titleInput.value = label
      break
    }
  }
  return false
}

// ダイアログ表示フラグ
const showDialogOr26257 = computed(() => {
  // Or26257のダイアログ開閉状態
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})
/**
 * AC008: 「No.GUI00220_職員検索」画面を表示する。
 * '作成者の入力支援アイコン
 */
function getDataAuthor() {
  or26257Data.selectMode = Or26257Const.DEFAULT.SELECT_MODE_12
  // Or26257のダイアログ開閉状態を更新する
  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
  // Set open Popup1
  local.actionClickPopup = '1'
}

function onclickChoiceOne() {
  // TODO : AC009: 「No.GUI00094_事業所選択」画面を表示する。
  console.log('TODO : AC009: 「No.GUI00094_事業所選択」画面を表示する。')
}

function getPersonInChargeData() {
  or26257Data.selectMode = Or26257Const.DEFAULT.SELECT_MODE_12
  // Or26257のダイアログ開閉状態を更新する
  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
  // Set open Popup2
  local.actionClickPopup = '2'
}

function getRecorderData() {
  or26257Data.selectMode = Or26257Const.DEFAULT.SELECT_MODE_12
  // Or26257のダイアログ開閉状態を更新する
  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
  // Set open Popup3
  local.actionClickPopup = '3'
}

/**
 * 変更データを取得する
 *
 * @returns 出力帳票印刷情報リスト
 */
async function getDataTable() {
  const choPrtList = mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => {
    if (id === mo01334TypeReport.value.value) {
      return {
        ...rest,
        prtTitle: local.titleInput.value,
        prnDate: local.mo00039Type,
        param03: local.mo00018TypeChangeTitle.modelValue ? '0' : '1',
        param04: local.textInput.value,
        param05: local.mo00040FacilityName.modelValue ? '0' : '1',
        param06: localOneway.mo01338OneWayAuthorName.value,
        param07: local.mo00018TypePrintDuration.modelValue ? '0' : '1',
        param08: local.mo00018TypeFilterTypeOffer.modelValue ? '0' : '1',
        param09: local.mo00040SelectBusiness.modelValue,
        param10: local.mo00018TypeFilterPersonInCharge.modelValue ? '0' : '1',
        param11: localOneway.mo01338OneWayFilterPersonInChargeData.value,
        param12: local.mo00018TypeFilterByRecorder.modelValue ? '0' : '1',
        param13: localOneway.mo01338OneWayFilterByRecorderData.value
      } }
    return {
      ...rest,
    }
  }) as prtList[]

  refValue.value = { choPrtList: choPrtList }

  await nextTick()

  return choPrtList
}

/**
 * 閉じる
 */
async function close() {
  await checkTitleInput()

  const choPrtList = await getDataTable()

  if (isEdit.value) await printSettingsInfoUpdate(choPrtList)

  setState({ isOpen: false })
}

/**
 * 印刷
 */
async function print() {
  await checkTitleInput()

  if (!choPro.value) await showOr21813MsgOneBtn()

  const choPrtList = await getDataTable()

  if (isEdit.value) await printSettingsInfoUpdate(choPrtList)

  setState({ isOpen: false })
}

/**
 * 施設名
 * 画面.施設名値カラム = 1(※画面.出力帳票一覧明細に選択される行.パラメータ05の範囲が0-4以外)
 *
 * @param value - 施設名
 */
function validRange(value: number): string {
  return value >= 0 && value <= 4 ? String(value) : '1'
}

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

/**
 * AC016:「担当ケアマネ検索」ボタン押下
 */
watch(
  () => orX0145Type.value,
  (newValue) => {
    // TODO 担当ケアマネ有機体が未作成 「担当ケアマネプルダウン」選択変更がある場合
    console.log('担当ケアマネプルダウン', newValue)
  }
)

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334TypeReport.value.value,
  (newValue, oldValue) => {
    if (oldValue) {
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (oldValue === item.id) {
            if (local.titleInput.value) item.prtTitle = local.titleInput.value
            item.prnDate = local.mo00039Type
            // 画面.敬称を変更する = 画面.出力帳票一覧明細に選択される行.パラメータ03
            item.param03 = local.mo00018TypeChangeTitle.modelValue ? '0' : '1'
            // 画面.敬称 = 画面.出力帳票一覧明細に選択される行.パラメータ04
            item.param04 = local.textInput.value
            // 画面.施設名値カラム = 画面.出力帳票一覧明細に選択される行.パラメータ05
            // (※画面.出力帳票一覧明細に選択される行.パラメータ05の範囲が0-4)
            // 画面.施設名値カラム = 1(※画面.出力帳票一覧明細に選択される行.パラメータ05の範囲が0-4以外)
            item.param05 = local.mo00040FacilityName.modelValue
            item.param06 = localOneway.mo01338OneWayAuthorName.value
            item.param07 = local.mo00018TypePrintDuration.modelValue ? '0' : '1'
            item.param08 = local.mo00018TypeFilterTypeOffer.modelValue ? '0' : '1'
            localOneway.mo00040OnewaySelectBusiness.disabled = item.param08 !== '0' ? true : false
            localOneway.mo00009OnewayChoiceOneButton.disabled = item.param08 !== '0' ? true : false
            item.param09 = local.mo00040SelectBusiness.modelValue
            item.param10 = local.mo00018TypeFilterPersonInCharge.modelValue ? '0' : '1'
            localOneway.mo00009OnewayChoiceTwoButton.disabled = item?.param10 !== '0' ? true : false
            item.param11 = localOneway.mo01338OneWayFilterPersonInChargeData.value
            item.param12 = local.mo00018TypeFilterByRecorder.modelValue ? '0' : '1'
            localOneway.mo00009OnewayChoiceThreeButton.disabled = item?.param12 !== '0' ? true : false
            item.param13 = localOneway.mo01338OneWayFilterByRecorderData.value
          }
        }
      }
    }

    for (const item of mo01334OnewayReport.value.items) {
      if (item) {
        if (newValue === item.id) {
          local.titleInput.value = item?.prtTitle as string
          local.mo00039Type = item?.prnDate as string
          // 印刷オプションセクションの初期化を行う
          // 画面.敬称を変更する = 画面.出力帳票一覧明細に選択される行.パラメータ03
          local.mo00018TypeChangeTitle.modelValue = item?.param03 === '0' ? true : false
          // 画面.敬称 = 画面.出力帳票一覧明細に選択される行.パラメータ04
          local.textInput.value = item?.param04 as string
          // 画面.記入用シートを印刷する = 0(チェックオフ)
          local.mo00018TypePrintTheForm.modelValue = true
          // 画面.施設名値カラム = 画面.出力帳票一覧明細に選択される行.パラメータ05
          // (※画面.出力帳票一覧明細に選択される行.パラメータ05の範囲が0-4)
          // 画面.施設名値カラム = 1(※画面.出力帳票一覧明細に選択される行.パラメータ05の範囲が0-4以外)
          local.mo00040FacilityName.modelValue = String(validRange(Number(item.param05)))
          localOneway.mo01338OneWayAuthorName.value = item.param06 as string
          // 画面.期間を印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ07
          local.mo00018TypePrintDuration.modelValue = item?.param07 === '0' ? true : false
          // 画面.提供種別で絞り込む = 画面.出力帳票一覧明細に選択される行.パラメータ08
          // 画面.提供種別 = 画面.出力帳票一覧明細に選択される行.パラメータ09
          local.mo00018TypeFilterTypeOffer.modelValue = item?.param08 === '0' ? true : false
          localOneway.mo00040OnewaySelectBusiness.disabled = item?.param08 !== '0' ? true : false
          localOneway.mo00009OnewayChoiceOneButton.disabled = item?.param08 !== '0' ? true : false
          local.mo00040SelectBusiness.modelValue = item.param09 as string

          // 画面.担当者で絞り込む = 画面.出力帳票一覧明細に選択される行.パラメータ10
          // 画面.担当者値カラム = 画面.出力帳票一覧明細に選択される行.パラメータ11
          local.mo00018TypeFilterPersonInCharge.modelValue = item?.param10 === '0' ? true : false
          localOneway.mo00009OnewayChoiceTwoButton.disabled = item?.param10 !== '0' ? true : false
          localOneway.mo01338OneWayFilterPersonInChargeData.value = item.param11 as string

          // 画面.記録者で絞り込む = 画面.出力帳票一覧明細に選択される行.パラメータ12
          // 画面.記録者値カラム = 画面.出力帳票一覧明細に選択される行.パラメータ13
          local.mo00018TypeFilterByRecorder.modelValue = item?.param12 === '0' ? true : false
          localOneway.mo00009OnewayChoiceThreeButton.disabled = item?.param12 !== '0' ? true : false
          localOneway.mo01338OneWayFilterByRecorderData.value = item.param13 as string

          choPro.value = item?.choPro as string
        }
      }
    }
  }
)

/**
 * 「利用者選択方」ラジオボタン選択
 */
watch(
  () => local.mo00039TypeUserSelectType,
  (newValue) => {

    // 利用者選択方法が「単一」の場合
    if (Or35030Const.TANI === newValue) {
      localOneway.mo00018OneWayPrintTheForm.disabled = false
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
    } else {
      localOneway.mo00018OneWayPrintTheForm.disabled = true
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  (newValue) => {
    if (newValue?.clickFlg) {
      if (newValue.userList.length > 0) {
        local.userSelect = false
        local.selectUserId = newValue.userList[0].id

        local.userList = []
        for (const item of newValue.userList) {
          if (item) {
            local.userList.push({
              userId: item.userId,
              userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
            } as UserEntity)
          }
        }

        // 利用者選択方法が「単一」の場合
        if (Or35030Const.TANI === local.mo00039TypeUserSelectType) {
          // アセスメント履歴情報を取得する
          // await getPrintSettingsHistoryList()
        }
        // 利用者選択方法が「複数」の場合
        else if (Or35030Const.HUKUSUU === local.mo00039TypeUserSelectType) {
          // 基盤コンポーネントの再作成#147472 利用者一覧明細に前回選択された利用者が選択状態になる
        }
      } else {
        local.userList = []
      }
    } else {
      local.userList = []
    }
  }
)

/**
 * ラジオボタンの選択状態を追跡する
 */
watch(
  () => local.mo00039Type,
  async () => {
    await checkTitleInput()
  }
)

/**
 *  テーブルの変更を追跡する
 */
watch(
  () => mo01334OnewayReport.value.items,
  () => {
    const choPrtList = [
      ...mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => ({
        ...rest,
      })),
    ] as prtList[]
    refValue.value = { choPrtList: choPrtList }
  },
  { deep: true }
)

/**
 * 「No.GUI00220_職員検索」画面を表示する。
 * '職員名（姓）+' '+職員名（名）
 */
watch(
  () => or26257Type.value,
  () => {
    console.log('or26257Type.value', or26257Type.value);

    const fullName =
      or26257Type.value.shokuin?.shokuin1Knj && or26257Type.value.shokuin?.shokuin2Knj
    ? `${or26257Type.value.shokuin.shokuin1Knj} ${or26257Type.value.shokuin.shokuin2Knj}`
    : '';
    if (local.actionClickPopup === '1') {
      // AC008: 作成者の入力支援アイコン押下
      localOneway.mo01338OneWayAuthorName.value = fullName
    } else if (local.actionClickPopup === '2') {
      // AC010: 担当者の入力支援アイコン押下
      localOneway.mo01338OneWayFilterPersonInChargeData.value = fullName
    } else if (local.actionClickPopup === '3') {
      // AC011: 記録者の入力支援アイコン押下
      localOneway.mo01338OneWayFilterByRecorderData.value = fullName
    }
  }
)

/**
 * 「提供種別で絞り込む」チェックボックスがONの場合：活性
 *  上記以外：非活性
 */

watch(
  () => local.mo00018TypeFilterTypeOffer,
  (newValue) => {
    localOneway.mo00040OnewaySelectBusiness.disabled = !newValue.modelValue
    localOneway.mo00009OnewayChoiceOneButton.disabled = !newValue.modelValue
  }
)

/**
 * 「提供種別で絞り込む」チェックボックスがONの場合：活性
 *  上記以外：非活性
 */
watch(
  () => local.mo00018TypeFilterPersonInCharge,
  (newValue) => {
    localOneway.mo00009OnewayChoiceTwoButton.disabled = !newValue.modelValue
  }
)

/**
 * 「提供種別で絞り込む」チェックボックスがONの場合：活性
 *  上記以外：非活性
 */
watch(
  () => local.mo00018TypeFilterByRecorder,
  (newValue) => {
    localOneway.mo00009OnewayChoiceThreeButton.disabled = !newValue.modelValue
  }
)

</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or35030_screen"
      >
        <c-v-col
          cols="12"
          sm="2"
          class="pa-0 pt-2 pl-2 or35030_border_right"
        >
          <base-mo-01334
            v-model="mo01334TypeReport"
            :oneway-model-value="mo01334OnewayReport"
            class="list-wrapper"
          >
            <!-- 帳票 -->
            <template #[`item.prtTitle`]="{ item }">
              <base-mo01337 :oneway-model-value="item.mo01337OnewayReport"/>
            </template>
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="5"
          class="pa-0 pt-2 or35030_border_right content_center"
        >
          <c-v-row
            no-gutter
            class="or35030_row"
            style="margin-top: 10px !important"
          >
            <base-mo00610
              :oneway-model-value="localOneway.mo00610SealColumnBtnOneWay"
              class="mx-2 mb-2"
              @click.stop="onClickSealColumn()"
            />
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="or35030_row flex-center"
          >
            <c-v-col
              cols="12"
              sm="3"
              class="pa-0"
            >
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="9"
              class="pa-0"
            >
              <base-mo00045
                v-model="local.titleInput"
                :oneway-model-value="localOneway.mo00045OnewayTitleInput"
                @keyup.enter="checkTitleInput"
              />
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or35030_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              class="pa-0"
            >
              <!-- 印刷日付選択ラジオボタングループ -->
              <base-mo00039
                v-model="local.mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              id="test"
              cols="12"
              sm="5"
              class="pa-0"
            >
              <!-- 印刷日付ラベル -->
              <base-mo00020
                v-if="local.mo00039Type === '2'"
                v-model="local.mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
                @mousedown="checkTitleInput"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="printerOption customCol or35030_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pt-0 pb-0"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or35030_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 d-flex"
            >
              <!-- 敬称を変更する -->
              <base-mo00018
                v-model="local.mo00018TypeChangeTitle"
                :oneway-model-value="localOneway.mo00018OneWayChangeTitle"
              >
              </base-mo00018>
              <!-- 敬称を変更する -->
              <base-mo00045
                v-model="local.textInput"
                :oneway-model-value="localOneway.mo00045OnewayTextInput"
                :disabled="!local.mo00018TypeChangeTitle.modelValue"
              />
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <!-- 記入用シートを印刷する -->
              <base-mo00018
                v-model="local.mo00018TypePrintTheForm"
                :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              class="pa-0"
            >
              <!-- 施設名 -->
              <base-mo00040
                v-model="local.mo00040FacilityName"
                :oneway-model-value="localOneway.mo00040OnewayFacilityName"
              />
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 flex-center"
              style="margin-left: 10px"
            >
              <!-- 作成者 -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OnewayAuthorLabel"
                style="background-color: transparent; float: left"
              >
              </base-mo01338>
              <!-- 作成者 -->
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayAuthorButton"
                variant="flat"
                density="compact"
                @click="getDataAuthor"
              ></base-mo00009>
              <!-- 作成者 -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayAuthorName"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <!-- 期間を印刷する-->
              <base-mo00018
                v-model="local.mo00018TypePrintDuration"
                :oneway-model-value="localOneway.mo00018OneWayPrintDuration"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 flex-center"
            >
              <!-- 提供種別で絞り込む-->
              <base-mo00018
                v-model="local.mo00018TypeFilterTypeOffer"
                :oneway-model-value="localOneway.mo00018OneWayFilterTypeOffer"
              >
              </base-mo00018>
              <!-- 選択 -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OnewayChoiceOne"
                style="background-color: transparent; float: left"
              >
              </base-mo01338>
              <!-- 作成者 -->
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayChoiceOneButton"
                variant="flat"
                density="compact"
                @click="onclickChoiceOne"
              ></base-mo00009>
              <!-- SI032: '提供種別セレクトボックス -->
              <base-mo00040
                v-model="local.mo00040SelectBusiness"
                :oneway-model-value="localOneway.mo00040OnewaySelectBusiness"
              />
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 flex-center"
            >
              <!-- 担当者で絞り込む-->
              <base-mo00018
                v-model="local.mo00018TypeFilterPersonInCharge"
                :oneway-model-value="localOneway.mo00018OneWayFilterPersonInCharge"
                style="margin-right: 12px"
              >
              </base-mo00018>
              <!-- 選択 -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OnewayChoiceTwo"
                style="background-color: transparent; float: left"
              >
              </base-mo01338>
              <!-- 作成者 -->
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayChoiceTwoButton"
                variant="flat"
                density="compact"
                @click="getPersonInChargeData"
              ></base-mo00009>
              <!-- SI036: '担当者名ラベル -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayFilterPersonInChargeData"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 flex-center"
            >
              <!-- 記録者で絞り込む-->
              <base-mo00018
                v-model="local.mo00018TypeFilterByRecorder"
                :oneway-model-value="localOneway.mo00018OneWayFilterByRecorder"
                style="margin-right: 12px"
              >
              </base-mo00018>
              <!-- 選択 -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OnewayChoiceOne"
                style="background-color: transparent; float: left"
              >
              </base-mo01338>
              <!-- 作成者 -->
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayChoiceThreeButton"
                variant="flat"
                density="compact"
                @click="getRecorderData"
              ></base-mo00009>
              <!-- SI040: '記録者名ラベル -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayFilterByRecorderData"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="5"
          class="pa-0"
        >
          <c-v-row
            class="or35030_row"
            no-gutter
            style="align-items: center"
          >
            <c-v-col
              cols="12"
              sm="2"
              class="pa-0"
            >
              <!-- 利用者選択ラベル -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="4"
              class="pa-0"
            >
              <!-- 利用者選択ラジオボタングループ -->
              <base-mo00039
                v-model="local.mo00039TypeUserSelectType"
                :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
              >
              </base-mo00039>
            </c-v-col>
          </c-v-row>
          <c-v-row
            class="or35030_row"
            no-gutter
            style="align-items: center"
          >
            <!-- 期間ラベル -->
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 flex-center"
            >
              <!-- 期間ラベル -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPriodLabel"
                style="background-color: transparent"
              >
              </base-mo01338>
              <!-- 期間の入力支援アイコン -->
              <!-- <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayCareManagerSelection"
                variant="flat"
                density="compact"
                :disabled="true"
                @click="getCareManagerSelection"
              ></base-mo00009> -->
              <!-- 期間開始日ラベル -->
              <!-- 印刷日付ラベル -->
              <base-mo00020
                v-model="local.mo00020TypePeriodStartDate"
                :oneway-model-value="localOneway.mo00020OneWayPeriodStartDate"
                @mousedown="checkTitleInput"
              >
              </base-mo00020>
              <!-- ～ラベル -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPriodTildeLabel"
                style="background-color: transparent"
              ></base-mo01338>
              <!-- 期間終了ラベル日ラベル -->
              <base-mo00020
                v-model="local.mo00020TypePeriodEndDate"
                :oneway-model-value="localOneway.mo00020OneWayPeriodEndDate"
                @mousedown="checkTitleInput"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            class="or35030_row"
            no-gutter
            style="align-items: center; margin-left: 0px !important;"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 flex-center"
            >
              <!-- 担当ケアマネプルダウン -->
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
              ></g-custom-or-x-0145>
            </c-v-col>
          </c-v-row>
          <c-v-row
            style="margin: 4px;"
            no-gutter
          >
            <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="print()"
        >
        </base-mo00609>
        <g-custom-or-10016
          v-if="showDialogOr10016"
          v-bind="or10016"
          :oneway-model-value="or10016Data"
        />
        <g-custom-or-26257
          v-if="showDialogOr26257"
          v-bind="or26257"
          v-model="or26257Type"
          :oneway-model-value="or26257Data"
        />
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813 v-bind="or21813"> </g-base-or21813>
  <g-base-or21815 v-bind="or21815"> </g-base-or21815>
</template>

<style scoped lang="scss">
@use '@/styles/base.scss';
@use '@/styles/cmn/dialog-base.scss';
@use '@/styles/cmn/dialog-data-table-list.scss';

.or35030_screen {
  margin: -8px !important;
  overflow-x: hidden;
  max-width: 100%;
}

.or35030_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or35030_row {
  margin: 0px 0px 0px 4px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.user_class {
  margin-left: -12px;
}

:deep(.v-table__wrapper) {
  overflow-x: auto !important;
  overflow-y: scroll !important;
}

:deep(.itemClass) {
  .v-col {
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
}

.table-header {
  :deep(.v-table__wrapper) {
    height: 520px;

    .mo01338-footer {
      background-color: rgb(var(--v-theme-black-536));

      label {
        color: rgb(var(--v-theme-secondaryBackground));
      }
    }
  }
}

.min-width-65 {
  min-width: 65px;
}

.min-width-75 {
  min-width: 75px;
}

.min-width-120 {
  min-width: 120px;
}

.min-width-200 {
  min-width: 200px;
}
</style>
