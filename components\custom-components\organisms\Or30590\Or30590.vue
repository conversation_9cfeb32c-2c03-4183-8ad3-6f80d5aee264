<script setup lang="ts">
/**
 * Or30590:有機体:印刷設定
 * GUI01085_印刷設定
 *
 * @description
 * 印刷設定の処理
 *
 * <AUTHOR> DO AI QUOC
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, nextTick, computed } from 'vue'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import { OrX0130Const } from '../OrX0130/OrX0130.constants'
import { OrX0128Const } from '../OrX0128/OrX0128.constants'
import { OrX0130Logic } from '../OrX0130/OrX0130.logic'
import { OrX0128Logic } from '../OrX0128/OrX0128.logic'
import { Or30590Const } from './Or30590.constants'
import type { IPrintSetting, Or30590StateType } from './Or30590.type'
import type { Or30590OnewayType } from '~/types/cmn/business/components/Or30590Type'
import { useSetupChildProps, useScreenOneWayBind, useSystemCommonsStore, useCmnRouteCom, useReportUtils, dateUtils } from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '@/types/business/components/Mo01338Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00020Type, Mo00020OnewayType } from '@/types/business/components/Mo00020Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type {
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { CustomClass } from '~/types/CustomClassType'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { IChoPrtInfo, PrintSettingScreenInitialInfoSelectInEntity, PrintSettingScreenInitialInfoSelectOutEntity } from '~/repositories/cmn/entities/printSettingsScreenInitialInfoSelectGUI01085Entity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { IIniDataObject, LedgerInitializeDataSelectInEntity, LedgerInitializeDataSelectOutEntity } from '~/repositories/cmn/entities/ledgerInitializeDataSelectGUI01085Entity'
import type { BasicChecklistHistInfoSelectInEntity, BasicChecklistHistInfoSelectOutEntity, IRirekiInfo } from '~/repositories/cmn/entities/basicChecklistHistInfoSelectGUI01085Entity'
import type { Or21813EventType, Or21813StateType } from '~/components/base-components/organisms/Or21813/Or21813.type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type { PrintSettingsInfoUpdateInEntity } from '~/repositories/cmn/entities/PrintSettingsInfoUpdateGUI01085Entity'
import type { FilterForUserIdSelectInEntity, FilterForUserIdSelectOutEntity, IUserSelInfo } from '~/repositories/cmn/entities/filterForUserIdSelectGUI01085Entity'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { OrX0128Headers, OrX0128Items, OrX0128OnewayType } from '~/types/cmn/business/components/OrX0128Type'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import { reportOutputType } from '~/utils/useReportUtils'
import type { Or21814EventType, Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { BasicChecklistReportSelectInEntity, PrintSetEntity, PrintOptionEntity } from '~/repositories/cmn/entities/BasicChecklistReportSelectEntity'

/**
 * useI18n初期化
 */
const { t } = useI18n()

/**
 * システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()
const { reportOutput } = useReportUtils()
const { convertDateToSeireki } = dateUtils()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or30590OnewayType
  uniqueCpId: string
}
/**
 * 引継情報を取得する
 */
const props = defineProps<Props>()
/**
 * or21814子コンポーネント用変数
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 * or21813子コンポーネント用変数
 */
const or21813 = ref({ uniqueCpId: '' })
/**
 * orX0117子コンポーネント用変数
 */
const orX0117 = ref({ uniqueCpId: '' })
/**
 * orx0145子コンポーネント用変数
 */
const orx0145 = ref({ uniqueCpId: '' })
/**
 * orx0145子コンポーネント用変数
 */
const orX0130 = ref({ uniqueCpId: '' })
/**
 * orX0128子コンポーネント用変数
 */
const orX0128 = ref({ uniqueCpId: '' })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
})

/**
 * デフォルトの片道モデル値
 */
const defaultOnewayModelValue: Or30590OnewayType = {
  userList: [],
  userId: '',
  sectionName: '',
  index50on: '',
  vowel50on: '',
  histId: '',
  selectLedgerNumber: '',
  kikanFlg: '',
}

/**
 * 片道モデル値
 */
const localOneway = reactive({
  or30590: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // ダイアログ
  mo00024Oneway: {
    width: '1300px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or30590',
      toolbarTitle: t('label.print-set'),
      toolbarName: 'Or30590ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      cardTextClass: 'or30590_content',
    } as Mo01344OnewayType,
  } as Mo00024OnewayType,
  // タイトル
  mo00615OnewayTitle: {
    itemLabel: t('label.print-settings-title'),
  } as Mo00615OnewayType,
  // タイトル表示
  mo00045OnewayTitle: {
    itemLabel: '',
    showItemLabel: false,
    width: '220px',
  } as Mo00045OnewayType,
  mo01338OnewayTitle: {
    value: '',
  } as Mo01338OnewayType,
  // 出力帳票名一覧
  mo01334Oneway: {
    headers: [
      {
        title: t('label.ledge-name-left-sidebar'),
        key: 'ledgerName',
        sortable: false,
        minWidth: '100',
      },
    ],
    items: [],
    height: 655,
  } as Mo01334OnewayType,
  // 日付印刷区分
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  // 指定日
  mo00020OneWay: {
    showItemLabel: false,
    width: '125px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  // メモを印刷するチェックボックス
  mo00018OneWayPrintMemo: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-memo'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 被保険者番号を印刷するチェックボックス
  mo00018OneWayPrintInsuredPerson: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-insured-person'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 住所を印刷するチェックボックス
  mo00018OneWayPrintAddress: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-address'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 敬称を変更するチェックボックス
  mo00018OneWayPrintHonorificsChange: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 記入用シートを印刷するチェックボックス
  mo00018OneWayPrintEntrySheet: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // タイトルを変更するチェックボックス
  mo00018OneWayPrintTitleChange: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-title-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 記入日を印刷するチェックボックス
  mo00018OneWayPrintEntryDate: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-entry-date'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 生年月日を印刷するチェックボックス
  mo00018OneWayPrintBirthday: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-birthday'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 希望するサービス内容を印刷するチェックボックス
  mo00018OneWayPrintHopeService: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-hope-service-content'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 印刷オプション
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 利用者選択ラベル
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 履歴選択ラベル
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 利用者選択
  mo00039UserSelectTypeOneWay: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  // 履歴選択
  mo00039HistorySelectTypeOneWay: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  // 基準日: 表用テキストフィールド
  mo00020KijunbiOneWay: {
    itemLabel: t('label.base-date'),
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '300px',
  } as Mo00020OnewayType,
  // 担当ケアマネ
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: false,
    multiple: false,
    selectedUserCounter: '2',
    width: '160px',
  } as OrX0145OnewayType,
  // 閉じるボタン
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // PDFダウンロードボタン
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  mo00045PrintHonorificsChange: {
    class: 'text-filed',
    showItemLabel: false,
    customClass: {
      outerClass: 'mr-0',
    },
  } as Mo00045OnewayType,
  mo00045Title: {
    class: 'text-filed',
    showItemLabel: false,
    customClass: {
      outerClass: 'mr-0',
    },
  } as Mo00045OnewayType,
  // 利用者一覧
  orX0130Oneway: {
    selectMode: Or30590Const.DEFAULT.TANI,
    tableStyle: 'width:210px',
    // 指定行選択
    userId: '',
  } as OrX0130OnewayType,
  // 計画期間＆履歴一覧
  orX0128Oneway: {
    /**
     * 期間管理フラグ
     */
    kikanFlg: props.onewayModelValue.kikanFlg,
    /**
     * 単一複数フラグ(0:単一,1:複数)
     */
    singleFlg: Or30590Const.DEFAULT.TANI,
    tableStyle: '',
    headers: [
      { title: t('label.create-date'), key: 'createYmd', minWidth: '120px', sortable: false },
      { title: t('label.author'), key: 'shokuKnj', sortable: false },
      // { title: t('label.caseNo'), key: 'caseNo', minWidth: '200px', sortable: false },
      // { title: t('label.fiscal-year'), key: 'nendoY', minWidth: '80px', sortable: false },
      // { title: t('label.revision-1'), key: 'kaiteiFlg', minWidth: '80px', sortable: false },
    ] as OrX0128Headers[],
    items: [],
  } as OrX0128OnewayType,
  orX0117Oneway: {
    type: Or30590Const.DEFAULT.TANI,
    historyList: [] as OrX0117History[],
  } as OrX0117OnewayType
})

/**
 * 共通情報
 */
const commonInfoData = reactive({
  // 利用者ID
  userId: systemCommonsStore.getUserId ?? '',
  // 職員ID
  //shokuId: systemCommonsStore.getStaffId ?? '',
  shokuId: '1',
  // 法人ID
  houjinId: systemCommonsStore.getHoujinId ?? '',
  // アセスメントID
  assessmentId: '',
  // 親画面.利用者情報リストにデータを選択フラゲ
  parentUserIdSelectDataFlag: false,
  // セクション番号
  sectionNo: '',
  // プリントナンバー
  prtNo: '',
  // 処理年月日
  systemDate: systemCommonsStore.getSystemDate ?? '',
  // 担当者ID
  tantoId: systemCommonsStore.getManagerId ?? '',
  // 事業所ID
  //svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
  svJigyoId: '26',
  // システムコード
  //sysCode: systemCommonsStore.getSystemCode ?? '',
  sysCode: '71101',
  // システム略称
  // sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '', // TODO
  sysRyaku: '3GK',
  // 施設ID
  //shisetuId: systemCommonsStore.getShisetuId ?? '',
  shisetuId: '7',
  // 処理年月日
  //processDate: systemCommonsStore.getProcessDate ?? ''
  processDate: '2025/04/01',
  // 共通情報.担当ケアマネ設定フラグ
  // kkjTantoFlg: cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? '',
  kkjTantoFlg: '1',
})

/**
 * ロカール変数
 */
const local = reactive({
  // 担当ケアマネ
  orX0145: {} as OrX0145Type,
  /**
   * 選択利用者ID
   */
  selectUserId: localOneway.or30590.userId,
  /**
   * 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
   */
  userNoSelect: false,
  /**
   * 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
   */
  historyNoSelect: false,
  /**
   * 履歴選択の明細
   */
  orX0128DetList: [] as OrX0128Items[],
  /**
   * 帳票ID
   */
  reportId: '',
  /**
   * 事業所名
   */
  svJigyoKnj: '', // TODO
  /**
   * タイトル
   */
  title: ''
})

/**
 * ロカール変数
 */
const localData = reactive({
  // 敬称を変更する
  parameter03: {
    modelValue: false
  } as Mo00018Type,
  // 敬称
  parameter04: {
    value: ''
  } as Mo00045Type,
  // 記入用シートを印刷する = 0(チェックオフ)
  mo00018Type: {
    modelValue: false
  } as Mo00018Type,
  // メモを印刷する
  parameter05: {
    modelValue: false
  } as Mo00018Type,
  // 被保険者番号を印刷する
  parameter06: {
    modelValue: false
  } as Mo00018Type,
  // 住所を印刷する
  parameter07: {
    modelValue: false
  } as Mo00018Type,
  // タイトルを変更する
  parameter08: {
    modelValue: false
  } as Mo00018Type,
  // タイトル
  parameter09: {
    value: ''
  } as Mo00046Type,
  // 記入日を印刷する
  parameter10: {
    modelValue: false
  } as Mo00018Type,
  // 生年月日を印刷する
  parameter11: {
    modelValue: false
  } as Mo00018Type,
  // 希望するサービス内容を印刷する
  parameter12: {
    modelValue: false
  } as Mo00018Type,
})

/**************************************************
 * 変数定義
 **************************************************/
/**
 * ダイアログ 双方向バインド
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or30590Const.DEFAULT.IS_OPEN,
})

/**
 * 印刷帳票情報
 */
const printSettingData = ref<IPrintSetting>({} as IPrintSetting)
/**
 * 印刷帳票情報Map
 */
const choPrtMapData = ref<Record<string, IChoPrtInfo>>({});

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 日付印刷区分
 */
const mo00039Type = ref<string>('')

/**
 * 指定日
 */
const mo00020Type = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 利用者選択
 */
const mo00039UserSelectType = ref<string>('')

/**
 * 履歴選択
 */
const mo00039HistorySelectType = ref<string>('')

/**
 * 基準日
 */
const mo00020TypeKijunbi = ref<Mo00020Type>({
  value: '2024/11/11',
} as Mo00020Type)

/**
 * 指定日非表示/表示フラゲ
 */
const mo00020Flag = ref<boolean>(false)
/**
 * 基準日フラゲ
 */
const kijunbiFlag = ref<boolean>(false)
/**
 * 履歴一覧セクションフラゲ
 */
const mo01334TypeHistoryFlag = ref<boolean>(false)
/**
 * 担当ケアマネ選択非表示/表示フラゲ
 */
const tantoSelectDisplayFlag = ref<boolean>(true)

/**
 * 利用者列幅
 */
const userSelectCols = ref<number>(4)

/**
 * 帳票イニシャライズデータ
 */
const reportIniData = ref<IIniDataObject>({
  // 氏名伏字印刷
  prtName: '',
  // 文書番号印刷
  prtBng: '',
  // 個人情報印刷
  prtKojin: '',
})

/**
 * 作成者Map
 */
const shokuinMap = ref<Record<string, string>>({})

/**
 * 初期情報取得フラゲ
 */
const initFlag = ref<boolean>(false)

/**
 * 確認ダダイアログのPromise
 */
let or21814ResolvePromise: (value: Or21814EventType) => void

/**
 * エラーダダイアログのPromise
 */
let or21813ResolvePromise: (value: Or21813EventType) => void

/**************************************************
 * Pinia
 **************************************************/

/**
 * 自身のPinia領域に設定する処理
 */
const { setState } = useScreenOneWayBind<Or30590StateType>({
  cpId: Or30590Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 開閉フラグ
     *
     * @param value - isOpen
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or30590Const.DEFAULT.IS_OPEN
    },
    /**
     * 期間管理フラグ
     *
     * @param vallue - 期間管理フラグ
     */
    kikanFlg: (vallue) => {
      localOneway.or30590.kikanFlg = vallue ?? '0'
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 共通関数より汎用コード一覧を取得する。
  await initCodes()

  // 初期情報取得
  await initOr30590()
})

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )

  // 日付印刷区分一覧が存在した場合
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    localOneway.mo00039OneWay.items = bizukePrintCategoryCodeTypes as Mo00039Items[]
  }

  // 日単複数選択区分
  const tanMultipleSelectCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 日単複数選択区分一覧が存在した場合
  if (tanMultipleSelectCategoryCodeTypes?.length > 0) {
    mo00039UserSelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    mo00039HistorySelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    localOneway.mo00039HistorySelectTypeOneWay.items = tanMultipleSelectCategoryCodeTypes as Mo00039Items[]
    localOneway.mo00039UserSelectTypeOneWay.items = tanMultipleSelectCategoryCodeTypes as Mo00039Items[]
  }
}

/**
 * 画面初期化
 */
const initOr30590 = async () => {
  let tantoId = '0'
  // 共通情報.担当ケアマネ設定フラグ > 0、且つ、共通情報.担当者IDが0以外の場合
  if (
    systemCommonsStore.getManagerId !== Or30590Const.DEFAULT.STR.ZERO &&
    Number(commonInfoData.kkjTantoFlg) > Or30590Const.DEFAULT.NUMBER.ZERO
  ) {
    // 共通情報.担当者ID
    tantoId = commonInfoData.tantoId
  }
  // バックエンドAPIから初期情報取得
  const inputData: PrintSettingScreenInitialInfoSelectInEntity = {
    sysCd: commonInfoData.sysCode,
    sysRyaku: commonInfoData.sysRyaku,
    kinounameKnj: 'PRT',
    houjinId: commonInfoData.houjinId,
    shisetuId: commonInfoData.shisetuId,
    svJigyoId: commonInfoData.svJigyoId,
    userId: localOneway.or30590.userId,
    shokuId: commonInfoData.shokuId,
    tantoId: '138',
    sectionName: localOneway.or30590.sectionName,
    choIndex: localOneway.or30590.selectLedgerNumber,
    kojinhogoFlg: '0',
    sectionAddNo: '0',
    kikanFlg: localOneway.or30590.kikanFlg,
    startYmd: commonInfoData.processDate,
    endYmd: '',
  }
  const ret: PrintSettingScreenInitialInfoSelectOutEntity = await ScreenRepository.select(
    'printSettingsScreenInitialInfoSelectGUI01085',
    inputData
  )
  // 画面に初期化データを設定する
  await setScreenData(ret)
}

/**
 * 画面に初期化データを設定する
 *
 * @param ret - レスポンス
 */
const setScreenData = async (ret: PrintSettingScreenInitialInfoSelectOutEntity) => {
  const retData = ret.data
  // 印刷設定情報を設定する
  printSettingData.value = ret.data

  // 作成者Map設定
  if(retData.shokuList.length) {
    shokuinMap.value = retData.shokuList.reduce((acc, item) => {
      acc[item.chkShokuId] = item.shokuinKnj
      return acc
    }, {} as Record<string, string>)
  }

  // 帳票共通部セクションの初期化を行う
  if(printSettingData.value.choPrtList.length) {
    // 印刷帳票一覧設定
    const mo01334OnewayList: Mo01334Items[] = printSettingData.value.choPrtList.map(x => ({
      id: x.prtNo,
      mo01337OnewayLedgerName: {
        value: x.prtTitle,
        unit: Or30590Const.DEFAULT.STR.EMPTY
      } as Mo01337OnewayType,
      selectable: true,
      prtNo: x.prtNo,
    } as Mo01334Items))

    // 出力帳票名一覧設定
    localOneway.mo01334Oneway.items = mo01334OnewayList

    // 親画面で選択されている帳票インデックスに対応するレコードを、出力帳票一覧明細で選択状態にする
    if(localOneway.or30590.selectLedgerNumber) {
      mo01334Type.value.value = localOneway.or30590.selectLedgerNumber
    } else {
      mo01334Type.value.value = printSettingData.value.choPrtList[0].prtNo
    }

    // 印刷帳票一覧Map
    choPrtMapData.value = printSettingData.value.choPrtList.reduce(
      (acc, item) => {
        acc[item.prtNo] = item;
        return acc;
      },
      {} as Record<string, IChoPrtInfo>
    );
  }

  // 担当セクションの初期化を行う
  // 共通情報.処理年月日が""の場合
  if(commonInfoData.systemDate === Or30590Const.DEFAULT.STR.EMPTY) {
    tantoSelectDisplayFlag.value = false
  } else {
    // 共通情報.担当ケアマネ設定フラグ > 0、かつ、共通情報.担当者IDが0以外の場合
    if(Number(commonInfoData.kkjTantoFlg) > 0 && commonInfoData.tantoId !== Or30590Const.DEFAULT.STR.ZERO) {
      // 画面.担当ケアマネ選択を非活性にする
      // 画面.担当ケアマネを非活性にする
      localOneway.orX0145Oneway.disabled = true
      // 画面.担当ケアマネに共通情報.担当者IDを設定する
      localOneway.orX0145Oneway.selectedUserCounter = commonInfoData.tantoId
    } else {
      // 画面.担当ケアマネ選択を活性表示にする
      // 画面.担当ケアマネを活性表示にする
      localOneway.orX0145Oneway.disabled = false
      // 画面.担当ケアマネに""を設定する
      localOneway.orX0145Oneway.selectedUserCounter = ''
    }
  }

  // TODO
  // 利用者一覧セクションの初期化を行う

  // TODO
  // 引継情報.50音行番号、引継情報.50音母音、OUTPUT情報.利用者絞込リストより、利用者一覧明細データをフィルターする

  // 画面.利用者一覧明細にOUTPUT情報.利用者IDが存在する場合
  localOneway.orX0130Oneway.userId = localOneway.or30590.userId

  initFlag.value = true
}

/**
 * アセスメント履歴一覧データ
 */
const getHistoryData = async () => {
  // 利用者選択方法が「単一」の場合
  if(mo00039UserSelectType.value === Or30590Const.DEFAULT.TANI) {
    // アセスメント履歴情報を取得する。
    const inputData: BasicChecklistHistInfoSelectInEntity = {
      kikanFlg: localOneway.or30590.kikanFlg,
      svJigyoId: commonInfoData.svJigyoId,
      userId: local.selectUserId
    }
    const ret: BasicChecklistHistInfoSelectOutEntity = await ScreenRepository.select(
      'basicChecklistHistInfoSelectGUI01085',
      inputData
    )

    if (Or30590Const.DEFAULT.KIKAN_FLG_1 === localOneway.or30590.kikanFlg) {
      const tempList: string[] = [] as string[]
      let list: OrX0128Items[] = []
      for (const item of ret.data.rirekiList) {
        if (item) {
          const planPeriod =
            t('label.plan-period') +
            Or30590Const.DEFAULT.STR.SPLIT_COLON +
            item.startYmd +
            Or30590Const.DEFAULT.STR.SPLIT_TILDE +
            item.endYmd
          if (!tempList.includes(planPeriod)) {
            const historyList: OrX0128Items[] = []
            for (const data of ret.data.rirekiList) {
              const dataPlanPeriod =
                t('label.plan-period') +
                Or30590Const.DEFAULT.STR.SPLIT_COLON +
                data.startYmd +
                Or30590Const.DEFAULT.STR.SPLIT_TILDE +
                data.endYmd
              if (planPeriod === dataPlanPeriod) {
                historyList.push({
                  id: data.nenkan1Id,
                  sc1Id: data.sc1Id,
                  startYmd: data.startYmd,
                  endYmd: data.endYmd,
                  createYmd: data.createYmd,
                  shokuKnj: data.shokuinKnj,
                  caseNo: data.caseNo,
                  nendoY: data.nendoY,
                  kaiteiFlg: data.kaiteiFlg,
                } as OrX0128Items)
              }
            }
            if (historyList.length > 0) {
              list.push({
                sc1Id: item.sc1Id,
                startYmd: item.startYmd,
                endYmd: item.endYmd,
                isPeriodManagementMergedRow: true,
                planPeriod:
                  t('label.plan-period') +
                  Or30590Const.DEFAULT.STR.SPLIT_COLON +
                  item.startYmd +
                  Or30590Const.DEFAULT.STR.SPLIT_TILDE +
                  item.endYmd,
                id: Or30590Const.DEFAULT.STR.EMPTY,
              } as OrX0128Items)
              list = list.concat(historyList)
              tempList.push(planPeriod)
            }
          }
        }
      }
      list.forEach((item, index) => {
        item.id = String(++index)
      })
      localOneway.orX0128Oneway.items = list
      // 画面.利用者一覧明細に親画面.利用者IDが存在する場合
      if (localOneway.or30590.histId) {
        // 親画面.アセスメントIDを対するレコードを選択状態にする
        localOneway.orX0128Oneway.initSelectId = (
          list.findIndex((item) => item.id === localOneway.or30590.histId) + 1
        ).toString()
      }
    } else {
      const list: OrX0128Items[] = []
      for (const data of ret.data.rirekiList) {
        if (data) {
          list.push({
            id: data.nenkan1Id,
            sc1Id: data.sc1Id,
            startYmd: data.startYmd,
            endYmd: data.endYmd,
            createYmd: data.createYmd,
            shokuKnj: data.shokuinKnj,
            caseNo: data.caseNo,
            nendoY: data.nendoY,
            kaiteiFlg: data.kaiteiFlg,
          } as OrX0128Items)
        }
      }
      localOneway.orX0128Oneway.items = list
      // 履歴一覧明細に親画面.履歴IDが存在する場合
      if (localOneway.or30590.histId) {
        // 画面.履歴一覧明細に親画面.履歴IDを対するレコードを選択状態にする
        localOneway.orX0128Oneway.initSelectId = (
          list.findIndex((item) => item.id === localOneway.or30590.histId) + 1
        ).toString()
      }
    }
  }
}

/**
 * 画面の印刷設定情報を保存する
 */
const save = async () => {
  const reportList: IChoPrtInfo[] = []
  for (const key in choPrtMapData.value) {
    reportList.push(choPrtMapData.value[key])
  }
  // バックエンドAPIから印刷設定情報保存
  const inputData: PrintSettingsInfoUpdateInEntity = {
    sysCd: commonInfoData.sysCode,
    sysRyaku: '3gk',
    kinounameKnj: 'PRT',
    houjinId: commonInfoData.houjinId,
    svJigyoId: commonInfoData.svJigyoId,
    shisetuId: commonInfoData.shisetuId,
    shokuId: commonInfoData.shokuId,
    choPro: choPrtMapData.value[mo01334Type.value.value].choPro,
    kojinhogoFlg: '0',
    sectionAddNo: '0',
    choPrtList: reportList,
    iniDataObject: reportIniData.value
  }
  await ScreenRepository.update(
    'printSettingsInfoUpdateGUI01085',
    inputData
  )
}

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  // 画面の印刷設定情報を保存する。
  await save()

  // 本画面を閉じ、親画面に返却する。
  setState({ isOpen: false, kikanFlg: '' })
}

/**
 * 帳票ID設定
 *
 * @param prtNo - 帳票番号
 */
const setReportId = (prtNo: string) => {
  switch (prtNo) {
    case Or30590Const.DEFAULT.STR.ONE:
      local.reportId = Or30590Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.BASIC_CHECKLIST_REPORT
      break
    case Or30590Const.DEFAULT.STR.TWO:
      local.reportId = Or30590Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.BASIC_CHECKLIST_NO_ITEM_REPORT
      break
    default:
      local.reportId = Or30590Const.DEFAULT.STR.EMPTY
      break
  }
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const downloadPdf = async () => {
  // 利用者選択方法が「単一」
  if (Or30590Const.DEFAULT.TANI === mo00039UserSelectType.value) {
    // 履歴選択が「単一」
    if (Or30590Const.DEFAULT.TANI === mo00039HistorySelectType.value) {
      localOneway.orX0117Oneway.type = Or30590Const.DEFAULT.STR.ONE
      // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
      if (local.userNoSelect) {
        // メッセージを表示
        const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11393'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
        })
        // はい
        if (dialogResult.firstBtnClickFlg) {
          // 処理終了
          return
        }
      }

      // 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
      if (local.historyNoSelect) {
        // メッセージを表示
        const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11455'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
        })
        // はい
        if (dialogResult.firstBtnClickFlg) {
          // 処理終了
          return
        }
      }

      // 履歴情報リストにデータを選択する場合
      if (!local.historyNoSelect) {
        // 帳票側の処理を呼び出し、帳票レイアウトのみ印刷する
        const inputData: BasicChecklistReportSelectInEntity = {
          houjinId: systemCommonsStore.getHoujinId ?? '',
          title: localData.parameter08.modelValue ? (localData.parameter09.value ?? '') : local.title,
          userId: local.selectUserId,
          rirekiId: local.orX0128DetList[0].id,
          memoPrtFlg: localData.parameter05.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.ZERO,
          hokenNoPrtFlg: localData.parameter06.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.ZERO,
          kiboSvPrtFlg: localData.parameter12.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.ZERO,
          addressPrtFlg: localData.parameter07.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.ZERO,
          createYmdPrtFlg: localData.parameter10.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.ZERO,
          birthdayYmdPrtFlg: localData.parameter11.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.ZERO,
          svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
          svJigyoKnj: local.svJigyoKnj,
          createYmd: '',
          printSet: {
            shiTeiKubun: mo00039Type.value,
            shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
          } as PrintSetEntity,
          printOption: {
            emptyFlg: localData.mo00018Type.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.ZERO,
            keishoFlg: localData.parameter03.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.ZERO,
            keishoKnj: localData.parameter04.value
          } as PrintOptionEntity
        }
        // 帳票出力
        await reportOutput(local.reportId, inputData, reportOutputType.DOWNLOAD)
        return
      }
    }
    // 履歴選択方法が「複数」
    else if (Or30590Const.DEFAULT.HUKUSUU === mo00039HistorySelectType.value) {
      // 履歴一覧が0件選択場合（※履歴リストが0件、複数件を含む）
      if (local.historyNoSelect) {
        // メッセージを表示
        const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11455'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
        })
        // はい
        if (dialogResult.firstBtnClickFlg) {
          // 処理終了
          return
        }
      }
      // 履歴情報リストにデータを選択する場合
      else {
        // 印刷設定情報リストを作成
      }
    }
  }

  // 利用者選択方法が「複数」の場合
  if (Or30590Const.DEFAULT.HUKUSUU === mo00039UserSelectType.value) {
    localOneway.orX0117Oneway.type = Or30590Const.DEFAULT.STR.ONE
    // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
    if (local.userNoSelect) {
      // メッセージを表示
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11393'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if (dialogResult.firstBtnClickFlg) {
        // 処理終了
        return
      }
    }
    // 利用者情報リストにデータを選択する場合
    else {
      // 印刷設定情報リストを作成
    }
  }

  // AC019-2と同じ => 画面の印刷設定情報を保存する
  await save()

  // 選択された帳票のプロファイルが””の場合
  if (!localOneway.mo01338OnewayTitle.value) {
    // メッセージを表示
    const dialogResult = await openErrorDialog(or21813.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // はい
    if (dialogResult.firstBtnClickFlg) {
      // 処理終了
      return
    }
  }

  // 「AC020-5-2 また AC020-5-3」で取得した印刷設定情報リスト＞0件の場合
  if (localOneway.orX0117Oneway.historyList.length > 0) {
    // OrX0117のダイアログ開閉状態を更新する
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 確認ダイアログ開く
 *
 * @param uniqueCpId - uniqueCpId
 *
 * @param state - 確認ダイアログOneWayBind領域
 */
function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    or21814ResolvePromise = resolve
  })
}

/**
 * エラーダイアログ開く
 *
 * @param uniqueCpId - uniqueCpId
 *
 * @param state - エラーダイアログOneWayBind領域
 */
function openErrorDialog(uniqueCpId: string, state: Or21813StateType) {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21813EventType>((resolve) => {
    or21813ResolvePromise = resolve
  })
}

watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.or30590 = {
      ...newValue
    }
  },
  {

    deep: true,
    immediate: true
  }
)

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    await nextTick()
    if (or21814ResolvePromise !== undefined && newValue !== undefined) {
      or21814ResolvePromise(newValue)
    }
    return
  }
)

/**
 * Or21813のイベントを監視
 *
 * @description
 * またOr21813のボタン押下フラグをリセットする。
 */
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  async (newValue) => {
    await nextTick()
    if (or21813ResolvePromise !== undefined && newValue !== undefined) {
      or21813ResolvePromise(newValue)
    }
    return
  }
)

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334Type.value.value,
  async (newValue, oldValue) => {
    // ①帳票タイトル :  出力帳票一覧明細に選択される行.帳票タイトル
    let label = ''
    for (const item of localOneway.mo01334Oneway.items) {
      if (item) {
        if (newValue === item.id && item.mo01337OnewayLedgerName) {
          const data = item.mo01337OnewayLedgerName as Mo01337OnewayType
          label = data.value
          commonInfoData.prtNo = item.id
          // 帳票ID
          setReportId(item.id)
        }
      }
    }
    // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
    localOneway.mo01338OnewayTitle.value = label

    if(oldValue) {
      choPrtMapData.value[oldValue] = {
        ...choPrtMapData.value[oldValue],
        param03: localData.parameter03.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
        param04: localData.parameter04.value,
        param05: localData.parameter05.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
        param06: localData.parameter06.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
        param07: localData.parameter07.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
        param08: localData.parameter08.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
        param09: localData.parameter09.value ?? Or30590Const.DEFAULT.STR.EMPTY,
        param10: localData.parameter10.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
        param11: localData.parameter11.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
        param12: localData.parameter12.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
        prndate: mo00039Type.value
      }
    }

    // 出力帳票一覧明細に選択される行を取得する
    const selectedReport: IChoPrtInfo | undefined = choPrtMapData.value[newValue]

    if(selectedReport) {
      // 日付印刷区分情報を設定する
      mo00039Type.value = selectedReport.prndate

      // 印刷オプションセクションの初期化を行う
      localData.parameter03.modelValue = selectedReport.param03 === Or30590Const.DEFAULT.STR.ONE
      localData.parameter04.value = selectedReport.param04
      localData.parameter05.modelValue = selectedReport.param05 === Or30590Const.DEFAULT.STR.ONE
      localData.parameter06.modelValue = selectedReport.param06 === Or30590Const.DEFAULT.STR.ONE
      localData.parameter07.modelValue = selectedReport.param07 === Or30590Const.DEFAULT.STR.ONE
      localData.parameter08.modelValue = selectedReport.param08 === Or30590Const.DEFAULT.STR.ONE
      localData.parameter09.value = selectedReport.param09
      localData.parameter10.modelValue = selectedReport.param10 === Or30590Const.DEFAULT.STR.ONE
      localData.parameter11.modelValue = selectedReport.param11 === Or30590Const.DEFAULT.STR.ONE
      localData.parameter12.modelValue = selectedReport.param12 === Or30590Const.DEFAULT.STR.ONE

      // タイトル設定
      local.title = selectedReport.param09
    }

    // 帳票イニシャライズデータ取得ためのパラメータ情報を設定する
    const inputData: LedgerInitializeDataSelectInEntity = {
      sysCd: commonInfoData.sysCode,
      kinounameKnj: 'PRT',
      shokuId: commonInfoData.shokuId,
      sectionName: selectedReport.choPro,
      kojinhogoFlg: '0',
      sectionAddNo: '0'
    }
    // 帳票イニシャライズデータ取得する
    const ret: LedgerInitializeDataSelectOutEntity = await ScreenRepository.select(
      'ledgerInitializeDataSelectGUI01085',
      inputData
    )

    // 帳票イニシャライズデータ設定
    reportIniData.value.prtBng = ret.data.iniDataObject.prtBng
    reportIniData.value.prtName = ret.data.iniDataObject.prtName
    reportIniData.value.prtKojin = ret.data.iniDataObject.prtKojin
  }
)

/**
 * 印刷オプション設定の監視
 */
watch(
  () => localData,
  (newValue) => {
    if(!mo01334Type.value.value) return
    choPrtMapData.value[mo01334Type.value.value] = {
      ...choPrtMapData.value[mo01334Type.value.value],
      param03: newValue.parameter03.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
      param04: newValue.parameter04.value,
      param05: newValue.parameter05.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
      param06: newValue.parameter06.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
      param07: newValue.parameter07.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
      param08: newValue.parameter08.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
      param09: newValue.parameter09.value ?? Or30590Const.DEFAULT.STR.EMPTY,
      param10: newValue.parameter10.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
      param11: newValue.parameter11.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
      param12: newValue.parameter12.modelValue ? Or30590Const.DEFAULT.STR.ONE : Or30590Const.DEFAULT.STR.EMPTY,
      prndate: mo00039Type.value
    }
  },
  {
    deep: true
  }
)

/**
 * 「利用者選択方法」ラジオボタン選択
 */
watch(
  () => mo00039UserSelectType.value,
  (newValue) => {
    // 利用者選択方法が「単一」の場合
    if (Or30590Const.DEFAULT.TANI === newValue) {
      localOneway.orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      localOneway.orX0130Oneway.tableStyle = 'width: 210px'
      // 利用者列幅設定
      userSelectCols.value = 4
      mo01334TypeHistoryFlag.value = true

      // 基準日選択が非表示
      kijunbiFlag.value = false
    } else {
      localOneway.orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      localOneway.orX0130Oneway.tableStyle = 'width: 430px'
      // 利用者列幅設定
      userSelectCols.value = 12
      mo01334TypeHistoryFlag.value = false

      // 基準日選択が表示
      kijunbiFlag.value = true
    }
  }
)

/**
 * 日付印刷区分編集の監視
 */
watch(
  () => mo00039Type.value,
  (newValue) => {
    // 日付印刷区分が2の場合、指定日と指定日選択を活性表示にする
    if(Or30590Const.DATE_DISPLAY.TWO === newValue) {
      mo00020Flag.value = true
    } else {
      // 定日選択を非表示にする
      mo00020Flag.value = false
    }
  }
)

/**
 * 「基準日」変更
 */
watch(
  () => mo00020TypeKijunbi.value.value,
  (newValue) => {
    // TODO 入力した日付が空、もしくは不正な日付の場合
    // 変更前の日付に戻
    console.log(newValue)
  }
)

/**
 * 「履歴選択方法」ラジオボタン押下
 */
watch(
  () => mo00039HistorySelectType.value,
  (newValue) => {
    // 履歴選択方法が「単一」の場合
    if (Or30590Const.DEFAULT.TANI === newValue) {
      localOneway.orX0128Oneway.singleFlg = Or30590Const.DEFAULT.TANI
    } else {
      localOneway.orX0128Oneway.singleFlg = Or30590Const.DEFAULT.HUKUSUU
    }
  }
)

/**
 * 「希望するサービス内容を印刷する」チェックの監視
 */
watch(
  () => localData.parameter12,
  (newValue) => {
    if(newValue === undefined) return
    // チェックの場合、メモを印刷するチェックボックスが非活性
    if(newValue.modelValue) {
      localOneway.mo00018OneWayPrintMemo.disabled = true
    } else {
      // メモを印刷するチェックボックスが活性
      localOneway.mo00018OneWayPrintMemo.disabled = false
    }
  },
  {
    deep: true
  }
)

/**
 * 「敬称を変更するチェックボックス」チェックの監視
 */
watch(
  () => localData.parameter03,
  (newValue) => {
    if(newValue === undefined) return
    // チェックの場合、敬称テキストボックスが活性
    if(newValue.modelValue) {
      localOneway.mo00045PrintHonorificsChange.disabled = false
    } else {
      // それ以外の場合、非活性
      localOneway.mo00045PrintHonorificsChange.disabled = true
    }
  },
  {
    deep: true,
    immediate: true
  }
)

// 利用者選択と履歴選択編集の監視
watch(
  [() => mo00039UserSelectType.value, () => mo00039HistorySelectType.value],
  ([userSelected, histSelected]) => {
    // 利用者選択=「”単一”」かつ履歴選択＝「”単一”」の場合
    if(Or30590Const.DEFAULT.TANI === userSelected && Or30590Const.DEFAULT.TANI === histSelected) {
      // 記入用シートを印刷するチェックボックスが活性
      localOneway.mo00018OneWayPrintEntrySheet.disabled = false
    } else {
      // それ以外の場合、非活性
      localOneway.mo00018OneWayPrintEntrySheet.disabled = true
    }
  },
  {
    deep: true
  }
)

/**
 * タイトルを変更するチェックボックス」チェックするの監視
 */
watch(
  () => localData.parameter08,
  (newValue) => {
    if(newValue === undefined) return
    // チェックの場合、タイトル テキストボックスが活性
    if(newValue.modelValue) {
      localOneway.mo00045Title.disabled = false
    } else {
      // それ以外の場合、非活性
      localOneway.mo00045Title.disabled = true
    }
  },
  {
    deep: true,
    immediate: true
  }
)

/**
 * 「メモを印刷する」チェックするの監視
 */
watch(
  () => localData.parameter05,
  (newValue) => {
    if(newValue === undefined) return
    // チェックの場合、希望するサービス内容を印刷するチェックボックスが活性
    if(newValue.modelValue) {
      localOneway.mo00018OneWayPrintHopeService.disabled = true
    } else {
      // それ以外の場合、非活性
      localOneway.mo00018OneWayPrintHopeService.disabled = false
    }
  },
  {
    deep: true
  }
)

/**
 * 利用者選択の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickFlg) {
      console.log('----------user list: ', newValue.userList);
      // 利用者を選択した場合、この画面に対応するデータの初期化APIを呼び出す
      if (newValue.userList.length > 0) {
        local.userNoSelect = false
        local.selectUserId = newValue.userList[0].userId
        // 選択された利用者のデータ初期化APIを再度呼び出す
        await getHistoryData()
      } else {
        local.userNoSelect = true
      }
    }
  }
)

/**
 * 「履歴選択」の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      if (newValue.historyDetClickFlg) {
        local.orX0128DetList = newValue.orX0128DetList
        if (newValue.orX0128DetList.length > 0) {
          local.historyNoSelect = false
        } else {
          local.historyNoSelect = true
        }
      } else {
        local.historyNoSelect = true
      }
    }
  }
)

/**
 * 担当ケアマネ更新の監視
 */
watch(
  () => local.orX0145.value,
  async (newValue) => {
    let chkShokuIdNewValue = ''
    if(newValue) {
      chkShokuIdNewValue = (newValue as unknown as TantoCmnShokuin).chkShokuId
    }

    // // 利用者一覧明細データをフィルターする
    // let userSelList: IUserSelInfo[]  = []
    // // 画面.担当ケアマネが空白以外の場合
    // if(chkShokuIdNewValue !== '') {
    //   const inputParam: FilterForUserIdSelectInEntity = {
    //     tantoId: chkShokuIdNewValue,
    //     startYmd: commonInfoData.systemDate,
    //     endYmd: ''
    //   }
    //   // 担当ケアマネ対象の利用者の取得
    //   const ret: FilterForUserIdSelectOutEntity = await ScreenRepository.select(
    //     'filterForUserIdSelectGUI01085',
    //     inputParam
    //   )

    //   userSelList = ret.data.userSelList
    // }

    // if(userSelList.length) {
    //   // 選択されている50音行番号、選択されている50音母音、OUTPUT情報.利用者絞込リストより、利用者一覧明細データをフィルターする
    //   localOneway.or03981Oneway.userList = localOneway.or30590.userList.filter(x => userSelList.some(y => x.selfId === y.userId))
    // } else {
    //   localOneway.or03981Oneway.userList = localOneway.or30590.userList
    // }

    // // 利用者一覧明細のデータ件数 > 0、かつ、画面.利用者単複数選択が単一の場合
    // if(localOneway.or03981Oneway.userList.length && mo00039UserSelectType.value === Or30590Const.DEFAULT.TANI) {
    //   // 利用者一覧明細の1件目レコードを選択状態にする
    //   or03981Type.value = localOneway.or03981Oneway.userList[0].userId
    //   // 履歴設定
    //   await getHistoryData(or03981Type.value)
    // }
  },
  {
    deep: true
  }
)


/**
 * イベントリスナーの解除
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await save()
    }
  }
)

// ダイアログ表示フラグ
const showDialogOrX0117 = computed(() => {
  // Or30590のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        class="or30590_row"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="2"
          class="or30590_table"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="localOneway.mo01334Oneway"
            class="list-wrapper"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="3"
          class="content_center"
        >
          <!-- タイトル入力 -->
          <c-v-row
            no-gutter
            class="customCol or30590_row d-flex align-center"
          >
            <!-- タイトルラベル -->
            <c-v-col
              cols="12"
              sm="3"
              class="label_left"
            >
              <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayTitle" />
            </c-v-col>
            <!-- タイトルテキストボックス -->
            <c-v-col
              cols="12"
              sm="auto"
              class="label_right"
            >
              <base-mo-01338 :oneway-model-value="localOneway.mo01338OnewayTitle" />
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <!-- 印刷日付入力 -->
          <c-v-row
            no-gutter
            class="customCol or30590_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              style="padding-left: 0px; padding-right: 0px"
            >
              <base-mo00039
                v-model="mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              style="padding-left: 0px; padding-right: 0px"
            >
              <base-mo00020
                v-if="mo00020Flag"
                v-model="mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <!-- オプション設定ラベル -->
          <c-v-row
            no-gutter
            class="printerOption customCol or30590_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <!-- メモを印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or30590_row pt-2"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="localData.parameter05"
                :oneway-model-value="localOneway.mo00018OneWayPrintMemo"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 被保険者番号を印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or30590_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="localData.parameter06"
                :oneway-model-value="localOneway.mo00018OneWayPrintInsuredPerson"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 住所を印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or30590_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="localData.parameter07"
                :oneway-model-value="localOneway.mo00018OneWayPrintAddress"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 敬称を変更するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or30590_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 d-flex align-center"
            >
              <base-mo00018
                v-model="localData.parameter03"
                :oneway-model-value="localOneway.mo00018OneWayPrintHonorificsChange"
              >
              </base-mo00018>
              <span class="pr-1">(</span>
              <base-mo00045
                v-model="localData.parameter04"
                :oneway-model-value="localOneway.mo00045PrintHonorificsChange"
              />
              <span class="pl-1">)</span>
            </c-v-col>
          </c-v-row>
          <!-- 記入用シートを印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or30590_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="localData.mo00018Type"
                :oneway-model-value="localOneway.mo00018OneWayPrintEntrySheet"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- タイトルを変更するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or30590_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="localData.parameter08"
                :oneway-model-value="localOneway.mo00018OneWayPrintTitleChange"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- タイトル テキストボックス -->
          <c-v-row
            no-gutter
            class="customCol or30590_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-left: 12px;"
            >
              <base-mo00045
                v-model="localData.parameter09"
                :oneway-model-value="localOneway.mo00045Title"
              />
            </c-v-col>
          </c-v-row>
          <!-- 記入日を印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or30590_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="localData.parameter10"
                :oneway-model-value="localOneway.mo00018OneWayPrintEntryDate"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 生年月日を印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or30590_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="localData.parameter11"
                :oneway-model-value="localOneway.mo00018OneWayPrintBirthday"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 希望するサービス内容を印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or30590_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="localData.parameter12"
                :oneway-model-value="localOneway.mo00018OneWayPrintHopeService"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="7"
          class="content_center"
        >
          <c-v-row
            class="or30590_row"
            no-gutter
            style="align-items: center"
          >
            <!-- 利用者選択ラベル -->
            <c-v-col
              cols="12"
              sm="2"
              style="padding-right: 0px; padding-left: 8px;"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
            <!-- 利用者選択ラジオボタングループ -->
            <c-v-col
              cols="12"
              sm="4"
              style="padding-left: 0px; padding-right: 0px"
            >
              <base-mo00039
                v-model="mo00039UserSelectType"
                :oneway-model-value="localOneway.mo00039UserSelectTypeOneWay"
              >
              </base-mo00039>
            </c-v-col>
            <!-- 履歴選択ラベル -->
            <c-v-col
              v-if="!kijunbiFlag"
              cols="12"
              sm="2"
              style="padding-left: 8px; padding-right: 0px"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
            <!-- 履歴選択-ラジオボタングループ -->
            <c-v-col
              v-if="!kijunbiFlag"
              cols="12"
              sm="4"
              style="padding-left: 0px; padding-right: 0px"
            >
              <base-mo00039
                v-model="mo00039HistorySelectType"
                :oneway-model-value="localOneway.mo00039HistorySelectTypeOneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              v-if="kijunbiFlag"
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <base-mo00020
                v-model="mo00020TypeKijunbi"
                :oneway-model-value="localOneway.mo00020KijunbiOneWay"
              />
            </c-v-col>
          </c-v-row>
          <!-- 担当ケアマネ選択 -->
          <c-v-row
            v-if="tantoSelectDisplayFlag"
            class="or30590_row d-flex align-center pl-2"
            no-gutter
          >
            <g-custom-or-x-0145
              v-bind="orx0145"
              v-model="local.orX0145"
              :oneway-model-value="localOneway.orX0145Oneway"
            ></g-custom-or-x-0145>
          </c-v-row>
          <c-v-row
            class="or30590_row"
            no-gutter
          >
            <c-v-col
              :cols="userSelectCols"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="localOneway.orX0130Oneway.selectMode && initFlag"
                v-bind="orX0130"
                :oneway-model-value="localOneway.orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag && initFlag"
              cols="8"
              style="overflow-x: hidden"
            >
              <div style="width: 100%; height: 100%; overflow: auto">
                <!-- 計画期間＆履歴一覧 -->
                <g-custom-or-x-0128
                  v-if="localOneway.orX0128Oneway.singleFlg"
                  v-bind="orX0128"
                  :oneway-model-value="localOneway.orX0128Oneway"
                  style="width: 515px"
                ></g-custom-or-x-0128>
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row class="pr-1">
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="downloadPdf()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="localOneway.orX0117Oneway"
  ></g-custom-or-x-0117>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or-21814 v-bind="or21814" />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or-21813 v-bind="or21813" />
</template>
<style>
.or30590_content {
  padding: 0px !important;
}

.or30590_gokeiClass {
  label {
    color: #ffffff;
  }
}
</style>
<style scoped lang="scss">
:deep(.v-table__wrapper) {
  overflow-x: auto !important;
}

:deep(.v-data-table__td) {
  border-left: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

.or30590_table {
  padding: 0px !important;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or30590_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
    padding-top: 4px;
  }

  .printerOption {
    background-color: #edf1f7;
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}
</style>
