<script setup lang="ts">
/**
 * GUI00842:有機体:印刷設定
 * GUI00842_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or52060Const } from '~/components/custom-components/organisms/Or52060/Or52060.constants'
import { Or52060Logic } from '~/components/custom-components/organisms/Or52060/Or52060.logic'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00842'
// ルーティング
const routing = 'GUI00842/pinia'
// 画面物理名
const screenName = 'GUI00842'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or52060 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00895' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or52060.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00895',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or52060Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or52060Const.CP_ID(1)]: or52060.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or52060Logic.initialize(or52060.value.uniqueCpId)
}

// ダイアログ表示フラグ
const showDialogOr52060 = computed(() => {
  // Or52060のダイアログ開閉状態
  return Or52060Logic.state.get(or52060.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or52060)--期間管理フラグが「管理する」の場合
 *
 */
function onClickOr52060() {
  // Or52060のダイアログ開閉状態を更新する
  Or52060Logic.state.set({
    uniqueCpId: or52060.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        /**
         * 処理年月日
         */
        processYmd: '2024/11/11',
        /**
         * 基準日
         */
        basicDate: '2024/10/11',
        /**
         * セクション名
         */
        sectionName: '',
        /**
         * 施設ID
         */
        shisetuId: '',
        /**
         * 事業者ID
         */
        svJigyoId: '',
        /**
         * 事業所名
         */
        svJigyoKnj: '',
        /**
         * 職員ID
         */
        shokuId: '',
        /**
         * 利用者ID
         */
        userId: '41',
        /**
         * 履歴ID
         */
        assId: '2',
        /**
         * 担当者ID
         */
        tantoId: '',
        /**
         * 選択帳票番号
         */
        prtNo: '2',
        /**
         * フォーカス設定用イニシャル
         */
        focusSettingInitial: ['や', 'ゆ', 'よ'],
        /**
         * 担当者カウンタ値
         */
        selectedUserCounter: '2',
      },
    },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr52060"
        >GUI00842_印刷設定
      </v-btn>
      <g-custom-or-52060
        v-if="showDialogOr52060"
        v-bind="or52060"
      />
    </c-v-col>
  </c-v-row>
</template>
