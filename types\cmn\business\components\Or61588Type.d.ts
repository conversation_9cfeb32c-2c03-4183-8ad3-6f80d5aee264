/**
 * Or61588:有機体:分類/領域[のまとめ一覧
 * 単方向/双方向バインドのデータ構造
 *
 *  <AUTHOR>
 */

import type { DataTableListItem } from '~/components/custom-components/organisms/Or61588/Or61588.type'

/**
 *  単方向バインド用インタフェース
 *
 */
export interface Or61588OnewayType {
  /** 表示タイプ(1:分類, 0:領域) */
  showType: string
  /** 現在の領域ID */
  filedTabId: string
  /** 現在のカテゴリID */
  classificationTabId: string
}
/**
 * 双方向バインド用インタフェース
 *
 */
export interface Or61588TwowayType {
  /** アセスメントまとめ情報リスト */
  or61588ModelValue: DataTableListItem[]
}
