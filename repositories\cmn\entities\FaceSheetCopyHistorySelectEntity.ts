import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 取得入力エンティティ
 */
export interface FaceSheetCopyHistorySelectInEntity extends InWebEntity {
  /** 計画期間ID */
  sc1Id: string
  /** 事業所ID */
  svjigyoId: string
  /** 利用者ID */
  userid: string
  /** 期間管理フラグ */
  kikanFlg: string
  /** 適用事業所IDリスト */
  jigyoIdList: { jigyoId: string }[]
}

/**
 * 取得出力エンティティ
 */
export interface FaceSheetCopyHistorySelectOutEntity extends OutWebEntity {
  /**
   * 取得出力データエンティティ
   */
  data: FaceSheetCopyHistorySelectDataEntity
}

/**
 * 取得出力データエンティティ
 */
export interface FaceSheetCopyHistorySelectDataEntity {
  /**
   * 履歴情報リスト
   */
  historyInfoList: FaceSheetCopyHistoryInfo[]
  /**
   * タブチェックリスト
   */
  tabCheckList: FaceSheetTabCheckInfo[]
}

/**
 * 履歴情報
 */
export interface FaceSheetCopyHistoryInfo {
  /** フェースシート履歴ID */
  faceId: string
  /** 計画期間ID */
  sc1Id: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userid: string
  /** 作成日 */
  create_ymd: string
  /** 作成者 */
  shokuId: string
  /** ケース番号 */
  caseNo: string
  /** 変更回数 */
  henkoKaisu: string
  /** フェースシート①作成 */
  face1Flg: string
  /** フェースシート②作成 */
  face2Flg: string
  /** フェースシート③作成 */
  face3Flg: string
  /** 改訂フラグ */
  kaiteiFlg: string
  /** 初回作成日 */
  shokai_ymd: string
  /** フェースシート④作成 */
  face4Flg: string
  /** 更新回数 */
  modifiedCnt: string
  /** 事業名 */
  jigyoKnj: string
  /** 事業名（略称） */
  jigyoRyakuKnj: string
  /** 作成者名 */
  shokuKnj: string
}

/**
 * タブチェック
 */
export interface FaceSheetTabCheckInfo {
  /**
   * タブCheck1
   */
  check1: string
  /**
   * タブCheck2
   */
  check2: string
  /**
   * タブCheck3
   */
  check3: string
  /**
   * タブCheck4
   */
  check4: string
}
