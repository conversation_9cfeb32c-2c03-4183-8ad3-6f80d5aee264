<script setup lang="ts">
/**
 * Or06146:有機体:［アセスメント］画面_コンテンツ
 * GUI00794_［アセスメント］画面_コンテンツ
 *
 * @description
 * ［退院・退所情報記録書］画面_コンテンツ
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28287Logic } from '../Or28287/Or28287.logic'
import { Or28287Const } from '../Or28287/Or28287.constants'
import { Or60075Logic } from '../Or60075/Or60075.logic'
import { Or31535Const } from '../Or31535/Or31535.constants'
import { Or51813Logic } from '../Or51813/Or51813.logic'
import { Or51813Const } from '../Or51813/Or51813.constants'
import type { UserManagementInfo, AttendingPhysicianInfo } from '../Or51813/Or51813.type'
import { Or06146Const } from './Or06146.constants'
import type { Or06146OnewayType } from './Or06146.type'
import { useNuxtApp } from '#imports'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'

import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { OrCpGroupDefinitionInputFormDeleteDialogOnewayType } from '~/types/business/generator-components/OrCpGroupDefinitionInputFormDeleteDialog'
import { useSetupChildProps } from '~/composables/useComponentVue'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00046OnewayType, Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Mo00039Items, Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { TeX0012Type } from '~/types/cmn/business/components/TeX0012Type'
import type {
  DischargeFromHospitalLeavingInfoInitInfoSelectInEntity,
  DischargeFromHospitalLeavingInfoInitInfoSelectOutEntity,
} from '~/repositories/cmn/entities/DischargeFromHospitalLeavingInfoSelectServiceEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or28287Type, Or28287OnewayType } from '~/types/cmn/business/components/Or28287Type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  onewayModelValue: Or06146OnewayType
  parentUniqueCpId: string
}

/**************************************************
 * 変数定義
 **************************************************/
const $log = useNuxtApp().$log as DebugLogPluginInterface
const { t } = useI18n()

// ラベル名称テンプレート
const mo00615OnewayType = {
  showItemLabel: true,
  showRequiredLabel: false,
  itemLabelFontWeight: 'bold',
  customClass: {
    outerStyle: 'background: none',
  },
} as Mo00615OnewayType

// チェックボックステンプレート
const mo00018OnewayType = {
  hideDetails: true,
  showItemLabel: false,
  checkboxLabel: '',
} as Mo00018OnewayType

// チェックボックステンプレート
const mo00045OnewayType = {
  showItemLabel: true,
  isVerticalLabel: false,
  customClass: new CustomClass({
    outerClass: 'mr-8',
    labelClass: 'ma-1',
  }),
} as Mo00045OnewayType

const props = defineProps<Props>()

const list = reactive([
  { label: '', value: '' },
  { label: '非該当', value: '1' },
  { label: '事業対象者', value: '2' },
  { label: '要支援１', value: '3' },
  { label: '要支援２', value: '4' },
  { label: '経過的要介護', value: '5' },
  { label: '要介護１', value: '6' },
  { label: '要介護２', value: '7' },
  { label: '要介護３', value: '8' },
  { label: '要介護４', value: '9' },
  { label: '要介護５', value: '10' },
])

const localOneway = reactive({
  mo00020Oneway: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: false,
    showSelectArrow: false,
  } as Mo00020OnewayType,

  // 「ヘッダー部」名称
  // 基本情報・現在の状態等
  mo00615BaseInfoOnewayType: {
    ...mo00615OnewayType,
    itemLabel: t('label.base-info-current-state-etc'),
    itemLabelCustomClass: new CustomClass({ labelStyle: 'font-size:18px' }),
  } as Mo00615OnewayType,
  // 属性
  mo00615AttributeOnewayType: {
    ...mo00615OnewayType,
    itemLabel: t('label.attribute'),
    itemLabelCustomClass: new CustomClass({
      labelClass: 'header-title1',
    }),
  } as Mo00615OnewayType,
  // 退院(所)時の要介護度
  mo00615LevelOfCareRequiredOnewayType: {
    ...mo00615OnewayType,
    itemLabel: t('label.discharge-from-hospital-leaving-level-of-care-required'),
  } as Mo00615OnewayType,
  // 選択アイコンボタン
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,

  // 入院（所）概要（ヘッダー）
  mo00615HospitalizationAdmissionInfoOnewayType: {
    ...mo00615OnewayType,
    itemLabel: t('label.hospitalization-admission-info-details'),
    itemLabelCustomClass: new CustomClass({
      labelClass: 'header-title1',
    }),
  } as Mo00615OnewayType,
  // (入院情報)
  mo00615HospitalizationOneway: {
    ...mo00615OnewayType,
    itemLabel: t('label.hospitalization-info-details'),
  } as Mo00615OnewayType,
  // (入所情報)
  mo00615AdmissionInfoOneway: {
    ...mo00615OnewayType,
    itemLabel: t('label.admission-info-details'),
  } as Mo00615OnewayType,
  // 入院原因疾患\r\n(入所目的等)
  mo00615HospitalizationCauseOneway: {
    ...mo00615OnewayType,
    itemLabel: t('label.hospitalization-cause-disease-purpose-etc'),
  } as Mo00615OnewayType,
  // 入院・入所先
  mo00615HospitalizationAdmissionOneway: {
    ...mo00615OnewayType,
    itemLabel: t('label.hospitalization2'),
  } as Mo00615OnewayType,
  // 今後の医学管理
  mo00615NextMedicineManagementOneway: {
    ...mo00615OnewayType,
    itemLabel: t('label.hospitalization3'),
  } as Mo00615OnewayType,
  // 方法
  mo00615MethodOneway: {
    ...mo00615OnewayType,
    itemLabel: '方法',
  } as Mo00615OnewayType,
  // UDF等の食形態区分
  mo00615UdfOneway: {
    ...mo00615OnewayType,
    itemLabel: 'UDF等の食形態区分',
  } as Mo00615OnewayType,
  // 義歯
  mo00615ArtificialToothOneway: {
    ...mo00615OnewayType,
    itemLabel: '義歯',
  } as Mo00615OnewayType,

  // ①疾患と入院（所）中の状況（ヘッダー）
  mo00615DiseaseHospitalizationSituationOnewayType: {
    ...mo00615OnewayType,
    itemLabel: t('label.disease-hospitalization-situation'),
    itemLabelCustomClass: new CustomClass({
      labelClass: 'header-title1',
    }),
  } as Mo00615OnewayType,
  // 現在治療中の疾患
  mo00615CurrentTherapyDiseaseOneway: {
    ...mo00615OnewayType,
    itemLabel: t('label.current-therapy-disease'),
  } as Mo00615OnewayType,
  // 移動手段
  mo00615CurrentTherapyDiseaseOneway2: {
    ...mo00615OnewayType,
    itemLabel: '移動手段',
  } as Mo00615OnewayType,
  // 排泄方法
  mo00615CurrentTherapyDiseaseOneway3: {
    ...mo00615OnewayType,
    itemLabel: '排泄方法',
  } as Mo00615OnewayType,
  // 入浴方法
  mo00615CurrentTherapyDiseaseOneway4: {
    ...mo00615OnewayType,
    itemLabel: '入浴方法',
  } as Mo00615OnewayType,
  // 食事形態
  mo00615CurrentTherapyDiseaseOneway5: {
    ...mo00615OnewayType,
    itemLabel: '食事形態',
  } as Mo00615OnewayType,
  // 嚥下機能(むせ)
  mo00615CurrentTherapyDiseaseOneway6: {
    ...mo00615OnewayType,
    itemLabel: '嚥下機能(むせ)',
  } as Mo00615OnewayType,
  // 口腔清潔
  mo00615CurrentTherapyDiseaseOneway7: {
    ...mo00615OnewayType,
    itemLabel: '口腔清潔',
  } as Mo00615OnewayType,
  // 口腔ケア
  mo00615CurrentTherapyDiseaseOneway8: {
    ...mo00615OnewayType,
    itemLabel: '口腔ケア',
  } as Mo00615OnewayType,
  // 睡眠
  mo00615CurrentTherapyDiseaseOneway9: {
    ...mo00615OnewayType,
    itemLabel: '睡眠',
  } as Mo00615OnewayType,
  // 認知・精神
  mo00615CurrentTherapyDiseaseOneway10: {
    ...mo00615OnewayType,
    itemLabel: '認知・精神',
  } as Mo00615OnewayType,
  // 眠剤使用
  mo00615SleepingPillsUseOneway: {
    ...mo00615OnewayType,
    itemLabel: '眠剤使用',
  } as Mo00615OnewayType,

  // ②受け止め/意向
  mo00615AcceptanceIntentionOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '②受け止め/意向',
    itemLabelCustomClass: new CustomClass({
      labelClass: 'header-title1',
    }),
  } as Mo00615OnewayType,
  // ＜本人＞病気、障害、後遺症等の受け止め方
  mo00615AcceptanceIntentionOnewayType1: {
    ...mo00615OnewayType,
    itemLabel: '＜本人＞病気、障害、後遺症等の受け止め方',
  } as Mo00615OnewayType,
  // ＜本人＞退院後の生活に関する意向
  mo00615AcceptanceIntentionOnewayType2: {
    ...mo00615OnewayType,
    itemLabel: '＜本人＞退院後の生活に関する意向',
  } as Mo00615OnewayType,
  // ＜家族＞病気、障害、後遺症等の受け止め方
  mo00615AcceptanceIntentionOnewayType3: {
    ...mo00615OnewayType,
    itemLabel: '＜家族＞病気、障害、後遺症等の受け止め方',
  } as Mo00615OnewayType,
  // ＜家族＞退院後の生活に関する意向
  mo00615AcceptanceIntentionOnewayType4: {
    ...mo00615OnewayType,
    itemLabel: '＜家族＞退院後の生活に関する意向',
  } as Mo00615OnewayType,
  // 本人への病名告知
  mo00615AcceptanceIntentionOnewayType5: {
    ...mo00615OnewayType,
    itemLabel: '本人への病名告知',
  } as Mo00615OnewayType,

  // ２.課題認識のための情報
  mo00615IssuesCognitiveInfoOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '２.課題認識のための情報',
    itemLabelCustomClass: new CustomClass({ labelStyle: 'font-size:18px' }),
  } as Mo00615OnewayType,
  // ③退院後に必要な事柄
  mo00615DischargeFromHospitalMatterOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '③退院後に必要な事柄',
    itemLabelCustomClass: new CustomClass({
      labelClass: 'header-title1',
    }),
  } as Mo00615OnewayType,
  // 医療処置の内容
  mo00615MedicalcareTreatmentContentsOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '医療処置の内容',
  } as Mo00615OnewayType,
  // 看護の視点
  mo00615NursingPerspectiveOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '看護の視点',
  } as Mo00615OnewayType,
  // リハビリの視点
  mo00615RehabilitationPerspectiveOnewayType: {
    ...mo00615OnewayType,
    itemLabel: 'リハビリの視点',
  } as Mo00615OnewayType,
  // 禁忌事項
  mo00615ContraindicationsMatterOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '禁忌事項',
  } as Mo00615OnewayType,

  // (禁忌の有無)
  mo00615ContraindicationsPreOrAbOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '(禁忌の有無)',
  } as Mo00615OnewayType,
  // (禁忌の内容/留意点)
  mo00615ContraindicationsContentsOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '(禁忌の内容/留意点)',
  } as Mo00615OnewayType,

  // 症状・病状の予後・予測
  mo00615SymptomsPredictionOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '症状・病状の\r\n予後・予測',
  } as Mo00615OnewayType,
  // 退院に際しての日常生活の阻害要因(心身状況・環境等)
  mo00615InhibitionFactorOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '退院に際しての日常生活の\r\n阻害要因(心身状況・環境等)',
  } as Mo00615OnewayType,
  // 例）医療機関からの見立て・意見（今後の見通し、急変の可能性や今後、どんなことが起こりうるか（合併症）、良くなっていく又はゆっくり落ちていく方向なのか等）について、①疾患と入院中の状況、②本人・家族の受け止めや意向、③退院後に必要な事柄、④その他の観点から必要と思われる事柄について記載する。
  mo00615InhibitionFactorSampleOnewayType: {
    ...mo00615OnewayType,
    itemLabel:
      '例）医療機関からの見立て・意見（今後の見通し、急変の可能性や今後、どんなことが起こりうるか（合併症）、良くなっていく又はゆっくり落ちていく方向なのか　等）について、\r\n①疾患と入院中の状況、②本人・家族の受け止めや意向、③退院後に必要な事柄、④その他の観点から必要と思われる事柄について記載する。',
  } as Mo00615OnewayType,
  // 在宅復帰のために\r\n整えなければならない要件
  mo00615ArrangedRequirementOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '在宅復帰のために\r\n整えなければならない要件',
  } as Mo00615OnewayType,

  // 回目
  mo00615TimeOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '回目',
  } as Mo00615OnewayType,
  // 聞き取り日
  mo00615InterviewDateOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '聞き取り日',
  } as Mo00615OnewayType,
  // 情報提供を受けた職種(氏名)
  mo00615ReceivedOccupationOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '情報提供を受けた職種(氏名)',
  } as Mo00615OnewayType,
  // 会議出席
  mo00615MeetingAttendanceOnewayType: {
    ...mo00615OnewayType,
    itemLabel: '会議出席',
  } as Mo00615OnewayType,

  // 1回目
  mo00615Time1OnewayType: {
    ...mo00615OnewayType,
    itemLabel: '1',
  } as Mo00615OnewayType,
  // 2回目
  mo00615Time2OnewayType: {
    ...mo00615OnewayType,
    itemLabel: '2',
  } as Mo00615OnewayType,
  // 3回目
  mo00615Time3OnewayType: {
    ...mo00615OnewayType,
    itemLabel: '3',
  } as Mo00615OnewayType,

  // データコンポーネント部
  // チェックボックスコンポーネント
  // 要区分変更
  mo00018Oneway1: {
    ...mo00018OnewayType,
    checkboxLabel: t('label.category-modified'),
  } as Mo00018OnewayType,
  // 要介護度チェックボックス
  mo00018Oneway2: {
    ...mo00018OnewayType,
  } as Mo00018OnewayType,
  // 申請中
  mo00018Oneway3: {
    ...mo00018OnewayType,
    checkboxLabel: t('label.application_in_progress_flag'),
  } as Mo00018OnewayType,
  // 無し
  mo00018Oneway4: {
    ...mo00018OnewayType,
    checkboxLabel: t('label.radio-no'),
  } as Mo00018OnewayType,
  // 通院
  mo00018Oneway5: {
    ...mo00018OnewayType,
    checkboxLabel: '通院',
  } as Mo00018OnewayType,
  // 訪問診療
  mo00018Oneway6: {
    ...mo00018OnewayType,
    checkboxLabel: '訪問診療',
  } as Mo00018OnewayType,

  //移動手段
  // 自立
  mo00018Oneway7: {
    ...mo00018OnewayType,
    checkboxLabel: '自立',
  } as Mo00018OnewayType,
  // 杖
  mo00018Oneway8: {
    ...mo00018OnewayType,
    checkboxLabel: '杖',
  } as Mo00018OnewayType,
  // 歩行器
  mo00018Oneway9: {
    ...mo00018OnewayType,
    checkboxLabel: '歩行器',
  } as Mo00018OnewayType,
  // 車いす
  mo00018Oneway10: {
    ...mo00018OnewayType,
    checkboxLabel: '車いす',
  } as Mo00018OnewayType,
  // その他
  mo00018Oneway11: {
    ...mo00018OnewayType,
    checkboxLabel: 'その他',
  } as Mo00018OnewayType,

  // 排泄方法
  // トイレ
  mo00018Oneway12: {
    ...mo00018OnewayType,
    checkboxLabel: 'トイレ',
  } as Mo00018OnewayType,
  // ポータブル
  mo00018Oneway13: {
    ...mo00018OnewayType,
    checkboxLabel: 'ポータブル',
  } as Mo00018OnewayType,
  // おむつ
  mo00018Oneway14: {
    ...mo00018OnewayType,
    checkboxLabel: 'おむつ',
  } as Mo00018OnewayType,
  // カテーテル・パウチ
  mo00018Oneway15: {
    ...mo00018OnewayType,
    checkboxLabel: 'カテーテル・パウチ',
  } as Mo00018OnewayType,

  // 入浴方法
  // 自立
  mo00018Oneway16: {
    ...mo00018OnewayType,
    checkboxLabel: '自立',
  } as Mo00018OnewayType,
  // シャワー浴
  mo00018Oneway17: {
    ...mo00018OnewayType,
    checkboxLabel: 'シャワー浴',
  } as Mo00018OnewayType,
  // 一般浴
  mo00018Oneway18: {
    ...mo00018OnewayType,
    checkboxLabel: '一般浴',
  } as Mo00018OnewayType,
  // 機械浴
  mo00018Oneway19: {
    ...mo00018OnewayType,
    checkboxLabel: '機械浴',
  } as Mo00018OnewayType,
  // 行わず
  mo00018Oneway20: {
    ...mo00018OnewayType,
    checkboxLabel: '行わず',
  } as Mo00018OnewayType,

  // 食事形態
  // 普通
  mo00018Oneway21: {
    ...mo00018OnewayType,
    checkboxLabel: '普通',
  } as Mo00018OnewayType,
  // 経管栄養
  mo00018Oneway22: {
    ...mo00018OnewayType,
    checkboxLabel: '経管栄養',
  } as Mo00018OnewayType,
  // その他
  mo00018Oneway23: {
    ...mo00018OnewayType,
    checkboxLabel: 'その他',
  } as Mo00018OnewayType,

  // 認知・精神
  // 認知機能低下
  mo00018Oneway24: {
    ...mo00018OnewayType,
    checkboxLabel: '認知機能低下',
  } as Mo00018OnewayType,
  // せん妄
  mo00018Oneway25: {
    ...mo00018OnewayType,
    checkboxLabel: 'せん妄',
  } as Mo00018OnewayType,
  // 徘徊
  mo00018Oneway26: {
    ...mo00018OnewayType,
    checkboxLabel: '徘徊',
  } as Mo00018OnewayType,
  // 焦燥・不穏
  mo00018Oneway27: {
    ...mo00018OnewayType,
    checkboxLabel: '焦燥・不穏',
  } as Mo00018OnewayType,
  // 攻撃性
  mo00018Oneway28: {
    ...mo00018OnewayType,
    checkboxLabel: '攻撃性',
  } as Mo00018OnewayType,
  // その他
  mo00018Oneway29: {
    ...mo00018OnewayType,
    checkboxLabel: 'その他',
  } as Mo00018OnewayType,

  // 医療処置の内容
  // なし
  mo00018Oneway30: {
    ...mo00018OnewayType,
    checkboxLabel: 'なし',
  } as Mo00018OnewayType,
  // 点滴
  mo00018Oneway31: {
    ...mo00018OnewayType,
    checkboxLabel: '点滴',
  } as Mo00018OnewayType,
  // 酸素療法
  mo00018Oneway32: {
    ...mo00018OnewayType,
    checkboxLabel: '酸素療法',
  } as Mo00018OnewayType,
  // 喀痰吸引
  mo00018Oneway33: {
    ...mo00018OnewayType,
    checkboxLabel: '喀痰吸引',
  } as Mo00018OnewayType,
  // 気管切開
  mo00018Oneway34: {
    ...mo00018OnewayType,
    checkboxLabel: '気管切開',
  } as Mo00018OnewayType,
  // 胃ろう
  mo00018Oneway35: {
    ...mo00018OnewayType,
    checkboxLabel: '胃ろう',
  } as Mo00018OnewayType,
  // 経鼻栄養
  mo00018Oneway36: {
    ...mo00018OnewayType,
    checkboxLabel: '経鼻栄養',
  } as Mo00018OnewayType,
  // 経腸栄養
  mo00018Oneway37: {
    ...mo00018OnewayType,
    checkboxLabel: '経腸栄養',
  } as Mo00018OnewayType,
  // 褥瘡
  mo00018Oneway38: {
    ...mo00018OnewayType,
    checkboxLabel: '褥瘡',
  } as Mo00018OnewayType,
  // 尿道カテーテル
  mo00018Oneway39: {
    ...mo00018OnewayType,
    checkboxLabel: '尿道カテーテル',
  } as Mo00018OnewayType,
  // 尿路ストーマ
  mo00018Oneway40: {
    ...mo00018OnewayType,
    checkboxLabel: '尿路ストーマ',
  } as Mo00018OnewayType,
  // 消化管ストーマ
  mo00018Oneway41: {
    ...mo00018OnewayType,
    checkboxLabel: '消化管ストーマ',
  } as Mo00018OnewayType,
  // 痛みコントロール
  mo00018Oneway42: {
    ...mo00018OnewayType,
    checkboxLabel: '痛みコントロール',
  } as Mo00018OnewayType,
  // 排便コントロール
  mo00018Oneway43: {
    ...mo00018OnewayType,
    checkboxLabel: '排便コントロール',
  } as Mo00018OnewayType,
  // 自己注射
  mo00018Oneway44: {
    ...mo00018OnewayType,
    checkboxLabel: '自己注射',
  } as Mo00018OnewayType,
  // その他
  mo00018Oneway45: {
    ...mo00018OnewayType,
    checkboxLabel: 'その他',
  } as Mo00018OnewayType,

  // 看護の視点
  // 血圧
  mo00018Oneway46: {
    ...mo00018OnewayType,
    checkboxLabel: '血圧',
  } as Mo00018OnewayType,
  // 水分制限
  mo00018Oneway47: {
    ...mo00018OnewayType,
    checkboxLabel: '水分制限',
  } as Mo00018OnewayType,
  // 食事制限
  mo00018Oneway48: {
    ...mo00018OnewayType,
    checkboxLabel: '食事制限',
  } as Mo00018OnewayType,
  // 食形態
  mo00018Oneway49: {
    ...mo00018OnewayType,
    checkboxLabel: '食形態',
  } as Mo00018OnewayType,
  // 嚥下
  mo00018Oneway50: {
    ...mo00018OnewayType,
    checkboxLabel: '嚥下',
  } as Mo00018OnewayType,
  // 口腔ケア
  mo00018Oneway51: {
    ...mo00018OnewayType,
    checkboxLabel: '口腔ケア',
  } as Mo00018OnewayType,
  // 清潔ケア
  mo00018Oneway52: {
    ...mo00018OnewayType,
    checkboxLabel: '清潔ケア',
  } as Mo00018OnewayType,
  // 血糖コントロール
  mo00018Oneway53: {
    ...mo00018OnewayType,
    checkboxLabel: '血糖コントロール',
  } as Mo00018OnewayType,
  // 排泄
  mo00018Oneway54: {
    ...mo00018OnewayType,
    checkboxLabel: '排泄',
  } as Mo00018OnewayType,
  // 皮膚状態
  mo00018Oneway55: {
    ...mo00018OnewayType,
    checkboxLabel: '皮膚状態',
  } as Mo00018OnewayType,
  // 睡眠
  mo00018Oneway56: {
    ...mo00018OnewayType,
    checkboxLabel: '睡眠',
  } as Mo00018OnewayType,
  // 認知機能・精神面
  mo00018Oneway57: {
    ...mo00018OnewayType,
    checkboxLabel: '認知機能・精神面',
  } as Mo00018OnewayType,
  // 服薬指導
  mo00018Oneway58: {
    ...mo00018OnewayType,
    checkboxLabel: '服薬指導',
  } as Mo00018OnewayType,
  // 療養上の指導（食事・水分・睡眠・清潔ケア・排泄 などにおける指導）
  mo00018Oneway59: {
    ...mo00018OnewayType,
    checkboxLabel: '療養上の指導（食事・水分・睡眠・清潔ケア・排泄 などにおける指導）',
  } as Mo00018OnewayType,
  // ターミナル
  mo00018Oneway60: {
    ...mo00018OnewayType,
    checkboxLabel: 'ターミナル',
  } as Mo00018OnewayType,
  // その他
  mo00018Oneway61: {
    ...mo00018OnewayType,
    checkboxLabel: 'その他',
  } as Mo00018OnewayType,

  // リハビリの視点
  // 本人指導
  mo00018Oneway62: {
    ...mo00018OnewayType,
    checkboxLabel: '本人指導',
  } as Mo00018OnewayType,
  // 家族指導
  mo00018Oneway63: {
    ...mo00018OnewayType,
    checkboxLabel: '家族指導',
  } as Mo00018OnewayType,
  // 関節可動域練習（ｽﾄﾚｯﾁ含む）
  mo00018Oneway64: {
    ...mo00018OnewayType,
    checkboxLabel: '関節可動域練習（ｽﾄﾚｯﾁ含む）',
  } as Mo00018OnewayType,
  // 筋力増強練習
  mo00018Oneway65: {
    ...mo00018OnewayType,
    checkboxLabel: '筋力増強練習',
  } as Mo00018OnewayType,
  // バランス練習
  mo00018Oneway66: {
    ...mo00018OnewayType,
    checkboxLabel: 'バランス練習',
  } as Mo00018OnewayType,
  // 麻痺・筋緊張改善練習
  mo00018Oneway67: {
    ...mo00018OnewayType,
    checkboxLabel: '麻痺・筋緊張改善練習',
  } as Mo00018OnewayType,
  // 起居／立位等基本動作練習
  mo00018Oneway68: {
    ...mo00018OnewayType,
    checkboxLabel: '起居／立位等基本動作練習',
  } as Mo00018OnewayType,
  // 摂食・嚥下訓練
  mo00018Oneway69: {
    ...mo00018OnewayType,
    checkboxLabel: '摂食・嚥下訓練',
  } as Mo00018OnewayType,
  // 言語訓練
  mo00018Oneway70: {
    ...mo00018OnewayType,
    checkboxLabel: '言語訓練',
  } as Mo00018OnewayType,
  // ADL練習（歩行／入浴／トイレ動作／移乗等）
  mo00018Oneway71: {
    ...mo00018OnewayType,
    checkboxLabel: 'ADL練習（歩行／入浴／トイレ動作／移乗等）',
  } as Mo00018OnewayType,
  // IADL練習（買い物、調理等）
  mo00018Oneway72: {
    ...mo00018OnewayType,
    checkboxLabel: 'IADL練習（買い物、調理等）',
  } as Mo00018OnewayType,
  // 疼痛管理（痛みコントロール）
  mo00018Oneway73: {
    ...mo00018OnewayType,
    checkboxLabel: '疼痛管理（痛みコントロール）',
  } as Mo00018OnewayType,
  // 更生装具・福祉用具等管理
  mo00018Oneway74: {
    ...mo00018OnewayType,
    checkboxLabel: '更生装具・福祉用具等管理',
  } as Mo00018OnewayType,
  // 運動耐容能練習
  mo00018Oneway75: {
    ...mo00018OnewayType,
    checkboxLabel: '運動耐容能練習',
  } as Mo00018OnewayType,
  // 地域活動支援
  mo00018Oneway76: {
    ...mo00018OnewayType,
    checkboxLabel: '地域活動支援',
  } as Mo00018OnewayType,
  // 社会参加支援
  mo00018Oneway77: {
    ...mo00018OnewayType,
    checkboxLabel: '社会参加支援',
  } as Mo00018OnewayType,
  // 就労支援
  mo00018Oneway78: {
    ...mo00018OnewayType,
    checkboxLabel: '就労支援',
  } as Mo00018OnewayType,
  // その他
  mo00018Oneway79: {
    ...mo00018OnewayType,
    checkboxLabel: 'その他',
  } as Mo00018OnewayType,

  // 要介護度セレクトボックス
  mo00040Oneway: {
    name: 'cpNursingCareRequiredInputNuresingCareRequiredSelectField',
    showItemLabel: false,
    width: '150',
    items: list,
    itemTitle: 'label',
    itemValue: 'value',
  },

  // カレンダーコンポーネント
  // 入院(所)日
  hospitalizationDateOneway: {
    itemLabel: '入院(所)日',
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  } as Mo00020OnewayType,
  // 退院(所)予定日
  hospitalLeavingDateOneway: {
    itemLabel: '退院(所)予定日',
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  } as Mo00020OnewayType,
  // 聞き取り日（１回目）
  ListenDate1Oneway: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: false,
    showSelectArrow: false,
    width: '140',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  } as Mo00020OnewayType,
  // 聞き取り日（２回目）
  ListenDate2Oneway: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: false,
    showSelectArrow: false,
    width: '140',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  } as Mo00020OnewayType,
  // 聞き取り日（３回目）
  ListenDate3Oneway: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: false,
    showSelectArrow: false,
    width: '140',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  } as Mo00020OnewayType,

  // Mo00046 テキストエリア
  // 入院原因疾患(入所目的等)
  mo00046CauseDiseaseOneway: {
    showItemLabel: true,
    iconBtnDisplayFlg: false,
    autoGrow: false,
    noResize: true,
    maxlength: '4000',
    rows: '2',
    maxRows: '2',
  } as Mo00046OnewayType,
  // ＜本人＞病気、障害、後遺症等の受け止め方
  mo00046PersonAcceptOneway: {
    showItemLabel: true,
    iconBtnDisplayFlg: false,
    autoGrow: false,
    noResize: true,
    maxlength: '4000',
    rows: '2',
    maxRows: '2',
  } as Mo00046OnewayType,
  // ＜本人＞退院後の生活に関する意向
  mo00046PersonIntentionOneway: {
    showItemLabel: true,
    iconBtnDisplayFlg: false,
    autoGrow: false,
    noResize: true,
    maxlength: '4000',
    rows: '2',
    maxRows: '2',
  } as Mo00046OnewayType,
  // ＜家族＞病気、障害、後遺症等の受け止め方
  mo00046FamilyAcceptOneway: {
    showItemLabel: true,
    iconBtnDisplayFlg: false,
    autoGrow: false,
    noResize: true,
    maxlength: '4000',
    rows: '2',
    maxRows: '2',
  } as Mo00046OnewayType,
  // ＜家族＞退院後の生活に関する意向
  mo00046FamilyIntentionOneway: {
    showItemLabel: true,
    iconBtnDisplayFlg: false,
    autoGrow: false,
    noResize: true,
    maxlength: '4000',
    rows: '2',
    maxRows: '2',
  } as Mo00046OnewayType,
  // 症状・病状の予後・予測
  mo00046PrognosisOneway: {
    showItemLabel: true,
    iconBtnDisplayFlg: false,
    autoGrow: false,
    noResize: true,
    maxlength: '4000',
    rows: '2',
    maxRows: '2',
  } as Mo00046OnewayType,
  // 退院に際しての日常生活の阻害要因(心身状況・環境等)
  mo00046ObstructiveFactorOneway: {
    showItemLabel: true,
    iconBtnDisplayFlg: false,
    autoGrow: false,
    noResize: true,
    maxlength: '4000',
    rows: '2',
    maxRows: '2',
  } as Mo00046OnewayType,
  // 在宅復帰のために整えなければならない要件
  mo00046ReturnHomeOneway: {
    showItemLabel: true,
    iconBtnDisplayFlg: false,
    autoGrow: false,
    noResize: true,
    maxlength: '4000',
    rows: '2',
    maxRows: '2',
  } as Mo00046OnewayType,

  // Mo00045 テキストフィールド
  // 施設名テキストボックス
  mo00045Oneway1: {
    ...mo00045OnewayType,
    itemLabel: '施設名',
    maxlength: '36',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      labelClass: 'ma-1',
      outerStyle: 'width: 70%',
    }),
  } as Mo00045OnewayType,
  // 棟テキストボックス
  mo00045Oneway2: {
    ...mo00045OnewayType,
    showItemLabel: false,
    maxlength: '36',
    width: '100px',
    appendLabel: '棟',
  } as Mo00045OnewayType,
  // 室テキストボックス
  mo00045Oneway3: {
    ...mo00045OnewayType,
    showItemLabel: false,
    maxlength: '36',
    width: '100px',
    isVerticalLabel: false,
    appendLabel: '室',
  } as Mo00045OnewayType,
  // 医療機関名テキストボックス
  mo00045Oneway4: {
    ...mo00045OnewayType,
    itemLabel: '医療機関名',
    maxlength: '36',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      labelClass: 'ma-1',
      outerStyle: 'width: 95%',
    }),
  } as Mo00045OnewayType,
  // 現在治療中の疾患⓵
  mo00045Oneway5: {
    ...mo00045OnewayType,
    itemLabel: '①',
    maxlength: '36',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      labelClass: 'ma-1',
      outerStyle: 'width: 70%',
    }),
  } as Mo00045OnewayType,
  // 現在治療中の疾患⓶
  mo00045Oneway6: {
    ...mo00045OnewayType,
    itemLabel: '②',
    maxlength: '36',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      labelClass: 'ma-1',
      outerStyle: 'width: 70%',
    }),
  } as Mo00045OnewayType,
  // 現在治療中の疾患⓷
  mo00045Oneway7: {
    ...mo00045OnewayType,
    itemLabel: '③',
    maxlength: '36',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      labelClass: 'ma-1',
      outerStyle: 'width: 70%',
    }),
  } as Mo00045OnewayType,
  // 移動手段
  mo00045Oneway8: {
    ...mo00045OnewayType,
    showItemLabel: false,
    maxlength: '36',
    customClass: new CustomClass({
      outerClass: 'ml-2 mr-2',
      labelClass: 'ma-1',
      outerStyle: 'width: 40%',
    }),
  } as Mo00045OnewayType,
  // 排泄方法
  mo00045Oneway9: {
    ...mo00045OnewayType,
    showItemLabel: false,
    maxlength: '36',
    customClass: new CustomClass({
      outerClass: 'ml-2 mr-2',
      labelClass: 'ma-1',
      outerStyle: 'width: 40%',
    }),
  } as Mo00045OnewayType,
  // 食事形態
  mo00045Oneway10: {
    ...mo00045OnewayType,
    showItemLabel: false,
    maxlength: '36',
    customClass: new CustomClass({
      outerClass: 'ml-2 mr-2',
      labelClass: 'ma-1',
      outerStyle: 'width: 40%',
    }),
  } as Mo00045OnewayType,
  // UDF等の食形態区分
  mo00045Oneway11: {
    ...mo00045OnewayType,
    showItemLabel: false,
    maxlength: '36',
    customClass: new CustomClass({
      outerClass: 'ml-2 mr-2',
      labelClass: 'ma-1',
      outerStyle: 'width: 90%',
    }),
  } as Mo00045OnewayType,

  // ⓷退院後に必要な事柄
  // 医療処置の内容
  // 自己注射備考
  mo00045Oneway12: {
    ...mo00045OnewayType,
    showItemLabel: false,
    maxlength: '20',
    customClass: new CustomClass({
      outerClass: 'ml-2 mr-2',
      labelClass: 'ma-1',
      outerStyle: 'min-width: 200px',
    }),
  } as Mo00045OnewayType,
  // その他備考
  mo00045Oneway13: {
    ...mo00045OnewayType,
    showItemLabel: false,
    maxlength: '40',
    customClass: new CustomClass({
      outerClass: 'ml-2 mr-2',
      labelClass: 'ma-1',
      outerStyle: 'min-width: 300px',
    }),
  } as Mo00045OnewayType,

  // 看護の視点
  // その他備考
  mo00045Oneway14: {
    ...mo00045OnewayType,
    showItemLabel: false,
    maxlength: '40',
    customClass: new CustomClass({
      outerClass: 'ml-2 mr-2',
      labelClass: 'ma-1',
      outerStyle: 'min-width: 300px',
    }),
  } as Mo00045OnewayType,

  // リハビリの視点
  // その他備考
  mo00045Oneway15: {
    ...mo00045OnewayType,
    showItemLabel: false,
    maxlength: '40',
    customClass: new CustomClass({
      outerClass: 'ml-2 mr-2',
      labelClass: 'ma-1',
      outerStyle: 'min-width: 300px',
    }),
  } as Mo00045OnewayType,

  // １回目
  mo00045Oneway16: {
    ...mo00045OnewayType,
    showItemLabel: false,
    maxlength: '40',
    customClass: new CustomClass({
      outerClass: 'ml-2 mr-2',
      labelClass: 'ma-1',
      outerStyle: 'width: 80%;',
    }),
  } as Mo00045OnewayType,
  // ２回目
  mo00045Oneway17: {
    ...mo00045OnewayType,
    showItemLabel: false,
    maxlength: '40',
    customClass: new CustomClass({
      outerClass: 'ml-2 mr-2',
      labelClass: 'ma-1',
      outerStyle: 'width: 80%;',
    }),
  } as Mo00045OnewayType,
  // ３回目
  mo00045Oneway18: {
    ...mo00045OnewayType,
    showItemLabel: false,
    maxlength: '40',
    customClass: new CustomClass({
      outerClass: 'ml-2 mr-2',
      labelClass: 'ma-1',
      outerStyle: 'width: 80%;',
    }),
  } as Mo00045OnewayType,

  // ラジオボタンコンポーネント
  // 「安定／不安定」選択
  mo00039DiseaseStateTypeOneway: {
    showItemLabel: false,
    inline: true,
    items: [] as Mo00039Items[],
  } as Mo00039OnewayType,
  // 「有無区分」選択---（なし／あり）
  mo00039PresenceOrAbsenceTypeOneway: {
    showItemLabel: false,
    inline: true,
    items: [] as Mo00039Items[],
  } as Mo00039OnewayType,
  // 「有無区分」選択---（あり／なし）
  mo00039PresenceOrAbsenceTypeOneway2: {
    showItemLabel: false,
    inline: true,
    items: [] as Mo00039Items[],
  } as Mo00039OnewayType,
  // 「嚥下機能状況区分」選択
  mo00039SwallowingFunctionTypeOneway: {
    showItemLabel: false,
    inline: true,
    items: [] as Mo00039Items[],
  } as Mo00039OnewayType,
  // 「義歯状況区分」選択
  mo00039ArtificialToothTypeOneway: {
    showItemLabel: false,
    inline: true,
    items: [] as Mo00039Items[],
  } as Mo00039OnewayType,
  // 「口腔清潔」選択
  mo00039OralCavityCleanlinessTypeOneway: {
    showItemLabel: false,
    inline: true,
    items: [] as Mo00039Items[],
  } as Mo00039OnewayType,
  // 「口腔ケア状況区分」選択
  mo00039OralCareTypeOneway: {
    showItemLabel: false,
    inline: true,
    items: [] as Mo00039Items[],
  } as Mo00039OnewayType,
  // 「睡眠状況区分」選択
  mo00039SleepingSituationTypeOneway: {
    showItemLabel: false,
    inline: true,
    items: [] as Mo00039Items[],
  } as Mo00039OnewayType,
  // 「会議出席状況区分」選択
  mo00039MeetingAttendanceTypeOneway: {
    showItemLabel: false,
    inline: true,
    items: [] as Mo00039Items[],
  } as Mo00039OnewayType,

  // ラベル 単方向モデルバリュー
  mo01338Oneway1: {
    // デフォルト値を設定
    value: t('label.left-parentheses‌'),
    itemLabelFontWeight: 'normal',
    customClass: new CustomClass({ outerStyle: 'padding: 6px;' }),
  } as Mo01338OnewayType,
  mo01338Oneway2: {
    // デフォルト値を設定
    value: t('label.right-parentheses‌'),
    itemLabelFontWeight: 'normal',
    customClass: new CustomClass({ outerStyle: 'padding: 6px;' }),
  } as Mo01338OnewayType,
  // 入院（所）中の使用
  mo01338Oneway3: {
    // デフォルト値を設定
    value: '入院（所）中の使用',
    itemLabelFontWeight: 'normal',
    customClass: new CustomClass({ outerStyle: 'padding: 6px;' }),
  } as Mo01338OnewayType,

  // GUI00649_［認定情報選択］画面
  or28287Oneway: {
    /** 利用者ID */
    userId: '1',
  } as Or28287OnewayType,
  // GUI01307_受診状況情報画面
  or51813OneWayType: {
    functionName: '',
    assessmentStyleNumber: '',
    userId: '',
    houjinId: '',
    shisetuId: '',
    svJigyoId: '',
  },

  // 確認ダイアログ
  mo00009DialogOneway: {
    msg: t('message.i-cmn-11276'),
    btnNoDisplay: false,
    btnCancelDisplay: false,
    mo00024Oneway: {
      class: 'mr-1',
      name: '',
      width: '360px',
    } as Mo00024OnewayType,
  } as OrCpGroupDefinitionInputFormDeleteDialogOnewayType,

  // 上書確認ダイアログ
  updateConfirm: {
    emitType: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  },
})

// 「移動手段」チェックボックス情報
const mo00018MoveMethodInfo = [
  ['mo00018Oneway7', 'mo00018Twoway7'],
  ['mo00018Oneway8', 'mo00018Twoway8'],
  ['mo00018Oneway9', 'mo00018Twoway9'],
  ['mo00018Oneway10', 'mo00018Twoway10'],
  ['mo00018Oneway11', 'mo00018Twoway11'],
] as const

// 「排泄方法」チェックボックス情報
const mo00018ExcretionMethodInfo = [
  ['mo00018Oneway12', 'mo00018Twoway12'],
  ['mo00018Oneway13', 'mo00018Twoway13'],
  ['mo00018Oneway14', 'mo00018Twoway14'],
  ['mo00018Oneway15', 'mo00018Twoway15'],
] as const

// 「入浴方法」チェックボックス情報
const mo00018BathingMethodInfo = [
  ['mo00018Oneway16', 'mo00018Twoway16'],
  ['mo00018Oneway17', 'mo00018Twoway17'],
  ['mo00018Oneway18', 'mo00018Twoway18'],
  ['mo00018Oneway19', 'mo00018Twoway19'],
  ['mo00018Oneway20', 'mo00018Twoway20'],
] as const

// 「食事形態」チェックボックス情報
const mo00018MealFormInfo = [
  ['mo00018Oneway21', 'mo00018Twoway21'],
  ['mo00018Oneway22', 'mo00018Twoway22'],
  ['mo00018Oneway23', 'mo00018Twoway23'],
] as const

// 「認知・精神」チェックボックス情報
const mo00018CognitiveMentalInfo = [
  ['mo00018Oneway24', 'mo00018Twoway24'],
  ['mo00018Oneway25', 'mo00018Twoway25'],
  ['mo00018Oneway26', 'mo00018Twoway26'],
  ['mo00018Oneway27', 'mo00018Twoway27'],
  ['mo00018Oneway28', 'mo00018Twoway28'],
  ['mo00018Oneway29', 'mo00018Twoway29'],
] as const

// 「医療処置の内容」チェックボックス情報
const mo00018MedicalcareTreatmentContentsInfo = [
  ['mo00018Oneway31', 'mo00018Twoway31'],
  ['mo00018Oneway32', 'mo00018Twoway32'],
  ['mo00018Oneway33', 'mo00018Twoway33'],
  ['mo00018Oneway34', 'mo00018Twoway34'],
  ['mo00018Oneway35', 'mo00018Twoway35'],
  ['mo00018Oneway36', 'mo00018Twoway36'],
  ['mo00018Oneway37', 'mo00018Twoway37'],
  ['mo00018Oneway38', 'mo00018Twoway38'],
  ['mo00018Oneway39', 'mo00018Twoway39'],
  ['mo00018Oneway40', 'mo00018Twoway40'],
  ['mo00018Oneway41', 'mo00018Twoway41'],
  ['mo00018Oneway42', 'mo00018Twoway42'],
  ['mo00018Oneway43', 'mo00018Twoway43'],
  ['mo00018Oneway44', 'mo00018Twoway44'],
  ['mo00018Oneway45', 'mo00018Twoway45'],
] as const

// 「看護の視点」チェックボックス情報
const mo00018NursingPerspectiveInfo = [
  ['mo00018Oneway46', 'mo00018Twoway46'],
  ['mo00018Oneway47', 'mo00018Twoway47'],
  ['mo00018Oneway48', 'mo00018Twoway48'],
  ['mo00018Oneway49', 'mo00018Twoway49'],
  ['mo00018Oneway50', 'mo00018Twoway50'],
  ['mo00018Oneway51', 'mo00018Twoway51'],
  ['mo00018Oneway52', 'mo00018Twoway52'],
  ['mo00018Oneway53', 'mo00018Twoway53'],
  ['mo00018Oneway54', 'mo00018Twoway54'],
  ['mo00018Oneway55', 'mo00018Twoway55'],
  ['mo00018Oneway56', 'mo00018Twoway56'],
  ['mo00018Oneway57', 'mo00018Twoway57'],
  ['mo00018Oneway58', 'mo00018Twoway58'],
  ['mo00018Oneway59', 'mo00018Twoway59'],
  ['mo00018Oneway60', 'mo00018Twoway60'],
  ['mo00018Oneway61', 'mo00018Twoway61'],
] as const

// 「リハビリの視点」チェックボックス情報
const mo00018RehabilitationPerspectiveInfo = [
  ['mo00018Oneway62', 'mo00018Twoway62'],
  ['mo00018Oneway63', 'mo00018Twoway63'],
  ['mo00018Oneway64', 'mo00018Twoway64'],
  ['mo00018Oneway65', 'mo00018Twoway65'],
  ['mo00018Oneway66', 'mo00018Twoway66'],
  ['mo00018Oneway67', 'mo00018Twoway67'],
  ['mo00018Oneway68', 'mo00018Twoway68'],
  ['mo00018Oneway69', 'mo00018Twoway69'],
  ['mo00018Oneway70', 'mo00018Twoway70'],
  ['mo00018Oneway71', 'mo00018Twoway71'],
  ['mo00018Oneway72', 'mo00018Twoway72'],
  ['mo00018Oneway73', 'mo00018Twoway73'],
  ['mo00018Oneway74', 'mo00018Twoway74'],
  ['mo00018Oneway75', 'mo00018Twoway75'],
  ['mo00018Oneway76', 'mo00018Twoway76'],
  ['mo00018Oneway77', 'mo00018Twoway77'],
  ['mo00018Oneway78', 'mo00018Twoway78'],
  ['mo00018Oneway79', 'mo00018Twoway79'],
] as const

// ローカルTwoway
const local = reactive({
  commonInfo: {} as TeX0012Type,

  // チェックボックスデータ
  // 要区分変更チェックボックス
  mo00018Twoway1: {
    modelValue: false,
  },
  // 要介護状態チェックボックス
  mo00018Twoway2: {
    modelValue: false,
  },
  // 申請中チェックボックス
  mo00018Twoway3: {
    modelValue: false,
  },
  // 要介護度_なしチェックボックス
  mo00018Twoway4: {
    modelValue: false,
  },

  // 通院
  mo00018Twoway5: {
    modelValue: false,
  },
  // 訪問診療
  mo00018Twoway6: {
    modelValue: false,
  },

  //移動手段
  // 自立
  mo00018Twoway7: {
    modelValue: false,
  },
  // 杖
  mo00018Twoway8: {
    modelValue: false,
  },
  // 歩行器
  mo00018Twoway9: {
    modelValue: false,
  },
  // 車いす
  mo00018Twoway10: {
    modelValue: false,
  },
  // その他
  mo00018Twoway11: {
    modelValue: false,
  },

  // 排泄方法
  // トイレ
  mo00018Twoway12: {
    modelValue: false,
  },
  // ポータブル
  mo00018Twoway13: {
    modelValue: false,
  },
  // おむつ
  mo00018Twoway14: {
    modelValue: false,
  },
  // カテーテル・パウチ
  mo00018Twoway15: {
    modelValue: false,
  },

  // 入浴方法
  // 自立
  mo00018Twoway16: {
    modelValue: false,
  },
  // シャワー浴
  mo00018Twoway17: {
    modelValue: false,
  },
  // 一般浴
  mo00018Twoway18: {
    modelValue: false,
  },
  // 機械浴
  mo00018Twoway19: {
    modelValue: false,
  },
  // 行わず
  mo00018Twoway20: {
    modelValue: false,
  },

  // 食事形態
  // 普通
  mo00018Twoway21: {
    modelValue: false,
  },
  // 経管栄養
  mo00018Twoway22: {
    modelValue: false,
  },
  // その他
  mo00018Twoway23: {
    modelValue: false,
  },

  // 認知・精神
  // 認知機能低下
  mo00018Twoway24: {
    modelValue: false,
  },
  // せん妄
  mo00018Twoway25: {
    modelValue: false,
  },
  // 徘徊
  mo00018Twoway26: {
    modelValue: false,
  },
  // 焦燥・不穏
  mo00018Twoway27: {
    modelValue: false,
  },
  // 攻撃性
  mo00018Twoway28: {
    modelValue: false,
  },
  // その他
  mo00018Twoway29: {
    modelValue: false,
  },

  // 医療処置の内容
  // なし
  mo00018Twoway30: {
    modelValue: false,
  },
  // 点滴
  mo00018Twoway31: {
    modelValue: false,
  },
  // 酸素療法
  mo00018Twoway32: {
    modelValue: false,
  },
  // 喀痰吸引
  mo00018Twoway33: {
    modelValue: false,
  },
  // 気管切開
  mo00018Twoway34: {
    modelValue: false,
  },
  // 胃ろう
  mo00018Twoway35: {
    modelValue: false,
  },
  // 経鼻栄養
  mo00018Twoway36: {
    modelValue: false,
  },
  // 経腸栄養
  mo00018Twoway37: {
    modelValue: false,
  },
  // 褥瘡
  mo00018Twoway38: {
    modelValue: false,
  },
  // 尿道カテーテル
  mo00018Twoway39: {
    modelValue: false,
  },
  // 尿路ストーマ
  mo00018Twoway40: {
    modelValue: false,
  },
  // 消化管ストーマ
  mo00018Twoway41: {
    modelValue: false,
  },
  // 痛みコントロール
  mo00018Twoway42: {
    modelValue: false,
  },
  // 排便コントロール
  mo00018Twoway43: {
    modelValue: false,
  },
  // 自己注射
  mo00018Twoway44: {
    modelValue: false,
  },
  // その他
  mo00018Twoway45: {
    modelValue: false,
  },

  // 看護の視点
  // なし
  mo00018Twoway80: {
    modelValue: false,
  },
  // 血圧
  mo00018Twoway46: {
    modelValue: false,
  },
  // 水分制限
  mo00018Twoway47: {
    modelValue: false,
  },
  // 食事制限
  mo00018Twoway48: {
    modelValue: false,
  },
  // 食形態
  mo00018Twoway49: {
    modelValue: false,
  },
  // 嚥下
  mo00018Twoway50: {
    modelValue: false,
  },
  // 口腔ケア
  mo00018Twoway51: {
    modelValue: false,
  },
  // 清潔ケア
  mo00018Twoway52: {
    modelValue: false,
  },
  // 血糖コントロール
  mo00018Twoway53: {
    modelValue: false,
  },
  // 排泄
  mo00018Twoway54: {
    modelValue: false,
  },
  // 皮膚状態
  mo00018Twoway55: {
    modelValue: false,
  },
  // 睡眠
  mo00018Twoway56: {
    modelValue: false,
  },
  // 認知機能・精神面
  mo00018Twoway57: {
    modelValue: false,
  },
  // 服薬指導
  mo00018Twoway58: {
    modelValue: false,
  },
  // 療養上の指導（食事・水分・睡眠・清潔ケア・排泄 などにおける指導）
  mo00018Twoway59: {
    modelValue: false,
  },
  // ターミナル
  mo00018Twoway60: {
    modelValue: false,
  },
  // その他
  mo00018Twoway61: {
    modelValue: false,
  },

  // リハビリの視点
  // なし
  mo00018Twoway81: {
    modelValue: false,
  },
  // 本人指導
  mo00018Twoway62: {
    modelValue: false,
  },
  // 家族指導
  mo00018Twoway63: {
    modelValue: false,
  },
  // 関節可動域練習（ｽﾄﾚｯﾁ含む）
  mo00018Twoway64: {
    modelValue: false,
  },
  // 筋力増強練習
  mo00018Twoway65: {
    modelValue: false,
  },
  // バランス練習
  mo00018Twoway66: {
    modelValue: false,
  },
  // 麻痺・筋緊張改善練習
  mo00018Twoway67: {
    modelValue: false,
  },
  // 起居／立位等基本動作練習
  mo00018Twoway68: {
    modelValue: false,
  },
  // 摂食・嚥下訓練
  mo00018Twoway69: {
    modelValue: false,
  },
  // 言語訓練
  mo00018Twoway70: {
    modelValue: false,
  },
  // ADL練習（歩行／入浴／トイレ動作／移乗等）
  mo00018Twoway71: {
    modelValue: false,
  },
  // IADL練習（買い物、調理等）
  mo00018Twoway72: {
    modelValue: false,
  },
  // 疼痛管理（痛みコントロール）
  mo00018Twoway73: {
    modelValue: false,
  },
  // 更生装具・福祉用具等管理
  mo00018Twoway74: {
    modelValue: false,
  },
  // 運動耐容能練習
  mo00018Twoway75: {
    modelValue: false,
  },
  // 地域活動支援
  mo00018Twoway76: {
    modelValue: false,
  },
  // 社会参加支援
  mo00018Twoway77: {
    modelValue: false,
  },
  // 就労支援
  mo00018Twoway78: {
    modelValue: false,
  },
  // その他
  mo00018Twoway79: {
    modelValue: false,
  },

  // GUI00649_［認定情報選択］画面
  or28287: {
    /** 保険者*/
    hokensya: '',
    /** 被保険者番号*/
    hHokenNo: '',
    /** 認定有効開始日（表示用）*/
    certificationValidityStartDate: '',
    /**  認定有効終了日（表示用）*/
    certificationValidityEndDate: '',
    /** 要介護度*/
    yokaiKnj: '',
    /** 限度額*/
    limit: '',
  },

  // 入院(所)日
  hospitalizationDate: {
    value: '',
  } as Mo00020Type,
  // 退院(所)予定日
  hospitalLeavingDate: {
    value: '',
  } as Mo00020Type,
  // 聞き取り日 １回目
  listenDate1: {
    value: '',
  } as Mo00020Type,
  // 聞き取り日 ２回目
  listenDate2: {
    value: '',
  } as Mo00020Type,
  // 聞き取り日 ３回目
  listenDate3: {
    value: '',
  } as Mo00020Type,

  // 入院原因疾患(入所目的等)
  mo00046CauseDisease: {
    value: '',
  } as Mo00046Type,
  // ＜本人＞病気、障害、後遺症等の受け止め方
  mo00046PersonAnnouncement: {
    value: '',
  } as Mo00046Type,
  // ＜本人＞退院後の生活に関する意向
  mo00046PersonIntention: {
    value: '',
  } as Mo00046Type,
  // ＜家族＞病気、障害、後遺症等の受け止め方
  mo00046FamilyAccept: {
    value: '',
  } as Mo00046Type,
  // ＜家族＞退院後の生活に関する意向
  mo00046FamilyIntention: {
    value: '',
  } as Mo00046Type,
  // 症状・病状の予後・予測
  mo00046Prognosis: {
    value: '',
  } as Mo00046Type,
  // 退院に際しての日常生活の阻害要因(心身状況・環境等)
  mo00046ObstructiveFactor: {
    value: '',
  } as Mo00046Type,
  // 在宅復帰のために整えなければならない要件
  mo00046ReturnHome: {
    value: '',
  } as Mo00046Type,

  // 入院原因疾患(入所目的等)
  mo00045_1: {
    value: '',
  } as Mo00045Type,
  mo00045_2: {
    value: '',
  } as Mo00045Type,
  mo00045_3: {
    value: '',
  } as Mo00045Type,
  // 医療機関名
  mo00045_4: {
    value: '',
  } as Mo00045Type,
  // 現在治療中の疾患⓵
  mo00045_5: {
    value: '',
  } as Mo00045Type,
  // 現在治療中の疾患⓶
  mo00045_6: {
    value: '',
  } as Mo00045Type,
  // 現在治療中の疾患⓷
  mo00045_7: {
    value: '',
  } as Mo00045Type,
  // 移動手段 テキストボックス
  mo00045_8: {
    value: '',
  } as Mo00045Type,
  // 排泄方法 テキストボックス
  mo00045_9: {
    value: '',
  } as Mo00045Type,
  // 食事形態 テキストボックス
  mo00045_10: {
    value: '',
  } as Mo00045Type,
  // UDF等の食形態区分 テキストボックス
  mo00045_11: {
    value: '',
  } as Mo00045Type,
  // 睡眠 テキストボックス
  mo00045_12: {
    value: '',
  } as Mo00045Type,
  // 認知・精神 テキストボックス
  mo00045_13: {
    value: '',
  } as Mo00045Type,
  // 医療処置の内容 自己注射 テキストボックス
  mo00045_14: {
    value: '',
  } as Mo00045Type,
  // 医療処置の内容 自己注射 その他
  mo00045_15: {
    value: '',
  } as Mo00045Type,
  // 看護の視点 その他
  mo00045_16: {
    value: '',
  } as Mo00045Type,
  // リハビリの視点 その他
  mo00045_17: {
    value: '',
  } as Mo00045Type,
  // 禁忌事項 テキストボックス
  mo00045_18: {
    value: '',
  } as Mo00045Type,
  // 情報提供を受けた職種(氏名)１ テキストボックス
  mo00045_19: {
    value: '',
  } as Mo00045Type,
  // 情報提供を受けた職種(氏名)２ テキストボックス
  mo00045_20: {
    value: '',
  } as Mo00045Type,
  // 情報提供を受けた職種(氏名)３ テキストボックス
  mo00045_21: {
    value: '',
  } as Mo00045Type,

  // 安定／不安定⓵
  mo00039DiseaseStateSelectType1: {
    value: '',
  },
  // 安定／不安定⓶
  mo00039DiseaseStateSelectType2: {
    value: '',
  },
  // 安定／不安定⓷
  mo00039DiseaseStateSelectType3: {
    value: '',
  },

  // 会議出席⓵
  mo00039MeetingAttendanceType1: {
    value: '',
  },
  // 会議出席⓶
  mo00039MeetingAttendanceType2: {
    value: '',
  },
  // 会議出席⓷
  mo00039MeetingAttendanceType3: {
    value: '',
  },

  // 有無区分 咽有無
  mo00039CoughKbnSelectType: {
    value: '',
  },
  // 有無区分 義歯_有無
  mo00039DentureKbnSelectType: {
    value: '',
  },
  // 有無区分 眠剤使用
  mo00039SleepDrugSelectType: {
    value: '',
  },
  // 有無区分 本人への病名告知
  mo00039PersonAnnouncementSelectType: {
    value: '',
  },
  // 有無区分 禁忌の有無
  mo00039ContraindicationSelectType: {
    value: '',
  },

  // 嚥下機能状況区分
  mo00039SwallowingFunctionSelectType: {
    value: '',
  },
  // 義歯状況区分
  mo00039ArtificialToothSelectType: {
    value: '',
  },
  // 入院（所）中の使用
  mo00039PresenceOrAbsenceTypeSelectType: {
    value: '',
  },
  // 口腔清潔
  mo00039OralCavityCleanlinessSelectType: {
    value: '',
  },
  // 口腔ケア状況区分
  mo00039OralCareSelectType: {
    value: '',
  },
  // 睡眠状況区分
  mo00039SleepingSituationSelectType: {
    value: '',
  },
  // 会議出席状況区分
  mo00039MeetingAttendanceSelectType: {
    value: '',
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 汎用コード取得API実行
  await initCodes()
  $log.debug(`★[onMounted] [cpId]${Or06146Const.CP_ID(0)} [uId]${props.uniqueCpId}`)
  await reload()
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {})

/**************************************************
 * 変数（表示データ）
 **************************************************/
/** 入力フォーム */

/**************************************************
 * 関数
 **************************************************/
/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 381:疾病状態区分[1：安定 2：不安定]
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DISEASE_STATE_CATEGORY },
    // 380:有無区分[1：なし 2：あり]
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRESENCE_OR_ABSENCE_TYPE },
    // 379:嚥下機能状況区分[1：時々 2：常に]
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SWALLOWING_FUNCTION_SITUATION },
    // 378:義歯状況区分[1：部分 2：総]
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ARTIFICIALTOOTH_SITUATION },
    // 293:口腔清潔[1：良 2：不良 3：著しく不良]
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_ORAL_CAVITY_CLEANLINESS },
    // 376:口腔ケア状況区分[1:自立 2:一部介助 3:全介助]
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ORAL_CARE_STATE },
    // 375:睡眠状況区分[1：良好 2：不良]
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SLEEPING_SITUATION_NURSINGCARE },
    // 374:会議出席状況区分[1:無 2:有]
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEETING_ATTENDANCE_SITUATION },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 疾病状態区分
  setCodeTypeToLocalState(
    CmnMCdKbnId.M_CD_KBN_ID_DISEASE_STATE_CATEGORY,
    local.mo00039DiseaseStateSelectType1,
    'mo00039DiseaseStateTypeOneway'
  )
  // 有無区分１「なし／あり」
  setCodeTypeToLocalState(
    CmnMCdKbnId.M_CD_KBN_ID_PRESENCE_OR_ABSENCE_TYPE,
    local.mo00039SleepDrugSelectType,
    'mo00039PresenceOrAbsenceTypeOneway'
  )
  // 有無区分２「あり／なし」
  setCodeTypeToLocalState(
    CmnMCdKbnId.M_CD_KBN_ID_PRESENCE_OR_ABSENCE_TYPE,
    local.mo00039SleepDrugSelectType,
    'mo00039PresenceOrAbsenceTypeOneway2'
  )
  // 嚥下機能状況区分
  setCodeTypeToLocalState(
    CmnMCdKbnId.M_CD_KBN_ID_SWALLOWING_FUNCTION_SITUATION,
    local.mo00039SwallowingFunctionSelectType,
    'mo00039SwallowingFunctionTypeOneway'
  )
  // 義歯状況区分
  setCodeTypeToLocalState(
    CmnMCdKbnId.M_CD_KBN_ID_ARTIFICIALTOOTH_SITUATION,
    local.mo00039ArtificialToothSelectType,
    'mo00039ArtificialToothTypeOneway'
  )
  // 口腔清潔
  setCodeTypeToLocalState(
    CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_ORAL_CAVITY_CLEANLINESS,
    local.mo00039OralCavityCleanlinessSelectType,
    'mo00039OralCavityCleanlinessTypeOneway'
  )
  // 口腔ケア状況区分
  setCodeTypeToLocalState(
    CmnMCdKbnId.M_CD_KBN_ID_ORAL_CARE_STATE,
    local.mo00039OralCareSelectType,
    'mo00039OralCareTypeOneway'
  )
  // 睡眠状況区分
  setCodeTypeToLocalState(
    CmnMCdKbnId.M_CD_KBN_ID_SLEEPING_SITUATION_NURSINGCARE,
    local.mo00039SleepingSituationSelectType,
    'mo00039SleepingSituationTypeOneway'
  )
  // 会議出席状況区分
  setCodeTypeToLocalState(
    CmnMCdKbnId.M_CD_KBN_ID_MEETING_ATTENDANCE_SITUATION,
    local.mo00039MeetingAttendanceSelectType,
    'mo00039MeetingAttendanceTypeOneway'
  )
}

/**
 * コードマスターデータ設定の共通処理
 *
 * @param codeId - コードキー
 *
 * @param localRef - 設定値
 *
 * @param propertyName - 設定対象
 */
const setCodeTypeToLocalState = (
  codeId: number,
  localRef: { value: string },
  propertyName: string
) => {
  const codeTypes: CodeType[] = CmnSystemCodeRepository.filter(codeId)

  if (codeTypes?.length > 0) {
    // 1. デフォルト値設定
    localRef.value = codeTypes[0].value

    let items = []
    // 2. リスト情報取得
    // const items = codeTypes
    if ('mo00039PresenceOrAbsenceTypeOneway' === propertyName) {
      items = codeTypes
        .filter((item): item is CodeType => item !== null)
        .map(
          (item) =>
            ({
              label: item.label,
              value: item.value,
            }) as unknown as Mo00039Items
        )
        .sort((a, b) => a.value - b.value)
    } else {
      items = codeTypes
        .filter((item): item is CodeType => item !== null)
        .map(
          (item) =>
            ({
              label: item.label,
              value: item.value,
            }) as unknown as Mo00039Items
        )
    }

    ;(localOneway[propertyName as keyof typeof localOneway] as { items: Mo00039Items[] }).items =
      items
  }
}

/**
 * 有無区分チェック処理
 *
 * @param type - 種類
 *
 * @param setValue - 設定された値
 */
const presenceCheck = (type: string, setValue: string) => {
  if ('CoughKbn' === type) {
    // 嚥下機能(むせ) 有無区分
    if ('2' === setValue) {
      // 嚥下機能(むせ)状況区分選択を解除する
      local.mo00039SwallowingFunctionSelectType.value = ''
    }
  } else if ('SwallowingFunction' === type) {
    // 嚥下機能状況区分
    local.mo00039CoughKbnSelectType.value = '2'
  } else if ('DentureKbn' === type) {
    // 義歯状況区分 有無区分
    if ('2' === setValue) {
      // 嚥下機能(むせ)状況区分選択を解除する
      local.mo00039ArtificialToothSelectType.value = ''
    }
  } else if ('ArtificialTooth' === type) {
    // 嚥下機能状況区分
    local.mo00039DentureKbnSelectType.value = '2'
  }
}

/**
 * 有無区分チェック処理
 *
 * @param type - 種類
 *
 * @param modelValue - チェックフラグ
 */
const nothingCheck = (type: string, modelValue: boolean) => {
  if (!modelValue) {
    // 未チェックする場合、何もしない
    return
  }
  if ('treatmentNone' === type) {
    // 医療処置の内容「なし」
    for (const item of mo00018MedicalcareTreatmentContentsInfo) {
      // 医療処置の内容 を「未チェック」に設定する
      local[item[1]].modelValue = false
    }
  } else if ('nurseNone' === type) {
    // 看護の視点
    for (const item of mo00018NursingPerspectiveInfo) {
      // 医療処置の内容 を「未チェック」に設定する
      local[item[1]].modelValue = false
    }
  } else if ('rehabNone' === type) {
    // リハビリの視点
    for (const item of mo00018RehabilitationPerspectiveInfo) {
      // 医療処置の内容 を「未チェック」に設定する
      local[item[1]].modelValue = false
    }
  } else if ('treatment' === type) {
    // 「医療処置の内容」明細がチェックある場合、「無し」をチェック外にする
    if (local.mo00018Twoway30.modelValue) {
      local.mo00018Twoway30.modelValue = false
    }
  } else if ('nurse' === type) {
    // 「看護の視点」明細がチェックある場合、「無し」をチェック外にする
    if (local.mo00018Twoway80.modelValue) {
      local.mo00018Twoway80.modelValue = false
    }
  } else if ('rehab' === type) {
    // 「リハビリの視点」明細がチェックある場合、「無し」をチェック外にする
    if (local.mo00018Twoway81.modelValue) {
      local.mo00018Twoway81.modelValue = false
    }
  }
}

// ［GUI00649_認定情報選択］画面
const or28287 = ref({ uniqueCpId: '' })
// ［GUI01307_受診状況情報］画面
const or51813 = ref({ uniqueCpId: '' })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or28287Const.CP_ID(1)]: or28287.value,
  [Or51813Const.CP_ID(1)]: or51813.value,
})

function getUserManagementInfo(data: UserManagementInfo) {
  console.log(data, '===>UserManagementInfo')
}
function getAttendingPhysicianInfo(data: AttendingPhysicianInfo) {
  console.log(data, '===>AttendingPhysicianInfo')
}

/**
 * ダイアログ「ボタン」押下の処理
 *
 * @param dialogId - ダイアログ画面ID
 */
const handleByDialog = (dialogId: string) => {
  if ('Or28287' === dialogId) {
    // Or28287のダイアログ開閉状態を更新する
    Or28287Logic.state.set({
      uniqueCpId: or28287.value.uniqueCpId,
      state: { isOpen: true },
    })
  } else if ('Or51813' === dialogId) {
    // Or51813のダイアログ開閉状態を更新する
    Or51813Logic.state.set({
      uniqueCpId: or51813.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

// ダイアログ表示フラグ
const showDialog = (dialogId: string) => {
  if ('Or28287' === dialogId) {
    // Or28287のダイアログ開閉状態
    return Or28287Logic.state.get(or28287.value.uniqueCpId)?.isOpen ?? false
  } else if ('Or51813' === dialogId) {
    // Or51813のダイアログ開閉状態
    return Or51813Logic.state.get(or51813.value.uniqueCpId)?.isOpen ?? false
  }
}

const isLoading = ref(false)
/**
 *  コントロール初期化
 */
async function reload() {
  isLoading.value = true

  let start: number = performance.now()
  // clearData()
  let end: number = performance.now()
  console.log(`initCodes実行時間: ${(end - start).toFixed(3)} ミリ秒`)

  start = performance.now()
  // 汎用コードマスタからコード情報を取得
  // await initCodes()

  // 画面初期情報取得
  await getInitDataInfo()
  end = performance.now()
  console.log(`getInitDataInfo実行時間: ${(end - start).toFixed(3)} ミリ秒`)
  isLoading.value = false
}

/**
 *  画面初期情報取得
 */
async function getInitDataInfo() {
  const inputData: DischargeFromHospitalLeavingInfoInitInfoSelectInEntity = {
    recId: '',
    shokuinId: '',
    svJigyoList: [''],
    gsysCd: '',
  }
  const resData: DischargeFromHospitalLeavingInfoInitInfoSelectOutEntity =
    await ScreenRepository.select('dischargeDepartureInfoInitSelect', inputData)

  // 画面情報を設定
  if ('success' === resData.statusCode && resData.data) {
    setFormData(resData)
  } else {
    // if (props.onewayModelValue?.mode === Or31535Const.DEFAULT.MODE_COPY) {
    //   setOr35672State({ noData: true })
    // }
  }

  // const startF: number = performance.now()
  // try {
  //   const start: number = performance.now()
  //   const param: IAssessmentFaceInfoSelectInEntity = {
  //     /** 改定フラグ */
  //     ninteiFormF: local.commonInfo.ninteiFormF,
  //     /** 施設ID */
  //     shisetuId: '',

  //     /** 法人ID */
  //     houjinId: '',
  //     /** 事業所ID */
  //     svJigyoId: local.commonInfo.jigyoId,
  //     /** 計画期間ID */
  //     sc1Id: local.commonInfo.sc1Id,
  //     /** 履歴ID */
  //     gdlId: local.commonInfo.gdlId,
  //     /** 利用者ID */
  //     userid: local.commonInfo.createUserId,
  //     /** 履歴作成日 */
  //     kijunbiYmd: local.commonInfo.createYmd,
  //   }

  //   const res: IAssessmentFaceInfoSelectOutEntity = await ScreenRepository.select(
  //     'assessmentFaceInfoSelect',
  //     param
  //   )
  //   const end: number = performance.now()
  //   console.log(`assessmentHomeTab61Select API実行時間: ${(end - start).toFixed(3)} ミリ秒`)
  //   // 画面情報を設定
  //   if (res.statusCode === ResBodyStatusCode.SUCCESS) {
  //     setFormData(res)
  //   } else {
  //     if (props.onewayModelValue?.mode === Or30732Const.DEFAULT.MODE_COPY) {
  //       setOr35672State({ noData: true })
  //     }
  //   }
  // } catch (e: unknown) {
  //   console.log(e)
  // }
  // const endF: number = performance.now()
  // console.log(`getInitDataInfo メソッド内実行時間: ${(endF - startF).toFixed(3)} ミリ秒`)
}

/**
 *  画面データを設定
 *
 * @param resData - 設定データ
 */
function setFormData(resData: DischargeFromHospitalLeavingInfoInitInfoSelectOutEntity) {
  // 複写モードの場合、APIから取得のデータを保持
  if (props.onewayModelValue.mode === Or31535Const.DEFAULT.MODE_COPY) {
    // Or31535Logic.data.set({
    //   uniqueCpId: props.uniqueCpId,
    //   value: {
    //     copyData: resData,
    //   },
    // })
  }

  if (resData.data && typeof resData.data === 'object' && Object.keys(resData.data).length === 0) {
    // if (props.onewayModelValue?.mode === Or31535Const.DEFAULT.MODE_COPY) {
    //   setOr35672State({ noData: true })
    // }
    // return
  }

  // 退院(所)時の要介護度
  // 退院・退所情報記録書情報.要区分変更
  // 退院・退所情報記録書情報.要介護状態
  // 退院・退所情報記録書情報.申請中
  // 退院・退所情報記録書情報.退院(所)時の要介護度_なし
  setCheckBoxData(local.mo00018Twoway1, resData.data.cpnTucTaiKirokuData.certifiedChange)
  setCheckBoxData(local.mo00018Twoway2, resData.data.cpnTucTaiKirokuData.certifiedStatusYokaigo)
  setCheckBoxData(local.mo00018Twoway3, resData.data.cpnTucTaiKirokuData.certifiedStatusApplication)
  setCheckBoxData(local.mo00018Twoway4, resData.data.cpnTucTaiKirokuData.certifiedStatusUnapplied)

  // 退院・退所情報記録書情報.要介護度

  // 入院（所）概要
  // 退院退所情報記録書情報.入院（所）日
  // 退院退所情報記録書情報.退院(所)予定日
  local.hospitalizationDate.value = resData.data.cpnTucTaiKirokuData.admissionYmd ?? ''
  local.hospitalLeavingDate.value = resData.data.cpnTucTaiKirokuData.dischargeYmd ?? ''

  // 入院原因疾患(入所目的等)
  // 退院退所情報記録書情報.入院原因疾患
  // 退院退所情報記録書情報.施設名
  // 退院退所情報記録書情報.施設棟
  // 退院退所情報記録書情報.施設室
  // 退院退所情報記録書情報.医療機関名
  local.mo00046CauseDisease.value = resData.data.cpnTucTaiKirokuData.causeDiseaseKnj ?? ''
  local.mo00045_1.value = resData.data.cpnTucTaiKirokuData.facilityKnj ?? ''
  local.mo00045_2.value = resData.data.cpnTucTaiKirokuData.facilityWardKnj ?? ''
  local.mo00045_3.value = resData.data.cpnTucTaiKirokuData.facilityRoomKnj ?? ''
  local.mo00045_4.value = resData.data.cpnTucTaiKirokuData.medicHospKnj ?? ''

  // 退院退所情報記録書情報.通院
  // 退院退所情報記録書情報.訪問診療
  setCheckBoxData(local.mo00018Twoway5, resData.data.cpnTucTaiKirokuData.medicOutVisit)
  setCheckBoxData(local.mo00018Twoway6, resData.data.cpnTucTaiKirokuData.medicVisit)

  // 「①疾患と入院（所）中の状況」
  // 「現在治療中の疾患」
  // 退院退所情報記録書情報.現在治療中の疾患①_疾患名
  // 退院退所情報記録書情報.現在治療中の疾患①_安定状況
  // 退院退所情報記録書情報.現在治療中の疾患②_疾患名
  // 退院退所情報記録書情報. 現在治療中の疾患②_安定状況
  // 退院退所情報記録書情報.現在治療中の疾患③_疾患名
  // 退院退所情報記録書情報. 現在治療中の 現在治療中の疾患③_安定状況
  local.mo00045_5.value = resData.data.cpnTucTaiKirokuData.disease1SickKnj ?? ''
  local.mo00039DiseaseStateSelectType1.value = resData.data.cpnTucTaiKirokuData.disease1Status ?? ''
  local.mo00045_6.value = resData.data.cpnTucTaiKirokuData.disease2SickKnj ?? ''
  local.mo00039DiseaseStateSelectType2.value = resData.data.cpnTucTaiKirokuData.disease2Status ?? ''
  local.mo00045_7.value = resData.data.cpnTucTaiKirokuData.disease3SickKnj ?? ''
  local.mo00039DiseaseStateSelectType3.value = resData.data.cpnTucTaiKirokuData.disease3Status ?? ''

  // 「移動手段」
  // 退院退所情報記録書情報.移動手段_自立
  // 退院退所情報記録書情報.移動手段_杖
  // 退院退所情報記録書情報.移動手段_歩行器
  // 退院退所情報記録書情報.移動手段_車
  // 退院退所情報記録書情報.移動手段_その他
  setCheckBoxData(local.mo00018Twoway7, resData.data.cpnTucTaiKirokuData.move1)
  setCheckBoxData(local.mo00018Twoway8, resData.data.cpnTucTaiKirokuData.move2)
  setCheckBoxData(local.mo00018Twoway9, resData.data.cpnTucTaiKirokuData.move3)
  setCheckBoxData(local.mo00018Twoway10, resData.data.cpnTucTaiKirokuData.move4)
  setCheckBoxData(local.mo00018Twoway11, resData.data.cpnTucTaiKirokuData.move5)
  local.mo00045_8.value = resData.data.cpnTucTaiKirokuData.move5Knj ?? ''

  // 「排泄方法」
  // 退院退所情報記録書情報.排泄方法_トイレ
  // 退院退所情報記録書情報.排泄方法_ポータブル
  // 退院退所情報記録書情報.排泄方法_おむつ
  // 退院退所情報記録書情報.排泄方法_カテーテル・パウチ
  // 退院退所情報記録書情報.排泄方法_カテーテル・パウチ_備考
  setCheckBoxData(local.mo00018Twoway12, resData.data.cpnTucTaiKirokuData.toilet1)
  setCheckBoxData(local.mo00018Twoway13, resData.data.cpnTucTaiKirokuData.toilet2)
  setCheckBoxData(local.mo00018Twoway14, resData.data.cpnTucTaiKirokuData.toilet3)
  setCheckBoxData(local.mo00018Twoway15, resData.data.cpnTucTaiKirokuData.toilet4)
  local.mo00045_9.value = resData.data.cpnTucTaiKirokuData.toilet4Knj ?? ''

  // 「入浴方法」
  // 退院退所情報記録書情報.入浴方法_自立
  // 退院退所情報記録書情報.入浴方法_シャワー浴
  // 退院退所情報記録書情報.入浴方法_一般浴
  // 退院退所情報記録書情報.入浴方法_機械浴
  // 退院退所情報記録書情報.入浴方法_行わず
  setCheckBoxData(local.mo00018Twoway16, resData.data.cpnTucTaiKirokuData.bath1)
  setCheckBoxData(local.mo00018Twoway17, resData.data.cpnTucTaiKirokuData.bath2)
  setCheckBoxData(local.mo00018Twoway18, resData.data.cpnTucTaiKirokuData.bath3)
  setCheckBoxData(local.mo00018Twoway19, resData.data.cpnTucTaiKirokuData.bath4)
  setCheckBoxData(local.mo00018Twoway20, resData.data.cpnTucTaiKirokuData.bath5)

  // 「食事形態」
  // 退院退所情報記録書情報.普通
  // 退院退所情報記録書情報.経管栄養
  // 退院退所情報記録書情報.食事形態_その他
  // 退院退所情報記録書情報.食事形態_その他_備考
  setCheckBoxData(local.mo00018Twoway21, resData.data.cpnTucTaiKirokuData.meal1)
  setCheckBoxData(local.mo00018Twoway22, resData.data.cpnTucTaiKirokuData.meal2)
  setCheckBoxData(local.mo00018Twoway23, resData.data.cpnTucTaiKirokuData.meal3)
  local.mo00045_10.value = resData.data.cpnTucTaiKirokuData.meal3Knj ?? ''

  // 退院退所情報記録書情報.UDF等の食形態区分
  local.mo00045_11.value = resData.data.cpnTucTaiKirokuData.mealUdfKnj ?? ''

  // 「嚥下機能(むせ)」
  // 退院退所情報記録書情報.嚥下機能(むせ)有無区分
  // 退院退所情報記録書情報.嚥下機能(むせ)状況区分_常に
  local.mo00039CoughKbnSelectType.value = resData.data.cpnTucTaiKirokuData.coughKbn ?? ''
  local.mo00039SwallowingFunctionSelectType.value =
    resData.data.cpnTucTaiKirokuData.coughStatus ?? ''

  // 退院退所情報記録書情報.口腔清潔状況区分
  // 退院退所情報記録書情報.義歯有無区分
  // 退院退所情報記録書情報.義歯状況区分
  // 退院退所情報記録書情報.義入院（所）中の使用有無区分
  // 退院退所情報記録書情報.口腔ケア状況区分
  // 退院退所情報記録書情報.睡眠状況区分
  // 退院退所情報記録書情報.睡眠_状況_備考
  // 退院退所情報記録書情報.眠剤使用
  local.mo00039OralCavityCleanlinessSelectType.value =
    resData.data.cpnTucTaiKirokuData.oralCleanliness ?? ''
  local.mo00039DentureKbnSelectType.value = resData.data.cpnTucTaiKirokuData.dentureKbn ?? ''
  local.mo00039ArtificialToothSelectType.value =
    resData.data.cpnTucTaiKirokuData.dentureStatus ?? ''
  local.mo00039PresenceOrAbsenceTypeSelectType.value =
    resData.data.cpnTucTaiKirokuData.dentureUse ?? ''
  local.mo00039OralCareSelectType.value = resData.data.cpnTucTaiKirokuData.oralCare ?? ''
  local.mo00039SleepingSituationSelectType.value =
    resData.data.cpnTucTaiKirokuData.sleepStatus ?? ''
  local.mo00045_12.value = resData.data.cpnTucTaiKirokuData.sleepStatusKnj ?? ''
  local.mo00039SleepDrugSelectType.value = resData.data.cpnTucTaiKirokuData.sleepDrug ?? ''

  // 「認知・精神」
  // 退院退所情報記録書情報.認知機能低下
  // 退院退所情報記録書情報.せん妄
  // 退院退所情報記録書情報.徘徊
  // 退院退所情報記録書情報.焦燥・不穏
  // 退院退所情報記録書情報.攻撃性
  // 退院退所情報記録書情報.認知精神_その他
  // 退院退所情報記録書情報.認知精神_その他_備考
  setCheckBoxData(local.mo00018Twoway24, resData.data.cpnTucTaiKirokuData.bpsd1)
  setCheckBoxData(local.mo00018Twoway25, resData.data.cpnTucTaiKirokuData.bpsd2)
  setCheckBoxData(local.mo00018Twoway26, resData.data.cpnTucTaiKirokuData.bpsd3)
  setCheckBoxData(local.mo00018Twoway27, resData.data.cpnTucTaiKirokuData.bpsd4)
  setCheckBoxData(local.mo00018Twoway28, resData.data.cpnTucTaiKirokuData.bpsd5)
  setCheckBoxData(local.mo00018Twoway29, resData.data.cpnTucTaiKirokuData.bpsd6)
  local.mo00045_13.value = resData.data.cpnTucTaiKirokuData.bpsd6Knj ?? ''

  // 「②受け止め/意向」
  // 退院退所情報記録書情報.本人への病名
  // 退院退所情報記録書情報.本人への病名告知
  // 退院退所情報記録書情報.本人_退院後の生活に関する意向
  // 退院退所情報記録書情報.家族_病気障害後遺症等の受け止め方
  // 退院退所情報記録書情報.家族_退院後の生活に関する意向
  local.mo00039PersonAnnouncementSelectType.value =
    resData.data.cpnTucTaiKirokuData.personAnnouncement ?? ''
  local.mo00046PersonAnnouncement.value = resData.data.cpnTucTaiKirokuData.personAcceptKnj ?? ''
  local.mo00046PersonIntention.value = resData.data.cpnTucTaiKirokuData.personIntentionKnj ?? ''
  local.mo00046FamilyAccept.value = resData.data.cpnTucTaiKirokuData.familyAcceptKnj ?? ''
  local.mo00046FamilyIntention.value = resData.data.cpnTucTaiKirokuData.familyIntentionKnj ?? ''

  // 「医療処置の内容」
  // 退院退所情報記録書情報.医療処置の内容_なし
  // 退院退所情報記録書情報.医療処置の内容_点滴
  // 退院退所情報記録書情報.医療処置の内容_酸素療法
  // 退院退所情報記録書情報.医療処置の内容_喀痰吸引
  // 退院退所情報記録書情報.医療処置の内容_気管切開
  // 退院退所情報記録書情報.医療処置の内容_胃
  // 退院退所情報記録書情報.医療処置の内容_経鼻栄養
  // 退院退所情報記録書情報.医療処置の内容_経腸栄養
  // 退院退所情報記録書情報.医療処置の内容_褥瘡
  // 退院退所情報記録書情報.医療処置の内容_尿道
  // 退院退所情報記録書情報.医療処置の内容_尿路
  // 退院退所情報記録書情報.医療処置の内容_消化管
  // 退院退所情報記録書情報.医療処置の内容_痛
  // 退院退所情報記録書情報.医療処置の内容_排便
  // 退院退所情報記録書情報.医療処置の内容_自己注射
  // 退院退所情報記録書情報.医療処置の内容_自己注射備考
  // 退院退所情報記録書情報.医療処置の内容_その他
  // 退院退所情報記録書情報.医療処置の内容_その他備考
  setCheckBoxData(local.mo00018Twoway30, resData.data.cpnTucTaiKirokuData.treatmentNone)
  setCheckBoxData(local.mo00018Twoway31, resData.data.cpnTucTaiKirokuData.treatment1)
  setCheckBoxData(local.mo00018Twoway32, resData.data.cpnTucTaiKirokuData.treatment2)
  setCheckBoxData(local.mo00018Twoway33, resData.data.cpnTucTaiKirokuData.treatment3)
  setCheckBoxData(local.mo00018Twoway34, resData.data.cpnTucTaiKirokuData.treatment4)
  setCheckBoxData(local.mo00018Twoway35, resData.data.cpnTucTaiKirokuData.treatment5)
  setCheckBoxData(local.mo00018Twoway36, resData.data.cpnTucTaiKirokuData.treatment6)
  setCheckBoxData(local.mo00018Twoway37, resData.data.cpnTucTaiKirokuData.treatment7)
  setCheckBoxData(local.mo00018Twoway38, resData.data.cpnTucTaiKirokuData.treatment8)
  setCheckBoxData(local.mo00018Twoway39, resData.data.cpnTucTaiKirokuData.treatment9)
  setCheckBoxData(local.mo00018Twoway40, resData.data.cpnTucTaiKirokuData.treatment10)
  setCheckBoxData(local.mo00018Twoway41, resData.data.cpnTucTaiKirokuData.treatment11)
  setCheckBoxData(local.mo00018Twoway42, resData.data.cpnTucTaiKirokuData.treatment12)
  setCheckBoxData(local.mo00018Twoway43, resData.data.cpnTucTaiKirokuData.treatment13)
  setCheckBoxData(local.mo00018Twoway44, resData.data.cpnTucTaiKirokuData.treatment14)
  setCheckBoxData(local.mo00018Twoway45, resData.data.cpnTucTaiKirokuData.treatment15)

  local.mo00045_14.value = resData.data.cpnTucTaiKirokuData.treatment14Knj ?? ''
  local.mo00045_15.value = resData.data.cpnTucTaiKirokuData.treatment15Knj ?? ''

  // 「看護の視点」
  // 退院退所情報記録書情報.看護の視点_なし
  // 退院退所情報記録書情報.看護の視点_血圧
  // 退院退所情報記録書情報.看護の視点_水分制限
  // 退院退所情報記録書情報.看護の視点_食事制限
  // 退院退所情報記録書情報.看護の視点_食形態
  // 退院退所情報記録書情報.看護の視点_嚥下
  // 退院退所情報記録書情報.看護の視点_口腔
  // 退院退所情報記録書情報.看護の視点_清潔
  // 退院退所情報記録書情報.看護の視点_血糖
  // 退院退所情報記録書情報.看護の視点_排泄
  // 退院退所情報記録書情報.看護の視点_皮膚状態
  // 退院退所情報記録書情報.看護の視点_睡眠
  // 退院退所情報記録書情報.看護の視点_認知機能・精神面
  // 退院退所情報記録書情報.看護の視点_服薬指導
  // 退院退所情報記録書情報.看護の視点_療養上の指導（食事・水分・睡眠・清潔ケア・排泄 などにおける指導）
  // 退院退所情報記録書情報.看護の視点_ターミナル
  // 退院退所情報記録書情報.看護の視点_看護の視点_その他
  // 退院退所情報記録書情報.看護の視点_その他_備考
  setCheckBoxData(local.mo00018Twoway80, resData.data.cpnTucTaiKirokuData.nurseNone)
  setCheckBoxData(local.mo00018Twoway46, resData.data.cpnTucTaiKirokuData.nurse1)
  setCheckBoxData(local.mo00018Twoway47, resData.data.cpnTucTaiKirokuData.nurse2)
  setCheckBoxData(local.mo00018Twoway48, resData.data.cpnTucTaiKirokuData.nurse3)
  setCheckBoxData(local.mo00018Twoway49, resData.data.cpnTucTaiKirokuData.nurse4)
  setCheckBoxData(local.mo00018Twoway50, resData.data.cpnTucTaiKirokuData.nurse5)
  setCheckBoxData(local.mo00018Twoway51, resData.data.cpnTucTaiKirokuData.nurse6)
  setCheckBoxData(local.mo00018Twoway52, resData.data.cpnTucTaiKirokuData.nurse7)
  setCheckBoxData(local.mo00018Twoway53, resData.data.cpnTucTaiKirokuData.nurse8)
  setCheckBoxData(local.mo00018Twoway54, resData.data.cpnTucTaiKirokuData.nurse9)
  setCheckBoxData(local.mo00018Twoway55, resData.data.cpnTucTaiKirokuData.nurse10)
  setCheckBoxData(local.mo00018Twoway56, resData.data.cpnTucTaiKirokuData.nurse11)
  setCheckBoxData(local.mo00018Twoway57, resData.data.cpnTucTaiKirokuData.nurse12)
  setCheckBoxData(local.mo00018Twoway58, resData.data.cpnTucTaiKirokuData.nurse13)
  setCheckBoxData(local.mo00018Twoway59, resData.data.cpnTucTaiKirokuData.nurse14)
  setCheckBoxData(local.mo00018Twoway60, resData.data.cpnTucTaiKirokuData.nurse15)
  setCheckBoxData(local.mo00018Twoway61, resData.data.cpnTucTaiKirokuData.nurse16)

  local.mo00045_16.value = resData.data.cpnTucTaiKirokuData.nurse16Knj ?? ''

  // 「リハビリの視点」
  // 退院退所情報記録書情報.リハビリの視点_なし
  // 退院退所情報記録書情報.リハビリの視点_本人指導
  // 退院退所情報記録書情報.リハビリの視点_家族指導
  // 退院退所情報記録書情報.リハビリの視点_関節可動域練習（ｽﾄﾚｯﾁ含む）
  // 退院退所情報記録書情報.リハビリの視点_筋力増強練習
  // 退院退所情報記録書情報.リハビリの視点_バランス練習
  // 退院退所情報記録書情報.リハビリの視点_麻痺・筋緊張改善練習
  // 退院退所情報記録書情報.リハビリの視点_起居／立位等基本動作練習
  // 退院退所情報記録書情報.リハビリの視点_摂食・嚥下訓練
  // 退院退所情報記録書情報.リハビリの視点_言語訓練
  // 退院退所情報記録書情報.リハビリの視点_ADL練習（歩行／入浴／トイレ動作／移乗等）
  // 退院退所情報記録書情報.リハビリの視点_IADL練習（買い物、調理等）
  // 退院退所情報記録書情報.リハビリの視点_疼痛管理（痛みコントロール）
  // 退院退所情報記録書情報.リハビリの視点_更生装具・福祉用具等管理
  // 退院退所情報記録書情報.リハビリの視点_運動耐容能練習
  // 退院退所情報記録書情報.リハビリの視点_地域活動支援
  // 退院退所情報記録書情報.リハビリの視点_社会参加支援
  // 退院退所情報記録書情報.リハビリの視点_就労支援
  // 退院退所情報記録書情報. リハビリの視点_その他
  // 退院退所情報記録書情報. リハビリの視点_その他_備考
  setCheckBoxData(local.mo00018Twoway81, resData.data.cpnTucTaiKirokuData.rehabNone)
  setCheckBoxData(local.mo00018Twoway62, resData.data.cpnTucTaiKirokuData.rehab1)
  setCheckBoxData(local.mo00018Twoway63, resData.data.cpnTucTaiKirokuData.rehab2)
  setCheckBoxData(local.mo00018Twoway64, resData.data.cpnTucTaiKirokuData.rehab3)
  setCheckBoxData(local.mo00018Twoway65, resData.data.cpnTucTaiKirokuData.rehab4)
  setCheckBoxData(local.mo00018Twoway66, resData.data.cpnTucTaiKirokuData.rehab5)
  setCheckBoxData(local.mo00018Twoway67, resData.data.cpnTucTaiKirokuData.rehab6)
  setCheckBoxData(local.mo00018Twoway68, resData.data.cpnTucTaiKirokuData.rehab7)
  setCheckBoxData(local.mo00018Twoway69, resData.data.cpnTucTaiKirokuData.rehab8)
  setCheckBoxData(local.mo00018Twoway70, resData.data.cpnTucTaiKirokuData.rehab9)
  setCheckBoxData(local.mo00018Twoway71, resData.data.cpnTucTaiKirokuData.rehab10)
  setCheckBoxData(local.mo00018Twoway72, resData.data.cpnTucTaiKirokuData.rehab11)
  setCheckBoxData(local.mo00018Twoway73, resData.data.cpnTucTaiKirokuData.rehab12)
  setCheckBoxData(local.mo00018Twoway74, resData.data.cpnTucTaiKirokuData.rehab13)
  setCheckBoxData(local.mo00018Twoway75, resData.data.cpnTucTaiKirokuData.rehab14)
  setCheckBoxData(local.mo00018Twoway76, resData.data.cpnTucTaiKirokuData.rehab15)
  setCheckBoxData(local.mo00018Twoway77, resData.data.cpnTucTaiKirokuData.rehab16)
  setCheckBoxData(local.mo00018Twoway78, resData.data.cpnTucTaiKirokuData.rehab17)
  setCheckBoxData(local.mo00018Twoway79, resData.data.cpnTucTaiKirokuData.rehab18)

  local.mo00045_17.value = resData.data.cpnTucTaiKirokuData.rehab18Knj ?? ''

  // 「禁忌事項」
  // 退院退所情報記録書情報. 禁忌の有無
  // 退院退所情報記録書情報.禁忌事項_内容
  local.mo00039ContraindicationSelectType.value =
    resData.data.cpnTucTaiKirokuData.contraindication ?? ''
  local.mo00045_18.value = resData.data.cpnTucTaiKirokuData.contraindicationKnj ?? ''

  // 退院退所情報記録書情報.症状病状の予後予測
  // 退院退所情報記録書情報.退院に際しての日常生活の阻害要因
  // 退院退所情報記録書情報.在宅復帰のために整えなければならない要件
  local.mo00046Prognosis.value = resData.data.cpnTucTaiKirokuData.prognosisKnj ?? ''
  local.mo00046ObstructiveFactor.value = resData.data.cpnTucTaiKirokuData.obstructiveFactorKnj ?? ''
  local.mo00046ReturnHome.value = resData.data.cpnTucTaiKirokuData.returnHomeKnj ?? ''

  // 「情報提供を受けた職種」
  // 退院退所情報記録書情報.聞き取り日1
  // 退院退所情報記録書情報.聞き取り①_職種氏名
  // 退院退所情報記録書情報.聞き取り①_会議出席
  local.listenDate1.value = resData.data.cpnTucTaiKirokuData.listen1Ymd ?? ''
  local.mo00045_19.value = resData.data.cpnTucTaiKirokuData.listen1StaffKnj ?? ''
  local.mo00039MeetingAttendanceType1.value =
    resData.data.cpnTucTaiKirokuData.listen1Conference ?? ''

  // 退院退所情報記録書情報.聞き取り日2
  // 退院退所情報記録書情報.聞き取り②_職種氏名
  // 退院退所情報記録書情報.聞き取り②_会議出席
  local.listenDate2.value = resData.data.cpnTucTaiKirokuData.listen2Ymd ?? ''
  local.mo00045_20.value = resData.data.cpnTucTaiKirokuData.listen2StaffKnj ?? ''
  local.mo00039MeetingAttendanceType2.value =
    resData.data.cpnTucTaiKirokuData.listen2Conference ?? ''

  // 退院退所情報記録書情報.聞き取り日3
  // 退院退所情報記録書情報.聞き取り③_職種氏名
  // 退院退所情報記録書情報.聞き取り③_会議出席
  local.listenDate3.value = resData.data.cpnTucTaiKirokuData.listen3Ymd ?? ''
  local.mo00045_21.value = resData.data.cpnTucTaiKirokuData.listen3StaffKnj ?? ''
  local.mo00039MeetingAttendanceType3.value =
    resData.data.cpnTucTaiKirokuData.listen3Conference ?? ''
}

// チェックボックス対象設定
function setCheckBoxData(itemTarget: Mo00018Type, dataField: string | undefined) {
  if (dataField && dataField === '1') {
    itemTarget.modelValue = true
  } else {
    itemTarget.modelValue = false
  }
}

/**
 *  ボタン押下時の処理(Or28287)
 */
function onClickOr28287() {
  // Or28287のダイアログ開閉状態を更新する
  Or28287Logic.state.set({
    uniqueCpId: or28287.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログ表示フラグ
const showDialogOr28287 = computed(() => {
  // Or28287のダイアログ開閉状態
  return Or28287Logic.state.get(or28287.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 利用者一覧における利用者の変更を監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue === undefined) {
      return
    }
  },
  { deep: true }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or60075Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    // // 画面共通情報を取得
    // getCommonInfo()

    if (newValue.isRefresh) {
      // 画面情報再取得
      await reload()

      // // 次の描画までまつ（ターゲットエレメントのサイズが取得できるため）
      // await nextTick()
    }
    if (newValue.isCreateDateChanged) {
      // 保存ボタンが押下された場合、保存処理を実行する
      // 改訂バージョンをチェック
    }

    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      // void _save()
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      // createNew()
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
    }
    if (newValue.copyEventFlg) {
      // // 複写ボタンが押下された場合、複写処理を実行する
      // const copyData = local.commonInfo.copyData as IAssessmentFaceInfoSelectOutEntity
      // setFormData(copyData)
    }
  }
)
</script>

<template>
  <c-v-row
    no-gutters
    class="content"
  >
    <!-- コンテンツエリア -->
    <c-v-sheet class="content-area">
      <div>
        <!-- １.基本情報・現在の状態等 -->
        <c-v-row>
          <c-v-col class="header-col-base">
            <base-mo00615 :oneway-model-value="localOneway.mo00615BaseInfoOnewayType" />
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <!-- 属性-->
          <c-v-col class="header-col1">
            <base-mo00615 :oneway-model-value="localOneway.mo00615AttributeOnewayType" />
          </c-v-col>

          <!-- 退院(所)時の要介護度-->
          <c-v-col class="header-col2-btn">
            <base-mo00615
              :oneway-model-value="localOneway.mo00615LevelOfCareRequiredOnewayType"
              class="white-space"
            />
            <!--医療機関名選択アイコンボタン-->
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="handleByDialog('Or28287')"
            />
          </c-v-col>

          <!-- 退所する場合の帰来先の有無-->
          <c-v-col class="data-col1">
            <!-- 退所する場合の帰来先の有無ラベル-->
            <c-v-row class="pa-3 pl-2">
              <base-mo00018
                v-model="local.mo00018Twoway1"
                :oneway-model-value="localOneway.mo00018Oneway1"
              />
            </c-v-row>
            <!-- 退所する場合の帰来先の有無パネル-->
            <c-v-row class="data-row2">
              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
              >
                <base-mo00018
                  v-model="local.mo00018Twoway2"
                  :oneway-model-value="localOneway.mo00018Oneway2"
                />
                <base-mo00040 :oneway-model-value="localOneway.mo00040Oneway" />
              </c-v-col>
              <c-v-col class="pa-2 d-flex align-center">
                <base-mo00018
                  v-model="local.mo00018Twoway3"
                  :oneway-model-value="localOneway.mo00018Oneway3"
                />
                <base-mo00018
                  v-model="local.mo00018Twoway4"
                  :oneway-model-value="localOneway.mo00018Oneway4"
                />
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <!-- 入院（所）概要-->
          <c-v-col
            class="header-col1"
            style="min-height: 315px"
          >
            <base-mo00615
              :oneway-model-value="localOneway.mo00615HospitalizationAdmissionInfoOnewayType"
            />
          </c-v-col>

          <c-v-col style="min-width: 160px; max-width: 160px; padding: 0px; align-items: stretch">
            <c-v-row style="padding: 0px !important; height: 15%">
              <!-- (入院情報) -->
              <c-v-col class="header-col2-btn">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615HospitalizationOneway"
                  class="white-space"
                />
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="handleByDialog('Or51813')"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 15%">
              <!-- (入所情報) -->
              <c-v-col class="header-col2-btn">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615AdmissionInfoOneway"
                  class="white-space"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 30%">
              <!-- 入院原因疾患(入所目的等) -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615HospitalizationCauseOneway"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 20%">
              <!-- 入院・入所先 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615HospitalizationAdmissionOneway"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 20%">
              <!-- 今後の医学管理 -->
              <c-v-col class="header-col2-btn">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615NextMedicineManagementOneway"
                  class="white-space"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>
          </c-v-col>

          <!-- 退所する場合の帰来先の有無-->
          <c-v-col
            class="data-col1"
            style="border-top: 0px !important"
          >
            <!-- 退所する場合の帰来先の有無ラベル-->
            <c-v-row
              class="pa-3 pl-2"
              style="
                height: 30%;
                border-top: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */
              "
            >
              <c-v-col class="pa-2 d-flex align-center">
                <!-- 入院(所)日 -->
                <base-mo00020
                  v-model="local.hospitalizationDate"
                  :oneway-model-value="localOneway.hospitalizationDateOneway"
                />
                <!-- 退院(所)予定日 -->
                <base-mo00020
                  v-model="local.hospitalLeavingDate"
                  :oneway-model-value="localOneway.hospitalLeavingDateOneway"
                />
              </c-v-col>
            </c-v-row>
            <!-- 入院原因疾患(入所目的等) -->
            <c-v-row
              class="data-row2"
              style="height: 30%"
            >
              <c-v-col class="d-flex align-center">
                <base-mo00046
                  v-model="local.mo00046CauseDisease"
                  :oneway-model-value="localOneway.mo00046CauseDiseaseOneway"
                  style="width: 85%"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>
            <!-- 入院・入所先 -->
            <c-v-row
              class="data-row2"
              style="height: 20%"
            >
              <c-v-col class="pa-2 d-flex align-center">
                <base-mo00045
                  v-model="local.mo00045_1"
                  :oneway-model-value="localOneway.mo00045Oneway1"
                />
                <base-mo00045
                  v-model="local.mo00045_2"
                  :oneway-model-value="localOneway.mo00045Oneway2"
                />
                <base-mo00045
                  v-model="local.mo00045_3"
                  :oneway-model-value="localOneway.mo00045Oneway3"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row
              class="data-row2"
              style="height: 20%"
            >
              <!-- 医療機関名 -->
              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
                style="width: 65%"
              >
                <base-mo00045
                  v-model="local.mo00045_4"
                  :oneway-model-value="localOneway.mo00045Oneway4"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
              <!-- 方法 -->
              <c-v-col
                class="data-col-header"
                style="min-width: 100px; max-width: 100px"
              >
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615MethodOneway"
                  class="white-space"
                />
              </c-v-col>

              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
              >
                <base-mo00018
                  v-model="local.mo00018Twoway5"
                  :oneway-model-value="localOneway.mo00018Oneway5"
                />
                <base-mo00018
                  v-model="local.mo00018Twoway6"
                  :oneway-model-value="localOneway.mo00018Oneway6"
                />
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <!-- ①疾患と入院（所）中の状況-->
          <c-v-col
            class="header-col1"
            style="min-height: 630px"
          >
            <!-- ①疾患と入院（所）中の状況 -->
            <base-mo00615
              :oneway-model-value="localOneway.mo00615DiseaseHospitalizationSituationOnewayType"
            />
          </c-v-col>

          <c-v-col style="min-width: 160px; max-width: 160px; padding: 0px; align-items: stretch">
            <c-v-row style="padding: 0px !important; height: 27%">
              <!-- 現在治療中の疾患 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615CurrentTherapyDiseaseOneway"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 8%">
              <!-- 移動手段 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615CurrentTherapyDiseaseOneway2"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 8%">
              <!-- 排泄方法 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615CurrentTherapyDiseaseOneway3"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 8%">
              <!-- 入浴方法 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615CurrentTherapyDiseaseOneway4"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 9%">
              <!-- 食事形態 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615CurrentTherapyDiseaseOneway5"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 8%">
              <!-- 嚥下機能(むせ) -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615CurrentTherapyDiseaseOneway6"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 8%">
              <!-- 口腔清潔 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615CurrentTherapyDiseaseOneway7"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 8%">
              <!-- 口腔ケア -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615CurrentTherapyDiseaseOneway8"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 8%">
              <!-- 睡眠 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615CurrentTherapyDiseaseOneway9"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 8%">
              <!-- 認知・精神 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615CurrentTherapyDiseaseOneway10"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
          </c-v-col>

          <!-- ①疾患と入院（所）中の状況---データ部 -->
          <c-v-col
            class="data-col1"
            style="border-top: 0px !important"
          >
            <c-v-row
              class="data-row2"
              style="height: 9%"
            >
              <!-- 現在治療中の疾患⓵ -->
              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
                style="width: 100%"
              >
                <base-mo00045
                  v-model="local.mo00045_5"
                  :oneway-model-value="localOneway.mo00045Oneway5"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
                <base-mo00039
                  v-model="local.mo00039DiseaseStateSelectType1.value"
                  :oneway-model-value="localOneway.mo00039DiseaseStateTypeOneway"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row
              class="data-row2"
              style="height: 9%"
            >
              <!-- 現在治療中の疾患⓶ -->
              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
                style="width: 100%"
              >
                <base-mo00045
                  v-model="local.mo00045_6"
                  :oneway-model-value="localOneway.mo00045Oneway6"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
                <base-mo00039
                  v-model="local.mo00039DiseaseStateSelectType2.value"
                  :oneway-model-value="localOneway.mo00039DiseaseStateTypeOneway"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row
              class="data-row2"
              style="height: 9%"
            >
              <!-- 現在治療中の疾患⓷ -->
              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
                style="width: 100%"
              >
                <base-mo00045
                  v-model="local.mo00045_7"
                  :oneway-model-value="localOneway.mo00045Oneway7"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
                <base-mo00039
                  v-model="local.mo00039DiseaseStateSelectType3.value"
                  :oneway-model-value="localOneway.mo00039DiseaseStateTypeOneway"
                />
              </c-v-col>
            </c-v-row>

            <!-- 移動手段 -->
            <c-v-row
              class="data-row2"
              style="height: 8%"
            >
              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
                style="width: 100%"
              >
                <!-- 自立／杖／歩行器／車いす／その他 -->
                <base-mo00018
                  v-for="(prop, index) in mo00018MoveMethodInfo"
                  :key="index"
                  v-model="local[prop[1]]"
                  :oneway-model-value="localOneway[prop[0]]"
                />
                <base-mo00045
                  v-model="local.mo00045_8"
                  :oneway-model-value="localOneway.mo00045Oneway8"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>

            <!-- 排泄方法 -->
            <c-v-row
              class="data-row2"
              style="height: 8%"
            >
              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
                style="width: 100%"
              >
                <!-- トイレ／ポータブル／おむつ／カテーテル・パウチ -->
                <base-mo00018
                  v-for="(prop, index) in mo00018ExcretionMethodInfo"
                  :key="index"
                  v-model="local[prop[1]]"
                  :oneway-model-value="localOneway[prop[0]]"
                />

                <base-mo00045
                  v-model="local.mo00045_9"
                  :oneway-model-value="localOneway.mo00045Oneway9"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>

            <!-- 入浴方法 -->
            <c-v-row
              style="
                height: 8%;
                border-top: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */
              "
            >
              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
                style="width: 100%"
              >
                <!-- トイレ／ポータブル／おむつ／カテーテル・パウチ -->
                <base-mo00018
                  v-for="(prop, index) in mo00018BathingMethodInfo"
                  :key="index"
                  v-model="local[prop[1]]"
                  :oneway-model-value="localOneway[prop[0]]"
                />
              </c-v-col>
            </c-v-row>

            <!-- 食事形態 -->
            <c-v-row
              class="data-row2"
              style="height: 9%"
            >
              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
                style="width: 65%"
              >
                <!-- トイレ／ポータブル／おむつ／カテーテル・パウチ -->
                <base-mo00018
                  v-for="(prop, index) in mo00018MealFormInfo"
                  :key="index"
                  v-model="local[prop[1]]"
                  :oneway-model-value="localOneway[prop[0]]"
                />
                <base-mo00045
                  v-model="local.mo00045_10"
                  :oneway-model-value="localOneway.mo00045Oneway10"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>

              <!-- UDF等の食形態区分 -->
              <c-v-col
                class="data-col-header"
                style="width: 15%"
              >
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615UdfOneway"
                  class="white-space"
                />
              </c-v-col>

              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
                style="width: 20%"
              >
                <base-mo00045
                  v-model="local.mo00045_11"
                  :oneway-model-value="localOneway.mo00045Oneway11"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>

            <!-- 嚥下機能(むせ) -->
            <c-v-row
              class="data-row2"
              style="height: 16%"
            >
              <c-v-col style="padding: 0">
                <c-v-row style="border-bottom: 1px rgb(var(--v-theme-black-200)) solid">
                  <!-- 有無区分[1：なし 2：あり] -->
                  <c-v-col cols="auto">
                    <base-mo00039
                      v-model="local.mo00039CoughKbnSelectType.value"
                      :oneway-model-value="localOneway.mo00039PresenceOrAbsenceTypeOneway"
                      @click="presenceCheck('CoughKbn', local.mo00039CoughKbnSelectType.value)"
                    />
                  </c-v-col>
                  <!-- 嚥下機能状況区分[1：時々 2：常に] -->
                  <c-v-col cols="auto">
                    <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway1" />
                  </c-v-col>
                  <c-v-col cols="auto">
                    <base-mo00039
                      v-model="local.mo00039SwallowingFunctionSelectType.value"
                      :oneway-model-value="localOneway.mo00039SwallowingFunctionTypeOneway"
                      @click="
                        presenceCheck(
                          'SwallowingFunction',
                          local.mo00039SwallowingFunctionSelectType.value
                        )
                      "
                    />
                  </c-v-col>
                  <c-v-col cols="auto">
                    <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway2" />
                  </c-v-col>
                </c-v-row>
                <c-v-row>
                  <!-- 口腔清潔 -->
                  <c-v-col>
                    <base-mo00039
                      v-model="local.mo00039OralCavityCleanlinessSelectType.value"
                      :oneway-model-value="localOneway.mo00039OralCavityCleanlinessTypeOneway"
                    />
                  </c-v-col>
                </c-v-row>
              </c-v-col>
              <c-v-col
                cols="auto"
                class="data-col-header"
                style="min-width: 100px"
              >
                <!-- 義歯 -->
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615ArtificialToothOneway"
                  class="white-space"
                />
              </c-v-col>
              <c-v-col style="padding: 0">
                <c-v-row style="border-bottom: 1px rgb(var(--v-theme-black-200)) solid">
                  <!-- 義歯状況区分 -->
                  <c-v-col cols="auto">
                    <base-mo00039
                      v-model="local.mo00039DentureKbnSelectType.value"
                      :oneway-model-value="localOneway.mo00039PresenceOrAbsenceTypeOneway"
                      @click="presenceCheck('DentureKbn', local.mo00039DentureKbnSelectType.value)"
                    />
                  </c-v-col>
                  <!-- 義歯状況区分[1：部分 2：総] -->
                  <c-v-col cols="auto">
                    <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway1" />
                  </c-v-col>
                  <c-v-col cols="auto">
                    <base-mo00039
                      v-model="local.mo00039ArtificialToothSelectType.value"
                      :oneway-model-value="localOneway.mo00039ArtificialToothTypeOneway"
                      @click="
                        presenceCheck(
                          'ArtificialTooth',
                          local.mo00039ArtificialToothSelectType.value
                        )
                      "
                    />
                  </c-v-col>
                  <c-v-col cols="auto">
                    <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway2" />
                  </c-v-col>
                </c-v-row>
                <c-v-row>
                  <!-- 入院（所）中の使用 -->
                  <c-v-col cols="auto">
                    <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway3" />
                  </c-v-col>
                  <c-v-col cols="auto">
                    <base-mo00039
                      v-model="local.mo00039PresenceOrAbsenceTypeSelectType.value"
                      :oneway-model-value="localOneway.mo00039PresenceOrAbsenceTypeOneway"
                    /> </c-v-col
                ></c-v-row>
              </c-v-col>
            </c-v-row>

            <!-- 口腔ケア -->
            <c-v-row
              style="
                height: 8%;
                border-top: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */
              "
            >
              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
                style="width: 100%"
              >
                <!-- 1:自立 2:一部介助 3:全介助 -->
                <base-mo00039
                  v-model="local.mo00039OralCareSelectType.value"
                  :oneway-model-value="localOneway.mo00039OralCareTypeOneway"
                />
              </c-v-col>
            </c-v-row>

            <!-- 睡眠 -->
            <c-v-row
              style="
                height: 8%;
                border-top: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */
              "
            >
              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
                style="width: 50%"
              >
                <!-- 睡眠状況区分[1：良好 2：不良] -->
                <base-mo00039
                  v-model="local.mo00039SleepingSituationSelectType.value"
                  :oneway-model-value="localOneway.mo00039SleepingSituationTypeOneway"
                />
                <base-mo00045
                  v-model="local.mo00045_12"
                  :oneway-model-value="localOneway.mo00045Oneway10"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>

              <!-- 眠剤使用 -->
              <c-v-col
                class="data-col-header2"
                style="width: 50%"
              >
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615SleepingPillsUseOneway"
                  class="white-space"
                />
                <!-- [1：なし 2：あり] -->
                <base-mo00039
                  v-model="local.mo00039SleepDrugSelectType.value"
                  :oneway-model-value="localOneway.mo00039PresenceOrAbsenceTypeOneway"
                />
              </c-v-col>
            </c-v-row>

            <!-- 認知・精神 -->
            <c-v-row
              style="
                height: 8%;
                border-top: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */
              "
            >
              <c-v-col
                cols="auto"
                class="pa-2 d-flex align-center"
                style="width: 100%"
              >
                <!-- 認知機能低下／せん妄／徘徊／焦燥・不穏／攻撃性／その他 -->
                <base-mo00018
                  v-for="(prop, index) in mo00018CognitiveMentalInfo"
                  :key="index"
                  v-model="local[prop[1]]"
                  :oneway-model-value="localOneway[prop[0]]"
                />
                <base-mo00045
                  v-model="local.mo00045_13"
                  :oneway-model-value="localOneway.mo00045Oneway10"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>

        <!-- ②受け止め/意向-->
        <c-v-row>
          <c-v-col
            class="header-col1"
            style="min-height: 450px"
          >
            <base-mo00615 :oneway-model-value="localOneway.mo00615AcceptanceIntentionOnewayType" />
          </c-v-col>

          <c-v-col style="min-width: 160px; max-width: 160px; padding: 0px; align-items: stretch">
            <c-v-row style="padding: 0px !important; height: 40%">
              <!-- ＜本人＞病気、障害、後遺症等の受け止め方 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615AcceptanceIntentionOnewayType1"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 20%">
              <!-- ＜本人＞退院後の生活に関する意向 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615AcceptanceIntentionOnewayType2"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 20%">
              <!-- ＜家族＞病気、障害、後遺症等の受け止め方 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615AcceptanceIntentionOnewayType3"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 20%">
              <!-- ＜家族＞退院後の生活に関する意向 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615AcceptanceIntentionOnewayType4"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
          </c-v-col>

          <c-v-col
            class="data-col1"
            style="border-top: 0px !important"
          >
            <c-v-row
              style="
                height: 15%;
                border-top: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */
              "
            >
              <c-v-col class="pa-2 d-flex align-center">
                <!-- 本人への病名告知-->
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615AcceptanceIntentionOnewayType5"
                  class="white-space"
                />
                <base-mo00039
                  v-model="local.mo00039PersonAnnouncementSelectType.value"
                  :oneway-model-value="localOneway.mo00039PresenceOrAbsenceTypeOneway2"
                />
              </c-v-col>
            </c-v-row>
            <!-- ＜本人＞病気、障害、後遺症等の受け止め方-->
            <c-v-row style="height: 25%; border-top: 1px rgb(var(--v-theme-black-200)) solid">
              <c-v-col class="pa-2 d-flex align-center">
                <base-mo00046
                  v-model="local.mo00046PersonAnnouncement"
                  :oneway-model-value="localOneway.mo00046PersonAcceptOneway"
                  style="width: 85%"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>

            <!-- ＜本人＞退院後の生活に関する意向-->
            <c-v-row style="height: 20%; border-top: 1px rgb(var(--v-theme-black-200)) solid">
              <c-v-col class="pa-2 d-flex align-center">
                <base-mo00046
                  v-model="local.mo00046PersonIntention"
                  :oneway-model-value="localOneway.mo00046PersonIntentionOneway"
                  style="width: 85%"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>

            <!-- ＜家族＞病気、障害、後遺症等の受け止め方-->
            <c-v-row style="height: 20%; border-top: 1px rgb(var(--v-theme-black-200)) solid">
              <c-v-col class="pa-2 d-flex align-center">
                <base-mo00046
                  v-model="local.mo00046FamilyAccept"
                  :oneway-model-value="localOneway.mo00046FamilyAcceptOneway"
                  style="width: 85%"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>

            <!-- ＜家族＞退院後の生活に関する意向-->
            <c-v-row style="height: 20%; border-top: 1px rgb(var(--v-theme-black-200)) solid">
              <c-v-col class="pa-2 d-flex align-center">
                <base-mo00046
                  v-model="local.mo00046FamilyIntention"
                  :oneway-model-value="localOneway.mo00046FamilyIntentionOneway"
                  style="width: 85%"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>

        <!-- ２.課題認識のための情報 -->
        <c-v-row>
          <c-v-col class="header-col-base">
            <base-mo00615 :oneway-model-value="localOneway.mo00615IssuesCognitiveInfoOnewayType" />
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <!-- ③退院後に必要な事柄 -->
          <c-v-col
            class="header-col1"
            style="min-height: 1100px"
          >
            <base-mo00615
              :oneway-model-value="localOneway.mo00615DischargeFromHospitalMatterOnewayType"
            />
          </c-v-col>

          <c-v-col style="min-width: 160px; max-width: 160px; padding: 0px; align-items: stretch">
            <!-- 医療処置の内容 -->
            <c-v-row style="padding: 0px !important; height: 30%">
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615MedicalcareTreatmentContentsOnewayType"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 30%">
              <!-- 看護の視点 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615NursingPerspectiveOnewayType"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 30%">
              <!-- リハビリの視点 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615RehabilitationPerspectiveOnewayType"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="padding: 0px !important; height: 10%">
              <!-- 禁忌事項 -->
              <c-v-col class="header-col2">
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615ContraindicationsMatterOnewayType"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
          </c-v-col>

          <c-v-col
            class="data-col1"
            style="border-top: 0px !important"
          >
            <!-- 医療処置の内容 -->
            <div style="height: 30%">
              <c-v-row style="height: 52px; border-top: 1px rgb(var(--v-theme-black-200)) solid">
                <c-v-col class="pa-2 d-flex align-center">
                  <!-- 医療処置の内容「なし」 -->
                  <base-mo00018
                    v-model="local.mo00018Twoway30"
                    :oneway-model-value="localOneway.mo00018Oneway30"
                    @change="nothingCheck('treatmentNone', local.mo00018Twoway30.modelValue)"
                  />
                </c-v-col>
              </c-v-row>
              <c-v-row style="border-top: 0">
                <c-v-col
                  cols="auto"
                  class="pl-8"
                  style="display: flex; flex-wrap: wrap; flex-direction: row; max-width: 930px"
                >
                  <!-- 「医療処置の内容」チェックボックス情報 -->
                  <template
                    v-for="(prop, index) in mo00018MedicalcareTreatmentContentsInfo"
                    :key="index"
                  >
                    <c-v-col
                      cols="auto"
                      class="d-flex align-center"
                    >
                      <base-mo00018
                        v-model="local[prop[1]]"
                        :oneway-model-value="localOneway[prop[0]]"
                        @change="nothingCheck('treatment', local[prop[1]].modelValue)"
                      />
                      <template v-if="prop[0] == 'mo00018Oneway44' || prop[0] == 'mo00018Oneway45'">
                        <template v-if="prop[0] == 'mo00018Oneway44'">
                          <!-- 自己注射備考 -->
                          <base-mo00045
                            v-model="local.mo00045_14"
                            :oneway-model-value="localOneway.mo00045Oneway12"
                          />
                        </template>
                        <template v-else>
                          <!-- その他備考 -->
                          <base-mo00045
                            v-model="local.mo00045_15"
                            :oneway-model-value="localOneway.mo00045Oneway13"
                          />
                        </template>
                      </template>
                      <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
                    </c-v-col>
                  </template>
                </c-v-col>
              </c-v-row>
            </div>

            <!-- 看護の視点 -->
            <div style="height: 30%">
              <c-v-row
                style="
                  height: 52px;
                  border-top: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */
                "
              >
                <c-v-col class="pa-2 d-flex align-center">
                  <!-- 看護の視点「なし」 -->
                  <base-mo00018
                    v-model="local.mo00018Twoway80"
                    :oneway-model-value="localOneway.mo00018Oneway30"
                    @change="nothingCheck('nurseNone', local.mo00018Twoway80.modelValue)"
                  />
                </c-v-col>
              </c-v-row>
              <c-v-row style="border-top: 0">
                <c-v-col
                  cols="auto"
                  class="pl-8"
                  style="display: flex; flex-wrap: wrap; flex-direction: row; max-width: 930px"
                >
                  <!-- 「看護の視点」チェックボックス情報 -->
                  <template
                    v-for="(prop, index) in mo00018NursingPerspectiveInfo"
                    :key="index"
                  >
                    <c-v-col
                      cols="auto"
                      class="d-flex align-center"
                    >
                      <base-mo00018
                        v-model="local[prop[1]]"
                        :oneway-model-value="localOneway[prop[0]]"
                        @change="nothingCheck('nurse', local[prop[1]].modelValue)"
                      />
                      <template v-if="prop[0] == 'mo00018Oneway61'">
                        <!-- その他備考 -->
                        <base-mo00045
                          v-model="local.mo00045_16"
                          :oneway-model-value="localOneway.mo00045Oneway14"
                        />
                        <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
                      </template>
                    </c-v-col>
                  </template>
                </c-v-col>
              </c-v-row>
            </div>

            <!-- リハビリの視点 -->
            <div style="height: 30%">
              <c-v-row
                style="
                  height: 52px;
                  border-top: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */
                "
              >
                <c-v-col class="pa-2 d-flex align-center">
                  <!-- リハビリの視点「なし」 -->
                  <base-mo00018
                    v-model="local.mo00018Twoway81"
                    :oneway-model-value="localOneway.mo00018Oneway30"
                    @change="nothingCheck('rehabNone', local.mo00018Twoway81.modelValue)"
                  />
                </c-v-col>
              </c-v-row>
              <c-v-row style="border-top: 0">
                <c-v-col
                  cols="auto"
                  class="pl-8"
                  style="display: flex; flex-wrap: wrap; flex-direction: row; max-width: 930px"
                >
                  <!-- 「リハビリの視点」チェックボックス情報 -->
                  <template
                    v-for="(prop, index) in mo00018RehabilitationPerspectiveInfo"
                    :key="index"
                  >
                    <c-v-col
                      cols="auto"
                      class="d-flex align-center"
                    >
                      <base-mo00018
                        v-model="local[prop[1]]"
                        :oneway-model-value="localOneway[prop[0]]"
                        @change="nothingCheck('rehab', local[prop[1]].modelValue)"
                      />
                      <template v-if="prop[0] == 'mo00018Oneway79'">
                        <!-- その他備考 -->
                        <base-mo00045
                          v-model="local.mo00045_17"
                          :oneway-model-value="localOneway.mo00045Oneway15"
                        />
                        <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
                      </template>
                    </c-v-col>
                  </template>
                </c-v-col>
              </c-v-row>
            </div>

            <!-- 禁忌事項 -->
            <div style="height: 10%">
              <c-v-row style="height: 52px; border-top: 1px rgb(var(--v-theme-black-200)) solid">
                <!-- (禁忌の有無)---ラベル -->
                <c-v-col
                  style="
                    max-width: 200px;
                    border-top: 0;
                    display: flex;
                    background-color: rgb(var(--v-theme-background));
                  "
                >
                  <base-mo00615
                    :oneway-model-value="localOneway.mo00615ContraindicationsPreOrAbOnewayType"
                    class="white-space"
                  />
                </c-v-col>
                <!-- (禁忌の内容/留意点)---ラベル -->
                <c-v-col
                  style="
                    width: 70%;
                    border-top: 0;
                    display: flex;
                    border-left: 1px rgb(var(--v-theme-black-200)) solid;
                    background-color: rgb(var(--v-theme-background));
                  "
                >
                  <base-mo00615
                    :oneway-model-value="localOneway.mo00615ContraindicationsContentsOnewayType"
                    class="white-space"
                  />
                </c-v-col>
              </c-v-row>
              <c-v-row style="border-top: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */">
                <!-- [1：なし 2：あり] -->
                <c-v-col
                  class="pa-2 d-flex align-center"
                  style="max-width: 200px"
                >
                  <base-mo00039
                    v-model="local.mo00039ContraindicationSelectType.value"
                    :oneway-model-value="localOneway.mo00039PresenceOrAbsenceTypeOneway"
                  />
                </c-v-col>

                <!-- (禁忌の内容/留意点) -->
                <c-v-col
                  class="pa-2 d-flex align-left"
                  style="border-left: 1px rgb(var(--v-theme-black-200)) solid"
                >
                  <base-mo00045
                    v-model="local.mo00045_18"
                    :oneway-model-value="localOneway.mo00045Oneway15"
                  />
                  <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
                </c-v-col>
              </c-v-row>
            </div>
          </c-v-col>
        </c-v-row>

        <!-- 症状・病状の予後・予測 -->
        <c-v-row>
          <c-v-col class="header-col3">
            <base-mo00615
              :oneway-model-value="localOneway.mo00615SymptomsPredictionOnewayType"
              class="white-space"
            />
          </c-v-col>

          <c-v-col
            class="data-col1"
            style="border-top: 0px !important"
          >
            <c-v-row style="border-top: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */">
              <c-v-col class="pa-2 d-flex align-center">
                <base-mo00046
                  v-model="local.mo00046Prognosis"
                  :oneway-model-value="localOneway.mo00046PrognosisOneway"
                  style="width: 85%"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>

        <!-- 退院に際しての日常生活の阻害要因(心身状況・環境等) -->
        <c-v-row>
          <c-v-col class="header-col3">
            <base-mo00615
              :oneway-model-value="localOneway.mo00615InhibitionFactorOnewayType"
              class="white-space"
            />
          </c-v-col>

          <c-v-col
            class="data-col1"
            style="border-top: 0px !important"
          >
            <c-v-row style="border-top: 1px rgb(var(--v-theme-black-200)) solid">
              <!-- 例ラベル -->
              <c-v-col
                style="
                  border-top: 0;
                  display: flex;
                  background-color: rgb(var(--v-theme-background));
                "
              >
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615InhibitionFactorSampleOnewayType"
                  class="white-space"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row style="border-top: 1px rgb(var(--v-theme-black-200)) solid">
              <c-v-col class="pa-2 d-flex align-center">
                <base-mo00046
                  v-model="local.mo00046ObstructiveFactor"
                  :oneway-model-value="localOneway.mo00046ObstructiveFactorOneway"
                  style="width: 85%"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>

        <!-- 在宅復帰のために整えなければならない要件 -->
        <c-v-row>
          <c-v-col class="header-col3">
            <base-mo00615
              :oneway-model-value="localOneway.mo00615ArrangedRequirementOnewayType"
              class="white-space"
            />
          </c-v-col>

          <c-v-col
            class="data-col1"
            style="border-top: 0px !important"
          >
            <c-v-row style="border-top: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */">
              <c-v-col class="pa-2 d-flex align-center">
                <base-mo00046
                  v-model="local.mo00046ReturnHome"
                  :oneway-model-value="localOneway.mo00046ReturnHomeOneway"
                  style="width: 85%"
                />
                <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>

        <!-- 回目 -->
        <c-v-row>
          <c-v-col class="header-col4">
            <base-mo00615 :oneway-model-value="localOneway.mo00615TimeOnewayType" />
          </c-v-col>

          <!-- 聞き取り日 -->
          <c-v-col
            class="header-col2"
            style="border-left: 1px rgb(var(--v-theme-black-200)) solid"
          >
            <base-mo00615 :oneway-model-value="localOneway.mo00615InterviewDateOnewayType" />
          </c-v-col>

          <!-- 情報提供を受けた職種(氏名) -->
          <c-v-col
            class="data-col-header3"
            cols="auto"
            style="width: 70%"
          >
            <base-mo00615 :oneway-model-value="localOneway.mo00615ReceivedOccupationOnewayType" />
          </c-v-col>

          <!-- 会議出席 -->
          <c-v-col
            class="data-col-header3"
            style="border-right: 1px rgb(var(--v-theme-black-200)) solid"
          >
            <base-mo00615 :oneway-model-value="localOneway.mo00615MeetingAttendanceOnewayType" />
          </c-v-col>
        </c-v-row>

        <!-- （１回目）データ -->
        <c-v-row>
          <c-v-col class="data-time-col1 pa-2 d-flex align-right">
            <base-mo00615 :oneway-model-value="localOneway.mo00615Time1OnewayType" />
          </c-v-col>

          <c-v-col class="data-time-col2 pa-2 d-flex align-center">
            <!-- 聞き取り日 -->
            <base-mo00020
              v-model="local.listenDate1"
              :oneway-model-value="localOneway.ListenDate1Oneway"
            />
          </c-v-col>

          <!-- 情報提供を受けた職種(氏名) -->
          <c-v-col
            class="data-col1 pa-2 d-flex align-center"
            cols="auto"
            style="width: 70%; border-right: 0"
          >
            <base-mo00045
              v-model="local.mo00045_19"
              :oneway-model-value="localOneway.mo00045Oneway16"
            />
            <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
          </c-v-col>

          <!-- 会議出席 -->
          <c-v-col class="data-col1 pa-2 d-flex align-center">
            <base-mo00039
              v-model="local.mo00039MeetingAttendanceType1.value"
              :oneway-model-value="localOneway.mo00039MeetingAttendanceTypeOneway"
            />
          </c-v-col>
        </c-v-row>

        <!-- （２回目）データ -->
        <c-v-row>
          <c-v-col class="data-time-col1 pa-2 d-flex align-right">
            <base-mo00615 :oneway-model-value="localOneway.mo00615Time2OnewayType" />
          </c-v-col>

          <c-v-col class="data-time-col2 pa-2 d-flex align-center">
            <!-- 聞き取り日 -->
            <base-mo00020
              v-model="local.listenDate2"
              :oneway-model-value="localOneway.ListenDate2Oneway"
            />
          </c-v-col>

          <!-- 情報提供を受けた職種(氏名) -->
          <c-v-col
            class="data-col1 pa-2 d-flex align-center"
            cols="auto"
            style="width: 70%; border-right: 0"
          >
            <base-mo00045
              v-model="local.mo00045_20"
              :oneway-model-value="localOneway.mo00045Oneway16"
            />
            <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
          </c-v-col>

          <!-- 会議出席 -->
          <c-v-col class="data-col1 pa-2 d-flex align-center">
            <base-mo00039
              v-model="local.mo00039MeetingAttendanceType2.value"
              :oneway-model-value="localOneway.mo00039MeetingAttendanceTypeOneway"
            />
          </c-v-col>
        </c-v-row>

        <!-- （３回目）データ -->
        <c-v-row>
          <c-v-col
            class="data-time-col1 pa-2 d-flex align-right"
            style="border-bottom: 1px rgb(var(--v-theme-black-200)) solid"
          >
            <base-mo00615 :oneway-model-value="localOneway.mo00615Time3OnewayType" />
          </c-v-col>

          <c-v-col
            class="data-time-col2 pa-2 d-flex align-center"
            style="border-bottom: 1px rgb(var(--v-theme-black-200)) solid"
          >
            <!-- 聞き取り日 -->
            <base-mo00020
              v-model="local.listenDate3"
              :oneway-model-value="localOneway.ListenDate3Oneway"
            />
          </c-v-col>

          <!-- 情報提供を受けた職種(氏名) -->
          <c-v-col
            class="data-col1 pa-2 d-flex align-center"
            cols="auto"
            style="
              width: 70%;
              border-right: 0;
              border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
            "
          >
            <base-mo00045
              v-model="local.mo00045_21"
              :oneway-model-value="localOneway.mo00045Oneway16"
            />
            <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
          </c-v-col>

          <!-- 会議出席 -->
          <c-v-col
            class="data-col1 pa-2 d-flex align-center"
            style="border-bottom: 1px rgb(var(--v-theme-black-200)) solid"
          >
            <base-mo00039
              v-model="local.mo00039MeetingAttendanceType3.value"
              :oneway-model-value="localOneway.mo00039MeetingAttendanceTypeOneway"
            />
          </c-v-col>
        </c-v-row>
      </div>
    </c-v-sheet>
  </c-v-row>

  <!-- ［GUI00649_認定情報選択］モーダル -->
  <g-custom-or-28287
    v-if="showDialog('Or28287')"
    v-bind="or28287"
    v-model="local.or28287"
    :oneway-model-value="localOneway.or28287Oneway"
  />

  <!-- (入院情報)アイコンボタン -->
  <!-- ［GUI01307_受診状況情報］モーダル -->
  <g-custom-or-51813
    v-if="showDialog('Or51813')"
    v-bind="or51813"
    :oneway-model-value="localOneway.or51813OneWayType"
    @user-management-confirm="getUserManagementInfo"
    @attending-physician-confirm="getAttendingPhysicianInfo"
  />

  <!-- 退院(所)時の要介護度 -->
  <!-- <g-custom-or-26273
    v-if="showDialogOr26273"
    v-bind="or26273"
    v-model="local.or26273"
    :oneway-model-value="localOneway.or26273OnewayModel"
  /> -->
</template>

<style scoped lang="scss">
:deep(.v-input__control) {
  background-color: rgb(var(--v-theme-surface));
}
.content-area {
  width: 100%;
  margin-top: 16px;
}

.content {
  display: flex;
  height: 100%;
  width: 100%;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}

.bordered {
  border: 1px rgb(var(--v-theme-black-200)) solid;
}

.bordered-yellow {
  border: 2px rgb(245, 204, 142) solid;
}

.header-col-base {
  border: 1px rgb(var(--v-theme-black-200)) solid;
  padding: 8px;
  border-bottom: 0;
  background-color: #fff;
}

.v-row {
  margin: 0px;
}

.v-col {
  padding: 8px;
}

.white-space {
  white-space: pre-wrap !important;
}

// 「属性」列のスタイル
.header-col1 {
  min-width: 50px;
  max-width: 50px;
  border: 1px rgb(var(--v-theme-black-200)) solid;
  // border-right: 0;
  border-bottom: 0;
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  align-items: center;
  white-space: pre;
}

:deep(.header-title1) {
  writing-mode: vertical-rl;
  text-orientation: upright;
  font-size: 14px;
  text-align: center;
  letter-spacing: 8px;
}

// ２列目のスタイル 「退院(所)時の要介護度」※ ボタン付け
.header-col2-btn {
  min-width: 160px;
  max-width: 160px;
  border: 1px rgb(var(--v-theme-black-200)) solid;
  padding: 8px;
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

// ２列目のスタイル 「入院原因疾患(入所目的等)」等
.header-col2 {
  min-width: 160px;
  max-width: 160px;
  border: 1px rgb(var(--v-theme-black-200)) solid;
  padding: 8px;
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
  display: flex;
}

// 「症状・病状の予後・予測」等のスタイル
.header-col3 {
  min-width: 210px;
  max-width: 210px;
  border: 1px rgb(var(--v-theme-black-200)) solid;
  padding: 8px;
  border-right: 0;
  border-bottom: 0;
  display: flex;
}

// 「回目」のスタイル
.header-col4 {
  min-width: 50px;
  max-width: 50px;
  border: 1px rgb(var(--v-theme-black-200)) solid;
  padding: 8px;
  border-right: 0;
  border-bottom: 0;
  display: flex;
}

// ３列目のデータスタイル
.data-col1 {
  border: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */
  border-bottom: 0;
  padding: 0; /* pa-0 */
  background-color: #fff; /* bg-white */
  align-items: stretch;
}

.data-row2 {
  border-top: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */
  border-bottom: 0px; /* border-b-0 */
  border-left: 0; /* border-s-0 (LTR環境で左側) */
  border-right: 0; /* border-e-0 (LTR環境で右側) */
  background-color: #fff; /* bg-white */
}

// ３列目のスタイル
.data-col-header {
  border-top: 0;
  display: flex;
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
  background-color: rgb(var(--v-theme-background));
}

// ３列目のスタイル
.data-col-header2 {
  border-top: 0;
  display: flex;
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

// ３列目のスタイル
.data-col-header3 {
  display: flex;
  border-top: 1px rgb(var(--v-theme-black-200)) solid;
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

// 「回目」データ-列１スタイル
.data-time-col1 {
  min-width: 50px;
  max-width: 50px;
  border: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */
  border-right: 0;
  border-bottom: 0;
  padding: 0; /* pa-0 */
  background-color: #fff; /* bg-white */
  align-items: stretch;
}
// 「回目」データ-列２スタイル
.data-time-col2 {
  min-width: 160px;
  max-width: 160px;
  border: 1px rgb(var(--v-theme-black-200)) solid; /* bordered */
  border-right: 0;
  border-bottom: 0;
  padding: 0; /* pa-0 */
  background-color: #fff; /* bg-white */
  align-items: stretch;
}
</style>
