import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or29976Const:有機体:印刷設定モーダル
 * GUI00986_［印刷設定］画面
 * 静的データ
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> 張凱旋
 */
export namespace Or29976Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or29976', seq)

  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false

    /**
     * 単一
     */
    export const TANI = '0'
    /**
     * 複数
     */
    export const HUKUSUU = '1'
    /**
     * 機能名
     */
    export const KINOU_NAMEKNJ = 'PRT'
    /**
     * 0
     */
    export const ZERO = '0'
    /**
     * 1
     */
    export const ONE = '1'
    /**
     * empty
     */
    export const EMPTY = ''
  }
  /**
   * 帳票ID: PDFダウンロード
   */
  export namespace PDF_DOWNLOAD_REPORT_ID {
    /**
     * （Ⅳ）週間表(H21改訂版)
     */
    export const GENER_ALL = 'ShuuKanReport'
  }
    /**
     * プリントナンバー 1
     */
  export const PRINT_NO_1 = '1'
}
