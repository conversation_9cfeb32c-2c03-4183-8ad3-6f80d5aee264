<script setup lang="ts">
/**
 * Or28571:［履歴選択］画面 課題立案
 * GUI00907_［履歴選択］画面 課題立案
 *
 * @description
 * 履歴選択画面課題立案
 *
 * <AUTHOR>
 */

import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28571Const } from './Or28571.constants'
import type { HistorySelectTableDataItem, Or28571StateType } from './Or28571.type'
import { useScreenOneWayBind } from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  HistorySelectIssuesPlanningInitSelectInEntity,
  HistorySelectIssuesPlanningInitSelectOutEntity,
} from '~/repositories/cmn/entities/HistorySelectIssuesPlanningInitSelectEntity'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01334Headers, Mo01334OnewayType } from '~/types/business/components/Mo01334Type'
import type {
  HistorySelectInfoType,
  Or28571OnewayType,
} from '~/types/cmn/business/components/Or28571Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: HistorySelectTableDataItem
  onewayModelValue: HistorySelectInfoType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const defaultOnewayModelValue: Or28571OnewayType = {
  historySelectInfo: {
    // 事業者ID
    svJigyoId: '0',
    // 利用者ID
    userId: '0',
    // 計画期間ID
    sc1Id: '1',
    // 履歴ID
    assId: '0',
  },
}

const localOneway = reactive({
  or28571: {
    ...defaultOnewayModelValue.historySelectInfo,
    ...props.onewayModelValue,
  },
  // 「履歴選択」ダイアログ
  mo00024Oneway: {
    width: '586px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.history-select'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  mo01334HistoryselectOnewayModelValue: {
    headers: [
      // 作成日
      {
        // ヘッダーに表示される名称
        title: t('label.create-date'),
        key: 'createYmd',
        sortable: false,
        width: '140px',
      },
      // 作成者
      {
        // ヘッダーに表示される名称
        title: t('label.author'),
        key: 'memoContent',
        sortable: false,
        width: '190px',
      },
      // 様式
      {
        // ヘッダーに表示される名称
        title: t('label.style'),
        key: 'titleKnj',
        sortable: false,
        width: '225px',
      },
    ] as unknown as Mo01334Headers[],
    height: '326px',
    items: [],
  } as Mo01334OnewayType,
})

// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
}

// 確定ボタン設置
const mo00609Oneway: Mo00609OnewayType = {
  btnLabel: t('btn.confirm'),
  width: '90px',
  tooltipText: t('tooltip.confirm-btn'),
}

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28571Const.DEFAULT.IS_OPEN,
})

// 履歴選択情報選択行データ設定
const historySelectedItem = ref<HistorySelectTableDataItem>({
  createYmd: '',
  memoContent: '',
  titleKnj: '',
  assId: '',
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or28571StateType>({
  cpId: Or28571Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or28571Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: HistorySelectIssuesPlanningInitSelectInEntity = {
    // 計画期間ID
    sc1Id: localOneway.or28571.sc1Id,
    // 事業者ID
    svJigyoId: localOneway.or28571.svJigyoId,
    // 利用者ID
    userId: localOneway.or28571.userId,
  }
  const resData: HistorySelectIssuesPlanningInitSelectOutEntity = await ScreenRepository.select(
    'historySelectIssuesPlanningInitSelect',
    inputData
  )
  const dataList = resData.data.rirekiInfoList.map((item) => {
    return {
      ...item,
      id: item.assId,
    }
  })
  // データ情報設定
  localOneway.mo01334HistoryselectOnewayModelValue.items = dataList

  // 親画面.履歴IDが存在する場合
  historySelectedItem.value =
    dataList.find((item) => item.assId === localOneway.or28571.assId) ?? dataList[0]
}
/**
 * 履歴情報選択行
 *
 * @param item - 履歴情報選択行
 */
const clickHistorySelectRow = (item: HistorySelectTableDataItem) => {
  historySelectedItem.value = item
}

/**
 * 履歴情報の行をダブルクリックで選択
 *
 * @param item - 履歴情報選択行
 */
const doubleClickHistorySelectRow = (item: HistorySelectTableDataItem) => {
  historySelectedItem.value = item
  onConfirmBtn()
}

/**
 * 履歴情報選択行設定様式
 *
 * @param item - 履歴情報選択行
 */
const historyIsSelected = (item: { assId: null }) => historySelectedItem.value?.assId === item.assId

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = () => {
  // 履歴データがない場合、操作なし
  if (!historySelectedItem.value) return
  // 選択情報値戻り
  emit('update:modelValue', historySelectedItem.value)
  onClickCloseBtn()
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            class="table-header"
          >
            <!-- 履歴選択一覧 -->
            <base-mo01334
              class="list-wrapper"
              hide-default-footer
              :oneway-model-value="localOneway.mo01334HistoryselectOnewayModelValue"
            >
              <template #item="{ item }">
                <tr
                  :class="{ 'selected-row': historyIsSelected(item) }"
                  @click="clickHistorySelectRow(item)"
                  @dblclick="doubleClickHistorySelectRow(item)"
                >
                  <td>
                    <!-- 作成日 -->
                    <span>{{ item.createYmd }}</span>
                  </td>
                  <td>
                    <!-- 作成者 -->
                    <span>{{ item.memoContent }}</span>
                  </td>
                  <td>
                    <div class="tooltip-div">
                      <!-- 様式 -->
                      <span class="tooltip-cell">{{ item.titleKnj }}</span>
                      <c-v-tooltip
                        v-if="item.titleKnj"
                        activator="parent"
                        location="bottom"
                        :max-width="225"
                        :text="item.titleKnj"
                        open-delay="200"
                      />
                    </div>
                  </td>
                </tr>
              </template>
            </base-mo01334>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="mo00611Oneway"
          class="mr-2"
          @click="onClickCloseBtn"
        >
        </base-mo00611>
        <!-- 確定ボタン-->
        <base-mo00609
          :oneway-model-value="mo00609Oneway"
          @click="onConfirmBtn"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
.tooltip-cell {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tooltip-div {
  width: 225px;
  overflow: hidden;
}
</style>
