<script setup lang="ts">
/**
 * Or28409:(共通)年金手帳選択モーダル
 * GUI00644_［年金手帳選択］画面
 *
 * @description
 * (共通)年金手帳選択モーダル
 *
 * <AUTHOR> DAO DINH DUONG
 */
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28409Const } from './Or28409.constants'
import type { Or28409StateType, ToriNen<PERSON> } from './Or28409.type'
import { Or27301Const } from '~/components/custom-components/organisms/Or27301/Or27301.constants'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type {
  ToriNenkinInfoInputInEntity,
  ToriNenkinOutEntity,
} from '~/repositories/cmn/entities/ToriNenkinSelectEntity'
import type { Mo00018OnewayType, Mo00018Type } from '@/types/business/components/Mo00018Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'

/**
 * 国際化関数の取得
 */
const { t } = useI18n()
interface Props {
  uniqueCpId: string
}
/**
 * 親コンポーネントから渡されたプロパティを定義
 */
const props = defineProps<Props>()
/**
 * イベントのemit定義（ダイアログクローズ用）
 */
const emit = defineEmits(['confirm'])
/**
 * ユニークなCP IDを保持するリアクティブなオブジェクト。
 */
const or27301 = ref({ uniqueCpId: '' })
useSetupChildProps(props.uniqueCpId, {
  [Or27301Const.CP_ID(0)]: or27301.value,
})
/**
 * Mo00024Type 型のリアクティブオブジェクト。
 * コンポーネントやUIの開閉状態を管理します。
 *
 * 初期値は Or28409Const.DEFAULT.IS_OPEN に設定されています。
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28409Const.DEFAULT.IS_OPEN,
})
/**
 * 調査票一覧情報取得(IN)
 */
const paramInit = ref<ToriNenkinInfoInputInEntity>({
  userId: '',
})
/**
 * OneWayバインドの状態管理
 */
const { setState } = useScreenOneWayBind<Or28409StateType>({
  cpId: Or28409Const.CP_ID(0), // CP IDを取得
  uniqueCpId: props.uniqueCpId, // ユニークなCP ID
  onUpdate: {
    /**
     * isOpen の状態を更新します。
     *
     * @param value - 更新する開閉状態。未定義の場合はデフォルト値を設定します。
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or28409Const.DEFAULT.IS_OPEN // isOpenの状態を更新
    },
    /**
     * param の状態を更新します。
     *
     * @param value - 更新する開閉状態。未定義の場合はデフォルト値を設定します。
     */
    param: (value) => {
      paramInit.value = value!
    },
  },
})
/**
 * ローカルの片方向データを保持するリアクティブオブジェクト。
 */
const localOneway = reactive({
  // 認定情報入力ダイアログの設定
  mo00024Oneway: {
    width: '780px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or28409',
      toolbarTitle: t('label.pension-book-selection'),
      toolbarName: 'Or28409ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00018Oneway: {
    name: 'calculatedAmountByMonths',
    checkboxLabel: t('label.amount-times-relevant-months'),
    showItemLabel: false,
  } as Mo00018OnewayType,
  dataButtonOneway: {
    btnIcon: 'database',
    minWidth: '30px',
    minHeight: '30px',
    tooltipText: t('tooltip.others-function'),
  } as Mo00009OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
})
/**
 * ローカルの片方向データを保持するリアクティブオブジェクト。
 */
const local = reactive({
  // タブ
  calculatedAmountByMonths: {
    modelValue: true,
  } as Mo00018Type,
})
/**
 * モーダルやUIの開閉状態を「閉じる（isOpenをfalseに設定）」に更新します。
 */
const close = () => {
  setState({ isOpen: false })
}

/**
 * 各オブジェクトはヘッダーの設定（キー・ラベルなど）を保持します。
 */
const headers = [
  {
    title: t('label.issue-date-1'),
    key: 'getYmd',
    width: '120px',
    sortable: false,
  },
  {
    title: t('label.pension-type'),
    key: 'tKindKnj',
    width: '120px',
    sortable: false,
  },
  {
    title: t('label.book-number'),
    key: 'tBango',
    width: '110px',
    sortable: false,
  },
  {
    title: t('label.amount-1'),
    key: 'kingaku',
    width: '120px',
    sortable: false,
  },
  {
    title: t('label.month-key', { key: '1' }),
    key: 'tuki1Flg',
    width: '40px',
    align: 'center',
    sortable: false,
  },
  {
    title: t('label.month-key', { key: '2' }),
    key: 'tuki2Flg',
    width: '40px',
    align: 'center',
    sortable: false,
  },
  {
    title: t('label.month-key', { key: '3' }),
    key: 'tuki3Flg',
    width: '40px',
    align: 'center',
    sortable: false,
  },
  {
    title: t('label.month-key', { key: '4' }),
    key: 'tuki4Flg',
    width: '40px',
    align: 'center',
    sortable: false,
  },
  {
    title: t('label.month-key', { key: '5' }),
    key: 'tuki5Flg',
    width: '40px',
    align: 'center',
    sortable: false,
  },
  {
    title: t('label.month-key', { key: '6' }),
    key: 'tuki6Flg',
    width: '40px',
    align: 'center',
    sortable: false,
  },
  {
    title: t('label.month-key', { key: '7' }),
    key: 'tuki7Flg',
    width: '40px',
    align: 'center',
    sortable: false,
  },
  {
    title: t('label.month-key', { key: '8' }),
    key: 'tuki8Flg',
    width: '40px',
    align: 'center',
    sortable: false,
  },
  {
    title: t('label.month-key', { key: '9' }),
    key: 'tuki9Flg',
    align: 'center',
    width: '40px',
    sortable: false,
  },
  {
    title: t('label.month-key', { key: '10' }),
    key: 'tuki10Flg',
    width: '40px',
    align: 'center',
    sortable: false,
  },
  {
    title: t('label.month-key', { key: '11' }),
    key: 'tuki11Flg',
    width: '40px',
    align: 'center',
    sortable: false,
  },
  {
    title: t('label.month-key', { key: '12' }),
    key: 'tuki12Flg',
    width: '40px',
    align: 'center',
    sortable: false,
  },
]
/**
 * 選択されているアイテムの識別子（string型）を保持するリアクティブ変数。
 */
const selectedItem = ref<string | undefined>()
/**
 * 指定されたアイテムを選択状態に設定します。
 *
 * @param item - 選択対象の調査票アイテム（tBango プロパティを持つ）
 */
function selectRow(item: ToriNenkin) {
  selectedItem.value = item.tBango
}
/**
 * アイテムが選択されているかどうかを確認します。
 *
 * @param item - 確認したいアイテム（tBango プロパティを持つオブジェクト）
 *
 * @returns 選択されていれば true、そうでなければ false
 */
const isSelected = (item: ToriNenkin) => selectedItem.value === item.tBango
/**
 * 年金情報の一覧データを格納するリアクティブ変数。
 */
const tableData = ref<ToriNenkin[]>([])

/**
 * 初期処理を実行します。
 *
 * 調査票一覧情報を取得し、画面に表示するデータを設定します。
 * また、取得した一覧のうち最初のアイテムを初期選択状態として設定します。
 *
 * @returns 非同期処理のため、Promise を返します
 */
async function init() {
  const res: ToriNenkinOutEntity = await ScreenRepository.select(
    'pensionBookSelectionSelect',
    paramInit.value
  )
  tableData.value = res.data.nenkinTList
  selectedItem.value = tableData.value[0].tBango
}

onMounted(async () => {
  await nextTick()
  await init()
})
/**
 * 選択された行の情報を基に金額を計算し、confirm イベントを発火します。
 * また、処理完了後に UI の開閉状態を閉じる（isOpen = false）に設定します。
 *
 * - 選択行から日付や金額などの情報を抽出し、
 * - 月ごとの給与支給があるかをカウントして合計月数を計算、
 * - `local.calculatedAmountByMonths.modelValue` の状態に応じて金額を調整します。
 *
 * @fires confirm - 計算済みの金額情報を親コンポーネントに送信します。
 */
const confirm = () => {
  const { getYmd, kingaku, tBango, tKindKnj, ...selectedRow } = tableData.value.find(
    (item) => item.tBango === selectedItem.value
  ) as ToriNenkin
  const totalMonthHasSalary = Object.values(selectedRow).filter(
    (value) => value === Or28409Const.DEFAULT.HAS_SALARY
  ).length
  if (local.calculatedAmountByMonths.modelValue) {
    emit('confirm', { tKindKnj, kingaku: Number(kingaku) * totalMonthHasSalary })
  } else {
    emit('confirm', { tKindKnj, kingaku })
  }
  setState({ isOpen: false })
}
/**
 * mo00024のisOpen監視
 * 組織ダイアログの自動クローズを手動判定に変更
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    mo00024.value.isOpen = true
    if (!newValue) {
      void close()
    }
  }
)
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet class="container">
        <c-v-row
          no-guitter
          align="center"
          style="justify-content: space-between"
        >
          <base-mo00018
            v-model="local.calculatedAmountByMonths"
            :oneway-model-value="localOneway.mo00018Oneway"
          />
          <base-mo00009
            class="mr-5"
            :oneway-model-value="localOneway.dataButtonOneway"
          />
        </c-v-row>
        <c-v-row class="mt-2">
          <c-v-data-table
            fixed-header
            :headers="headers"
            height="277px"
            class="table-wrapper overflow-y-auto elevation-1 table-torin"
            :items-per-page="9999"
            :items="tableData"
            hide-default-footer
            hover
          >
            <template #item="{ item }">
              <tr
                :class="{ 'highlight-row': isSelected(item) }"
                @click="selectRow(item)"
                @dblclick="confirm"
              >
                <td>
                  {{ item.getYmd }}
                </td>
                <td>
                  {{ item.tkindKnj }}
                </td>
                <td>
                  {{ item.tBango }}
                </td>
                <td>
                  <base-mo01336
                    :oneway-model-value="{ value: Number(item.kingaku).toLocaleString('ja-JP') }"
                    style="background-color: transparent"
                  />
                </td>
                <td>
                  {{ item.tuki1Flg === Or28409Const.DEFAULT.HAS_SALARY ? t('label.maru') : '' }}
                </td>
                <td>
                  {{ item.tuki2Flg === Or28409Const.DEFAULT.HAS_SALARY ? t('label.maru') : '' }}
                </td>
                <td>
                  {{ item.tuki3Flg === Or28409Const.DEFAULT.HAS_SALARY ? t('label.maru') : '' }}
                </td>
                <td>
                  {{ item.tuki4Flg === Or28409Const.DEFAULT.HAS_SALARY ? t('label.maru') : '' }}
                </td>
                <td>
                  {{ item.tuki5Flg === Or28409Const.DEFAULT.HAS_SALARY ? t('label.maru') : '' }}
                </td>
                <td>
                  {{ item.tuki6Flg === Or28409Const.DEFAULT.HAS_SALARY ? t('label.maru') : '' }}
                </td>
                <td>
                  {{ item.tuki7Flg === Or28409Const.DEFAULT.HAS_SALARY ? t('label.maru') : '' }}
                </td>
                <td>
                  {{ item.tuki8Flg === Or28409Const.DEFAULT.HAS_SALARY ? t('label.maru') : '' }}
                </td>
                <td>
                  {{ item.tuki9Flg === Or28409Const.DEFAULT.HAS_SALARY ? t('label.maru') : '' }}
                </td>
                <td>
                  {{ item.tuki10Flg === Or28409Const.DEFAULT.HAS_SALARY ? t('label.maru') : '' }}
                </td>
                <td>
                  {{ item.tuki11Flg === Or28409Const.DEFAULT.HAS_SALARY ? t('label.maru') : '' }}
                </td>
                <td>
                  {{ item.tuki12Flg === Or28409Const.DEFAULT.HAS_SALARY ? t('label.maru') : '' }}
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-row>
      </c-v-sheet>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        />
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609ConfirmOneway"
          class="mx-2"
          @click="confirm"
        />
      </c-v-row>
    </template>
  </base-mo00024>
</template>
<style lang="scss" scoped>
@use '@/styles/cmn/dialog-data-table-list.scss';
.container {
  padding: 8px;
  height: 340px;
  background-color: transparent;
  overflow: hidden;
}
:deep(.table-torin) {
  table {
    table-layout: fixed;
  }
  max-height: 420px;
  overflow: auto;
  td:nth-child(4) {
    padding: 0 !important;
  }
  td:nth-last-child(-n + 12) {
    text-align: center;
  }
  th:nth-last-child(-n + 12) {
    padding: 0;
  }
}
.table-wrapper :deep(td) {
  height: 32px !important;
}
</style>
