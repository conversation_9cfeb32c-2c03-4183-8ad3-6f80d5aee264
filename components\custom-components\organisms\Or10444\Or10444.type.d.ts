import type { Mo00018Type } from '@/types/business/components/Mo00018Type'
import type {
  ChoPrtList
} from '~/repositories/cmn/entities/PrintSettingsScreenGUI1131Entity'

/**
 * Or10444:処理ロジック
 * GUI01131_［印刷設定］画面
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> DAO VAN DUONG
 */
export interface Or10444StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * 双方向バインドのデータ構造
 */
export interface Or10444TwoWayData {
  /**
   * 出力帳票印刷情報リスト
   */
  choPrtList: Record<string, ChoPrtListMaping>
}

/**
 * 行選択
 */
export interface CheckRow {
  /** 行番号 */
  index: number
  /** 選択value */
  checkbox: Mo00018Type
}

/**
 * 利用者情報リスト
 */
export interface userInfoList {
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 利用者名
   */
  nameKnj: string
  /**
   * 利用者番号
   */
  userNumber: string
  /**
   * 性別
   */
  sex: string
}

/**
 * 月間・年間表履歴リスト
 */
export interface rirekiList {
  /**
   * 履歴ID
   */
  nenkan1Id?: string
  /**
   * 計画期間ID
   */
  sc1Id: string
  /**
   * 法人ID
   */
  houjinId?: string
  /**
   * 施設ID
   */
  shisetuId?: string
  /**
   * 事業者ID
   */
  svJigyoId?: string
  /**
   * 利用者ID
   */
  userId?: string
  /**
   * 作成者
   */
  shokuId?: string
  /**
   * 作成日
   */
  createYmd?: string
  /**
   * ケース番号
   */
  caseNo?: string
  /**
   * 年度
   */
  nendoY?: string
  /**
   * 年間行事の実施に関わる総合的支援の視点
   */
  sogoShitenKnj?: string
  /**
   * 改訂
   */
  kaiteiFlg?: string
  /**
   * 開始日
   */
  startYmd?: string
  /**
   * 終了日
   */
  endYmd?: string
  /**
   * タイプ
   */
  type?: string
  /**
   * 索引
   */
  index?: number
}

interface CustomClassOptions {
  /** 分子全体のマージン・パディング調整 */
  outerClass?: string
  /** 分子全体の高さ・幅調整 */
  outerStyle?: string
  /** ラベルのマージン・パディング調整 */
  labelClass?: string
  /** ラベルの高さ・幅調整 */
  labelStyle?: string
  /** 項目（値）のマージン・パディング調整 */
  itemClass?: string
  /** 項目（値）の高さ・幅調整 */
  itemStyle?: string
}

/** カスタムクラス */
export class TyppeCustomClass {
  /** 分子全体のマージン・パディング調整 */
  outerClass: string | null | undefined
  /** 分子全体の高さ・幅調整 */
  outerStyle: string | null | undefined
  /** ラベルのマージン・パディング調整 */
  labelClass: string | null | undefined
  /** ラベルの高さ・幅調整 */
  labelStyle: string | null | undefined
  /** 項目（値）のマージン・パディング調整 */
  itemClass: string | null | undefined
  /** 項目（値）の高さ・幅調整 */
  itemStyle: string | null | undefined

  constructor({
    outerClass,
    outerStyle,
    labelClass,
    labelStyle,
    itemClass,
    itemStyle,
  }: CustomClassOptions) {
    this.outerClass = outerClass ?? ''
    this.outerStyle = outerStyle ?? ''
    this.labelClass = labelClass ?? ''
    this.labelStyle = labelStyle ?? ''
    this.itemClass = itemClass ?? ''
    this.itemStyle = itemStyle ?? ''
  }
}

/**
 * 出力帳票印刷情報リスト
 */
export interface ChoPrtListMaping extends ChoPrtList {
  /** 帳票タイトル */
  prtTitle?: { value: string }
  /** パラメータ02 */
  param02?: { value: string }
  /** パラメータ03 */
  param03?: { modelValue: string }
  /** パラメータ04 */
  param04?: { value: string }
  /** パラメータ05 */
  param05?: { modelValue: string }
  /** パラメータ045 */
  param045?: { modelValue: string }
  /** パラメータ046 */
  param046?: { modelValue: string }
  /** パラメータ047 */
  param047?: { modelValue: string }
  /** パラメータ048 */
  param048?: { modelValue: string }
  /** パラメータ049 */
  param049?: { modelValue: string }
  /** パラメータ050 */
  param050?: { modelValue: string }
}
/**
 * 会議出席者１
 */
export interface Attendance1List {
  /** 氏名 */
  name: '',
  /** 所属(職種) */
  group: '',
}
/**
 * 会議出席者2
 */
export interface Attendance2List {
  /** 氏名 */
  name: '',
  /** 所属(職種) */
  group: '',
}
/**
 * 家族情報
 */
export interface KazokuInfo {
  /** カラム */
  colStr: '',
}
/**
 * 印刷対象履歴リスト
 */
export interface PrintSubjectHistoryList191 {
  /** 利用者ID */
  userId: '',
  /** 利用者名 */
  userName: '',
  /** 開始日 */
  startYmd: '',
  /** 終了日 */
  endYmd: '',
}
/**
 * 印刷対象履歴リスト
 */
export interface PrintSubjectHistoryList198 {
  /** 利用者ID */
  userId?: string
  /** 利用者名 */
  userName?: string
  /** 職員名 */
  shokuName?: string
  /** 期間ID */
  sc1Id?: string
  /** 開始日 */
  startYmd?: string
  /** 終了日 */
  endYmd?: string
  /** アセスメントID */
  raiId?: string
  /** 調査アセスメント種別 */
  assType?: string
  /** 調査日 */
  assDateYmd?: string
  /** 調査者 */
  assShokuId?: string
  /** 結果 */
  result?: string
}

