import type {
  HeadList,
  MasterList,
} from '~/repositories/cmn/entities/MonitoringDisplayOrderSettingSelectEntity'

/**
 * Or10004:有機体:((計画ﾓﾆﾀﾘﾝｸﾞ)表示順変更モニタリング記録表)ダイアログ
 * Gui01230_表示順変更モニタリング記録表
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or10004StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * パラメータエンティティ
 */
export interface Or10004Param {
  /**
   * 実行フラグ(getData：データ再取得,''：何もしません)
   */
  executeFlag: 'getData' | ''
  /**
   * 表示順リスト
   */
  seqList: SeqList
  /**
   * データ行の選択値
   */
  selectRowId: string
}

/**
 * OneWayType
 */
export interface Or10004OneWayType {
  /**
   * ケアプラン方式
   */
  carePlanFlg: string

  /**
   * システム略称
   */
  sys3ryaku: string

  /**
   * 様式ID
   */
  youshikiId: string
  /**
   * 番号区分
   */
  noFlg?: string
  /**
   * 長期目標区分
   */
  longTermFlg?: string
  /**
   * マスタヘッダID
   */
  free1Id?: string
  /**
   * 計画ﾓﾆﾀﾘﾝｸﾞ画面の一覧
   */
  list: Or10004OneList[] | Or10004TwoList[] | Or10004ThreeList[]
}
/**
 * Or10004Type
 */
export interface Or10004Type {
  /**
   * 計画ﾓﾆﾀﾘﾝｸﾞ画面の一覧
   */
  list: Or10004OneList[] | Or10004TwoList[] | Or10004ThreeList[]
}

/**
 * Or10004CopyType
 */
export interface Or10004CopyType {
  /**
   * 計画ﾓﾆﾀﾘﾝｸﾞ画面の一覧
   */
  list1: Or10004OneList[]
  /**
   * 計画ﾓﾆﾀﾘﾝｸﾞ画面の一覧
   */
  list2: Or10004TwoList[]
  /**
   * 計画ﾓﾆﾀﾘﾝｸﾞ画面の一覧
   */
  list3: Or10004ThreeList[]
  /**
   * データ行の選択値
   */
  selectRowId: string
}

/**
 * Or10004List
 */
export interface Or10004OneList {
  /**
   * id
   */
  id: string

  /**
   * 表示順
   */
  seq: string

  /**
   * 目標
   */
  mokuhyoKnj: string

  /**
   * 対象期間
   */
  kikanKnj: string

  /**
   * サービスの実施状況
   */
  jyoukyouKnj: string

  /**
   * 目標達成状況（担当者評価）
   */
  tantoHyoukaCd: string

  /**
   * その理由（担当者評価）
   */
  tantoHyoukaKnj: string

  /**
   * 目標達成状況（利用者評価）
   */
  userHyoukaCd: string

  /**
   * その理由（利用者評価）
   */
  userHyoukaKnj: string

  /**
   * 今後の対応
   */
  taiouCd: string

  /**
   * その理由
   */
  taiouKnj: string
}

/**
 * Or10004List
 */
export interface Or10004TwoList {
  /**
   * id
   */
  id: string
  /**
   * 課題
   */
  kadaiKnj: string

  /**
   * 長期目標
   */
  choukiKnj: string

  /**
   * 短期目標
   */
  tankiKnj: string

  /**
   * サービス内容
   */
  kaigoKnj: string

  /**
   * 課題番号
   */
  kadaiNo: string

  /**
   * 介護番号
   */
  servNo: string

  /**
   * 実行の確認ＣＤ
   */
  kakuninCd: string

  /**
   * 実行の確認
   */
  kakuninKnj: string

  /**
   * 実行の方法ＣＤ
   */
  houhouCd: string

  /**
   * 実行の方法
   */
  houhouKnj: string

  /**
   * 確認期日
   */
  kakuninYmd: string

  /**
   * 本人の意見ＣＤ
   */
  ikenHonCd: string

  /**
   * 本人の意見
   */
  ikenHonKnj: string

  /**
   * 家族の意見ＣＤ
   */
  ikenKazCd: string

  /**
   * 家族の意見
   */
  ikenKazKnj: string

  /**
   * ニーズ充足度ＣＤ
   */
  jusokuCd: string

  /**
   * ニーズ充足度
   */
  jusokuKnj: string

  /**
   * 今後の対応ＣＤ
   */
  taiouCd: string

  /**
   * 今後の対応
   */
  taiouKnj: string

  /**
   * 表示順
   */
  seq: string
}

/**
 * Or10004List
 */
export interface Or10004ThreeList {
  /**
   * id
   */
  id: string
  /**
   * seq
   */
  seq: string
  /** カスタマイズデータ */
  [key: string]:
    | {
        /** 文章 */
        koumokuKnj: string
        /** コード */
        koumokuCod: string
        /** 日付 */
        koumokuYmd: string
      }
    | { value: string }
    | string
}

/**
 * マスタ
 */
export interface MasterType {
  /**
   *区分コード
   */
  kbnCd: string
  /**
   * 内容
   */
  textKnj: string
}

/**
 * 表示順変更モニタリング記録表モーダルの初期情報（表示順リスト）
 */
export interface SeqList {
  /**
   * 項目数
   */
  columnCount: string
  /**
   * ヘッダデータ情報リスト
   */
  headList: HeadList[]
  /**
   * 実行確認情報リスト
   */
  jikouList: MasterList[]
  /**
   * 確認方法情報リスト
   */
  kakuninList: MasterList[]
  /**
   * 意見情報リスト
   */
  ikenList: MasterList[]
  /**
   * 充足度情報リスト
   */
  jusokuList: MasterList[]
  /**
   * 対応情報リスト
   */
  taiouList: MasterList[]
}
