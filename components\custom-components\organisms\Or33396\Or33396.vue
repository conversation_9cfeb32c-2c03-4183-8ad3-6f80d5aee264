<script setup lang="ts">
/**
 * Or33396:［フェースシート（パッケージプラン）］③画面
 * GUI00637_［フェースシート（パッケージプラン）］③画面
 *
 * @description
 * ［フェースシート（パッケージプラン）］③画面
 *
 * <AUTHOR>
 */
import { computed, onBeforeMount, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useScreenTwoWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { Or27017Const } from '~/components/custom-components/organisms/Or27017/Or27017.constants'
import { Or27017Logic } from '~/components/custom-components/organisms/Or27017/Or27017.logic'
import { Or28333Const } from '~/components/custom-components/organisms/Or28333/Or28333.constants'
import { Or28333Logic } from '~/components/custom-components/organisms/Or28333/Or28333.logic'
import { Or29520Const } from '~/components/custom-components/organisms/Or29520/Or29520.constants'
import { Or29520Logic } from '~/components/custom-components/organisms/Or29520/Or29520.logic'
import { Or33396Const } from '~/components/custom-components/organisms/Or33396/Or33396.constants'
import { Or51773Const } from '~/components/custom-components/organisms/Or51773/Or51773.constants'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Or27017Type, Or27017OnewayType } from '~/types/cmn/business/components/Or27017Type'
import type { Or28333Type, Or28333OnewayType } from '~/types/cmn/business/components/Or28333Type'
import type { Or29520Type } from '~/types/cmn/business/components/Or29520Type'
import type { Or33396Type, Or33396OnewayType } from '~/types/cmn/business/components/Or33396Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'

const { t } = useI18n()
// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or33396OnewayType
}
// ①インターフェース定義
const props = defineProps<Props>()

// ②デフォルト定義
const defaultOtherOneWay = reactive({
  mo00009: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType, // ボタン
  mo00020: {
    showItemLabel: false,
    maxlength: '10',
    totalWidth: '140',
  } as Mo00020OnewayType, // 年月日テキストフィールド
  mo00045: {
    class: 'text-filed',
    showItemLabel: false,
    customClass: {
      outerClass: 'mr-0',
    },
  } as Mo00045OnewayType, // テキストフィールド
  mo00046: {
    showItemLabel: false,
    noResize: true,
    autoGrow: false,
  } as Mo00046OnewayType, // テキストエリア
  mo00615: {
    showItemLabel: true,
    showRequiredLabel: false,
    customClass: {
      outerStyle: 'background: none; min-height: 30px;',
    },
  } as Mo00615OnewayType, // ラベル
})

// ③双方向バインド用の内部変数
const { refValue } = useScreenTwoWayBind<Or33396Type>({
  cpId: Or33396Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
const other = reactive({
  or51775: {} as Or51775Type,
  or28333: {} as Or28333Type,
  or27017: {} as Or27017Type,
  or29520: {} as Or29520Type,
})

// ④片方向バインド用の内部変数
const localOneway = reactive<Or33396OnewayType>({
  ...cloneDeep(Or33396Const.DEFAULT.ONE_WAY),
  ...props.onewayModelValue,
})

const otherOneWay = reactive({
  // ラベル
  mo00615MedicalInsurance: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.medical-insurance'),
  } as Mo00615OnewayType, // 医療保険
  mo00615Procedures: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.procedures-for-the-residential-exception'),
  } as Mo00615OnewayType, // 国民健康保険における住所地特例手続き
  mo00615NationalInsurance: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.national-health-insurance-full'),
  } as Mo00615OnewayType, // 国民健康保険
  mo00615Insurer: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.insurer'),
  } as Mo00615OnewayType, // 保険者
  mo00615InsuredNum: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.insured-person-number-1'),
  } as Mo00615OnewayType, // 被保険者番号
  mo00615HouseholdHead: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.household-head') + t('label.white-space-jp'),
  } as Mo00615OnewayType, // 世帯主
  mo00615LaterCareInsurance: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.later-period-senior-medical-care-insurance'),
  } as Mo00615OnewayType, // 後期高齢者医療保険
  mo00615HealthInsurance: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.health-insurance'),
  } as Mo00615OnewayType, // 健康保険
  mo00615InsuredPerson: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.insured-person'),
  } as Mo00615OnewayType, // 被保険者
  mo00615HavingHealthNb: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.having-health-notebook'),
  } as Mo00615OnewayType, // 健康手帳の有無
  mo00615StatusOfIll: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.status-of-major-illnesses'),
  } as Mo00615OnewayType, // 主な疾病の状況等
  mo00615PastCurrentIll: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.past-medical-history') + t('label.connect') + t('label.current-illness'),
  } as Mo00615OnewayType, // 既往歴・現疾患
  mo00615StatusOfMedic: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.status-of-medication'),
  } as Mo00615OnewayType, // 服薬状況
  mo00615HosHistory: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.hospitalization-history'),
  } as Mo00615OnewayType, // 入院履歴
  mo00615DiseaseName1: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.disease-name-circle-1'),
  } as Mo00615OnewayType, // ①病名
  mo00615DiseaseName2: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.disease-name-circle-2'),
  } as Mo00615OnewayType, // ②病名
  mo00615DiseaseName3: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.disease-name-circle-3'),
  } as Mo00615OnewayType, // ③病名
  mo00615DiseaseName4: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.disease-name-circle-4'),
  } as Mo00615OnewayType, // ④病名
  mo00615DiseaseName5: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.disease-name-circle-5'),
  } as Mo00615OnewayType, // ⑤病名
  mo00615DiseaseName6: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.disease-name-circle-6'),
  } as Mo00615OnewayType, // ⑥病名
  mo00615DiseaseName7: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.disease-name-circle-7'),
  } as Mo00615OnewayType, // ⑦病名
  mo00615DiseaseName8: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.disease-name-circle-8'),
  } as Mo00615OnewayType, // ⑧病名
  mo00615DiseaseName9: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.disease-name-circle-9'),
  } as Mo00615OnewayType, // ⑨病名
  mo00615DiseaseName10: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.disease-name-circle-10'),
  } as Mo00615OnewayType, // ⑩病名
  mo00615DateOfOnset: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.date-of-onset'),
  } as Mo00615OnewayType, // 発病年月日
  mo00615CompletionDateLeft: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.left-bracket') + t('label.completion-date-1'),
  } as Mo00615OnewayType, // (完了年月日
  mo00615CompletionDateRight: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.right-bracket'),
  } as Mo00615OnewayType, // )
  mo00615DrugNameEfficacyPrecautions: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.drug-name-efficacy-precautions-etc'),
  } as Mo00615OnewayType, // ＊薬剤名・効能・注意事項等
  mo00615RegularMedication: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.regular-medication'),
  } as Mo00615OnewayType, // 定期薬剤
  mo00615AsNeededMedication: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.as-needed-medication-etc'),
  } as Mo00615OnewayType, // ＊頓服等
  mo00615InfectiousDisease: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.infectious-disease-allergy-info'),
  } as Mo00615OnewayType, // 感染症・アレルギー情報
  mo00615NameAndContactInfo: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.name-and-contact-information-of-physician-institution'),
  } as Mo00615OnewayType, // 嘱託医 医療機関等の名称・連絡先
  mo00615PastMedical: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.past-medical-history-info'),
  } as Mo00615OnewayType, // 既往歴情報
  mo00615Hospital: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.hospital'),
  } as Mo00615OnewayType, // 医療機関
  mo00615Period: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.period'),
  } as Mo00615OnewayType, // 期間
  mo00615Wavy: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.wavy-withoutblank'),
  } as Mo00615OnewayType, // ～
  mo00615PrincipalDiagnosis1: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.principal-diagnosis-circle-1'),
  } as Mo00615OnewayType, // ①主たる傷病名
  mo00615PrincipalDiagnosis2: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.principal-diagnosis-circle-2'),
  } as Mo00615OnewayType, // ②主たる傷病名
  mo00615PrincipalDiagnosis3: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.principal-diagnosis-circle-3'),
  } as Mo00615OnewayType, // ③主たる傷病名
  mo00615PrincipalDiagnosis4: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.principal-diagnosis-circle-4'),
  } as Mo00615OnewayType, // ④主たる傷病名
  mo00615PrincipalDiagnosis5: {
    ...defaultOtherOneWay.mo00615,
    itemLabel: t('label.principal-diagnosis-circle-5'),
  } as Mo00615OnewayType, // ⑤主たる傷病名
  // テキストフィールド
  mo00045Insurer: {
    ...defaultOtherOneWay.mo00045,
    maxLength: '8',
  } as Mo00045OnewayType, // 保険者
  mo00045InsuredNum: {
    ...defaultOtherOneWay.mo00045,
    maxLength: '10',
  } as Mo00045OnewayType, // 被保険者番号
  mo00045HouseholdHead: {
    ...defaultOtherOneWay.mo00045,
    maxLength: '8',
  } as Mo00045OnewayType, // 世帯主
  mo00045InsuredPerson: {
    ...defaultOtherOneWay.mo00045,
    maxLength: '10',
  } as Mo00045OnewayType, // 被保険者
  mo00045DiseaseName: {
    ...defaultOtherOneWay.mo00045,
    maxLength: '18',
  } as Mo00045OnewayType, // 病名
  mo00045PrincipalDiagnosis: {
    ...defaultOtherOneWay.mo00045,
    maxLength: '18',
  } as Mo00045OnewayType, // 主たる傷病名
  // ラジオボタン
  mo00039StatusofIll: {
    name: 'status-of-illness',
    showItemLabel: false,
    items: [],
  } as Mo00039OnewayType,
  mo00039TokuRei: {
    name: 'toku-rei-te-tsuki',
    showItemLabel: false,
    items: [],
  } as Mo00039OnewayType,
  mo00039UMuSetTei: {
    name: 'u-mu-sei-tei',
    showItemLabel: false,
    items: [],
  } as Mo00039OnewayType,
  // テキストエリア
  mo00046DrugNameEfficacyPrecautions: {
    ...defaultOtherOneWay.mo00046,
    maxlength: '396',
    rows: 9,
    maxRows: '9',
  } as Mo00046OnewayType, // ＊薬剤名・効能・注意事項等
  mo00046AsNeededMedication: {
    ...defaultOtherOneWay.mo00046,
    maxlength: '176',
    rows: 4,
    maxRows: '4',
  } as Mo00046OnewayType, // ＊頓服等
  mo00046InfectiousDisease: {
    ...defaultOtherOneWay.mo00046,
    maxlength: '352',
    rows: 8,
    maxRows: '8',
  } as Mo00046OnewayType, // 感染症・アレルギー情報
  mo00046NameAndContactInfo: {
    ...defaultOtherOneWay.mo00046,
    maxlength: '240',
    rows: 2,
    maxRows: '2',
  } as Mo00046OnewayType, // 嘱託医 医療機関等の名称・連絡先
  // 入力支援
  or51775: {
    title: '',
    screenId: Or33396Const.SCREEN_ID,
    bunruiId: '',
    t1Cd: '2030',
    t2Cd: '',
    t3Cd: '',
    tableName: 'cpn_tuc_syp_face23',
    columnName: '',
    assessmentMethod: systemCommonsStore.getAuthzOtherCaremanager?.assessmentMethod ?? '',
    inputContents: '',
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    mode: '',
  } as Or51775OnewayType,
  // 保険選択
  or28333: {
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    botanName: '',
  } as Or28333OnewayType,
  // 定期薬剤情報
  or27017: {
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    jisshiYmd: localOneway.createDate,
  } as Or27017OnewayType,
})

// ⑤ウォッチャー
watch(
  () => props.onewayModelValue,
  (newValue) => {
    Object.assign(localOneway, newValue)

    // 複写画面の場合、テキストフィールドを編集不可にする
    otherOneWay.mo00045Insurer.readonly = props.onewayModelValue.isCopy
    otherOneWay.mo00045InsuredNum.readonly = props.onewayModelValue.isCopy
    otherOneWay.mo00045HouseholdHead.readonly = props.onewayModelValue.isCopy
    otherOneWay.mo00045InsuredPerson.readonly = props.onewayModelValue.isCopy
    otherOneWay.mo00045DiseaseName.readonly = props.onewayModelValue.isCopy
    otherOneWay.mo00045PrincipalDiagnosis.readonly = props.onewayModelValue.isCopy

    // 複写画面の場合、日付テキストフィールドを編集不可にする
    defaultOtherOneWay.mo00020.disabled = props.onewayModelValue.isCopy

    // 複写画面の場合、テキストエリアを編集不可にする
    otherOneWay.mo00046DrugNameEfficacyPrecautions.readonly = props.onewayModelValue.isCopy
    otherOneWay.mo00046AsNeededMedication.readonly = props.onewayModelValue.isCopy
    otherOneWay.mo00046InfectiousDisease.readonly = props.onewayModelValue.isCopy
    otherOneWay.mo00046NameAndContactInfo.readonly = props.onewayModelValue.isCopy

    // 複写画面の場合、ラジオボタンを編集不可にする
    otherOneWay.mo00039StatusofIll.disabled = props.onewayModelValue.isCopy
    otherOneWay.mo00039TokuRei.disabled = props.onewayModelValue.isCopy
    otherOneWay.mo00039UMuSetTei.disabled = props.onewayModelValue.isCopy

    // 複写画面の場合、アイコンボタンをクリック不可にする
    defaultOtherOneWay.mo00009.disabled = props.onewayModelValue.isCopy
  },
  { deep: true, immediate: true }
)

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or51775 = ref({ uniqueCpId: '' }) // Or51775：入力支援［ケアマネ］モーダル
const or28333 = ref({ uniqueCpId: '' }) // Or28333:［保険選択］画面モーダル
const or27017 = ref({ uniqueCpId: '' }) // Or27017：定期薬剤情報モーダル
const or29520 = ref({ uniqueCpId: '' }) // Or29520：［医師選択］画面

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or28333Const.CP_ID(1)]: or28333.value,
  [Or27017Const.CP_ID(1)]: or27017.value,
  [Or29520Const.CP_ID(1)]: or29520.value,
})

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ［保険選択］画面の表示状態を返すComputed
 */
const showDialogOr28333 = computed(() => {
  return Or28333Logic.state.get(or28333.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ［定期薬剤情報］画面の表示状態を返すComputed
 */
const showDialogOr27017 = computed(() => {
  return Or27017Logic.state.get(or27017.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ［医師選択］画面の表示状態を返すComputed
 */
const showDialogOr29520 = computed(() => {
  return Or29520Logic.state.get(or29520.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 疾病の状況
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_STATUS_OF_ILLNESS },
    // 特例手続き
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TOKUREITETSUDUKI },
    // 有無設定
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_UMUSETTEI },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 疾病の状況
  otherOneWay.mo00039StatusofIll.items?.splice(0)
  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_STATUS_OF_ILLNESS).forEach((item) => {
    otherOneWay.mo00039StatusofIll.items?.push({
      label: item.label,
      value: item.value as unknown as number,
    })
  })

  // 特例手続き
  otherOneWay.mo00039TokuRei.items?.splice(0)
  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_TOKUREITETSUDUKI).forEach((item) => {
    otherOneWay.mo00039TokuRei.items?.push({
      label: item.label,
      value: item.value as unknown as number,
    })
  })

  // 有無設定
  otherOneWay.mo00039UMuSetTei.items?.splice(0)
  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_UMUSETTEI).forEach((item) => {
    otherOneWay.mo00039UMuSetTei.items?.push({
      label: item.label,
      value: item.value as unknown as number,
    })
  })
}

/**
 * 共通入力支援画面を開く
 *
 * @param title - タイトル
 */
const openOr51775 = (title: string) => {
  otherOneWay.or51775.title = title
  otherOneWay.or51775.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  switch (title) {
    case t('label.national-health-insurance-full'): {
      // 国民健康保険 保険者
      otherOneWay.or51775.t2Cd = '1'
      otherOneWay.or51775.t3Cd = '1'
      otherOneWay.or51775.columnName = 'kokuho_knj'
      otherOneWay.or51775.inputContents = refValue.value?.kokuhoKnj.value ?? ''
      other.or51775.modelValue = refValue.value?.kokuhoKnj.value ?? ''
      break
    }
    case t('label.medical-care-recipient-certificate'): {
      // 後期高齢者医療保険 保険者
      otherOneWay.or51775.t2Cd = '1'
      otherOneWay.or51775.t3Cd = '3'
      otherOneWay.or51775.columnName = 'iryo_jukyu_knj'
      otherOneWay.or51775.inputContents = refValue.value?.iryoJukyuKnj.value ?? ''
      other.or51775.modelValue = refValue.value?.iryoJukyuKnj.value ?? ''
      break
    }
    case t('label.social-insurance'): {
      // 健康保険 保険者
      otherOneWay.or51775.t2Cd = '1'
      otherOneWay.or51775.t3Cd = '2'
      otherOneWay.or51775.columnName = 'shaho_knj'
      otherOneWay.or51775.inputContents = refValue.value?.shahoKnj.value ?? ''
      other.or51775.modelValue = refValue.value?.shahoKnj.value ?? ''
      break
    }
    case t('label.disease-name-circle-1'): {
      // ①病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'sick1_knj'
      otherOneWay.or51775.inputContents = refValue.value?.sick1Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.sick1Knj.value ?? ''
      break
    }
    case t('label.disease-name-circle-2'): {
      // ②病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'sick2_knj'
      otherOneWay.or51775.inputContents = refValue.value?.sick2Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.sick2Knj.value ?? ''
      break
    }
    case t('label.disease-name-circle-3'): {
      // ③病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'sick3_knj'
      otherOneWay.or51775.inputContents = refValue.value?.sick3Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.sick3Knj.value ?? ''
      break
    }
    case t('label.disease-name-circle-4'): {
      // ④病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'sick4_knj'
      otherOneWay.or51775.inputContents = refValue.value?.sick4Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.sick4Knj.value ?? ''
      break
    }
    case t('label.disease-name-circle-5'): {
      // ⑤病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'sick5_knj'
      otherOneWay.or51775.inputContents = refValue.value?.sick5Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.sick5Knj.value ?? ''
      break
    }
    case t('label.disease-name-circle-6'): {
      // ⑥病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'sick6_knj'
      otherOneWay.or51775.inputContents = refValue.value?.sick6Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.sick6Knj.value ?? ''
      break
    }
    case t('label.disease-name-circle-7'): {
      // ⑦病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'sick7_knj'
      otherOneWay.or51775.inputContents = refValue.value?.sick7Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.sick7Knj.value ?? ''
      break
    }
    case t('label.disease-name-circle-8'): {
      // ⑧病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'sick8_knj'
      otherOneWay.or51775.inputContents = refValue.value?.sick8Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.sick8Knj.value ?? ''
      break
    }
    case t('label.disease-name-circle-9'): {
      // ⑨病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'sick9_knj'
      otherOneWay.or51775.inputContents = refValue.value?.sick9Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.sick9Knj.value ?? ''
      break
    }
    case t('label.disease-name-circle-10'): {
      // ⑩病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'sick10_knj'
      otherOneWay.or51775.inputContents = refValue.value?.sick10Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.sick10Knj.value ?? ''
      break
    }
    case t('label.principal-diagnosis-circle-1'): {
      // ①主たる傷病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'nyuin1_knj'
      otherOneWay.or51775.inputContents = refValue.value?.nyuin1Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.nyuin1Knj.value ?? ''
      break
    }
    case t('label.principal-diagnosis-circle-2'): {
      // ②主たる傷病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'nyuin2_knj'
      otherOneWay.or51775.inputContents = refValue.value?.nyuin2Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.nyuin2Knj.value ?? ''
      break
    }
    case t('label.principal-diagnosis-circle-3'): {
      // ③主たる傷病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'nyuin3_knj'
      otherOneWay.or51775.inputContents = refValue.value?.nyuin3Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.nyuin3Knj.value ?? ''
      break
    }
    case t('label.principal-diagnosis-circle-4'): {
      // ④主たる傷病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'nyuin4_knj'
      otherOneWay.or51775.inputContents = refValue.value?.nyuin4Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.nyuin4Knj.value ?? ''
      break
    }
    case t('label.principal-diagnosis-circle-5'): {
      // ⑤主たる傷病名
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '4'
      otherOneWay.or51775.columnName = 'nyuin5_knj'
      otherOneWay.or51775.inputContents = refValue.value?.nyuin5Knj.value ?? ''
      other.or51775.modelValue = refValue.value?.nyuin5Knj.value ?? ''
      break
    }
    case t('label.drug-name-efficacy-precautions-etc'): {
      // ＊薬剤名・効能・注意事項等
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '8'
      otherOneWay.or51775.columnName = 'fukuyaku_knj'
      otherOneWay.or51775.inputContents = refValue.value?.fukuyakuKnj.value ?? ''
      other.or51775.modelValue = refValue.value?.fukuyakuKnj.value ?? ''
      break
    }
    case t('label.as-needed-medication-etc'): {
      // ＊頓服等
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '9'
      otherOneWay.or51775.columnName = 'tonpuku_knj'
      otherOneWay.or51775.inputContents = refValue.value?.tonpukuKnj.value ?? ''
      other.or51775.modelValue = refValue.value?.tonpukuKnj.value ?? ''
      break
    }
    case t('label.infectious-disease-allergy-info'): {
      // 感染症・アレルギー情報
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '10'
      otherOneWay.or51775.columnName = 'kansen_knj'
      otherOneWay.or51775.inputContents = refValue.value?.kansenKnj.value ?? ''
      other.or51775.modelValue = refValue.value?.kansenKnj.value ?? ''
      break
    }
    case t('label.name-and-contact-information-of-physician-institution'): {
      // 嘱託医 医療機関等の名称・連絡先
      otherOneWay.or51775.t2Cd = '2'
      otherOneWay.or51775.t3Cd = '11'
      otherOneWay.or51775.columnName = 'zokutakui_knj'
      otherOneWay.or51775.inputContents = refValue.value?.zokutakuiKnj.value ?? ''
      other.or51775.modelValue = refValue.value?.zokutakuiKnj.value ?? ''
      break
    }
    default:
      break
  }

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or51773Const.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or51773Const.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }

  switch (otherOneWay.or51775.title) {
    case t('label.national-health-insurance-full'): {
      // 国民健康保険 保険者
      refValue.value!.kokuhoKnj.value = setOrAppendValue(
        refValue.value?.kokuhoKnj.value ?? '',
        data
      )
      break
    }
    case t('label.medical-care-recipient-certificate'): {
      // 後期高齢者医療保険 保険者
      refValue.value!.iryoJukyuKnj.value = setOrAppendValue(
        refValue.value?.iryoJukyuKnj.value ?? '',
        data
      )
      break
    }
    case t('label.social-insurance'): {
      // 健康保険 保険者
      refValue.value!.shahoKnj.value = setOrAppendValue(refValue.value?.shahoKnj.value ?? '', data)
      break
    }
    case t('label.disease-name-circle-1'): {
      // ①病名
      refValue.value!.sick1Knj.value = setOrAppendValue(refValue.value?.sick1Knj.value ?? '', data)
      break
    }
    case t('label.disease-name-circle-2'): {
      // ②病名
      refValue.value!.sick2Knj.value = setOrAppendValue(refValue.value?.sick2Knj.value ?? '', data)
      break
    }
    case t('label.disease-name-circle-3'): {
      // ③病名
      refValue.value!.sick3Knj.value = setOrAppendValue(refValue.value?.sick3Knj.value ?? '', data)
      break
    }
    case t('label.disease-name-circle-4'): {
      // ④病名
      refValue.value!.sick4Knj.value = setOrAppendValue(refValue.value?.sick4Knj.value ?? '', data)
      break
    }
    case t('label.disease-name-circle-5'): {
      // ⑤病名
      refValue.value!.sick5Knj.value = setOrAppendValue(refValue.value?.sick5Knj.value ?? '', data)
      break
    }
    case t('label.disease-name-circle-6'): {
      // ⑥病名
      refValue.value!.sick6Knj.value = setOrAppendValue(refValue.value?.sick6Knj.value ?? '', data)
      break
    }
    case t('label.disease-name-circle-7'): {
      // ⑦病名
      refValue.value!.sick7Knj.value = setOrAppendValue(refValue.value?.sick7Knj.value ?? '', data)
      break
    }
    case t('label.disease-name-circle-8'): {
      // ⑧病名
      refValue.value!.sick8Knj.value = setOrAppendValue(refValue.value?.sick8Knj.value ?? '', data)
      break
    }
    case t('label.disease-name-circle-9'): {
      // ⑨病名
      refValue.value!.sick9Knj.value = setOrAppendValue(refValue.value?.sick9Knj.value ?? '', data)
      break
    }
    case t('label.disease-name-circle-10'): {
      // ⑩病名
      refValue.value!.sick10Knj.value = setOrAppendValue(
        refValue.value?.sick10Knj.value ?? '',
        data
      )
      break
    }
    case t('label.principal-diagnosis-circle-1'): {
      // ①主たる傷病名
      refValue.value!.nyuin1Knj.value = setOrAppendValue(
        refValue.value?.nyuin1Knj.value ?? '',
        data
      )
      break
    }
    case t('label.principal-diagnosis-circle-2'): {
      // ②主たる傷病名
      refValue.value!.nyuin2Knj.value = setOrAppendValue(
        refValue.value?.nyuin2Knj.value ?? '',
        data
      )
      break
    }
    case t('label.principal-diagnosis-circle-3'): {
      // ③主たる傷病名
      refValue.value!.nyuin3Knj.value = setOrAppendValue(
        refValue.value?.nyuin3Knj.value ?? '',
        data
      )
      break
    }
    case t('label.principal-diagnosis-circle-4'): {
      // ④主たる傷病名
      refValue.value!.nyuin4Knj.value = setOrAppendValue(
        refValue.value?.nyuin4Knj.value ?? '',
        data
      )
      break
    }
    case t('label.principal-diagnosis-circle-5'): {
      // ⑤主たる傷病名
      refValue.value!.nyuin5Knj.value = setOrAppendValue(
        refValue.value?.nyuin5Knj.value ?? '',
        data
      )
      break
    }
    case t('label.drug-name-efficacy-precautions-etc'): {
      // ＊薬剤名・効能・注意事項等
      refValue.value!.fukuyakuKnj.value = setOrAppendValue(
        refValue.value?.fukuyakuKnj.value ?? '',
        data
      )
      break
    }
    case t('label.as-needed-medication-etc'): {
      // ＊頓服等
      refValue.value!.tonpukuKnj.value = setOrAppendValue(
        refValue.value?.tonpukuKnj.value ?? '',
        data
      )
      break
    }
    case t('label.infectious-disease-allergy-info'): {
      // 感染症・アレルギー情報
      refValue.value!.kansenKnj.value = setOrAppendValue(
        refValue.value?.kansenKnj.value ?? '',
        data
      )
      break
    }
    case t('label.name-and-contact-information-of-physician-institution'): {
      // 嘱託医 医療機関等の名称・連絡先
      refValue.value!.zokutakuiKnj.value = setOrAppendValue(
        refValue.value?.zokutakuiKnj.value ?? '',
        data
      )
      break
    }
    default:
      break
  }

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * GUI00641 保険選択画面をポップアップで起動する。
 *
 * @param botanName - ボタン名
 */
const openOr28333 = (botanName: string) => {
  otherOneWay.or28333.botanName = botanName
  otherOneWay.or28333.userId = systemCommonsStore.getUserSelectSelfId() ?? ''

  // Or28333のダイアログ開閉状態を更新する
  Or28333Logic.state.set({
    uniqueCpId: or28333.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 保険選択画面閉じる後のデータ更新
 */
watch(
  () => other.or28333,
  () => {
    if (!other.or28333) {
      return
    }

    // 返却情報.保険者番号の先頭から 8バイト分
    const insurer = other.or28333.publicno.substring(
      0,
      Number(otherOneWay.mo00045Insurer.maxLength)
    )
    // 返却情報.記号・番号の先頭から 10バイト分
    const insureredNo = other.or28333.kigonoKnj.substring(
      0,
      Number(otherOneWay.mo00045InsuredNum.maxLength)
    )

    switch (otherOneWay.or28333.botanName) {
      // 健康保険
      case Or33396Const.GUI00637_BTN_1: {
        refValue.value!.shahoKnj.value = insurer
        refValue.value!.shahoHiHokenjaNo.value = insureredNo
        break
      }
      // 国民健康保険
      case Or33396Const.GUI00637_BTN_2: {
        refValue.value!.kokuhoKnj.value = insurer
        refValue.value!.kokuhoHiHokenjaNo.value = insureredNo
        break
      }
      // 後期高齢者医療保険
      case Or33396Const.GUI00637_BTN_6: {
        refValue.value!.iryoJukyuKnj.value = insurer
        refValue.value!.koukiHiHokenjaNo.value = insureredNo
        break
      }
      default:
        break
    }
  }
)

/**
 * GUI00640 定期薬剤情報画面をポップアップで起動する。
 */
const openOr27017 = () => {
  otherOneWay.or27017.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  otherOneWay.or27017.houjinId = systemCommonsStore.getHoujinId ?? ''
  otherOneWay.or27017.shisetuId = systemCommonsStore.getShisetuId ?? ''
  otherOneWay.or27017.jisshiYmd = localOneway.createDate

  // or27017のダイアログ開閉状態を更新する
  Or27017Logic.state.set({
    uniqueCpId: or27017.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 定期薬剤情報画面閉じる後のデータ更新
 *
 * @param result - 画面の戻り値
 */
const handleOr27017Confirm = (result: string) => {
  // 画面.服薬状況＝返却情報.服薬状況の先頭から 396バイト分を設定する（上書き）。
  refValue.value!.fukuyakuKnj.value = result.substring(
    0,
    Number(otherOneWay.mo00046DrugNameEfficacyPrecautions.maxlength)
  )
}

/**
 * GUI00643_［医師選択］画面をポップアップで起動する。
 */
const openOr29520 = () => {
  // or29520のダイアログ開閉状態を更新する
  Or29520Logic.state.set({
    uniqueCpId: or29520.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 医師選択画面閉じる後のデータ更新
 */
watch(
  () => other.or29520,
  () => {
    if (!other.or29520) {
      return
    }
    // 画面.属託医、医療機関等の名称・連絡先＝変数.医療機関情報の240バイト分
    refValue.value!.zokutakuiKnj.value = other.or29520.resultInfo.substring(
      0,
      Number(otherOneWay.mo00046NameAndContactInfo.maxlength)
    )
  }
)

/**************************************************
 * ライフサイクルフック
 **************************************************/
onBeforeMount(async () => {
  // 汎用コード取得
  await initCodes()
})
</script>
<template>
  <c-v-container
    fluid
    class="container pa-0"
  >
    <c-v-row>
      <!-- 医療保険 -->
      <c-v-col
        cols="8"
        class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
      >
        <base-mo00615 :oneway-model-value="otherOneWay.mo00615MedicalInsurance" />
      </c-v-col>
      <!-- 国民健康保険における住所地特例手続き -->
      <c-v-col
        cols="4"
        class="bordered border-b-0 pa-2 d-flex align-center"
      >
        <base-mo00615 :oneway-model-value="otherOneWay.mo00615Procedures" />
      </c-v-col>
      <!-- 国民健康保険 -->
      <c-v-col
        cols="2"
        class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
      >
        <c-v-row
          justify="space-between"
          class="h-100"
        >
          <c-v-col
            cols="auto"
            class="d-flex pa-0 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615NationalInsurance" />
          </c-v-col>
          <c-v-col
            cols="auto"
            class="border-s-thin pa-0 pl-2 d-flex align-center"
          >
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              @click="openOr28333(Or33396Const.GUI00637_BTN_2)"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <!-- 保険者 -->
      <c-v-col
        cols="2"
        class="pa-0 d-flex align-center"
      >
        <c-v-row class="h-100">
          <c-v-col
            cols="auto"
            class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615Insurer" />
          </c-v-col>
          <c-v-col class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center">
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              @click="openOr51775(t('label.national-health-insurance-full'))"
            />
            <base-mo00045
              v-model="refValue!.kokuhoKnj"
              :oneway-model-value="otherOneWay.mo00045Insurer"
              class="w-100"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <!-- 被保険者番号 -->
      <c-v-col
        cols="2"
        class="pa-0 d-flex align-center"
      >
        <c-v-row class="h-100">
          <c-v-col
            cols="auto"
            class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615InsuredNum" />
          </c-v-col>
          <c-v-col class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center">
            <base-mo00045
              v-model="refValue!.kokuhoHiHokenjaNo"
              :oneway-model-value="otherOneWay.mo00045InsuredNum"
              class="w-100"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <!-- 世帯主 -->
      <c-v-col
        cols="2"
        class="pa-0 d-flex align-center"
      >
        <c-v-row class="h-100">
          <c-v-col
            cols="auto"
            class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615HouseholdHead" />
          </c-v-col>
          <c-v-col class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center">
            <base-mo00045
              v-model="refValue!.kokuhoSetainushiKnj"
              :oneway-model-value="otherOneWay.mo00045HouseholdHead"
              class="w-100"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <!-- 国民健康保険における住所地特例手続き パネル -->
      <c-v-col
        cols="4"
        class="bordered bg-white border-b-0 pa-2 py-0 d-flex align-center"
      >
        <base-mo00039
          v-model="refValue!.iryoTokuKbn"
          :oneway-model-value="otherOneWay.mo00039TokuRei"
        />
        <base-mo00615
          :oneway-model-value="otherOneWay.mo00615CompletionDateLeft"
          class="pr-1"
        />
        <base-mo00020
          v-model="refValue!.iryoTokuYmd"
          :oneway-model-value="defaultOtherOneWay.mo00020"
        />
        <base-mo00615
          :oneway-model-value="otherOneWay.mo00615CompletionDateRight"
          class="pl-1"
        />
      </c-v-col>
      <!-- 後期高齡者医療保険 -->
      <c-v-col
        cols="2"
        class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
      >
        <c-v-row justify="space-between">
          <c-v-col
            cols="auto"
            class="d-flex pa-0 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615LaterCareInsurance" />
          </c-v-col>
          <c-v-col
            cols="auto"
            class="border-s-thin pa-0 pl-2 d-flex align-center"
          >
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              @click="openOr28333(Or33396Const.GUI00637_BTN_6)"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <!-- 保険者 -->
      <c-v-col
        cols="2"
        class="pa-0 d-flex align-center"
      >
        <c-v-row class="h-100">
          <c-v-col
            cols="auto"
            class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615Insurer" />
          </c-v-col>
          <c-v-col class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center">
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              @click="openOr51775(t('label.medical-care-recipient-certificate'))"
            />
            <base-mo00045
              v-model="refValue!.iryoJukyuKnj"
              :oneway-model-value="otherOneWay.mo00045Insurer"
              class="w-100"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <!-- 被保険者番号 -->
      <c-v-col
        cols="2"
        class="pa-0 d-flex align-center"
      >
        <c-v-row class="h-100">
          <c-v-col
            cols="auto"
            class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615InsuredNum" />
          </c-v-col>
          <c-v-col class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center">
            <base-mo00045
              v-model="refValue!.koukiHiHokenjaNo"
              :oneway-model-value="otherOneWay.mo00045InsuredNum"
              class="w-100"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <c-v-col
        cols="2"
        class="bordered bg-white border-e-0 border-b-0"
      >
      </c-v-col>
      <!-- 健康手帳の有無 -->
      <c-v-col
        cols="4"
        class="bordered border-b-0 pa-2 d-flex align-center"
      >
        <base-mo00615 :oneway-model-value="otherOneWay.mo00615HavingHealthNb" />
      </c-v-col>
      <!-- 健康保険 -->
      <c-v-col
        cols="2"
        class="bordered border-e-0 pa-2 d-flex align-center"
      >
        <c-v-row justify="space-between">
          <c-v-col
            cols="auto"
            class="d-flex pa-0 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615HealthInsurance" />
          </c-v-col>
          <c-v-col
            cols="auto"
            class="border-s-thin pa-0 pl-2 d-flex align-center"
          >
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              @click="openOr28333(Or33396Const.GUI00637_BTN_1)"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <!-- 保険者 -->
      <c-v-col
        cols="2"
        class="pa-0 d-flex align-center"
      >
        <c-v-row class="h-100">
          <c-v-col
            cols="auto"
            class="bordered border-e-0 pa-2 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615Insurer" />
          </c-v-col>
          <c-v-col class="bordered bg-white border-e-0 pa-2 py-0 d-flex align-center">
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              @click="openOr51775(t('label.social-insurance'))"
            />
            <base-mo00045
              v-model="refValue!.shahoKnj"
              :oneway-model-value="otherOneWay.mo00045Insurer"
              class="w-100"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <!-- 被保険者番号 -->
      <c-v-col
        cols="2"
        class="pa-0 d-flex align-center"
      >
        <c-v-row class="h-100">
          <c-v-col
            cols="auto"
            class="bordered border-e-0 pa-2 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615InsuredNum" />
          </c-v-col>
          <c-v-col class="bordered bg-white border-e-0 pa-2 py-0 d-flex align-center">
            <base-mo00045
              v-model="refValue!.shahoHiHokenjaNo"
              :oneway-model-value="otherOneWay.mo00045InsuredNum"
              class="w-100"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <!-- 被保険者 -->
      <c-v-col
        cols="2"
        class="pa-0 d-flex align-center"
      >
        <c-v-row class="h-100">
          <c-v-col
            cols="auto"
            class="bordered border-e-0 pa-2 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615InsuredPerson" />
          </c-v-col>
          <c-v-col class="bordered bg-white border-e-0 pa-2 py-0 d-flex align-center">
            <base-mo00045
              v-model="refValue!.shahoHiHokenjaKnj"
              :oneway-model-value="otherOneWay.mo00045InsuredPerson"
              class="w-100"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <!-- 健康手帳の有無 パネル -->
      <c-v-col
        cols="4"
        class="bordered bg-white pa-2 py-0 d-flex align-center"
      >
        <base-mo00039
          v-model="refValue!.kenkoTechoUmu"
          :oneway-model-value="otherOneWay.mo00039UMuSetTei"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row class="divider" />
    <c-v-row>
      <!-- 主な疾病の状況等 -->
      <c-v-col
        cols="12"
        class="bordered border-b-0 pa-2"
      >
        <base-mo00615 :oneway-model-value="otherOneWay.mo00615StatusOfIll" />
      </c-v-col>
      <!-- 既往歴・現疾患 -->
      <c-v-col
        cols="8"
        class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
      >
        <c-v-row justify="space-between">
          <c-v-col
            cols="auto"
            class="d-flex pa-0 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615PastCurrentIll" />
          </c-v-col>
          <!-- 課題 #139106 中期開発時点では常に非表示 -->
          <!-- <c-v-col
            cols="auto"
            class="border-s-thin pa-0 pl-2 d-flex align-center"
          >
            <base-mo00009 :oneway-model-value="defaultOtherOneWay.mo00009" />
          </c-v-col> -->
        </c-v-row>
      </c-v-col>
      <!-- 服薬状況 -->
      <c-v-col
        cols="4"
        class="bordered border-b-0 pa-2"
      >
        <base-mo00615 :oneway-model-value="otherOneWay.mo00615StatusOfMedic" />
      </c-v-col>
      <c-v-col
        cols="8"
        class="pa-0"
      >
        <c-v-row>
          <!-- ① -->
          <c-v-col
            cols="6"
            class="pa-0"
          >
            <c-v-row class="h-100">
              <c-v-col
                class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DiseaseName1" />
                <base-mo00009
                  :oneway-model-value="defaultOtherOneWay.mo00009"
                  class="ml-1"
                  @click="openOr51775(t('label.disease-name-circle-1'))"
                />
                <base-mo00045
                  v-model="refValue!.sick1Knj"
                  :oneway-model-value="otherOneWay.mo00045DiseaseName"
                  class="w-100"
                />
              </c-v-col>
              <!-- 発病年月日 -->
              <c-v-col
                cols="auto"
                class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DateOfOnset" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
          <c-v-col
            cols="6"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
          >
            <!-- 発病年月日 パネル -->
            <base-mo00020
              v-model="refValue!.sick1Ymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00039
              v-model="refValue!.sick1Flg"
              :oneway-model-value="otherOneWay.mo00039StatusofIll"
            />
          </c-v-col>
          <!-- ② -->
          <c-v-col
            cols="6"
            class="pa-0"
          >
            <c-v-row class="h-100">
              <c-v-col
                class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DiseaseName2" />
                <base-mo00009
                  :oneway-model-value="defaultOtherOneWay.mo00009"
                  class="ml-1"
                  @click="openOr51775(t('label.disease-name-circle-2'))"
                />
                <base-mo00045
                  v-model="refValue!.sick2Knj"
                  :oneway-model-value="otherOneWay.mo00045DiseaseName"
                  class="w-100"
                />
              </c-v-col>
              <!-- 発病年月日 -->
              <c-v-col
                cols="auto"
                class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DateOfOnset" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
          <c-v-col
            cols="6"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
          >
            <!-- 発病年月日 パネル -->
            <base-mo00020
              v-model="refValue!.sick2Ymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00039
              v-model="refValue!.sick2Flg"
              :oneway-model-value="otherOneWay.mo00039StatusofIll"
            />
          </c-v-col>
          <!-- ③ -->
          <c-v-col
            cols="6"
            class="pa-0"
          >
            <c-v-row class="h-100">
              <c-v-col
                class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DiseaseName3" />
                <base-mo00009
                  :oneway-model-value="defaultOtherOneWay.mo00009"
                  class="ml-1"
                  @click="openOr51775(t('label.disease-name-circle-3'))"
                />
                <base-mo00045
                  v-model="refValue!.sick3Knj"
                  :oneway-model-value="otherOneWay.mo00045DiseaseName"
                  class="w-100"
                />
              </c-v-col>
              <!-- 発病年月日 -->
              <c-v-col
                cols="auto"
                class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DateOfOnset" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
          <c-v-col
            cols="6"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
          >
            <!-- 発病年月日 パネル -->
            <base-mo00020
              v-model="refValue!.sick3Ymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00039
              v-model="refValue!.sick3Flg"
              :oneway-model-value="otherOneWay.mo00039StatusofIll"
            />
          </c-v-col>
          <!-- ④ -->
          <c-v-col
            cols="6"
            class="pa-0"
          >
            <c-v-row class="h-100">
              <c-v-col
                class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DiseaseName4" />
                <base-mo00009
                  :oneway-model-value="defaultOtherOneWay.mo00009"
                  class="ml-1"
                  @click="openOr51775(t('label.disease-name-circle-4'))"
                />
                <base-mo00045
                  v-model="refValue!.sick4Knj"
                  :oneway-model-value="otherOneWay.mo00045DiseaseName"
                  class="w-100"
                />
              </c-v-col>
              <!-- 発病年月日 -->
              <c-v-col
                cols="auto"
                class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DateOfOnset" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
          <c-v-col
            cols="6"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
          >
            <!-- 発病年月日 パネル -->
            <base-mo00020
              v-model="refValue!.sick4Ymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00039
              v-model="refValue!.sick4Flg"
              :oneway-model-value="otherOneWay.mo00039StatusofIll"
            />
          </c-v-col>
          <!-- ⑤ -->
          <c-v-col
            cols="6"
            class="pa-0"
          >
            <c-v-row class="h-100">
              <c-v-col
                class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DiseaseName5" />
                <base-mo00009
                  :oneway-model-value="defaultOtherOneWay.mo00009"
                  class="ml-1"
                  @click="openOr51775(t('label.disease-name-circle-5'))"
                />
                <base-mo00045
                  v-model="refValue!.sick5Knj"
                  :oneway-model-value="otherOneWay.mo00045DiseaseName"
                  class="w-100"
                />
              </c-v-col>
              <!-- 発病年月日 -->
              <c-v-col
                cols="auto"
                class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DateOfOnset" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
          <c-v-col
            cols="6"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
          >
            <!-- 発病年月日 パネル -->
            <base-mo00020
              v-model="refValue!.sick5Ymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00039
              v-model="refValue!.sick5Flg"
              :oneway-model-value="otherOneWay.mo00039StatusofIll"
            />
          </c-v-col>
          <!-- ⑥ -->
          <c-v-col
            cols="6"
            class="pa-0"
          >
            <c-v-row class="h-100">
              <c-v-col
                class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DiseaseName6" />
                <base-mo00009
                  :oneway-model-value="defaultOtherOneWay.mo00009"
                  class="ml-1"
                  @click="openOr51775(t('label.disease-name-circle-6'))"
                />
                <base-mo00045
                  v-model="refValue!.sick6Knj"
                  :oneway-model-value="otherOneWay.mo00045DiseaseName"
                  class="w-100"
                />
              </c-v-col>
              <!-- 発病年月日 -->
              <c-v-col
                cols="auto"
                class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DateOfOnset" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
          <c-v-col
            cols="6"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
          >
            <!-- 発病年月日 パネル -->
            <base-mo00020
              v-model="refValue!.sick6Ymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00039
              v-model="refValue!.sick6Flg"
              :oneway-model-value="otherOneWay.mo00039StatusofIll"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <!-- 服薬状況 パネル1 -->
      <c-v-col
        cols="4"
        class="bordered bg-white border-b-0 pa-2 d-flex flex-column"
      >
        <c-v-row
          justify="space-between"
          class="flex-grow-0"
        >
          <!-- ＊薬剤名・効能・注意事項等 -->
          <c-v-col
            cols="auto"
            class="pa-0 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615DrugNameEfficacyPrecautions" />
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              @click="openOr51775(t('label.drug-name-efficacy-precautions-etc'))"
            />
          </c-v-col>
          <!-- 定期薬剤 -->
          <c-v-col
            cols="auto"
            class="pa-0 d-flex align-center"
          >
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615RegularMedication" />
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              @click="openOr27017"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <c-v-col
            cols="12"
            class="pa-0 d-flex align-center"
          >
            <base-mo00046
              v-model="refValue!.fukuyakuKnj"
              :oneway-model-value="otherOneWay.mo00046DrugNameEfficacyPrecautions"
              class="w-100"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <c-v-col
        cols="8"
        class="pa-0"
      >
        <c-v-row>
          <!-- ⑦ -->
          <c-v-col
            cols="6"
            class="pa-0"
          >
            <c-v-row class="h-100">
              <c-v-col
                class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DiseaseName7" />
                <base-mo00009
                  :oneway-model-value="defaultOtherOneWay.mo00009"
                  class="ml-1"
                  @click="openOr51775(t('label.disease-name-circle-7'))"
                />
                <base-mo00045
                  v-model="refValue!.sick7Knj"
                  :oneway-model-value="otherOneWay.mo00045DiseaseName"
                  class="w-100"
                />
              </c-v-col>
              <!-- 発病年月日 -->
              <c-v-col
                cols="auto"
                class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DateOfOnset" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
          <c-v-col
            cols="6"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
          >
            <!-- 発病年月日 パネル -->
            <base-mo00020
              v-model="refValue!.sick7Ymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00039
              v-model="refValue!.sick7Flg"
              :oneway-model-value="otherOneWay.mo00039StatusofIll"
            />
          </c-v-col>
          <!-- ⑧ -->
          <c-v-col
            cols="6"
            class="pa-0"
          >
            <c-v-row class="h-100">
              <c-v-col
                class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DiseaseName8" />
                <base-mo00009
                  :oneway-model-value="defaultOtherOneWay.mo00009"
                  class="ml-1"
                  @click="openOr51775(t('label.disease-name-circle-8'))"
                />
                <base-mo00045
                  v-model="refValue!.sick8Knj"
                  :oneway-model-value="otherOneWay.mo00045DiseaseName"
                  class="w-100"
                />
              </c-v-col>
              <!-- 発病年月日 -->
              <c-v-col
                cols="auto"
                class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DateOfOnset" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
          <c-v-col
            cols="6"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
          >
            <!-- 発病年月日 パネル -->
            <base-mo00020
              v-model="refValue!.sick8Ymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00039
              v-model="refValue!.sick8Flg"
              :oneway-model-value="otherOneWay.mo00039StatusofIll"
            />
          </c-v-col>
          <!-- ⑨ -->
          <c-v-col
            cols="6"
            class="pa-0"
          >
            <c-v-row class="h-100">
              <c-v-col
                class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DiseaseName9" />
                <base-mo00009
                  :oneway-model-value="defaultOtherOneWay.mo00009"
                  class="ml-1"
                  @click="openOr51775(t('label.disease-name-circle-9'))"
                />
                <base-mo00045
                  v-model="refValue!.sick9Knj"
                  :oneway-model-value="otherOneWay.mo00045DiseaseName"
                  class="w-100"
                />
              </c-v-col>
              <!-- 発病年月日 -->
              <c-v-col
                cols="auto"
                class="bordered border-e-0 border-b-0 pa-2 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DateOfOnset" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
          <c-v-col
            cols="6"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-0 d-flex align-center"
          >
            <!-- 発病年月日 パネル -->
            <base-mo00020
              v-model="refValue!.sick9Ymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00039
              v-model="refValue!.sick9Flg"
              :oneway-model-value="otherOneWay.mo00039StatusofIll"
            />
          </c-v-col>
          <!-- ⑩ -->
          <c-v-col
            cols="6"
            class="pa-0"
          >
            <c-v-row class="h-100">
              <c-v-col class="bordered bg-white border-e-0 pa-2 py-0 d-flex align-center">
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DiseaseName10" />
                <base-mo00009
                  :oneway-model-value="defaultOtherOneWay.mo00009"
                  class="ml-1"
                  @click="openOr51775(t('label.disease-name-circle-10'))"
                />
                <base-mo00045
                  v-model="refValue!.sick10Knj"
                  :oneway-model-value="otherOneWay.mo00045DiseaseName"
                  class="w-100"
                />
              </c-v-col>
              <!-- 発病年月日 -->
              <c-v-col
                cols="auto"
                class="bordered border-e-0 pa-2 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615DateOfOnset" />
              </c-v-col>
            </c-v-row>
          </c-v-col>
          <c-v-col
            cols="6"
            class="bordered bg-white border-e-0 pa-2 py-0 d-flex align-center"
          >
            <!-- 発病年月日 パネル -->
            <base-mo00020
              v-model="refValue!.sick10Ymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00039
              v-model="refValue!.sick10Flg"
              :oneway-model-value="otherOneWay.mo00039StatusofIll"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <!-- 服薬状況 パネル2 -->
      <c-v-col
        cols="4"
        class="bordered bg-white pa-2 d-flex flex-column"
      >
        <c-v-row class="flex-grow-0">
          <!-- ＊頓服等 -->
          <c-v-col class="pa-0 d-flex align-center">
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615AsNeededMedication" />
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              @click="openOr51775(t('label.as-needed-medication-etc'))"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <c-v-col
            cols="12"
            class="pa-0 d-flex align-center"
          >
            <base-mo00046
              v-model="refValue!.tonpukuKnj"
              :oneway-model-value="otherOneWay.mo00046AsNeededMedication"
              class="w-100"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <c-v-row class="divider" />
    <c-v-row>
      <c-v-col
        cols="8"
        class="pa-0"
      >
        <!-- 入院履歴 -->
        <c-v-row>
          <c-v-col
            cols="12"
            class="bordered border-e-0 border-b-0 pa-2"
          >
            <c-v-row justify="space-between">
              <c-v-col
                cols="auto"
                class="d-flex pa-0 d-flex align-center"
              >
                <base-mo00615 :oneway-model-value="otherOneWay.mo00615HosHistory" />
              </c-v-col>
              <!-- 課題 #139106 中期開発時点では常に非表示 -->
              <!-- <c-v-col
                cols="auto"
                class="border-s-thin pa-0 pl-2 d-flex align-center"
              >
                <base-mo00009 :oneway-model-value="defaultOtherOneWay.mo00009" />
              </c-v-col> -->
            </c-v-row>
          </c-v-col>
          <!-- ① -->
          <c-v-col
            cols="12"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-1 d-flex align-center"
          >
            <!-- 主たる傷病名 -->
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615PrincipalDiagnosis1" />
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              class="pl-1"
              @click="openOr51775(t('label.principal-diagnosis-circle-1'))"
            />
            <base-mo00045
              v-model="refValue!.nyuin1Knj"
              :oneway-model-value="otherOneWay.mo00045PrincipalDiagnosis"
              class="w-100"
            />
            <base-mo00615
              :oneway-model-value="otherOneWay.mo00615Period"
              class="px-2"
            />
            <!-- 期間 -->
            <!-- <base-mo00009 :oneway-model-value="defaultOtherOneWay.mo00009" /> -->
            <base-mo00020
              v-model="refValue!.nyuin1Symd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00615
              :oneway-model-value="otherOneWay.mo00615Wavy"
              class="px-2"
            />
            <base-mo00020
              v-model="refValue!.nyuin1Eymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
          </c-v-col>
          <!-- ② -->
          <c-v-col
            cols="12"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-1 d-flex align-center"
          >
            <!-- 主たる傷病名 -->
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615PrincipalDiagnosis2" />
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              class="pl-1"
              @click="openOr51775(t('label.principal-diagnosis-circle-2'))"
            />
            <base-mo00045
              v-model="refValue!.nyuin2Knj"
              :oneway-model-value="otherOneWay.mo00045PrincipalDiagnosis"
              class="w-100"
            />
            <base-mo00615
              :oneway-model-value="otherOneWay.mo00615Period"
              class="px-2"
            />
            <!-- 期間 -->
            <!-- <base-mo00009 :oneway-model-value="defaultOtherOneWay.mo00009" /> -->
            <base-mo00020
              v-model="refValue!.nyuin2Symd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00615
              :oneway-model-value="otherOneWay.mo00615Wavy"
              class="px-2"
            />
            <base-mo00020
              v-model="refValue!.nyuin2Eymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
          </c-v-col>
          <!-- ③ -->
          <c-v-col
            cols="12"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-1 d-flex align-center"
          >
            <!-- 主たる傷病名 -->
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615PrincipalDiagnosis3" />
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              class="pl-1"
              @click="openOr51775(t('label.principal-diagnosis-circle-3'))"
            />
            <base-mo00045
              v-model="refValue!.nyuin3Knj"
              :oneway-model-value="otherOneWay.mo00045PrincipalDiagnosis"
              class="w-100"
            />
            <base-mo00615
              :oneway-model-value="otherOneWay.mo00615Period"
              class="px-2"
            />
            <!-- 期間 -->
            <!-- <base-mo00009 :oneway-model-value="defaultOtherOneWay.mo00009" /> -->
            <base-mo00020
              v-model="refValue!.nyuin3Symd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00615
              :oneway-model-value="otherOneWay.mo00615Wavy"
              class="px-2"
            />
            <base-mo00020
              v-model="refValue!.nyuin3Eymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
          </c-v-col>
          <!-- ④ -->
          <c-v-col
            cols="12"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-1 d-flex align-center"
          >
            <!-- 主たる傷病名 -->
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615PrincipalDiagnosis4" />
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              class="pl-1"
              @click="openOr51775(t('label.principal-diagnosis-circle-4'))"
            />
            <base-mo00045
              v-model="refValue!.nyuin4Knj"
              :oneway-model-value="otherOneWay.mo00045PrincipalDiagnosis"
              class="w-100"
            />
            <base-mo00615
              :oneway-model-value="otherOneWay.mo00615Period"
              class="px-2"
            />
            <!-- 期間 -->
            <!-- <base-mo00009 :oneway-model-value="defaultOtherOneWay.mo00009" /> -->
            <base-mo00020
              v-model="refValue!.nyuin4Symd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00615
              :oneway-model-value="otherOneWay.mo00615Wavy"
              class="px-2"
            />
            <base-mo00020
              v-model="refValue!.nyuin4Eymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
          </c-v-col>
          <!-- ⑤ -->
          <c-v-col
            cols="12"
            class="bordered bg-white border-e-0 border-b-0 pa-2 py-1 d-flex align-center"
          >
            <!-- 主たる傷病名 -->
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615PrincipalDiagnosis5" />
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              class="pl-1"
              @click="openOr51775(t('label.principal-diagnosis-circle-5'))"
            />
            <base-mo00045
              v-model="refValue!.nyuin5Knj"
              :oneway-model-value="otherOneWay.mo00045PrincipalDiagnosis"
              class="w-100"
            />
            <base-mo00615
              :oneway-model-value="otherOneWay.mo00615Period"
              class="px-2"
            />
            <!-- 期間 -->
            <!-- <base-mo00009 :oneway-model-value="defaultOtherOneWay.mo00009" /> -->
            <base-mo00020
              v-model="refValue!.nyuin5Symd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
            <base-mo00615
              :oneway-model-value="otherOneWay.mo00615Wavy"
              class="px-2"
            />
            <base-mo00020
              v-model="refValue!.nyuin5Eymd"
              :oneway-model-value="defaultOtherOneWay.mo00020"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <c-v-col
        cols="4"
        class="bordered bg-white border-b-0 pa-2 d-flex flex-column"
      >
        <c-v-row class="flex-grow-0">
          <!-- 感染症・アレルギー情報 -->
          <c-v-col class="pa-0 d-flex align-center">
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615InfectiousDisease" />
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              @click="openOr51775(t('label.infectious-disease-allergy-info'))"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <c-v-col
            cols="12"
            class="pa-0 d-flex align-center"
          >
            <base-mo00046
              v-model="refValue!.kansenKnj"
              :oneway-model-value="otherOneWay.mo00046InfectiousDisease"
              class="w-100"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <c-v-col
        cols="12"
        class="bordered bg-white pa-2 d-flex flex-column"
      >
        <c-v-row class="flex-grow-0">
          <!-- 嘱託医 医療機関等の名称・連絡先 -->
          <c-v-col class="pa-0 d-flex align-center">
            <base-mo00615 :oneway-model-value="otherOneWay.mo00615NameAndContactInfo" />
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              @click="openOr51775(t('label.name-and-contact-information-of-physician-institution'))"
            />
            <!-- 課題 #139106 中期開発時点では常に非表示 -->
            <base-mo00615
              :oneway-model-value="otherOneWay.mo00615PastMedical"
              class="pl-2"
            />
            <!-- <base-mo00009 :oneway-model-value="defaultOtherOneWay.mo00009" /> -->
            <base-mo00615
              :oneway-model-value="otherOneWay.mo00615Hospital"
              class="pl-2"
            />
            <base-mo00009
              :oneway-model-value="defaultOtherOneWay.mo00009"
              @click="openOr29520"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <c-v-col
            cols="12"
            class="pa-0 pt-2 d-flex align-center"
          >
            <base-mo00046
              v-model="refValue!.zokutakuiKnj"
              :oneway-model-value="otherOneWay.mo00046NameAndContactInfo"
              class="w-100"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-container>
  <!-- Or51775: GUI00937 入力支援［ケアマネ］ -->
  <g-custom-or51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="other.or51775"
    :oneway-model-value="otherOneWay.or51775"
    @confirm="handleOr51775Confirm"
  />
  <!-- Or28333: GUI00641_［保険選択］画面 -->
  <g-custom-or28333
    v-if="showDialogOr28333"
    v-bind="or28333"
    v-model="other.or28333"
    :oneway-model-value="otherOneWay.or28333"
  />
  <!-- Or27017: GUI00640_［定期薬剤情報］画面 -->
  <g-custom-or27017
    v-if="showDialogOr27017"
    v-bind="or27017"
    v-model="other.or27017"
    :oneway-model-value="otherOneWay.or27017"
    @update:model-value="handleOr27017Confirm"
  />
  <!-- Or29520: GUI00643_［医師選択］画面 -->
  <g-custom-or29520
    v-if="showDialogOr29520"
    v-bind="or29520"
    v-model="other.or29520"
  />
</template>

<style lang="scss" scoped>
@use '@/styles/base.scss';
.container {
  min-width: 1200px;
  overflow-x: auto;
  background-color: rgb(var(--v-theme-background));

  :deep(.v-row) {
    margin: 0px;
  }
}

.divider {
  height: 8px;
  background-color: rgb(var(--v-theme-secondaryBackground));
}

.bordered {
  border: 1px rgb(var(--v-theme-form)) solid;
}
</style>
