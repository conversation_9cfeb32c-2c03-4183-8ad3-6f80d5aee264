<script setup lang="ts">
/**
 * GUI01259_支援経過確認一覧
 *
 * @description
 * 支援経過確認一覧
 *
 * <AUTHOR> DAO DINH DUONG
 */
import _ from 'lodash'
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or11017Const } from '~/components/custom-components/organisms/Or11017/Or11017.constants'
import type { Or11017OnewayType } from '~/types/cmn/business/components/Or11017Type'
import { Or11017Logic } from '~/components/custom-components/organisms/Or11017/Or11017.logic'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP支援経過確認一覧
 * KMD DAO DINH DUONG 2025/06/06 ADD START
 **************************************************/
/**
 *画面ID
 */
const screenId = 'GUI01259'
/**
 *ルーティング
 */
const routing = 'GUI01259/pinia'
/**
 *画面物理名
 */
const screenName = 'GUI01259'

/**
 *画面状態管理用操作変数
 */
const screenStore = useScreenStore()

/**
 *Or11017のID
 */
const or11017 = ref<Or11017OnewayType>({ uniqueCpId: '',  ym: '2024/12', userid: '**********', svJigyoId: '1' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01259' },
})

/**************************************************
 * Props
 **************************************************/
/**
 *piniaから最上位の画面コンポーネント情報を取得する
 *これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
 */
const pageComponent = screenStore.screen().supplement.pageComponent
// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01259',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or11017Const.CP_ID(1) }],
})
// 自身のPinia領域をセットアップ
// コンポーネントの初期化処理を開始する
// Or11017Logic.initialize(or11017.value.uniqueCpId)

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or11017Const.CP_ID(1)]: or11017.value,
})
Or11017Logic.initialize(or11017.value.uniqueCpId)
/**
 *ダイアログ表示フラグ
 */
const showDialogOr11017CksFlg1 = computed(() => {
  // Or11017 cks_flg=1 のダイアログ開閉状態
  return Or11017Logic.state.get(or11017.value.uniqueCpId)?.isOpen ?? false
})
/**
 *  ボタン押下時の処理(Or11017)
 *
 */
function onClickOr11017() {
  // Or11017 ダイアログ開閉状態を更新する
  Or11017Logic.state.set({
    uniqueCpId: or11017.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- GUI01259_支援経過確認一覧 DAO DINH DUONG 2025/06/06 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr11017"
      >
        GUI01259_課題整理総括設定マスタ
      </v-btn>
      <g-custom-or-11017
        v-if="showDialogOr11017CksFlg1"
        v-bind="or11017"
      />
    </c-v-col>
  </c-v-row>
  <!-- GUI01259_支援経過確認一覧 DAO DINH DUONG 2025/06/17 ADD END-->
</template>
