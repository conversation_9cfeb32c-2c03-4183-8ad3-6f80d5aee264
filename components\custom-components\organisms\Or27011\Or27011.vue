<script setup lang="ts">
/**
 * GUI01261_照会内容取込
 *
 * @description
 * 照会内容取込
 *
 * <AUTHOR> DAO DINH DUONG
 */
import { reactive, ref, watch, computed, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27011Const } from './Or27011.constants'
import type { Or27011StateType, Rirek, Kikan, Naiyo, ParamComfirm } from './Or27011.type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import { v4 as uuidv4 } from 'uuid'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00046OnewayType, Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Or27011Type } from '~/types/cmn/business/components/Or27011Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type {
  inquiryContentsImportInfoSelectInEntity,
  inquiryContentsImportInfoSelectOutEntity,
  RiyuItem,
} from '~/repositories/cmn/entities/inquiryContentsImportInfoSelectEntity'
import type {
  inquiryContentsImportHistoryListSelectInEntity,
  inquiryContentsImportHistoryListSelectOutEntity,
} from '~/repositories/cmn/entities/inquiryContentsImportHistoryListSelectEntity'
import type {
  inquiryContentsImportHistoryDetailsListSelectInEntity,
  inquiryContentsImportHistoryDetailsListSelectOutEntity,
} from '~/repositories/cmn/entities/inquiryContentsImportHistoryDetailsListSelectEntity'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import { cloneDeep } from 'lodash'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

/**
 * 国際化関数の取得
 */
const { t } = useI18n()

interface Props {
  uniqueCpId: string
  modelValue: Or27011Type
}

/**
 * 親からのプロパティ
 */
const props = withDefaults(defineProps<Props>(), {})
const emit = defineEmits(['update:modelValue'])
const systemCommonsStore = useSystemCommonsStore()

/**
 * Mo00024の状態
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27011Const.DEFAULT.IS_OPEN,
})
/**
 * 修正ポップアップ（Or21814）用のユニークCP ID
 */
const or21814 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })
// ダイアログ表示フラグ
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value, // 確認ダイアログ
  [Or51775Const.CP_ID(0)]: or51775.value, // 確認ダイアログ
})
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
/**
 * OneWayバインドの状態管理
 */
const { setState } = useScreenOneWayBind<Or27011StateType>({
  cpId: Or27011Const.CP_ID(0), // CP IDを取得
  uniqueCpId: props.uniqueCpId, // ユニークなCP ID
  onUpdate: {
    /**
     * ダイアログ表示状態を更新
     *
     * @param value - 表示フラグ
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or27011Const.DEFAULT.IS_OPEN // isOpenの状態を更新
    },
  },
})
/**
 * OneWayバインド用のローカル状態
 */
const localOneway = reactive({
  mo00018Oneway: {
    showItemLabel: false,
  } as Mo00018OnewayType,
  // 認定情報入力ダイアログの設定
  mo00611OverWriteBtnOneway: {
    tooltipText: t('tooltip.overwrite-confirm'),
    btnLabel: t('btn.overwrite'),
    width: '90px',
  } as Mo00611OnewayType,
  mo00611AddBtnOneway: {
    tooltipText: t('tooltip.add-item'),
    btnLabel: t('btn.add'),
    width: '90px',
  } as Mo00611OnewayType,
  memoInputIconBtn: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  mo00046: {
    isVerticalLabel: true,
    showItemLabel: false,
    hideDetails: true,
    noResize: true,
    rows: 4,
    maxRows: '4',
    maxlength: '4000',
  } as Mo00046OnewayType,
  mo00024Oneway: {
    persistent: true,
    showCloseBtn: true,
    width: 'fit-content',
    mo01344Oneway: {
      name: 'Or27011',
      toolbarName: 'Or27011ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      toolbarTitle: t('label.import-inquiry'),
    },
  } as Mo00024OnewayType,
  mo00611CloseBtnOneWay: {
    tooltipText: t('tooltip.screen-close'),
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    tooltipText: t('tooltip.confirm-btn'),
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  or51775Oneway: {
    title: t('label.content'),
    screenId: 'GUI01261',
    bunruiId: '2',
    t1Cd: '2700',
    t2Cd: '1',
    t3Cd: '0',
    tableName: 'cpn_tuc_syp_keika',
    columnName: 'memo_knj',
    assessmentMethod: '共通情報.アセスメント方式',
    inputContents: '',
    userId: systemCommonsStore.getUserId,
    mode: '',
  } as Or51775OnewayType,
})
const kmk1 = ref<Mo00018Type>({ modelValue: false })
const kmk2 = ref<Mo00018Type>({ modelValue: false })
const kmk3 = ref<Mo00018Type>({ modelValue: false })
const kmk4 = ref<Mo00018Type>({ modelValue: false })
const kmk5 = ref<Mo00018Type>({ modelValue: false })
const kmk6 = ref<Mo00018Type>({ modelValue: false })
const kmk7 = ref<Mo00018Type>({ modelValue: false })
const openPopupComfirm = (param: ParamComfirm) => {
  return new Promise((resolve) => {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        isOpen: true,
        dialogText: param.message,
        dialogTitle: t('label.confirm'),
        firstBtnType: 'normal1',
        firstBtnLabel: param?.firstBtnLabel || t('btn.yes'),
        secondBtnType: param?.secondBtnType || 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnLabel: t('btn.cancel'),
        thirdBtnType: param?.thirdBtnType || 'blank',
      },
    })
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        if (event?.firstBtnClickFlg) {
          if (param.excuteFunction) {
            void param.excuteFunction()
          }
          resolve(true)
        }
        if (event?.secondBtnClickFlg) {
          if (param.excuteFunction1) {
            void param.excuteFunction1()
          }
        }
        if (event?.thirdBtnClickFlg) {
          if (param.excuteFunction2) {
            void param.excuteFunction2()
          }
        }
        resolve(false)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
      },
      { once: true }
    )
  })
}
function Or51775OnClick() {
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

const mappedDataHistoryList = (res: inquiryContentsImportHistoryListSelectOutEntity) => {
  RirekList.value = res.data.rirekiList.map((item) => ({ ...item, uniqueId: uuidv4() }))
  selectedRirekUniqueId.value = RirekList.value[0].uniqueId
  kmk1.value.modelValue = res.data.kmkList.kmk1 === '1'
  kmk2.value.modelValue = res.data.kmkList.kmk2 === '1'
  kmk3.value.modelValue = res.data.kmkList.kmk3 === '1'
  kmk4.value.modelValue = res.data.kmkList.kmk4 === '1'
  kmk5.value.modelValue = res.data.kmkList.kmk5 === '1'
  kmk6.value.modelValue = res.data.kmkList.kmk6 === '1'
  kmk7.value.modelValue = res.data.kmkList.kmk7 === '1'
  mappedDataHistoryListDetail(res)
}
const mappedDataHistoryListDetail = (
  res: inquiryContentsImportHistoryDetailsListSelectOutEntity
) => {
  RiyuList.value = res.data.riyuList
  NaiyoList.value = res.data.naiyoList.map((item) => ({ ...item, uniqueId: uuidv4() }))
  selectedNaiyoUniqueid.value = NaiyoList.value[0].uniqueId
}
async function getHistoryList(sc1Id: string) {
  const paramGetList: inquiryContentsImportHistoryListSelectInEntity = {
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    sc1Id,
  }
  const res: inquiryContentsImportHistoryListSelectOutEntity = await ScreenRepository.select(
    'inquiryContentsImportHistoryListSelect',
    paramGetList
  )
  mappedDataHistoryList(res)
}
async function getHistoryListDetail(sc1Id: string, shoukaiId: string) {
  const paramGetList: inquiryContentsImportHistoryDetailsListSelectInEntity = {
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    sc1Id,
    shoukaiId,
  }
  const res: inquiryContentsImportHistoryDetailsListSelectOutEntity = await ScreenRepository.select(
    'inquiryContentsImportHistoryDetailsListSelect',
    paramGetList
  )
  mappedDataHistoryListDetail(res)
}
const kikanFlag = ref(false)
/**
 * ローカル状態管理用オブジェクト
 */
async function init() {
  const paramGetList: inquiryContentsImportInfoSelectInEntity = {
    userId: systemCommonsStore.getUserId ?? '',
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
  }
  const res: inquiryContentsImportInfoSelectOutEntity = await ScreenRepository.select(
    'inquiryContentsImportInfoSelect',
    paramGetList
  )
  KikanList.value = res.data.kghTucKrkKikanList.map((item) => ({
    ...item,
    uniqueId: uuidv4(),
  }))
  selectedKikan.value = KikanList.value[0].uniqueId
  kikanFlag.value = res.data.kikanFlg
  mappedDataHistoryList(res)
  watch(selectedKikan, () => {
    getHistoryList(
      kikanFlag.value
        ? (KikanList.value.find((item) => item.uniqueId === selectedKikan.value)?.sc1Id ?? '')
        : '0'
    )
  })
  watch(selectedRirekUniqueId, () => {
    getHistoryListDetail(
      kikanFlag.value ? (selectedRirek.value?.sc1Id ?? '') : '0',
      selectedRirek.value?.shoukaiId ?? ''
    )
  })
}
const RirekList = ref<Rirek[]>([])
const KikanList = ref<Kikan[]>([])
const RiyuList = ref<RiyuItem[]>([])
const NaiyoList = ref<Naiyo[]>([])
onMounted(async () => {
  await init()
})

/**
 * ダイアログを閉じる処理（非同期）
 *
 * @returns Promise
 */
const close = async () => {
  setState({ isOpen: false })
}

/**
 * 保存処理
 *
 */
const onSave = () => {
  emit('update:modelValue', SI035.value)
  setState({ isOpen: false })
}
/**
 * mo00024のisOpen監視
 * 組織ダイアログの自動クローズを手動判定に変更
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    mo00024.value.isOpen = true
    if (!newValue) {
      void close()
    }
  }
)

const headersKikanList = ref([
  {
    title: t('label.plan-period'),
    key: 'startEndYmdPeriod',
    width: '206px',
    sortable: false,
  },
  {
    title: t('label.within-the-period-number-of-history'),
    key: 'dmyCntPeriod',
    width: '105px',
    sortable: false,
  },
])
const headersRirekList = ref([
  {
    title: t('label.create-date'),
    key: 'dateYmd',
    width: '100px',
    sortable: false,
  },
  {
    title: t('label.author'),
    key: 'shokuId',
    width: '180px',
    sortable: false,
  },
])
const headersNaiyoList = ref([
  {
    title: t('label.reference-request-destination'),
    key: 'ssakiKnj',
    width: '140px',
    sortable: false,
  },
  {
    title: t('label.reference-request-date'),
    key: 'sYmd',
    width: '140px',
    sortable: false,
  },
  {
    title: t('label.inquiry-request-detail'),
    key: 'snaiyouKnj',
    width: '230px',
    sortable: false,
  },
  {
    title: t('label.respondent-name'),
    key: 'ksimeiKnj',
    width: '109px',
    sortable: false,
  },
  {
    title: t('label.answer-date'),
    key: 'kYmd',
    width: '119px',
    sortable: false,
  },
  {
    title: t('label.answer-content'),
    key: 'knaiyouKnj',
    width: '230px',
    sortable: false,
  },
])
/**
 * 選択中の項目
 */
const selectedNaiyoUniqueid = ref<string>()
/**
 * 行選択処理
 *
 * @param item - 対象の困難度
 */
function selectRowNaiyo(item: Naiyo) {
  selectedNaiyoUniqueid.value = item.uniqueId
}
/**
 * 選択行かどうか判定
 *
 * @param item - 対象の行
 *
 * @returns true: 選択中, false: 非選択
 */
const isselectedNaiyoUniqueid = (item: Naiyo) => selectedNaiyoUniqueid.value === item.uniqueId
/**
 * 選択中の項目
 */
const selectedKikan = ref<string>()
/**
 * 行選択処理
 *
 * @param item - 対象の困難度
 */
function selectRowKikan(item: Kikan) {
  selectedKikan.value = item.uniqueId
}
/**
 * 選択行かどうか判定
 *
 * @param item - 対象の行
 *
 * @returns true: 選択中, false: 非選択
 */
const isSelectedKikan = (item: Kikan) => selectedKikan.value === item.uniqueId
/**
 * 選択中の項目
 */
const selectedRirekUniqueId = ref<string>()
/**
 * 行選択処理
 *
 * @param item - 対象の困難度
 */
function selectRowRirek(item: Rirek) {
  selectedRirekUniqueId.value = item.uniqueId
}
/**
 * 選択行かどうか判定
 *
 * @param item - 対象の行
 *
 * @returns true: 選択中, false: 非選択
 */
const isselectedRirekUniqueId = (item: Rirek) => selectedRirekUniqueId.value === item.uniqueId
const selectedRirek = computed(() =>
  RirekList.value.find((item) => item.uniqueId === selectedRirekUniqueId.value)
)
const currentRiyuKnj = computed(
  () =>
    RiyuList.value.find((item) => item.shoukaiId === selectedRirek.value?.shoukaiId)?.riyuKnj ??
    null
)

const SI030 = ref<Mo00018Type>({ modelValue: true })
const SI035 = ref<Mo00046Type>(cloneDeep(props.modelValue))
const selectedNaiyo = computed(() =>
  NaiyoList.value.find((item) => item.uniqueId === selectedNaiyoUniqueid.value)
)
const kikanText = computed(() => {
  let txt = ''
  if (kmk1.value.modelValue && currentRiyuKnj.value) {
    txt += `${t('【理由】')} ${currentRiyuKnj.value}`
    txt += SI030.value.modelValue ? '\r\n' : '、'
  }
  if (kmk2.value.modelValue && selectedNaiyo.value?.ssakiKnj) {
    txt += `${t('【照会先】')} ${selectedNaiyo.value.ssakiKnj}`
    txt += SI030.value.modelValue ? '\r\n' : '、'
  }
  if (kmk3.value.modelValue && selectedNaiyo.value?.sYmd) {
    txt += `${t('【照会年月日】')} ${selectedNaiyo.value.sYmd}`
    txt += SI030.value.modelValue ? '\r\n' : '、'
  }
  if (kmk4.value.modelValue && selectedNaiyo.value?.snaiyouKnj) {
    txt += `${t('【照会内容】')} ${selectedNaiyo.value?.snaiyouKnj}`
    txt += SI030.value.modelValue ? '\r\n' : '、'
  }
  if (kmk5.value.modelValue && selectedNaiyo.value?.ksimeiKnj) {
    txt += `${t('【回答者氏名】')} ${selectedNaiyo.value?.ksimeiKnj}`
    txt += SI030.value.modelValue ? '\r\n' : '、'
  }
  if (kmk6.value.modelValue && selectedNaiyo.value?.kYmd) {
    txt += `${t('【回答年月日】')} ${selectedNaiyo.value?.kYmd}`
    txt += SI030.value.modelValue ? '\r\n' : '、'
  }
  if (kmk7.value.modelValue && selectedNaiyo.value?.knaiyouKnj) {
    txt += `${t('【回答内容】')} ${selectedNaiyo.value?.knaiyouKnj}`
    txt += SI030.value.modelValue ? '\r\n' : '、'
  }
  return txt
})
const overWriteText = () => {
  const overWrite = () => {
    SI035.value.value = kikanText.value
  }
  if (SI035.value.value) {
    const param: ParamComfirm = {
      message: t('message.i-cmn-10218'),
      excuteFunction: overWrite,
    }
    openPopupComfirm(param)
  } else {
    overWrite()
  }
}
const addText = () => {
  SI035.value.value += kikanText.value
}
const handleSetText = ({ value, type }: { value: string; type: string }) => {
  if (type === '1') {
    SI035.value.value = value
  } else {
    SI035.value.value += value
  }
}
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="{
      ...localOneway.mo00024Oneway,
      mo01344Oneway: { ...localOneway.mo00024Oneway.mo01344Oneway },
    }"
  >
    <template #cardItem>
      <div class="content-container">
        <c-v-row no-gutters>
          <c-v-col
            v-if="kikanFlag"
            class="pr-2"
            cols="auto"
          >
            <c-v-data-table
              fixed-header
              :headers="headersKikanList"
              class="table-wrapper overflow-y-auto elevation-1 table-container-1 table-1"
              :items-per-page="-1"
              :items="KikanList"
              hide-default-footer
              hover
            >
              <template #item="{ item }">
                <tr
                  :class="{ 'select-row': isSelectedKikan(item) }"
                  @click="selectRowKikan(item)"
                >
                  <td>
                    {{ item.startEndYmdPeriod }}
                  </td>
                  <td style="text-align: right">
                    {{ item.dmyCntPeriod }}
                  </td>
                </tr>
              </template>
            </c-v-data-table>
          </c-v-col>
          <c-v-col
            class="pl-2"
            cols="auto"
          >
            <c-v-data-table
              fixed-header
              :headers="headersRirekList"
              class="table-wrapper overflow-y-auto elevation-1 table-container-1"
              :items-per-page="-1"
              :items="RirekList"
              hide-default-footer
              hover
            >
              <template #item="{ item }">
                <tr
                  :class="{ 'select-row': isselectedRirekUniqueId(item) }"
                  @click="selectRowRirek(item)"
                >
                  <td>{{ item.dateYmd }}</td>
                  <td>{{ item.shokuId }}</td>
                </tr>
              </template>
            </c-v-data-table>
          </c-v-col>
        </c-v-row>
        <c-v-row
          class="mt-3"
          no-gutters
          v-if="currentRiyuKnj !== null"
        >
          <c-v-col
            class="p-5 reason-no-meeting"
            style="width: 308px"
            cols="auto"
          >
            {{ t('label.reason-no-meeting-or-absence') }}</c-v-col
          >
          <div
            style="width: 625px"
            class="bordered reason-no-meeting-content"
          >
            {{ currentRiyuKnj }}
          </div>
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-data-table
            fixed-header
            :headers="headersNaiyoList"
            class="table-wrapper mt-3 overflow-y-auto elevation-1 table-container-2"
            :items-per-page="-1"
            height="260px"
            :items="NaiyoList"
            hide-default-footer
            hover
          >
            <template #item="{ item }">
              <tr
                :class="[{ 'select-row': isselectedNaiyoUniqueid(item) }, 'naiyo-row']"
                @click="selectRowNaiyo(item)"
              >
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.ssakiKnj,
                    }"
                  />
                </td>
                <td>
                  <base-mo01335
                    :oneway-model-value="{
                      value: item.sYmd,
                    }"
                  />
                </td>
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.snaiyouKnj,
                    }"
                  />
                </td>
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.ksimeiKnj,
                    }"
                  />
                </td>
                <td>
                  <base-mo01335
                    :oneway-model-value="{
                      value: item.kYmd,
                    }"
                  />
                </td>
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.knaiyouKnj,
                    }"
                  />
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-row>
        <c-v-row
          class="mt-3"
          no-gutters
        >
          <c-v-col
            class="p-5 reason-no-meeting"
            cols="auto"
          >
            {{ t('label.import-item-1') }}</c-v-col
          >
          <c-v-col
            class="bordered reason-no-meeting-content"
            cols="auto"
          >
            <div class="flex-container">
              <base-mo00018
                v-model="kmk1"
                :oneway-model-value="{
                  ...localOneway.mo00018Oneway,
                  checkboxLabel: t('label.reason'),
                }"
              />
              <base-mo00018
                v-model="kmk2"
                :oneway-model-value="{
                  ...localOneway.mo00018Oneway,
                  checkboxLabel: t('label.reference-destination'),
                }"
              />
              <base-mo00018
                v-model="kmk3"
                :oneway-model-value="{
                  ...localOneway.mo00018Oneway,
                  checkboxLabel: t('label.inquiry-date'),
                }"
              />
              <base-mo00018
                v-model="kmk4"
                :oneway-model-value="{
                  ...localOneway.mo00018Oneway,
                  checkboxLabel: t('label.reference-content'),
                }"
              />
              <base-mo00018
                v-model="kmk5"
                :oneway-model-value="{
                  ...localOneway.mo00018Oneway,
                  checkboxLabel: t('label.responder'),
                }"
              />
              <base-mo00018
                v-model="kmk6"
                :oneway-model-value="{
                  ...localOneway.mo00018Oneway,
                  checkboxLabel: t('label.answer-date'),
                }"
              />
              <base-mo00018
                v-model="kmk7"
                :oneway-model-value="{
                  ...localOneway.mo00018Oneway,
                  checkboxLabel: t('label.answer-content'),
                }"
              />
            </div>
          </c-v-col>
        </c-v-row>
        <c-v-row
          class="mt-3"
          no-gutters
        >
          <base-mo00018
            v-model="SI030"
            :oneway-model-value="{
              ...localOneway.mo00018Oneway,
              checkboxLabel: t('label.line-break'),
            }"
          />
          <base-mo00611
            @click="overWriteText"
            :oneway-model-value="localOneway.mo00611OverWriteBtnOneway"
            class="mx-1"
            append-icon="arrow_downward"
          />
          <base-mo00611
            @click="addText"
            :oneway-model-value="localOneway.mo00611AddBtnOneway"
            class="mx-1"
            append-icon="arrow_downward"
          />
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col cols="6">
            <c-v-row
              no-gutters
              justify="space-between"
              align="center"
              class="mt-3"
            >
              <c-v-col cols="auto">
                <span class="text-title"> {{ t('label.content') }} </span>
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00009
                  @click="Or51775OnClick"
                  :oneway-model-value="localOneway.memoInputIconBtn"
                />
              </c-v-col>
            </c-v-row>
            <base-mo00046
              v-model="SI035"
              :oneway-model-value="localOneway.mo00046"
              class="text-area w-100"
              v-bind="{ ...$attrs }"
            />
          </c-v-col>
        </c-v-row>
      </div>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          :oneway-model-value="{
            ...localOneway.mo00609ConfirmOneway,
          }"
          class="ml-2"
          @click="onSave"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21814 v-bind="or21814" />
  <g-custom-or-51775
    v-if="showDialogOr51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="handleSetText"
    v-bind="or51775"
  />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/page-data-table-list.scss';
.content-container {
  height: 750px;
}
.flex-container {
  display: flex;
  align-items: center;
}
:deep(.table-container-1) {
  max-height: 135px;
  overflow-y: auto;
}
:deep(.table-1) {
  th:nth-child(2) {
    span {
      font-size: 11px;
    }
  }
}
:deep(.table-container-2) {
  table {
    table-layout: fixed;
    width: 968px;
  }
    th:nth-child(2) {
    span {
      font-size: 11px;
    }
  }
  overflow-y: auto;
  .naiyo-row {
    height: 112px;
  }
}
.reason-no-meeting {
  display: flex;
  align-items: center;
  background-color: rgb(var(--v-theme-black-100));
  padding: 8px !important;
  font-size: 14px;
  font-weight: bold;
}
.reason-no-meeting-content {
  padding: 8px !important;
  font-size: 14px;
}
.bordered {
  border: 1px solid rgb(var(--v-theme-black-100));
}
</style>
