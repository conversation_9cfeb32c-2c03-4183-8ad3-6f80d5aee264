import { getSequencedCpId } from '#imports'

/**
 * OrX0157：有機体：入力支援付きテキストフィールド
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace OrX0157Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('OrX0155', seq)

  /**
   * 初期値
   */
  export namespace DEFAULT {}
  /**
   * "テキストフィールド入力モード"
   */
  export namespace INPUT_MODE {
    /**
     * 半角文字専用
     */
    export const HALF_WIDTH_CHARS_ONLY = 'HalfWidthCharsOnly'
    /**
     * 数値専用
     */
    export const NUMERIC_ONLY = 'NumericOnly'
    /**
     * テキストフィールド
     */
    export const TEXT_ONLY = 'TextOnly'
  }
}
