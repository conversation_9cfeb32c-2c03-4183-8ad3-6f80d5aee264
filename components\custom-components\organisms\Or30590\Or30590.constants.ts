import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or30590:有機体:印刷設定モーダル（画面/特殊コンポーネント）
 * GUI01085_印刷設定
 *
 * @description
 * 印刷設定の処理
 *
 * <AUTHOR> DO AI QUOC
 */
export namespace Or30590Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or30590', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * 単一
     */
    export const TANI = '0'
    /**
     * 複数
     */
    export const HUKUSUU = '1'
    /**
     * 期間管理フラグ
     */
    export const KIKAN_FLG_1 = '1'
    /**
     * 数値定数
     */
    export namespace NUMBER  {
      /**
       * ZERO
       */
      export const ZERO = 0
      /**
       * 一
       */
      export const ONE = 1
    }
    /**
     * 固定文字セット
     */
    export namespace STR {
      /**
       * 空文字
       */
      export const EMPTY = ''
      /**
       * ZERO
       */
      export const ZERO = '0'
      /**
       * ONE
       */
      export const ONE = '1'
      /**
       * TWO
       */
      export const TWO = '2'
      /**
       * true
       */
      export const TRUE = 'true'
      /**
       * 区切り文字-コロン
       */
      export const SPLIT_COLON = '：'
      /**
       * 区切り文字- チルダ
       */
      export const SPLIT_TILDE = ' ～ '
    }
    /**
     * 帳票ID: PDFダウンロード
     */
    export namespace PDF_DOWNLOAD_REPORT_ID {
      /**
       * 基本チェックリスト
       */
      export const BASIC_CHECKLIST_REPORT = 'basicChecklistReport'
      /**
       * 基本チェックリスト(項目なし)
       */
      export const BASIC_CHECKLIST_NO_ITEM_REPORT = 'basicChecklistNoItemReport'
    }
  }

  /**
   * 日付表示有無
   */
  export namespace DATE_DISPLAY {
    /**
     * 日付印刷区分:2
     */
    export const TWO = '2'
  }
}
