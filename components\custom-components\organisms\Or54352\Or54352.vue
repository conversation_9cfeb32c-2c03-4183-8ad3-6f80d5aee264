<script setup lang="ts">
/**
 * Or54352:有機体:フリーアセスメントフェースシート表示設定マスタ
 * GUI00898_フリーアセスメントフェースシート表示設定マスタ
 *
 * @description
 * フリーアセスメントフェースシート表示設定マスタ
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch } from 'vue'
import { Or54352Const } from './Or54352.constants'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type {
  BunruiList,
  FreeFaceList,
  Or54352OnewayType,
  Or54352Type,
} from '~/types/cmn/business/components/Or54352Type'

import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  Mo01354Headers,
  Mo01354OnewayType,
  Mo01354Items,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import type { Mo01332Type } from '~/types/business/components/Mo01332Type'
import type { ResizableGridBinding } from '~/plugins/resizableGrid.client'
import type { Mo01278OnewayType, Mo01278Type } from '~/types/business/components/Mo01278Type'
import type { Mo01360OnewayType, Mo01360Type } from '~/types/business/components/Mo01360Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { CustomClass } from '~/types/CustomClassType'
import type {
  FreeAssessmentSheetDisplaySettingsRowSelectedUpdateInEntity,
  FreeAssessmentSheetDisplaySettingsRowSelectedUpdateOutEntity,
  FreeAssessmentSheetDisplaySettingsUpdateInEntity,
  FreeAssessmentSheetDisplaySettingsUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentSheetDisplaySettingsTabChangedUpdateEntity'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or54352Type
  onewayModelValue: Or54352Type
  uniqueCpId: string
}
const props = defineProps<Props>()
const mo01354OldValue = ref<FreeFaceList>()
const getMo01354Bool = ref(false)
const isEdit = ref(false)
const isRightEdit = ref(false)
const mo00009Bool = ref(false)
/**
 * isPermissionRegist
 */
const isPermissionRegist = ref<boolean>(true)
/**************************************************
 * 変数定義
 **************************************************/

const defaultOnewayModelValue: Or54352OnewayType = {
  /**
   * 事業者ID
   */
  svJigyoId: '0',
  /**
   *タブID
   */
  tabId: '1',
}
//Or21814_有機体:確認ダイアログ
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(1) })
const localOneway = reactive({
  or54352: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // フリーアセスメントフェースシート表示設定情報情報データテーブルのヘッダー
  mo01354Oneway: {
    height: '520px',
    headers: [] as Mo01354Headers[],
    showPaginationBottomFlg: false,
    showPaginationTopFlg: false,
    density: 'default',
    columnMinWidth: {
      columnWidths: [],
    },
    rowProps(data: { item: { useFlg: Mo01360Type; id: string } }) {
      const selected = local.mo01354.values.selectedRowId === data.item?.id
      return {
        class: {
          'selected-row': selected,
          'hidden-color': data.item?.useFlg.value === '0' && !selected,
        },
      }
    },
  } as Mo01354OnewayType,
  // フリーアセスメントフェースシート表示設定情報情報データテーブルのヘッダー
  mo01354CategoryOneway: {
    height: '520px',
    columnMinWidth: {
      columnWidths: [60, 275, 200, 90],
    },
    headers: [
      // Id
      {
        title: t('label.id'), // ヘッダーに表示される名称
        key: 'item2Id',
        sortable: false,
      },
      // 分類名称
      {
        title: t('label.category-name'), // ヘッダーに表示される名称
        key: 'daibunruiKnj',
        sortable: false,
      },
      // 表示
      {
        title: t('label.display'), // ヘッダーに表示される名称
        key: 'useFlg',
        sortable: false,
      },
      // 表示順
      {
        title: t('label.item-sort'), // ヘッダーに表示される名称
        key: 'sort',
        sortable: false,
      },
    ] as unknown as Mo01354Headers[],
    showPaginationBottomFlg: false,
    showPaginationTopFlg: false,
    density: 'default',
    rowProps(data: { item: { useFlg: Mo01360Type; id: string } }) {
      const selected = local.mo01354Category.values.selectedRowId === data.item?.id
      return {
        class: {
          'selected-row': selected,
          'hidden-color': data.item?.useFlg.value === '0' && !selected,
        },
      }
    },
  } as Mo01354OnewayType,
  // タブ
  mo00043OneWay: {
    tabItems: [
      { id: '1', title: t('label.latest-information') },
      { id: '2', title: t('label.screen-startup') },
      {
        id: '5',
        title: t('label.print-set'),
      },
    ],
  } as Mo00043OnewayType,

  mo01360Oneway: {
    name: '',
  } as Mo01360OnewayType,
  mo00009_1: {
    btnIcon: 'chevron_left',
    name: 'specialNoteShouldSolutionIssuesBtn',
    density: 'compact',
    width: '46px',
    height: '46px',
  } as Mo00009OnewayType,
  mo00009_2: {
    btnIcon: 'chevron_right',
    name: 'specialNoteShouldSolutionIssuesBtn',
    density: 'compact',
    width: '46px',
    height: '46px',
  } as Mo00009OnewayType,
  /** 表示順input Oneway 固定様式 */
  mo01278Oneway: {
    max: 999,
    min: 1,
  } as Mo01278OnewayType,
  mo01338Oneway: {
    value: t('label.save-by-bussiness-unit'),
    customClass: { outerClass: 'flex-start' } as CustomClass,
  } as Mo01338OnewayType,
})

// codeListは、各項目の設定を保持するための配列です。
const codeList = ref<CodeType[]>([])

const defaultModelValue = {
  // フリーアセスメントフェースシート表示設定情報情報
  mo01354: {
    values: {
      selectedRowId: '0',
      selectedRowIds: [],
      items: [] as Mo01354Items[],
    },
  },
  mo01354Category: {
    values: {
      selectedRowId: '0',
      selectedRowIds: [],
      items: [] as Mo01354Items[],
    },
  },
}

const local = reactive({
  ...defaultModelValue,
  mo00043: { id: '1' } as Mo00043Type,
})

// フリーアセスメントフェースシート表示設定情報情報選択行データ設定
const historySelectedItem = ref<Mo01354Items | undefined>()
const categorySelectedItem = ref<Mo01354Items | undefined>()
/**************************************************
 * Emit
 **************************************************/
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  isPermissionRegist.value = await hasRegistAuth()
  // 各コードグループの初期化を行います。
  await initCodes([
    // 数値
    { key: 0, mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DISPLAY_RADIO },
  ])
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  local.mo01354.values.items = []
  getMo01354Bool.value = false
  isEdit.value = false
  isRightEdit.value = false
  // バックエンドAPIから初期情報取得
  const inputData: FreeAssessmentSheetDisplaySettingsRowSelectedUpdateInEntity = {
    ...localOneway.or54352,
    tabId: local.mo00043.id,
  }
  const resData: FreeAssessmentSheetDisplaySettingsRowSelectedUpdateOutEntity =
    await ScreenRepository.select('freeAssessmentSheetDisplaySettingsTabChangedUpdate', inputData)
  const arrPicture = [
    {
      koumokuId: '3',
      pictureName: t('label.family-record-diagram'),
    },
    {
      koumokuId: '4',
      pictureName: t('label.site-plan'),
    },
    {
      koumokuId: '16',
      pictureName: t('label.human-body-diagram'),
    },
  ]
  // データ情報設定
  local.mo01354.values.items = resData.data.itemList.map((item, index) => {
    return {
      id: index.toString(),
      ...item,
      useFlg: {
        value: item.useFlg,
      },
      pictureFlg: {
        values: [],
      },
      sort: {
        value: item.sort,
      },
      lineCnt: {
        value: item.lineCnt,
      },
      pictureName:
        arrPicture.find((itm) => itm.koumokuId === item.koumokuId)?.pictureName ?? item.pictureName,
    }
  })
  local.mo01354.values.selectedRowId = local.mo01354.values.items[0].id
}

/**
 * 指定されたコードグループのコードを初期化します。
 *
 * @param codeGroups - コードを取得およびフィルタリングするためのキーとmCdKbnIdを含むオブジェクトの配列。
 */
async function initCodes(codeGroups: { key: number; mCdKbnId: number }[]) {
  for (const group of codeGroups) {
    const selectCodeKbnList = [{ mCdKbnId: group.mCdKbnId }]
    await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
    codeList.value = CmnSystemCodeRepository.filter(group.mCdKbnId)
  }
}

/**
 * フリーアセスメントフェースシート表示設定情報選択行
 *
 * @param item - フリーアセスメントフェースシート表示設定情報選択行
 */
const clickHistorySelectRow = async (item: Mo01354Type) => {
  if (historySelectedItem.value?.id === item?.values.selectedRowId) {
    return
  }
  historySelectedItem.value = local.mo01354.values.items.find(
    (itm) => itm.id === item?.values.selectedRowId
  )
  if (isRightEdit.value) {
    await showDialog2()
  }
  if (getMo01354Bool.value) {
    isRightEdit.value = true
    return
  }
  if (Or54352Const.DEFAULT.subIds.includes(historySelectedItem.value?.item1Id as string)) {
    await getCategoryDataInfo()
  } else {
    isRightEdit.value = false
    // データ情報設定
    local.mo01354Category.values.items = []
  }
}

/**
 * フリーアセスメントフェースシート表示設定情報選択行
 *
 * @param item - フリーアセスメントフェースシート表示設定情報選択行
 */
const clickCategorySelectRow = (item: Mo01354Type) => {
  categorySelectedItem.value = local.mo01354Category.values.items.find(
    (itm) => itm.id === item?.values.selectedRowId
  )
}

const getCategoryDataInfo = async () => {
  local.mo01354Category.values.items = []
  // バックエンドAPIから分类情報取得
  const inputData: FreeAssessmentSheetDisplaySettingsRowSelectedUpdateInEntity = {
    ...localOneway.or54352,
    koumokuId: (historySelectedItem.value?.koumokuId as string) || '',
    item1Id: (historySelectedItem.value?.item1Id as string) || '',
  }
  const resData: FreeAssessmentSheetDisplaySettingsRowSelectedUpdateOutEntity =
    await ScreenRepository.select('freeAssessmentSheetDisplaySettingsRowSelectedUpdate', inputData)
  // データ情報設定
  local.mo01354Category.values.items = resData.data.bunruiList.map((item, index) => {
    return {
      id: index.toString(),
      ...item,
      useFlg: {
        value: item.useFlg,
      },
      sort: {
        value: item.sort,
      },
    }
  })
  local.mo01354Category.values.selectedRowId = local.mo01354Category.values.items[0].id
  isRightEdit.value = false
  getMo01354Bool.value = false
}

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = async () => {
  if (isEdit.value) {
    await save()
    return true
  } else {
    showOr21814MsgOneBtn(t('message.i-cmn-21800'))
  }
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = async () => {
  return await showDialog()
}

const save = async () => {
  // 変更ありの項目データを保存する
  const inputData: FreeAssessmentSheetDisplaySettingsUpdateInEntity = {
    tabId: local.mo00043.id,
    itemList: local.mo01354.values.items.map((item) => {
      return {
        ...item,
        useFlg: (item.useFlg as Mo01360Type).value,
        pictureFlg: (item.pictureFlg as Mo01332Type).values.length ? '1' : '0',
        sort: (item.sort as Mo01278Type).value,
        lineCnt: (item.lineCnt as Mo01278Type).value,
      }
    }) as FreeFaceList[],
    bunruiList: local.mo01354Category.values.items.map((item) => {
      return {
        ...item,
        useFlg: (item.useFlg as Mo01360Type).value,
        sort: (item.sort as Mo01278Type).value,
      }
    }) as BunruiList[],
  }
  const resData: FreeAssessmentSheetDisplaySettingsUpdateOutEntity = await ScreenRepository.update(
    'freeAssessmentSheetDisplaySettingsUpdate',
    inputData
  )
  await getInitDataInfo()
}

watch(
  () => local.mo00043.id,
  async (newValue) => {
    if (['1', '2'].includes(newValue)) {
      ;(localOneway.mo01354Oneway.columnMinWidth as ResizableGridBinding).columnWidths = [
        60, 275, 200, 90,
      ]
      localOneway.mo01354Oneway.headers = [
        // Id
        {
          title: t('label.id'), // ヘッダーに表示される名称
          key: 'item1Id',
          sortable: false,
        },
        // 項目名
        {
          title: t('label.item-nm'), // ヘッダーに表示される名称
          key: 'koumokuKnj',
          sortable: false,
        },
        // 表示
        {
          title: t('label.display'), // ヘッダーに表示される名称
          key: 'useFlg',
          sortable: false,
        },
        // 表示順
        {
          title: t('label.item-sort'), // ヘッダーに表示される名称
          key: 'sort',
          sortable: false,
        },
      ] as Mo01354Headers[]
    } else if (newValue === '5') {
      ;(localOneway.mo01354Oneway.columnMinWidth as ResizableGridBinding).columnWidths = [
        60, 273, 220, 90, 90, 90,
      ]
      localOneway.mo01354Oneway.headers = [
        // Id
        {
          title: t('label.id'), // ヘッダーに表示される名称
          key: 'item1Id',
          sortable: false,
        },
        // 項目名
        {
          title: t('label.item-nm'), // ヘッダーに表示される名称
          key: 'koumokuKnj',
          sortable: false,
        },
        // 表示
        {
          title: t('label.display'), // ヘッダーに表示される名称
          key: 'useFlg',
          sortable: false,
        },
        // 行数
        {
          title: t('label.line-count'), // ヘッダーに表示される名称
          key: 'lineCnt',
          sortable: false,
          background: '#ccc',
        },
        // 作図名
        {
          title: t('label.drawing-name'), // ヘッダーに表示される名称
          key: 'pictureFlg',
          sortable: false,
        },
        // 表示順
        {
          title: t('label.item-sort'), // ヘッダーに表示される名称
          key: 'sort',
          sortable: false,
        },
      ] as Mo01354Headers[]
    }

    // 初期情報取得
    await getInitDataInfo()
  },
  { immediate: true }
)

/**
 * テキストエリアフォーカス
 *
 * 'addition':"加算"
 * "subtraction":'減算'
 *
 * @param type - 加算の場合 | 減算の場合
 *
 * @param data - 集合
 */
async function setTextareaFocus(type: 'addition' | 'subtraction', data: Mo01354Type) {
  if (isRightEdit.value && !mo00009Bool.value) {
    await showDialog2('setTextareaFocus')
  }
  if (getMo01354Bool.value && !mo00009Bool.value) {
    isRightEdit.value = true
    return
  }
  const table = data.values.items
  const selectedRowId = data.values.selectedRowId
  // 長さをチェックする、0の場合は処理を中断する
  if (!table.length && !selectedRowId) return
  const index = table.findIndex((item) => item.id === selectedRowId)
  // 加算の場合
  if (type === 'addition') {
    if (index !== table.length - 1) {
      ;[table[index], table[index + 1]] = [table[index + 1], table[index]]
      ;[table[index].sort as string, table[index + 1].sort as string] = [
        table[index + 1].sort as string,
        table[index].sort as string,
      ]
    }
  }
  // 減算の場合
  if (type === 'subtraction') {
    if (index) {
      ;[table[index], table[index - 1]] = [table[index - 1], table[index]]
      ;[table[index].sort as string, table[index - 1].sort as string] = [
        table[index - 1].sort as string,
        table[index].sort as string,
      ]
    }
  }
  isEdit.value = true
}

const handleChange = (item: FreeFaceList, type = 'left') => {
  if (type === 'right') {
    isRightEdit.value = true
  }
  isEdit.value = true
}

/**
 * 共通処理の編集権限チェックを行う。
 *
 */
const showDialog = async () => {
  // 該当画面にデータが変更ありの場合
  if (isEdit.value) {
    if (isPermissionRegist.value) {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がない場合)
      // メッセージ内容：情報を保存する権限がありません。入力内容は破棄されますが、処理を続けますか？
      showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
    } else {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がある場合)
      // 他の項目を選択すると、変更内容は失われます。「改行」変更を保存しますか？
      showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
    }
    return new Promise((resolve) => {
      watch(
        () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
        async () => {
          const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
          // 編集権限あり
          // 'はい'
          if (event?.firstBtnClickFlg) {
            // 保存
            await save()
            resolve(true)
          } else if (event?.secondBtnClickFlg) {
            // 編集権限なし
            // 'はい'
            resolve(true)
          } else {
            resolve(false)
          }
          Or21814Logic.event.set({
            uniqueCpId: or21814.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              thirdBtnClickFlg: false,
            },
          })
        },
        { once: true }
      )
    })
  } else {
    return true
  }
}

// 画面入力データに変更がある場合
async function updateModelValue(newValue: Mo00043Type) {
  if (await showDialog()) {
    local.mo00043.id = newValue.id
  }
}

/**
 * 共通処理の編集権限チェックを行う。
 *
 * @param type - 処理
 */
const showDialog2 = async (type = 'click') => {
  // 該当画面にデータが変更ありの場合
  if (isRightEdit.value) {
    if (isPermissionRegist.value) {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がない場合)
      // メッセージ内容：情報を保存する権限がありません。入力内容は破棄されますが、処理を続けますか？
      showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
    } else {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がある場合)
      // 他の項目を選択すると、変更内容は失われます。「改行」変更を保存しますか？
      showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
    }
    return new Promise((resolve) => {
      watch(
        () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
        async () => {
          const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
          // 編集権限あり
          // 'はい'
          if (event?.firstBtnClickFlg) {
            // 保存
            await save()
            resolve(true)
          } else if (event?.secondBtnClickFlg) {
            await getCategoryDataInfo()
            resolve(false)
          } else {
            // 編集権限なし
            // 'はい'
            if (type === 'click') {
              local.mo01354.values.selectedRowId = mo01354OldValue.value?.id ?? ''
            }
            getMo01354Bool.value = true
            isRightEdit.value = false
            resolve(true)
          }
          Or21814Logic.event.set({
            uniqueCpId: or21814.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              thirdBtnClickFlg: false,
            },
          })
        },
        { once: true }
      )
    })
  } else {
    return true
  }
}

watch(
  () => historySelectedItem.value,
  (newValue, oldValue) => {
    mo01354OldValue.value = oldValue as FreeFaceList
  }
)

/**
 * 表示順設定 タブ変更
 *
 * @param tableItem - テーブルデータ
 */
function setSortOrder(tableItem: FreeFaceList) {
  // 全空白チェック
  const allBlankFlg = local.mo01354.values.items.every(
    (item) => (item.sort as Mo01278Type).value === ''
  )
  if (allBlankFlg) {
    ;(tableItem.sort as unknown as Mo01278Type).value = '1'
    isEdit.value = true
  } else {
    // 最大値を取得する
    let maxSeq = Math.max(
      ...local.mo01354.values.items
        .map((item) => parseInt((item.sort as Mo01278Type).value))
        .filter((sort) => !isNaN(sort))
    )
    // 最大値チェック
    if (maxSeq < 0) {
      maxSeq = 0
    }
    if ((tableItem.sort as unknown as Mo01278Type).value === '') {
      // 最大値に1を足して新しいseq.valueを設定
      ;(tableItem.sort as unknown as Mo01278Type).value = (maxSeq + 1).toString()
      isEdit.value = true
    }
  }
}

/**
 * 閉じるボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgThreeBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 閉じるボタン押下_保存権限がない場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgOneBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 閉じるボタン押下_保存権限がない場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

defineExpose({
  onConfirmBtn,
  onClickCloseBtn,
  showDialog,
  getInitDataInfo,
})
</script>

<template>
  <base-mo00043
    :model-value="local.mo00043"
    :oneway-model-value="localOneway.mo00043OneWay"
    @update:model-value="updateModelValue"
  ></base-mo00043>
  <c-v-window v-model="local.mo00043.id">
    <c-v-window-item value="1">
      <c-v-sheet
        v-show="local.mo00043.id === '1'"
        class="mx-2"
      >
        <c-v-row>
          <c-v-col class="d-flex justify-end">
            <base-mo00009
              :oneway-model-value="localOneway.mo00009_1"
              class="arrow-up"
              @click="
                setTextareaFocus('subtraction', mo00009Bool ? local.mo01354Category : local.mo01354)
              "
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009_2"
              class="arrow-down"
              @click="
                setTextareaFocus('addition', mo00009Bool ? local.mo01354Category : local.mo01354)
              "
            />
          </c-v-col>
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col class="table-header">
            <!-- フリーアセスメントフェースシート表示設定情報一覧 -->
            <base-mo01354
              v-model="local.mo01354"
              :oneway-model-value="localOneway.mo01354Oneway"
              hide-default-footer
              class="list-wrapper mt-2"
              @update:model-value="clickHistorySelectRow"
              @click="mo00009Bool = false"
            >
              <!-- Id -->
              <template #[`item.item1Id`]="{ item }">
                <p class="omitted-content flex-end">
                  {{ item.item1Id }}
                </p>
              </template>
              <!-- 表示順 -->
              <template #[`item.sort`]="{ item }">
                <c-v-col
                  class="d-flex align-center data-table-cell"
                  @click="setSortOrder(item)"
                >
                  <base-mo01278
                    v-model="item.sort"
                    :oneway-model-value="localOneway.mo01278Oneway"
                    @change="
                      () => {
                        handleChange(item)
                      }
                    "
                  />
                </c-v-col>
              </template>
              <!-- 項目名 -->
              <template #[`item.koumokuKnj`]="{ item }">
                <p class="omitted-content">
                  {{ item.koumokuKnj }}
                </p>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :text="item.koumokuKnj"
                ></c-v-tooltip>
              </template>
              <!-- 表示 -->
              <template #[`item.useFlg`]="{ item }">
                <base-mo01360
                  v-model="item.useFlg"
                  :oneway-model-value="localOneway.mo01360Oneway"
                  @click="
                    () => {
                      handleChange(item)
                    }
                  "
                >
                  <base-at-radio
                    v-for="(itm, index) in codeList"
                    :key="index"
                    :name="'radio-' + '-' + index"
                    :radio-label="itm.label"
                    :value="itm.value"
                  />
                </base-mo01360>
              </template>
            </base-mo01354>
          </c-v-col>
          <c-v-col
            v-show="local.mo01354Category.values.items.length"
            class="table-header"
            @click="mo00009Bool = true"
          >
            <!-- フリーアセスメントフェースシート表示設定情報一覧 -->
            <base-mo01354
              v-model="local.mo01354Category"
              :oneway-model-value="localOneway.mo01354CategoryOneway"
              hide-default-footer
              class="list-wrapper mt-2"
              @update:model-value="clickCategorySelectRow"
            >
              <!-- Id -->
              <template #[`item.item2Id`]="{ item }">
                <p class="omitted-content flex-end">
                  {{ item.item1Id }}
                </p>
              </template>
              <!-- 分類名称 -->
              <template #[`item.daibunruiKnj`]="{ item }">
                <p class="omitted-content">
                  {{ item.daibunruiKnj }}
                </p>
              </template>
              <!-- 表示順 -->
              <template #[`item.sort`]="{ item }">
                <c-v-col
                  class="d-flex align-center data-table-cell"
                  @click="setSortOrder(item)"
                >
                  <base-mo01278
                    v-model="item.sort"
                    :oneway-model-value="localOneway.mo01278Oneway"
                    @change="
                      () => {
                        handleChange(item)
                      }
                    "
                  />
                </c-v-col>
              </template>
              <!-- 項目名 -->
              <template #[`item.koumokuKnj`]="{ item }">
                <p class="omitted-content">
                  {{ item.koumokuKnj }}
                </p>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :text="item.koumokuKnj"
                ></c-v-tooltip>
              </template>
              <!-- 表示 -->
              <template #[`item.useFlg`]="{ item }">
                <base-mo01360
                  v-model="item.useFlg"
                  :oneway-model-value="localOneway.mo01360Oneway"
                  @click="
                    () => {
                      handleChange(item, 'right')
                    }
                  "
                >
                  <base-at-radio
                    v-for="(itm, index) in codeList"
                    :key="index"
                    :name="'radio-' + '-' + index"
                    :radio-label="itm.label"
                    :value="itm.value"
                  />
                </base-mo01360>
              </template>
            </base-mo01354>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </c-v-window-item>
    <c-v-window-item value="2">
      <c-v-sheet
        v-show="local.mo00043.id === '2'"
        class="mx-2"
      >
        <c-v-row style="height: 58px"> </c-v-row>
        <c-v-row no-gutters>
          <c-v-col class="table-header">
            <!-- フリーアセスメントフェースシート表示設定情報一覧 -->
            <base-mo01354
              v-model="local.mo01354"
              :oneway-model-value="localOneway.mo01354Oneway"
              hide-default-footer
              class="list-wrapper mt-2"
              @update:model-value="clickHistorySelectRow"
              @click="mo00009Bool = false"
            >
              <!-- Id -->
              <template #[`item.item1Id`]="{ item }">
                <p class="omitted-content flex-end">
                  {{ item.item1Id }}
                </p>
              </template>
              <!-- 表示順 -->
              <template #[`item.sort`]="{ item }">
                <c-v-col
                  class="d-flex align-center data-table-cell"
                  @click="setSortOrder(item)"
                >
                  <base-mo01278
                    v-model="item.sort"
                    :oneway-model-value="localOneway.mo01278Oneway"
                    @change="
                      () => {
                        handleChange(item)
                      }
                    "
                  />
                </c-v-col>
              </template>
              <!-- 項目名 -->
              <template #[`item.koumokuKnj`]="{ item }">
                <p class="omitted-content">
                  {{ item.koumokuKnj }}
                </p>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :text="item.koumokuKnj"
                ></c-v-tooltip>
              </template>
              <!-- 表示 -->
              <template #[`item.useFlg`]="{ item }">
                <base-mo01360
                  v-model="item.useFlg"
                  :oneway-model-value="localOneway.mo01360Oneway"
                  @click="
                    () => {
                      handleChange(item)
                    }
                  "
                >
                  <base-at-radio
                    v-for="(itm, index) in codeList"
                    :key="index"
                    :name="'radio-' + '-' + index"
                    :radio-label="itm.label"
                    :value="itm.value"
                  />
                </base-mo01360>
              </template>
            </base-mo01354>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </c-v-window-item>
    <c-v-window-item value="5">
      <c-v-sheet
        v-show="local.mo00043.id === '5'"
        class="mx-2"
      >
        <c-v-row>
          <c-v-col class="d-flex justify-end">
            <base-mo00009
              :oneway-model-value="localOneway.mo00009_1"
              class="arrow-up"
              @click="
                setTextareaFocus('subtraction', mo00009Bool ? local.mo01354Category : local.mo01354)
              "
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009_2"
              class="arrow-down"
              @click="
                setTextareaFocus('addition', mo00009Bool ? local.mo01354Category : local.mo01354)
              "
            />
          </c-v-col>
        </c-v-row>
        <c-v-row
          no-gutters
          class="d-flex"
        >
          <c-v-col
            class="table-header"
            :style="{ maxWidth: local.mo01354Category.values.items.length ? '50%' : '65.5%' }"
          >
            <!-- フリーアセスメントフェースシート表示設定情報一覧 -->
            <base-mo01354
              v-model="local.mo01354"
              :oneway-model-value="localOneway.mo01354Oneway"
              hide-default-footer
              class="list-wrapper mt-2"
              @update:model-value="clickHistorySelectRow"
              @click="mo00009Bool = false"
            >
              <!-- Id -->
              <template #[`item.item1Id`]="{ item }">
                <p class="omitted-content flex-end">
                  {{ item.item1Id }}
                </p>
              </template>
              <!-- 表示順 -->
              <template #[`item.sort`]="{ item }">
                <c-v-col
                  class="d-flex align-center data-table-cell"
                  @click="setSortOrder(item)"
                >
                  <base-mo01278
                    v-model="item.sort"
                    :oneway-model-value="localOneway.mo01278Oneway"
                    @change="
                      () => {
                        handleChange(item)
                      }
                    "
                  />
                </c-v-col>
              </template>
              <!-- 項目名 -->
              <template #[`item.koumokuKnj`]="{ item }">
                <p class="omitted-content">
                  {{ item.koumokuKnj }}
                </p>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :text="item.koumokuKnj"
                ></c-v-tooltip>
              </template>
              <!-- 行数 -->
              <template #[`item.lineCnt`]="{ item }">
                <c-v-col
                  class="lineCnt flex-end"
                  :style="{
                    background: item.lineCnt.value === '-1' || item.useFlg === '0' ? '#E5E5E5' : '',
                  }"
                >
                  <base-mo01278
                    v-if="item.lineCnt.value !== '-1' && item.useFlg === '1'"
                    v-model="item.lineCnt"
                    :oneway-model-value="localOneway.mo01278Oneway"
                    @change="
                      () => {
                        handleChange(item)
                        item.printModifiedFlg = '1'
                      }
                    "
                  />
                  <p
                    v-else
                    class="lineCnt-text"
                  >
                    {{ item.lineCnt.value === '-1' ? '' : item.lineCnt.value }}
                  </p>
                </c-v-col>
              </template>
              <!-- 表示 -->
              <template #[`item.useFlg`]="{ item }">
                <base-mo01360
                  v-model="item.useFlg"
                  :oneway-model-value="localOneway.mo01360Oneway"
                  @click="
                    () => {
                      handleChange(item)
                    }
                  "
                >
                  <base-at-radio
                    v-for="(itm, index) in codeList"
                    :key="index"
                    :name="'radio-' + '-' + index"
                    :radio-label="itm.label"
                    :value="itm.value"
                  />
                </base-mo01360>
              </template>
              <!-- 作図名 -->
              <template #[`item.pictureFlg`]="{ item }">
                <c-v-col class="d-flex align-center">
                  <base-mo01332
                    v-show="item.pictureName"
                    v-model="item.pictureFlg"
                    :oneway-model-value="{
                      showItemLabel: true,
                      items: [
                        {
                          label: item.pictureName,
                          value: '0',
                        },
                      ],
                    }"
                    @change="
                      () => {
                        handleChange(item)
                      }
                    "
                  />
                </c-v-col>
              </template>
            </base-mo01354>
          </c-v-col>
          <c-v-col
            v-show="local.mo01354Category.values.items.length"
            class="table-header"
          >
            <!-- フリーアセスメントフェースシート表示設定情報一覧 -->
            <base-mo01354
              v-model="local.mo01354Category"
              :oneway-model-value="localOneway.mo01354CategoryOneway"
              hide-default-footer
              class="list-wrapper mt-2"
              @update:model-value="clickCategorySelectRow"
              @click="mo00009Bool = true"
            >
              <template #[`item.item2Id`]="{ item }">
                <p class="omitted-content flex-end">
                  {{ item.item1Id }}
                </p>
              </template>
              <!-- 分類名称 -->
              <template #[`item.daibunruiKnj`]="{ item }">
                <p class="omitted-content">
                  {{ item.daibunruiKnj }}
                </p>
              </template>
              <!-- 表示順 -->
              <template #[`item.sort`]="{ item }">
                <c-v-col
                  class="d-flex align-center data-table-cell"
                  @click="setSortOrder(item)"
                >
                  <base-mo01278
                    v-model="item.sort"
                    :oneway-model-value="localOneway.mo01278Oneway"
                    @change="
                      () => {
                        handleChange(item)
                      }
                    "
                  />
                </c-v-col>
              </template>
              <!-- 項目名 -->
              <template #[`item.koumokuKnj`]="{ item }">
                <p class="omitted-content">
                  {{ item.koumokuKnj }}
                </p>
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :text="item.koumokuKnj"
                ></c-v-tooltip>
              </template>
              <!-- 表示 -->
              <template #[`item.useFlg`]="{ item }">
                <base-mo01360
                  v-model="item.useFlg"
                  :oneway-model-value="localOneway.mo01360Oneway"
                  @click="
                    () => {
                      handleChange(item, 'right')
                    }
                  "
                >
                  <base-at-radio
                    v-for="(itm, index) in codeList"
                    :key="index"
                    :name="'radio-' + '-' + index"
                    :radio-label="itm.label"
                    :value="itm.value"
                  />
                </base-mo01360>
              </template>
            </base-mo01354>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </c-v-window-item>
  </c-v-window>
  <div class="mx-2 title"><base-mo01338 :oneway-model-value="localOneway.mo01338Oneway" /></div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';
:deep(.v-sheet) {
  background: transparent !important;
}
:deep(.radio-group) {
  .v-col {
    padding: 0 !important;
  }
}
// セル内容行数の設定
.omitted-content {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 16px;
}
.lineCnt {
  padding: 0 16px;
  height: 100%;
  display: flex;
  align-items: center;
}
.lineCnt-text {
  padding: 0 11px;
}
:deep(.pa-2) {
  display: flex;
  align-items: center;
  label {
    margin-left: 4px;
  }
}
.arrow-up {
  transform: rotate(90deg);
  :deep(i) {
    font-size: 46px;
  }
}
.arrow-down {
  transform: rotate(90deg);
  :deep(i) {
    font-size: 46px;
  }
}
.table-header {
  max-width: 50%;
}
:deep(.v-table__wrapper) {
  overflow-y: scroll;
}
:deep(.full-width-field) {
  width: 72px !important;
}
.v-radio:nth-of-type(1) {
  margin-left: 6px;
}
.justify-end {
  padding-bottom: 0 !important;
}
:deep(.hidden-color) {
  background-color: #e5e5e5 !important;
}
:deep(.list-wrapper .v-table__wrapper tr td:nth-child(5) .pa-2) {
  padding: 0 8px 0 8px !important;
}
:deep(td) {
  height: 32px !important;
}
:deep(.v-radio) {
  min-height: 31px !important;
  height: 31px !important;
}
:deep(.v-selection-control--density-default) {
  --v-selection-control-size: 32px !important;
}
:deep(.outer_div_style) {
  display: flex;
  align-items: center;
  border: 0 !important;
}
.title {
  margin-top: 1%;
  :deep(.item-label) {
    font-size: 12px !important;
  }
}
</style>
