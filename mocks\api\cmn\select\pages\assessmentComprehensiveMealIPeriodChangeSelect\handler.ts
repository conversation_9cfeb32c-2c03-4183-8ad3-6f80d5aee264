/**
 * TeX0008_「アセスメント」（包括）画面共通情報モック
 * GUI00834_「アセスメント」（包括）共通画面
 *
 * <AUTHOR>
 */

import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import defaultData2 from './data/default2.json'
import defaultData3 from './data/default3.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type {
  assessmentComprehensiveMealIPeriodChangeSelectOutEntity,
  assessmentComprehensiveMealIPeriodChangeSelectInEntity,
} from '~/repositories/cmn/entities/assessmentComprehensiveMealIPeriodChangeSelect'

/**
 * GUI00834_アセスメント」（包括）の取得APIモック
 *
 * @description
 * GUI01051_アセスメント」（包括）画面に表示されるデータを返却する。
 * dataName："assessmentComprehensiveMealIPeriodChangeSelect"
 */
export function handler(inEntity: assessmentComprehensiveMealIPeriodChangeSelectInEntity) {
  let res
  if (inEntity.pageFlag === '1') {
    res = defaultData2
  } else {
    res = defaultData
  }

  const responceJson: BaseResponseBody<assessmentComprehensiveMealIPeriodChangeSelectOutEntity> = {
    statusCode: 'success',
    data: {
      ...res,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
