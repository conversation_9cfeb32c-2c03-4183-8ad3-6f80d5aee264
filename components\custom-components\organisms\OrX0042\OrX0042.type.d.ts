/**
 * OrX0042の計画書（２）初期情報取得のタイプ
 * GUI01014_計画書（２）
 *
 * @description
 * 初期情報取得のタイプ
 *
 * <AUTHOR>
 */
export interface CarePlan2SelectOutData {
  /** 計画書ID */
  ks21Id: string
  /** PL取込フラグ */
  plFlg: string
  /** 更新回数 */
  modifiedCnt: string
  /** 期間管理フラグ */
  kikanFlg: string
  /** 計画期間情報 */
  kikanObj: KikanObj | null
  /** 履歴情報 */
  rirekiObj: RirekiObj | null
  /** 計画書（２）リスト */
  keikasyo2List: Keikasyo2[]
  /** 適用事業所一覧情報 */
  jigyoList: Jigyo[]
}
/** 計画期間情報 */
export interface KikanObj {
  /** 期間ID */
  sc1Id: string
  /** 開始日*/
  startYmd: string
  /** 終了日*/
  endYmd: string
  /** 計画対象期間インデックス*/
  kikanIndex: string
  /** 計画対象期間件数*/
  kikanTotal: string
}
/**
 * 履歴情報
 */
export interface RirekiObj {
  /** 計画書（２）ID*/
  ks21Id: string

  /** 作成日*/
  createYmd: string

  /** 作成者ID*/
  shokuId: string

  /** 作成者名*/
  shokuKnj: string

  /** 有効期間ID*/
  termid: string

  /** 履歴インデックス*/
  rirekiIndex: string

  /** 履歴件数*/
  rirekiCnt: string
}
/**
 * 計画書（２）リスト
 */
export interface Keikasyo2 {
  /** テーブルインデックス */
  tableIndex: number
  /** カウンター*/
  ks22Id?: string

  /** 計画書（２）ID*/
  ks21Id?: string

  /** 具体的*/
  gutaitekiKnj?: ModelValue

  /** 長期*/
  choukiKnj?: ModelValue

  /** 短期*/
  tankiKnj?: ModelValue

  /** 介護*/
  kaigoKnj?: ModelValue

  /** サービス種*/
  svShuKnj?: ModelValue

  /** 頻度*/
  hindoKnj?: ModelValue

  /** 期間*/
  kikanKnj?: ModelValue

  /** 通番*/
  seq?: string

  /** 課題番号*/
  kadaiNo?: ModelValue

  /** 介護番号*/
  kaigoNo?: ModelValue

  /** 長期期間*/
  choKikanKnj?: ModelValue

  /** 短期期間*/
  tanKikanKnj?: ModelValue

  /** 給付対象*/
  hkyuKbn?: string

  /** サービス事業者ＣＤ*/
  jigyouId?: string

  /** サービス事業者名*/
  jigyoNameKnj?: ModelValue

  /** 給付文字*/
  hkyuKnj?: string

  /** 長期期間開始日*/
  choSYmd?: ModelValue

  /** 長期期間終了日*/
  choEYmd?: ModelValue

  /** 短期期間開始日*/
  tanSYmd?: ModelValue

  /** 短期期間終了日*/
  tanEYmd?: ModelValue

  /** 期間開始日*/
  kikanSYmd?: ModelValue

  /** 期間終了日*/
  kikanEYmd?: ModelValue

  /** 週間日課 */
  weekDailyRouting?: string

  /** 保険サービスリスト */
  hokenList?: Hoken[]

  /** 月日指定リスト */
  tukihiList?: Tukihi[]

  /** サービス曜日リスト */
  yobiList?: Yobi[]

  /** 担当者リスト */
  tantoList?: Tanto[]
  /**
   * 更新区分
   */
  updateKbn?: string
  /** チェックボックス選択フラグ */
  checked: { modelValue: boolean }
}
/** 保険サービスリスト */
export interface Hoken {
  /** 保険サービス更新区分 */
  updateKbn2?: string
  /** カウンター*/
  ks211Id: string

  /** 計画書（２）ID*/
  ks21Id: string
  /** 曜日*/
  youbi: string
  /** 週単位以外のｻｰﾋﾞｽ区分*/
  igaiKbn: string

  /** 週単位以外のｻｰﾋﾞｽ（日付指定）*/
  igaiDate: string

  /** 週単位以外のｻｰﾋﾞｽ（曜日指定）*/
  igaiWeek: string

  /** 居宅：開始時間*/
  kaishiJikan: string

  /** 居宅：終了時間*/
  shuuryouJikan: string

  /** 居宅：サービス種類*/
  svShuruiCd: string

  /** 居宅：サービス項目（台帳）*/
  svItemCd: string

  /** 居宅：サービス事業者CD*/
  svJigyoId: string

  /** 居宅：福祉用具貸与単位*/
  tanka: string

  /** 福祉用具貸与商品コード*/
  fygId: string

  /** 合成識別区分*/
  gouseiSikKbn: string

  /** 加算フラグ*/
  kasanFlg: string

  /** 親レコード番号*/
  oyaLineNo: string
}
/** 月日指定 */
export interface Tukihi {
  /**
   *月日指定更新区分
   */
  updateKbn3?: string
  /** カウンター*/
  ks212Id: string

  /** 計画書（２）ID*/
  ks21Id: string

  /** 月日指定開始日*/
  startYmd: string

  /** 月日指定終了日*/
  endYmd: string
}
/** サービス曜日 */
export interface Yobi {
  /**
   *サービス曜日更新区分
   */
  updateKbn4?: string
  /** カウンター*/
  ks221Id: string

  /** 計画書（２）行データID*/
  ks22Id: string

  /** 計画書（２）ID*/
  ks21Id: string

  /** 曜日*/
  youbi: string

  /** 週単位以外のｻｰﾋﾞｽ区分*/
  igaiKbn: string

  /** 週単位以外のｻｰﾋﾞｽ（日付指定）*/
  igaiDate: string

  /** 週単位以外のｻｰﾋﾞｽ（曜日指定）*/
  igaiWeek: string

  /** 居宅：開始時間*/
  kaishiJikan: string

  /** 居宅：終了時間*/
  shuuryouJikan: string

  /** 居宅：サービス種類*/
  svShuruiCd: string

  /** 居宅：サービス項目（台帳）*/
  svItemCd: string

  /** 居宅：サービス事業者CD*/
  svJigyoId: string

  /** 居宅：福祉用具貸与単位*/
  tanka: string

  /** 福祉用具貸与商品コード*/
  fygId: string

  /** 合成識別区分*/
  gouseiSikKbn: string

  /** 加算フラグ*/
  kasanFlg: string
}
/** 担当者リスト */
export interface Tanto {
  /**
   * 担当者更新区分
   */
  updateKbn5?: string
  /** カウンター*/
  ks222Id: string

  /** 計画書（２）行データID*/
  ks22Id: string

  /** 計画書（２）ID*/
  ks21Id: string

  /** 施設：職種（担当者）*/
  shokushuId: string
}
/**
 * 適用事業所一覧情報
 */
export interface Jigyo {
  /** サービス事業者ID*/
  svJigyoId: string

  /** 事業名*/
  jigyoKnj: string

  /** 事業名（略称）*/
  jigyoRyakuKnj: string

  /** サービス事業者コード*/
  svJigyoCd: string

  /** 法人ID*/
  houjinId: string

  /** 施設ID*/
  shisetuId: string

  /** 外部サービス型*/
  chsGaibuFlg: string

  /** 表示順(サービス事業者)*/
  sortSvjigyo: string

  /** 表示順(施設)*/
  sortShisetu: string

  /** 表示順(法人)*/
  sortHoujin: string
}
/**
 * ModelValue構造
 */
export interface ModelValue {
  /**
   * value
   */
  value: string
}
/**
 * テーブルの列info
 */
export interface ColumnInfo {
  /**
   * キー
   */
  key: string
  /**
   * タイトル
   */
  title: string
}
