<script setup lang="ts">
/**
 * Or55904:有機体:印刷設定
 * GUI00871_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { Or55904Const } from '../Or55904/Or55904.constants'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import type { Or10269MsgBtnType } from '../Or10269/Or10269.type'
import type { Or55904StateType } from './Or55904.type'
import { useSetupChildProps, useScreenOneWayBind, useNuxtApp, dateUtils } from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '@/types/business/components/Mo01338Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00020Type, Mo00020OnewayType } from '@/types/business/components/Mo00020Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  UserEntity,
  considerTableInterRAIHistoryEntity,
  PrtEntity,
  ConsiderTableInterRAIPrintSettingsInitSelectInEntity,
  ConsiderTableInterRAIPrintSettingsInitSelectOutEntity,
  IAssessmentInterRAIPrintSettingsSubjectSelectInEntity,
  IAssessmentInterRAIPrintSettingsSubjectSelectOutEntity,
  ConsiderTableInterRAIPrintSettingsHistorySelectInEntity,
  ConsiderTableInterRAIPrintSettingsHistorySelectOutEntity,
} from '~/repositories/cmn/entities/ConsiderTableInterRAIPrintSettingsInitUpdateEntity'
import type {
  SysIniInfoEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsPrtNoChangeUpdateEntity'
import type {
  IFreeAssessmentFacePrintSettingsUpdateInEntity,
  IFreeAssessmentFacePrintSettingsUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsUpdateEntity'
import type {
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { CustomClass } from '~/types/CustomClassType'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type {
  OrX0128OnewayType,
  OrX0128Items,
  OrX0128Headers,
} from '~/types/cmn/business/components/OrX0128Type'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import type { Or55904Param } from '~/components/custom-components/organisms/Or55904/Or55904.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useReportUtils, reportOutputType } from '~/utils/useReportUtils'
import type {
  PrintSetEntity,
  ChoPrtEntity,
} from '~/repositories/cmn/entities/CpnTucRaiAssReportSelectEntity'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo01408OnewayType, Mo01408ItemType } from '@/types/business/components/Mo01408Type'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { useCmnCom } from '@/utils/useCmnCom'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import type { Mo00038OnewayType, Mo00038Type } from '~/types/business/components/Mo00038Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type { Or21815StateType } from '~/components/base-components/organisms/Or21815/Or21815.type'
import type {
  ConsiderTableInterRAIPrintInEntity,
  printConfigureEntity,
  PrintOptionEntity,
  PrintSubjectHistoryEntity,
  ChoPrtList,
} from '~/repositories/cmn/entities/ConsiderTableInterRAIPrintEntity'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { Or21813StateType } from '~/components/base-components/organisms/Or21813/Or21813.type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'

const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()
const { reportOutput } = useReportUtils()
const { convertDateToSeireki } = dateUtils()
const $log = useNuxtApp().$log as DebugLogPluginInterface

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

// 子コンポーネント用変数
const or21815 = ref({ uniqueCpId: '' })
const orX0117 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const localOneway = reactive({
  /**
   * 基本設定
   */
  mo01338OneWayBasicSettings: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 印刷枠の高さを行に設定するラベル
  mo00038Oneway: {
    showSpinBtn: true,
    min: 1,
    max: 30,
    showItemLabel: false,
    isVerticalLabel: false,
    mo00045Oneway: {
      width: '60px',
      disabled: true,
      isVerticalLabel: false,
      showItemLabel: false,
      maxLength: '2',
    } as Mo00045OnewayType,
  } as Mo00038OnewayType,
  /**
   * 帳票タイトル
   */
  mo00615OneWayTypeTitle: {
    itemLabel: t('label.print-settings-title'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * タイトル表示
   */
  mo01338OneWay: {
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo00045OnewayType,
  /**
   * 日付印刷区分
   */
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 指定日
   */
  mo00020OneWay: {
    showItemLabel: false,
    showSelectArrow: false,
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 記入用シートを印刷する
   */
  mo00018OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * アセスメントタイプ
   */
  mo00039OneWayAssessmentType: {
    name: '',
    showItemLabel: false,
    inline: false,
    disabled: true,
  } as Mo00039OnewayType,
  /**
   * トリガーされたCAPのみ印刷する
   */
  mo00018OneWayCap: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.CAP-display-selection-print'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: true,
  } as Mo00018OnewayType,
  /**
   * 印刷オプション
   */
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択ラベル
   */
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 履歴選択ラベル
   */
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択
   */
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 履歴選択
   */
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 基準日
   */
  mo00615OneWayType: {
    itemLabel: t('label.base-date'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * 基準日: 表用テキストフィールド
   */
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '220px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 担当ケアマネ
   */
  mo00615OneWayCareManagerInCharge: {
    itemLabel: t('label.care-manager-in-charge'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
  } as OrX0145OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  mo01408OneWayType: {
    items: [] as Mo01408ItemType[],
    disabled: false,
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01408OnewayType,
  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    disabled: false,
  } as Mo00609OnewayType,
})

/**
 * 親画面の情報
 */
const local = {
  /**
   * 個人情報使用フラグ
   */
  kojinhogoUsedFlg: Or55904Const.DEFAULT.KOJINHOGO_USED_FLG,
  /**
   * 個人情報番号
   */
  sectionAddNo: Or55904Const.DEFAULT.SECTION_ADD_NO,
  /**
   * 親画面.事業所ID
   */
  svJigyoId: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
   */
  historyNoSelect: false,
  /**
   * 親画面.施設ID
   */
  shisetuId: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.担当者ID
   */
  tantoId: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.種別ID
   */
  syubetsuId: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
   */
  userNoSelect: false,
  /**
   * 親画面.フォーカス設定用イニシャル
   */
  focusSettingInitial: [] as string[],
  /**
   * 親画面.初期選択状態の担当者カウンタ値
   */
  selectedUserCounter: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.セクション名
   */
  sectionName: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.利用者ID
   */
  userId: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.事業所名
   */
  svJigyoKnj: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 職員ID：親画面.職員ID
   */
  shokuId: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.処理年月日
   */
  processYmd: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.アセスメントID
   */
  assessmentId: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 選択された帳票のプロファイル
   */
  profile: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者ID
   */
  selectUserId: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 出力帳票名一覧に選択行番号
   */
  index: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * システムINI情報
   */
  sysIniInfo: {} as SysIniInfoEntity,
  /**
   * 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
   */
  historySelect: false,
  /**
   * 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
   */
  userSelect: false,
  /**
   * 帳票番号
   */
  prtNo: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者リスト
   */
  userList: [] as UserEntity[],
  /**
   * 帳票ID
   */
  reportId: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 履歴選択の明細
   */
  orX0128DetList: [] as OrX0128Items[],
  /**
   * 印刷設定情報リスト
   */
  prtList: [] as PrtEntity[],
}

/**
 * レスポンスパラメータ詳細
 */
const localData: ConsiderTableInterRAIPrintSettingsInitSelectOutEntity = {
  data: {},
} as ConsiderTableInterRAIPrintSettingsInitSelectOutEntity
/**************************************************
 * 変数定義
 **************************************************/
// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1400px',
  maxWidth: '1400px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or55904',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or55904ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'or55904_content',
  } as Mo01344OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or55904Const.DEFAULT.IS_OPEN,
})

/**
 * 出力帳票名一覧
 */
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.ledger'),
      key: 'ledgerName',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 日付印刷区分
 */
const mo00039Type = ref<string>('')
const mo00045OneWay = ref<Mo00045Type>({
  value: '',
} as Mo00045Type)
const currentTile = ref<string>('')
/**
 * 指定日
 */
const mo00020Type = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 記入用シートを印刷する
 */
const mo00018Type = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

/**
 * トリガーされたCAPのみ印刷するチェック
 */
const mo00018TypeCap = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 利用者選択
 */
const mo00039OneWayUserSelectType = ref<string>('')

/**
 * 履歴選択
 */
const mo00039OneWayHistorySelectType = ref<string>('')

/**
 * 基準日
 */
const mo00020TypeKijunbi = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 指定日非表示/表示フラゲ
 */
const mo00020Flag = ref<boolean>(false)
/**
 * 基準日フラゲ
 */
const kijunbiFlag = ref<boolean>(false)
/**
 * 履歴一覧セクションフラゲ
 */
const mo01334TypeHistoryFlag = ref<boolean>(true)

const insatuFlg = ref(true)

/**
 * 利用者列幅
 */
const userCols = ref<number>(4)

const orX0128OnewayModel = reactive<OrX0128OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: '1',
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: OrX0128Const.DEFAULT.TANI,
  tableStyle: 'width:526px',
  headers: [
    { title: t('label.base-date'), key: 'assDateYmd', minWidth: '120px', sortable: false },
    { title: t('label.author'), key: 'shokuinKnj', minWidth: '160px', sortable: false },
    { title: t('label.assessment-kind'), key: 'plnType', sortable: false, itemClass: 'text-right' },
  ] as OrX0128Headers[],
  items: [],
})

/**
 * 利用者
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width:210px',
  // 指定行選択
  userId: Or55904Const.DEFAULT.STR.EMPTY,
  /**
   * 複数選択時、50音の選択数を表示するか
   */
  showKanaSelectionCount: true,
  /**
   * フォーカス設定用イニシャル
   */
  focusSettingInitial: [] as string[],
})

/**
 * 担当ケアマネ選択
 */
const tantoIconBtn = ref<boolean>(false)
/**
 * 印刷設定帳票出力
 */
const orX0117Oneway: OrX0117OnewayType = {
  type: Or55904Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType

// 印刷枠の高度テキストフィールド
const mo00038ModelValue = ref<Mo00038Type>({
  mo00045: {
    value: '',
  } as Mo00045Type,
})

/**
 * 初期情報取得フラゲ
 */
const initFlag = ref<boolean>(false)

/**
 * 期間管理フラグ
 */
const kikanFlag = ref<string>(Or55904Const.DEFAULT.STR.EMPTY)
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or55904StateType>({
  cpId: Or55904Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or55904Const.DEFAULT.IS_OPEN
    },
    param: (value) => {
      if (value) {
        local.prtNo = value.prtNo
        local.svJigyoId = value.svJigyoId
        local.shisetuId = value.shisetuId
        local.tantoId = value.tantoId
        local.syubetsuId = value.syubetsuId
        local.sectionName = value.sectionName
        local.userId = value.userId
        local.svJigyoKnj = value.svJigyoKnj
        local.processYmd = value.processYmd
        local.shokuId = value.shokuId
        local.focusSettingInitial = value.focusSettingInitial
        local.selectedUserCounter = value.selectedUserCounter
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 画面ID
const screenId = 'GUI00793'
// ルーティング
const routing = 'GUI00793/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})

// 共通情報.初期設定マスタ.インターライ様式フラグ
const kentouFlg = cmnRouteCom.getInitialSettingMaster()?.kentouFlg

// トリガーされたCAPのみ印刷する 印刷枠の高さを行に設定する 非表示
const kentouDisplayFlg = ref<boolean>(true)

onMounted(async () => {
  if (kentouFlg === '1') {
    kentouDisplayFlg.value = true
  } else {
    kentouDisplayFlg.value = false
  }
  // 汎用コード取得API実行
  await initCodes()

  // 画面ボタン活性非活性設定
  btnItemSetting()

  // 初期情報取得
  await init()

  // 初期選択データ設定
  selectRowDataSetting()
})

// ダイアログ表示フラグ
const showDialogOrX0117 = computed(() => {
  // Or55904のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 初期選択データ設定
 */
const selectRowDataSetting = () => {
  // フォーカス設定用イニシャル設定
  orX0130Oneway.focusSettingInitial = local.focusSettingInitial
  // 初期選択状態の担当者カウンタ値設定
  localOneway.orX0145Oneway.selectedUserCounter = local.selectedUserCounter
  // 利用者一覧明細に親画面.利用者IDが存在する場合
  if (local.userId) {
    // 利用者IDを対するレコードを選択状態にする
    orX0130Oneway.userId = local.userId
  }
  // 利用者一覧明細に親画面.利用者IDが存在しない場合
  else {
    orX0130Oneway.userId = Or55904Const.DEFAULT.STR.EMPTY
  }
}

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // アセスメント種別コード
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // 性別区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    mo00039Type.value = bizukePrintCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of bizukePrintCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWay.items = list
  }

  // アセスメントタイプ
  const assessmentKindCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )
  if (assessmentKindCodeTypes?.length > 0) {
    const list: Mo00039Items[] = []
    for (const item of assessmentKindCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayAssessmentType.items = list
  }

  // 利用者選択 TAN_MULTIPLE_SELECT_CATEGORY
  const tanMultipleSelectCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  if (tanMultipleSelectCategoryCodeTypes?.length > 0) {
    mo00039OneWayUserSelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    mo00039OneWayHistorySelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of tanMultipleSelectCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayHistorySelectType.items = list
    localOneway.mo00039OneWayUserSelectType.items = list
  }
}

/**
 * 初期プロジェクト設定
 */
const initSetting = () => {
  // 親画面.処理年月日が""の場合
  if (Or55904Const.DEFAULT.STR.EMPTY === local.processYmd) {
    // 担当ケアマネ選択
    tantoIconBtn.value = false
  } else {
    // 担当ケアマネ選択
    tantoIconBtn.value = true
  }
}

/**
 * 初期情報取得
 */
const init = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: ConsiderTableInterRAIPrintSettingsInitSelectInEntity = {
    sysCd: systemCommonsStore.getSystemCode,
    sysRyaku: Or55904Const.DEFAULT.SYS_RYAKU,
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    shokuId: local.shokuId,
    userId: local.userId,
    tantoId: local.tantoId,
    sectionName: local.sectionName,
    index: Or55904Const.DEFAULT.INDEX,
    syubetsuId: local.syubetsuId,
    kojinhogoUsedFlg: Or55904Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or55904Const.DEFAULT.SECTION_ADD_NO,
  } as ConsiderTableInterRAIPrintSettingsInitSelectInEntity
  const resp: ConsiderTableInterRAIPrintSettingsInitSelectOutEntity = await ScreenRepository.update(
    'considerTableInterRAIPrintSettingsInitUpdate',
    inputData
  )
  if (resp?.data) {
    if (kentouFlg === '1') {
      resp.data.prtList = resp.data.prtList.filter((item: PrtEntity) => item.prtNo !== '3')
    } else {
      resp.data.prtList = resp.data.prtList.filter((item: PrtEntity) => item.prtNo === '3')
    }
    // レスポンスパラメータ詳細
    localData.data = { ...resp?.data }

    // 期間管理フラグ
    kikanFlag.value = localData.data.kikanFlg

    const prtList: Mo01334Items[] = []

    for (const item of resp.data.prtList) {
      if (item) {
        prtList.push({
          id: item.prtNo,
          mo01337OnewayLedgerName: {
            value: item.defPrtTitle,
            unit: Or55904Const.DEFAULT.STR.EMPTY,
          } as Mo01337OnewayType,
          prnDate: item.prnDate === Or55904Const.DEFAULT.STR.TRUE,
          selectable: true,
          profile: item.profile,
          index: item.index,
          prtNo: item.prtNo,
          prtTitle: item.prtTitle,
        } as Mo01334Items)
      }
    }
    mo00018TypeCap.value.modelValue = Boolean(localData.data.prtList[0]?.param08)
    mo00038ModelValue.value.mo00045.value = localData.data.prtList[0]?.param07
    mo01334Type.value.value = localData.data.prtList[0]?.prtNo
    mo01334Oneway.value.items = prtList
    initFlag.value = true
    // アセスメント履歴情報を取得する
    getHistoryData(resp.data.considerTableInterRAIHistoryList)
    outputLedgerName(local.prtNo)
  }

  initSetting()
}

/**
 * インターライ方式履歴データ
 *
 * @param considerTableInterRAIHistoryList - インターライ方式履歴リスト
 */
const getHistoryData = (considerTableInterRAIHistoryList: considerTableInterRAIHistoryEntity[]) => {
  if (Or55904Const.DEFAULT.KIKAN_FLG_1 === kikanFlag.value) {
    const tempList: string[] = [] as string[]
    let list: OrX0128Items[] = []
    for (const item of considerTableInterRAIHistoryList) {
      if (item) {
        const planPeriod =
          t('label.plan-period') +
          Or55904Const.DEFAULT.STR.SPLIT_COLON +
          item.startYmd +
          Or55904Const.DEFAULT.STR.SPLIT_TILDE +
          item.endYmd
        if (!tempList.includes(planPeriod)) {
          const historyList: OrX0128Items[] = []
          for (const data of considerTableInterRAIHistoryList) {
            const dataPlanPeriod =
              t('label.plan-period') +
              Or55904Const.DEFAULT.STR.SPLIT_COLON +
              data.startYmd +
              Or55904Const.DEFAULT.STR.SPLIT_TILDE +
              data.endYmd
            if (planPeriod === dataPlanPeriod) {
              historyList.push({
                sel: data.sel,
                raiId: data.raiId,
                // userid: data.userId,
                assType: data.plnType,
                plnType: getAssessmentTypeName(data.plnType),
                assDateYmd: data.plnDateYmd,
                assShokuId: data.plnShokuId,
                shokuinKnj: data.authorKnj,
                id: data.raiId,
                sc1Id: data.sc1Id,
                startYmd: data.startYmd,
                endYmd: data.endYmd,
              } as OrX0128Items)
            }
          }
          if (historyList.length > 0) {
            list.push({
              sc1Id: item.sc1Id,
              startYmd: item.startYmd,
              endYmd: item.endYmd,
              isPeriodManagementMergedRow: true,
              planPeriod:
                t('label.plan-period') +
                Or55904Const.DEFAULT.STR.SPLIT_COLON +
                item.startYmd +
                Or55904Const.DEFAULT.STR.SPLIT_TILDE +
                item.endYmd,
              id: Or55904Const.DEFAULT.STR.EMPTY,
            } as OrX0128Items)
            list = list.concat(historyList)
            tempList.push(planPeriod)
          }
        }
      }
    }
    list.forEach((item, index) => {
      item.id = String(++index)
    })
    orX0128OnewayModel.items = list
    // 画面.利用者一覧明細に親画面.利用者IDが存在する場合
    if (local.assessmentId) {
      // 親画面.アセスメントIDを対するレコードを選択状態にする
      orX0128OnewayModel.initSelectId = (
        list.findIndex((item) => item.raiId === local.assessmentId) + 1
      ).toString()
    }
  } else {
    const list: OrX0128Items[] = []
    for (const data of considerTableInterRAIHistoryList) {
      if (data) {
        list.push({
          sel: data.sel,
          raiId: data.raiId,
          // userid: data.userId,
          assType: data.plnType,
          plnType: getAssessmentTypeName(data.plnType),
          assDateYmd: data.plnDateYmd,
          assShokuId: data.plnShokuId,
          shokuinKnj: data.authorKnj,
          id: data.raiId,
          sc1Id: data.sc1Id,
          startYmd: data.startYmd,
          endYmd: data.endYmd,
        } as OrX0128Items)
      }
    }
    orX0128OnewayModel.items = list
    // 履歴一覧明細に親画面.履歴IDが存在する場合
    if (local.assessmentId) {
      // 画面.履歴一覧明細に親画面.履歴IDを対するレコードを選択状態にする
      orX0128OnewayModel.initSelectId = (
        list.findIndex((item) => item.raiId === local.assessmentId) + 1
      ).toString()
    }
  }
}
/**
 * アセスメント
 *
 * @param assessmentType - アセスメントタイプ
 */
const getAssessmentTypeName = (assessmentType: string): string => {
  let assessmentTypeName = Or55904Const.DEFAULT.STR.EMPTY
  if (
    localOneway.mo00039OneWayAssessmentType.items &&
    localOneway.mo00039OneWayAssessmentType.items?.length > 0
  ) {
    for (const item of localOneway.mo00039OneWayAssessmentType.items) {
      if (item) {
        if (item.value === assessmentType) {
          assessmentTypeName = item.label
        }
      }
    }
  }

  return assessmentTypeName
}

/**
 * 画面印刷設定内容を保存
 */
const save = async (): Promise<IFreeAssessmentFacePrintSettingsUpdateOutEntity> => {
  // 当選択情報
  const oldItem = localData.data.prtList.find((item) => item.prtNo === mo01334Type.value.value)
  if (oldItem && oldItem.prtTitle !== mo00045OneWay.value.value) {
    oldItem.prtTitle = mo00045OneWay.value.value
  }
  // バックエンドAPIから印刷設定情報保存
  const inputData: IFreeAssessmentFacePrintSettingsUpdateInEntity = {
    sysRyaku: Or55904Const.DEFAULT.SYS_RYAKU,
    sectionName: local.sectionName,
    gsyscd: systemCommonsStore.getSystemCode,
    shokuId: systemCommonsStore.getStaffId,
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    index: local.index,
    sysIniInfo: local.sysIniInfo,
    prtList: localData.data.prtList,
  } as IFreeAssessmentFacePrintSettingsUpdateInEntity

  const resp: IFreeAssessmentFacePrintSettingsUpdateOutEntity = await ScreenRepository.update(
    'freeAssessmentFacePrintSettingsUpdate',
    inputData
  )
  return resp
}

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  await save()
  setState({
    isOpen: false,
    param: {} as Or55904Param,
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const pdfDownload = async () => {
  // 利用者選択方法が「単一」
  if (Or55904Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 履歴選択が「単一」
    if (Or55904Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      orX0117Oneway.type = Or55904Const.DEFAULT.STR.ONE
      //記入用シートを印刷するチェック入れるの場合
      if (mo00018Type.value.modelValue) {
        // 帳票側の処理を呼び出し、帳票レイアウトのみ印刷する
        const inputData: ConsiderTableInterRAIPrintInEntity = {
          svJigyoKnj: local.svJigyoKnj,
          syscd: systemCommonsStore.getSystemCode,
          printConfigure: {
            shiTeiKubun: mo00039Type.value,
            shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
          } as printConfigureEntity,
          printOption: {
            emptyFlg: mo00018Type.value.modelValue ? '1' : '0',
            gyoSu: mo00038ModelValue.value.mo00045.value,
            triggerPrtFlg: mo00018TypeCap.value.modelValue ? '1' : '0',
          } as PrintOptionEntity,
          printSubjectHistoryList: [],
        } as ConsiderTableInterRAIPrintInEntity
        // 帳票出力
        await reportOutput(local.reportId, inputData, reportOutputType.DOWNLOAD)
        return
      }
      // 記入用シートを印刷するチェック外すの場合
      else {
        // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
        if (local.userNoSelect) {
          // メッセージを表示
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11393'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'blank',
          })
          // はい
          if (dialogResult === Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
            // 処理終了
            return
          }
        }

        // 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
        if (local.historyNoSelect) {
          // メッセージを表示
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11455'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'blank',
          })
          // はい
          if (dialogResult === Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
            // 処理終了
            return
          }
        }
      }

      // 履歴情報リストにデータを選択する場合
      if (!local.historySelect) {
        // 印刷ダイアログ画面を開かずに、画面.印刷対象履歴リスト「利用者情報+履歴情報+出力帳票対象」を直接に利用して、帳票側の処理を呼び出す
        await reportOutputPdf()
        return
      }
    }
    // 履歴選択方法が「複数」
    else if (Or55904Const.DEFAULT.HUKUSUU === mo00039OneWayHistorySelectType.value) {
      // 履歴一覧が0件選択場合（※履歴リストが0件、複数件を含む）
      if (local.historyNoSelect) {
        // メッセージを表示
        const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11455'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
        })
        // はい
        if (dialogResult === Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
          // 処理終了
          return
        }
      }
      // 履歴情報リストにデータを選択する場合
      else {
        // 印刷設定情報リストを作成
        createReportOutputData(local.prtNo)
      }
    }
  }

  // 利用者選択方法が「複数」の場合
  if (Or55904Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
    orX0117Oneway.type = Or55904Const.DEFAULT.STR.ONE
    // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
    if (local.userNoSelect) {
      // メッセージを表示
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11393'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if (dialogResult === Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
        // 処理終了
        return
      }
    }
    // 利用者情報リストにデータを選択する場合
    else {
      // 印刷設定情報リストを作成
      await PrintSettingsSubjectSelect()
    }
  }

  // AC019-2と同じ => 画面の印刷設定情報を保存する
  await save()

  // 選択された帳票のプロファイルが””の場合
  if (local.profile === Or55904Const.DEFAULT.STR.EMPTY) {
    // メッセージを表示
    const dialogResult = await openErrorDialog(or21813.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // はい
    if (dialogResult === Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
      // 処理終了
      return
    }
  }

  // 「AC020-5-2 また AC020-5-3」で取得した印刷設定情報リスト＞0件の場合
  if (orX0117Oneway.historyList.length > 0) {
    // OrX0117のダイアログ開閉状態を更新する
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}
/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openErrorDialog = async (
  uniqueCpId: string,
  state: Or21813StateType
): Promise<Or10269MsgBtnType> => {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(uniqueCpId)

        let result = Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL as Or10269MsgBtnType

        if (event?.firstBtnClickFlg) {
          result = Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // エラーダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 印刷ダイアログ画面を開
 */
const reportOutputPdf = async () => {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    const reportData: ConsiderTableInterRAIPrintInEntity = {
      svJigyoKnj: local.svJigyoKnj,
      syscd: systemCommonsStore.getSystemCode,
      printConfigure: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
      } as PrintSetEntity,
      printOption: {
        emptyFlg: mo00018Type.value.modelValue ? '1' : '0',
        gyoSu: mo00038ModelValue.value.mo00045.value,
        triggerPrtFlg: mo00018TypeCap.value.modelValue ? '1' : '0',
      } as PrintOptionEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as ConsiderTableInterRAIPrintInEntity

    const choPrtList: ChoPrtList[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        //  単一帳票
        if (local.prtNo === item.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtList)
        }
      }
    }

    // 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or55904Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or55904Const.DEFAULT.STR.EMPTY,
      sc1Id:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].sc1Id as string)
          : Or55904Const.DEFAULT.STR.EMPTY,
      startYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].startYmd as string)
          : Or55904Const.DEFAULT.STR.EMPTY,
      endYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].endYmd as string)
          : Or55904Const.DEFAULT.STR.EMPTY,
      raiId:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].raiId as string)
          : Or55904Const.DEFAULT.STR.EMPTY,
      assType:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].assType as string)
          : Or55904Const.DEFAULT.STR.EMPTY,
      assDateYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].assDateYmd as string)
          : Or55904Const.DEFAULT.STR.EMPTY,
      assShokuId:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].assShokuId as string)
          : Or55904Const.DEFAULT.STR.EMPTY,
      result: Or55904Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    // 帳票出力
    await reportOutput(local.reportId, reportData, reportOutputType.DOWNLOAD)
  } catch (e) {
    $log.debug('帳票の出力に失敗しました。', local.reportId, reportOutputType.DOWNLOAD, e)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * 印刷設定情報リストを作成(利用者選択が「複数」、利用者情報リストにデータを選択する場合)
 */
const PrintSettingsSubjectSelect = async () => {
  // 印刷設定情報リストを作成する
  const inputData: IAssessmentInterRAIPrintSettingsSubjectSelectInEntity = {
    // prtNo: local.prtNo,
    svJigyoId: local.svJigyoId,
    kijunbi: mo00020TypeKijunbi.value.value ?? Or55904Const.DEFAULT.STR.EMPTY,
    userList: local.userList,
  } as IAssessmentInterRAIPrintSettingsSubjectSelectInEntity
  const resp: IAssessmentInterRAIPrintSettingsSubjectSelectOutEntity =
    await ScreenRepository.select(
      'considerTableInterRAIPrintSettingsPrintSettingsSubjectSelect',
      inputData
    )

  const list: OrX0117History[] = []
  if (resp.data) {
    for (const data of resp.data.prtHistoryList) {
      if (data) {
        // 利用者複数の場合
        if (Or55904Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 印刷設定情報リストを作成
          const reportData: ConsiderTableInterRAIPrintInEntity = {
            svJigyoKnj: local.svJigyoKnj,
            syscd: systemCommonsStore.getSystemCode,
            printConfigure: {
              shiTeiKubun: mo00039Type.value,
              shiTeiDate: mo00020Type.value.value
                ? mo00020Type.value.value.split('/').join('-')
                : '',
            } as PrintSetEntity,
            printOption: {
              emptyFlg: mo00018Type.value.modelValue ? '1' : '0',
              gyoSu: mo00038ModelValue.value.mo00045.value,
              triggerPrtFlg: mo00018TypeCap.value.modelValue ? '1' : '0',
            } as PrintOptionEntity,
            printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
          } as ConsiderTableInterRAIPrintInEntity

          const choPrtList: ChoPrtEntity[] = []
          for (const item of localData.data.prtList) {
            if (item) {
              //  単一帳票
              if (local.prtNo === item.prtNo) {
                choPrtList.push({
                  shokuId: systemCommonsStore.getStaffId,
                  sysRyaku: systemCommonsStore.getSystemAbbreviation,
                  section: local.sectionName,
                  prtNo: item.prtNo,
                  choPro: item.profile,
                  sectionName: item.defPrtTitle,
                  dwobject: item.dwobject,
                  prtOrient: item.prtOrient,
                  prtSize: item.prtSize,
                  listTitle: item.listTitle,
                  prtTitle: item.prtTitle,
                  mTop: item.mtop,
                  mBottom: item.mbottom,
                  mLeft: item.mleft,
                  mRight: item.mright,
                  ruler: item.ruler,
                  prndate: item.prnDate,
                  prnshoku: item.prnshoku,
                  serialFlg: item.serialFlg,
                  modFlg: item.modFlg,
                  secFlg: item.secFlg,
                  param01: item.param01,
                  param02: item.param02,
                  param03: item.param03,
                  param04: item.param04,
                  param05: item.param05,
                  param06: item.param06,
                  param07: item.param07,
                  param08: item.param08,
                  param09: item.param09,
                  param10: item.param10,
                  serialHeight: item.serialHeight,
                  serialPagelen: item.serialPagelen,
                  hsjId: systemCommonsStore.getHoujinId,
                  param11: item.param11,
                  param12: item.param12,
                  param13: item.param13,
                  param14: item.param14,
                  param15: item.param15,
                  param16: item.param16,
                  param17: item.param17,
                  param18: item.param18,
                  param19: item.param19,
                  param20: item.param20,
                  param21: item.param21,
                  param22: item.param22,
                  param23: item.param23,
                  param24: item.param24,
                  param25: item.param25,
                  param26: item.param26,
                  param27: item.param28,
                  param28: item.param28,
                  param29: item.param29,
                  param30: item.param30,
                  param31: item.param31,
                  param32: item.param32,
                  param33: item.param33,
                  param34: item.param34,
                  param35: item.param35,
                  param36: item.param36,
                  param37: item.param37,
                  param38: item.param38,
                  param39: item.param39,
                  param40: item.param40,
                  param41: item.param41,
                  param42: item.param42,
                  param43: item.param43,
                  param44: item.param44,
                  param45: item.param45,
                  param46: item.param46,
                  param47: item.param47,
                  param48: item.param48,
                  param49: item.param49,
                  param50: item.param50,
                  houjinId: systemCommonsStore.getHoujinId,
                  shisetuId: systemCommonsStore.getShisetuId,
                  svJigyoId: systemCommonsStore.getSvJigyoId,
                  zoomRate: item.zoomRate,
                  modifiedCnt: item.modifiedCnt,
                } as ChoPrtEntity)
              }
            }
          }
          reportData.printSubjectHistoryList.push({
            userId: data.userId,
            userName: data.userName,
            sc1Id: data.sc1Id,
            startYmd: data.startYmd,
            endYmd: data.endYmd,
            raiId: data.raiId,
            assType: data.plnType,
            assDateYmd: data.plnDateYmd,
            assShokuId: data.plnShokuId,
            result: data.result,
            choPrtList: choPrtList,
          } as PrintSubjectHistoryEntity)
          list.push({
            reportId: local.reportId,
            outputType: reportOutputType.DOWNLOAD,
            reportData: reportData,
            userName: data.userName,
            historyDate: data.startYmd,
            result: data.result,
          } as OrX0117History)
        }
      }
    }
  }
  orX0117Oneway.historyList = list
}

/**
 * 切替前の印刷設定を保存する
 *
 * @param selectId - 出力帳票ID
 */
const setBeforChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          item.prnDate = mo00039Type.value
          break
        }
      }
    }
  }
}

/**
 * 切替後の印刷設定を画面に設定する
 *
 * @param selectId - 出力帳票ID
 */
const setAfterChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          mo00039Type.value = item.prnDate
          break
        }
      }
    }
  }
}

/**
 * 「出力帳票名」選択
 *
 * @param selectId - 出力帳票ID
 */
const outputLedgerName = (selectId: string) => {
  if (!selectId) {
    selectId = Or55904Const.DEFAULT.SECTION_NO
  }
  let label = Or55904Const.DEFAULT.STR.EMPTY
  for (const item of mo01334Oneway.value.items) {
    if (item) {
      if (selectId === item.id && item.mo01337OnewayLedgerName) {
        label = item.prtTitle as string
        mo01334Type.value.value = item.id

        // プロファイル
        local.profile = item.profile as string

        // 出力帳票名一覧に選択行番号
        local.index = item.index as string

        // 帳票番号
        local.prtNo = item.prtNo as string

        // 帳票ID
        setReportId(item.id)
        break
      }
    }
  }

  mo00045OneWay.value.value = label
  currentTile.value = label

  // 帳票イニシャライズデータを取得する
  void getSectionInitializeData()
}

/**
 * 帳票ID設定
 *
 * @param prtNo - 帳票番号
 */
const setReportId = (prtNo: string) => {
  switch (prtNo) {
    case Or55904Const.DEFAULT.STR.TWO:
      local.reportId = Or55904Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.CAPCONSIDERBLANKFORMREPORT
      break
    case Or55904Const.DEFAULT.STR.THREE:
      local.reportId = Or55904Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.CAPSUMMARYSHEETREPORT
      break
    case Or55904Const.DEFAULT.STR.FOUR:
      local.reportId = Or55904Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.PROBLEMREGIONCONSIDERTABLEREPORT
      break
    default:
      local.reportId = Or55904Const.DEFAULT.STR.EMPTY
      break
  }
}

/**
 * 印刷設定情報リストを作成
 *
 * @param prtNo - 帳票番号
 */
const createReportOutputData = (prtNo: string) => {
  const list: OrX0117History[] = []
  for (const orX0128DetData of local.orX0128DetList) {
    const reportData: ConsiderTableInterRAIPrintInEntity = {
      svJigyoKnj: local.svJigyoKnj,
      printConfigure: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
      } as PrintSetEntity,
      printOption: {
        emptyFlg: mo00018Type.value.modelValue ? '1' : '0',
        gyoSu: mo00038ModelValue.value.mo00045.value,
        triggerPrtFlg: mo00018TypeCap.value.modelValue ? '1' : '0',
      } as PrintOptionEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as ConsiderTableInterRAIPrintInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        //  単一帳票
        if (prtNo === item.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or55904Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or55904Const.DEFAULT.STR.EMPTY,
      sc1Id: orX0128DetData.sc1Id as string,
      startYmd: orX0128DetData.startYmd as string,
      endYmd: orX0128DetData.endYmd as string,
      raiId: orX0128DetData.raiId as string,
      assType: orX0128DetData.assType as string,
      assDateYmd: orX0128DetData.assDateYmd as string,
      assShokuId: orX0128DetData.assShokuId as string,
      result: Or55904Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    list.push({
      reportId: local.reportId,
      outputType: reportOutputType.DOWNLOAD,
      reportData: reportData,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or55904Const.DEFAULT.STR.EMPTY,
      historyDate: orX0128DetData.assDateYmd as string,
      result: Or55904Const.DEFAULT.STR.EMPTY,
    } as OrX0117History)
  }
  orX0117Oneway.historyList = list
}

/**
 * 帳票イニシャライズデータを取得する
 */
const getSectionInitializeData = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity = {
    profile: local.profile,
    gsyscd: systemCommonsStore.getSystemCode,
    shokuId: systemCommonsStore.getStaffId,
    kojinhogoUsedFlg: Or55904Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or55904Const.DEFAULT.SECTION_ADD_NO,
  } as IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity
  const resp: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity =
    await ScreenRepository.update('freeAssessmentFacePrintSettingsPrtNoChangeUpdate', inputData)
  if (resp.data) {
    local.sysIniInfo = resp.data.sysIniInfo
  }
}

/**
 * 印刷設定履歴リスト取得
 */
const getPrintSettingsHistoryList = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: ConsiderTableInterRAIPrintSettingsHistorySelectInEntity = {
    svJigyoId: local.svJigyoId,
    userId: local.selectUserId,
    kikanFlg: kikanFlag.value,
  } as ConsiderTableInterRAIPrintSettingsHistorySelectInEntity
  const resp: ConsiderTableInterRAIPrintSettingsHistorySelectOutEntity =
    await ScreenRepository.select('considerTableInterRAIPrintSettingsHistorySelect', inputData)
  if (resp.data) {
    // アセスメント履歴一覧データ
    getHistoryData(resp.data.considerTableInterRAIHistoryList)
  }
}

/**
 * 画面ボタン活性非活性設定
 */
const btnItemSetting = () => {
  // 利用者選択方法が「単一」の場合
  if (Or55904Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 基準日を非表示にする
    // 履歴選択を活性表示にする
    kijunbiFlag.value = false

    // 履歴選択方法が「単一」の場合
    if (Or55904Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWay.disabled = false
    }
    // 以外の場合
    else {
      // 記入用シートを印刷するを非活性表示にする
      localOneway.mo00018OneWay.disabled = true
    }
  }
  // 以外の場合
  else {
    // 基準日を活性表示にする
    // 履歴選択を非表示にする
    kijunbiFlag.value = true
    mo00018Type.value.modelValue = false
    localOneway.mo00018OneWay.disabled = true
  }

  // 履歴選択方法が「単一」の場合
  if (Or55904Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
    // 利用者選択方法が「単一」の場合
    if (Or55904Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWay.disabled = false
    }
    // 以外の場合
    else {
      localOneway.mo00018OneWay.disabled = true
    }
  }
  // 以外の場合
  else {
    // 記入用シートを印刷するを非活性表示にする
    localOneway.mo00018OneWay.disabled = true

    // 記入用シートを印刷するをチェックオフにする
    mo00018Type.value.modelValue = false
  }

  // 履歴一覧セクション
  if (Or55904Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    userCols.value = 4
    mo01334TypeHistoryFlag.value = true
  } else {
    userCols.value = 11
    mo01334TypeHistoryFlag.value = false
  }

  // 担当ケアマネ選択アイコンボタン非活性/活性設定
  const kkjTantoFlg: string =
    cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? Or55904Const.DEFAULT.STR.EMPTY
  // 共通情報.担当ケアマネ設定フラグ > 0、且つ、共通情報.担当者IDが0以外の場合
  if (
    systemCommonsStore.getManagerId !== Or55904Const.DEFAULT.STR.ZERO &&
    parseInt(kkjTantoFlg) > Or55904Const.DEFAULT.NUMBER.ZERO
  ) {
    // 非活性
    localOneway.mo01408OneWayType.disabled = true
  }
  // その他場合
  else {
    // 活性
    localOneway.mo01408OneWayType.disabled = false
  }
}

/**
 * 警告ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openWarningDialog = (uniqueCpId: string, state: Or21815StateType) => {
  Or21815Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(uniqueCpId)

        let result = 'no'

        if (event?.thirdBtnClickFlg) {
          result = 'no'
        }
        if (event?.closeBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 帳票タイトルフォーカスアウト処理
 */
const handleBlur = async () => {
  if (mo00045OneWay.value.value === '') {
    await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.yes'),
    })
    mo00045OneWay.value.value = currentTile.value
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = async (
  uniqueCpId: string,
  state: Or21814OnewayType
): Promise<Or10269MsgBtnType> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result: Or10269MsgBtnType = Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_YES

        if (event?.firstBtnClickFlg) {
          result = Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or55904Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 担当ケアマネプルダウン
 *
 * @param result - 戻り値
 */
const orx0145UpdateModelValue = (result: OrX0145Type) => {
  if (result) {
    if (result.value) {
      if (!Array.isArray(result.value) && 'chkShokuId' in result.value) {
        // TODO API疎通時に確認
        orX0130Oneway.tantouCareManager = result.value.chkShokuId
      }
    }
  }
}

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334Type.value.value,
  (newValue, oldValue) => {
    setBeforChangePrintData(oldValue)
    setAfterChangePrintData(newValue)
    outputLedgerName(newValue)

    const foundPrt = localData.data.prtList.find((item) => item.prtNo === newValue)
    mo00038ModelValue.value.mo00045.value = foundPrt?.param07 ?? '1'
    mo00018TypeCap.value.modelValue = foundPrt?.param08 === '1' ? true : false
    //帳票選択CAPマリー表の場合
    if (newValue === '2') {
      localOneway.mo00018OneWayCap.disabled = false
      if (mo00018Type.value.modelValue) {
        localOneway.mo00018OneWayCap.disabled = true
        insatuFlg.value = false
        const target = localOneway.mo00038Oneway.mo00045Oneway
        if (target) {
          target.disabled = false
        }
      }
    }
    //帳票選択CAP検討用紙の場合
    else {
      localOneway.mo00018OneWayCap.disabled = true
      insatuFlg.value = true
      mo00018TypeCap.value.modelValue = false
    }
    // 日付印刷区分が2の場合
    if (Or55904Const.DEFAULT.STR.TWO === mo00039Type.value) {
      // 指定日を活性表示にする。
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする。
      mo00020Flag.value = false
    }
  }
)

/**
 * 「日付印刷区分」ラジオボタン押下
 */
watch(
  () => mo00039Type.value,
  (newValue) => {
    if (Or55904Const.DEFAULT.STR.TWO === newValue) {
      // 指定日を活性表示にする
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする
      mo00020Flag.value = false
    }
  }
)

/**
 * 「利用者選択方法」ラジオボタン選択
 */
watch(
  () => mo00039OneWayUserSelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    // 利用者選択方法が「単一」の場合
    if (Or55904Const.DEFAULT.TANI === newValue) {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width: 210px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }
      // 利用者一覧明細に親画面.利用者IDが存在する場合
      if (local.userId) {
        // 利用者IDを対するレコードを選択状態にする
        orX0130Oneway.userId = local.userId
      }
    } else {
      // 復元
      orX0117Oneway.type = Or55904Const.DEFAULT.STR.ONE
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width: 430px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }
    }
  }
)

/**
 * 「履歴選択方法」ラジオボタン押下
 */
watch(
  () => mo00039OneWayHistorySelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    if (OrX0128Logic.event.get(orX0128.value.uniqueCpId)) {
      if (
        OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList &&
        OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList.length > 0
      ) {
        local.historyNoSelect = false
      } else {
        local.historyNoSelect = true
      }
    } else {
      local.historyNoSelect = true
    }

    // 履歴選択方法が「単一」の場合
    if (Or55904Const.DEFAULT.TANI === newValue) {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.TANI
    } else {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.HUKUSUU
      orX0117Oneway.type = Or55904Const.DEFAULT.STR.ZERO
    }
  }
)

/**
 * 記入用シート方式ラジオボタン監視
 */
watch(
  () => mo00018Type.value.modelValue,
  () => {
    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 「履歴選択」の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()
    if (newValue) {
      if (newValue.historyDetClickFlg) {
        local.orX0128DetList = newValue.orX0128DetList
        if (newValue.orX0128DetList.length > 0) {
          local.historySelect = false
        } else {
          local.historySelect = true
        }
      }
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickFlg) {
      if (newValue.userList.length > 0) {
        local.userNoSelect = false
        local.selectUserId = newValue.userList[0].userId
        local.userList = []
        for (const item of newValue.userList) {
          if (item) {
            local.userList.push({
              userId: item.userId,
              userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
            } as UserEntity)
          }
        }

        // 利用者選択方法が「単一」の場合
        if (Or55904Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
          if (initFlag.value) {
            // アセスメント履歴情報を取得する
            await getPrintSettingsHistoryList()
          }
        }
        // 利用者選択方法が「複数」の場合
        else if (Or55904Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 利用者一覧明細に前回選択された利用者が選択状態になる
          createReportOutputData(local.prtNo)
        }
      } else {
        local.userNoSelect = true
        local.userList = []
      }
    } else {
      local.userNoSelect = true
      local.userList = []
    }
  }
)

/**
 * イベントリスナーの解除
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await save()
    }
  }
)

/**
 * 記入用シートを印刷するチェックボックス監視
 */
watch(
  () => mo00018Type.value.modelValue,
  (newValue) => {
    if (newValue) {
      localOneway.mo00018OneWayCap.disabled = true
    } else if (mo01334Type.value.value === '2') {
      localOneway.mo00018OneWayCap.disabled = false
    }
    if (mo01334Type.value.value === '2' && newValue) {
      insatuFlg.value = false
      const target = localOneway.mo00038Oneway.mo00045Oneway
      if (target) {
        target.disabled = false
      }
    } else {
      insatuFlg.value = true
      const target = localOneway.mo00038Oneway.mo00045Oneway
      if (target) {
        target.disabled = true
      }
    }
  }
)

/**
 * 印刷枠の高度テキストフィールド監視
 */
watch(
  () => mo00038ModelValue.value.mo00045.value,
  (newValue) => {
    const foundPrt = localData.data.prtList.find((item) => item.prtNo === mo01334Type.value.value)
    if (foundPrt) {
      foundPrt.param07 = newValue
    }
  }
)

/**
 * トリガーされたCAPのみ印刷するチェックボックス監視
 */
watch(
  () => mo00018TypeCap.value.modelValue,
  (newValue) => {
    const foundPrt = localData.data.prtList.find((item) => item.prtNo === mo01334Type.value.value)
    if (foundPrt) {
      if (newValue) {
        foundPrt.param08 = '1'
      } else {
        foundPrt.param08 = '0'
      }
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        class="or55904_row pa-2"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="2"
          class="or55904_table table-header"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="mo01334Oneway"
            class="list-wrapper left-table"
            style="text-align: center"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="3"
          class="content_center"
        >
          <c-v-row
            no-gutter
            class="printerOption customCol or55904_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayBasicSettings"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or55904_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="label_left"
            >
              <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayTypeTitle"></base-mo00615>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or55904_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-top: 0px; padding-left: 24px"
            >
              <base-mo00045
                v-model="mo00045OneWay"
                :oneway-model-value="localOneway.mo01338OneWay"
                @blur="handleBlur"
              ></base-mo00045>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or55904_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              style="padding-left: 0px; padding-right: 0px"
            >
              <base-mo00039
                v-model="mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              style="padding-left: 0px; padding-right: 8px"
            >
              <base-mo00020
                v-if="mo00020Flag"
                v-model="mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="printerOption customCol or55904_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or55904_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-bottom: 0px; padding-left: 0px"
            >
              <!-- 記入用シートを印刷する -->
              <base-mo00018
                v-model="mo00018Type"
                :oneway-model-value="localOneway.mo00018OneWay"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-show="kentouDisplayFlg"
            no-gutter
            class="customCol or55904_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="d-flex justify-center align-center"
              :class="{ 'v-selection-control--disabled': insatuFlg }"
              style="padding-top: 0px; padding-bottom: 0px"
            >
              <div>{{ t('label.printing-frame-height') }}</div>
              <div>
                <base-mo00038
                  v-model="mo00038ModelValue"
                  :oneway-model-value="localOneway.mo00038Oneway"
                ></base-mo00038>
              </div>
              <div>{{ t('label.line-set') }}</div>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or55904_row"
          >
            <c-v-col
              v-show="kentouDisplayFlg"
              cols="12"
              sm="12"
              style="padding-top: 0px; padding-left: 0px"
            >
              <base-mo00018
                v-model="mo00018TypeCap"
                :oneway-model-value="localOneway.mo00018OneWayCap"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="7"
          class="content_center"
        >
          <c-v-row
            class="or55904_row"
            no-gutter
            style="align-items: center; padding-bottom: 8px"
          >
            <c-v-col
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or55904_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or55904_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <!-- 利用者選択 -->
                  <base-mo00039
                    v-model="mo00039OneWayUserSelectType"
                    :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="kijunbiFlag"
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or55904_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                >
                  <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayType"> </base-mo00615>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or55904_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00020
                    v-model="mo00020TypeKijunbi"
                    :oneway-model-value="localOneway.mo00020KijunbiOneWay"
                  />
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-else
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or55904_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or55904_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00039
                    v-model="mo00039OneWayHistorySelectType"
                    :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="tantoIconBtn"
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <!-- 担当ケアマネプルダウン -->
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
                @update:model-value="orx0145UpdateModelValue"
              ></g-custom-or-x-0145>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            class="or55904_row"
            no-gutter
          >
            <c-v-col :cols="userCols">
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag && initFlag"
              cols="8"
              style="overflow-x: hidden"
              class="pr-0 right-table"
            >
              <div>
                <!-- 計画期間＆履歴一覧 -->
                <g-custom-or-x-0128
                  v-if="orX0128OnewayModel.singleFlg"
                  v-bind="orX0128"
                  :oneway-model-value="orX0128OnewayModel"
                ></g-custom-or-x-0128>
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="ml-2"
          @click="pdfDownload()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- メッセージ 警告 -->
  <g-base-or-21815 v-bind="or21815" />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>
<style>
.or55904_content {
  padding: 0px !important;
}

.or55904_gokeiClass {
  label {
    color: #ffffff;
  }
}
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';

:deep(.v-table__wrapper) {
  overflow-x: auto !important;
}

.or55904_table {
  padding: 0px !important;
  border: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or55904_row {
  margin: 0px !important;
}

.content_center {
  border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  border-top: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: #edf1f7;
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}
:deep(.left-table .v-table__wrapper th) {
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
}
.left-table :deep(.v-table__wrapper thead tr th:last-child) {
  border-right: none !important;
}
:deep(.left-table tbody td) {
  text-align: left;
}
</style>
