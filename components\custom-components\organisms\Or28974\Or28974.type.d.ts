import type { LedgerPrintInfo } from '~/repositories/cmn/entities/PrintSettingsScreenInitialInfoSelectGUI01283Entity'
/**
 * Or28974:（認定調査）印刷設定モーダル
 * GUI01283_印刷設定
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> 劉顕康
 */
export interface Or28974StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 *明細表データ
 */
export interface TableDataType {
  /**
   *一覧データリスト
   */
  dataList: DataRow[]
}

/**
 * 行のデータ
 */
export interface DataRow extends LedgerPrintInfo {
  /** id */
  id: string
}

/**
 * コードデータ
 */
export interface CodeData {
  /**
   * 値
   */
  value: string
  /**
   * タイトル
   */
  title: string
}

/**
 * 印刷設定利用者リスト
 */
export interface userList {
  /** 利用者ID */
  id: string
  /** 利用者番号 */
  selfid: string
  /** 氏名（姓） */
  name1knj: string
  /** 氏名（名） */
  name2knj: string
  /** 性別 */
  sex: string
  /** 生年月日 */
  birthdayYmd: string
  /** 集計する要介護度 */
  stTaisho: string
}

/**
 * 認定調査票情報リスト
 */
export interface cpnTucCschList {
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
  /**
   * 履歴リスト
   */
  historyList: HistoryInfo[]
}

/**
 * 履歴情報
 */
export interface HistoryInfo {
  /** id */
  id: string
  /** 実施日 */
  jisshiDateYmd: string
  /** 記入者コード */
  chkShokuId: string
  /** 調査票ID1 */
  cschId1: string
  /** 調査票ID2 */
  cschId2: string
  /** 調査票ID3 */
  cschId3: string
  /** 調査票ID4 */
  cschId4: string
  /** 改定コード */
  dmyCho: string
  /** 調査票改訂フラグ */
  ninteiFlg: string
  /** 職員名 */
  shokuinKnj: string
}
