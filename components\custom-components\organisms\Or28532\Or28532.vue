npm
<script setup lang="ts">
/**
 * Or28532:有機体:留意事項取込画面モーダル
 * GUI00972_留意事項取込画面
 *
 * @description
 *［留意事項取込画面］では、［利用者管理］の［介護保険］画面で登録されている留意事項を選択し、呼び出し元の画面に上書きまたは追加します。
 *［留意事項取込画面］は、［ケアマネ］→［計画書］→［計画書（1）］画面などで［留意事項］をクリックすると表示されます。
 *
 * <AUTHOR> 李晨昊
 */
import { useI18n } from 'vue-i18n'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import type { Or28532StateType, DataTableData } from './Or28532.type'
import { Or28532Const } from './Or28532.constants'
import type { Or28532Type, Or28532OnewayType } from '~/types/cmn/business/components/Or28532Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind } from '#imports'
import type {
  ThinkToKeepInMindInfoSelectInEntity,
  ThinkToKeepInMindInfoSelectOutEntity,
} from '~/repositories/cmn/entities/ThinkToKeepInMindInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or28532Type
  onewayModelValue: Or28532OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or28532OnewayType = {
  userId: '',
}

const localOneway = reactive({
  or28532: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 上書ボタン設置
  mo00609OverwriteBtnOneway: {
    btnLabel: t('btn.overwrite'),
    width: '90px',
    disabled: true,
  } as Mo00609OnewayType,
  // 追加ボタン設置
  mo00609AddBtnOneway: {
    btnLabel: t('btn.add'),
    width: '90px',
    disabled: true,
  } as Mo00609OnewayType,
  // 留意事項取込画面ダイアログ
  mo00024Oneway: {
    width: '650px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or28532',
      toolbarTitle: t('label.show-content-items-opinion-screen-id'),
      toolbarName: 'Or28532ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  //閉じるボタン
  mo00611OneWay: {
    btnLabel: t('btn.close'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 上書ボタン
  mo00609OverwriteOneway: {
    btnLabel: t('btn.overwrite'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.overwrite'),
  } as Mo00609OnewayType,
  // 追加ボタン
  mo00609AddOneway: {
    btnLabel: t('btn.add'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.add'),
  } as Mo00609OnewayType,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28532Const.DEFAULT.IS_OPEN,
})
// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

const or21814 = ref({ uniqueCpId: '' })
// 取込情報初期情報
const tableData = ref<DataTableData>({
  yuuijikouList: [],
})
// ポスト最小幅
const columnMinWidth = ref<number[]>([210,120,288])
// テーブルヘッダ
const headers = [
  //認定期間
  {
    title: t('label.certification-eriod'),
    width: '180px',
    align: 'left',
    sortable: false,
    key: 'certificationEriod',
  },
  //要介護度
  {
    title: t('label.level-of-care-required'),
    width: '100px',
    align: 'left',
    sortable: false,
    key: 'yokaiKnj',
  },
  //留意事項
  {
    title: t('label.show-content-items-opinion'),
    width: '250px',
    align: 'left',
    sortable: false,
    key: 'ryuuiKnj',
  },
]
const tableDataFilter = computed(() => {
  const yuuijikouList = tableData.value.yuuijikouList
  return { yuuijikouList: yuuijikouList }
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or28532StateType>({
  cpId: Or28532Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or28532Const.DEFAULT.IS_OPEN
    },
  },
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await init()
})

/**
 * 取込情報初期情報取得
 */
async function init() {
  //対象期間入力初期情報取得(IN)
  const inputData: ThinkToKeepInMindInfoSelectInEntity = {
    userId: props.onewayModelValue.userId,
  }

  //対象期間入力初期情報取得
  const ret: ThinkToKeepInMindInfoSelectOutEntity = await ScreenRepository.select(
    'ryuuiKnjImportInitSelect',
    inputData
  )
  console.log('ret.data', ret.data)
  // 戻り値はテーブルデータとして処理されます
  for (const outInfo of ret.data.yuuijikouList) {
    tableData.value.yuuijikouList.push({
      // 認定期間
      certificationEriod: outInfo.dispStartYmd + ' ～ ' + outInfo.dispEndYmd,
      //要介護度
      yokaiKnj: outInfo.yokaiKnj,
      //留意事項
      ryuuiKnj: outInfo.ryuuiKnj,
    })
  }

  //取込情報一覧に1行目を選択する。
  if (tableData.value.yuuijikouList.length > 0) {
    onSelectRow(0)
  }
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function onSelectRow(index: number) {
  selectedItemIndex.value = index
  // 「上書ボタン」活性
  localOneway.mo00609OverwriteBtnOneway.disabled = false
  // 「追加ボタン」活性
  localOneway.mo00609AddBtnOneway.disabled = false
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 *  上書ボタン押下時の処理
 */
function onClick_Overwrite() {
  if (selectedItemIndex.value !== -1) {
    // 確認ダイアログ表示
    showOr21814Msg(t('message.i-cmn-10887'))
  }
}

//上書ボタンの選択結果を聞きます
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue.secondBtnClickFlg) {
      if (selectedItemIndex.value !== null) {
        rtnDataSet(Or28532Const.DEFAULT.REWRITER)
      }
    }
  }
)

/**
 * 入力エラーを表示する
 *
 * @param errormsg - メッセージ内容
 */
function showOr21814Msg(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      // 第2ボタンボタンラベル
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      // 第3ボタンラベル
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 *  追加ボタン押下時の処理
 */
function onClick_Add() {
  rtnDataSet(Or28532Const.DEFAULT.ADD)
}

// 戻り値設定 btnFlag: 1:上書き 2:追加
function rtnDataSet(btnFlag: string) {
  // 戻り値設定
  const rtnData: Or28532Type = {
    //・取込区分
    importKbn: btnFlag,
    //・画面.留意事項
    ryuuiKnj: tableData.value.yuuijikouList[selectedItemIndex.value].ryuuiKnj,
  }
  // 選択情報値戻り
  emit('update:modelValue', rtnData)

  // 画面閉じる
  close()
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row>
        <c-v-col>
          <c-v-data-table
            v-resizable-grid="{columnWidths:columnMinWidth}"
            :headers="headers"
            class="table-wrapper"
            hide-default-footer
            :items="tableDataFilter.yuuijikouList"
            fixed-header
            hover
            height="234px"
            :items-per-page="-1"
          >
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="onSelectRow(index)"
              >
                <!-- 認定期間 -->
                <td class="align-top">
                  <base-mo01337
                    :oneway-model-value="{ value: item.certificationEriod }"
                    style="width: 100%"
                  />
                </td>
                <!-- 要介護度 -->
                <td class="align-top">
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.yokaiKnj,
                     }"
                    style="width: 100%"
                  />
                </td>
                <!-- 留意事項 -->
                <td>
                  <span class="overflowText">{{ item.ryuuiKnj }}</span>
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :max-width="350"
                    :text="item.ryuuiKnj"
                    open-delay="200"
                  />
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00611OneWay.tooltipText"
            :text="localOneway.mo00611OneWay.tooltipText"
            :location="localOneway.mo00611OneWay.tooltipLocation"
            activator="parent"
          />
        </base-mo00611>
        <!-- 上書ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609OverwriteBtnOneway"
          @click="onClick_Overwrite"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00609OverwriteOneway.tooltipText"
            :text="localOneway.mo00609OverwriteOneway.tooltipText"
            :location="localOneway.mo00609OverwriteOneway.tooltipLocation"
            activator="parent"
          />
        </base-mo00609>
        <!-- 追加ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609AddBtnOneway"
          class="mx-2"
          @click="onClick_Add"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00609AddOneway.tooltipText"
            :text="localOneway.mo00609AddOneway.tooltipText"
            :location="localOneway.mo00609AddOneway.tooltipLocation"
            activator="parent"
          />
        </base-mo00609>
      </c-v-row>
    </template>
    <!-- 確認ダイアログ -->
    <g-base-or21814
      v-if="showDialogOr21814"
      v-bind="or21814"
    ></g-base-or21814>
  </base-mo00024>
</template>
<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
.table-wrapper .v-table__wrapper td {
  padding-left: 0;
}
.overflowText {
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 3;
  -webkit-line-clamp: 3;
  min-height: 64px;
}
:deep(.v-table--fixed-header) {
  position: sticky;
  // ジッタ除去
  top: 0px;
  z-index: 2;
}

.align-top{
  vertical-align: top;
}

.table-wrapper :deep(.v-row.v-row--no-gutters > .v-col) {
  padding: 0px !important;
  font-size: 14px;
}
</style>
