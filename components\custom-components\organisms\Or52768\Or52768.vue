<script setup lang="ts">
/**
 * Or52768：有機体：計画対象期間選択
 * GUI00971_総合計画
 *
 * @description
 * 計画対象期間選択の処理
 *
 * <AUTHOR> DO AI QUOC
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or52768Const } from './Or52768.constants'
import { Or52768Logic } from './Or52768.logic'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Or52768OnewayType, Or52768Type } from '~/types/cmn/business/components/Or52768Type'
import { useScreenTwoWayBind, useSetupChildProps } from '#imports'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or52768OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
const planTargetPeriodDataIndex = ref<number>(-1)

const or21814 = ref({ uniqueCpId: '' })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
})

const cssVars = computed(() => {
  return {
    '--custom-color': localOneway.kikanDataExitFlg ? 'inherit' : 'rgb(217, 2, 20)',
  }
})

const localOneway = reactive({
  computed: {
    cssVars() {
      return {
        '--custom-color': 'red',
        '--border-size': '2px',
      }
    },
  },
  or52768Oneway: {} as Or52768OnewayType,
  // 計画対象期間タイトルラベル
  mo00615Oneway: {
    itemLabel: t('label.planning-period'),
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1' }),
  } as Mo00615OnewayType,
  // 計画対象期間選択アイコンボタン
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  // 計画対象期間
  mo01338Oneway: {
    value: '',
    valueFontWeight: 'true',
    customClass: new CustomClass({
      itemClass: 'ml-1 font-color-red',
      labelClass: 'ma-1 font-color-red',
    }),
  } as Mo01338OnewayType,
  // 計画対象期間-前へアイコンボタン
  mo00009Twoway: {
    // デフォルト値の設定
    btnIcon: 'chevron_left',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01338Twoway: {
    value: '0 / 0',
    valueFontWeight: 'true',
  } as Mo01338OnewayType,
  // 計画対象期間-次へアイコンボタン
  mo00009Threeway: {
    // デフォルト値の設定
    btnIcon: 'chevron_right',
    density: 'compact',
  } as Mo00009OnewayType,
  kikanDataExitFlg: false,
})

/**************************************************
 * Pinia
 **************************************************/
useScreenTwoWayBind<Or52768Type>({
  cpId: Or52768Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted( () => {
  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogTitle: t('label.top-btn-title'),
      dialogText: '',
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      iconName: 'info',
      iconColor: 'rgb(var(--v-theme-blue-700))',
      iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
    },
  })
})

/**
 * 計画対象期間データ更新の監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.or52768Oneway = {
      ...newValue,
    }
    init()
  },
  { deep: true }
)

/**
 * 計画期間情報初期化
 */
function init() {
  //   ・計画期間が登録されている場合、計画期間リスト1件目.開始日 + " ～ " + 計画期間リスト1件目.終了日
  // ・上記以外の場合、固定文字「計画対象期間アイコンボタンから（改行）期間登録を行ってください」で表示※太字、赤色
  if (
    localOneway.or52768Oneway.planTargetPeriodData &&
    localOneway.or52768Oneway.planTargetPeriodData.totalCount > 0
  ) {
    localOneway.mo01338Oneway.value =
      localOneway.or52768Oneway.planTargetPeriodData.planTargetPeriod!
    localOneway.mo01338Twoway.value =
      localOneway.or52768Oneway.planTargetPeriodData.currentIndex +
      ' / ' +
      localOneway.or52768Oneway.planTargetPeriodData.totalCount
    localOneway.mo01338Oneway.valueFontWeight = undefined
    planTargetPeriodDataIndex.value = localOneway.or52768Oneway.planTargetPeriodData.currentIndex
    localOneway.kikanDataExitFlg = true
  } else {
    localOneway.mo01338Oneway.value = t('label.plan-no-data-label')
    localOneway.mo01338Twoway.value =
      localOneway.or52768Oneway.planTargetPeriodData.currentIndex +
      ' / ' +
      localOneway.or52768Oneway.planTargetPeriodData.totalCount
    localOneway.kikanDataExitFlg = false
  }
}
/**
 *  ボタン押下時の処理
 */
function onClickDialog() {
  //計画期間変更区分:0:選択先の期間ID
  Or52768Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      planTargetPeriodId: Or52768Logic.data.get(props.uniqueCpId)!.planTargetPeriodId,
      PlanTargetPeriodUpdateFlg: Or52768Const.UPDATE_CATEGORY_SELECT,
    },
  })
}

/**
 *  計画対象期間-前へアイコンボタンボタン押下時の処理
 */
function onClickMo00009Twoway() {
  // 1件目の計画対象期間データが表示されている状態
  if (planTargetPeriodDataIndex.value === 1) {
    // 確認ダイアログを開く
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.i-cmn-11262')
      },
    })
    return
  }

  //計画期間変更区分：1:選択している期間IDの前
  Or52768Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      planTargetPeriodId: Or52768Logic.data.get(props.uniqueCpId)!.planTargetPeriodId,
      PlanTargetPeriodUpdateFlg: Or52768Const.UPDATE_CATEGORY_PREVIOUS,
    },
  })
}
/**
 *  計画対象期間-次へアイコンボタンボタン押下時の処理
 *
 */
function onClickMo00009Threeway() {
  // 1件目の計画対象期間データが表示されている状態
  if (
    planTargetPeriodDataIndex.value === localOneway.or52768Oneway.planTargetPeriodData.totalCount
  ) {
    // 上書確認ダイアログ
    // 確認ダイアログを開く
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.i-cmn-11263')
      },
    })
    return
  }
  //計画期間変更区分設定：2:選択している期間IDの次
  Or52768Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      planTargetPeriodId: Or52768Logic.data.get(props.uniqueCpId)!.planTargetPeriodId,
      PlanTargetPeriodUpdateFlg: Or52768Const.UPDATE_CATEGORY_NEXT,
    },
  })
}

/**
 * wrap要素に適用するCSSクラス名を返す
 */
const getWrapCss = computed(() => {
  return localOneway.or52768Oneway.planTargetPeriodData?.totalCount === 0 ? 'align-center' : ''
})
</script>

<template>
  <c-v-row
    no-gutters
    class="text-center"
    :class="getWrapCss"
  >
    <!--計画対象期間  ラベル-->
    <c-v-col cols="auto">
      <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway" />
    </c-v-col>
    <!--計画対象期間アイコンボタン-->
    <c-v-col
      cols="auto"
      align-self="center"
      style="padding-top: 0px; padding-bottom: 0px; padding-right: 0px; padding-left: 0px"
    >
      <base-mo00009
        :oneway-model-value="localOneway.mo00009Oneway"
        @click="onClickDialog"
      />
    </c-v-col>
    <!-- 計画対象期間 -->
    <c-v-col
      id="k1"
      cols="auto"
      class="multiline-text"
      :style="cssVars"
      style="padding-top: 0px; padding-bottom: 0px; padding-right: 0px; padding-left: 0px"
    >
      <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway" />
    </c-v-col>
    <!-- 計画対象期間-前へアイコンボタン -->
    <c-v-col
      cols="auto"
      align-self="center"
      style="padding-top: 0px; padding-bottom: 0px; padding-right: 0px; padding-left: 0px"
    >
      <base-mo00009
        :oneway-model-value="localOneway.mo00009Twoway"
        @click="onClickMo00009Twoway()"
      />
    </c-v-col>
    <!-- 画対象期間-ページング -->
    <c-v-col cols="auto">
      <base-mo01338 :oneway-model-value="localOneway.mo01338Twoway" />
    </c-v-col>
    <!--計画対象期間-次へアイコンボタン-->
    <c-v-col
      cols="auto"
      align-self="center"
    >
      <base-mo00009
        :oneway-model-value="localOneway.mo00009Threeway"
        @click="onClickMo00009Threeway"
      />
    </c-v-col>
  </c-v-row>

  <!-- Or21814 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>

<style scoped lang="scss">
.font-color-red {
  color: rgb(217, 2, 20);
}
.text-center {
  align-items: baseline;
}
.multiline-text {
  white-space: pre-line;
  text-align: left;
}
</style>
