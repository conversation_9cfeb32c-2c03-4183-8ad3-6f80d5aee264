import { Or52063<PERSON>og<PERSON> } from '../Or52063/Or52063.logic'
import { Or52063Const } from '../Or52063/Or52063.constants'
import { Or09997Logic } from '../Or09997/Or09997.logic'
import { Or09997Const } from '../Or09997/Or09997.constants'
import { Or61587Const } from './Or61587.constants'
import { Or21735Const } from '~/components/base-components/organisms/Or21735/Or21735.constants'

import { useInitialize } from '~/composables/useComponentLogic'
import { Or21736Const } from '~/components/base-components/organisms/Or21736/Or21736.constants'
import { Or21737Const } from '~/components/base-components/organisms/Or21737/Or21737.constants'
import { Or21735Logic } from '~/components/base-components/organisms/Or21735/Or21735.logic'
import { Or21736Logic } from '~/components/base-components/organisms/Or21736/Or21736.logic'
import { Or21737Logic } from '~/components/base-components/organisms/Or21737/Or21737.logic'
import { Or21738Logic } from '~/components/base-components/organisms/Or21738/Or21738.logic'
import { Or21738Const } from '~/components/base-components/organisms/Or21738/Or21738.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

/**
 * Or61587:GUI00816_アセスメント(パッケージプラン)
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or61587Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or61587Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or21735Const.CP_ID(1) },
        { cpId: Or21736Const.CP_ID(1) },
        { cpId: Or21737Const.CP_ID(1) },
        { cpId: Or21738Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or52063Const.CP_ID(1) },
        { cpId: Or09997Const.CP_ID(1) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or21735Logic.initialize(childCpIds[Or21735Const.CP_ID(1)].uniqueCpId)
    Or21736Logic.initialize(childCpIds[Or21736Const.CP_ID(1)].uniqueCpId)
    Or21737Logic.initialize(childCpIds[Or21737Const.CP_ID(1)].uniqueCpId)
    Or21738Logic.initialize(childCpIds[Or21738Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or52063Logic.initialize(childCpIds[Or52063Const.CP_ID(1)].uniqueCpId)
    Or09997Logic.initialize(childCpIds[Or09997Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
}
