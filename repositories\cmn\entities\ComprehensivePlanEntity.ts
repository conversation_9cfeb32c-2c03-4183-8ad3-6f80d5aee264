import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * Or52786:（総合計画）総合計画
 * GUI00971_総合計画
 *
 * @description
 * 総合計画入力エンティティ
 *
 * <AUTHOR> DO AI QUOC
 */
export interface IComprehensivePlanInEntity extends InWebEntity {
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 種別ID
   */
  syubetsuId: string
}

/**
 * 総合計画出力エンティティ
 */
export interface IComprehensivePlanOutEntity extends OutWebEntity {
  /** data */
  data: {
    /**
     * 計画対象期間リスト
     */
    kakTaisyoKikanList: IKakTaisyoKikan[]
    /**
     * 総合計画履歴情報
     */
    sougouKakRirekiInfo: ISougouKakRireki[]
    /**
     * 総合計画詳細情報
     */
    sougouKakSyousaiInfo: ISougouKakSyousai[]
    /**
     * 期間管理フラグ
     */
    kikanKanriFlg: string
  }
}

/**
 * 計画対象期間情報
 */
export interface IKakTaisyoKikan {
  /**
   * ダミーカウント
   */
  dmyCnt: string
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
  /**
   * 事業名
   */
  jigyoKnj: string
  /**
   * 事業名（略称）
   */
  jigyoRyakuKnj: string
}

/**
 * 総合計画履歴情報
 */
export interface ISougouKakRireki {
  /**
   * カウンター
   */
  kkak1Id: string
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userid: string
  /**
   * 作成日
   */
  createYmd: string
  /**
   * 作成者ID
   */
  shokuId: string
  /**
   * 作成者名
   */
  shokuKnj: string
  /**
   * ケースNo.
   */
  caseNo: string
  /**
   * 2:H21/4 1:旧様式
   */
  kaiteiFlg: string
  /**
   * 初回作成日
   */
  syokaiYmd: string
  /**
   * 計画期間ID
   */
  sc1Id: string
}

/**
 * 総合計画詳細情報
 */
export interface ISougouKakSyousai {
  /**
   * カウンター
   */
  kkak1Id: string
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userid: string
  /**
   * 作成日
   */
  createYmd: string
  /**
   * 作成者
   */
  shokuId: string
  /**
   * ケースNo
   */
  caseNo: string
  /**
   * 要介護状態区分
   */
  yokaiKbn: string
  /**
   * 認定有効期間（開始）
   */
  ninStartYmd: string
  /**
   * 認定有効期間（終了）
   */
  ninEndYmd: string
  /**
   * 入所者の希望意向など勘案すべき事項
   */
  hoshin1Knj: string
  /**
   * 実施機関の意向や判断などに関する事項
   */
  hoshin2Knj: string
  /**
   * 援助に関する事項
   */
  hoshin3Knj: string
  /**
   * 社会活動復帰に関する事項
   */
  hoshin4Knj: string
  /**
   * 介護保険サービスに留意すべき事項
   */
  hoshin5Knj: string
  /**
   * 達成のための意欲等
   */
  iyokuKnj: string
  /**
   * 総合的な援助の方針
   */
  sogoHoshinKnj: string
  /**
   * 説明年月日
   */
  setumeiYmd: string
  /**
   * 様式
   */
  kaiteiFlg: string
  /**
   * 初回作成日
   */
  shokaiYmd: string
  /**
   * 更新回数
   */
  modifiedCnt: string
}
