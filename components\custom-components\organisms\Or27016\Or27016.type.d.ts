/**
 * Or27016:有機体:(利用票) カレンダー入力モーダル
 * GUI01158_カレンダー入力
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> 沈溢良
 */
export interface Or27016StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * 選ぶ日です
 */
export interface SelectedDate {
  /**
   * 当月の日
   */
  number: number
  /**
   * 当月の日付
   */
  date: Date
  /**
   * 当月
   */
  isCurrentMonth: boolean
}

/**
 * DataTableのデータ
 */
export interface TableData {
  /** 休日情報 */
  holidayList: HolidayList[]
  /** 利用票リスト */
  meisaiList: MeisaiList[]
}

/**
 * 休日情報リスト
 */
export interface HolidayList {
  /** 日 */
  day: string
  /** 曜日 */
  youbi: string
  /** 稼働フラグ */
  holiday: string
  /** 説明 */
  setsumei: string
  /** 指定事業者休日判定 */
  rest: string
}

/**
 * 利用票リスト
 */
export interface MeisaiList {
  /**	番号ID	*/
  id: string
  /**	ラジオ	*/
  dmySel: string
  /**	支援事業者ID	*/
  shienId: string
  /**	利用者ID	*/
  userId: string
  /**	サービス提供年月	*/
  yymmYm: string
  /**	サービス提供年月（変更日）	*/
  yymmD: string
  /**	サービス事業者ID	*/
  svJigyoId: string
  /**	サービス項目ID	*/
  svItemCd: string
  /**	枝番	*/
  edaNo: string
  /**	有効期間ID	*/
  termId: string
  /**	サービス事業者名	*/
  jigyoRyakuKnj: string
  /**	サービス種別コード	*/
  svType: string
  /**	サービスコード	*/
  scode: string
  /**	サービス開始時間	*/
  svStartTime: string
  /**	サービス終了時間	*/
  svEndTime: string
  /**	サービス単位数	*/
  svTani: string
  /**	予定回数０１	*/
  yday01: string
  /**	予定回数０２	*/
  yday02: string
  /**	予定回数０３	*/
  yday03: string
  /**	予定回数０４	*/
  yday04: string
  /**	予定回数０５	*/
  yday05: string
  /**	予定回数０６	*/
  yday06: string
  /**	予定回数０７	*/
  yday07: string
  /**	予定回数０８	*/
  yday08: string
  /**	予定回数０９	*/
  yday09: string
  /**	予定回数１０	*/
  yday10: string
  /**	予定回数１１	*/
  yday11: string
  /**	予定回数１２	*/
  yday12: string
  /**	予定回数１３	*/
  yday13: string
  /**	予定回数１４	*/
  yday14: string
  /**	予定回数１５	*/
  yday15: string
  /**	予定回数１６	*/
  yday16: string
  /**	予定回数１７	*/
  yday17: string
  /**	予定回数１８	*/
  yday18: string
  /**	予定回数１９	*/
  yday19: string
  /**	予定回数２０	*/
  yday20: string
  /**	予定回数２１	*/
  yday21: string
  /**	予定回数２２	*/
  yday22: string
  /**	予定回数２３	*/
  yday23: string
  /**	予定回数２４	*/
  yday24: string
  /**	予定回数２５	*/
  yday25: string
  /**	予定回数２６	*/
  yday26: string
  /**	予定回数２７	*/
  yday27: string
  /**	予定回数２８	*/
  yday28: string
  /**	予定回数２９	*/
  yday29: string
  /**	予定回数３０	*/
  yday30: string
  /**	予定回数３１	*/
  yday31: string
  /**	予定合計	*/
  ytotal: string
  /**	福祉用具貸与フラグ（予定）	*/
  yrentalF: string
  /**	小規模での提供区分	*/
  shoukiboKbn: string
  /**	３０日超過フラグ（予定）	*/
  yov30Fl: string
  /**	実績回数０１	*/
  jday01: string
  /**	実績回数０２	*/
  jday02: string
  /**	実績回数０３	*/
  jday03: string
  /**	実績回数０４	*/
  jday04: string
  /**	実績回数０５	*/
  jday05: string
  /**	実績回数０６	*/
  jday06: string
  /**	実績回数０７	*/
  jday07: string
  /**	実績回数０８	*/
  jday08: string
  /**	実績回数０９	*/
  jday09: string
  /**	実績回数１０	*/
  jday10: string
  /**	実績回数１１	*/
  jday11: string
  /**	実績回数１２	*/
  jday12: string
  /**	実績回数１３	*/
  jday13: string
  /**	実績回数１４	*/
  jday14: string
  /**	実績回数１５	*/
  jday15: string
  /**	実績回数１６	*/
  jday16: string
  /**	実績回数１７	*/
  jday17: string
  /**	実績回数１８	*/
  jday18: string
  /**	実績回数１９	*/
  jday19: string
  /**	実績回数２０	*/
  jday20: string
  /**	実績回数２１	*/
  jday21: string
  /**	実績回数２２	*/
  jday22: string
  /**	実績回数２３	*/
  jday23: string
  /**	実績回数２４	*/
  jday24: string
  /**	実績回数２５	*/
  jday25: string
  /**	実績回数２６	*/
  jday26: string
  /**	実績回数２７	*/
  jday27: string
  /**	実績回数２８	*/
  jday28: string
  /**	実績回数２９	*/
  jday29: string
  /**	実績回数３０	*/
  jday30: string
  /**	実績回数３１	*/
  jday31: string
  /**	実績合計	*/
  jtotal: string
  /**	福祉用具貸与フラグ（実績）	*/
  jrentalF: string
  /**	３０日超過フラグ（実績）	*/
  jov30Fl: string
  /**	予定転送日付	*/
  tensouTime: string
  /**	転送枝番のバックアップ	*/
  edaBack: string
  /**	ソート順	*/
  sortNo: string
  /**	サービス変更年月日	*/
  henkouTime: string
  /**	計画転送後修正有無フラグ	*/
  tensouFl: string
  /**	福祉用具貸与マスタID	*/
  fygId: string
  /**	連番	*/
  dmySeqNo: string
  /**	上限数	*/
  max: string
  /**	予定済みフラグ	*/
  yoteiZumiFlg: string
  /**	実績済みフラグ	*/
  jissekiZumiFlg: string
  /**	項目名	*/
  dmyItemnameKnj: string
  /**	サービス	*/
  dmyFormalnameKnj: string
  /**	合成識別区分	*/
  gouseiSikKbn: string
  /**	支給限度額対象区分	*/
  genTaiKbn: string
  /**	親レコード番号	*/
  oyaLineNo: string
  /**	算定単位	*/
  santeiTani: string
  /**	期間・時期	*/
  kikanJiki: string
  /**	回数・日数	*/
  kaisuNisu: string
  /**	点金区分	*/
  tenkintype: string
  /**	規定数	*/
  kiteiSu: string
  /**	開始時間	*/
  dmyStartTime: string
  /**	終了時間	*/
  dmyEndTime: string
  /**	模式	*/
  modFComp: string
  /**	選択	*/
  cbSel: string
  /**	予定	*/
  dmyDayY: string
  /**	実績	*/
  dmyDayJ: string
  /**	現行の行	*/
  dmyRow: string
  /**	事業者名	*/
  dmyJigyoNameKnj: string
  /**	ｓｖ単位	*/
  dmy0SvTani: string
  /**	ｓｖ単位（予定）	*/
  dmy0SvTanj: string
  /**	ハス	*/
  dmyRHasu: string
  /**	予定回数０１背景色	*/
  dmyBgColorY01: string
  /**	予定回数０２背景色	*/
  dmyBgColorY02: string
  /**	予定回数０３背景色	*/
  dmyBgColorY03: string
  /**	予定回数０４背景色	*/
  dmyBgColorY04: string
  /**	予定回数０５背景色	*/
  dmyBgColorY05: string
  /**	予定回数０６背景色	*/
  dmyBgColorY06: string
  /**	予定回数０７背景色	*/
  dmyBgColorY07: string
  /**	予定回数０８背景色	*/
  dmyBgColorY08: string
  /**	予定回数０９背景色	*/
  dmyBgColorY09: string
  /**	予定回数１０背景色	*/
  dmyBgColorY10: string
  /**	予定回数１１背景色	*/
  dmyBgColorY11: string
  /**	予定回数１２背景色	*/
  dmyBgColorY12: string
  /**	予定回数１３背景色	*/
  dmyBgColorY13: string
  /**	予定回数１４背景色	*/
  dmyBgColorY14: string
  /**	予定回数１５背景色	*/
  dmyBgColorY15: string
  /**	予定回数１６背景色	*/
  dmyBgColorY16: string
  /**	予定回数１７背景色	*/
  dmyBgColorY17: string
  /**	予定回数１８背景色	*/
  dmyBgColorY18: string
  /**	予定回数１９背景色	*/
  dmyBgColorY19: string
  /**	予定回数２０背景色	*/
  dmyBgColorY20: string
  /**	予定回数２１背景色	*/
  dmyBgColorY21: string
  /**	予定回数２２背景色	*/
  dmyBgColorY22: string
  /**	予定回数２３背景色	*/
  dmyBgColorY23: string
  /**	予定回数２４背景色	*/
  dmyBgColorY24: string
  /**	予定回数２５背景色	*/
  dmyBgColorY25: string
  /**	予定回数２６背景色	*/
  dmyBgColorY26: string
  /**	予定回数２７背景色	*/
  dmyBgColorY27: string
  /**	予定回数２８背景色	*/
  dmyBgColorY28: string
  /**	予定回数２９背景色	*/
  dmyBgColorY29: string
  /**	予定回数３０背景色	*/
  dmyBgColorY30: string
  /**	予定回数３１背景色	*/
  dmyBgColorY31: string
  /**	実績回数０１背景色	*/
  dmyBgColorJ01: string
  /**	実績回数０２背景色	*/
  dmyBgColorJ02: string
  /**	実績回数０３背景色	*/
  dmyBgColorJ03: string
  /**	実績回数０４背景色	*/
  dmyBgColorJ04: string
  /**	実績回数０５背景色	*/
  dmyBgColorJ05: string
  /**	実績回数０６背景色	*/
  dmyBgColorJ06: string
  /**	実績回数０７背景色	*/
  dmyBgColorJ07: string
  /**	実績回数０８背景色	*/
  dmyBgColorJ08: string
  /**	実績回数０９背景色	*/
  dmyBgColorJ09: string
  /**	実績回数１０背景色	*/
  dmyBgColorJ10: string
  /**	実績回数１１背景色	*/
  dmyBgColorJ11: string
  /**	実績回数１２背景色	*/
  dmyBgColorJ12: string
  /**	実績回数１３背景色	*/
  dmyBgColorJ13: string
  /**	実績回数１４背景色	*/
  dmyBgColorJ14: string
  /**	実績回数１５背景色	*/
  dmyBgColorJ15: string
  /**	実績回数１６背景色	*/
  dmyBgColorJ16: string
  /**	実績回数１７背景色	*/
  dmyBgColorJ17: string
  /**	実績回数１８背景色	*/
  dmyBgColorJ18: string
  /**	実績回数１９背景色	*/
  dmyBgColorJ19: string
  /**	実績回数２０背景色	*/
  dmyBgColorJ20: string
  /**	実績回数２１背景色	*/
  dmyBgColorJ21: string
  /**	実績回数２２背景色	*/
  dmyBgColorJ22: string
  /**	実績回数２３背景色	*/
  dmyBgColorJ23: string
  /**	実績回数２４背景色	*/
  dmyBgColorJ24: string
  /**	実績回数２５背景色	*/
  dmyBgColorJ25: string
  /**	実績回数２６背景色	*/
  dmyBgColorJ26: string
  /**	実績回数２７背景色	*/
  dmyBgColorJ27: string
  /**	実績回数２８背景色	*/
  dmyBgColorJ28: string
  /**	実績回数２９背景色	*/
  dmyBgColorJ29: string
  /**	実績回数３０背景色	*/
  dmyBgColorJ30: string
  /**	実績回数３１背景色	*/
  dmyBgColorJ31: string
  /**	可視単位	*/
  dmySvTaniVisible: string
  /**	月の一つ	*/
  dmyOneOfMonth: string
}
