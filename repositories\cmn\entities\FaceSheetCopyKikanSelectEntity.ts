import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 取得入力エンティティ
 */
export interface FaceSheetCopyKikanSelectInEntity extends InWebEntity {
  /** 種別ID */
  syubetuId: string
  /** 適用事業所IDリスト */
  jigyoIdList: { jigyoId: string }[]
  /** 施設ID */
  shisetuId: string
  /** 利用者ID */
  userid: string
}

/**
 * 取得出力エンティティ
 */
export interface FaceSheetCopyKikanSelectOutEntity extends OutWebEntity {
  /**
   * 取得出力データエンティティ
   */
  data: FaceSheetCopyKikanSelectDataEntity
}

/**
 * 取得出力データエンティティ
 */
export interface FaceSheetCopyKikanSelectDataEntity {
  /**
   * 期間情報リスト
   */
  kikanInfoList: FaceSheetCopyKikanInfo[]
}

/**
 * 期間情報
 */
export interface FaceSheetCopyKikanInfo {
  /**
   * 計画期間ID
   */
  sc1Id: string
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
  /**
   * 事業者名称ID
   */
  jigyoKnjId: string
  /**
   * 事業者名称(略称)
   */
  jigyoRyakuKnj: string
  /**
   * 期間内履歴数
   */
  cnt: string
}
