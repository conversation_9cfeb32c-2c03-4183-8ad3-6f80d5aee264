import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or11122:((計画ﾓﾆﾀﾘﾝｸﾞ)表示順変更モニタリング記録表)タブ
 * Gui01230_表示順変更モニタリング記録表
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or11122Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or11122', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * 長期目標区分：表示
     */
   export const NO_FLG = '1'
   /**
    * 実行確認マスタ
    */
   export const EXECUTE_CONFIRM_MASTER = '300'
    /**
     * 確認方法マスタ
     */
    export const CONFIRM_METHOD_MASTER = '301'
    /**
     * 意見マスタ
     */
    export const IKEN_MASTER = '302'
    /**
     * 充足度マスタ
     */
    export const JUSOKUDO_MASTER = '303'
    /**
     * 対応マスタ
     */
    export const TAIOU_MASTER = '304'
  }
}
