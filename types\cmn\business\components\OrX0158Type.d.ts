import type { Ref } from 'vue'
import type { MoInputCommonType } from '~/types/business/components/MoInputCommonType'
/**
 * OrX0158：有機体：入力支援付きセレクト
 *
 * 単方向バインドのデータ構造
 *
 * <AUTHOR>
 */
export interface OrX0158OnewayType extends MoInputCommonType {
  /**
   * 入力支援ボタン表示・非表示フラグ
   */
  showEditBtnFlg?: boolean
  /**
   * 入力支援ボタンのクラス
   */
  editBtnClass?: string
  /**
   * アイテム
   */
  items?: Record<Ref, Ref>

  /**
   * アイテムラベル
   */
  itemLabel?: string

  /**
   * 必須フラグ
   */
  isRequired?: boolean

  /**
   * ラベル表示フラグ
   */
  showItemLabel?: boolean

  /**
   * ラベルのフォントの太さ
   */
  itemLabelFontWeight?: string

  /**
   * カスタムクラス
   */
  customClass?: CustomClass

  /**
   * コンポーネント名
   */
  name?: string

  /**
   * コンポーネントのスタイル
   */
  variant?: string

  /**
   * 非活性
   */
  disabled?: boolean

  /**
   * アイテムのタイトル
   */
  itemTitle?: string

  /**
   * アイテムの値
   */
  itemValue?: string

  /**
   * クラス
   */
  class?: string

  /**
   * 背景色
   */
  bgColor?: string

  /**
   * 幅
   */
  width?: string

  /**
   * 読み取り専用
   */
  readonly?: bool
}
/**
 * OrX0158：有機体：入力支援付きセレクト双方向バインド
 */
export interface OrX0158Type {
  /**
   * 値
   */
  modelValue: string | undefined
}
