import { getSequencedCpId } from '#imports'

/**
 * Or28974:（認定調査）印刷設定モーダル
 * GUI01283_印刷設定
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> 劉顕康
 */
export namespace Or28974Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or28974', seq)

  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * empty
     */
    export const EMPTY = ''
  }

  /**
   * 単複数選択区分 0：単一
   */
  export const SELECT_CATEGORY_SINGLE = '0'

  /**
   * 単複数選択区分 1：複数
   */
  export const SELECT_CATEGORY_MULTIPLE = '1'

  /**
   * 機能名 PRT
   */
  export const FUNCTION_NAME_PRINT = 'PRT'

  /**
   * 個人情報表示フラグ 0
   */
  export const PERSONAL_INFO_SHOW_FLG_0 = '0'

  /**
   * 個人情報表示値 0
   */
  export const PERSONAL_INFO_SHOW_VALUE_0 = '0'

  /**
   * プリントナンバー 1
   */
  export const PRINT_NO_1 = '1'

  /**
   * プリントナンバー 2
   */
  export const PRINT_NO_2 = '2'

  /**
   * プリントナンバー 3
   */
  export const PRINT_NO_3 = '3'

  /**
   * プリントナンバー 4
   */
  export const PRINT_NO_4 = '4'

  /**
   * プリントナンバー 5
   */
  export const PRINT_NO_5 = '5'

  /**
   * プリントナンバー 6
   */
  export const PRINT_NO_6 = '6'

  /**
   * プリントナンバー 7
   */
  export const PRINT_NO_7 = '7'

  /**
   * プリントナンバー 8
   */
  export const PRINT_NO_8 = '8'

  /**
   * プリントナンバー 9
   */
  export const PRINT_NO_9 = '9'

  /**
   * プリントナンバー 10
   */
  export const PRINT_NO_10 = '10'

  /**
   * プリントナンバー 11
   */
  export const PRINT_NO_11 = '11'

  /**
   * プリントナンバー 12
   */
  export const PRINT_NO_12 = '12'

  /**
   * プリントナンバー 13
   */
  export const PRINT_NO_13 = '13'

  /**
   * プリントナンバー 14
   */
  export const PRINT_NO_14 = '14'

  /**
   * プリントナンバー 1-7
   */
  export const PRINT_NO_1_TO_7 = [
    PRINT_NO_1,
    PRINT_NO_2,
    PRINT_NO_3,
    PRINT_NO_4,
    PRINT_NO_5,
    PRINT_NO_6,
    PRINT_NO_7,
  ]

  /**
   * 帳票ID: PDFダウンロード
   */
  export namespace PDF_DOWNLOAD_REPORT_ID {
    /**
     * 認定調査票(概況調査)
     */
    export const GENER_ALL = 'GeneralSituationSurveyReport'
    /**
     * 認定調査票(基本調査１)
     */
    export const BASIC_1 = 'BasicSurveyOneReport'
    /**
     * 認定調査票(基本調査２)
     */
    export const BASIC_2 = 'BasicSurveyTwoReport'
    /**
     * 認定調査票(基本調査３)
     */
    export const BASIC_3 = 'BasicSurveyThreeReport'
    /**
     * 認定調査票(基本調査４)
     */
    export const BASIC_4 = 'BasicSurveyFourReport'
    /**
     * 認定調査票(特記事項)
     */
    export const BASIC_SPEC = 'SpecialNoteMindH24Report'
    /**
     * 認定調査票(概況調査～特記事項)
     */
    export const CERTIFIC = 'CertificationSurveyH24AllReport'
    /**
     * １次判定調査票(H24.4～)
     */
    export const FIRST = 'FirstDecisionSurveyH24Report'
    /**
     * 調査内容一覧票
     */
    export const SURVEY = 'SurveyContentsListH24Report'
    /**
     * 認定調査票(特記事項/別様式)
     */
    export const SPECIA = 'SpecialNoteMindByStyleReport'
    /**
     * 要介護度による集計表
     */
    export const YOKAIGODO = 'YokaigodoShukeiH24Report'
    /**
     * 要介護度一覧表
     */
    export const LEVELOF = 'LevelOfCareRequiredListReport'
    /**
     * 調査員別調査対象者一覧表
     */
    export const INVESTIGA = 'InvestigatorsListH24Report'
    /**
     * 認定調査票(基本調査５)
     */
    export const BASIC_5 = 'BasicSurveyFiveReport'
    /**
     * 認定調査票(概況調査)
     */
    export const GENER_ALL_R34 = 'GeneralSituationSurveyR34Report'
    /**
     * 認定調査票(基本調査１)
     */
    export const BASIC_1_R34 = 'BasicSurveyOneR34Report'
    /**
     * 認定調査票(基本調査２)
     */
    export const BASIC_2_R34 = 'BasicSurveyTwoR34Report'
    /**
     * 認定調査票(基本調査３)
     */
    export const BASIC_3_R34 = 'BasicSurveyThreeR34Report'
    /**
     * 認定調査票(基本調査４)
     */
    export const BASIC_4_R34 = 'BasicSurveyFourR34Report'
    /**
     * 認定調査票(特記事項)
     */
    export const BASIC_SPEC_R34 = 'SpecialNoteMindR34Report'
    /**
     * 認定調査票(概況調査～特記事項)
     */
    export const CERTIFIC_R34 = 'CertificationSurveyR3AllReport'
    /**
     * １次判定調査票(H24.4～)
     */
    export const FIRST_R34 = 'FirstDecisionSurveyR34Report'
    /**
     * 調査内容一覧票
     */
    export const SURVEY_R34 = 'SurveyContentsListR34Report'
    /**
     * 認定調査票(特記事項/別様式)
     */
    export const SPECIA_R34 = 'SpecialNoteMindByStyleR34Report'
    /**
     * 要介護度による集計表
     */
    export const YOKAIGODO_R34 = 'YokaigodoShukeiR3Report'
    /**
     * 要介護度一覧表
     */
    export const LEVELOF_R34 = 'LevelOfCareRequiredListR34Report'
    /**
     * 調査員別調査対象者一覧表
     */
    export const INVESTIGA_R34 = 'InvestigatorsListR34Report'
    /**
     * 認定調査票(基本調査５)
     */
    export const BASIC_5_R34 = 'BasicSurveyFiveR34Report'
  }

  /**
   * 集計する要介護度 7:要介護５
   */
  export const YOUKAIGODO_CODE_7 = '7'

  /**
   * 集計する要介護度 0:全て
   */
  export const YOUKAIGODO_CODE_ALL = '0'

  /**
   * 日付印刷区分 2：指定日を印刷する
   */
  export const DATE_PRINT_CATEGORY_2 = '2'

  /**
   * 選択モード 12
   */
  export const SELECT_MODE_12 = '12'

  /**
   * 未設定行表示フラグ 1
   */
  export const MISETTEI_SHOW_FLG_1 = '1'

  /**
   * フィルターフラグ
   */
  export const FILTER_FLG_1 = '1'

  /**
   * 表示するカラム
   */
  export const SHOW_COL_VALUE = 'shokushu_id'

  /**
   * 期間管理フラグ 1:管理する
   */
  export const PERIOD_MANAGE_FLG_MANAGE = '1'

  /**
   * 改訂 H21
   */
  export const H21 = 'H21'

  /**
   * 改訂 R3
   */
  export const R3 = 'R3'

  /**
   * 2012/04/01
   */
  export const DATE = '2012/04/01'
}
