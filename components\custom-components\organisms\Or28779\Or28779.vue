<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { OrX0130Const } from '../OrX0130/OrX0130.constants'
import { OrX0130Logic } from '../OrX0130/OrX0130.logic'
import { OrX0133Const } from '../OrX0133/OrX0133.constants'
import { OrX0133Logic } from '../OrX0133/OrX0133.logic'
import { OrX0117Const } from '../OrX0117/OrX0117.constants'
import { OrX0117Logic } from '../OrX0117/OrX0117.logic'
import { Or28779Const } from './Or28779.constants'
import type { DataRow, Or28779StateType } from './Or28779.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import {
  computed,
  onBeforeMount,
  reactive,
  ref,
  useScreenOneWayBind,
  useSetupChildProps,
  watch,
} from '#imports'
import type { Or28779Type, Or28779OnewayType } from '~/types/cmn/business/components/Or28779Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { Mo01334OnewayType, Mo01334Type } from '~/types/business/components/Mo01334Type'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { PrintSettingsInfoComUpdateInEntity } from '~/repositories/cmn/entities/printSettingsInfoComUpdate'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type {
  LedgerInitializeDataComSelectInEntity,
  LedgerInitializeDataComSelectOutEntity,
} from '~/repositories/cmn/entities/ledgerInitializeDataComSelect'
import type {
  OrX0133OnewayType,
  OrX0133TableData,
} from '~/types/cmn/business/components/OrX0133Type'
import { dateUtils } from '~/utils/dateUtils'
import type {
  FilterForUserIdComSelectInEntity,
  FilterForUserIdComSelectOutEntity,
} from '~/repositories/cmn/entities/filterForUserIdComSelect'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type {
  FaceSheetHistoryInfo,
  GetInitialPrintSettingsInfoSelectInEntity,
  GetInitialPrintSettingsInfoSelectOutEntity,
  LedgerInitData,
  StaffInfo,
} from '~/repositories/cmn/entities/GetInitialPrintSettingsInfoSelectEntity'
import type { Mo01408OnewayType } from '~/types/business/components/Mo01408Type'
import type {
  FaceSheetHistoryInfoSelectInEntity,
  FaceSheetHistoryInfoSelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheetHistoryInfoSelectEntity'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
/**
 * Or28779:（フェイスシート(パッケージプラン)）印刷設定モーダル
 * GUI00651_［印刷設定］画面
 *
 * @description
 *［印刷設定］画面では、印刷設定画面を表示します。
 *
 * <AUTHOR> 劉顕康
 */

const { t } = useI18n()
const { convertDateToSeireki } = dateUtils()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or28779Type
  onewayModelValue: Or28779OnewayType
  uniqueCpId: string
}

/**
 * 引継情報を取得する
 */
const props = defineProps<Props>()

const orx0145 = ref({ uniqueCpId: '' })

/**
 * default値
 */
const defaultOnewayModelValue: Or28779OnewayType = {
  /** システムコード */
  systemCode: '',
  /** システム略称 */
  systemNameShort: '',
  /** 計画期間管理フラグ */
  planPeriodManagementFlg: '',
  /** 法人ID */
  corporationId: '',
  /** 施設ID */
  facilityId: '',
  /** 事業者ID */
  officeId: '',
  /** 利用者ID */
  userId: '',
  /** 職員ID */
  staffId: '',
  /** 担当ケアマネ設定フラグ */
  careManagerSettingFlg: '',
  /** 担当者ID */
  tantoshaId: '',
  /** セクション名 */
  sectionName: '',
  /** 選択帳票番号 */
  selectedLedgerNo: '',
  /** 処理年月日 */
  processYmd: '',
  /** 50音行番号 */
  gojuuonRowNo: '',
  /** 50音母音 */
  gojuuonKana: [''],
  /** 履歴ID */
  historyId: '',
  /** 利用者情報リスト */
  userInfoList: [],
  /** アセスメントID */
  assessmentId: '',
}

const localOneway = reactive({
  or28779: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo00039OnewayDate: {
    name: 'date',
    items: [],
    showItemLabel: false,
    hideDetails: true,
    inline: false,
  } as Mo00039OnewayType,
  mo00039OnewayUser: {
    name: 'user',
    itemLabel: t('label.printer-user-select'),
    items: [],
    showItemLabel: true,
    hideDetails: true,
    inline: true,
    customClass: new CustomClass({
      outerClass: 'ma-0',
    }),
  } as Mo00039OnewayType,
  mo00039OnewayHistory: {
    name: 'history',
    itemLabel: t('label.printer-history-select'),
    items: [],
    showItemLabel: true,
    hideDetails: true,
    inline: true,
    customClass: new CustomClass({
      outerClass: 'ml-11',
    }),
  } as Mo00039OnewayType,
  mo01338OnewayTitle: {
    value: '',
    customClass: {
      outerClass: 'mr-0',
    },
  } as Mo01338OnewayType,
  mo01338OnewayH21: {
    value: t('label.valid-only-h21-revision'),
    customClass: new CustomClass({
      itemStyle: 'color: #A8A8A8; font-size: smaller;',
    }),
  } as Mo01338OnewayType,
  // PDFダウンロードボタン
  mo00609OneWay: {
    btnLabel: t('btn.pdf-download'),
    disabled: !(await hasPrintAuth()),
  } as Mo00609OnewayType,
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-0' }),
  } as OrX0145OnewayType,
})

/**
 * 担当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

const local = reactive({
  /** 帳票INIデータリスト */
  iniDataList: [] as LedgerInitData[],
  /** フェースシート履歴リスト */
  rirekiList: [] as FaceSheetHistoryInfo[],
  /** 利用者絞込リスト */
  userSelList: [] as { userid: string }[],
  /** 職員基本リスト */
  selList: [] as StaffInfo[],
  mo00039DateModel: Or28779Const.DATE_PRINT_CATEGORY_2,
  mo00039UserModel: Or28779Const.SELECT_CATEGORY_SINGLE,
  mo00039HistoryModel: Or28779Const.SELECT_CATEGORY_SINGLE,
  // mo00045ModelTitle: {
  //   value: '',
  // },
  // コードマスタ 改訂区分
  codeMasterValuesKaitei: [] as CodeType[],
  /** 担当者ID */
  tantoshaId: '',
  /** 基準日 */
  mo00020modelKijun: {
    value: '',
  } as Mo00020Type,
  /** 担当ケアマネ */
  mo01408modelCare: {
    value: '',
  },
  /** 指定日 */
  mo00020modelShitei: {
    value: '',
  } as Mo00020Type,
  mo00018ModelSheet: {
    modelValue: false,
  } as Mo00018Type,
  mo00018ModelCreater: {
    modelValue: false,
  } as Mo00018Type,
  mo00018ModelCreateDate: {
    modelValue: false,
  } as Mo00018Type,
})

/**
 * ダイアログ設定
 */
const mo00024Oneway = reactive({
  width: '1400px',
  maxWidth: '1400px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or28779',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or28779ToolBar',
    // ツールバータイトルの左寄せ
    toolbarTitleCenteredFlg: false,
    showToolbar: true,
    showCardActions: true,
    scrollable: false,
    cardTextClass: 'or28779-nopadding-content',
  },
}) as Mo00024OnewayType

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**
 * Orx0130 oneway
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width: 300px;',
  focusSettingInitial: localOneway.or28779.gojuuonKana,
})

// 利用者一覧の幅
const userCols = ref('6')

/**
 * Orx0133 oneway
 */
const orX0133Oneway = reactive<OrX0133OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: localOneway.or28779.planPeriodManagementFlg,
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: Or28779Const.SELECT_CATEGORY_SINGLE,
  tableStyle: 'width:335px; height: 512px',
  itemShowFlg: {
    createYmdShokuKnjFlg: true,
    caseNoFlg: true,
    kaisuuFlg: true,
    kaiteiKnjFlg: true,
  },
  rirekiList: [] as OrX0133TableData[],
})

const orX0117Oneway = reactive<OrX0117OnewayType>({
  type: local.mo00039UserModel,
  historyList: [] as OrX0117History[],
})

const showDialogOrx0117 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  担当ケアマネ選択非活性化
 */
const careManagerDisabled = computed(() => {
  // 親画面.担当ケアマネ設定フラグが0より大きい、かつ、親画面.担当者IDが0以外の場合
  return localOneway.or28779.careManagerSettingFlg > '0' && localOneway.or28779.tantoshaId !== '0'
})

/**
 * 担当ケアマネ
 */
const mo01408OnewayCare = reactive({
  itemLabel: '',
  showItemLabel: false,
  width: '200px',
  items: [],
  disabled: careManagerDisabled.value,
}) as Mo01408OnewayType

/**
 * 年月日テキストフィールドの単方向モデル
 */
const mo00020OnewayNoArrow = ref<Mo00020OnewayType>({
  isRequired: false,
  showItemLabel: false,
  hideDetails: true,
  maxlength: '10',
  width: '130px',
  showSelectArrow: false,
  customClass: new CustomClass({
    outerClass: 'ml-0',
  }),
  mo01343Oneway: {
    selectMode: '0',
    closedDayDisplayOffice: '',
    rangeSelectDefaultPeriod: 0,
  },
})

/**
 * 基準日
 */
const mo00020OnewayKijun = ref<Mo00020OnewayType>({
  isRequired: false,
  itemLabel: t('label.base-date'),
  showItemLabel: true,
  isVerticalLabel: true,
  hideDetails: true,
  width: '130px',
  showSelectArrow: true,
  customClass: new CustomClass({
    outerClass: 'ml-0',
  }),
  mo01343Oneway: {
    selectMode: '0',
    closedDayDisplayOffice: '',
    rangeSelectDefaultPeriod: 0,
  },
})

/**
 * 記入用シートを印刷する
 */
const mo00018OnewaySheet = ref<Mo00018OnewayType>({
  checkboxLabel: t('label.printer-entry-sheet'),
  isVerticalLabel: false,
})

/**
 * 作成者を印刷する
 */
const mo00018OnewayCreater = ref<Mo00018OnewayType>({
  checkboxLabel: t('label.print-author'),
  isVerticalLabel: false,
})

/**
 * 初回作成日を印刷する
 */
const mo00018OnewayCreateDate = ref<Mo00018OnewayType>({
  checkboxLabel: t('label.print-first-create-date'),
  isVerticalLabel: false,
})

/**
 * 出力帳票名一覧
 */
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'defPrtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 637,
})

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**************************************************
 * 変数定義
 **************************************************/
/**
 * mo00024
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28779Const.DEFAULT.IS_OPEN,
})

// 子コンポーネント用変数
// Or21813
const or21813 = ref({ uniqueCpId: '' })
// Or21814
const or21814 = ref({ uniqueCpId: '' })
// OrX0130
const orX0130 = ref({ uniqueCpId: '' })
// OrX0133
const orX0133 = ref({ uniqueCpId: '' })
// OrX0117
const orX0117 = ref({ uniqueCpId: '' })

/**************************************************
 * Pinia
 **************************************************/
/**
 * setState
 */
const { setState } = useScreenOneWayBind<Or28779StateType>({
  cpId: Or28779Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * isOpen
     *
     * @param value - 開閉フラグ
     */
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or28779Const.DEFAULT.IS_OPEN
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(1)]: or21813.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [OrX0130Const.CP_ID(1)]: orX0130.value,
  [OrX0133Const.CP_ID(1)]: orX0133.value,
  [OrX0117Const.CP_ID(1)]: orX0117.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onBeforeMount(async () => {
  await initCodes()
  await init()
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // 性別区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
    // 改訂区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_REVISION_CATEGORY_PRINT_SETTING },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分ラジオ
  localOneway.mo00039OnewayDate.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  // 利用者選択ラジオ
  localOneway.mo00039OnewayUser.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  // 履歴選択ラジオ
  localOneway.mo00039OnewayHistory.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  // 改訂様式リスト
  local.codeMasterValuesKaitei = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_REVISION_CATEGORY_PRINT_SETTING
  )
}

/**
 * 初期情報取得
 */
async function init() {
  // 指定日
  local.mo00020modelShitei.value = convertDateToSeireki(new Date())
  // 基準日
  local.mo00020modelKijun.value = localOneway.or28779.processYmd

  // リクエストパラメータ
  const inputData: GetInitialPrintSettingsInfoSelectInEntity = {
    /** システムコード */
    sysCd: localOneway.or28779.systemCode,
    /** システム略称 */
    sysRyaku: localOneway.or28779.systemNameShort,
    /** 機能名 */
    kinounameKnj: Or28779Const.FUNCTION_NAME_PRINT,
    /** 計画期間管理フラグ */
    kikanFlg: localOneway.or28779.planPeriodManagementFlg,
    /** 法人ID */
    houjinId: localOneway.or28779.corporationId,
    /** 施設ID */
    shisetuId: localOneway.or28779.facilityId,
    /** 事業者ID */
    svJigyoId: localOneway.or28779.officeId,
    /** 利用者ID */
    userId: localOneway.or28779.userId,
    /** 職員ID */
    shokuId: localOneway.or28779.staffId,
    /** 担当者ID */
    tantoId: careManagerDisabled.value ? localOneway.or28779.tantoshaId : '0',
    /** 処理年月日 */
    appYmd: localOneway.or28779.processYmd,
    /** セクション名 */
    sectionName: localOneway.or28779.sectionName,
    /** 選択帳票番号 */
    choIndex: localOneway.or28779.selectedLedgerNo,
    /** 個人情報表示フラグ */
    kojinhogoFlg: Or28779Const.PERSONAL_INFO_SHOW_FLG_0,
    /** 個人情報表示値 */
    sectionAddNo: Or28779Const.PERSONAL_INFO_SHOW_VALUE_0,
  }

  // 初期情報取得
  const ret: GetInitialPrintSettingsInfoSelectOutEntity = await ScreenRepository.select(
    'getInitialPrintSettingsInfoSelect',
    inputData
  )
  // 担当ケアマネ
  orX0145Type.value.value = {
    counter: Or28779Const.EMPTY,
    chkShokuId: Or28779Const.EMPTY,
    houjinId: Or28779Const.EMPTY,
    shisetuId: Or28779Const.EMPTY,
    svJigyoId: Or28779Const.EMPTY,
    shokuin1Kana: Or28779Const.EMPTY,
    shokuin2Kana: Or28779Const.EMPTY,
    shokuin1Knj: Or28779Const.EMPTY,
    shokuin2Knj: Or28779Const.EMPTY,
    sex: Or28779Const.EMPTY,
    birthdayYmd: Or28779Const.EMPTY,
    zip: Or28779Const.EMPTY,
    kencode: Or28779Const.EMPTY,
    citycode: Or28779Const.EMPTY,
    areacode: Or28779Const.EMPTY,
    addressKnj: Or28779Const.EMPTY,
    tel: Or28779Const.EMPTY,
    kaikeiId: Or28779Const.EMPTY,
    kyuyoKbn: Or28779Const.EMPTY,
    partKbn: Or28779Const.EMPTY,
    inYmd: Or28779Const.EMPTY,
    outYmd: Or28779Const.EMPTY,
    shozokuId: Or28779Const.EMPTY,
    shokushuId: Or28779Const.EMPTY,
    shokuId: Or28779Const.EMPTY,
    timeStmp: Or28779Const.EMPTY,
    delFlg: Or28779Const.EMPTY,
    shokuNumber: Or28779Const.EMPTY,
    caremanagerKbn: Or28779Const.EMPTY,
    shokuType1: Or28779Const.EMPTY,
    shokuType2: Or28779Const.EMPTY,
    kGroupid: Or28779Const.EMPTY,
    bmpPath: Or28779Const.EMPTY,
    bmpYmd: Or28779Const.EMPTY,
    hankoPath: Or28779Const.EMPTY,
    kojinPath: Or28779Const.EMPTY,
    keitaitel: Or28779Const.EMPTY,
    eMail: Or28779Const.EMPTY,
    senmonNo: Or28779Const.EMPTY,
    sgfFlg: Or28779Const.EMPTY,
    srvSekiKbn: Or28779Const.EMPTY,
    shokushuId2: Or28779Const.EMPTY,
    shokushuId3: Or28779Const.EMPTY,
    shokushuId4: Or28779Const.EMPTY,
    shokushuId5: Or28779Const.EMPTY,
    shikakuId1: Or28779Const.EMPTY,
    shikakuId2: Or28779Const.EMPTY,
    shikakuId3: Or28779Const.EMPTY,
    shikakuId4: Or28779Const.EMPTY,
    shikakuId5: Or28779Const.EMPTY,
    kyuseiFlg: Or28779Const.EMPTY,
    kyuseiKana: Or28779Const.EMPTY,
    kyuseiKnj: Or28779Const.EMPTY,
    sort: Or28779Const.EMPTY,
    selfNumber: Or28779Const.EMPTY,
    ichiranShokushuIdNm: Or28779Const.EMPTY,
    shokushuId2Nm: Or28779Const.EMPTY,
    shokushuId3Nm: Or28779Const.EMPTY,
    shokushuId4Nm: Or28779Const.EMPTY,
    shokushuId5Nm: Or28779Const.EMPTY,
    stopFlg: Or28779Const.EMPTY,
    shokuinKnj: '',
    shokuinKana: Or28779Const.EMPTY,
    title: Or28779Const.EMPTY,
    value: Or28779Const.EMPTY,
  } as TantoCmnShokuin

  // 帳票INIデータリスト
  local.iniDataList = ret.data.iniDataList ?? []
  // フェースシート履歴リスト
  local.rirekiList = ret.data.rirekiList ?? []
  // 利用者絞込リスト
  local.userSelList =
    ret.data.userSelList.map((item) => {
      return { userid: item.userId }
    }) ?? []

  // 職員基本リスト
  local.selList = ret.data.selList ?? []
  // 担当ケアマネ
  local.selList.forEach((item) => {
    mo01408OnewayCare.items?.push({ title: item.shokuinKnj, value: item.chkShokuId })
  })

  // 出力帳票一覧の設定
  ret.data.choPrtList.forEach((item, index) => {
    const rowData = { id: String(index + 1), ...item } as DataRow
    mo01334Oneway.value.items.push(rowData)
  })
  // 出力帳票一覧明細行選択
  mo01334Type.value.value = localOneway.or28779.selectedLedgerNo

  // タイトル
  localOneway.mo01338OnewayTitle.value = selectedRowData.value.prtTitle
  // 日付印刷区分
  local.mo00039DateModel = selectedRowData.value.prndate

  // 記入用シートを印刷する
  local.mo00018ModelSheet.modelValue = false
  // 作成者を印刷する
  local.mo00018ModelCreater.modelValue = Boolean(Number(selectedRowData.value.param05))
  // 初回作成日を印刷する
  local.mo00018ModelCreateDate.modelValue = Boolean(Number(selectedRowData.value.param06))

  // 親画面.処理年月日が""でない場合
  if (localOneway.or28779.processYmd !== '') {
    // 親画面.担当ケアマネ設定フラグが0より大きい、かつ、親画面.担当者IDが0以外の場合
    if (careManagerDisabled.value) {
      // 担当ケアマネ
      local.mo01408modelCare.value = localOneway.or28779.tantoshaId
      local.tantoshaId = localOneway.or28779.tantoshaId
    } else {
      // 担当ケアマネ
      local.mo01408modelCare.value = ''
      local.tantoshaId = '0'
    }
  }
}

/**
 * 職員基本リストから職員名を取得
 *
 * @param staffId - 職員id
 */
function getStaffName(staffId: string) {
  return local.selList.find((item) => item.chkShokuId === staffId)?.shokuinKnj ?? ''
}

/**
 *  出力帳票一覧明細選択変更
 */
watch(
  () => mo01334Type.value.value,
  async (newValue, oldValue) => {
    // 帳票タイトルが空白以外の場合
    if (oldValue !== '' && localOneway.mo01338OnewayTitle.value.trim().length !== 0) {
      const oldRow = mo01334Oneway.value.items.find((item) => item.id === oldValue) as DataRow
      // 帳票タイトル = 帳票タイトル
      oldRow.prtTitle = localOneway.mo01338OnewayTitle.value
    }
    // 切替前の情報を退避する
    saveRowStatus(oldValue)

    // 切替後の情報を設定する
    // 帳票イニシャライズデータを取得する
    await getReportInitData()

    // タイトル
    localOneway.mo01338OnewayTitle.value = selectedRowData.value.prtTitle
    // 日付印刷区分
    local.mo00039DateModel = selectedRowData.value.prndate

    // 選択帳票番号 = 1の場合
    if (selectedRowData.value.id === Or28779Const.LEDGER_NUMBER_1) {
      // 作成者を印刷する
      local.mo00018ModelCreater.modelValue = true
      mo00018OnewayCreater.value.disabled = true
      // 初回作成日を印刷する
      local.mo00018ModelCreateDate.modelValue = true
      mo00018OnewayCreateDate.value.disabled = true
    } else {
      // 作成者を印刷する
      local.mo00018ModelCreater.modelValue = Boolean(Number(selectedRowData.value.param05))
      mo00018OnewayCreater.value.disabled = false
      // 初回作成日を印刷する
      local.mo00018ModelCreateDate.modelValue = Boolean(Number(selectedRowData.value.param06))
      mo00018OnewayCreateDate.value.disabled = false
    }
  }
)

/**
 * 帳票イニシャライズデータを取得する。
 */
async function getReportInitData() {
  const inputData: LedgerInitializeDataComSelectInEntity = {
    // システムコード
    sysCd: localOneway.or28779.systemCode,
    // 機能名
    kinounameKnj: Or28779Const.FUNCTION_NAME_PRINT,
    // 職員ID
    shokuId: localOneway.or28779.staffId,
    // セクション
    sectionKnj: selectedRowData.value.choPro,
    // 個人情報表示フラグ
    kojinhogoFlg: Or28779Const.PERSONAL_INFO_SHOW_FLG_0,
    // 個人情報表示値
    sectionAddNo: Or28779Const.PERSONAL_INFO_SHOW_VALUE_0,
  }
  // バックエンドAPIから初期情報取得
  const ret: LedgerInitializeDataComSelectOutEntity = await ScreenRepository.select(
    'ledgerInitializeDataComSelect',
    inputData
  )
  local.iniDataList = ret.data.iniDataObject
}

/**
 * 出力帳票一覧の選択行変更時、画面の入力内容を行のデータとして保存する
 *
 * @param oldId - 変更前のID
 */
function saveRowStatus(oldId: string) {
  if (oldId === '') {
    return
  }

  const oldRow = mo01334Oneway.value.items.find((item) => item.id === oldId) as DataRow
  // 日付表示有無 = 日付印刷区分
  oldRow.prndate = local.mo00039DateModel
  // パラメータ05 = 画面.作成者を印刷する
  oldRow.param05 = String(Number(local.mo00018ModelCreater.modelValue))
  // パラメータ06 = 画面.初回作成日を印刷する
  oldRow.param06 = String(Number(local.mo00018ModelCreateDate.modelValue))
}

/**
 * 選択行のデータ
 */
const selectedRowData = computed(() => {
  return mo01334Oneway.value.items.find((item) => item.id === mo01334Type.value.value) as DataRow
})

// 「利用者選択」ラジオボタン選択変更
watch(
  () => local.mo00039UserModel,
  (newValue) => {
    orX0130Oneway.selectMode = newValue

    // 利用者選択が単一の場合
    if (newValue === Or28779Const.SELECT_CATEGORY_SINGLE) {
      // 幅の調整
      userCols.value = '6'

      // 利用者選択が単一で、かつ履歴単複数選択が”単一”の場合、記入用シートを印刷するを活性表示にする
      if (local.mo00039HistoryModel === Or28779Const.SELECT_CATEGORY_SINGLE) {
        mo00018OnewaySheet.value.disabled = false
      } else {
        mo00018OnewaySheet.value.disabled = true
      }

      orX0130Oneway.tableStyle = 'width: 300px'
    } else {
      // 幅の調整
      userCols.value = '11'

      // 記入用シートを印刷するをチェックオフにする
      local.mo00018ModelSheet.modelValue = false
      // 記入用シートを印刷するを非活性にする
      mo00018OnewaySheet.value.disabled = true

      orX0130Oneway.tableStyle = 'width: 430px'
    }
  }
)

// 「履歴選択」ラジオボタン選択変更
watch(
  () => local.mo00039HistoryModel,
  (newValue) => {
    // 履歴選択が単一の場合
    if (newValue === Or28779Const.SELECT_CATEGORY_SINGLE) {
      // 記入用シートを印刷するを活性表示にする
      mo00018OnewaySheet.value.disabled = false

      // 履歴一覧明細が単一選択モードにする
      orX0133Oneway.singleFlg = Or28779Const.SELECT_CATEGORY_SINGLE
    } else {
      // 記入用シートを印刷するをチェックオフにする
      local.mo00018ModelSheet.modelValue = false
      // 記入用シートを印刷するを非活性にする
      mo00018OnewaySheet.value.disabled = true

      // 履歴一覧明細を複数選択モードにする
      orX0133Oneway.singleFlg = Or28779Const.SELECT_CATEGORY_MULTIPLE
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.userList !== undefined && newValue.userList.length > 0) {
      // 利用者選択
      // 利用者選択方法が「単一」の場合
      if (local.mo00039UserModel === Or28779Const.SELECT_CATEGORY_SINGLE) {
        await getFaceSheetHistoryInfoList(newValue.userList[0].selfId)
      }
    }
  }
)

/**
 * 「担当ケアマネ」の監視
 */
watch(
  () => local.mo01408modelCare.value,
  async (newValue) => {
    // 担当者ID
    local.tantoshaId = newValue

    // 担当者IDが空白以外の場合
    if (local.tantoshaId !== null && local.tantoshaId !== '') {
      // 絞込用利用者IDを取得する
      await getFilterForUserIdComSelect()
    }
  }
)

/**
 * フェースシート履歴情報を取得する
 *
 *   @param userId - 利用者ID
 */
async function getFaceSheetHistoryInfoList(userId: string) {
  orX0133Oneway.rirekiList = []
  const inputData: FaceSheetHistoryInfoSelectInEntity = {
    // 計画期間管理フラグ
    kikanFlg: localOneway.or28779.planPeriodManagementFlg,
    // 事業者ID
    svJigyoId: localOneway.or28779.officeId,
    // 利用者ID
    userId: userId,
  }
  // バックエンドAPIから初期情報取得
  const ret: FaceSheetHistoryInfoSelectOutEntity = await ScreenRepository.select(
    'faceSheetHistoryInfoSelect',
    inputData
  )

  local.rirekiList = ret.data.rirekiList ?? []

  // フェースシート履歴リスト
  if (local.rirekiList.length > 0) {
    for (const item of local.rirekiList) {
      // 「期間管理フラグが「管理する」の場合」、表示
      if (localOneway.or28779.planPeriodManagementFlg === Or28779Const.PERIOD_MANAGE_FLG_MANAGE) {
        // 計画期間明細
        const tmpItem = {
          planPeriod:
            t('label.plan-period') +
            t('label.colon-mark') +
            item.startYmd +
            t('label.wavy') +
            item.endYmd,
        } as OrX0133TableData
        orX0133Oneway.rirekiList.push(tmpItem)
      }

      for (const e of item.historyList) {
        const tmpItem1 = {
          planPeriod: '',
          // 作成日
          createYmd: e.createYmd,
          kijunbiYmd: '',
          // 作成者
          shokuKnj: getStaffName(e.shokuId),
          shokuinKnj: '',
          // ケース番号
          caseNo: e.caseNo,
          tougaiYm: '',
          // 変更回数
          kaisuu: e.henkoKaisu,
          // 改訂
          kaiteiKnj: getMastCodeLabel(local.codeMasterValuesKaitei, e.kaiteiFlg),
          youshikiKnj: '',
          gdlId: '',
          assType: '',
          assDateYmd: '',
        } as OrX0133TableData
        orX0133Oneway.rirekiList.push(tmpItem1)
      }
    }
  }
}

/**
 * マスタコード情報から指定したvalueでlabelを取得する
 *
 * @param codeValues - マスタコード情報
 *
 * @param value  - 指定したvalue
 */
function getMastCodeLabel(codeValues: CodeType[], value: string) {
  return codeValues.find((item) => item.value === value)?.label ?? ''
}

/**
 * 絞込用利用者IDを取得する
 */
async function getFilterForUserIdComSelect() {
  const inputData: FilterForUserIdComSelectInEntity = {
    // 担当者ID
    tantoId: local.tantoshaId,
    startYmd: localOneway.or28779.processYmd,
    endYmd: '',
  }

  // バックエンドAPIから初期情報取得
  const ret: FilterForUserIdComSelectOutEntity = await ScreenRepository.select(
    'filterForUserIdComSelect',
    inputData
  )

  // 利用者絞込リスト
  local.userSelList = ret.data.userSelList
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  // 画面状態を選択行のデータに保存
  saveRowStatus(selectedRowData.value?.id ?? '')

  // 保存処理
  await savePrintSettingInfo()

  setState({ isOpen: false })
}

/**
 * 画面の印刷設定情報を保存する
 */
async function savePrintSettingInfo() {
  // 画面の印刷設定情報を保存する
  const inputData: PrintSettingsInfoComUpdateInEntity = {
    // システムコード
    sysCd: localOneway.or28779.systemCode,
    // システム略称
    sysRyaku: localOneway.or28779.systemNameShort,
    // 機能名
    kinounameKnj: Or28779Const.FUNCTION_NAME_PRINT,
    // 法人ID
    houjinId: localOneway.or28779.corporationId,
    // 施設ID
    shisetuId: localOneway.or28779.facilityId,
    // 事業者ID
    svJigyoId: localOneway.or28779.officeId,
    // 職員ID
    shokuId: localOneway.or28779.staffId,
    // プロファイル
    choPro: selectedRowData.value?.choPro ?? '',
    // 個人情報表示フラグ
    kojinhogoFlg: Or28779Const.PERSONAL_INFO_SHOW_FLG_0,
    // 個人情報表示値
    sectionAddNo: Or28779Const.PERSONAL_INFO_SHOW_VALUE_0,
    // 出力帳票印刷情報リスト
    choPrtList: [selectedRowData.value],
    // 帳票INIデータオブジェクト
    iniDataObject: [...local.iniDataList],
  }

  await ScreenRepository.update('printSettingsInfoComUpdate', inputData)
}

/**
 * PDFダウンロードボタンクリック
 */
async function pdfDownloadBtnClick() {
  // 選択された帳票のプロファイルが””の場合
  if (selectedRowData.value.choPro === '') {
    showOr21813Msg(t('message.e-cmn-40172'))
    return
  }
  // 選択された帳票.プロファイル = "3GKU0P131P005"の場合
  if (selectedRowData.value.choPro === Or28779Const.CHECK_PROFILE_VALUE) {
    showOr21813Msg(t('message.e-cmn-41000'))
    return
  }

  const userList = OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList
  const historyList = OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList

  // 利用者選択が「単一」 」且つ、利用者一覧が0件選択の場合
  if (
    local.mo00039UserModel === Or28779Const.SELECT_CATEGORY_SINGLE &&
    (userList === undefined || userList.length === 0)
  ) {
    showOr21814Msg(t('message.i-cmn-11393'))
    return
  }

  // 利用者選択が「単一」  且つ、履歴一覧が0件選択の場合
  if (
    local.mo00039UserModel === Or28779Const.SELECT_CATEGORY_SINGLE &&
    (historyList === undefined || historyList.length === 0)
  ) {
    showOr21814Msg(t('message.i-cmn-11455'))
    return
  }

  // 利用者選択が「複数」 」且つ、利用者一覧が0件選択の場合
  if (
    local.mo00039UserModel === Or28779Const.SELECT_CATEGORY_MULTIPLE &&
    (userList === undefined || userList.length === 0)
  ) {
    showOr21814Msg(t('message.i-cmn-11393'))
    return
  }

  // 画面の印刷データを設定する
  saveRowStatus(selectedRowData.value.id)
  // 画面の印刷設定情報を保存する。
  await savePrintSettingInfo()

  // 帳票出力処理
}

/**
 * エラーメッセージを表示する
 *
 * @param errorMsg - メッセージ内容
 */
function showOr21813Msg(errorMsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: errorMsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.yes'),
    },
  })
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

// Or21813戻り値の確認
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.thirdBtnClickFlg) {
      return
    }
  }
)

/**
 * 確認メッセージを表示する
 *
 * @param errorMsg - メッセージ内容
 */
function showOr21814Msg(errorMsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogTitle: t('label.confirm'),
      dialogText: errorMsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.yes'),
    },
  })
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

// Or21814戻り値の確認
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.thirdBtnClickFlg) {
      return
    }
  }
)

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await close()
    }
  }
)
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row class="no-margin">
        <c-v-col
          cols="12"
          sm="2"
          class="np-border-right"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="mo01334Oneway"
            class="list-wrapper"
          >
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="4"
          class="np-border-right"
          style="padding-left: 8px !important; padding-right: 8px !important"
        >
          <c-v-row
            class="has-border-bottom mt-0"
            style="align-items: center"
          >
            <base-mo01338
              :oneway-model-value="{
                value: t('label.print-settings-title'),
                customClass: {
                  labelStyle: 'display: none',
                },
              }"
            ></base-mo01338>
            <base-mo01338 :oneway-model-value="localOneway.mo01338OnewayTitle"></base-mo01338>
          </c-v-row>
          <c-v-row
            class="has-border-bottom no-pl"
            style="height: 134px"
          >
            <!-- 日付印刷区分 -->
            <base-mo00039
              v-model="local.mo00039DateModel"
              :oneway-model-value="localOneway.mo00039OnewayDate"
            >
            </base-mo00039>
            <!-- 指定日 -->
            <base-mo00020
              v-show="local.mo00039DateModel == Or28779Const.DATE_PRINT_CATEGORY_2"
              v-model="local.mo00020modelShitei"
              :oneway-model-value="mo00020OnewayNoArrow"
            >
            </base-mo00020>
          </c-v-row>
          <c-v-row
            class="mt-2"
            style="height: 42px"
          >
            <!-- 印刷オプションラベル -->
            <base-mo01338
              :oneway-model-value="{
                value: t('label.printer-option'),
                valueFontWeight: 'bolder',
                customClass: {
                  labelStyle: 'display: none',
                },
              }"
              style="width: 100%; background-color: #e2ecf5"
            >
            </base-mo01338>
          </c-v-row>
          <c-v-row class="no-pl">
            <c-v-col>
              <!-- 記入用シートを印刷する -->
              <base-mo00018
                v-model="local.mo00018ModelSheet"
                :oneway-model-value="mo00018OnewaySheet"
              ></base-mo00018>
              <!-- 作成者を印刷する -->
              <base-mo00018
                v-model="local.mo00018ModelCreater"
                :oneway-model-value="mo00018OnewayCreater"
              ></base-mo00018>
              <!-- 初回作成日を印刷する -->
              <base-mo00018
                v-model="local.mo00018ModelCreateDate"
                :oneway-model-value="mo00018OnewayCreateDate"
              ></base-mo00018>
              <!-- ※H21改訂版のみ有効 -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OnewayH21"
                class="ml-8"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          style="padding: 0px 8px 0px 0px"
        >
          <c-v-row
            class="ml-0"
            style="align-items: center"
          >
            <c-v-col
              cols="5"
              class="user-history"
            >
              <!-- 利用者選択 -->
              <base-mo00039
                v-model="local.mo00039UserModel"
                :oneway-model-value="localOneway.mo00039OnewayUser"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col class="user-history">
              <!-- 基準日 -->
              <base-mo00020
                v-show="local.mo00039UserModel == Or28779Const.SELECT_CATEGORY_MULTIPLE"
                v-model="local.mo00020modelKijun"
                :oneway-model-value="mo00020OnewayKijun"
              >
              </base-mo00020>
              <!-- 履歴選択 -->
              <base-mo00039
                v-show="local.mo00039UserModel == Or28779Const.SELECT_CATEGORY_SINGLE"
                v-model="local.mo00039HistoryModel"
                :oneway-model-value="localOneway.mo00039OnewayHistory"
              >
              </base-mo00039>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="localOneway.or28779.processYmd != ''"
            style="align-items: center"
          >
            <!-- 担当ケアマネラベル -->
            <g-custom-or-x-0145
              v-bind="orx0145"
              v-model="orX0145Type"
              :oneway-model-value="localOneway.orX0145Oneway"
              class="search-tack"
              style="display: flex"
            ></g-custom-or-x-0145>
          </c-v-row>
          <c-v-row
            class="no-margin"
            no-gutters
          >
            <c-v-col
              :cols="userCols"
              class="or28779_0130"
            >
              <g-custom-or-x-0130
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="local.mo00039UserModel == Or28779Const.SELECT_CATEGORY_SINGLE"
              cols="6"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0133
                v-bind="orX0133"
                :oneway-model-value="orX0133Oneway"
              ></g-custom-or-x-0133>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row
        no-gutters
        style="padding-right: 8px; padding-bottom: -8px"
      >
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- PDFダウンロードボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          @click="pdfDownloadBtnClick"
        >
          <!--ツールチップ表示："PDFをダウンロードします"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.pdf-download')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- エラーダイアログを表示する。 -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  ></g-base-or21813>
  <!-- 確認ダイアログを表示する。 -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  ></g-base-or21814>
  <!-- 印刷ログ画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrx0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
</template>
<style>
.or28779-nopadding-content {
  padding: 0px !important;
}
</style>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
:deep(.gray-label-style) {
  font-size: smaller;
}

:deep(.v-data-table__th) {
  min-width: 70px;
}

.has-border-bottom {
  margin-bottom: 8px;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
}

.no-margin {
  margin: 0px !important;
}

.np-border-right {
  padding: 0px 8px !important;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

:deep(td .v-row--no-gutters > .v-col) {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

:deep(.no-pl .v-col) {
  padding-left: 0 !important;
}

:deep(.v-table--density-compact .row-height) {
  height: 31px !important;
}

:deep(.v-checkbox-btn .v-label) {
  color: #0000ff !important;
}

:deep(.user-history) {
  padding: 8px 0 0 0 !important;

  .radio-group {
    margin-top: 0px !important;
    > div {
      padding: 0px !important;
    }
  }
}

:deep(.search-tack) {
  margin-left: 8px;
  > div {
    margin: 0 !important;
  }

  .v-input__control {
    width: 200px;
  }
}

:deep(.search-tack) {
  > div:last-child {
    .v-col {
      padding-left: 0 !important;
      > div {
        margin-left: 0 !important;
      }
    }
  }
}

:deep(.or28779_0130) {
  .bg-w {
    .v-theme--mainTheme {
      padding: 0 !important;
    }
  }
  .list-wrapper {
    .v-table__wrapper {
      tbody {
        tr {
          td:first-child {
            .v-col {
              padding: 0 !important;
            }
          }
        }
      }
    }
  }
}
</style>
