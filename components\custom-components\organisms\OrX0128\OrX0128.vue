<script setup lang="ts">
/**
 * OrX0128:有機体:［計画期間＆履歴一覧］画面
 * ［計画期間＆履歴一覧］画面
 *
 * @description
 * ［計画期間＆履歴一覧］画面では、内容を選択し、呼び出し元の画面に反映します。
 *
 * <AUTHOR>
 */
import { reactive, ref, watch } from 'vue'
import { OrX0128Const } from './OrX0128.constants'
import type {
  OrX0128Items,
  OrX0128EventType,
  OrX0128OnewayType,
  OrX0128Selected,
} from '~/types/cmn/business/components/OrX0128Type'
import { useScreenEventStatus, useSetupChildProps } from '~/composables/useComponentVue'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: OrX0128OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: OrX0128OnewayType = {
  kikanFlg: '',
  singleFlg: '',
  tableStyle: 'width:520px',
  headers: [],
  items: [],
}

const localOneway = reactive({
  orX0128: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  assType: [] as CodeType[],
})

/**************************************************
 * Pinia
 **************************************************/
const { setEvent } = useScreenEventStatus<OrX0128EventType>({
  cpId: OrX0128Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
useSetupChildProps(props.uniqueCpId, {})

/**************************************************
 * 変数定義
 **************************************************/
/** 表の双方向モデル */
const tableSelectArr = ref<string[]>([])
/** 選択結果が保持 */
const selectedHist = ref<OrX0128Selected>({
  taniSelectedId: '',
  hukusuuSelectedId: [],
} as OrX0128Selected)
// 全選択フラグ
const allSelectVal = ref<boolean>(false)
/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 行選択イベント
 *
 * @param row - 行情報
 */
const selectRow = (row: OrX0128Items) => {
  if (row.isPeriodManagementMergedRow) {
    return
  }
  if (
    tableSelectArr.value.includes(row.id) &&
    localOneway.orX0128.singleFlg === OrX0128Const.DEFAULT.HUKUSUU
  ) {
    // チェックを外す
    tableSelectArr.value = tableSelectArr.value.filter((i) => i !== row.id)
  } else {
    // 未チェックの場合は追加
    if (localOneway.orX0128.singleFlg === OrX0128Const.DEFAULT.TANI) {
      tableSelectArr.value = [row.id]
    } else {
      tableSelectArr.value = [...tableSelectArr.value, ...[row.id]]
    }
  }
}

/**
 * 全選択
 */
const selectAll = () => {
  if (allSelectVal.value) {
    tableSelectArr.value = []
    allSelectVal.value = false
  } else {
    const filteredRowIds: string[] = localOneway.orX0128.items
      .filter((item) => !item.isPeriodManagementMergedRow)
      .map((item) => item.id)
    tableSelectArr.value = [...filteredRowIds]
    allSelectVal.value = true
  }
}
/**
 * 全選択チェック
 */
const allSelect = () => {
  const filteredRows: OrX0128Items[] = localOneway.orX0128.items.filter(
    (item) => !item.isPeriodManagementMergedRow
  )
  return filteredRows.length
}

/**
 * 行選択監視
 */
watch(
  () => tableSelectArr.value,
  () => {
    if (localOneway.orX0128.singleFlg === OrX0128Const.DEFAULT.HUKUSUU) {
      allSelectVal.value =
        tableSelectArr.value.length === allSelect() && tableSelectArr.value.length > 0
    }
    const selectedRows: OrX0128Items[] = localOneway.orX0128.items.filter((item) =>
      tableSelectArr.value.includes(item.id)
    )
    setEvent({
      orX0128DetList: selectedRows,
      historyDetClickFlg: tableSelectArr.value.length !== 0,
    })
  }
)

/**
 * アセスメント履歴一覧データ
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.orX0128 = {
      ...defaultOnewayModelValue,
      ...newValue,
    }
  },
  { deep: true }
)

/**
 * 「履歴選択方法」ラジオボタン押下
 */
watch(
  () => props.onewayModelValue.singleFlg,
  (newValue) => {
    // 単一と複数の切り替え時に、選択結果が保持
    if (newValue) {
      if (newValue === OrX0128Const.DEFAULT.HUKUSUU) {
        selectedHist.value.taniSelectedId = tableSelectArr.value[0]
        tableSelectArr.value = selectedHist.value.hukusuuSelectedId
      } else {
        selectedHist.value.hukusuuSelectedId = tableSelectArr.value
        tableSelectArr.value = [
          selectedHist.value.taniSelectedId === '' && localOneway.orX0128.items.length > 0
            ? (
                localOneway.orX0128.items.findIndex(
                  (item) => item.isPeriodManagementMergedRow !== true
                ) + 1
              ).toString()
            : selectedHist.value.taniSelectedId,
        ]
      }
    }
  }
)
/**
 * 利用者選択
 */
watch(
  () => props.onewayModelValue.items,
  (newValue) => {
    selectedHist.value = {
      taniSelectedId: '',
      hukusuuSelectedId: [],
    } as OrX0128Selected
    if (localOneway.orX0128.singleFlg === OrX0128Const.DEFAULT.HUKUSUU) {
      tableSelectArr.value = []
    } else {
      if (newValue && newValue.length > 0) {
        if (localOneway.orX0128.initSelectId && Number(localOneway.orX0128.initSelectId) > 0) {
          // 親画面を対するレコードを選択状態
          tableSelectArr.value = [localOneway.orX0128.initSelectId]
          localOneway.orX0128.initSelectId = ''
        } else {
          tableSelectArr.value = [
            (
              newValue.findIndex((item) => item.isPeriodManagementMergedRow !== true) + 1
            ).toString(),
          ]
        }
      } else {
        tableSelectArr.value = []
      }
    }
  }
)
</script>

<template>
  <c-v-row
    no-gutter
    class="orx_0128_row"
  >
    <c-v-col class="d-flex orx_0128_col">
      <!-- 初期表示(期間管理フラグが「管理する」の場合) -->
      <c-v-data-table
        v-model="tableSelectArr"
        :headers="localOneway.orX0128.headers"
        class="table-wrapper"
        fixed-header
        :items="localOneway.orX0128.items"
        height="480px"
        hide-default-footer
        :style="localOneway.orX0128.tableStyle"
        :items-per-page="-1"
        :show-select="localOneway.orX0128.singleFlg === OrX0128Const.DEFAULT.HUKUSUU"
        item-selectable="selectable"
        item-value="id"
      >
        <!-- ヘッダーの選択チェックボックスを置き換え -->
        <template #[`header.data-table-select`]>
          <c-v-row
            no-gutters
            align="center"
            class="flex-nowrap justify-space-between"
          >
            <!-- ヘッダーの選択チェックボックス -->
            <c-v-col v-if="localOneway.orX0128.singleFlg === OrX0128Const.DEFAULT.HUKUSUU">
              <base-at-checkbox
                :indeterminate="tableSelectArr.length > 0 && tableSelectArr.length < allSelect()"
                :model-value="allSelectVal"
                @update:model-value="selectAll"
              />
            </c-v-col>
          </c-v-row>
        </template>
        <!-- 一覧 -->
        <template #item="{ item, isSelected, internalItem }">
          <tr
            :class="{
              'header-row': item.planPeriod,
              'selected-row': tableSelectArr.includes(item.id) && !item.planPeriod,
            }"
            @click="selectRow(item)"
          >
            <td
              v-if="item.planPeriod && item.planPeriod !== ''"
              :colspan="
                localOneway.orX0128.singleFlg === OrX0128Const.DEFAULT.HUKUSUU
                  ? localOneway.orX0128.headers.length + 1
                  : localOneway.orX0128.headers.length
              "
            >
              {{ item.planPeriod }}
            </td>
            <!-- 初期表示(期間管理フラグが「管理しない」の場合) -->
            <template v-else>
              <!-- データ行の選択チェックボックスを置き換え -->
              <td v-if="localOneway.orX0128.singleFlg === OrX0128Const.DEFAULT.HUKUSUU">
                <c-v-row
                  no-gutters
                  align="center"
                  class="flex-nowrap justify-space-between row-height"
                >
                  <!-- 行の選択チェックボックス -->
                  <c-v-col
                    v-if="localOneway.orX0128.singleFlg === OrX0128Const.DEFAULT.HUKUSUU"
                    class="select-checkbox-col"
                  >
                    <base-at-checkbox
                      :model-value="isSelected(internalItem)"
                      checkbox-label=""
                      readonly
                    />
                  </c-v-col>
                </c-v-row>
              </td>
              <td
                v-for="headerItem in localOneway.orX0128.headers"
                :key="headerItem.key"
                :class="headerItem.itemClass"
                :style="headerItem.itemStyle"
              >
                {{ item[headerItem.key] }}
              </td>
            </template>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-col>
  </c-v-row>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
.table-wrapper .v-table__wrapper td {
  padding: 0;
}
:deep(.table-header td) {
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-color: lightgrey;
  max-height: 40px;
  font-size: 14px;
}
:deep(.table-wrapper .v-table__wrapper table) {
  table-layout: fixed;
}
:deep(.v-table--fixed-header) {
  position: sticky;
  // ジッタ除去
  top: 0px;
  z-index: 2;
}

.header-row {
  cursor: default;
  background-color: rgb(var(--v-theme-blue-100));
}
// 選択した行のCSS
.selected-row {
  background-color: rgb(var(--v-theme-blue-100));
}
:deep(.table-checkbox > div) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
}
// 行の高さ: 32px
:deep(.table-wrapper table tbody tr td),
:deep(.table-wrapper .row-height) {
  height: 32px !important;
}
.v-table :deep(i.fill) {
  color: unset !important;
}
.table-wrapper :deep(tr th) {
  left: unset !important;
}

.orx_0128_row {
  margin: 0px !important;

  .orx_0128_col {
    padding: 0px !important;
  }
}
</style>
