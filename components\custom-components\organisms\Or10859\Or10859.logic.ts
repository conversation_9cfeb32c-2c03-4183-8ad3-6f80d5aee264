import { Or10859Const } from './Or10859.constants'
import type { Or10859StateType } from './Or10859.type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

/**
 * Or10859:有機体:モーダル（表示順変更アセスメントモーダル）
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 */
export namespace Or10859Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or10859Const.CP_ID(0),
      uniqueCpId,
      initOneWayState: {
        isOpen: Or10859Const.DEFAULT.IS_OPEN,
      },
      childCps: [{ cpId: Or21814Const.CP_ID(1) }],
    })

    // 子コンポーネントのセットアップ
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or10859StateType>(Or10859Const.CP_ID(0))
}
