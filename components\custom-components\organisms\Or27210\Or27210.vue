<script setup lang="ts">
/**
 * Or27210:有機体:[手帳情報選択］画面
 * GUI00809_[手帳情報選択］画面
 *
 * @description
 * 手帳情報選択
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch } from 'vue'
import { Or27210Const } from './Or27210.constants'
import type { Or27210StateType } from './Or27210.type'
import type {
  Or27210Type,
  Or27210OneWayType,
  notebookInfoSelectType,
} from '~/types/cmn/business/components/Or27210Type'
import { useScreenOneWayBind } from '#imports'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  INotebookInfoSelectInEntity,
  INotebookInfoSelectOutEntity,
} from '~/repositories/cmn/entities/NotebookInfoSelectEntity'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01334Headers } from '~/types/business/components/Mo01334Type'
import type { Mo01344OnewayType } from '~/types/business/components/Mo01344Type'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or27210Type
  onewayModelValue: notebookInfoSelectType
  uniqueCpId: string
  orScreenMenuOnewayValue: Or27210OneWayType
}
const props = defineProps<Props>()

const defaultOnewayModelValue: Or27210OneWayType = {
  notebookInfoSelect: {
    /**
     * 利用者ID
     */
    userId: '',
    /**
     * 交付年月日
     */
    getYmd: '',
    /**
     *手帳区分
     */
    tkbn: '2',
  },
  /**
   * マスタ他Icon
   */
  mo00009OnewayMaster: {
    btnIcon: 'database',
    class: 'btn-mr',
  },

  /**
   * 表表示標識
   */
  tableDisplayFlag: 0,
}

const localOneway = reactive({
  or27210: {
    ...defaultOnewayModelValue,
    notebookInfoSelect: {
      ...props.onewayModelValue,
    },
  },

  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,

  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,

  // 手帳情報選択ダイアログ
  mo00024Oneway: {
    width: 'auto',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or27210',
      toolbarTitle: t('label.notebook-info-select'),
      toolbarName: 'Or27210ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    } as Mo01344OnewayType,
  } as Mo00024OnewayType,

  orScreenMenuOneway: {
    ...props.orScreenMenuOnewayValue,
    mo00009OnewayMaster: {
      ...defaultOnewayModelValue.mo00009OnewayMaster,
      ...props.orScreenMenuOnewayValue?.mo00009OnewayMaster,
    },
  },

  // 手帳情報選択一覧
  notebookMo01334: {
    // 手帳情報選択データテーブルのヘッダー
    headers: [] as Mo01334Headers[],
    height: 263,
    items: [
      {
        /** 一意の識別子を追加する*/
        id: '',
        /** 取得日*/
        getYmd: '',
        /** 等級*/
        toukyuuKnj: '',
        /** メモ*/
        bikoKnj: '',
        /** 程度*/
        teidoKnj: '',
        /** 合併障害*/
        gappeiShougaiKnj: '',
        /** 種類*/
        tKindKnj: '',
        /** 等級コード*/
        tTokyu: '',
        /** 手帳種類*/
        tKindCd: '',
      },
    ],
  },

  // 精神手帳/身体障害手帳データテーブルのヘッダー
  disabilityAndMentalheaders: [
    // 取得日
    {
      title: t('label.acquisition-date'),
      key: 'getYmd',
      width: '110px',
      sortable: true,
    },
    // 等級
    {
      title: t('label.grade'),
      key: 'toukyuuKnj',
      sortable: true,
    },
    // メモ
    {
      title: t('label.memo'),
      key: 'bikoKnj',
      width: '350px',
      sortable: true,
    },
  ] as Mo01334Headers[],

  // 療育手帳データテーブルのヘッダー
  remedialEducationheaders: [
    // 取得日
    {
      title: t('label.acquisition-date'),
      key: 'getYmd',
      width: '110px',
      sortable: true,
    },
    // 程度
    {
      title: t('label.degree'),
      key: 'teidoKnj',
      sortable: true,
    },
    // 合併障害
    {
      title: t('label.combined-handycap'),
      key: 'gappeiShougaiKnj',
      sortable: true,
      width: '350px',
    },
  ] as Mo01334Headers[],
})

const local = reactive({
  // 履歴選択情報
  mo01334: {
    value: '',
    values: [],
  },
})

// 選択した行のindex
const selectedItemIndex = ref<number>(0)

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27210Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
async function getInitDataInfo() {
  if (localOneway.or27210.notebookInfoSelect.tkbn === Or27210Const.DEFAULT.TKBN_DISABILITY) {
    // 身障手帳マスタ画面を開く
    localOneway.notebookMo01334.headers = localOneway.disabilityAndMentalheaders
  } else if (localOneway.or27210.notebookInfoSelect.tkbn === Or27210Const.DEFAULT.TKBN_MENTAL) {
    // 精神手帳マスタ画面を開く
    localOneway.notebookMo01334.headers = localOneway.disabilityAndMentalheaders
  } else if (
    localOneway.or27210.notebookInfoSelect.tkbn === Or27210Const.DEFAULT.TKBN_REMEDIA_EDUCATION
  ) {
    // 療育手帳マスタ画面を開く
    localOneway.notebookMo01334.headers = localOneway.remedialEducationheaders
  }

  // バックエンドAPIから初期情報取得
  const inputData: INotebookInfoSelectInEntity = {
    /**
     * 利用者ID
     */
    userId: localOneway.or27210.notebookInfoSelect.userId,
    /**
     * 交付年月日
     */
    getYmd: localOneway.or27210.notebookInfoSelect.getYmd,
    /**
     *手帳区分
     */
    tkbn: localOneway.or27210.notebookInfoSelect.tkbn,
  }

  const resData: INotebookInfoSelectOutEntity = await ScreenRepository.select(
    'notebookInfoSelect',
    inputData
  )

  //取得したデータをテーブルに割り当てる
  localOneway.notebookMo01334.items = resData.data.notebookInfoList.map((item, index) => {
    return {
      id: String(index),
      getYmd: item.getYmd,
      toukyuuKnj: item.toukyuuKnj,
      bikoKnj: item.bikoKnj,
      teidoKnj: item.teidoKnj,
      gappeiShougaiKnj: item.gappeiShougaiKnj,
      tKindKnj: item.tKindKnj,
      tTokyu: item.tTokyu,
      tKindCd: item.tKindCd,
    }
  })

  // データがある場合は最初の行を選択しま
  if (localOneway.notebookMo01334.items.length > 0) {
    selectedItemIndex.value = Number(localOneway.notebookMo01334.items[0].id)
  }
}

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27210StateType>({
  cpId: Or27210Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or27210Const.DEFAULT.IS_OPEN
    },
  },
})

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * その他の機能を呼び出すボタンを表示します
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 *
 * @param tableDisplayFlag - 表表示標識
 */
function showMasterScreen(tableDisplayFlag: number) {
  //TODO
  if (tableDisplayFlag === 0) {
    // GUI00539 身障手帳マスタ画面を開く 画面未作成
    console.log('身障手帳マスタ画面を開く')
  } else if (tableDisplayFlag === 1) {
    // GUI00541 精神手帳マスタ画面を開く 画面未作成
    console.log('精神手帳マスタ画面を開く')
  } else if (tableDisplayFlag === 2) {
    // GUI00540 療育手帳マスタ画面を開く 画面未作成
    console.log('療育手帳マスタ画面を開く')
  }
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function onSelectRow(index: number) {
  selectedItemIndex.value = index
}

/**
 * 「確定」ボタン押下
 */
function confirm() {
  // 戻り値： 手帳情報選択リスト
  const item = localOneway.notebookMo01334.items.find(
    (item) => Number(item.id) === selectedItemIndex.value
  )
  const resData = {
    notebookInfoData: {
      getYmd: item?.getYmd,
      toukyuuKnj: item?.toukyuuKnj,
      bikoKnj: item?.bikoKnj,
      teidoKnj: item?.teidoKnj,
      gappeiShougaiKnj: item?.gappeiShougaiKnj,
      tKindKnj: item?.tKindKnj,
      tTokyu: item?.tTokyu,
      tKindCd: item?.tKindCd,
    } as unknown as Or27210Type,
  }
  // 画面.手帳情報選択リストを親画面に戻る。
  emit('update:modelValue', resData)

  close()
}

/**
 * ダブルクリックで確認
 *
 * @param index -選択されたオブジェクトのID
 */
const doubleClickHistorySelectRow = (index: number) => {
  selectedItemIndex.value = index
  confirm()
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row
          v-if="false"
          no-gutters
        >
          <!-- マスタ他ボタン -->
          <c-v-col
            class="text-right pa-2"
            cols="12"
          >
            <base-mo00009
              :oneway-model-value="localOneway.orScreenMenuOneway.mo00009OnewayMaster"
              @click="showMasterScreen(localOneway.or27210.tableDisplayFlag)"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.others-function')"
              />
            </base-mo00009>
          </c-v-col>
        </c-v-row>

        <c-v-row>
          <c-v-col class="pa-2 w-auto flex-0-0 table-header table-wrapper">
            <base-mo01334
              v-model="local.mo01334"
              class="list-wrapper"
              :oneway-model-value="localOneway.notebookMo01334"
              hide-default-footer
            >
              <template
                v-if="
                  localOneway.or27210.notebookInfoSelect.tkbn ===
                    Or27210Const.DEFAULT.TKBN_DISABILITY ||
                  localOneway.or27210.notebookInfoSelect.tkbn === Or27210Const.DEFAULT.TKBN_MENTAL
                "
                #item="{ item, index }"
              >
                <tr
                  :class="{ 'row-selected': selectedItemIndex === index }"
                  @click="onSelectRow(index)"
                  @dblclick="doubleClickHistorySelectRow(index)"
                >
                  <td>
                    <span>{{ item.getYmd }}</span>
                  </td>
                  <td>
                    <span>{{ item.toukyuuKnj }}</span>
                  </td>

                  <td>
                    <span class="overflowText">{{ item.bikoKnj }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="350"
                      :text="item.bikoKnj"
                      open-delay="200"
                    />
                  </td>
                </tr>
              </template>
              <template
                v-else
                #item="{ item, index }"
              >
                <tr
                  :class="{ 'row-selected': selectedItemIndex === index }"
                  @click="onSelectRow(index)"
                  @dblclick="doubleClickHistorySelectRow(index)"
                >
                  <td>
                    <span>{{ item.getYmd }}</span>
                  </td>
                  <td>
                    <span>{{ item.teidoKnj }}</span>
                  </td>
                  <td>
                    <span class="overflowText">{{ item.gappeiShougaiKnj }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="350"
                      :text="item.gappeiShougaiKnj"
                      open-delay="200"
                    />
                  </td>
                </tr>
              </template>
            </base-mo01334>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        >
          <c-v-tooltip
            :text="t('tooltip.screen-close')"
            activator="parent"
            location="top"
          ></c-v-tooltip>
        </base-mo00611>
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          v-bind="localOneway.mo00609ConfirmOneway"
          class="mx-2"
          @click="confirm"
        >
          <c-v-tooltip
            :text="t('tooltip.confirm-btn')"
            activator="parent"
            location="top"
          ></c-v-tooltip>
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';
/* マスタ他ボタン 所在行高スタイル */
:deep(.v-table--density-default) {
  --v-table-row-height: 40px;
}
/* マスタ他ボタンスタイル */
.btn-mr {
  margin-right: -7px;
}

.overflowText {
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}

// /* 表の列スタイル */
:deep(.table-header td) {
  border-color: lightgrey;
  font-size: 14px;
}

/** 行選択の様式 */
.row-selected {
  background-color: rgb(var(--v-theme-blue-100)) !important;
  cursor: pointer;
}

.v-row {
  margin: -8px;
}
</style>
