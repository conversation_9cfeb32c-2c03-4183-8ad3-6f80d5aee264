<script setup lang="ts">
/**
 * Or53066: ［経過管理］画面ダイアログ
 * GUI00622_［経過管理］画面
 *
 * @description
 *  ［経過管理］画面 ダイアログ
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { onMounted, reactive, ref, watch, computed } from 'vue'

import { Gui00018Logic } from '../Gui00018/Gui00018.logic'
import { Gui00018Const } from '../Gui00018/Gui00018.constants'
import { Or53066Const } from './Or53066.constants'
import type { Or53066StateType } from './Or53066.type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or53066OnewayType } from '~/types/cmn/business/components/Or53066Type'
import type { Mo01334OnewayType } from '~/types/business/components/Mo01334Type'
import type {
  PassageManagementSelectInEntity,
  PassageManagementSelectOutEntity,
} from '~/repositories/cmn/entities/PassageManagementSelectEntity'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or53066OnewayType
  uniqueCpId: string
}
/**
 *props
 */
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/

const gui00018 = ref({ uniqueCpId: '' })
// 片方向バインド用の内部変数
/**
 *ローカルOneway
 */
const localOneway = reactive({
  or53066: {
    ...props.onewayModelValue,
  },
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('label.close'),
    width: '90px',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
    width: '90px',
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
  mo00024Oneway: {
    width: '1201px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.passagemanagement'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  } as Mo00024OnewayType,
  mo01334HistoryselectOnewayModelValue: {
    // ヘッダーに表示される名称
    headers: [],
    height: 405,
    items: [],
  } as Mo01334OnewayType,
  csvOutput: {
    btnLabel: t('btn.csv-btn'),
    tooltipText: t('tooltip.csv-output'),
  } as Mo00611OnewayType,
})

/**
 *ダイアログ設置
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or53066Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/
/**
 *State
 */
const { setState } = useScreenOneWayBind<Or53066StateType>({
  cpId: Or53066Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * ダイヤログ設定
     *
     * @param value - ダイヤログ open
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or53066Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Gui00018Const.CP_ID(0)]: gui00018.value,
})

onMounted(async () => {
  //初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: PassageManagementSelectInEntity = {
    shokuinId: localOneway.or53066.shokuinId,
    sysCd: localOneway.or53066.sysCd,
  }
  const resData: PassageManagementSelectOutEntity = await ScreenRepository.select(
    'passageManagementSelect',
    inputData
  )

  localOneway.mo01334HistoryselectOnewayModelValue.headers = resData.data.displayTitleList.map(
    (item) => {
      return {
        title: item.dispnameKnj,
        key: item.colnameKnj,
        sortable: true,
        minWidth: '211px',
      }
    }
  )

  localOneway.mo01334HistoryselectOnewayModelValue.items = localOneway.or53066.dataList.map(
    (item, index) => {
      return {
        id: index + '',
        ...item,
      }
    }
  )
}
const showDialog = computed(() => {
  // GuiD00018のダイアログ開閉状態
  return Gui00018Logic.state.get(gui00018.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「CSV出力」ボタン押下
 */
const onClickCSVBtn = () => {
  Gui00018Logic.state.set({
    uniqueCpId: gui00018.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <c-v-col class="d-flex justify-end">
            <base-mo00611
              v-if="false"
              :oneway-model-value="localOneway.csvOutput"
              @click="onClickCSVBtn"
            ></base-mo00611>
          </c-v-col>
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            class="table-header"
          >
            <base-mo01334
              class="list-wrapper"
              hide-default-footer
              :oneway-model-value="localOneway.mo01334HistoryselectOnewayModelValue"
            >
              <template
                v-for="element in localOneway.mo01334HistoryselectOnewayModelValue.headers"
                #[`item.${element.key}`]="{ item }"
                :key="element.key"
                ><div class="tooltip-div">
                  <span class="tooltip-cell">
                    {{ item[element.key] }}
                  </span>
                  <c-v-tooltip
                    v-if="item[element.key]"
                    activator="parent"
                    location="bottom"
                    :max-width="211"
                    :text="item[element.key]"
                    open-delay="211"
                  />
                </div>
              </template>
            </base-mo01334>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class=""
          @click="onClickCloseBtn"
        />
      </c-v-row>
    </template>
  </base-mo00024>
  <g-custom-gui-00018
    v-if="showDialog"
    v-bind="gui00018"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
.tooltip-cell {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tooltip-div {
  width: 211px;
  overflow: hidden;
}
</style>
