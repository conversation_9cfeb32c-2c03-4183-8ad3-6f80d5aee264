<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or52063Logic } from '~/components/custom-components/organisms/Or52063/Or52063.logic'
import { Or52063Const } from '~/components/custom-components/organisms/Or52063/Or52063.constants'
import type {
  CustomizeDataItem,
  Or52063OneWayType,
  Or52063Type,
} from '~/types/cmn/business/components/Or52063Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00824'
// ルーティング
const routing = 'GUI00824/pinia'
// 画面物理名
const screenName = 'GUI00824'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or52063 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00824' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00824',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or52063Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or52063Const.CP_ID(1)]: or52063.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or52063Logic.initialize(or52063.value.uniqueCpId)
}

/**************************************************
 * 変数定義
 **************************************************/
const or52063OneWayType = ref<Or52063OneWayType>({
  styleId: '',
  headers: [],
})
const or52063Type = ref<Or52063Type>({
  fixedDataList: [],
  customizeDataList: [],
})

/**
 *  ボタン押下時の処理
 *
 * @param styleId - 様式ID
 */
function onClickOr52063(styleId: string) {
  or52063OneWayType.value.styleId = styleId
  if (styleId === '0') {
    or52063Type.value.fixedDataList = [
      {
        id: '1',
        /** 表示順 */
        displayOrder: {
          value: '1',
        },
        /** 領域 */
        area: 'A1',
        /** 具体的状況 */
        specificSituation: '具体的状況1',
        /** その原因や要因 */
        cause: 'その原因や要因1',
        /** 影響している領域 */
        affectedAreas: '影響している領域1',
        /** 今後の見通し */
        futureOutlook: '今後の見通し1',
        /** 入所者の意向・受けとめ方 */
        methodAcceptance: '入所者の意向・受けとめ方1',
      },
      {
        id: '2',
        /** 表示順 */
        displayOrder: {
          value: '2',
        },
        /** 領域 */
        area: 'A',
        /** 具体的状況 */
        specificSituation: '具体的状況2',
        /** その原因や要因 */
        cause: 'その原因や要因2',
        /** 影響している領域 */
        affectedAreas: '影響している領域2',
        /** 今後の見通し */
        futureOutlook: '今後の見通し2',
        /** 入所者の意向・受けとめ方 */
        methodAcceptance: '入所者の意向・受けとめ方2',
      },
      {
        id: '3',
        /** 表示順 */
        displayOrder: {
          value: '3',
        },
        /** 領域 */
        area: 'B',
        /** 具体的状況 */
        specificSituation: '具体的状況3',
        /** その原因や要因 */
        cause: 'その原因や要因3',
        /** 影響している領域 */
        affectedAreas: '影響している領域3',
        /** 今後の見通し */
        futureOutlook: '今後の見通し3',
        /** 入所者の意向・受けとめ方 */
        methodAcceptance: '入所者の意向・受けとめ方3',
      },
      {
        id: '4',
        /** 表示順 */
        displayOrder: {
          value: '4',
        },
        /** 目標番号 */
        area: 'C',
        /** 具体的状況 */
        specificSituation: '具体的状況4',
        /** その原因や要因 */
        cause: 'その原因や要因4',
        /** 影響している領域 */
        affectedAreas: '影響している領域4',
        /** 今後の見通し */
        futureOutlook: '今後の見通し4',
        /** 入所者の意向・受けとめ方 */
        methodAcceptance: '入所者の意向・受けとめ方4',
      },
      {
        id: '5',
        /** 表示順 */
        displayOrder: {
          value: '5',
        },
        /** 目標番号 */
        area: 'D',
        /** 具体的状況 */
        specificSituation: '具体的状況5',
        /** その原因や要因 */
        cause: 'その原因や要因5',
        /** 影響している領域 */
        affectedAreas: '影響している領域5',
        /** 今後の見通し */
        futureOutlook: '今後の見通し5',
        /** 入所者の意向・受けとめ方 */
        methodAcceptance: '入所者の意向・受けとめ方5',
      },
      {
        id: '6',
        /** 表示順 */
        displayOrder: {
          value: '6',
        },
        /** 目標番号 */
        area: 'E',
        /** 具体的状況 */
        specificSituation: '具体的状況6',
        /** その原因や要因 */
        cause: 'その原因や要因6',
        /** 影響している領域 */
        affectedAreas: '影響している領域6',
        /** 今後の見通し */
        futureOutlook: '今後の見通し6',
        /** 入所者の意向・受けとめ方 */
        methodAcceptance: '入所者の意向・受けとめ方6',
      },
    ]
  } else {
    or52063Type.value.customizeDataList = [
      {
        id: '1',
        /** 表示順 */
        displayOrder: {
          value: '1',
        },
        classification: 'A　身体\na　基本情報１',
        summary: 'まとめ１',
        outlook: '今後の見通し１',
      },
      {
        id: '2',
        /** 表示順 */
        displayOrder: {
          value: '2',
        },
        classification: 'A　身体　a　基本情報２',
        summary: 'まとめ２',
        outlook: '今後の見通し２',
      },
      {
        id: '3',
        /** 表示順 */
        displayOrder: {
          value: '3',
        },
        classification: 'A　身体　a　基本情報３',
        summary: 'まとめ３',
        outlook: '今後の見通し３',
      },
      {
        id: '4',
        /** 表示順 */
        displayOrder: {
          value: '4',
        },
        classification: 'A　身体　a　基本情報４',
        summary: 'まとめ４',
        outlook: '今後の見通し４',
      },
      {
        id: '5',
        /** 表示順 */
        displayOrder: {
          value: '5',
        },
        classification: 'A　身体　a　基本情報５',
        summary: 'まとめ５',
        outlook: '今後の見通し５',
      },
      {
        id: '6',
        /** 表示順 */
        displayOrder: {
          value: '6',
        },
        classification: 'A　身体　a　基本情報６',
        summary: 'まとめ６',
        outlook: '今後の見通し６',
      },
    ] as CustomizeDataItem[]
  }
  Or52063Logic.state.set({
    uniqueCpId: or52063.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログ表示フラグ
const showDialogOr52063 = computed(() => {
  // Or52063のダイアログ開閉状態
  return Or52063Logic.state.get(or52063.value.uniqueCpId)?.isOpen ?? false
})
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ CD 2025/05/30 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr52063('0')"
        >GUI00824_表示順変更アセスメント（パターン２）
      </v-btn>

      <g-custom-or-52063
        v-if="showDialogOr52063"
        v-bind="or52063"
        v-model="or52063Type"
        :oneway-model-value="or52063OneWayType"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <v-btn
      variant="plain"
      @click="onClickOr52063('1')"
      >GUI00824_表示順変更アセスメント（パターン１）
    </v-btn>
  </c-v-row>
  <!-- POP画面ポップアップ CD 2025/05/30 ADD END-->
</template>
