/**
 * Or52063：有機体：表示順変更アセスメント
 * GUI00824_表示順変更アセスメント
 * 単方向バインドのデータ構造
 */

import type { Mo01280Type } from '~/types/business/components/Mo01280Type'
import type { Mo01354Headers } from '~/components/base-components/molecules/Mo01354/Mo01354Type'

/**
 * OneWayType
 */
export interface Or52063OneWayType {
  /**
   * 様式ID
   */
  styleId: string
  /**
   * ヘーダタイトル
   */
  headers: Mo01354Headers[]
}

/**
 * 双方向方向バインドのデータ構造
 */
export interface Or52063Type {
  /**
   * パターン２リスト
   */
  fixedDataList: FixedDataList[]
  /**
   * パターン１リスト
   */
  customizeDataList: CustomizeDataItem[]
}

/**
 * 固定様式リスト
 */
export interface FixedDataList {
  /**
   * 行ID
   */
  id: string
  /**
   * 表示順
   */
  displayOrder: Mo01280Type
  /**
   * 領域
   */
  area: string
  /**
   * 具体的状況
   */
  specificSituation: string
  /**
   * その原因や要因
   */
  cause: string
  /**
   * 影響している領域
   */
  affectedAreas: string
  /**
   * 今後の見通し
   */
  futureOutlook: string
  /**
   * 入所者の意向・受けとめ方
   */
  methodAcceptance: string
}

/**
 * パターン１リスト
 */
export interface CustomizeDataItem {
  /**
   * 行ID
   */
  id: string
  /**
   * 表示順
   */
  displayOrder: Mo01280Type
  /**
   * タイトル
   */
  classification: string
  /**
   * 課題
   */
  summary: string
  /**
   * 今後の見通し
   */
  outlook: string
}
