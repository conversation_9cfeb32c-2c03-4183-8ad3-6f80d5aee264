<script setup lang="ts">
/**
 * GUI01231_［履歴選択］画面 計画ﾓﾆﾀﾘﾝｸﾞ
 *
 * @description
 *［履歴選択］画面 計画ﾓﾆﾀﾘﾝｸﾞ
 *
 * <AUTHOR>
 */

import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  watch,
} from '#imports'
import type {
  Or10939Type,
  HistorySelectInfoType,
  Or10939OnewayType,
} from '~/types/cmn/business/components/Or10939Type'
import { Or10939Const } from '~/components/custom-components/organisms/Or10939/Or10939.constants'
import { Or10939Logic } from '~/components/custom-components/organisms/Or10939/Or10939.logic'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01231'
// ルーティング
const routing = 'GUI01231/pinia'
// 画面物理名
const screenName = 'GUI01231'
// 画面状態管理用操作変数
const screenStore = useScreenStore()

const Or10939 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01231' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or10939Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
// Or10939.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01231',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or10939Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or10939Const.CP_ID(1)]: Or10939.value,
})

/**************************************************
 * Props
 **************************************************/
// ダイアログ表示フラグ
const showDialogOr10939 = computed(() => {
  // Or10939のダイアログ開閉状態
  return Or10939Logic.state.get(Or10939.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or10939Data: Or10939OnewayType = {
  historySelectInfo: {
    // 計画期間ID
    sc1Id: '1',
    // 事業者ID
    svJigyoId: '1',
    // 利用者ID
    userId: '1',
    // パッケージプランフラグ
    sypFlg: '0',
    // ヘッダID
    historyId: '',
  } as HistorySelectInfoType,
}

const or10939Type = ref<Or10939Type>({
  cmoni1Id:''
})
watch(or10939Type, () => {
  console.log(or10939Type.value)
})

/**
 *  ボタン押下時の処理(Or10939)
 *
 * @param sypFlg - sypFlg
 */
function onClickOr10939(sypFlg: string) {
  or10939Data.historySelectInfo.sypFlg = sypFlg
  // Or10939のダイアログ開閉状態を更新する
  Or10939Logic.state.set({
    uniqueCpId: Or10939.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters>
    <c-v-col> ケアマネモックアップ開発ダイヤログ画面確認用ページ </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
      >
        ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- 2025/04/03 KMD DAM XUAN HIEU ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr10939('1')"
      >
        GUI01231_［履歴選択］画面 計画ﾓﾆﾀﾘﾝｸﾞ(パッケージプランフラグ=1)
      </v-btn>
      <g-custom-or-10939
        v-if="showDialogOr10939"
        v-bind="Or10939"
        v-model="or10939Type"
        :oneway-model-value="or10939Data"
      />
    </c-v-col>
  </c-v-row>
  <!-- 2025/04/03 KMD DAM XUAN HIEU ADD END-->
  <!-- 2025/04/03 KMD DAM XUAN HIEU ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr10939('0')"
      >
        GUI01231_［履歴選択］画面 計画ﾓﾆﾀﾘﾝｸﾞ(パッケージプランフラグ=0)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- 2025/04/03 KMD DAM XUAN HIEU ADD END-->
</template>
