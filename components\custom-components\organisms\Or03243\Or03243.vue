<script setup lang="ts">
/**
 * GUI00834_［アセスメント（包括）］食事画面
 *
 * @description
 *
 * ［アセスメント（包括）］食事画面
 *
 * 画面ID_ GUI00834
 *
 * <AUTHOR>
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'

import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { TeX0008Logic } from '../../template/TeX0008/TeX0008.logic'
import type { CareLabelType, CareLocationLabelType, Or34069Type } from '../Or34069/Or34069.type'
import { Or34069Const } from '../Or34069/Or34069.constants'
import { Or53105Const } from '../Or53105/Or53105.constants'
import { Or53105Logic } from '../Or53105/Or53105.logic'
import type {
  ConcreteCareItemType,
  ConcreteContentType,
  OrX00096OnewayType,
  OrX0096Type,
} from '../OrX0096/OrX0096.type'
import { Or59423Const } from '../Or59423/Or59423.constants'
import { Or59423Logic } from '../Or59423/Or59423.logic'
import { TeX0008Const } from '../../template/TeX0008/TeX0008.constants'
import { OrX0096Const } from '../OrX0096/OrX0096.constants'
import type { TeX0008StateType } from '../../template/TeX0008/TeX0008.type'
import type { Or03243OnewayType } from './Or34243.type'
import { Or03243Const } from './Or03243.constants'
import { Or03243Logic } from './Or03243.logic'
import type { TeX0008Type } from '~/types/cmn/business/components/TeX0008Type'
import {
  useCmnCom,
  useScreenStore,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type {
  AssessmentComprehensiveMealInitSelectEntity,
  AssessmentComprehensiveMealInitSelectOutEntity,
  AssessmentComprehensiveMealUpdateOutEntity,
} from '~/repositories/cmn/entities/assessmentComprehensiveMealInitSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { AssessmentComprehensiveMealUpdateInEntity } from '~/repositories/cmn/entities/assessmentComprehensiveMealUpdateInEntity'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or51105OnewayType, Or51105Type } from '~/types/cmn/business/components/Or51105Type'
import type { Or34069OnewayType } from '~/types/cmn/business/components/Or34069Type'
import type {
  assessmentComprehensiveQuestionInEntity,
  assessmentComprehensiveQuestionOutWebEntity,
} from '~/repositories/cmn/entities/assessmentComprehensiveQuestionSelect'

/**************************************************
 * Props
 **************************************************/

interface Props {
  uniqueCpId: string
  onewayModelValue: Or03243OnewayType
  parentUniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const { t } = useI18n()

const { getChildCpBinds } = useScreenUtils()

/**
 * ロード状態制御
 */
const isLoading = ref<boolean>(false)

/**
 * componentRef
 */
const componentRef = ref<HTMLDivElement | null>(null)

/**
 * 保存用テーブルデータ
 */
const tableData = ref<Or34069Type>({} as Or34069Type)

/**
 * 画面更新区分
 */
const screenUpdateKbn = ref(UPDATE_KBN.DELETE)

/**
 * 共通情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()

/**
 * データの更新回数
 */
let updateNum = ''

/**
 * API返却値の番号リストを一時保存する
 */
let dotNumberList: assessmentComprehensiveQuestionOutWebEntity['problemDotSolutionEtcInfo']['problemDotNumberInfoList'] =
  []

/**
 * 画面複写表示モード
 */
let screenFromDuplicate = false

/**
 * Twoway
 */
const local = reactive({
  tableHeader: {},
  commonInfo: {} as TeX0008Type,
  orX0096: {
    listSection: props.onewayModelValue.questionList ?? [],
  } as OrX0096Type,
  or51105: {
    kigoImiList: [],
  } as Or51105Type,
  or34096: {
    title: t('label.assessment-comprehensive-tab1-table-title'),
    careItems: [
      {
        title: t('label.assessment-comprehensive-tab1-table-cooking-title'),
        showMode: '0',
        careLabel: [
          {
            label: t('label.assessment-comprehensive-tab1-table-cooking-cn001'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.assessment-comprehensive-tab1-table-preparation-title1'),
        showMode: '2',
        careLabel: [
          {
            label: t('label.assessment-comprehensive-tab1-table-preparation-cn002'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-preparation-cn003'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-preparation-cn004'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-preparation-cn005'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-preparation-cn006'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.assessment-comprehensive-tab1-table-meal-care-title'),
        showMode: '0',
        careLabel: [
          {
            label: t('label.assessment-comprehensive-tab1-table-meal-care-cn007'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-meal-care-cn008'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-meal-care-cn009'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-meal-care-cn010'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-meal-care-cn011'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-meal-care-cn012'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-meal-care-cn013'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-meal-care-cn014'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.assessment-comprehensive-tab1-table-oral-liquid-diet-title1'),
        showMode: '1',
        careLabel: [
          {
            label: t('label.assessment-comprehensive-tab1-table-oral-liquid-diet-cn015'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-oral-liquid-diet-cn016'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-oral-liquid-diet-cn017'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.assessment-comprehensive-tab1-table-nutritional-title'),
        showMode: '0',
        careLabel: [
          {
            label: t('label.assessment-comprehensive-tab1-table-nutritional-cn018'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-nutritional-cn019'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-nutritional-cn020'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-nutritional-cn021'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t('label.assessment-comprehensive-tab1-table-nutritional-cn022'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t(
          'label.assessment-comprehensive-tab1-table-fluids-given-by-vein-transfusion-title1'
        ),
        showMode: '2',
        careLabel: [
          {
            label: t(
              'label.assessment-comprehensive-tab1-table-fluids-given-by-vein-transfusion-cn023'
            ),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t(
              'label.assessment-comprehensive-tab1-table-fluids-given-by-vein-transfusion-cn024'
            ),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t(
              'label.assessment-comprehensive-tab1-table-fluids-given-by-vein-transfusion-cn025'
            ),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t(
              'label.assessment-comprehensive-tab1-table-fluids-given-by-vein-transfusion-cn026'
            ),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            label: t(
              'label.assessment-comprehensive-tab1-table-fluids-given-by-vein-transfusion-cn027'
            ),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
    ],
    careLocationItems: [
      {
        title: t('label.assessment-comprehensive-tab1-table-meal-label-title'),
        showMode: '0',
        careLocationLabel: [
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-label-cb001'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-label-cb002'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-label-cb003'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-label-cb004'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-label-cb005'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
        ],
      },
      {
        title: t('label.assessment-comprehensive-tab1-table-meal-division-title'),
        showMode: '0',
        careLocationLabel: [
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-division-cb006'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-division-cb007'),
            inputShowMode: {
              appendInput: '2',
              unit: t('label.assessment-comprehensive-tab1-table-meal-division-cb007-unit'),
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '7',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-division-cb008'),
            inputShowMode: {
              appendInput: '2',
              unit: t('label.assessment-comprehensive-tab1-table-meal-division-cb008-unit'),
            },
            inputOptions: {
              maxLength: '5',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-division-cb009'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-division-cb010'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
        ],
      },
      {
        title: t('label.assessment-comprehensive-tab1-table-meal-staple-food-title'),
        showMode: '0',
        careLocationLabel: [
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-staple-food-cb011'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-staple-food-cb012'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-staple-food-cb013'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
        ],
      },
      {
        title: t('label.assessment-comprehensive-tab1-table-meal-side-dish-title'),
        showMode: '0',
        careLocationLabel: [
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-side-dish-cb014'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-side-dish-cb015'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-side-dish-cb016'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-side-dish-cb017'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
        ],
      },
      {
        title: t('label.assessment-comprehensive-tab1-table-meal-tools-title'),
        showMode: '1',
        careLocationLabel: [
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-tools-cn018'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-tools-cn019'),
            inputShowMode: {
              appendInput: '0',
              unit: 'Kcal',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-tools-cn020'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-tools-cn021'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-tools-cn022'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: t('label.assessment-comprehensive-tab1-table-meal-tools-cn023'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
          {
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
            inputOptions: {
              maxLength: '24',
            },
          },
        ],
      },
    ],
  } as Or34069Type,
})

/**
 * ロカールOneway
 */
const localOneway = reactive({
  orX0096Oneway: {
    showInputFlg: false,
    tableDisplayFlg: true,
    b1Cd: '1',
  } as OrX00096OnewayType,
  or51105Oneway: {
    sc1Id: '',
    cc1Id: '',
  } as Or51105OnewayType,
  or34069Oneway: {
    showTableBodyFlg: true,
  } as Or34069OnewayType,
})

const or34069 = ref({ uniqueCpId: '', showTableBodyFlg: true })
const or53105 = ref({ uniqueCpId: '' })
const orX0096 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or21814 = ref({ uniqueCpId: '' })

/**************************************************
 * Pinia
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or34069Const.CP_ID(0)]: or34069.value,
  [Or53105Const.CP_ID(0)]: or53105.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [OrX0096Const.CP_ID(1)]: orX0096.value,
})

/**************************************************
 * computed
 **************************************************/

/**
 * ダイアログ表示フラグ
 */
const showDialogOr53105CksFlg1 = computed(() => {
  // Or53105 cks_flg=1 のダイアログ開閉状態
  return Or53105Logic.state.get(or53105.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const showOr21814DialogFlg = computed(() => {
  // ダイアログの開閉フラグ
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 画面データ変更フラグ
 */
const _isEdit = computed(() => {
  const isEditByUniqueCpIds = useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
  return isEditByUniqueCpIds
})

/**************************************************
 * 関数定義
 **************************************************/
/**
 * コントロール初期化
 */
const initControl = () => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      firstBtnLabel: t('btn.yes'),
      firstBtnType: 'normal1',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      dialogTitle: t('label.confirm'),
    },
  })
}

/**
 * 番号設定関数
 *
 * @param index - 変換前のインデックス
 */
function setCircleNumber(index: number) {
  const circleNumbers = [
    '①',
    '②',
    '③',
    '④',
    '⑤',
    '⑥',
    '⑦',
    '⑧',
    '⑨',
    '⑩',
    '⑪',
    '⑫',
    '⑬',
    '⑭',
    '⑮',
    '⑯',
    '⑰',
    '⑱',
    '⑲',
    '⑳',
  ]
  if (index >= 1 && index <= 20) {
    return circleNumbers[index - 1]
  }
}

/**
 * 初期情報データより、テーブル初期化を実行する
 *
 * @param tableData - 初期化APIから取得したデータ
 *
 * @param items - テーブルデータ
 */
function initTableData<Label extends CareLabelType | CareLocationLabelType>(
  tableData: (string | undefined)[],
  items: Label
) {
  const getDefaultValue = (index: number, defaultValue = '') => tableData[index] ?? defaultValue

  if (tableData.length === Or03243Const.DEFAULT.TABLE_INIT_DATA_LIST_TYPE_CARE_CONTENT) {
    if ('offerValue' in items && 'familyValue' in items && 'planValue' in items) {
      items.offerValue = { modelValue: getDefaultValue(0) }
      items.familyValue = { modelValue: getDefaultValue(1) }
      items.planValue = { modelValue: getDefaultValue(2) }
    }
  } else if (
    tableData.length === Or03243Const.DEFAULT.TABLE_INIT_DATA_LIST_TYPE_CARE_LOCATION_NO_INPUT
  ) {
    if ('locationValue' in items) {
      items.locationValue = { modelValue: getDefaultValue(0) }
    }
  } else if (tableData.length === Or03243Const.DEFAULT.TABLE_INIT_DATA_LIST_TYPE_CARE_LOCATION) {
    if ('locationValue' in items && 'inputShowMode' in items) {
      items.locationValue = { modelValue: getDefaultValue(0) }
      items.inputContent = { value: getDefaultValue(1) }
    }
  }
}

/**
 * 共通情報取得
 */
function getCommonInfo() {
  const commonInfo = TeX0008Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo = commonInfo
    screenUpdateKbn.value = commonInfo.updateKbn ?? ''
    // 更新区分クリア
    if (commonInfo.updateKbn === UPDATE_KBN.DELETE) {
      localOneway.orX0096Oneway.tableDisplayFlg = false
    } else {
      localOneway.orX0096Oneway.tableDisplayFlg = true
    }
  }
}

/**
 * 複写モード共通情報取得
 */
const getDuplicateCommonInfo = () => {
  const commonInfo = Or59423Logic.data.get(props.parentUniqueCpId + Or59423Const.CP_ID(0))
  if (commonInfo) {
    local.commonInfo = {
      ninteiFormF: commonInfo.duplicateInfo?.ninteiFormF,
      activeTabId: commonInfo.duplicateInfo?.activeTabId,
      jigyoId: commonInfo.duplicateInfo?.jigyoId,
      houjinId: commonInfo.duplicateInfo?.houjinId,
      shisetuId: commonInfo.duplicateInfo?.shisetuId,
      userId: commonInfo.duplicateInfo?.userId,
      syubetsuId: commonInfo.duplicateInfo?.syubetsuId,
      createYmd: commonInfo.duplicateInfo?.createYmd,
      historyUpdateKbn: commonInfo.duplicateInfo?.historyUpdateKbn,
      historyModifiedCnt: commonInfo.duplicateInfo?.historyModifiedCnt,
      sc1Id: commonInfo.duplicateInfo?.sc1Id,
      recId: commonInfo.duplicateInfo?.recId,
      cc1Id: commonInfo.duplicateInfo?.cc1Id,
      createUserId: commonInfo.duplicateInfo?.createUserId,
      svJigyoId: commonInfo.duplicateInfo?.svJigyoId,
      updateKbn: commonInfo.duplicateInfo?.updateKbn,
      planPeriodFlg: commonInfo.duplicateInfo?.planPeriodFlg,
      svjigyoName: commonInfo.duplicateInfo.svjigyoName,
    }
  }
}

/**
 * 親画面のstateを変更する
 *
 * @param state - state
 */
const setTeX0008State = (state: TeX0008StateType) => {
  TeX0008Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

/**
 * 初期情報取得
 */
async function getInitDataInfo() {
  try {
    const inputData: AssessmentComprehensiveMealInitSelectEntity = {
      cc1Id: local.commonInfo.cc1Id ?? '',
      sc1Id: local.commonInfo.sc1Id ?? '',
    }

    // 初期情報取得APIを呼び出す
    const resData: BaseResponseBody<AssessmentComprehensiveMealInitSelectOutEntity> =
      await ScreenRepository.select('assessmentComprehensiveMealInitSelect', inputData)
    // 返却値チェック
    if (resData.data) {
      processInfoData(resData, true)
      updateNum = resData.data.subInfoMeal.modifiedCnt ?? ''
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 問題点初期情報取得
 */
const getProblemDotSolutionEtcInfoData = async () => {
  try {
    const inputData: assessmentComprehensiveQuestionInEntity = {
      cc1Id: local.commonInfo?.cc1Id,
      sc1Id: local.commonInfo?.sc1Id,
      // タブID：「1:食事」
      typeId: Or03243Const.DEFAULT.TAB_ID,
    }

    const resData: BaseResponseBody<assessmentComprehensiveQuestionOutWebEntity> =
      await ScreenRepository.select('problemDotSolutionEtcInfoSelect', inputData)
    if (resData.data) {
      processParentData(resData.data.problemDotSolutionEtcInfo)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 更新回数チェック
 *
 * @param isHistory - 履歴更新区分
 */
const getRequestScreenUpdataKbn = (isHistory: boolean) => {
  // 更新回数が不存在する場合、更新区分を新規にする
  if (isHistory) {
    return local.commonInfo.historyUpdateKbn === UPDATE_KBN.NONE
      ? UPDATE_KBN.UPDATE
      : (local.commonInfo.historyUpdateKbn ?? '')
  } else {
    if (updateNum === '' && screenUpdateKbn.value === UPDATE_KBN.NONE) return UPDATE_KBN.CREATE
    return screenUpdateKbn.value === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : screenUpdateKbn.value
  }
}

/**
 * 複写情報取得
 */
const getDuplicateDataInfo = async () => {
  const inputData: AssessmentComprehensiveMealInitSelectEntity = {
    cc1Id: local.commonInfo.duplicateCareCheckId ?? '',
    sc1Id: local.commonInfo.duplicatePlanId ?? '',
  }
  // 初期情報取得APIを呼び出す
  const resData: BaseResponseBody<AssessmentComprehensiveMealInitSelectOutEntity> =
    await ScreenRepository.select('assessmentComprehensiveMealInitSelect', inputData)
  // 返却値チェック
  if (resData.data) {
    processInfoData(resData, false)
  }
}

/**
 * 複写情報取得「問題点情報」
 */
const getDuplicateProblemDotSolutionEtcInfoData = async () => {
  try {
    const inputData: assessmentComprehensiveQuestionInEntity = {
      cc1Id: local.commonInfo?.duplicateCareCheckId ?? '',
      sc1Id: local.commonInfo?.duplicatePlanId ?? '',
      // タブID：「1：食事」
      typeId: '1',
    }

    const resData: BaseResponseBody<assessmentComprehensiveQuestionOutWebEntity> =
      await ScreenRepository.select('problemDotSolutionEtcInfoSelect', inputData)
    if (resData.data) {
      processDuplicateData(resData.data.problemDotSolutionEtcInfo)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * API返却値処理
 *
 * @param resData - API返却値
 *
 * @param updateFlg - 更新回数フラグ
 */
const processInfoData = (
  resData: BaseResponseBody<AssessmentComprehensiveMealInitSelectOutEntity>,
  updateFlg: boolean
) => {
  if (!resData.data) return
  // 複写モードの場合、返却値を保存する
  if (props.onewayModelValue.screenMode === TeX0008Const.DEFAULT.MODE_COPY) {
    Or03243Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        comprehensiveQuestionInfo: props.onewayModelValue.comprehensiveQuestionInfo,
        result: resData,
      },
    })
  }
  // セレクト選択肢設定
  const { careOfferLocationInfoList, scheduleInfoList, familyInfoList, offerInfoList } =
    resData.data.markInfo
  localOneway.or34069Oneway.careOfferMarkMeaning = offerInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.careFamilyMarkMeaning = familyInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.carePlanMarkMeaning = scheduleInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.careLocationMarkMeaning = careOfferLocationInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })

  // 複数回の更新による無限再帰を防ぐために、データをディープコピーし、以下の処理は別データに移管する
  tableData.value = cloneDeep(local.or34096)
  const {
    cn011,
    cn012,
    cn013,
    cn021,
    cn022,
    cn023,
    cn031,
    cn032,
    cn033,
    cn041,
    cn042,
    cn043,
    cn051,
    cn052,
    cn053,
    cn061,
    cn062,
    cn063,
    cn071,
    cn072,
    cn073,
    cn081,
    cn082,
    cn083,
    cn091,
    cn092,
    cn093,
    cn101,
    cn102,
    cn103,
    cn111,
    cn112,
    cn113,
    cn121,
    cn122,
    cn123,
    cn131,
    cn132,
    cn133,
    cn141,
    cn142,
    cn143,
    cn151,
    cn152,
    cn153,
    cn161,
    cn162,
    cn163,
    cn171,
    cn172,
    cn173,
    cn181,
    cn182,
    cn183,
    cn191,
    cn192,
    cn193,
    cn201,
    cn202,
    cn203,
    cn211,
    cn212,
    cn213,
    cn221,
    cn222,
    cn223,
    cn231,
    cn232,
    cn233,
    cn241,
    cn242,
    cn243,
    cn251,
    cn252,
    cn253,
    cn261,
    cn262,
    cn263,
    cn271,
    cn272,
    cn273,
    cb011,
    cb021,
    cb031,
    cb041,
    cb051,
    cb052Knj,
    cb061,
    cb071,
    cb072Knj,
    cb081,
    cb082Knj,
    cb091,
    cb101,
    cb102Knj,
    cb111,
    cb121,
    cb131,
    cb132Knj,
    cb141,
    cb151,
    cb161,
    cb171,
    cb172Knj,
    cb181,
    cb191,
    cb201,
    cb211,
    cb221,
    cb222Knj,
    cb231,
    cb232Knj,
    cb241,
    cb242Knj,
    cb251,
    cb252Knj,
    cb261,
    cb262Knj,
    cb271,
    cb272Knj,
    modifiedCnt,
  } = resData.data.subInfoMeal
  // 調理の内容
  initTableData([cn011, cn012, cn013], tableData.value.careItems[0].careLabel[0])
  // // 準備後始末の内容
  initTableData([cn021, cn022, cn023], tableData.value.careItems[1].careLabel[0])
  initTableData([cn031, cn032, cn033], tableData.value.careItems[1].careLabel[1])
  initTableData([cn041, cn042, cn043], tableData.value.careItems[1].careLabel[2])
  initTableData([cn051, cn052, cn053], tableData.value.careItems[1].careLabel[3])
  initTableData([cn061, cn062, cn063], tableData.value.careItems[1].careLabel[4])
  // 食事等々の摂取介護
  initTableData([cn071, cn072, cn073], tableData.value.careItems[2].careLabel[0])
  initTableData([cn081, cn082, cn083], tableData.value.careItems[2].careLabel[1])
  initTableData([cn091, cn092, cn093], tableData.value.careItems[2].careLabel[2])
  initTableData([cn101, cn102, cn103], tableData.value.careItems[2].careLabel[3])
  initTableData([cn111, cn112, cn113], tableData.value.careItems[2].careLabel[4])
  initTableData([cn121, cn122, cn123], tableData.value.careItems[2].careLabel[5])
  initTableData([cn131, cn132, cn133], tableData.value.careItems[2].careLabel[6])
  initTableData([cn141, cn142, cn143], tableData.value.careItems[2].careLabel[7])
  // 経口流動食
  initTableData([cn151, cn152, cn153], tableData.value.careItems[3].careLabel[0])
  initTableData([cn161, cn162, cn163], tableData.value.careItems[3].careLabel[1])
  initTableData([cn171, cn172, cn173], tableData.value.careItems[3].careLabel[2])
  // 経管栄養
  initTableData([cn181, cn182, cn183], tableData.value.careItems[4].careLabel[0])
  initTableData([cn191, cn192, cn193], tableData.value.careItems[4].careLabel[1])
  initTableData([cn201, cn202, cn203], tableData.value.careItems[4].careLabel[2])
  initTableData([cn211, cn212, cn213], tableData.value.careItems[4].careLabel[3])
  initTableData([cn221, cn222, cn223], tableData.value.careItems[4].careLabel[4])
  // 輸液・輸血
  initTableData([cn231, cn232, cn233], tableData.value.careItems[5].careLabel[0])
  initTableData([cn241, cn242, cn243], tableData.value.careItems[5].careLabel[1])
  initTableData([cn251, cn252, cn253], tableData.value.careItems[5].careLabel[2])
  initTableData([cn261, cn262, cn263], tableData.value.careItems[5].careLabel[3])
  initTableData([cn271, cn272, cn273], tableData.value.careItems[5].careLabel[4])

  // 食事の場所
  initTableData([cb011], tableData.value.careLocationItems[0].careLocationLabel[0])
  initTableData([cb021], tableData.value.careLocationItems[0].careLocationLabel[1])
  initTableData([cb031], tableData.value.careLocationItems[0].careLocationLabel[2])
  initTableData([cb041], tableData.value.careLocationItems[0].careLocationLabel[3])
  initTableData([cb051, cb052Knj], tableData.value.careLocationItems[0].careLocationLabel[4])
  // 食事の区分
  initTableData([cb061], tableData.value.careLocationItems[1].careLocationLabel[0])
  initTableData([cb071, cb072Knj], tableData.value.careLocationItems[1].careLocationLabel[1])
  initTableData([cb081, cb082Knj], tableData.value.careLocationItems[1].careLocationLabel[2])
  initTableData([cb091], tableData.value.careLocationItems[1].careLocationLabel[3])
  initTableData([cb101, cb102Knj], tableData.value.careLocationItems[1].careLocationLabel[4])
  // 主食
  initTableData([cb111], tableData.value.careLocationItems[2].careLocationLabel[0])
  initTableData([cb121], tableData.value.careLocationItems[2].careLocationLabel[1])
  initTableData([cb131, cb132Knj], tableData.value.careLocationItems[2].careLocationLabel[2])

  // 副食
  initTableData([cb141], tableData.value.careLocationItems[3].careLocationLabel[0])
  initTableData([cb151], tableData.value.careLocationItems[3].careLocationLabel[1])
  initTableData([cb161], tableData.value.careLocationItems[3].careLocationLabel[2])
  initTableData([cb171, cb172Knj], tableData.value.careLocationItems[3].careLocationLabel[3])

  // 食事用具
  initTableData([cb181], tableData.value.careLocationItems[4].careLocationLabel[0])
  initTableData([cb191], tableData.value.careLocationItems[4].careLocationLabel[1])
  initTableData([cb201], tableData.value.careLocationItems[4].careLocationLabel[2])
  initTableData([cb211], tableData.value.careLocationItems[4].careLocationLabel[3])
  initTableData([cb221, cb222Knj], tableData.value.careLocationItems[4].careLocationLabel[4])
  initTableData([cb231, cb232Knj], tableData.value.careLocationItems[4].careLocationLabel[5])
  initTableData([cb241, cb242Knj], tableData.value.careLocationItems[4].careLocationLabel[6])
  initTableData([cb251, cb252Knj], tableData.value.careLocationItems[4].careLocationLabel[7])
  initTableData([cb261, cb262Knj], tableData.value.careLocationItems[4].careLocationLabel[8])
  initTableData([cb271, cb272Knj], tableData.value.careLocationItems[4].careLocationLabel[9])
  // 処理済みのデータを画面に表示する
  local.or34096 = tableData.value
  // 複写再発火の場合は、サブ情報の更新回数は上書きしない
  if (updateFlg) {
    updateNum = modifiedCnt ?? ''
  } else {
    // 複写発火の場合は、画面の更新区分を `U` にする
    screenUpdateKbn.value = UPDATE_KBN.UPDATE
  }
}

/**
 * 新規処理
 */
const createNew = () => {
  local.or34096.careItems.forEach((item) => {
    // 更新区分を新規にする
    screenUpdateKbn.value = UPDATE_KBN.CREATE
    item.careLabel.forEach((sItem) => {
      sItem.planValue = { modelValue: '' }
      sItem.offerValue = { modelValue: '' }
      sItem.familyValue = { modelValue: '' }
      if (sItem.label === '') {
        sItem.inputContent = {
          value: '',
        }
      }
    })
  })
  local.or34096.careLocationItems.forEach((item) => {
    item.careLocationLabel.forEach((sItem) => {
      sItem.locationValue = { modelValue: '' }
      if (
        sItem.label === '' ||
        sItem.inputShowMode.appendInput === Or03243Const.DEFAULT.INPUT_DISPLAY_FLG_LAST ||
        sItem.inputShowMode.appendInput === Or03243Const.DEFAULT.INPUT_DISPLAY_FLG_UNIT
      ) {
        sItem.inputContent = {
          value: '',
        }
      }
    })
  })
  // 各ラベルをクリア
  const newQuestionList = local.orX0096.listSection.map((item) => {
    return {
      ...item,
      isHaveIssuesFlg: false,
      isPlanningFlg: false,
    }
  })
  // 要介護課題等々クリア
  local.orX0096 = {
    listSection: newQuestionList,
    concreteCareItemList: [],
    concreteContentList: [],
  }
  // 各プルダウン選択肢リストクリア
  localOneway.or34069Oneway = {
    carePlanMarkMeaning: [],
    careOfferMarkMeaning: [],
    careFamilyMarkMeaning: [],
    careLocationMarkMeaning: [],
    showTableBodyFlg: true,
  }
}

/**
 * 問題点情報の共通処理
 *
 * @param comprehensiveQuestionInfo - API返却値
 */
function processParentData(
  comprehensiveQuestionInfo: assessmentComprehensiveQuestionOutWebEntity['problemDotSolutionEtcInfo']
) {
  localOneway.orX0096Oneway.officeId = local.commonInfo.svJigyoId ?? ''
  // 本画面のデータを絞り出す
  // 対応するケア項目リスト
  const concreteCareItemList: ConcreteCareItemType[] = comprehensiveQuestionInfo.careItemList
    .filter((item) => item.b1Cd === Or03243Const.DEFAULT.TAB_ID)
    .map((item) => {
      return {
        key: item.cc33Id,
        content: item.memoKnj,
        dmyCc32Id: item.dmyCc32Id,
        modifiedCnt: item.modifiedCnt,
        cc33Id: item.cc33Id,
        b1Cd: item.b1Cd,
        cc32Id: item.cc32Id,
        seq: item.seq,
        ci2Id: item.ci2Id,
        updateKbn: '',
      }
    })
  local.orX0096.concreteCareItemList = concreteCareItemList
  // 具体的内容
  const concreteContentList: ConcreteContentType[] =
    comprehensiveQuestionInfo.concreteContentsInfoList
      .filter((item) => item.b1Cd === Or03243Const.DEFAULT.TAB_ID)
      .map((item): ConcreteContentType => {
        return {
          key: item.cc32Id,
          correspondenceKeys: [],
          content: item.memoKnj,
          cc32Id: item.cc32Id,
          modifiedCnt: item.modifiedCnt,
          juni: item.juni,
          b1Cd: item.b1Cd,
          seq: item.seq,
          cc32Type: item.cc32Type,
          ci1Id: item.ci1Id,
          dmyB4Cd: item.dmyB4Cd,
          b4Cd: item.b4Cd,
          number: '',
          updateKbn: '',
        }
      })
  // 番号リストを作成
  const numberList = comprehensiveQuestionInfo.problemDotNumberInfoList
    .filter((item) => item.b1Cd === Or03243Const.DEFAULT.TAB_ID)
    .map((item) => {
      return {
        cc32Id: item.cc32Id,
        b4Cd: item.b4Cd,
        seq: item.seq,
        modifiedCnt: item.modifiedCnt1,
        cc34Id: item.cc34Id,
        b1Cd: item.b1Cd,
        modifiedCnt1: item.modifiedCnt1,
      }
    })
  if (concreteContentList.length > 0) {
    concreteContentList.forEach((item) => {
      const findedItem = numberList.filter((sItem) => sItem.cc32Id === item.cc32Id)
      if (findedItem) {
        item.correspondenceKeys = findedItem
        item.number = findedItem.map((item) => setCircleNumber(parseInt(item.b4Cd))).join('')
      }
    })
  }

  local.orX0096.concreteContentList = concreteContentList
  /**
   * 問題点リスト
   */
  const questionList = comprehensiveQuestionInfo.problemDotSolutionInfoList.filter(
    (item) => item.b1Cd === Or03243Const.DEFAULT.TAB_ID
  )
  local.orX0096.listSection = local.orX0096.listSection.map((item) => {
    const findedItem = questionList.find((sItem) => sItem.b4Cd === item.b4cd)
    if (findedItem) {
      return {
        ...item,
        cc31Id: findedItem?.cc31Id ?? '',
        isPlanningFlg: findedItem.f1 === Or03243Const.DEFAULT.API_RESULT_CHECKON,
        isHaveIssuesFlg: findedItem.f2 === Or03243Const.DEFAULT.API_RESULT_CHECKON,
        modifiedCnt: findedItem?.modifiedCnt,
      }
    } else {
      return {
        ...item,
        isPlanningFlg: false,
        isHaveIssuesFlg: false,
        cc31Id: '',
        modifiedCnt: Or03243Const.DEFAULT.DATA_DEFAULT,
      }
    }
  })
  dotNumberList = comprehensiveQuestionInfo.problemDotNumberInfoList
  localOneway.orX0096Oneway.maxCount = parseInt(comprehensiveQuestionInfo.cc32IdMax)
  // 医療画面
  local.orX0096.thingsToKeepInMindContentsInfo =
    comprehensiveQuestionInfo.thingsToKeepInMindContentsInfo
}

/**
 * tabを制御
 *
 * @param component - コンポーネント
 */
const disableTab = (component: HTMLElement) => {
  const focusableSelectors = [
    'a[href]',
    'area[href]',
    'input',
    'select',
    'textarea',
    'button',
    'iframe',
    '[tabindex]',
    '[contenteditable]',
  ]

  const focusables = component.querySelectorAll(focusableSelectors.join(','))
  focusables.forEach((el) => {
    el.setAttribute('tabindex', '-1')
  })
}

/**
 * APIリクエストパラメータ作成
 *
 * @param request - リクエスト
 */
const createRequest = (request: string | undefined) => {
  if (!request) return Or03243Const.DEFAULT.DATA_DEFAULT
  if (request === '') {
    return Or03243Const.DEFAULT.DATA_DEFAULT
  }
  if (screenUpdateKbn.value === UPDATE_KBN.CREATE) {
    return Or03243Const.DEFAULT.DATA_DEFAULT
  }
  return request
}

/**
 * 保存処理
 */
const _userSave = async () => {
  // 保存前のチェック
  if (!_isEdit.value && screenUpdateKbn.value === UPDATE_KBN.NONE && !screenFromDuplicate) {
    setShowDialog(t('message.i-cmn-21800'))
    return
  }
  try {
    isLoading.value = true
    // 子コンポーネントからデータを一括取得する
    const childrenTableCpBinds = getChildCpBinds(props.uniqueCpId, {
      [Or34069Const.CP_ID(0)]: { cpPath: Or34069Const.CP_ID(0), twoWayFlg: true },
      [OrX0096Const.CP_ID(1)]: { cpPath: OrX0096Const.CP_ID(1), twoWayFlg: true },
    })
    // ケアの内容を取得する
    const careItems = (childrenTableCpBinds[Or34069Const.CP_ID(0)].twoWayBind?.value as Or34069Type)
      .careItems
    // ケアの提供場所を取得する
    const careLocationItems = (
      childrenTableCpBinds[Or34069Const.CP_ID(0)].twoWayBind?.value as Or34069Type
    ).careLocationItems
    // 要介護者などの健康上や生活上の問題点及び解決すべき課題等を取得する
    const careRecipientHealthAndLifeIssues = childrenTableCpBinds[OrX0096Const.CP_ID(1)].twoWayBind
      ?.value as OrX0096Type
    const inputData: AssessmentComprehensiveMealUpdateInEntity = {
      // e文書用パラメータ
      edocumentUseParam: useCmnCom().getEDocumentParam(),
      // e文書削除用パラメータ
      edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
      // 履歴番号
      rirekiNo: createRequest(local.commonInfo.cc1Id),
      // 機能ID
      functionId: '0',
      // ログインID
      loginId: '1',
      // システムコード
      sysCd:  '0',
      // システム略称
      sysRyaku: Or03243Const.DEFAULT.SYSTEM_ACRONYM,
      // メニュー２名称
      menu2Knj:  ' ',
      // メニュー３名称
      menu3Knj: Or03243Const.DEFAULT.MENU3_NAME,
      // セクション名
      sectionName: Or03243Const.DEFAULT.SECTION_NAME,
      // 個人情報使用フラグ
      kojinhogoUsedFlg: Or03243Const.DEFAULT.DATA_DEFAULT,
      // 個人情報番号
      sectionAddNo: Or03243Const.DEFAULT.DATA_DEFAULT,
      // 事業者名
      svJigyoKnj: local.commonInfo.svjigyoName ?? '',
      // 作成者名
      createKnj: local.commonInfo.createUserName ?? '管理者　一郎',
      // 期間管理フラグ
      kikanKanriFlg: local.commonInfo.planPeriodFlg ?? '',
      // 計画対象期間番号
      sc1Number: local.commonInfo.sc1Id ?? '',
      // 開始日
      startYmd: local.commonInfo.startYmd ?? '',
      // 終了日
      endYmd: local.commonInfo.endYmd ?? '',
      cc1Id: createRequest(local.commonInfo.cc1Id),
      sc1Id: local.commonInfo.sc1Id ?? Or03243Const.DEFAULT.DATA_DEFAULT,
      houjinId: local.commonInfo.houjinId ?? '',
      shisetuId: local.commonInfo.shisetuId ?? '',
      svJigyoId: local.commonInfo.svJigyoId ?? '',
      userId: local.commonInfo.userId ?? '',
      syubetsuId: local.commonInfo.syubetsuId ?? '',
      createYmd: local.commonInfo.createYmd ?? '',
      updateKbn: getRequestScreenUpdataKbn(false),
      historyUpdateKbn: getRequestScreenUpdataKbn(true),
      historyModifiedCnt: createRequest(local.commonInfo.historyModifiedCnt),
      shokuId: local.commonInfo.createUserId === '' ? '1' : local.commonInfo.createUserId!,
      typeId: Or03243Const.DEFAULT.TAB_ID,
    }
    const subInfoMeal: AssessmentComprehensiveMealUpdateInEntity['subInfoMeal'] = {
      cn011: careItems[0].careLabel[0].offerValue.modelValue,
      cn012: careItems[0].careLabel[0].familyValue.modelValue,
      cn013: careItems[0].careLabel[0].planValue.modelValue,
      cn021: careItems[1].careLabel[0].offerValue.modelValue,
      cn022: careItems[1].careLabel[0].familyValue.modelValue,
      cn023: careItems[1].careLabel[0].planValue.modelValue,
      cn031: careItems[1].careLabel[1].offerValue.modelValue,
      cn032: careItems[1].careLabel[1].familyValue.modelValue,
      cn033: careItems[1].careLabel[1].planValue.modelValue,
      cn041: careItems[1].careLabel[2].offerValue.modelValue,
      cn042: careItems[1].careLabel[2].familyValue.modelValue,
      cn043: careItems[1].careLabel[2].planValue.modelValue,
      cn051: careItems[1].careLabel[3].offerValue.modelValue,
      cn052: careItems[1].careLabel[3].familyValue.modelValue,
      cn053: careItems[1].careLabel[3].planValue.modelValue,
      cn061: careItems[1].careLabel[4].offerValue.modelValue,
      cn062: careItems[1].careLabel[4].familyValue.modelValue,
      cn063: careItems[1].careLabel[4].planValue.modelValue,
      cn071: careItems[2].careLabel[0].offerValue.modelValue,
      cn072: careItems[2].careLabel[0].familyValue.modelValue,
      cn073: careItems[2].careLabel[0].planValue.modelValue,
      cn081: careItems[2].careLabel[1].offerValue.modelValue,
      cn082: careItems[2].careLabel[1].familyValue.modelValue,
      cn083: careItems[2].careLabel[1].planValue.modelValue,
      cn091: careItems[2].careLabel[2].offerValue.modelValue,
      cn092: careItems[2].careLabel[2].familyValue.modelValue,
      cn093: careItems[2].careLabel[2].planValue.modelValue,
      cn101: careItems[2].careLabel[3].offerValue.modelValue,
      cn102: careItems[2].careLabel[3].familyValue.modelValue,
      cn103: careItems[2].careLabel[3].planValue.modelValue,
      cn111: careItems[2].careLabel[4].offerValue.modelValue,
      cn112: careItems[2].careLabel[4].familyValue.modelValue,
      cn113: careItems[2].careLabel[4].planValue.modelValue,
      cn121: careItems[2].careLabel[5].offerValue.modelValue,
      cn122: careItems[2].careLabel[5].familyValue.modelValue,
      cn123: careItems[2].careLabel[5].planValue.modelValue,
      cn131: careItems[2].careLabel[6].offerValue.modelValue,
      cn132: careItems[2].careLabel[6].familyValue.modelValue,
      cn133: careItems[2].careLabel[6].planValue.modelValue,
      cn141: careItems[2].careLabel[7].offerValue.modelValue,
      cn142: careItems[2].careLabel[7].familyValue.modelValue,
      cn143: careItems[2].careLabel[7].planValue.modelValue,
      cn151: careItems[3].careLabel[0].offerValue.modelValue,
      cn152: careItems[3].careLabel[0].familyValue.modelValue,
      cn153: careItems[3].careLabel[0].planValue.modelValue,
      cn161: careItems[3].careLabel[1].offerValue.modelValue,
      cn162: careItems[3].careLabel[1].familyValue.modelValue,
      cn163: careItems[3].careLabel[1].planValue.modelValue,
      cn171: careItems[3].careLabel[2].offerValue.modelValue,
      cn172: careItems[3].careLabel[2].familyValue.modelValue,
      cn173: careItems[3].careLabel[2].planValue.modelValue,
      cn181: careItems[4].careLabel[0].offerValue.modelValue,
      cn182: careItems[4].careLabel[0].familyValue.modelValue,
      cn183: careItems[4].careLabel[0].planValue.modelValue,
      cn191: careItems[4].careLabel[1].offerValue.modelValue,
      cn192: careItems[4].careLabel[1].familyValue.modelValue,
      cn193: careItems[4].careLabel[1].planValue.modelValue,
      cn201: careItems[4].careLabel[2].offerValue.modelValue,
      cn202: careItems[4].careLabel[2].familyValue.modelValue,
      cn203: careItems[4].careLabel[2].planValue.modelValue,
      cn211: careItems[4].careLabel[3].offerValue.modelValue,
      cn212: careItems[4].careLabel[3].familyValue.modelValue,
      cn213: careItems[4].careLabel[3].planValue.modelValue,
      cn221: careItems[4].careLabel[4].offerValue.modelValue,
      cn222: careItems[4].careLabel[4].familyValue.modelValue,
      cn223: careItems[4].careLabel[4].planValue.modelValue,
      cn231: careItems[5].careLabel[0].offerValue.modelValue,
      cn232: careItems[5].careLabel[0].familyValue.modelValue,
      cn233: careItems[5].careLabel[0].planValue.modelValue,
      cn241: careItems[5].careLabel[1].offerValue.modelValue,
      cn242: careItems[5].careLabel[1].familyValue.modelValue,
      cn243: careItems[5].careLabel[1].planValue.modelValue,
      cn251: careItems[5].careLabel[2].offerValue.modelValue,
      cn252: careItems[5].careLabel[2].familyValue.modelValue,
      cn253: careItems[5].careLabel[2].planValue.modelValue,
      cn261: careItems[5].careLabel[3].offerValue.modelValue,
      cn262: careItems[5].careLabel[3].familyValue.modelValue,
      cn263: careItems[5].careLabel[3].planValue.modelValue,
      cn271: careItems[5].careLabel[4].offerValue.modelValue,
      cn272: careItems[5].careLabel[4].familyValue.modelValue,
      cn273: careItems[5].careLabel[4].planValue.modelValue,
      cb011: careLocationItems[0].careLocationLabel[0].locationValue.modelValue,
      cb021: careLocationItems[0].careLocationLabel[1].locationValue.modelValue,
      cb031: careLocationItems[0].careLocationLabel[2].locationValue.modelValue,
      cb041: careLocationItems[0].careLocationLabel[3].locationValue.modelValue,
      cb051: careLocationItems[0].careLocationLabel[4].locationValue.modelValue,
      cb052Knj: careLocationItems[0].careLocationLabel[4].inputContent?.value,
      cb061: careLocationItems[1].careLocationLabel[0].locationValue.modelValue,
      cb071: careLocationItems[1].careLocationLabel[1].locationValue.modelValue,
      cb072Knj: careLocationItems[1].careLocationLabel[1].inputContent?.value,
      cb081: careLocationItems[1].careLocationLabel[2].locationValue.modelValue,
      cb082Knj: careLocationItems[1].careLocationLabel[2].inputContent?.value,
      cb091: careLocationItems[1].careLocationLabel[3].locationValue.modelValue,
      cb101: careLocationItems[1].careLocationLabel[4].locationValue.modelValue,
      cb102Knj: careLocationItems[1].careLocationLabel[4].inputContent?.value,
      cb111: careLocationItems[2].careLocationLabel[0].locationValue.modelValue,
      cb121: careLocationItems[2].careLocationLabel[1].locationValue.modelValue,
      cb131: careLocationItems[2].careLocationLabel[2].locationValue.modelValue,
      cb132Knj: careLocationItems[2].careLocationLabel[2].inputContent?.value,
      cb141: careLocationItems[3].careLocationLabel[0].locationValue.modelValue,
      cb151: careLocationItems[3].careLocationLabel[1].locationValue.modelValue,
      cb161: careLocationItems[3].careLocationLabel[2].locationValue.modelValue,
      cb171: careLocationItems[3].careLocationLabel[3].locationValue.modelValue,
      cb172Knj: careLocationItems[3].careLocationLabel[3].inputContent?.value,
      cb181: careLocationItems[4].careLocationLabel[0].locationValue.modelValue,
      cb191: careLocationItems[4].careLocationLabel[1].locationValue.modelValue,
      cb201: careLocationItems[4].careLocationLabel[2].locationValue.modelValue,
      cb211: careLocationItems[4].careLocationLabel[3].locationValue.modelValue,
      cb221: careLocationItems[4].careLocationLabel[4].locationValue.modelValue,
      cb222Knj: careLocationItems[4].careLocationLabel[4].inputContent?.value,
      cb231: careLocationItems[4].careLocationLabel[5].locationValue.modelValue,
      cb232Knj: careLocationItems[4].careLocationLabel[5].inputContent?.value,
      cb241: careLocationItems[4].careLocationLabel?.[6].locationValue.modelValue,
      cb242Knj: careLocationItems[4].careLocationLabel?.[6]?.inputContent?.value,
      cb251: careLocationItems[4].careLocationLabel?.[7]?.locationValue.modelValue,
      cb252Knj: careLocationItems[4].careLocationLabel?.[7]?.inputContent?.value,
      cb261: careLocationItems[4].careLocationLabel?.[8]?.locationValue.modelValue,
      cb262Knj: careLocationItems[4].careLocationLabel?.[8]?.inputContent?.value,
      cb271: careLocationItems[4].careLocationLabel?.[9]?.locationValue.modelValue,
      cb272Knj: careLocationItems[4].careLocationLabel?.[9]?.inputContent?.value,
      modifiedCnt: updateNum === '' ? Or03243Const.DEFAULT.DATA_DEFAULT : updateNum,
    }
    inputData.subInfoMeal = subInfoMeal
    const problemInfoList = processProblemDotLsit(careRecipientHealthAndLifeIssues)

    // 問題点番号リスト
    if (screenUpdateKbn.value !== UPDATE_KBN.CREATE) {
      inputData.problemDotNumberInfoList = dotNumberList
    } else {
      inputData.problemDotNumberInfoList = undefined
    }
    // 具体的内容リスト
    inputData.concreteContentsInfoList = problemInfoList.concreteContentsInfoList
    // 対応するケア項目リスト
    inputData.careItemList = problemInfoList.careItemList
    // 問題点解決情報リスト
    inputData.problemDotSolutionInfoList = problemInfoList.issuseList
    const resData: AssessmentComprehensiveMealUpdateOutEntity = await ScreenRepository.update(
      'assessmentComprehensiveMealUpdate',
      inputData
    )
    if (resData.data) {
      // 保存処理返却値を親画面Piniaに設定する
      TeX0008Logic.data.set({
        uniqueCpId: props.parentUniqueCpId,
        value: {
          ...local.commonInfo,
          childrenScreenApiResult: {
            sc1Id: resData.data.sc1Id,
            cc1Id: resData.data.cc1Id,
          },
        },
      })
      // 保存処理完了、親画面を通知する
      setTeX0008State({ saveCompletedState: true })
      // 更新区分クリア
      screenUpdateKbn.value = UPDATE_KBN.NONE
      screenFromDuplicate = false
    }
  } finally {
    isLoading.value = false
  }
}

/**
 * 問題点や解決すべき課題等々情報保存前処理
 *
 * @param careRecipientHealthAndLifeIssues - 課題情報
 */
const processProblemDotLsit = (careRecipientHealthAndLifeIssues: OrX0096Type) => {
  return {
    issuseList: createProblemDotList(careRecipientHealthAndLifeIssues.listSection),
    concreteContentsInfoList: createConcreteContentsInfoList(
      careRecipientHealthAndLifeIssues.concreteContentList
    ),
    careItemList: createCareList(careRecipientHealthAndLifeIssues.concreteCareItemList),
  }
}

/**
 * 問題点解決情報リスト作成関数
 *
 * @param listSection - 課題情報
 */
const createProblemDotList = (listSection: OrX0096Type['listSection']) => {
  return listSection.map((item) => {
    return {
      cc31Id: item.cc31Id,
      b1Cd: '1',
      b4Cd: item.b4cd,
      f1: item.isPlanningFlg ? '1' : '0',
      f2: item.isHaveIssuesFlg ? '1' : '0',
      modifiedCnt: item.modifiedCnt,
    }
  })
}

/**
 * 具体的内容作成関数
 *
 * @param concreteContentList - 具体的内容情報
 */
const createConcreteContentsInfoList = (
  concreteContentList: OrX0096Type['concreteContentList']
) => {
  // 具体内容情報リスト
  let concreteContentsInfoList: AssessmentComprehensiveMealUpdateInEntity['concreteContentsInfoList'] =
    []
  concreteContentsInfoList = concreteContentList?.map((item, index) => {
    return {
      // 更新区分チェック
      cc32Id:
        item.updateKbn === UPDATE_KBN.CREATE ? Or03243Const.DEFAULT.DATA_DEFAULT : item.cc32Id,
      // 更新区分チェック、新規の場合は、画面記録IDを設定する
      cc32IdRecord: item.updateKbn === UPDATE_KBN.CREATE ? item.cc32Id : '',
      b1Cd: Or03243Const.DEFAULT.TAB_ID,
      memoKnj: item.content,
      seq: (index + 1).toString(),
      juni: item.juni,
      cc32Type: item.cc32Type,
      ci1Id: item.ci1Id,
      dmyB4Cd: item.correspondenceKeys.map((item) => {
        return {
          b4Cd: item.b4Cd,
        }
      }),
      updateKbn: item.updateKbn === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : item.updateKbn,
      modifiedCnt: item.modifiedCnt,
    }
  })
  return concreteContentsInfoList
}

/**
 * 対応するケア項目作成関数
 *
 * @param concreteCareItemList - 対応するケア項目情報
 */
const createCareList = (concreteCareItemList: OrX0096Type['concreteCareItemList']) => {
  let careItemList: AssessmentComprehensiveMealUpdateInEntity['careItemList'] = []
  careItemList = concreteCareItemList?.map((item, index) => {
    return {
      // 該当情報が新規の場合、画面に表示したIDを削除する
      cc33Id:
        item.updateKbn === UPDATE_KBN.CREATE ? Or03243Const.DEFAULT.DATA_DEFAULT : item.cc33Id,
      b1Cd: '1',
      memoKnj: item.content,
      cc32Id: item.cc32Id,
      seq: (index + 1).toString(),
      ci2Id: item.ci2Id,
      dmyCc32Id: item.dmyCc32Id,
      updateKbn: item.updateKbn === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : item.updateKbn,
      modifiedCnt: item.modifiedCnt,
    }
  })
  return careItemList
}

/**
 * 複写データ作成関数
 *
 * @param comprehensiveQuestionInfo - 複写する問題点情報データ
 */
const processDuplicateData = (
  comprehensiveQuestionInfo: assessmentComprehensiveQuestionOutWebEntity['problemDotSolutionEtcInfo']
) => {
  let maxCount = localOneway.orX0096Oneway.maxCount
  local.orX0096.listSection.forEach((item) => {
    // ID以外、他のデータを画面に設定する
    const findedItem = comprehensiveQuestionInfo.problemDotSolutionInfoList.find(
      (listSectionItem) => listSectionItem.b4Cd === item.b4Cd
    )
    if (findedItem) {
      item.isPlanningFlg = findedItem.f1 === Or03243Const.DEFAULT.API_RESULT_CHECKON
      item.isHaveIssuesFlg = findedItem.f2 === Or03243Const.DEFAULT.API_RESULT_CHECKON
    }
  })
  // 元データを削除する
  local.orX0096.concreteContentList?.forEach((item) => {
    item.updateKbn = UPDATE_KBN.DELETE
  })
  // 取得したデータ画面に表示する
  const concreteContensList = comprehensiveQuestionInfo.concreteContentsInfoList.map(
    (item): ConcreteContentType => {
      maxCount++
      return {
        key: item.cc32Id,
        correspondenceKeys: [],
        content: item.memoKnj,
        // ID再設定
        cc32Id: maxCount.toString(),
        modifiedCnt: Or03243Const.DEFAULT.DATA_DEFAULT,
        juni: item.juni,
        b1Cd: item.b1Cd,
        seq: item.seq,
        cc32Type: item.cc32Type,
        ci1Id: item.ci1Id,
        dmyB4Cd: item.dmyB4Cd,
        b4Cd: item.b4Cd,
        number: '',
        updateKbn: UPDATE_KBN.CREATE,
      }
    }
  )
  // 番号リストを作成
  const numberList = comprehensiveQuestionInfo.problemDotNumberInfoList.map((item) => {
    return {
      cc32Id: item.cc32Id,
      b4Cd: item.b4Cd,
      seq: item.seq,
      modifiedCnt: item.modifiedCnt1,
      cc34Id: item.cc34Id,
      b1Cd: item.b1Cd,
      modifiedCnt1: Or03243Const.DEFAULT.DATA_DEFAULT,
    }
  })
  // 番号リスト対応するCc32Id再設定
  numberList.forEach((item) => {
    // 対応するIDのデータを探す
    const findedItem = concreteContensList.find((sItem) => sItem.key === item.cc32Id)
    if (findedItem) {
      item.cc32Id = findedItem.cc32Id
    }
  })
  // 対応するケア項目リスト作成
  const concreteCareList = comprehensiveQuestionInfo.careItemList.map(
    (item, index): ConcreteCareItemType => {
      return {
        key: item.cc32Id,
        dmyCc32Id: item.dmyCc32Id,
        cc33Id: item.cc33Id,
        content: item.memoKnj,
        b1Cd: item.b1Cd,
        cc32Id: item.cc32Id,
        updateKbn: UPDATE_KBN.CREATE,
        modifiedCnt: Or03243Const.DEFAULT.DATA_DEFAULT,
        seq: (index + 1).toString(),
        ci2Id: item.ci2Id,
      }
    }
  )
  const updatedConcreteCareList = concreteCareList
    .map((item) => {
      const findedItem = concreteContensList.find((sItem) => sItem.key === item.cc32Id)
      if (findedItem) {
        return {
          ...item,
          cc32Id: findedItem.cc32Id,
          dmyCc32Id: findedItem.cc32Id,
        }
      } else {
        // 対応するアイテムが見つからない場合はnullを返す
        return null
      }
    })
    .filter((item) => item !== null) // nullをフィルタリングして最終的なリストを作成
  if (concreteContensList.length > 0) {
    concreteContensList.forEach((item) => {
      const findedItem = numberList.filter((sItem) => sItem.cc32Id === item.cc32Id)
      if (findedItem) {
        item.correspondenceKeys = findedItem
        item.number = findedItem.map((item) => setCircleNumber(parseInt(item.b4Cd))).join('')
      }
    })
  }
  local.orX0096.concreteContentList?.push(...concreteContensList)
  local.orX0096.concreteCareItemList?.push(...updatedConcreteCareList)
  localOneway.orX0096Oneway.maxCount = maxCount
}

/**
 * 削除処理を行う
 */
const userDelete = () => {
  // 更新区分を D に設定する
  screenUpdateKbn.value = UPDATE_KBN.DELETE
  // テーブルボディーを非表示にする
  localOneway.or34069Oneway.showTableBodyFlg = false
  localOneway.orX0096Oneway.tableDisplayFlg = false
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */

const setShowDialog = (paramDialogText: string) => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
}
/**
 * 画面再表示関数
 */
const reload = async () => {
  // 全表示フラグをtrueにする
  localOneway.or34069Oneway.showTableBodyFlg = true
  localOneway.orX0096Oneway.tableDisplayFlg = true
  screenFromDuplicate = false
  // 更新区分クリア
  screenUpdateKbn.value = UPDATE_KBN.NONE
  isLoading.value = true
  await getInitDataInfo()
  await getProblemDotSolutionEtcInfoData()
  isLoading.value = false
}

/**
 * 初期化処理
 */
onMounted(() => {
  initControl()
})

/**************************************************
 * watch関数
 **************************************************/

/** 画面イベント監視 */
watch(
  () => TeX0008Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    // 初期情報取得
    getCommonInfo()

    // 本画面ではない場所、処理を中断する
    if (local.commonInfo.activeTabId !== Or03243Const.DEFAULT.TAB_ID) {
      return
    }
    /** 再表示処理 */
    if (newValue?.isRefresh) {
      await reload()
    }
    /** 保存イベント処理 */
    if (newValue?.saveEventFlg) {
      await _userSave()
    }

    /** 削除イベント処理 */
    if (newValue?.deleteEventFlg) {
      userDelete()
    }

    // 新規処理
    if (newValue?.createEventFlg) {
      createNew()
    }

    /** 作成日変更処理 */
    if (newValue?.isCreateDateChanged) {
      return
    }

    /** お気に入りイベント処理 */
    if (newValue?.favoriteEventFlg) {
      return
    }

    /** 複写イベント処理 */
    if (newValue?.copyEventFlg) {
      isLoading.value = true
      await getDuplicateDataInfo()
      await getDuplicateProblemDotSolutionEtcInfoData()
      screenFromDuplicate = true
      isLoading.value = false
      return
    }

    /** 印刷イベント処理 */
    if (newValue?.printEventFlg) {
      return
    }

    /** マスタ他イベント処理 */
    if (newValue?.masterEventFlg) {
      return
    }

    /** 優先順位表示フラグ */
    if (newValue?.priorityOrderEventFlg) {
      return
    }
  },
  { deep: true }
)

/**
 * 複写モードイベント監視
 */
watch(
  () => Or59423Logic.event.get(props.parentUniqueCpId + Or59423Const.CP_ID(0)),
  async (newValue) => {
    if (!newValue) return

    // 複写モード共通情報取得
    getDuplicateCommonInfo()

    // 本画面ではない場合、処理終了
    if (local.commonInfo.activeTabId !== Or03243Const.DEFAULT.TAB_ID) {
      return
    }

    if (newValue.reloadEvent) {
      isLoading.value = true
      await getInitDataInfo()
      await getProblemDotSolutionEtcInfoData()
      // 全処理済み、タブ変更を禁止する
      if (props.onewayModelValue.screenMode === TeX0008Const.DEFAULT.MODE_COPY) {
        if (componentRef.value) {
          disableTab(componentRef.value)
        }
      }
      isLoading.value = false
    }
  }
)
</script>

<template>
  <div
    ref="componentRef"
    class="or03243Wrapper"
  >
    <v-overlay
      :model-value="isLoading"
      :persistent="false"
      class="align-center justify-center"
      ><v-progress-circular
        indeterminate
        color="primary"
      ></v-progress-circular
    ></v-overlay>
    <c-v-row no-gutters>
      <g-custom-or34069
        v-bind="or34069"
        :oneway-model-value="localOneway.or34069Oneway"
        :model-value="local.or34096"
      />
      <div
        class="w-100 mt-2"
        style="height: 1px; background-color: #cccccc"
      ></div>
      <g-custom-or-x0096
        v-bind="orX0096"
        :oneway-model-value="localOneway.orX0096Oneway"
        :model-value="local.orX0096"
      />
    </c-v-row>
    <g-custom-or-53105
      v-if="showDialogOr53105CksFlg1"
      v-bind="or53105"
    />
    <g-base-or-21814
      v-if="showOr21814DialogFlg"
      v-bind="or21814"
    />
  </div>
</template>

<style scoped lang="scss"></style>
