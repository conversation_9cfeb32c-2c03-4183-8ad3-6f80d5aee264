<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27761Const } from './Or27761.constants'
import type { Or27761StateType, TableData } from './Or27761.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01278Type, Mo01278OnewayType } from '~/types/business/components/Mo01278Type'
import type {
  Or27761Type,
  Or27761OnewayType,
  ShowData,
} from '~/types/cmn/business/components/Or27761Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'

/**
 * Or27761:有機体:表示順変更認定調査票特記事項モーダル
 * GUI01281_表示順変更認定調査票特記事項
 *
 * @description
 *［表示順変更:認定調査票特記事項］画面では、呼び出し元の画面の項目の表示順を並べ替えます。［表示順変更］と［表示順移動］で、並べ替える場合の操作性が異なります。
 *［表示順変更:認定調査票特記事項］画面は、［ケアマネ］→［認定調査］→［特記事項］画面→［表示順］ボタンをクリックすると表示されます。
 *
 * <AUTHOR> 陳攀
 */

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or27761Type
  onewayModelValue: Or27761OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
// ポスト最小幅
const columnMinWidth = ref<number[]>([110, 110, 549])
const localOneway = reactive({
  Or27761: {
    ...props.onewayModelValue,
  },
  // 情報ダイアログ
  mo00024Oneway: {
    width: '800px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or27761',
      toolbarTitle: t('label.display-order-modified-certification-survey'),
      toolbarName: 'Or27761ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00043OneWay: {
    tabItems: [
      {
        id: 'change',
        title: t('label.display-order-modified'),
        tooltipText: t('label.display-order-modified'),
      },
      {
        id: 'move',
        title: t('label.display-order-move'),
        tooltipText: t('label.display-order-move'),
      },
    ],
  } as Mo00043OnewayType,
  // はいボタン
  mo01265OnewayModelValue: {
    btnLabel: t('btn.delete-display-order'),
    tooltipText: t('tooltip.display-order-delete-success'),
  } as Mo01265OnewayType,
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  mo00009Oneway: {
    btnIcon: 'dialpad',
    variant: 'flat',
    labelColor: 'red',
    rounded: '0',
    minWidth: '30px',
    minHeight: '31px',
    maxHeight: '31px',
  } as Mo00009OnewayType,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27761StateType>({
  cpId: Or27761Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or27761Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27761Const.DEFAULT.IS_OPEN,
})

const or21814 = ref({ uniqueCpId: '' })

const local = reactive({
  mo00043: { id: 'change' } as Mo00043Type,
})

// 表示順変更テーブル情報
const tableData = ref<TableData[]>([])

// ダイアログ表示フラグ
const showDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
})
const tableDateMove = ref<TableData[]>([])

// 表示順変更テーブルヘッダ
const changeTableHeaders = [
  { title: t('label.display-order'), key: 'sort', align: 'center', width: '62px', sortable: false },
  {
    title: t('label.number'),
    key: 'number',
    align: 'center',
    sortable: false,
    width: '80px',
  },
  {
    title: t('label.content'),
    key: 'content',
    align: 'start',
    width: '480px',
    sortable: false,
  },
]

// 表示順移動テーブルヘッダ
const moveTableHeaders = [
  { title: '', key: 'action1', align: 'start', width: '62px', sortable: false },
  { title: t('label.display-order'), key: 'sort', align: 'start', width: '80px', sortable: false },
  {
    title: t('label.number'),
    key: 'number',
    align: 'start',
    sortable: false,
    width: '114px',
  },
  {
    title: t('label.content'),
    key: 'content',
    align: 'start',
    width: '540px',
    sortable: false,
  },
]
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  init()
})

/**
 * AC001_初期情報取得
 */
function init() {
  // 「表示順変更」タブ初期表示
  // AC001-1取得した一覧リストデータを一覧に設定する。
  if (props.onewayModelValue.indexList && props.onewayModelValue.indexList.length > 0) {
    const tempArray = props.onewayModelValue.indexList
    for (const data of tempArray) {
      const item = {
        sort: {
          modelValue: {
            value: String(data.sort),
          } as Mo01278Type,
          onewayModelValue: {
            max: 999999,
            min: 1,
          } as Mo01278OnewayType,
        },
        number: {
          onewayModelValue: {
            value: data.number,
            customClass: {
              itemClass: 'tex-al',
              outerClass: 'bg-inh',
            },
          } as Mo01337OnewayType,
        },
        content: {
          onewayModelValue: {
            value: data.content,
          } as Mo01337OnewayType,
        },
        sortBackup: String(data.sortBackup),
        id: String(data.id),
        memoKnj: String(data.memoKnj),
        seqNo: String(data.seqNo),
        modifiedCnt: String(data.modifiedCnt),
      } as TableData
      tableData.value.push(item)
    }
  }
}
/**
 * AC007-2ポップアップウィンドウで選択確認ダイアログを表示し
 *
 * @param errormsg - Message
 */
function showOr21814Msg(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: Or27761Const.BTN_TYPE.BLANK,
      secondBtnType: Or27761Const.BTN_TYPE.NORMAL_1,
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: Or27761Const.BTN_TYPE.NORMAL_3,
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * AC007-2_Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.secondBtnClickFlg) {
      // 注意ダイアログの確認ボタン押下時
      await nextTick()
      await confirmOk()
    }
    if (newValue.thirdBtnClickFlg) {
      // 注意ダイアログの閉じるボタン押下時
      confirmCancle()
    }
  }
)

/**
 * 注意ダイアログの確認ボタン押下時
 */
async function confirmOk() {
  const startTemp = tableData.value[dragState.start - 1]
  const endTemp = tableData.value[dragState.end - 1]
  const startSort = startTemp.sort.modelValue.value
  startTemp.sort.modelValue.value = endTemp.sort.modelValue.value
  endTemp.sort.modelValue.value = startSort
  tableData.value[dragState.start - 1] = endTemp
  tableData.value[dragState.end - 1] = startTemp
  let testShowData = [...tableData.value]
  testShowData = testShowData
    .slice()
    .sort((a, b) => parseInt(a.sort.modelValue.value) - parseInt(b.sort.modelValue.value))
  await nextTick()
  tableDateMove.value = testShowData
  cssCallBack(dragState.start)
}

/**
 * 注意ダイアログの閉じるボタン押下時
 */
function confirmCancle() {
  cssCallBack(dragState.start)
}

/**
 * AC006_「表示順位削除」押下
 */
function sortDeleteClick() {
  // 表示順リスト表示順リスト内の順序列の内容をクリアする
  // リストのレコードを取得し、ループを使用してレコードの順序に基づいてソートする
  if (tableData.value && tableData.value.length > 0) {
    for (const item of tableData.value) {
      item.sort.modelValue.value = Or27761Const.EMPTY
    }
  }
}

const dragState = {
  // 開始索引
  start: -1,
  // 移動時に上書きされるインデックス
  end: -1,
  // 移動中ですか
  dragging: false,
  // 移動方向
  direction: Or27761Const.EMPTY,
  // 上の浮遊する行
  lastSort: -1,
}

/**
 * AC007_「空白ラベル」ドラッグする_1
 *
 * @param index - 表示順
 */
function sortBtnMousedown(index: number) {
  // 選択するとソートが成功します
  // 選択しない場合は記録位置をロールバックします
  dragState.dragging = true
  dragState.start = index

  const htmls = document.getElementsByClassName(Or27761Const.CLASS.SUSPENSION + index)
  if (htmls) {
    for (const item of htmls) {
      if (item) {
        const html = item.parentElement?.parentElement
        if (html) {
          html.style.background = Or27761Const.COLOR.F2
        }
      }
    }
  }
}

/**
 * AC007_「空白ラベル」ドラッグする_2
 *
 * @param index - 表示順
 */
function sortBtnMouseup(index: number) {
  if (dragState.start === dragState.end || dragState.start === index || -1 === index) {
    if (-1 !== index) {
      cssCallBack(dragState.start)
    }
    return
  }
  dragState.end = index

  // ポップアップウィンドウで選択確認ダイアログを表示し
  showOr21814Msg(t('message.i-cmn-10678', [dragState.start, dragState.end]))
}

/**
 * AC007_「空白ラベル」ドラッグする_3
 *
 * @param index - 表示順
 */
function sortBtnMousemove(index: number) {
  if (dragState.dragging && index !== dragState.lastSort && index !== dragState.start) {
    if (-1 !== dragState.lastSort) {
      const lastHtmls = document.getElementsByClassName(
        Or27761Const.CLASS.SUSPENSION + dragState.lastSort
      )
      if (lastHtmls) {
        for (const item of lastHtmls) {
          if (item) {
            const html = item.parentElement?.parentElement
            if (html) {
              html.style.background = Or27761Const.EMPTY
            }
          }
        }
      }
    }
    const htmls = document.getElementsByClassName(Or27761Const.CLASS.SUSPENSION + index)
    if (htmls) {
      for (const item of htmls) {
        if (item) {
          const html = item.parentElement?.parentElement
          if (html) {
            html.style.background = Or27761Const.COLOR.BK_COL
            dragState.lastSort = index
          }
        }
      }
    }
  }
}

/**
 * AC007_「空白ラベル」ドラッグする_4
 *
 */
function tdMousemove() {
  if (dragState.dragging) {
    if (-1 === dragState.end) {
      const lastSort = dragState.lastSort
      cssCallBack(dragState.start)
      if (-1 !== lastSort) {
        cssCallBack(lastSort)
      }
    }
  }
}

/**
 * AC002_「×ボタン」押下
 * AC009_「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * 元素パターン回復
 *
 * @param sort - 表示順
 */
function cssCallBack(sort: number) {
  const element = document.querySelector(
    Or27761Const.CLASS.SYMBOL + Or27761Const.CLASS.SUSPENSION + sort
  )
  if (element) {
    const html = element.parentElement?.parentElement
    if (html) {
      html.style.background = Or27761Const.CLASS.TRANSPARENT
    }
  }

  const lastElement = document.querySelector(
    Or27761Const.CLASS.SYMBOL + Or27761Const.CLASS.SUSPENSION + dragState.lastSort
  )
  if (lastElement) {
    const html = lastElement.parentElement?.parentElement
    if (html) {
      html.style.background = Or27761Const.CLASS.TRANSPARENT
    }
  }

  dragState.start = -1
  dragState.end = -1
  dragState.dragging = false
  dragState.direction = Or27761Const.EMPTY
  dragState.lastSort = -1
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * AC008_「確定ボタン」押下
 * 「確定」ボタン押下
 */
function confirm() {
  // 表示されているタブ画面の表示順欄の値で課題データ情報の内容をソートする
  // ※表示順欄に値のない行は順次に最後に移動する
  const respData: Or27761Type = {
    sortList: [],
  }

  if (tableData.value && tableData.value.length > 0) {
    const tempList = new Array<ShowData>()
    for (const item of tableData.value) {
      const data: ShowData = {
        sort: Number(item.sort.modelValue.value),
        number: item.number.onewayModelValue.value,
        content: item.content.onewayModelValue.value,
        sortBackup: item.sortBackup,
        id: item.id,
        memoKnj: item.memoKnj,
        seqNo: item.seqNo,
        modifiedCnt: item.modifiedCnt,
      }
      tempList.push(data)
    }
    tempList.sort((a, b) => {
      if (!a.sort && !b.sort) return 0
      if (!a.sort) return 1
      if (!b.sort) return -1
      return a.sort - b.sort
    })
    respData.sortList = tempList
  }
  // 返却情報.課題データ情報 = ソート後の課題データ情報の内容
  emit('update:modelValue', respData)
  // 本画面を閉じ、親画面に返却する。
  close()
}
/**
 * 「表示順」マウスダウン
 *
 * @param reSetIndex - reSetIndex
 */
async function sortReSetProc(reSetIndex: number) {
  const sortVal = {
    maxSort: 0,
  }
  for (const item of tableData.value) {
    if (
      item &&
      item.sort.modelValue.value !== Or27761Const.EMPTY &&
      Number(item.sort.modelValue.value) > sortVal.maxSort
    ) {
      sortVal.maxSort = Number(item.sort.modelValue.value)
    }
  }
  if (tableData.value[reSetIndex].sort.modelValue.value === Or27761Const.EMPTY) {
    tableData.value[reSetIndex].sort.modelValue.value = String(sortVal.maxSort + 1)
  }
  await nextTick()
}

/**
 * 数値入力イベント
 *
 *@param reSetIndex - reSetIndex
 */
async function onInputNumber(reSetIndex: number) {
  // 数値以外の文字を削除
  const value = tableData.value[reSetIndex].sort.modelValue.value
  tableData.value[reSetIndex].sort.modelValue.value = value.replace(
    Or27761Const.SEARCH,
    Or27761Const.EMPTY
  )
  await nextTick()
}

// メニュー切替
watch(
  () => local.mo00043.id,
  async () => {
    let testShowData = [...tableData.value]
    testShowData = testShowData.slice().sort((a, b) => {
      if (!a.sort.modelValue.value && !b.sort.modelValue.value) return 0
      if (!a.sort.modelValue.value) return 1
      if (!b.sort.modelValue.value) return -1
      return parseInt(a.sort.modelValue.value) - parseInt(b.sort.modelValue.value)
    })
    tableDateMove.value = testShowData
    tableData.value = testShowData
    await nextTick()
  }
)
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043"
        :oneway-model-value="localOneway.mo00043OneWay"
      >
      </base-mo00043>
      <!-- タブ switch -->
      <c-v-window v-model="local.mo00043.id">
        <c-v-window-item value="change">
          <c-v-card-text class="content-change">
            <!-- タブ：表示順変更 -->
            <div>
              <div class="mb-2">
                <base-mo01265
                  :oneway-model-value="localOneway.mo01265OnewayModelValue"
                  @click="sortDeleteClick"
                >
                </base-mo01265>
              </div>
              <c-v-data-table
                v-resizable-grid="{ columnWidths: columnMinWidth }"
                :headers="changeTableHeaders"
                class="table-wrapper tab-height-560"
                hide-default-footer
                :items="tableData"
                fixed-header
                hover
                :items-per-page="-1"
              >
                <template #headers>
                  <tr>
                    <!-- 表示順 -->
                    <th class="width-80">
                      {{ t('label.display-order') }}
                    </th>
                    <!-- 番号 -->
                    <th class="width-80">
                      {{ t('label.number') }}
                    </th>
                    <!-- 内容 -->
                    <th class="width-400">
                      {{ t('label.content') }}
                    </th>
                  </tr>
                </template>
                <template #item="{ item, index }">
                  <tr>
                    <td class="padding-zero">
                      <base-mo01278
                        v-model="item.sort.modelValue"
                        :oneway-model-value="item.sort.onewayModelValue"
                        @click.stop="sortReSetProc(index)"
                        @input="onInputNumber(index)"
                      >
                      </base-mo01278>
                    </td>
                    <td>
                      <base-mo01337 :oneway-model-value="item.number.onewayModelValue">
                      </base-mo01337>
                    </td>
                    <td>
                      <base-mo01337 :oneway-model-value="item.content.onewayModelValue">
                      </base-mo01337>
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </div>
          </c-v-card-text>
        </c-v-window-item>
        <c-v-window-item value="move">
          <!-- タブ：表示順移動 -->
          <c-v-card @mouseup="sortBtnMouseup(-1)">
            <c-v-card-text
              id="customCard"
              class="content-change"
            >
              <c-v-data-table
                :headers="moveTableHeaders"
                class="table-wrapper tab-height-560"
                hide-default-footer
                :items="tableDateMove"
                fixed-header
                hover
                :items-per-page="-1"
              >
                <template #headers>
                  <tr>
                    <!-- 行ドラッグアイコン -->
                    <th class="width-62"></th>
                    <!-- 表示順 -->
                    <th class="width-110">
                      {{ t('label.display-order') }}
                    </th>
                    <!-- 番号 -->
                    <th class="width-114">
                      {{ t('label.number') }}
                    </th>
                    <!-- 内容 -->
                    <th class="width-460">
                      {{ t('label.content') }}
                    </th>
                  </tr>
                </template>
                <template #item="{ item, index }">
                  <tr>
                    <td
                      class="bg-col-n"
                      @mousedown="sortBtnMousedown(index + 1)"
                      @mouseup="sortBtnMouseup(index + 1)"
                      @mousemove="sortBtnMousemove(index + 1)"
                    >
                      <base-mo00009
                        icon="mdi-plus"
                        :oneway-model-value="localOneway.mo00009Oneway"
                        :class="'suspension' + (index + 1)"
                      >
                      </base-mo00009>
                    </td>
                    <td @mousemove="tdMousemove">
                      <base-mo01278
                        :model-value="item.sort.modelValue"
                        :oneway-model-value="item.sort.onewayModelValue"
                        :disabled="true"
                      >
                      </base-mo01278>
                    </td>
                    <td
                      class="bg-col-n"
                      @mousemove="tdMousemove"
                    >
                      <base-mo01337 :oneway-model-value="item.number.onewayModelValue">
                      </base-mo01337>
                    </td>
                    <td
                      class="bg-col-n"
                      @mousemove="tdMousemove"
                    >
                      <base-mo01337 :oneway-model-value="item.content.onewayModelValue">
                      </base-mo01337>
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-card-text>
          </c-v-card>
        </c-v-window-item>
      </c-v-window>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mx-2"
          @click="confirm"
        >
          <!--ツールチップ表示："設定を確定します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialog"
    v-bind="or21814"
  />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
/** 表示順背景 */
:deep(.txt:disabled) {
  background: inherit;
}
/** 行ドラッグアイコン */
:deep(.material-symbols-rounded) {
  color: red;
}
/** タブコンテンツ */
.content-change {
  padding: 0;
  padding-top: 8px;
}
/** 表示順入力 */
:deep(.full-width-field) {
  width: 100% !important;
  text-align: center !important;
}
/** 幅:80 */
.width-80 {
  width: 80px !important;
  text-align: center !important;
}
/** 幅:400 */
.width-400 {
  width: 400px !important;
}
/** 幅:114 */
.width-114 {
  width: 114px !important;
  text-align: center !important;
}
/** 幅:460 */
.width-460 {
  width: 460px !important;
}
/** 幅:62 */
.width-62 {
  width: 62px !important;
}
/** 幅:110 */
.width-110 {
  width: 110px !important;
  text-align: center !important;
}
:deep(.v-col) {
  padding: 0 !important;
}
.padding-zero {
  padding: 0 !important;
}
:deep(.bg-inh .tex-al) {
  text-align: center !important;
}
.bg-inh {
  background: inherit !important;
}
.bg-col-n {
  background-color: unset !important;
}
.tab-height-560 {
  height: 560px !important;
}
</style>
