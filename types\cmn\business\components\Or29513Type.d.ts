import type {
  RirekiInfo,
  InfoCollectionInfoType,
} from '~/types/cmn/business/components/TeX0005Type'
/**
 * Or29513：有機体：アセスメント(インターライ)画面
 * 単方向バインドのデータ構造
 *
 * <AUTHOR>
 */
export interface Or29513OnewayType {
  /**
   * 期間管理フラグ「0:管理しない 1:管理する」
   */
  periodManageFlag: string

  /**
   * 履歴情報
   */
  rirekiInfo: RirekiInfo

  /**
   *copyParentId
   */
  copyParentId?: string
  /**
   *複数flg
   */
  copyFlg?: boolean

  /**
   * 削除フラグ
   */
  deleteFlg?: boolean
  /**
   *ボタン非表示フラグ
   */
  hiddenAction?: boolean
}

/**
 * 双方向バインドModelValue
 */
export interface Or29513Type {
  /** 情報収集画面（1）リスト */
  infoCollectionInfoList: InfoCollectionInfoType[]
}
