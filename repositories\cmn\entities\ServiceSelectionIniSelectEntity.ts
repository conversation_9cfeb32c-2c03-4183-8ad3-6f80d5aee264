/**
 * Or26287のエンティティ
 * GUI01151_サービス選択取り込み
 *
 * <AUTHOR>
 */
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * サービス選択（個別）初期処理 entry
 */
export interface ServiceSelectionIniSelectInEntity extends InWebEntity {
  /**
   * メインパラメータ
   */
  mainParm: string
  /**
   * 機能名
   */
  kinouName: string
  /**
   * 要介護度（変更前）
   */
  yokaiKbn: string
  /**
   * 要介護度（変更後）
   */
  yokaiKbn2: string
  /**
   * サービス有効期間
   */
  termId: string
  /**
   * 外部サービス区分
   */
  chsGaibuFlg: string
  /**
   * 利用票提供年月日（年月）
   */
  teiYmdYm: string
  /**
   * サービス情報パラメータ
   */
  svParm: svParmList[]
  /**
   * サービス種類
   */
  svtype: string
  /**
   * 保険者ID
   */
  hokenId: string
}

/**
 * サービス情報パラメータ
 */
export interface svParmList {
  /**
   * サービス種別
   */
  svtype: string
  /**
   * サービス提供事業所ID
   */
  svJigyoId: string
  /**
   * サービス区分
   */
  svkbn: string
  /**
   * 月日指定データ
   */
  adsTsukihi: string
  /**
   * 基本サービス明細
   */
  adsKihon: string
  /**
   * 加算サービス明細
   */
  adsKasan: string
}

/**
 * 要介護度リスト
 */
export interface ilYokaiKbn {
  /**
   * 要介護状態区分
   */
  yokaiKbn: string
  /**
   * 要介護度名
   */
  modifiedCnt: string
}

/**
 * 関連事業所リスト
 */
export interface idsJigyoFilter {
  /**
   * 事業所ID
   */
  svJigyoId: string
  /**
   *  サービス種類コード
   */
  svKindCd: string
  /**
   *  委託先事業者ID
   */
  itkJigyoId: string
  /**
   *  事業名
   */
  svJigyoName: string
  /**
   *  事業名Id
   */
  // shokuId: string
}

/**
 * 介護サービス種類リスト
 */
export interface isCdKaigo {
  /**
   * サービス種類
   */
  svShuruiCd: string
  /**
   * サービス種類名
   */
  svShuruiName: string
}

/**
 * 予防サービス種類リスト
 */
export interface isCdYobou {
  /**
   * サービス種類
   */
  svShuruiCd: string
  /**
   * サービス種類名
   */
  svShuruiName: string
}

/**
 * 総合事業サービス種類リスト
 */
export interface isCdSougou {
  /** サービス種類  */
  svShuruiCd: string
  /** サービス種類名  */
  svShuruiName: string
}

/**
 * 総合事業種類コードリスト
 */
export interface isCdSougouType {
  /** サービス有効期間ID */
  svShuruiTermid: string
  /** サービス種類CD */
  svShuruiCd: string
  /** サービス種類名 */
  svShuruiName: string
  /** サービス種類略称 */
  svShuruiNameKnj: string
  /** サービス種類CD（メイン） */
  svShuruiCdMain: string
  /** 更新回数 */
  modifiedCnt: string
}

/**
 * 絞り込み項目情報リスト
 */
export interface idsItem {
  /** 有効期間ID */
  termId: string
  /** サービス種別 */
  svType: string
  /** 項目区分 */
  itemKbn: string
  /** 項目ID */
  itemID: string
  /** 項目名 */
  itemName: string
  /** マスタ項目ID */
  mstId: string
  /** デフォルト値 */
  dataDefault: string
  /** 更新回数 */
  modifiedCnt: string
}

/** 絞り込み項目毎データリスト  */
export interface idsItemData {
  /** 有効期間ID */
  termId: string
  /** サービス種別 */
  svType: string
  /** 項目区分 */
  itemKbn: string
  /** 項目ID */
  itemID: string
  /** データID */
  dataId: string
  /** データ名 */
  dataName: string
  /** データ値 */
  dataValue: string
  /** 更新回数 */
  modifiedCnt: string
  /** 所要時間区分 */
  iiShoyouJikanFlg: string
}

/**
 * 基本サービスリスト
 */
export interface idwKihon {
  /** サービス事業者ID */
  svJigyoId: string
  /** 単か有効期間ID */
  tankaTermid: string
  /** 有効期間ID */
  termid: string
  /** 項目コード */
  itemcode: string
  /** 保険者ID */
  kHokenCd: string
  /** 開始日 */
  startdateYmd: string
  /** 終了日 */
  enddateYmd: string
  /** 実施区分 */
  useVal: string
  /** サービス種類コード */
  svtype: string
  /** サービス項目コード */
  svcode: string
  /** 種目ID */
  shumokuId: string
  /** 項目識別コード */
  scode: string
  /** 所要時間 */
  svtime: string
  /** 項目名 */
  itemnameKnj: string
  /** 正式名 */
  formalnameKnj: string
  /** 自立不明対象区分 */
  level1: string
  /** 要支援対象区分 */
  level2: string
  /** 要介護度Ⅰ対象区分 */
  level3: string
  /** 要介護度Ⅱ対象区分 */
  level4: string
  /** 要介護度Ⅲ対象区分 */
  level5: string
  /** 要介護度Ⅳ対象区分 */
  level6: string
  /** 要介護度Ⅴ対象区分 */
  level7: string
  /** 事業対象者実施区分 */
  levelSougou: string
  /** ケアマネ計画適用区分 */
  cmplan: string
  /** 上限数 */
  max: string
  /** 点金区分 */
  tenkintype: string
  /** 点数／金額／加算率 */
  tanka: string
  /** 単位金額ベース */
  base: string
  /** 規定数 */
  defaultVal: string
  /** 標準負担割合 */
  hutanwari: string
  /** 1点あたりの単価 */
  tentanka: string
  /** 予備3(要支援1対象区分) */
  reserve3: string
  /** 予備4(要支援2対象区分) */
  reserve4: string
  /** 支給限度額管理対象区分 */
  gendo: string
  /** 独自コード1 */
  ndcode1: string
  /** 独自コード2 */
  ndcode2: string
  /** 独自コード3 */
  ndcode3: string
  /** 独自コード4 */
  ndcode4: string
  /** 独自コード5 */
  ndcode5: string
  /** 独自コード6 */
  ndcode6: string
  /** 独自コード7 */
  ndcode7: string
  /** 独自コード8 */
  ndcode8: string
  /** 独自コード9 */
  ndcode9: string
  /** 独自コード10 */
  ndcode10: string
  /** 独自コード11 */
  ndcode11: string
  /** 独自コード18 */
  ndcode18: string
  /** 外部サービス利用種類コード */
  ndtype: string
  /** 事業所施設区分 */
  shisetuKbn: string
  /** 人員配置区分 */
  haichiKbn: string
  /** 定員超過人員欠如識別区分 */
  ketuinKbn: string
  /** 旧措置者区分 */
  sochiKbn: string
  /** 大規模事業所減算区分 */
  daigenKbn: string
  /** ユニットケア体制 */
  unitCare: string
  /** 意思疎通困難等ケア体制 */
  isisotuuCare: string
  /** 夜間勤務条件基準 */
  yakanKinmuKbn: string
  /** 日割り計算用コード識別区分 */
  hiwariKeisanKbn: string
  /** 居室区分 */
  kyosituKbn: string
  /** 療養環境基準 */
  ryoKankyouKbn: string
  /** サービス別負担割合 */
  svHutanwari: string
  /** 自己負担額1 */
  hutangaku1: string
  /** 予備6(摘要欄記載条件) */
  reserve6: string
  /** 予備7(期間時期) */
  reserve7: string
  /** 予備8(機能訓練指導体制) */
  reserve8: string
  /** 予備9(リハビリテーション機能強化体制) */
  reserve9: string
  /** 予備10(個別機能訓練体制) */
  reserve10: string
  /** 予備11(サービス利用条件) */
  reserve11: string
  /** 予備12(算定単位） */
  reserve12: string
  /** 独自コード17 */
  ndcode17: string
  /** 割引後の率（％） */
  waribikigoRitu: string
  /** 加算フラグ */
  kasanFlg: string
  /** サービス種類CD（メイン） */
  mainSvtype: string
  /** sougou_sv_kbn */
  sougouSvKbn: string
  /** 算定単位 */
  santeiTani: string
  /** 合成識別区分 */
  gouseiSikKbn: string
  /** 定員超過人員欠如識別区分 */
  cyukaKbn: string
  /** 時間延長サービス体制 */
  encyoTais: string
  /** 被保険者属性(要介護5） */
  kaigo5: string
  /** サービス内容(身体生活識別区分) */
  sinseiKbn: string
  /** サービス内容(サービス提供内容) */
  svKaisu: string
  /** 3ユニット事業所夜勤職員配置 */
  yobi365: string
  /** 介護職員等ベースアップ等支援加算 */
  yobi373: string
  /** レンタルIF */
  rentalF: string
  /** 小規模区分 */
  shoukiboKbn: string
  /** 送迎区分 */
  sougeiKbn: string
  /** MST設定 */
  mstSetting: string
  /** ロック区分 */
  lockFlg: string
  /** SEL */
  sel: string
  /** 保護 */
  protect: string
  /** 適用開始年月日 */
  tekiyoDateFr: string
  /** 加算回数区分 */
  kaisuKbn: string
}

/** 加算サービスリスト   */
export interface idwKasane {
  /** サービス事業者ID */
  svJigyoId: string
  /** 単か有効期間ID */
  tankaTermid: string
  /** 有効期間ID */
  termid: string
  /** 項目コード */
  itemcode: string
  /** 保険者ID */
  kHokenCd: string
  /** 開始日 */
  startdateYmd: string
  /** 終了日 */
  enddateYmd: string
  /** 実施区分 */
  useVal: string
  /** サービス種類コード */
  svtype: string
  /** サービス項目コード */
  svcode: string
  /** 種目ID */
  shumokuId: string
  /** 項目識別コード */
  scode: string
  /** 所要時間 */
  svtime: string
  /** 項目名 */
  itemnameKnj: string
  /** 正式名 */
  formalnameKnj: string
  /** 自立不明対象区分 */
  level1: string
  /** 要支援対象区分 */
  level2: string
  /** 要介護度Ⅰ対象区分 */
  level3: string
  /** 要介護度Ⅱ対象区分 */
  level4: string
  /** 要介護度Ⅲ対象区分 */
  level5: string
  /** 要介護度Ⅳ対象区分 */
  level6: string
  /** 要介護度Ⅴ対象区分 */
  level7: string
  /** 事業対象者実施区分 */
  levelSougou: string
  /** ケアマネ計画適用区分 */
  cmplan: string
  /** 上限数 */
  max: string
  /** 点金区分 */
  tenkintype: string
  /** 点数／金額／加算率 */
  tanka: string
  /** 単位金額ベース */
  base: string
  /** 規定数 */
  defaultVal: string
  /** 標準負担割合 */
  hutanwari: string
  /** 1点あたりの単価 */
  tentanka: string
  /** 予備3(要支援1対象区分) */
  reserve3: string
  /** 予備4(要支援2対象区分) */
  reserve4: string
  /** 支給限度額管理対象区分 */
  gendo: string
  /** 独自コード1 */
  ndcode1: string
  /** 独自コード2 */
  ndcode2: string
  /** 独自コード3 */
  ndcode3: string
  /** 独自コード4 */
  ndcode4: string
  /** 独自コード5 */
  ndcode5: string
  /** 独自コード6 */
  ndcode6: string
  /** 独自コード7 */
  ndcode7: string
  /** 独自コード8 */
  ndcode8: string
  /** 独自コード9 */
  ndcode9: string
  /** 独自コード10 */
  ndcode10: string
  /** 独自コード11 */
  ndcode11: string
  /** 独自コード18 */
  ndcode18: string
  /** 外部サービス利用種類コード */
  ndtype: string
  /** 事業所施設区分 */
  shisetuKbn: string
  /** 人員配置区分 */
  haichiKbn: string
  /** 定員超過人員欠如識別区分 */
  ketuinKbn: string
  /** 旧措置者区分 */
  sochiKbn: string
  /** 大規模事業所減算区分 */
  daigenKbn: string
  /** ユニットケア体制 */
  unitCare: string
  /** 意思疎通困難等ケア体制 */
  isisotuuCare: string
  /** 夜間勤務条件基準 */
  yakanKinmuKbn: string
  /** 日割り計算用コード識別区分 */
  hiwariKeisanKbn: string
  /** 居室区分 */
  kyosituKbn: string
  /** 療養環境基準 */
  ryoKankyouKbn: string
  /** サービス別負担割合 */
  svHutanwari: string
  /** 自己負担額1 */
  hutangaku1: string
  /** 予備6(摘要欄記載条件) */
  reserve6: string
  /** 予備7(期間時期) */
  reserve7: string
  /** 予備8(機能訓練指導体制) */
  reserve8: string
  /** 予備9(リハビリテーション機能強化体制) */
  reserve9: string
  /** 予備10(個別機能訓練体制) */
  reserve10: string
  /** 予備11(サービス利用条件) */
  reserve11: string
  /** 予備12(算定単位） */
  reserve12: string
  /** 独自コード17 */
  ndcode17: string
  /** 割引後の率（％） */
  waribikigoRitu: string
  /** 加算フラグ */
  kasanFlg: string
  /** サービス種類CD（メイン） */
  mainSvtype: string
  /** sougou_sv_kbn */
  sougouSvKbn: string
  /** 算定単位 */
  santeiTani: string
  /** 合成識別区分 */
  gouseiSikKbn: string
  /** 定員超過人員欠如識別区分 */
  cyukaKbn: string
  /** 時間延長サービス体制 */
  encyoTais: string
  /** 被保険者属性(要介護5） */
  kaigo5: string
  /** サービス内容(身体生活識別区分) */
  sinseiKbn: string
  /** サービス内容(サービス提供内容) */
  svKaisu: string

  /** サービス内容(緊急時治療管理算定区分) */
  kinkstKbn: string
  /** サービス内容(介護内容区分) */
  kainaiKbn: string
  /** サービス内容(有資格者区分) */
  yusikaKbn: string
  /** 所要開始時間 */
  syoyoTimeFr: string
  /** 所要終了時間 */
  syoyoTimeTo: string
  /** 注加減算コード(1) */
  cyuCd1: string
  /** 注加減算コード(2) */
  cyuCd2: string
  /** 注加減算コード(3) */
  cyuCd3: string
  /** 注加減算コード(4) */
  cyuCd4: string
  /** 施設類型 */
  cyuCd19: string
  /** 加算可能サービスコード(1) */
  addCd1: string
  /** 摘要欄記載条件  */
  tekiyoJkn: string
  /** 支給限度額対象区分 */
  gentaiKbn: string
  /** 期間時期 */
  kikanJiki: string
  /** 回数日数 */
  kaisuNisu: string
  /** 特別地域加算 */
  tokutiAd: string
  /** 緊急時訪問看護加算 */
  kinhokAd: string
  /** 特別管理体制 */
  tokkanAd: string
  /** 入浴介助体制 */
  nyuyokTs: string
  /** 医師配置基準 */
  dovhaiTs: string
  /** 送迎体制 */
  sougeiTs: string
  /** 単位数表構成区分(部) */
  bu: string
  /** 単位数表構成区分(部枝番) */
  buEda: string
  /** 単位数表構成区分(節) */
  setu: string
  /** 単位数表構成区分(項番1) */
  ko1: string
  /** 単位数表構成区分(項番2) */
  ko2: string
  /** サービス利用条件 */
  svRiyoJkn: string
  /** 特定事業所加算(訪問介護) */
  tokJigyoAdd: string
  /** ターミナルケア体制 */
  trmcarTs: string
  /** 栄養マネジメント体制 */
  eiymanTs: string
  /** 口腔機能向上体制 */
  kokikoTs: string
  /** 緊急受入体制 */
  kinukeTs: string
  /** 夜間看護体制 */
  yknkanTs: string
  /** 運動器機能向上体制 */
  undokoTs: string
  /** 事業所評価加算 */
  jighyoTs: string
  /** 医療連携体制 */
  iryrenTs: string
  /** 療養体制維持特別加算 */
  ryoyoIji: string
  /** 3級ヘルパー体制 */
  hlp3kyu: string
  /** 小規模事業所加算(地域状況) */
  tyusankanTiki: string
  /** 小規模事業所加算(規模状況) */
  tyusankanKibo: string
  /** サービス提供体制強化加算 */
  svTeikyo: string
  /** 認知症短期集中リハビリ加算 */
  tankiRiha: string
  /** 若年性認知症利用者等受入加算 */
  jyakuUkeire: string
  /** 看護体制加算 */
  kango: string
  /** 夜勤職員配置加算 */
  yakinHaiti: string
  /** 療養食加算 */
  ryoyoSyoku: string
  /** 認知症専門ケア加算 */
  nintiSenmon: string
  /** 24時間通報対応加算 */
  tuhoTaio: string
  /** 看護職員配置加算 */
  kangoHaiti: string
  /** 夜間ケア加算 */
  yakanCare: string
  /** 中山間地域等サービス提供加算 */
  tyusankanTeikyo: string
  /** サービス提供責任者体制の減算 */
  yobi249: string
  /** 緊急短期入所体制確保加算 */
  yobi250: string
  /** 在宅復帰・在宅療養支援機能加算  */
  yobi252: string
  /** 生活機能向上グループ活動加算 */
  yobi253: string
  /** 介護職員処遇改善加算 */
  yobi254: string
  /** 同一建物に居住する利用者の減算 */
  yobi256: string
  /** リハビリマネジメント加算  */
  yobi264: string
  /** 看護体制強化加算  */
  yobi265: string
  /** 短期集中リハビリ実施加算  */
  yobi266: string
  /** 社会参加支援加算  */
  yobi267: string
  /** 認知症加算  */
  yobi268: string
  /** 中重度者ケア体制加算  */
  yobi269: string
  /** 個別送迎体制強化加算  */
  yobi270: string
  /** 入浴介助体制強化加算  */
  yobi271: string
  /** 医療連携強化加算  */
  yobi276: string
  /** 選択的サービス複数実施加算  */
  yobi284: string
  /** 総合マネジメント体制強化加算  */
  yobi285: string
  /** 訪問体制強化加算  */
  yobi289: string
  /** 夜間支援体制加算  */
  yobi290: string
  /** 訪問看護体制減算  */
  yobi291: string
  /** 訪問看護体制強化加算  */
  yobi292: string
  /** 生活相談員配置等加算  */
  yobi306: string
  /** 生活機能向上連携加算  */
  yobi307: string
  /** リハビリテーション提供体制加算  */
  yobi311: string
  /** 重度認知症疾患療養体制加算  */
  yobi319: string
  /** 入居継続支援加算  */
  yobi320: string
  /** 介護職員等特定処遇改善加算 */
  yobi353: string
  /** 感染症及び災害による臨時的な対応 */
  yobi354: string
  /** 科学的介護推進体制加算 */
  yobi355: string
  /** 3ユニット事業所夜勤職員配置 */
  yobi365: string
  /** 介護職員等ベースアップ等支援加算 */
  yobi373: string
  /** レンタルIF */
  rentalF: string
  /** 小規模区分 */
  shoukiboKbn: string
  /** 送迎区分 */
  sougeiKbn: string
  /** MST設定 */
  mstSetting: string
  /** ロック区分 */
  lockFlg: string
  /** SEL */
  sel: string
  /** 保護 */
  protect: string
  /** 適用開始年月日 */
  tekiyoDateFr: string
  /** 加算回数区分 */
  kaisuKbn: string
}

/**
 * サービス選択（個別）初期処理 out
 */
export interface ServiceSelectionIniSelectOutEntity extends OutWebEntity {
  /** data */
  data: {
    /**
     * エラーコード
     */
    errCode: string
    /**
     * 結果
     */
    ret: string
    /**
     * 関連事業所の設定
     */
    iiKanrenSetKbn: string
    /**
     * 機能区分
     */
    iiKinouKbn: string
    /**
     * 利用者情報
     */
    ilUserid: string
    /**
     * 要介護度リスト
     */
    ilYokaiKbn: ilYokaiKbn[]
    /**
     * 基準日（スラッシュ付き）
     */
    isYmdS: string
    /**
     * 年月（スラッシュ付き）
     */
    isYmS: string
    /**
     * 基準日
     */
    isYmd: string
    /**
     * サービス事業所有効開始日
     */
    isPlanStartYmd: string
    /**
     * サービス事業所有効終了日
     */
    isPlanEndYmd: string
    /**
     * 所要時間絞り込みフラグ
     */
    iiTimeFilterFlg: string
    /**
     * 終了時間自動入力
     */
    iiEndTimeAuto: string
    /**
     * 候補絞り込みフラグ
     */
    iiKouhoFilterFlg: string
    /**
     * 要介護度絞り込みフラグ
     */
    iiYokaiFilterFlg: string
    /**
     * 外部サービス区分
     */
    iiGaibuKbn: string
    /**
     * サービス計画作成有効期間設定
     */
    iiSvjTermKbn: string
    /**
     * 頻度（年月日）リスト
     */
    idsTsukihi: string
    /**
     * サービス区分
     */
    iiSvkbn: string
    /**
     * 入力区分
     */
    iiInputKbn: string
    /**
     * 頻度区分
     */
    iiHindoKbn: string
    /**
     * 事業所絞り込み可・不可
     */
    ibJigyoFilter: string
    /**
     * 事業所絞り込みフラグ
     */
    iiJigyoFilter: string
    /**
     * サービス種類
     */
    isSvtype: string
    /**
     * サービス種類（総合事業）
     */
    isSvtypeSougou: string
    /**
     * 総合事業サービス区分
     */
    iiSougouSvKbn: string
    /**
     * サービス事業所
     */
    ilSvJigyoId: string
    /**
     * サービス単一選択フラグ
     */
    ibSingle: string
    /**
     * 関連事業所リスト
     */
    idsJigyoFilter: idsJigyoFilter[]
    /**
     * 介護サービス種類リスト
     */
    isCdKaigo: isCdKaigo[]
    /**
     * 予防サービス種類リスト
     */
    isCdYobou: isCdYobou[]
    /**
     * 総合事業サービス種類リスト
     */
    isCdSougou: isCdSougou[]
    /**
     * 総合事業種類コードリスト
     */
    isCdSougouType: isCdSougouType[]
    /**
     *更新回数
     */
    modifiedCnt: string
    /**
     * 絞り込み項目情報リスト
     */
    idsItem: idsItem[]
    /**
     * 絞り込み項目毎データリスト
     */
    idsItemData: idsItemData[]
    /**
     * 所要時間区分
     */
    iiShoyouJikanFlg: string
    /**
     * 基本サービスリスト
     */
    idwKihonList: idwKihon[]
    /**
     * 加算サービスリスト
     */
    idwKasaneList: idwKasane[]
  }
}
