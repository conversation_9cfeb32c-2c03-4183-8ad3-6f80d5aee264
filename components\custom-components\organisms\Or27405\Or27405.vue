<script setup lang="ts">
/**
 * Or27405:有機体:状況マスタモーダル
 * GUI00699_状況マスタ
 *
 * @description
 * 状況マスタ
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'

import { OrD1004Const } from '../OrD1004/OrD1004.constants'
import { OrD1004Logic } from '../OrD1004/OrD1004.logic'
import type { Or27405StateType, TableItemsType } from './Or27405.type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or27405OnewayType, Or27405Type } from '~/types/cmn/business/components/Or27405Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  Mo01354OnewayType,
  Mo01354Type,
  Mo01354Items,
  Mo01354Headers,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { Mo01332OnewayType, Mo01332Type } from '~/types/business/components/Mo01332Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  CpnMucGdlKoumokuEntity,
  CpnMucGdlKoumokuOutEntity,
} from '~/repositories/cmn/entities/CpnMucGdlKoumokuEntity'
import type { CpnMucGdlKoumokuUpdateEntity } from '~/repositories/cmn/entities/CpnMucGdlKoumokuUpdateEntity'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or27405Const } from '~/components/custom-components/organisms/Or27405/Or27405.constants'
import { useValidation } from '~/utils/useValidation'
import type { OrX0018OnewayType } from '~/types/cmn/business/components/OrX0018Type'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import type { CustomClass } from '~/types/CustomClassType'
import type { ResizableGridBinding } from '~/plugins/resizableGrid.client'

const { t } = useI18n()
const validation = useValidation()

/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or27405Type
  onewayModelValue: Or27405OnewayType
  uniqueCpId: string
  parentCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const isEdit = ref<boolean>(false)

// ローカル双方向bind
const local = reactive({
  or27405: {
    ...props.modelValue,
  } as Or27405Type,
  bkList: {
    values: {
      selectedRowId: '-1',
      selectedRowIds: [],
      items: [] as Mo01354Items[],
    },
  } as Mo01354Type,
})

const localOneway = reactive({
  // 保存権限
  saveAuthority: await hasRegistAuth(),
  // 編集権限
  editAuthority: await hasRegistAuth(),
  // 「状況マスタ」ダイアログ
  mo00024Oneway: {
    width: 'auto',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: true,
    mo01344Oneway: {
      toolbarTitle: t('label.situation-master'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  },
  or27405Oneway: {
    ...props.onewayModelValue,
  } as Or27405OnewayType,
  mo00611OneWayClose: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // チェックボックス
  mo01332OnewayType: {
    showItemLabel: false,
    items: [{ label: '', value: '1' }],
    isVerticalLabel: true,
    customClass: { itemClass: 'mt-1 ml-6' } as CustomClass,
  } as Mo01332OnewayType,
  // 行追加
  mo00611OneWay: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
  } as Mo00611OnewayType,
  // 行複写
  mo00611OneWayCopy: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    disabled: false,
  } as Mo00611OnewayType,
  // 行削除
  mo01265OneWay: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    disabled: false,
  } as Mo01265OnewayType,
  // 項目名: 表用テキストフィールド
  orX0018TitleInputOneWay: {
    width: '280',
    rules: [validation.required, validation.max(40)],
  } as OrX0018OnewayType,
  // 表用数値専用テキストフィールド
  mo01278Oneway: {
    isEditCamma: false,
    maxLength: '4',
    min: -999,
    max: 9999,
    rules: [validation.integer, validation.minValue(-999), validation.maxValue(9999)],
  },
})

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27405Const.DEFAULT.IS_OPEN,
})

// ターブルヘッダー情報
const INIT_HEADERS = [
  {
    title: t('label.id'),
    key: 'id',
    sortable: false,
    minWidth: '20',
    maxWidth: '20',
  },
  {
    title: t('label.item-nm'),
    key: 'koumokuKnj',
    sortable: false,
    minWidth: '100',
    required: true,
  },
  {
    title: t('label.item-sort'),
    key: 'sort',
    sortable: false,
    minWidth: '100',
  },
  {
    title: t('label.item-stop'),
    key: 'stopFlg',
    sortable: false,
    minWidth: '20',
  },
] as Mo01354Headers[]

/**
 * 分子：表
 * 単方向バインドモデルのローカル変数
 */
const mo01354Oneway = ref<Mo01354OnewayType>({
  /** 初期値：ヘッダー情報 */
  headers: INIT_HEADERS,
  height: '231px',
  rowHeight: '32px',
  /** 横幅調整機能を使用する際に指定 */
  columnMinWidth: {
    columnWidths: [54, 285, 100, 70],
  } as ResizableGridBinding,
})

const refs = ref<Record<string, HTMLInputElement>>({})
const setRef = (el: HTMLInputElement | null, tableIndex: string) => {
  if (el) {
    refs.value[tableIndex] = el
  }
}
/**************************************************
 * Pinia
 **************************************************/
const or21813_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const orD1004_1 = ref({ uniqueCpId: '' })

const { setState } = useScreenOneWayBind<Or27405StateType>({
  cpId: Or27405Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or27405Const.DEFAULT.IS_OPEN
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [OrD1004Const.CP_ID(1)]: orD1004_1.value,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
watch(
  () => local.or27405,
  () => {
    if (local.or27405.listValue.values.selectedRowId === '-1') {
      localOneway.mo00611OneWayCopy.disabled = true
      localOneway.mo01265OneWay.disabled = true
    } else {
      localOneway.mo00611OneWayCopy.disabled = false
      localOneway.mo01265OneWay.disabled = false
    }
    if (
      JSON.stringify(local.or27405.listValue.values.items) !==
      JSON.stringify(local.bkList.values.items)
    ) {
      isEdit.value = true
    } else {
      isEdit.value = false
    }
  },
  { deep: true }
)

/**
 * Xボタンのイベント監視
 */
watch(
  () => mo00024.value.emitType,
  async (newValue) => {
    if (newValue === 'closeBtnClick') {
      await onClickCloseBtn()
    }
    mo00024.value.emitType = 'blank'
  }
)

/**
 * 保存ボタンのイベント監視
 */
watch(
  () => OrD1004Logic.event.get(orD1004_1.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.saveFlg) {
      // 保存ボタンのイベントフラグをOFFにする
      OrD1004Logic.event.set({
        uniqueCpId: orD1004_1.value.uniqueCpId,
        events: {
          saveFlg: false,
        },
      })

      // 保存処理実行
      const result = saveProccess()

      if (await result) {
        // 保存処理が成功した場合
        // 画面の初期表示処理を実行（再表示）
        await init()
      }
    }
  }
)

onMounted(async () => {
  // 初期化
  await init()
})

/**
 *  初期化
 */
async function init() {
  // バックエンドAPIから初期情報取得
  const inputData: CpnMucGdlKoumokuEntity = {
    /** 法人ID */
    houjinId: localOneway.or27405Oneway.houjinId,
    /** 施設ID */
    shisetuId: localOneway.or27405Oneway.shisetuId,
    /** 事業所ID */
    svJigyoId: localOneway.or27405Oneway.svJigyoId,
  }

  const resData: CpnMucGdlKoumokuOutEntity = await ScreenRepository.select(
    'situationMasterSelect',
    inputData
  )

  if (
    resData?.data?.cpnMucGdlKoumokuList !== undefined &&
    resData.data.cpnMucGdlKoumokuList.length > 0
  ) {
    local.or27405.listValue.values.items = resData.data.cpnMucGdlKoumokuList.map((item) => ({
      id: item.id,
      koumokuKnj: { value: item.koumokuKnj },
      sort: { value: item.sort },
      stopFlg: item.stopFlg === '1' ? { values: ['1'] } : { values: [] },
      dbFlg: 'db',
      deleteFlg: false,
      lockFlg: item.lockFlg,
      mastId: item.mastId,
      houjinId: item.houjinId,
      shisetuId: item.shisetuId,
      svJigyoId: item.svJigyoId,
      modifiedCnt: item.modifiedCnt,
      updateKbn: '',
    }))
  }
  if (local.or27405.listValue.values.items.filter((item) => item.deleteFlg === false).length > 0) {
    local.or27405.listValue.values.selectedRowId = '1'
  }
  local.bkList = cloneDeep(local.or27405.listValue)
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = async () => {
  // 入力データ変更チェックを行う。
  // ・画面入力データの変更がある場合,共通処理の編集権限チェックを行う。
  if (isEdit.value) {
    // ■編集権限無の場合、以下のメッセージを表示
    if (!localOneway.editAuthority) {
      // ・メッセージコード：i.cmn.11140
      // ・メッセージ内容：情報を保存する権限がありません。入力内容は破棄されますが、処理を続けますか？
      const dialogResult = await openInfoDialog('message.i-cmn-11140')
      switch (dialogResult) {
        case Or27405Const.DEFAULT.DIALOG_RESULT_YES:
          // はい：処理続き
          break
        case Or27405Const.DEFAULT.DIALOG_RESULT_NO:
          // いいえ：処理終了
          return
      }
    }
    // ■編集権限有の場合、以下のメッセージを表示
    if (localOneway.editAuthority) {
      // ・メッセージコード：i.cmn.10430
      // ・メッセージ内容：他の項目を選択すると、変更内容は失われます。「改行」
      // 変更を保存しますか？
      const dialogResult = await openIsEditDialog()
      switch (dialogResult) {
        case Or27405Const.DEFAULT.DIALOG_RESULT_YES:
          // はい：AC008保存処理を行う。
          await saveProccess()
          break
        case Or27405Const.DEFAULT.DIALOG_RESULT_NO:
          // いいえ：画面項目の各選択内容を前回保存後の状態に戻す、処理続き
          await init()
          break
        case Or27405Const.DEFAULT.DIALOG_RESULT_CANCEL:
          // キャンセル：メッセージダイアログを閉じる。
          return
      }
    }
  }

  setState({ isOpen: false })
}

/**
 * 行追加ボタン押下
 */
const addRow = () => {
  const maxIndex = local.or27405.listValue.values.items.length - 1
  let tmpId = '1'
  let tmpSort = '1'
  if (maxIndex !== -1) {
    tmpId = local.or27405.listValue.values.items[maxIndex].id
    const items = local.or27405.listValue.values.items
    // 最大行データ
    const selectedItem = items?.find((item) => item.id === tmpId) as TableItemsType
    tmpSort = selectedItem.sort.value
  }
  if (tmpId !== Or27405Const.DEFAULT.MAX_VAL) {
    tmpId = String(parseInt(tmpId) + 1)
  }
  if (tmpSort !== Or27405Const.DEFAULT.MAX_VAL) {
    tmpSort = String(parseInt(tmpSort) + 1)
  }
  local.or27405.listValue.values.items.push({
    id: tmpId,
    koumokuKnj: { value: '' },
    sort: { value: tmpSort },
    stopFlg: { values: [] },
    dbFlg: '',
    deleteFlg: false,
    lockFlg: '0',
    mastId: tmpId,
    houjinId: localOneway.or27405Oneway.houjinId,
    shisetuId: localOneway.or27405Oneway.shisetuId,
    svJigyoId: localOneway.or27405Oneway.svJigyoId,
    modifiedCnt: '0',
    updateKbn: Or27405Const.DEFAULT.UPD_KBN_NEW,
  })
  localOneway.mo00611OneWayCopy.disabled = false
  localOneway.mo01265OneWay.disabled = false
}

/**
 * 行複製
 */
const copyRow = () => {
  const newValue = {
    values: {
      selectedRowId: '-1',
      selectedRowIds: [],
      items: [] as Mo01354Items[],
    },
  } as Mo01354Type
  if (local.or27405.listValue.values.selectedRowId !== '-1') {
    newValue.values.items = local.or27405.listValue.values.items.filter(
      (item) => item.id === local.or27405.listValue.values.selectedRowId
    )
    const selItems = newValue?.values?.items
    // 選択データ
    const selData = selItems?.find(
      (item) => item.id === local.or27405.listValue.values.selectedRowId
    ) as TableItemsType
    const knj = selData.koumokuKnj.value
    const maxIndex = local.or27405.listValue.values.items.length - 1
    let tmpId = local.or27405.listValue.values.items[maxIndex].id
    newValue.values.items = local.or27405.listValue.values.items.filter((item) => item.id === tmpId)
    const items = newValue?.values?.items
    // 最大行データ
    const selectedItem = items?.find((item) => item.id === tmpId) as TableItemsType
    if (tmpId !== Or27405Const.DEFAULT.MAX_VAL) {
      tmpId = String(parseInt(tmpId) + 1)
    }
    local.or27405.listValue.values.items.push({
      id: tmpId,
      koumokuKnj: { value: knj },
      sort: { value: String(parseInt(selectedItem.sort.value) + 1) },
      stopFlg: newValue.values.items[0].stopFlg === '1' ? { values: ['1'] } : { values: [] },
      dbFlg: '',
      deleteFlg: false,
      lockFlg: '0',
      mastId: tmpId,
      houjinId: localOneway.or27405Oneway.houjinId,
      shisetuId: localOneway.or27405Oneway.shisetuId,
      svJigyoId: localOneway.or27405Oneway.svJigyoId,
      modifiedCnt: '0',
      updateKbn: Or27405Const.DEFAULT.UPD_KBN_NEW,
    })
    // 行削除活性
    localOneway.mo00611OneWayCopy.disabled = false
    localOneway.mo01265OneWay.disabled = false
  }
}

/**
 * 行削除ボタン押下
 */
const deleteRow = async () => {
  const items = local.or27405.listValue.values.items
  // 選択データ
  const selectedItem = items?.find(
    (item) => item.id === local.or27405.listValue.values.selectedRowId
  ) as TableItemsType
  // 対象行のロックフラグが1:初期値の場合
  if (selectedItem.lockFlg === '1') {
    const dialogResult = await openErrorDialog('message.e-cmn-41519')
    switch (dialogResult) {
      case Or27405Const.DEFAULT.DIALOG_RESULT_YES:
        // OK：処理終了
        return
    }
  }
  // 対象行のロックフラグが0:追加項目の場合
  if (selectedItem.lockFlg === '0') {
    // ■以下のメッセージを表示
    // id: i.cnm.11397
    // ・メッセージ内容
    // マスタの行を削除しようとしています。「改行」
    // 削除すると復旧できません。「改行」
    // 削除してもよろしいですか？
    const dialogResult = await openInfoDialog('message.i-cmn-11397')
    switch (dialogResult) {
      case Or27405Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：以降の処理を行う。
        break
      case Or27405Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：以降の処理を行わない。
        return
    }
    // 対象行が新規行の場合
    if (selectedItem.dbFlg === '') {
      // 該当行を削除する。
      local.or27405.listValue.values.items = local.or27405.listValue.values.items.filter(
        (item) => item.id !== selectedItem.id
      )
    } else {
      // 対象行が既存行の場合、該当行を非表示する。
      local.or27405.listValue.values.items = local.or27405.listValue.values.items.map((item) => {
        if (item.id === selectedItem.id) {
          item.updateKbn = Or27405Const.DEFAULT.UPD_KBN_DEL
          return { ...item, deleteFlg: true }
        }
        return item
      })
    }
    if (
      local.or27405.listValue.values.items.length === 0 ||
      local.or27405.listValue.values.items.filter((item) => item.deleteFlg === true).length ===
        local.or27405.listValue.values.items.length
    ) {
      localOneway.mo00611OneWayCopy.disabled = true
      localOneway.mo01265OneWay.disabled = true
    }
  }
  // 削除後の初期化
  local.or27405.listValue.values.selectedRowId = '-1'
}

/**
 * チェックボックスクリック
 *
 * @param id - ID
 */
const onClickCheckboxForFirst = async (id: string) => {
  local.or27405.listValue.values.selectedRowId = id
  // 画面．IDが1の中止チェックボックスを選ぶ場合
  if (id === '1') {
    // ■以下のメッセージを表示
    // id: i.cmn.10611
    // ・メッセージ内容
    // 「全体のまとめ」は中止にすることができません。
    // OK：該当行の中止はチェックオフして処理終了
    await openConfirmInfoDialog('message.i-cmn-10611')
    return
  }
}

/**
 * チェックボックスクリック
 *
 * @param id - ID
 */
const onClickCheckbox = (id: string) => {
  local.or27405.listValue.values.selectedRowId = id

  const data = local.or27405.listValue.values.items?.find((item) => item.id === id) as unknown as {
    stopFlg: Mo01332Type
    updateKbn: string
  }
  if (data) {
    if (data.stopFlg.values?.includes('1')) {
      data.stopFlg.values = ['']
    } else {
      data.stopFlg.values = ['1']
    }
    if (data.updateKbn !== Or27405Const.DEFAULT.UPD_KBN_NEW) {
      data.updateKbn = Or27405Const.DEFAULT.UPD_KBN_UPD
    }
  }
}

/**
 * データ保存イベント
 */
const saveProccess = async (): Promise<boolean> => {
  // 画面の入力データが変更されない場合
  if (!isEdit.value) {
    // 処理終了する。
    return false
  }
  // 画面項目定義書を参照して、項目名、表示順の単項目チェックを行う
  const ret = await inputCheck()
  if (ret === false) {
    // 処理終了する。
    return false
  }

  const newValue = {
    values: {
      selectedRowId: '-1',
      selectedRowIds: [],
      items: [] as Mo01354Items[],
    },
  } as Mo01354Type
  newValue.values.items = local.bkList.values.items
  const selItems = newValue?.values?.items
  // 編集したDBデータの更新区分設定
  local.or27405.listValue.values.items = local.or27405.listValue.values.items.map((item) => {
    if (item.updateKbn === '') {
      const newValue = item as TableItemsType
      const oldValue = selItems?.find((tmp) => tmp.id === item.id) as TableItemsType
      if (
        oldValue.koumokuKnj.value !== newValue.koumokuKnj.value ||
        oldValue.sort.value !== newValue.sort.value ||
        oldValue.stopFlg.values[0] !== newValue.stopFlg.values[0]
      ) {
        item.updateKbn = Or27405Const.DEFAULT.UPD_KBN_UPD
        return { ...item }
      }
    }
    return item
  })
  const items = local.or27405.listValue.values.items
  const selectedItems = items as TableItemsType[]
  const tmpArr: CpnMucGdlKoumokuUpdateEntity['cpnMucGdlKoumokuList'] = []
  for (const item of selectedItems) {
    tmpArr.push({
      id: item.id,
      koumokuKnj: item.koumokuKnj.value,
      sort: item.sort.value,
      stopFlg: item.stopFlg.values.includes('1') ? '1' : '0',
      lockFlg: item.lockFlg,
      mastId: item.mastId,
      houjinId: item.houjinId,
      shisetuId: item.shisetuId,
      svJigyoId: item.svJigyoId,
      modifiedCnt: item.modifiedCnt,
      updateKbn: item.updateKbn,
    })
  }
  const param: CpnMucGdlKoumokuUpdateEntity = {
    cpnMucGdlKoumokuList: tmpArr,
  }
  // 画面データの保存
  await ScreenRepository.update('situationMasterUpdate', param)
  // AC001の処理を行う。
  await init()
  return true
}

/**
 * 入力チェック
 */
const inputCheck = async (): Promise<boolean> => {
  let result = true

  // 双方向バインドのvalueのitemsの入力チェックを行う
  for (const item of local.or27405.listValue.values.items) {
    if (item.deleteFlg) {
      // 削除済のデータの場合、入力チェック対象外のためスキップ
      continue
    } else {
      // 画面．項目名とほか行の項目名が重複入力の場合
      if (
        local.or27405.listValue.values.items.filter(
          (tmp) => JSON.stringify(tmp.koumokuKnj) === JSON.stringify(item.koumokuKnj)
        ).length > 1
      ) {
        // ■以下のメッセージを表示
        // id:e.cmn.30039
        // ・メッセージ内容
        // 項目名が重複しています。削除または変更してください。
        await openErrorDialog('message.e-cmn-30039')
        // OK：以降の処理を行わない。
        result = false
        break
      }
    }
  }
  return result
}

const showDialogOr21813_1 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21814_1 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 編集破棄ダイアログ表示
 *
 * @param msgId - メッセージID
 *
 * @returns ダイアログの選択結果（yes, no）
 */
async function openConfirmInfoDialog(msgId: string): Promise<string> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t(msgId),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })

  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = Or27405Const.DEFAULT.DIALOG_RESULT_NO

        if (event?.firstBtnClickFlg) {
          result = Or27405Const.DEFAULT.DIALOG_RESULT_YES
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 編集破棄ダイアログ表示
 *
 * @param msgId - メッセージID
 *
 * @returns ダイアログの選択結果（yes, no）
 */
async function openInfoDialog(msgId: string): Promise<string> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t(msgId),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })

  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = Or27405Const.DEFAULT.DIALOG_RESULT_NO

        if (event?.firstBtnClickFlg) {
          result = Or27405Const.DEFAULT.DIALOG_RESULT_YES
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 編集破棄ダイアログ表示
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openIsEditDialog(): Promise<string> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = Or27405Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or27405Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or27405Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or27405Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログ表示（e.cmn.40980）
 *
 * @param msgId - メッセージID
 *
 * @returns ダイアログの選択結果（yes, no）
 */
async function openErrorDialog(msgId: string): Promise<string> {
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t(msgId),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })

  // 確認ダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813_1.value.uniqueCpId)

        let result = Or27405Const.DEFAULT.DIALOG_RESULT_NO

        if (event?.firstBtnClickFlg) {
          result = Or27405Const.DEFAULT.DIALOG_RESULT_YES
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row style="margin-left: 0px; margin-top: 0px; margin-bottom: 8px">
        <!--行追加-->
        <base-mo00611
          class="mr-1"
          :oneway-model-value="localOneway.mo00611OneWay"
          @click="addRow"
        />
        <!--行複写-->
        <base-mo00611
          class="ml-1 mr-1"
          :oneway-model-value="localOneway.mo00611OneWayCopy"
          @click="copyRow"
        />
        <!--行削除-->
        <base-mo01265
          class="ml-1"
          :oneway-model-value="localOneway.mo01265OneWay"
          @click="deleteRow"
        />
      </c-v-row>
      <c-v-row style="margin: 0px">
        <!-- 分子：表 -->
        <base-mo-01354
          v-model="local.or27405.listValue"
          :oneway-model-value="mo01354Oneway"
          class="list-wrapper"
          hide-default-footer
        >
          <!-- ID（ラベル） -->
          <template #[`item.id`]="{ item }">
            <!-- ※※※本来は表用ラベルを使用する※※※ -->
            <c-v-col
              class="pa-0"
              style="padding-right: 16px !important; text-align: right"
              >{{ item.id }}</c-v-col
            >
          </template>
          <!-- 列１（テキストフィールド） -->
          <template #[`item.koumokuKnj`]="{ item }">
            <!-- 分子：表用テキストフィールド -->
            <g-custom-or-x-0018
              v-if="item.lockFlg === '1'"
              v-model="item.koumokuKnj"
              :re-ref="(el: HTMLInputElement | null) => setRef(el, `input-${item.tableIndex}`)"
              :oneway-model-value="localOneway.orX0018TitleInputOneWay"
              style="background-color: rgb(255, 193, 198)"
              disabled="disabled"
            ></g-custom-or-x-0018>
            <g-custom-or-x-0018
              v-else
              v-model="item.koumokuKnj"
              :re-ref="(el: HTMLInputElement | null) => setRef(el, `input-${item.tableIndex}`)"
              :oneway-model-value="localOneway.orX0018TitleInputOneWay"
            ></g-custom-or-x-0018>
          </template>
          <!-- 列２（テキストフィールド） -->
          <template #[`item.sort`]="{ item }">
            <!-- 分子：表用数値専用テキストフィールド -->
            <base-mo-01278
              v-model="item.sort"
              :oneway-model-value="localOneway.mo01278Oneway"
              style="width: 100%"
            />
          </template>
          <!-- 列３（チェックフィールド） -->
          <template #[`item.stopFlg`]="{ item }">
            <!-- 分子：表用チェックフィールド -->
            <base-mo01332
              v-if="item.id === '1'"
              v-model="item.stopFlg"
              :oneway-model-value="localOneway.mo01332OnewayType"
              @click.prevent="onClickCheckboxForFirst(item.id)"
            ></base-mo01332>
            <base-mo01332
              v-else
              v-model="item.stopFlg"
              :oneway-model-value="localOneway.mo01332OnewayType"
              @click="onClickCheckbox(item.id)"
            ></base-mo01332>
          </template>
          <!-- ページングを非表示 -->
          <template #bottom />
        </base-mo-01354>
      </c-v-row>

      <c-v-row>
        <c-v-col style="margin: 16px 8px 4px 8px; color: red">
          {{ t('label.all-stop-message') }}
        </c-v-col>
      </c-v-row>
      <c-v-row>
        <c-v-col style="margin: 8px">{{
          t('label.save-by-bussiness-unit')
        }}</c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OneWayClose"
          class="mx-2"
          @click="onClickCloseBtn"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.screen-close')"
          />
        </base-mo00611>
        <g-custom-or-d-1004
          v-if="!localOneway.saveAuthority"
          v-bind="orD1004_1"
          class="mr-2"
          disabled="disabled"
        />
        <g-custom-or-d-1004
          v-else
          v-bind="orD1004_1"
          class="mr-2"
        />
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813
    v-if="showDialogOr21813_1"
    v-bind="or21813_1"
  />
  <g-base-or21814
    v-if="showDialogOr21814_1"
    v-bind="or21814_1"
  />
</template>

<style lang="scss" scoped>
@use '@/styles/cmn/dialog-data-table.scss';
:deep(.list-wrapper) {
  .v-table__wrapper
    tr:not(.row-selected):not(.select-row):not(.selected-row)
    td:not(:has(input, textarea, select)) {
    background-color: #fafafa;
  }
}

:deep(.v-col) {
  padding: 0px !important;
}
:deep(.v-field__input) {
  min-height: 22px !important;
}
:deep(.full-cell) {
  height: 22px !important;
}
:deep(.v-table.v-table--fixed-header > .v-table__wrapper > table > thead > tr > th) {
  box-shadow: none !important;
}
</style>
