<script setup lang="ts">
/**
 * Or52275:(会議録取込)開催一覧1
 * GUI01260_会議録取込
 *
 * @description
 * (会議録取込)開催一覧1
 *
 * <AUTHOR> PHAM HO HAI DANG
 */
import { reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type {
  Mo01354Headers,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { Or52275Const } from '~/components/custom-components/organisms/Or52275/Or52275.constants'
import { useCommonProps } from '~/composables/useCommonProps'
import { useScreenOneWayBind, useScreenTwoWayBind } from '~/composables/useComponentVue'
import type { Or52275StateType } from '~/types/cmn/business/components/Or52275Type'
/**
 * i18nの初期化
 */
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
/**
 * 共通Propsを定義
 */
const props = defineProps(useCommonProps())

/**
 * ステートとの双方向バインド設定
 */
const { setState } = useScreenOneWayBind<Or52275StateType>({
  cpId: Or52275Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * ステート「items」が更新された時、ローカル変数に反映
     *
     * @param value - ステート値
     */
    items: (value) => {
      if (!value) {
        return
      }

      local.mo01354.values.items.splice(0)
      value.forEach((item) => {
        const tableItem = {
          id: item.kaigi1Id,
          createDate: ref(item.createYmd), // 作成日
          createUser: ref(item.shokuKnj), // 作成者
          kaigiYmd: ref(item.kaigiYmd), // 開催日
          whereKnj: ref(item.whereKnj), // 開催場所
          timeHm: ref(item.timeHm), // 開催時間
          kaisuu: ref(item.kaisuu), // 回数
          caseNo: ref(item.caseNo), // ケース番号
          revision: ref(item.kaiteiFlg), // 改訂フラグ
          selectable: true, // 選択可
        }

        local.mo01354.values.items.push(tableItem)
      })
    },
    /**
     * ステート「selectedItemId」が更新された時、ローカル変数に反映
     *
     * @param value - 選択されたID
     */
    selectedItemId: (value) => {
      if (value && value !== local.mo01354.values.selectedRowId) {
        local.mo01354.values.selectedRowId = value
      }
    },
    /**
     * ステート「kikanKanriFlg」が更新された時、ヘッダー構成を変更
     *
     * @param value - フラグ値
     */
    kikanKanriFlg: (value) => {
      localOneway.mo01354.headers.splice(0)
      if (value === Or52275Const.PERIOD_MANAGE_NO) {
        // 期間管理なしの場合のヘッダー
        localOneway.mo01354.headers = [...mo01354HeadersWOKikanKanriFlg]
      } else {
        // 期間管理ありの場合のヘッダー
        localOneway.mo01354.headers = [...mo01354HeadersWKikanKanriFlg]
      }
    },
  },
})

// ⑤ウォッチャー（必要に応じて実装）

/**************************************************
 * Emit
 **************************************************/
// ※現在未実装、必要に応じて追加

/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**
 * テーブルのローカル状態（バインド用）
 */
const local = reactive({
  mo01354: {
    values: {
      selectedRowId: '', // 選択行ID
      selectedRowIds: [], // 複数選択行ID
      items: [], // テーブル項目
    },
  } as unknown as Mo01354Type,
})

/** 期間管理フラグ＝1時のテーブルヘッダー */
const mo01354HeadersWKikanKanriFlg = [
  {
    title: t('label.create-date'),
    key: 'createDate',
    width: '130px',
    sortable: false,
  },
  {
    title: t('label.author'),
    key: 'createUser',
    width: '160px',
    sortable: false,
  },
  {
    title: t('label.held-date'),
    key: 'kaigiYmd',
    width: '125px',
    sortable: false,
  },
  {
    title: t('label.held-location'),
    key: 'whereKnj',
    width: '250px',
    sortable: false,
  },
  {
    title: t('label.event-time'),
    key: 'timeHm',
    width: '120px',
    sortable: false,
  },
  {
    title: t('label.number-of-times'),
    key: 'kaisuu',
    width: '68px',
    sortable: false,
  },
] as unknown as Mo01354Headers[]
/** 期間管理フラグ＝1以外時のテーブルヘッダー */
const mo01354HeadersWOKikanKanriFlg = [
  {
    title: t('label.held-date'),
    key: 'createDate',
    width: '125px',
    sortable: false,
  },
  {
    title: t('label.author'),
    key: 'createUser',
    width: '210px',
    sortable: false,
  },
  {
    title: t('label.held-location'),
    key: 'whereKnj',
    width: '180px',
    sortable: false,
  },
  {
    title: t('label.event-time'),
    key: 'timeHm',
    width: '120px',
    sortable: false,
  },
  {
    title: t('label.number-of-times'),
    key: 'kaisuu',
    width: '68px',
    sortable: false,
  },
  {
    title: t('label.caseNo'),
    key: 'caseNo',
    width: '125px',
    sortable: false,
  },
  {
    title: t('label.revision-1'),
    key: 'revision',
    width: '68px',
    sortable: false,
  },
] as unknown as Mo01354Headers[]

/**
 * ステートとの双方向バインド（refValueを使用）
 */
const { refValue } = useScreenTwoWayBind<Or52275StateType>({
  cpId: Or52275Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**
 * テーブルの表示設定（ローカル専用・片方向バインド）
 */
const localOneway = reactive({
  mo01354: {
    height: '102px', // テーブル高さ
    showPaginationTopFlg: false, // 上部ページネーション非表示
    showPaginationBottomFlg: false, // 下部ページネーション非表示
    headers:
      refValue.value?.kikanKanriFlg === Or52275Const.PERIOD_MANAGE_NO
        ? [...mo01354HeadersWOKikanKanriFlg] // 期間管理なしのヘッダー
        : [...mo01354HeadersWKikanKanriFlg], // 期間管理ありのヘッダー
    useDefaultHeader: false, // デフォルトヘッダー非使用
  } as unknown as Mo01354OnewayType,
})

watch(
  () => local.mo01354.values.selectedRowId,
  () => {
    setState({ selectedItemId: local.mo01354.values.selectedRowId })
  }
)

/**************************************************
 * ライフサイクルフック
 **************************************************/
</script>

<template>
  <c-v-sheet class="table-wrapper">
    <base-mo01354
      v-model="local.mo01354"
      :oneway-model-value="localOneway.mo01354"
      hide-default-footer
      hide-no-data
    >
      <template #[`item.kaisuu`]="{ item }">
        <span class="d-flex w-100 align-end justify-end">{{ item.kaisuu }}</span>
      </template>
      <!-- <template #[`item.revision`]="{ item }">
        <span class="d-flex w-100 align-end justify-end">{{ item.revision }}</span>
      </template> -->
    </base-mo01354>
  </c-v-sheet>
</template>

<style lang="scss" scoped>
@use '@/styles/cmn/dialog-data-table-list.scss';

.table-wrapper :deep(.v-table__wrapper .selected-row) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}
.padding-0 {
  padding: 0px !important;
}
</style>
