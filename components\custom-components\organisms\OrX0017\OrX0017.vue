<script setup lang="ts">
/**
 * OrX0017:有機体:週間計画パターンタイトルタブ：タイトル
 * GUI01045_週間計画パターンタイトル
 *
 * @description
 * 週間計画パターンタイトルタブ：タイトル
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { VForm } from 'vuetify/components'
import { OrX0017Const } from './OrX0017.constants'
import type { Style, TableData } from './OrX0017.type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type {
  OrX0017OnewayType,
  OrX0017Type,
  Title,
} from '~/types/cmn/business/components/OrX0017Type'

import type { OrX0018OnewayType } from '~/types/cmn/business/components/OrX0018Type'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { TermSelectOutEntity } from '~/repositories/cmn/entities/TermSelectEntity'
import type {
  WeekPlanPatternTitleSelectInEntity,
  WeekPlanPatternTitleSelectOutEntity,
} from '~/repositories/cmn/entities/WeekPlanPatternTitleSelectEntity'
import { useValidation } from '~/utils/useValidation'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { useScreenUtils } from '~/utils/useScreenUtils'
import { useScreenTwoWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  Or21814EventType,
  Or21814OnewayType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
const { setChildCpBinds } = useScreenUtils()

const { t } = useI18n()

const validation = useValidation()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: OrX0017OnewayType
  modelValue: OrX0017Type
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()

const defaultOnewayModelValue: OrX0017OnewayType = {
  cksFlg: 0,
}
const defaultModelValue: OrX0017Type = {
  editFlg: false,
  titleList: [],
  initFlg: false,
}

const localOneWay = reactive({
  orX0017: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 新規
  mo00611OneWay: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
  } as Mo00611OnewayType,
  // 行削除
  mo01265OneWay: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    disabled: true,
  } as Mo01265OnewayType,
  // 要介護度: 表用セレクトフィールド
  mo01282OneWay: {
    items: [],
    itemTitle: 'yokaiKnj',
    itemValue: 'yokaiKbn',
  } as Mo01282OnewayType,
  // 表示順: 表用テキストフィールド
  orX0018SeqInputOneWay: {
    maxLength: '3',
    rules: [validation.integer, validation.required, validation.minValue(1)],
  } as OrX0018OnewayType,
  // タイトル: 表用テキストフィールド
  orX0018TitleInputOneWay: {
    rules: [],
  } as OrX0018OnewayType,
  // 表用数値専用テキストフィールド
  mo01278Oneway: {
    maxLength: '3',
    min: 1,
    max: 999,
    rules: [
      validation.integer,
      validation.required,
      validation.minValue(1),
      validation.maxValue(999),
    ],
  },
})

const local = reactive({
  orX0017: {
    ...defaultModelValue,
    ...props.modelValue,
  },
})
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<TableData[]>({
  cpId: OrX0017Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = []
/**************************************************
 * 変数定義
 **************************************************/
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
})
// 確認ダイアログのPromise
let or21814ResolvePromise: (value: Or21814EventType) => void
// 選択した行のindex
const selectedItemIndex = ref<number>(-1)
// テーブルヘッダ
const headers = [
  { title: t('label.id'), key: 'ks51Id', width: '100px', sortable: false },
  { title: t('label.title'), key: 'nameKnj', sortable: false, required: true },
  { title: t('label.level-of-care-required'), key: 'youkaiCd', width: '200px', sortable: false },
  { title: t('label.valid-period'), key: 'termName', width: '200px', sortable: false, cksFlg: 2 },
  { title: t('label.display-order'), key: 'seq', width: '140px', sortable: false },
  { title: t('label.style'), key: 'style', width: '140px', sortable: false },
]
const headersFilter = computed(() => {
  if (localOneWay.orX0017.cksFlg === 1) {
    // 共通情報.計画書様式フラグ(cks_flg)が施設「1」の場合、メインセクションの有効期間表示しない。
    return headers.filter(
      (item) => item.cksFlg === undefined || item.cksFlg === localOneWay.orX0017.cksFlg
    )
  }
  return headers
})
// "*"
const required: string = OrX0017Const.DEFAULT.REQUIRED
// 説明
const description: string = t('label.all-common-description-care-plan-style-save')

// 元のテーブルデータ
const orgTableData = ref<string>('')
const tableForm = ref<VForm>()

const tableDataFilter = computed(() => {
  const titleList = refValue.value
  return titleList!.filter((i: TableData) => i.updateKbn !== UPDATE_KBN.DELETE)
})
const refs = ref<Record<string, HTMLInputElement>>({})
const setRef = (el: HTMLInputElement | null, tableIndex: string) => {
  if (el) {
    refs.value[tableIndex] = el
  }
}
// ポスト最小幅
const columnMinWidth = ref<number[]>([100, 458, 190, 220, 200])
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await init()
})

/**
 * 週間計画パターンタイトル情報取得
 */
async function init() {
  // 6columnテーブルのminWidth
  if (localOneWay.orX0017.cksFlg === 2) {
    columnMinWidth.value = [70, 400, 160, 170, 218, 150]
  }
  // 週間計画パターンタイトル情報取得(IN)
  const param: WeekPlanPatternTitleSelectInEntity = {
    // サービス区分: 引継情報.計画書様式 - 1
    serviceKbn: localOneWay.orX0017.cksFlg - 1,
  }
  // 週間計画パターンタイトル情報取得
  const ret: WeekPlanPatternTitleSelectOutEntity = await ScreenRepository.select(
    'weekPlanPatternTitleSelect',
    param
  )
  // 戻り値はテーブルデータとして処理されます
  let tmpArr = []
  tmpArr = []
  for (const item of ret.data.titleList) {
    tmpArr.push({
      ks51Id: item.ks51Id,
      nameKnj: { value: item.nameKnj },
      youkaiCd: { modelValue: item.youkaiCd },
      termId: item.termId,
      termName: item.termName,
      seq: { value: item.seq },
      styleCd: item.styleCd,
      tableIndex: tmpArr.length,
      updateKbn: UPDATE_KBN.NONE,
      modifiedCnt: item.modifiedCnt,
    })
  }
  // 要介護度のリスト
  localOneWay.mo01282OneWay.items = ret.data.yokaiList
  // タイトル Validation
  localOneWay.orX0018TitleInputOneWay.rules = [
    validation.equalLessThanLength(2048),
    validation.required,
  ]
  // 元のテーブルデータの設定
  orgTableData.value = JSON.stringify(refValue.value)
  setChildCpBinds(props.parentUniqueCpId, {
    OrX0017: {
      twoWayValue: tmpArr,
    },
  })
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
  // 行削除活性
  localOneWay.mo01265OneWay.disabled = false
}

/**
 * 様式取得
 *
 * @param styleCd - 様式CD
 */
function getStyleName(styleCd: string) {
  const styleList: Style[] = JSON.parse(OrX0017Const.DEFAULT.STYLE_LIST) as Style[]
  const style: Style[] = styleList.filter((item: Style) => item.styleCd === styleCd)
  if (style !== undefined && style.length > 0) {
    return style[0].styleName
  }
  return ''
}
/**
 * 「新規」押下
 */
async function createRow() {
  // 有効期間リストを取得する。
  const ret: TermSelectOutEntity = await ScreenRepository.select('termInfoSelect', {})
  let lastSeq = 0
  if (refValue.value!.length > 0) {
    for (const data of refValue.value!) {
      if (data.updateKbn !== UPDATE_KBN.DELETE) {
        lastSeq = Math.max(lastSeq, Number(data.seq.value))
      }
    }
  }
  // 表示順最大値＜999の場合
  if (lastSeq < 999) {
    // 表示順最大値＋1を設定
    lastSeq += 1
  } else {
    // 上記以外の場合、999を設定
    lastSeq = 999
  }
  // 週間計画パターンのタイトル一覧の最終に新しい行を追加する。
  const data = {
    ks51Id: '',
    // タイトル：空白
    nameKnj: { value: '' },
    // 要介護度：空白
    youkaiCd: { modelValue: '' },
    // 有効期間：有効期間リストに最後の有効期間を設定
    termId: ret.data.termid,
    // 表示順
    termName: ret.data.termName,
    seq: { value: String(lastSeq) },
    // 様式：引継情報.計画書様式
    styleCd: String(localOneWay.orX0017.cksFlg),
    // テーブルINDEX(行固有ID)
    tableIndex: refValue.value!.length,
    // 更新区分
    updateKbn: UPDATE_KBN.CREATE,
  }
  refValue.value!.push(data)
  await nextTick()
  const lastInput = refs.value[`input-${data.tableIndex}`]
  if (lastInput) {
    lastInput.focus()
  }
}

/**
 * 行削除ボタン押下
 */
async function deleteRow() {
  if (selectedItemIndex.value !== -1) {
    const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10878'),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンタイプ
      secondBtnType: 'normal3',
      // 第2ボタンラベル
      secondBtnLabel: t('btn.no'),
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    })
    if (rs.firstBtnClickFlg) {
      if (selectedItemIndex.value !== null) {
        refValue.value!.forEach((item: TableData) => {
          if (item.tableIndex === selectedItemIndex.value) {
            item.updateKbn = UPDATE_KBN.DELETE
            selectedItemIndex.value = -1
            // 行削除非活性
            localOneWay.mo01265OneWay.disabled = true
          }
        })
      }
    }
  }
}

/**
 * 検証
 */
async function tableValidation() {
  return (await tableForm.value!.validate()).valid
}
/**
 * コンテンツの更新
 */
function onUpdate() {
  refValue.value!.forEach((item: TableData) => {
    if (item.tableIndex === selectedItemIndex.value && item.updateKbn !== UPDATE_KBN.CREATE) {
      item.updateKbn = UPDATE_KBN.UPDATE
      selectedItemIndex.value = -1
      // 行削除非活性
      localOneWay.mo01265OneWay.disabled = true
    }
  })
}

/**
 * 確認ダイアログ開く
 *
 * @param uniqueCpId - uniqueCpId
 *
 * @param state - 確認ダイアログOneWayBind領域
 */
function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    or21814ResolvePromise = resolve
  })
}

watch(
  () => refValue.value,
  () => {
    local.orX0017.editFlg = orgTableData.value !== JSON.stringify(refValue.value)
    // タイトルのリスト
    const titleList: Title[] = []
    for (const title of refValue.value!) {
      titleList.push({
        ...title,
        nameKnj: title.nameKnj.value,
        youkaiCd: title.youkaiCd.modelValue,
        seq: title.seq.value,
      })
    }
    local.orX0017.titleList = titleList
    emit('update:modelValue', local.orX0017)
  },
  { deep: true }
)

/**
 * 表データの設定
 */
watch(
  () => local.orX0017.saveResultTitleList,
  () => {
    if (Array.isArray(local.orX0017.saveResultTitleList)) {
      const titleList = []
      for (const item of local.orX0017.saveResultTitleList) {
        titleList.push({
          // 週間計画ID
          ks51Id: item.ks51Id,
          // 名称
          nameKnj: { value: item.nameKnj },
          // 要介護度区分
          youkaiCd: { modelValue: item.youkaiCd },
          // 有効期間ID
          termId: item.termId,
          // 有効期間
          termName: item.termName,
          // 表示順
          seq: { value: item.seq },
          // 様式CD
          styleCd: item.styleCd,
          // テーブルINDEX(行固有ID)
          tableIndex: titleList.length,
          // 更新区分
          updateKbn: UPDATE_KBN.NONE,
        })
      }
      refValue.value = titleList
      // 元のテーブルデータの設定
      orgTableData.value = JSON.stringify(refValue.value)
    }
  }
)
/**
 * 初期表示フラグを傍受する
 */
watch(
  () => local.orX0017.initFlg,
  async (newVal) => {
    if (newVal) {
      await init()
      local.orX0017.initFlg = false
      emit('update:modelValue', local.orX0017)
    }
  }
)
/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    await nextTick()
    if (or21814ResolvePromise !== undefined && newValue !== undefined) {
      or21814ResolvePromise(newValue)
    }
    return
  }
)

defineExpose({
  tableValidation,
})
</script>

<template>
  <div class="title-container">
    <c-v-row>
      <c-v-col>
        <!-- 新規ボタン -->
        <base-mo00611
          v-bind="localOneWay.mo00611OneWay"
          @click="createRow"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.add-row')"
          />
        </base-mo00611>
        <!-- 削除ボタン -->
        <base-mo01265
          v-bind="localOneWay.mo01265OneWay"
          class="mx-2"
          @click="deleteRow"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.delete-row')"
          />
        </base-mo01265>
      </c-v-col>
    </c-v-row>
    <!-- 週間計画パターン一覧 -->
    <c-v-form ref="tableForm">
      <c-v-data-table
        v-resizable-grid="{ columnWidths: columnMinWidth }"
        fixed-header
        :headers="headersFilter"
        :items="tableDataFilter"
        class="table-wrapper mt-2"
        height="480px"
        hover
        hide-default-footer
        :items-per-page="-1"
      >
        <!-- ヘッダ Part -->
        <template #headers>
          <tr>
            <!-- ID -->
            <th class="width-100">{{ t('label.id') }}</th>
            <!-- *タイトル -->
            <th>
              <span style="color: red">{{ required }}</span
              >{{ t('label.title') }}
            </th>
            <!-- 要介護度 -->
            <th class="width-190">{{ t('label.level-of-care-required') }}</th>
            <!-- 有効期間 -->
            <th
              v-if="localOneWay.orX0017.cksFlg == 2"
              class="width-190"
            >
              {{ t('label.valid-period') }}
            </th>
            <!-- *表示順 -->
            <th class="width-200">
              <span style="color: red">{{ required }}</span
              >{{ t('label.display-order') }}
            </th>
            <!-- style -->
            <th class="width-150">{{ t('label.style') }}</th>
          </tr>
        </template>
        <!-- 一覧 -->
        <template #item="{ item, index }">
          <tr
            :class="{ 'select-row': selectedItemIndex === item.tableIndex }"
            @click="selectRow(item.tableIndex)"
          >
            <!-- ID: 右寄せ -->
            <td>{{ item.ks51Id }}</td>
            <!-- タイトル: テキストフィールド -->
            <td>
              <g-custom-or-x-0018
                v-model="tableDataFilter[index].nameKnj"
                :re-ref="(el: HTMLInputElement | null) => setRef(el, `input-${item.tableIndex}`)"
                :oneway-model-value="localOneWay.orX0018TitleInputOneWay"
                @change="onUpdate"
              ></g-custom-or-x-0018>
            </td>
            <!-- 要介護度: セレクトフィールド -->
            <td>
              <base-mo01282
                v-model="tableDataFilter[index].youkaiCd"
                :oneway-model-value="localOneWay.mo01282OneWay"
                @change="onUpdate"
              ></base-mo01282>
            </td>
            <!-- 有效期间: 共通情報.計画書様式フラグ(cks_flg)が居宅「2」の場合、メインセクションの有効期間表示する。 -->
            <td v-if="localOneWay.orX0017.cksFlg == 2">
              {{ item.termName }}
            </td>
            <!-- 表示順: 表用数値専用テキストフィールド -->
            <td class="text-align-right">
              <base-mo01278
                v-model="tableDataFilter[index].seq"
                :oneway-model-value="localOneWay.mo01278Oneway"
                @change="onUpdate"
              ></base-mo01278>
            </td>
            <!-- 様式 -->
            <td>{{ getStyleName(item.styleCd) }}</td>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-form>
  </div>
  <!-- 説明: ※全共通（計画書様式ごとに保存） -->
  <div class="body-text-s">
    {{ description }}
  </div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
:deep(.table-wrapper td):has(input),
:deep(.table-wrapper td):has(select) {
  padding: 0px !important;
}
:deep(.table-wrapper table) {
  border-collapse: collapse;
}
// タイトルのCSS
.title-container {
  margin: 8px 0px;
}
// 右寄せのCSS
.text-align-right {
  text-align: right;
}
// 選択した行のCSS
.select-row {
  background: #dbeefe;
}
// 幅: 140
.width-140 {
  width: 140px;
}
// 幅: 150
.width-150 {
  width: 150px;
}
// 幅: 200
.width-200 {
  width: 200px;
}
// 幅: 190
.width-190 {
  width: 190px;
}
// 幅: 100
.width-100 {
  width: 100px;
}
:deep(.v-table--fixed-header > .v-table__wrapper > table > thead) {
  position: sticky;
  top: 0px !important;
  z-index: 2;
}
</style>
