/**
 * OrX0154: 印刷設定モーダル
 * GUI04507_印刷設定
 *
 * @description
 * mock
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { PlanAllPrintSettingsSubjectSelectOutEntity } from '~/repositories/cmn/entities/PlanAllPrintSettingsSubjectSelectEntity'

/**
 *
 *
 * @description
 *
 * dataName："planDoc2History"
 */
export function handler(inEntity: PlanAllPrintSettingsSubjectSelectOutEntity) {
  const responseJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responseJson, { status: 200 })
}

export default { handler }
