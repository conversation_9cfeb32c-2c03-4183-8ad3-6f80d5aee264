/**
 * Or54352:有機体:フリーアセスメントフェースシート表示設定マスタ
 * GUI00898_フリーアセスメントフェースシート表示設定マスタ
 *
 * @description
 * フリーアセスメントフェースシート表示設定マスタ
 *
 * <AUTHOR>
 */
/**
 * 単方向バインドのデータ構造
 */
export interface Or54352OnewayType {
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   *タブID
   */
  tabId: string
  /**
   * 項目ID
   */
  koumokuId?: string
  /**
   *ヘッダID
   */
  item1Id?: string
}

/**
 * 双方向バインドModelValue
 */
export interface Or54352Type {
  /**
   *タブID
   */
  tabId?: string
  /**
   * 表示設定情報リスト
   */
  itemList: FreeFaceList[]
  /**
   * 表示設定マスタ（分類）情報リスト
   */
  bunruiList: BunruiList[]
}

// 表示設定情報リスト
interface FreeFaceList {
  // ID
  id?: string // 任意
  // ID
  item1Id: string // 必須
  // 事業者ID
  svJigyoId: string // 必須
  // 項目区分
  itemKbn: string // 必須
  // 種別ID
  syubetsuId: string // 必須
  // 項目ID
  koumokuId: string // 必須
  // 表示フラグ
  useFlg: string // 必須
  // 表示順
  sort: string // 必須
  // 項目名
  koumokuKnj: string // 必須
  // 作図名称
  pictureName?: string // 任意
  // 印刷行数
  lineCnt?: string // 任意
  // 作図出力フラグ
  pictureFlg?: string // 任意
  //ヘッダID
  controlItem1Id: string // 任意
  // 更新回数
  control1ModifiedCnt: string // 必須
  // 印刷設定の更新回数
  controlModifiedCnt: string // 必須
  // 印刷設定変更フラグ
  printModifiedFlg: string // 必須
}

// 表示設定マスタ（分類）情報リスト
interface BunruiList {
  // ID
  id?: string // 任意
  // ID
  item2Id: string // 必須
  // ヘッダID
  item1Id: string // 必須
  // 分類ID
  bunId: string // 必須
  // 表示フラグ
  useFlg: string // 必須
  // 表示順
  sort: string // 必須
  // 分類名
  daibunruiKnj: string // 必須
  // 更新回数
  modifiedCnt: string // 必須
}
