<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { OrX0130Const } from '../OrX0130/OrX0130.constants'
import { OrX0130Logic } from '../OrX0130/OrX0130.logic'
import { OrX0133Const } from '../OrX0133/OrX0133.constants'
import { OrX0133Logic } from '../OrX0133/OrX0133.logic'
import { OrX0117Const } from '../OrX0117/OrX0117.constants'
import { OrX0117Logic } from '../OrX0117/OrX0117.logic'
import { Or28974Const } from './Or28974.constants'
import type { CodeData, DataRow, Or28974StateType, userList, cpnTucCschList } from './Or28974.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import {
  computed,
  onBeforeMount,
  reactive,
  ref,
  useScreenOneWayBind,
  useSetupChildProps,
  watch,
} from '#imports'
import type { Or28974Type, Or28974OnewayType } from '~/types/cmn/business/components/Or28974Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { Mo01334OnewayType, Mo01334Type } from '~/types/business/components/Mo01334Type'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  LedgerInitData,
  NinteiHyoInfo,
  PrintSettingsScreenInitialInfoSelectGUI01283InEntity,
  PrintSettingsScreenInitialInfoSelectGUI01283OutEntity,
} from '~/repositories/cmn/entities/PrintSettingsScreenInitialInfoSelectGUI01283Entity'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Or26261Type, Shokuin } from '~/types/cmn/business/components/Or26261Type'
import type { PrintSettingsInfoComUpdateInEntity } from '~/repositories/cmn/entities/printSettingsInfoComUpdate'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type {
  LedgerInitializeDataComSelectInEntity,
  LedgerInitializeDataComSelectOutEntity,
} from '~/repositories/cmn/entities/ledgerInitializeDataComSelect'
import type {
  OrX0133OnewayType,
  OrX0133TableData,
} from '~/types/cmn/business/components/OrX0133Type'
import { dateUtils } from '~/utils/dateUtils'
import type {
  FilterForUserIdComSelectInEntity,
  FilterForUserIdComSelectOutEntity,
} from '~/repositories/cmn/entities/filterForUserIdComSelect'
import type {
  CertificationSurveySlipInfoSelectInEntity,
  CertificationSurveySlipInfoSelectOutEntity,
} from '~/repositories/cmn/entities/CertificationSurveySlipInfoSelectEntity'
import type { Mo01379OnewayType } from '~/types/business/components/Mo01379Type'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import { reportOutputType, useReportUtils } from '~/utils/useReportUtils'
import type { BasicSurveyReportEntity } from '~/repositories/cmn/entities/BasicSurveyReportEntity'

/**
 * Or28974:（認定調査）印刷設定モーダル
 * GUI01283_印刷設定
 *
 * @description
 *［印刷設定］画面では、印刷設定画面を表示します。
 *
 * <AUTHOR> 劉顕康
 */

const { t } = useI18n()
const { reportOutput } = useReportUtils()
const { convertDateToSeireki } = dateUtils()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or28974Type
  onewayModelValue: Or28974OnewayType
  uniqueCpId: string
}

/**
 * 引継情報を取得する
 */
const props = defineProps<Props>()

// 子コンポーネント用変数
// Or21813
const or21813 = ref({ uniqueCpId: '' })
// OrX0130
const orX0130 = ref({ uniqueCpId: '' })
// OrX0133
const orX0133 = ref({ uniqueCpId: '' })
// OrX0117
const orX0117 = ref({ uniqueCpId: '' })
// Or0145
const orx0145 = ref({ uniqueCpId: '' })

/**
 * default値
 */
const defaultOnewayModelValue: Or28974OnewayType = {
  /** システムコード */
  systemCode: '',
  /** システム略称 */
  systemNameShort: '',
  /** 計画期間管理フラグ */
  planPeriodManagementFlg: '',
  /** 法人ID */
  corporationId: '',
  /** 施設ID */
  facilityId: '',
  /** 事業者ID */
  officeId: '',
  /** 利用者ID */
  userId: '',
  /** 職員ID */
  staffId: '',
  /** 担当ケアマネ設定フラグ */
  careManagerSettingFlg: '',
  /** 担当者ID */
  tantoshaId: '',
  /** システム年月 */
  systemYm: '',
  /** システム年月日 */
  systemYmd: '',
  /** セクション名 */
  sectionName: '',
  /** 選択帳票番号 */
  selectedLedgerNo: '',
  /** 処理年月日 */
  processYmd: '',
  /** 50音行番号 */
  gojuuonRowNo: '',
  /** 50音母音 */
  gojuuonKana: [],
  /** 履歴ID */
  historyId: '',
  /** 利用者情報リスト */
  userInfoList: [],
  /** アセスメントID */
  assessmentId: '',
  /** 適用事業所IDリスト */
  applicableOfficeIdList: [],
  /** 事業所名 */
  jigyosha: '',
  /** 基準日 */
  stGendateYmd: '',
  /** 実施日 */
  appYmd: '',
}

const localOneway = reactive({
  or28974: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // コードマスタ戻り情報
  codeMasterValues: [] as CodeType[],
  mo00039OnewayDate: {
    name: 'date',
    items: [],
    showItemLabel: false,
    hideDetails: true,
    inline: false,
  } as Mo00039OnewayType,
  mo00039OnewayUser: {
    name: 'user',
    itemLabel: t('label.printer-user-select'),
    items: [],
    showItemLabel: true,
    hideDetails: true,
    inline: true,
  } as Mo00039OnewayType,
  mo00039OnewayHistory: {
    name: 'history',
    itemLabel: t('label.printer-history-select'),
    items: [],
    showItemLabel: true,
    hideDetails: true,
    inline: true,
    customClass: new CustomClass({
      outerClass: 'ml-10',
    }),
  } as Mo00039OnewayType,
  mo00040OnewayCare: {
    itemLabel: '',
    showItemLabel: false,
    isRequired: false,
    items: [],
    width: '200px',
  } as Mo00040OnewayType,
  mo00045Oneway: {
    showItemLabel: true,
    itemLabel: t('label.print-settings-title'),
    readonly: false,
    isVerticalLabel: false,
    customClass: {
      outerClass: 'mr-0 mt-1',
      outerStyle: 'width: -webkit-fill-available;',
    },
  } as Mo00045OnewayType,
  mo00009OnewayBack: {
    btnIcon: 'chevron_left',
    density: 'compact',
    size: '36px',
  },
  mo00009OnewayForward: {
    btnIcon: 'chevron_right',
    density: 'compact',
    size: '36px',
  },
  mo00009OnewayEdit: {
    btnIcon: 'edit_square',
    density: 'compact',
    size: '36px',
  },
  mo00045OnewayTitle: {
    showItemLabel: false,
    customClass: { itemClass: 'ml-0' },
  } as Mo00045OnewayType,
  mo01379OnewayH21: {
    value: t('label.valid-after-h21-revision'),
  } as Mo01379OnewayType,
  // 印刷ボタン
  mo00609OneWay: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-0' }),
  } as OrX0145OnewayType,
})

/**
 * 担当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

const local = reactive({
  iniDataObject: [] as LedgerInitData[],
  cpnTucCschList: [] as NinteiHyoInfo[],
  userSelList: [] as { userid: string }[],
  mo00039DateModel: Or28974Const.DATE_PRINT_CATEGORY_2,
  mo00039UserModel: Or28974Const.SELECT_CATEGORY_SINGLE,
  mo00039HistoryModel: Or28974Const.SELECT_CATEGORY_SINGLE,
  mo00045ModelTitle: {
    value: '',
  },
  mo00040ModelKaigodo: {
    modelValue: '0',
  },
  or26261Type: {
    shokuIdList: [],
    shokuin: {} as Shokuin,
    svJigyoIdList: [],
  } as Or26261Type,
  // コードマスタ 改訂区分
  codeMasterValuesKaitei: [] as CodeType[],
  /** 担当者ID */
  tantoshaId: '',
  /** 基準日 */
  mo00020modelKijun: {
    value: '',
  } as Mo00020Type,
  /** 指定日 */
  mo00020modelShitei: {
    value: '',
  } as Mo00020Type,
  /** 対象日 */
  mo00020modelTaishou: {
    value: '',
  } as Mo00020Type,
  mo00018ModelSheet: {
    modelValue: false,
  } as Mo00018Type,
  mo00018ModelOfficeName: {
    modelValue: false,
  } as Mo00018Type,
  mo00018ModelBlankRow: {
    modelValue: false,
  } as Mo00018Type,
  mo00018ModelExample: {
    modelValue: false,
  } as Mo00018Type,
  mo00018ModelMatter9: {
    modelValue: false,
  } as Mo00018Type,
  mo00018ModelLastResult: {
    modelValue: false,
  } as Mo00018Type,
  mo00018ModelUserName: {
    modelValue: false,
  } as Mo00018Type,
  /**
   * 帳票ID
   */
  reportId: Or28974Const.DEFAULT.EMPTY,
})

/**
 * ダイアログ設定
 */
const mo00024Oneway = reactive({
  width: '1400px',
  maxWidth: '1400px',
  height: '820px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or28974',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or28974ToolBar',
    // ツールバータイトルの左寄せ
    toolbarTitleCenteredFlg: false,
    showToolbar: true,
    showCardActions: true,
    scrollable: false,
    cardTextClass: 'or28974-nopadding-content',
  },
}) as Mo00024OnewayType

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 印刷設定利用者リスト
 */
const userList1 = ref<userList[]>([])

/**
 * 認定調査票情報リスト
 */
const cpnTucCschList1 = ref<cpnTucCschList[]>([])

/**
 * Orx0130 oneway
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width: 300px',
  //focusSettingInitial: [localOneway.or28974.gojuuonKana],
})

// 利用者一覧の幅
const userCols = ref('6')

/**
 * Orx0133 oneway
 */
const orX0133Oneway = reactive<OrX0133OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: localOneway.or28974.planPeriodManagementFlg,
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: Or28974Const.SELECT_CATEGORY_SINGLE,
  tableStyle: 'width:334px; height: 511px',
  itemShowFlg: {
    createYmdShokuKnjFlg: true,
    caseNoFlg: false,
    tougaiYmFlg: false,
    kaiteiKnjFlg: true,
  },
  rirekiList: [] as OrX0133TableData[],
})

const orX0117Oneway = reactive<OrX0117OnewayType>({
  type: local.mo00039UserModel,
  historyList: [] as OrX0117History[],
})

const showDialogOrx0117 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  担当ケアマネ選択非活性化
 */
const careManagerDisabled = computed(() => {
  // 親画面.担当ケアマネ設定フラグが0より大きい、かつ、親画面.担当者IDが0以外の場合
  return localOneway.or28974.careManagerSettingFlg > '0' && localOneway.or28974.tantoshaId !== '0'
})

/**
 * 担当ケアマネ
 */
const mo00615OnewayCareName = reactive({
  itemLabel: '',
  itemLabelFontWeight: careManagerDisabled.value ? '100' : '500',
}) as Mo00615OnewayType

/**
 * 年月テキストフィールドの単方向モデル
 */
const mo00020OnewayNoArrow = ref<Mo00020OnewayType>({
  isRequired: false,
  showItemLabel: false,
  hideDetails: true,
  width: '130px',
  showSelectArrow: false,
  customClass: new CustomClass({
    outerClass: 'ml-0',
  }),
  mo01343Oneway: {
    selectMode: '0',
    closedDayDisplayOffice: '',
    rangeSelectDefaultPeriod: 0,
  },
})

/**
 * 基準日
 */
const mo00020OnewayKijun = ref<Mo00020OnewayType>({
  isRequired: false,
  itemLabel: t('label.base-date'),
  showItemLabel: true,
  isVerticalLabel: true,
  hideDetails: true,
  width: '130px',
  showSelectArrow: true,
  customClass: new CustomClass({
    outerClass: 'ml-0',
  }),
  mo01343Oneway: {
    selectMode: '0',
    closedDayDisplayOffice: '',
    rangeSelectDefaultPeriod: 0,
  },
})

/**
 * 記入用シートを印刷する
 */
const mo00018OnewaySheet = ref<Mo00018OnewayType>({
  checkboxLabel: t('label.printer-entry-sheet'),
  isVerticalLabel: false,
})
/**
 * 記入用シートを印刷する 初期化時が非活性か
 */
const isInitDisabledSheet = ref(false)

/**
 * 空白行をつめて印刷する
 */
const mo00018OnewayBlankRow = ref<Mo00018OnewayType>({
  checkboxLabel: t('label.fill-with-blank-row-to-print'),
  isVerticalLabel: false,
  disabled: local.mo00018ModelSheet.modelValue,
})
/**
 * 空白行をつめて印刷する 初期化時が非活性か
 */
const isInitDisabledBlankRow = ref(false)

/**
 * 項目が頁をまたぐ場合は凡例を印刷しない
 */
const mo00018OnewayExample = ref<Mo00018OnewayType>({
  checkboxLabel: t('label.print-without-example-when-over-page'),
  isVerticalLabel: false,
  customClass: new CustomClass({ outerClass: 'ml-7' }),
})
/**
 * 項目が頁をまたぐ場合は凡例を印刷しない 初期化時が非活性か
 */
const isInitDisabledExample = ref(false)

/**
 * 特記事項9を印刷する
 */
const mo00018OnewayMatter9 = ref<Mo00018OnewayType>({
  checkboxLabel: t('label.print-matter-9'),
  isVerticalLabel: false,
})

/**
 * 前回結果を印刷する
 */
const mo00018OnewayLastResult = ref<Mo00018OnewayType>({
  checkboxLabel: t('label.print-last-result'),
  isVerticalLabel: false,
})

/**
 * 利用者名を印刷する
 */
const mo00018OnewayUserName = ref<Mo00018OnewayType>({
  checkboxLabel: t('label.print-user-name'),
  isVerticalLabel: false,
})

// 「記入用シートを印刷する」チェック変更の場合の処理
watch(
  () => local.mo00018ModelSheet.modelValue,
  (newValue) => {
    mo00018OnewayBlankRow.value.disabled = isInitDisabledBlankRow.value || newValue
  }
)

// 「空白行をつめて印刷する」状態変更の場合の処理
watch(
  [() => local.mo00018ModelBlankRow.modelValue, () => mo00018OnewayBlankRow.value.disabled],
  ([newValue1, newValue2]) => {
    mo00018OnewayExample.value.disabled = isInitDisabledExample.value || !newValue1 || newValue2
  }
)

/**
 * 出力帳票名一覧
 */
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'prtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 650,
})

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**************************************************
 * 変数定義
 **************************************************/
/**
 * mo00024
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28974Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/
/**
 * setState
 */
const { setState } = useScreenOneWayBind<Or28974StateType>({
  cpId: Or28974Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * isOpen
     *
     * @param value - 開閉フラグ
     */
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or28974Const.DEFAULT.IS_OPEN
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(1)]: or21813.value,
  [OrX0130Const.CP_ID(1)]: orX0130.value,
  [OrX0133Const.CP_ID(1)]: orX0133.value,
  [OrX0117Const.CP_ID(1)]: orX0117.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onBeforeMount(async () => {
  await initCodes()
  await init()
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 週単位以外のサービス
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_EVERYWEEK },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // 性別区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
    // 改訂区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NINTEIFLG },
    // 集計する要介護度
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DEGREE_NURSING_CARE },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分ラジオ
  localOneway.mo00039OnewayDate.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  // 利用者選択ラジオ
  localOneway.mo00039OnewayUser.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  // 履歴選択ラジオ
  localOneway.mo00039OnewayHistory.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  // 改訂様式リスト
  local.codeMasterValuesKaitei = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_NINTEIFLG)
  // 集計する要介護度
  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_DEGREE_NURSING_CARE).forEach((item) =>
    localOneway.mo00040OnewayCare.items?.push({ value: item.value, title: item.label })
  )
}

/**
 * 計画転送一覧初期情報取得
 */
async function init() {
  // 指定日
  local.mo00020modelShitei.value = convertDateToSeireki(new Date())
  // 対象年月
  local.mo00020modelTaishou.value = localOneway.or28974.systemYm
  // 基準日
  local.mo00020modelKijun.value = localOneway.or28974.processYmd

  // リクエストパラメータ
  const inputData: PrintSettingsScreenInitialInfoSelectGUI01283InEntity = {
    /** システムコード */
    sysCd: localOneway.or28974.systemCode,
    /** システム略称 */
    sysRyaku: localOneway.or28974.systemNameShort,
    /** 機能名 */
    kinounameKnj: Or28974Const.FUNCTION_NAME_PRINT,
    /** 計画期間管理フラグ */
    kikanFlg: localOneway.or28974.planPeriodManagementFlg,
    /** 法人ID */
    houjinId: localOneway.or28974.corporationId,
    /** 施設ID */
    shisetuId: localOneway.or28974.facilityId,
    /** 事業者ID */
    svJigyoId: localOneway.or28974.officeId,
    /** 利用者ID */
    userId: localOneway.or28974.userId,
    /** 職員ID */
    shokuId: localOneway.or28974.staffId,
    /** 担当者ID */
    tantoId: careManagerDisabled.value ? localOneway.or28974.tantoshaId : '0',
    /** 処理年月日 */
    appYmd: localOneway.or28974.systemYmd,
    /** セクション名 */
    sectionName: localOneway.or28974.sectionName,
    /** 選択帳票番号 */
    choIndex: localOneway.or28974.selectedLedgerNo,
    /** 個人情報表示フラグ */
    kojinhogoFlg: Or28974Const.PERSONAL_INFO_SHOW_FLG_0,
    /** 個人情報表示値 */
    sectionAddNo: Or28974Const.PERSONAL_INFO_SHOW_VALUE_0,
  }

  // 初期情報取得
  const ret: PrintSettingsScreenInitialInfoSelectGUI01283OutEntity = await ScreenRepository.select(
    'printSettingsScreenInitialInfoSelectGUI01283',
    inputData
  )

  console.log(ret, '===========')

  // 帳票INIデータリスト
  local.iniDataObject = ret.data.iniDataObject ?? []
  // 認定調査票情報リスト
  local.cpnTucCschList = ret.data.cpnTucCschList ?? []
  // 利用者絞込リスト
  if (ret.data.userSelList.length > 0) {
    local.userSelList =
      ret.data.userSelList.map((item) => {
        return { userid: item.userId }
      }) ?? []
  }
  // 出力帳票一覧の設定
  ret.data.choPrtList.forEach((item) => {
    const rowData = { id: item.prtNo, ...item } as DataRow
    mo01334Oneway.value.items.push(rowData)
  })
  // 出力帳票一覧明細行選択
  mo01334Type.value.value = localOneway.or28974.selectedLedgerNo

  // 記入用シートを印刷する
  local.mo00018ModelSheet.modelValue = false

  // 親画面.処理年月日が""でない場合
  if (localOneway.or28974.processYmd !== '') {
    // 親画面.担当ケアマネ設定フラグが0より大きい、かつ、親画面.担当者IDが0以外の場合
    if (careManagerDisabled.value) {
      // 担当ケアマネ
      mo00615OnewayCareName.itemLabel = localOneway.or28974.tantoshaId
      local.tantoshaId = localOneway.or28974.tantoshaId
    } else {
      // 担当ケアマネ
      mo00615OnewayCareName.itemLabel = ''
    }
  }
}

/**
 *  出力帳票一覧明細選択変更
 */
watch(
  () => mo01334Type.value.value,
  async (newValue, oldValue) => {
    // 切替前の情報を退避する
    saveRowStatus(oldValue)

    // 切替後の情報を設定する
    // 帳票イニシャライズデータを取得する
    await getReportInitData()

    // タイトル
    local.mo00045ModelTitle.value = selectedRowData.value.prtTitle
    // 日付印刷区分
    local.mo00039DateModel = selectedRowData.value.prndate
    // 印刷オプション
    // 記入用シートを印刷する
    // 画面.出力帳票一覧明細に選択される行.プリントナンバー = 1~7、10、14の場合
    //    画面.利用者選択 = 単一、かつ、画面.履歴選択 = 単一の場合、活性
    //    上記以外の場合、非活性
    // 上記以外の場合、非活性
    if (
      [
        ...Or28974Const.PRINT_NO_1_TO_7,
        Or28974Const.PRINT_NO_10,
        Or28974Const.PRINT_NO_14,
      ].includes(selectedRowData.value.prtNo)
    ) {
      if (
        local.mo00039UserModel === Or28974Const.SELECT_CATEGORY_SINGLE &&
        local.mo00039HistoryModel === Or28974Const.SELECT_CATEGORY_SINGLE
      ) {
        mo00018OnewaySheet.value.disabled = false
      } else {
        mo00018OnewaySheet.value.disabled = true
      }
      isInitDisabledSheet.value = false
    } else {
      mo00018OnewaySheet.value.disabled = true
      isInitDisabledSheet.value = true
    }

    // サービス事業者名を印刷する
    local.mo00018ModelOfficeName.modelValue = Boolean(Number(selectedRowData.value.param05))

    // 空白行をつめて印刷する
    local.mo00018ModelBlankRow.modelValue = Boolean(Number(selectedRowData.value.param09))
    // 画面.出力帳票一覧明細に選択される行.プリントナンバー = 6、7、10の場合
    //    画面.記入用シートを印刷する = チェックオフの場合、活性
    //    画面.記入用シートを印刷する = チェックオンの場合、非活性
    // 上記以外の場合、非活性
    if (
      [Or28974Const.PRINT_NO_6, Or28974Const.PRINT_NO_7, Or28974Const.PRINT_NO_10].includes(
        selectedRowData.value.prtNo
      )
    ) {
      if (!local.mo00018ModelSheet.modelValue) {
        mo00018OnewayBlankRow.value.disabled = false
      } else {
        mo00018OnewayBlankRow.value.disabled = true
      }
      isInitDisabledBlankRow.value = false
    } else {
      mo00018OnewayBlankRow.value.disabled = true
      isInitDisabledBlankRow.value = true
    }

    // 項目が頁をまたぐ場合は凡例を印刷しない
    local.mo00018ModelExample.modelValue = Boolean(Number(selectedRowData.value.param10))
    // 画面.出力帳票一覧明細に選択される行.プリントナンバー = 6、7の場合
    //   画面.空白行をつめて印刷する = チェックオフの場合、非活性
    //   画面.空白行をつめて印刷する = チェックオンの場合、活性
    // 上記以外の場合、非活性
    if ([Or28974Const.PRINT_NO_6, Or28974Const.PRINT_NO_7].includes(selectedRowData.value.prtNo)) {
      if (!local.mo00018ModelBlankRow.modelValue) {
        mo00018OnewayExample.value.disabled = true
      } else {
        mo00018OnewayExample.value.disabled = false
      }
      isInitDisabledExample.value = false
    } else {
      mo00018OnewayExample.value.disabled = true
      isInitDisabledExample.value = true
    }

    // 特記事項９を印刷する
    local.mo00018ModelMatter9.modelValue = Boolean(Number(selectedRowData.value.param07))
    // 画面.出力帳票一覧明細に選択される行.プリントナンバー = 6、7、10の場合
    //   画面.記入用シートを印刷する = チェックオフの場合、活性
    //   画面.記入用シートを印刷する = チェックオンの場合、非活性
    // 上記以外の場合、非活性
    if (
      [Or28974Const.PRINT_NO_6, Or28974Const.PRINT_NO_7, Or28974Const.PRINT_NO_10].includes(
        selectedRowData.value.prtNo
      )
    ) {
      if (!local.mo00018ModelSheet.modelValue) {
        mo00018OnewayMatter9.value.disabled = false
      } else {
        mo00018OnewayMatter9.value.disabled = true
      }
    } else {
      mo00018OnewayMatter9.value.disabled = true
    }

    // 前回結果を印刷する
    local.mo00018ModelLastResult.modelValue = Boolean(Number(selectedRowData.value.param08))
    // 画面.出力帳票一覧明細に選択される行.プリントナンバー = 9の場合、活性
    // 上記以外の場合、非活性
    if (selectedRowData.value.prtNo === Or28974Const.PRINT_NO_9) {
      mo00018OnewayLastResult.value.disabled = false
    } else {
      mo00018OnewayLastResult.value.disabled = true
    }

    // 利用者名を印刷する
    local.mo00018ModelUserName.modelValue = Boolean(Number(selectedRowData.value.param06))
    // 画面.出力帳票一覧明細に選択される行.プリントナンバー = 9の場合、活性
    // 上記以外の場合、非活性
    if (selectedRowData.value.prtNo === Or28974Const.PRINT_NO_9) {
      mo00018OnewayUserName.value.disabled = false
    } else {
      mo00018OnewayUserName.value.disabled = true
    }
  }
)

/**
 * 帳票イニシャライズデータを取得する。
 */
async function getReportInitData() {
  const inputData: LedgerInitializeDataComSelectInEntity = {
    // システムコード
    sysCd: localOneway.or28974.systemCode,
    // 機能名
    kinounameKnj: Or28974Const.FUNCTION_NAME_PRINT,
    // 職員ID
    shokuId: localOneway.or28974.staffId,
    // セクション
    sectionKnj: selectedRowData.value.choPro,
    // 個人情報表示フラグ
    kojinhogoFlg: Or28974Const.PERSONAL_INFO_SHOW_FLG_0,
    // 個人情報表示値
    sectionAddNo: Or28974Const.PERSONAL_INFO_SHOW_VALUE_0,
  }
  // バックエンドAPIから初期情報取得
  const ret: LedgerInitializeDataComSelectOutEntity = await ScreenRepository.select(
    'ledgerInitializeDataComSelect',
    inputData
  )
  local.iniDataObject = ret.data.iniDataObject
}

/**
 * 出力帳票一覧の選択行変更時、画面の入力内容を行のデータとして保存する
 *
 * @param oldId - 変更前のID
 */
function saveRowStatus(oldId: string) {
  if (oldId === '') {
    return
  }

  const oldRow = mo01334Oneway.value.items.find((item) => item.id === oldId) as DataRow
  // 帳票タイトルが空白以外の場合
  if (local.mo00045ModelTitle.value.trim().length !== 0) {
    // 帳票タイトル = 帳票タイトル
    oldRow.prtTitle = local.mo00045ModelTitle.value
  }
  // 日付表示有無 = 日付印刷区分
  oldRow.prndate = local.mo00039DateModel
  // パラメータ05 =画面.サービス事業者名を印刷する
  oldRow.param05 = String(Number(local.mo00018ModelOfficeName.modelValue))
  // パラメータ06 =画面.利用者名を印刷する
  oldRow.param06 = String(Number(local.mo00018ModelUserName.modelValue))
  // パラメータ07 =画面.特記事項９を印刷する
  oldRow.param07 = String(Number(local.mo00018ModelMatter9.modelValue))
  // パラメータ08 =画面.前回結果を印刷する
  oldRow.param08 = String(Number(local.mo00018ModelLastResult.modelValue))
  // パラメータ09 =画面.空白行をつめて印刷する
  oldRow.param09 = String(Number(local.mo00018ModelBlankRow.modelValue))
  // パラメータ10 =画面.項目が頁をまたぐ場合は凡例を印刷しない
  oldRow.param10 = String(Number(local.mo00018ModelExample.modelValue))
}

/**
 * 選択行のデータ
 */
const selectedRowData = computed(() => {
  return mo01334Oneway.value.items.find((item) => item.id === mo01334Type.value.value) as DataRow
})

// 「利用者選択」ラジオボタン選択変更
watch(
  () => local.mo00039UserModel,
  (newValue) => {
    orX0130Oneway.selectMode = newValue

    // 利用者選択が単一の場合
    if (newValue === OrX0130Const.DEFAULT.TANI) {
      // 幅の調整
      userCols.value = '6'

      // 利用者選択が単一で、かつ履歴単複数選択が”単一”の場合、記入用シートを印刷するを活性表示にする
      if (
        local.mo00039UserModel === Or28974Const.SELECT_CATEGORY_SINGLE &&
        local.mo00039HistoryModel === Or28974Const.SELECT_CATEGORY_SINGLE
      ) {
        mo00018OnewaySheet.value.disabled = isInitDisabledExample.value || false
      } else {
        mo00018OnewaySheet.value.disabled = true
      }
      orX0130Oneway.tableStyle = 'width: 300px'
    } else {
      // 幅の調整
      userCols.value = '11'

      // 記入用シートを印刷するをチェックオフにする
      local.mo00018ModelSheet.modelValue = false
      // 記入用シートを印刷するを非活性にする
      mo00018OnewaySheet.value.disabled = true
      orX0130Oneway.tableStyle = 'width: 430px'
    }
  }
)

// 「履歴選択」ラジオボタン選択変更
watch(
  () => local.mo00039HistoryModel,
  (newValue) => {
    // 履歴選択が単一の場合
    if (newValue === Or28974Const.SELECT_CATEGORY_SINGLE) {
      // 記入用シートを印刷するを活性表示にする
      mo00018OnewaySheet.value.disabled = isInitDisabledSheet.value || false

      // 履歴一覧明細が単一選択モードにする
      orX0133Oneway.singleFlg = Or28974Const.SELECT_CATEGORY_SINGLE
    } else {
      // 記入用シートを印刷するをチェックオフにする
      local.mo00018ModelSheet.modelValue = false
      // 記入用シートを印刷するを非活性にする
      mo00018OnewaySheet.value.disabled = true

      // 履歴一覧明細を複数選択モードにする
      orX0133Oneway.singleFlg = Or28974Const.SELECT_CATEGORY_MULTIPLE
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.userList !== undefined && newValue.userList.length > 0) {
      // 利用者選択
      // 利用者選択方法が「単一」の場合
      if (local.mo00039UserModel === Or28974Const.SELECT_CATEGORY_SINGLE) {
        await getHistoricalInfoList(newValue.userList[0].userId)
      }
    }
  }
)

/**
 * 履歴情報を取得する
 *
 *   @param userId - 利用者ID
 */
async function getHistoricalInfoList(userId: string) {
  orX0133Oneway.rirekiList = []
  const inputData: CertificationSurveySlipInfoSelectInEntity = {
    kikanFlg: localOneway.or28974.planPeriodManagementFlg,
    svJigyoId: localOneway.or28974.officeId,
    userId: userId,
  }
  // バックエンドAPIから初期情報取得
  const ret: CertificationSurveySlipInfoSelectOutEntity = await ScreenRepository.select(
    'certificationSurveySlipInfoSelect',
    inputData
  )

  local.cpnTucCschList = ret.data.cpnTucCschList
  cpnTucCschList1.value = ret.data.cpnTucCschList as unknown as cpnTucCschList[]

  // 認定調査票情報リスト
  if (ret.data.cpnTucCschList.length > 0) {
    let id = 1
    let id1 = 0
    ret.data.cpnTucCschList?.forEach((item) => {
      id = id1 + id
      // 「期間管理フラグが「管理する」の場合」、表示
      if (localOneway.or28974.planPeriodManagementFlg === Or28974Const.PERIOD_MANAGE_FLG_MANAGE) {
        // 計画期間明細
        const tmpItem = {
          planPeriod:
            t('label.plan-period') +
            t('label.colon-mark') +
            item.startYmd +
            t('label.wavy') +
            item.endYmd,
        } as OrX0133TableData
        orX0133Oneway.rirekiList.push(tmpItem)
      }
      id1 = item.historyList.length
      item.historyList?.forEach((item, index) => {
        const tmpItem1 = {
          planPeriod: '',
          // 作成日
          createYmd: item.jisshiDateYmd,
          kijunbiYmd: '',
          // 作成者
          shokuKnj: item.shokuinKnj,
          shokuinKnj: '',
          caseNo: '',
          tougaiYm: '',
          kaisuu: '',
          // 改訂
          kaiteiKnj: getMastCodeLabel(local.codeMasterValuesKaitei, item.dmyCho),
          youshikiKnj: '',
          gdlId: (index + id).toString(),
          assType: '',
          assDateYmd: '',
        } as OrX0133TableData
        orX0133Oneway.rirekiList.push(tmpItem1)
      })
      cpnTucCschList1.value.forEach((item) => {
        item.historyList.map((item1, index) => {
          item1.id = (index + id).toString()
          return {
            ...item1,
          }
        })
      })
    })
  }
}

/**
 * マスタコード情報から指定したvalueでlabelを取得する
 *
 * @param codeValues - マスタコード情報
 *
 * @param value  - 指定したvalue
 */
function getMastCodeLabel(codeValues: CodeType[], value: string) {
  return codeValues.find((item) => item.value === value)?.label ?? ''
}

/**
 * 集計する要介護度ボタンクリック
 */
function youkaigoBtnClick() {
  // 画面.集計する要介護度 = 要介護５の場合
  if (local.mo00040ModelKaigodo.modelValue === Or28974Const.YOUKAIGODO_CODE_7) {
    // 集計する要介護度 = 全て
    local.mo00040ModelKaigodo.modelValue = Or28974Const.YOUKAIGODO_CODE_ALL
  } else {
    // 集計する要介護度の次項目選択
    const index =
      localOneway.mo00040OnewayCare.items?.findIndex(
        (item) => (item as CodeData).value === local.mo00040ModelKaigodo.modelValue
      ) ?? 0
    const item = localOneway.mo00040OnewayCare.items?.at(index + 1) as CodeData
    local.mo00040ModelKaigodo.modelValue = item.value
  }
}

/**
 * ケアマネ選択戻り情報
 */
watch(
  () => local.or26261Type.shokuin,
  async (newValue) => {
    // 担当ケアマネ
    local.tantoshaId = newValue.chkShokuId
    mo00615OnewayCareName.itemLabel = newValue.shokuin1Knj + ' ' + newValue.shokuin2Knj

    if (local.tantoshaId && local.tantoshaId.trim() !== '') {
      // 絞込用利用者IDを取得する
      await getFilterForUserIdComSelect()
    }
  }
)

/**
 * 絞込用利用者IDを取得する
 */
async function getFilterForUserIdComSelect() {
  const inputData: FilterForUserIdComSelectInEntity = {
    // 担当者ID
    tantoId: local.tantoshaId,
    startYmd: localOneway.or28974.processYmd,
    endYmd: '',
  }

  // バックエンドAPIから初期情報取得
  const ret: FilterForUserIdComSelectOutEntity = await ScreenRepository.select(
    'filterForUserIdComSelect',
    inputData
  )

  local.userSelList = ret.data.userSelList
}

//変更前の年月を保存する変数
const beforeYearMonth = ref('')

/**
 * 文字列の形式の判定
 * YYYY/MM/DD または YYYY/MM の形式であるか照合する
 *
 * @param str -照合対象の文字列
 */
function isValidDateFormat(str: string): boolean {
  // 正規表現(YYYY/MM/DD or YYYY/MM)
  const regex = /^(?:\d{4})\/(?:\d{2})\/(?:\d{2})$|^(?:\d{4})\/(?:\d{2})$/

  // 正規表現と指定された文字列を照合
  return regex.test(str)
}

/**
 * YYYY/MM/DD形式をYYYY/MM形式に変更
 */
function formatDateToYYMM() {
  if (isValidDateFormat(local.mo00020modelTaishou.value)) {
    const date = new Date(local.mo00020modelTaishou.value)
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')

    local.mo00020modelTaishou.value = `${year}/${month}`

    beforeYearMonth.value = local.mo00020modelTaishou.value
  } else {
    local.mo00020modelTaishou.value = beforeYearMonth.value
  }
}

/**
 * 前月、翌月ボタンの処理
 *
 * @param isForward - 翌月の場合、true
 */
function clickForwardOrBack(isForward: boolean) {
  if (local.mo00020modelTaishou.value) {
    const date = new Date(local.mo00020modelTaishou.value)
    if (isForward) {
      date.setMonth(date.getMonth() + 1)
    } else {
      date.setMonth(date.getMonth() - 1)
    }
    local.mo00020modelTaishou.value = convertDateToSeireki(date)
  }
}

watch(
  // 処理年月
  () => local.mo00020modelTaishou,
  () => {
    formatDateToYYMM()
  }
)

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  // 画面状態を選択行のデータに保存
  saveRowStatus(selectedRowData.value.id)

  // 保存処理
  await savePrintSettingInfo()

  setState({ isOpen: false })
}

/**
 * 画面の印刷設定情報を保存する
 */
async function savePrintSettingInfo() {
  // 画面の印刷設定情報を保存する
  const inputData: PrintSettingsInfoComUpdateInEntity = {
    // システムコード
    sysCd: localOneway.or28974.systemCode,
    // システム略称
    sysRyaku: localOneway.or28974.systemNameShort,
    // 機能名
    kinounameKnj: Or28974Const.FUNCTION_NAME_PRINT,
    // 法人ID
    houjinId: localOneway.or28974.corporationId,
    // 施設ID
    shisetuId: localOneway.or28974.facilityId,
    // 事業者ID
    svJigyoId: localOneway.or28974.officeId,
    // 職員ID
    shokuId: localOneway.or28974.staffId,
    // プロファイル
    choPro: selectedRowData.value.choPro,
    // 個人情報表示フラグ
    kojinhogoFlg: Or28974Const.PERSONAL_INFO_SHOW_FLG_0,
    // 個人情報表示値
    sectionAddNo: Or28974Const.PERSONAL_INFO_SHOW_VALUE_0,
    // 出力帳票印刷情報リスト
    choPrtList: [selectedRowData.value],
    // 帳票INIデータオブジェクト
    iniDataObject: [...local.iniDataObject],
    // セクション名
    sectionName: '',
  }

  await ScreenRepository.update('printSettingsInfoComUpdate', inputData)
}

/**
 * 「PDFダウンロード」ボタン押下
 */
async function printBtnClick() {
  // 選択された帳票のプロファイルが””の場合
  if (selectedRowData.value.choPro === '') {
    showOr21813Msg(t('message.e-cmn-40172'))
    return
  }

  const userList = OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList
  const historyList = OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList

  // 利用者選択方法が「単一」、履歴一覧明細にデータを選択しない場合
  if (
    local.mo00039UserModel === Or28974Const.SELECT_CATEGORY_SINGLE &&
    (historyList === undefined || historyList.length === 0)
  ) {
    return
  }

  // 利用者選択方法が「複数」、利用者情報リストにデータを選択しない場合
  if (
    local.mo00039UserModel === Or28974Const.SELECT_CATEGORY_MULTIPLE &&
    (userList === undefined || userList.length === 0)
  ) {
    return
  }
  // 利用者選択方法が「単一」
  if (Or28974Const.SELECT_CATEGORY_SINGLE === local.mo00039UserModel) {
    // 履歴選択が「単一」
    if (Or28974Const.SELECT_CATEGORY_SINGLE === local.mo00039HistoryModel) {
      //記入用シートを印刷するチェック入れるの場合
      if (local.mo00018ModelSheet.modelValue) {
        const value = OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList[0].gdlId
        await downloadPdf(value)
        return
      }
      // 記入用シートを印刷するチェック外すの場合
      else {
        // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
        // if (local.userSelect) {
        //   const dialogResult = await openConfirmDialog(
        //     or21814.value.uniqueCpId,
        //     t('message.i-cmn-11393')
        //   )
        //   // はい
        //   if (dialogResult === Or28974Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
        //     // 処理終了
        //     return
        //   }
        // }
        // 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
        // if (!OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList.length) {
        //   const dialogResult = await openConfirmDialog(
        //     or21814.value.uniqueCpId,
        //     t('message.i-cmn-11455')
        //   )
        //   // はい
        //   if (dialogResult === Or28974Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
        //     // 処理終了
        //     return
        //   }
        // }
      }
    }
    // 履歴選択方法が「複数」
    else if (Or28974Const.SELECT_CATEGORY_MULTIPLE === local.mo00039HistoryModel) {
      // 履歴情報リストにデータを選択する場合
      if (OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList.length) {
        // 印刷設定情報リストを作成
        const list = OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList
        list?.forEach((item) => {
          if (item.kaiteiKnj) {
            const value = item.gdlId
            void downloadPdf(value)
          }
        })
      }
    }
  }

  // 画面の印刷データを設定する
  saveRowStatus(selectedRowData.value.id)
  // 画面の印刷設定情報を保存する。
  await savePrintSettingInfo()

  // OrX0117のダイアログ開閉状態を更新する
  OrX0117Logic.state.set({
    uniqueCpId: orX0117.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * pdf download
 *
 * @param val - id
 */

const downloadPdf = async (val: unknown) => {
  let sc1Id = ''
  let cschId = ''
  let flg = false

  cpnTucCschList1.value.forEach((item) => {
    item.historyList.forEach((item1) => {
      if (item1.id === val) {
        cschId = item1.cschId1
        flg = true
      }
    })
    if (flg) {
      sc1Id = item.sc1Id
    }
  })

  const inputData: BasicSurveyReportEntity = {
    createYmd: local.mo00020modelKijun.value,
    title: local.mo00045ModelTitle.value,
    sc1Id: sc1Id,
    cschId: cschId,
    userId: localOneway.or28974.userId,
    shokuId: localOneway.or28974.staffId,
    optionNo: '',
    houjinId: localOneway.or28974.corporationId,
    shisetuId: localOneway.or28974.facilityId,
    svJigyoId: localOneway.or28974.officeId,
    jigyosha: localOneway.or28974.jigyosha,
    syscd: localOneway.or28974.systemCode,
    asYmd: localOneway.or28974.systemYmd,
    stGendateYmd: localOneway.or28974.stGendateYmd,
    appYmd: localOneway.or28974.appYmd,
    taisyouDate: local.mo00020modelTaishou.value,
    printSet: {
      shiTeiKubun: local.mo00039DateModel,
      shiTeiDate: local.mo00020modelShitei.value,
    },
    printOption: {
      emptyFlg: local.mo00018ModelSheet.modelValue ? '1' : '0',
      jigyouFlg: local.mo00018ModelOfficeName.modelValue ? '1' : '0',
      choempFlg: local.mo00018ModelBlankRow.modelValue ? '1' : '0',
      chohanreiFlg: local.mo00018ModelExample.modelValue ? '1' : '0',
      zenFlg: local.mo00018ModelLastResult.modelValue ? '1' : '0',
      riyonameFlg: local.mo00018ModelUserName.modelValue ? '1' : '0',
      stTaisho: local.mo00040ModelKaigodo.modelValue,
    },
    userList: userList1.value,
  } as BasicSurveyReportEntity
  console.log(inputData, '===========')
  const list = OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList
  list?.forEach((item) => {
    if (item.kaiteiKnj) {
      const date1 = new Date(item.createYmd!)
      const date2 = new Date(Or28974Const.DATE)
      if (item.kaiteiKnj === Or28974Const.H21 && date1 >= date2) {
        switch (selectedRowData.value.prtNo) {
          case Or28974Const.PRINT_NO_1:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.GENER_ALL
            break
          case Or28974Const.PRINT_NO_2:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.BASIC_1
            break
          case Or28974Const.PRINT_NO_3:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.BASIC_2
            break
          case Or28974Const.PRINT_NO_4:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.BASIC_3
            break
          case Or28974Const.PRINT_NO_5:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.BASIC_4
            break
          case Or28974Const.PRINT_NO_6:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.BASIC_SPEC
            break
          case Or28974Const.PRINT_NO_7:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.CERTIFIC
            break
          case Or28974Const.PRINT_NO_8:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.FIRST
            break
          case Or28974Const.PRINT_NO_9:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.SURVEY
            break
          case Or28974Const.PRINT_NO_10:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.SPECIA
            break
          case Or28974Const.PRINT_NO_11:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.YOKAIGODO
            break
          case Or28974Const.PRINT_NO_12:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.LEVELOF
            break
          case Or28974Const.PRINT_NO_13:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.INVESTIGA
            break
          case Or28974Const.PRINT_NO_14:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.BASIC_5
            break
          default:
            local.reportId = Or28974Const.DEFAULT.EMPTY
            break
        }
      } else if (item.kaiteiKnj === Or28974Const.R3) {
        switch (selectedRowData.value.prtNo) {
          case Or28974Const.PRINT_NO_1:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.GENER_ALL_R34
            break
          case Or28974Const.PRINT_NO_2:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.BASIC_1_R34
            break
          case Or28974Const.PRINT_NO_3:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.BASIC_2_R34
            break
          case Or28974Const.PRINT_NO_4:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.BASIC_3_R34
            break
          case Or28974Const.PRINT_NO_5:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.BASIC_4_R34
            break
          case Or28974Const.PRINT_NO_6:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.BASIC_SPEC_R34
            break
          case Or28974Const.PRINT_NO_7:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.CERTIFIC_R34
            break
          case Or28974Const.PRINT_NO_8:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.FIRST_R34
            break
          case Or28974Const.PRINT_NO_9:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.SURVEY_R34
            break
          case Or28974Const.PRINT_NO_10:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.SPECIA_R34
            break
          case Or28974Const.PRINT_NO_11:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.YOKAIGODO_R34
            break
          case Or28974Const.PRINT_NO_12:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.LEVELOF_R34
            break
          case Or28974Const.PRINT_NO_13:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.INVESTIGA_R34
            break
          case Or28974Const.PRINT_NO_14:
            local.reportId = Or28974Const.PDF_DOWNLOAD_REPORT_ID.BASIC_5_R34
            break
          default:
            local.reportId = Or28974Const.DEFAULT.EMPTY
            break
        }
      }
    }
  })

  // 帳票出力
  await reportOutput(local.reportId, inputData, reportOutputType.DOWNLOAD)
  return
}

/**
 * エラーメッセージを表示する
 *
 * @param errorMsg - メッセージ内容
 */
function showOr21813Msg(errorMsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: errorMsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

// Or21813戻り値の確認
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.thirdBtnClickFlg) {
      return
    }
  }
)

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await close()
    }
  }
)
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row class="no-margin">
        <c-v-col
          cols="12"
          sm="2"
          class="np-border-right"
          style="padding: 8px !important"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="mo01334Oneway"
            class="list-wrapper"
          >
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="4"
          class="np-border-right"
          style="padding-left: 8px !important; padding-right: 8px !important"
        >
          <c-v-row class="has-border-bottom mt-0">
            <base-mo00045
              v-model="local.mo00045ModelTitle"
              :oneway-model-value="localOneway.mo00045Oneway"
            ></base-mo00045>
          </c-v-row>
          <c-v-row
            class="has-border-bottom no-pl"
            style="height: 134px"
          >
            <!-- 日付印刷区分 -->
            <base-mo00039
              v-model="local.mo00039DateModel"
              :oneway-model-value="localOneway.mo00039OnewayDate"
            >
            </base-mo00039>
            <!-- 指定日 -->
            <base-mo00020
              v-show="local.mo00039DateModel == Or28974Const.DATE_PRINT_CATEGORY_2"
              v-model="local.mo00020modelShitei"
              :oneway-model-value="mo00020OnewayNoArrow"
            >
            </base-mo00020>
          </c-v-row>
          <c-v-row
            v-show="
              selectedRowData != undefined && selectedRowData.prtNo != Or28974Const.PRINT_NO_11
            "
            class="mt-2"
            style="height: 42px"
          >
            <!-- 印刷オプションラベル -->
            <base-mo01338
              :oneway-model-value="{
                value: t('label.printer-option'),
                valueFontWeight: 'bolder',
                customClass: {
                  labelStyle: 'display: none',
                },
              }"
              style="width: 100%; background-color: #e2ecf5"
            >
            </base-mo01338>
          </c-v-row>
          <div
            v-show="
              selectedRowData != undefined &&
              ![
                Or28974Const.PRINT_NO_11,
                Or28974Const.PRINT_NO_12,
                Or28974Const.PRINT_NO_13,
              ].includes(selectedRowData.prtNo)
            "
          >
            <c-v-row
              class="no-pl"
              style="height: 100px"
            >
              <c-v-col>
                <!-- 記入用シートを印刷する -->
                <base-mo00018
                  v-model="local.mo00018ModelSheet"
                  :oneway-model-value="mo00018OnewaySheet"
                ></base-mo00018>
                <!-- サービス事業者名を印刷する -->
                <base-mo00018
                  v-model="local.mo00018ModelOfficeName"
                  :oneway-model-value="{
                    checkboxLabel: t('label.print-service-office-name'),
                    isVerticalLabel: false,
                  }"
                ></base-mo00018>
                <!-- 空白行をつめて印刷する -->
                <base-mo00018
                  v-model="local.mo00018ModelBlankRow"
                  :oneway-model-value="mo00018OnewayBlankRow"
                ></base-mo00018>
                <!-- 項目が頁をまたぐ場合は凡例を印刷しない -->
                <base-mo00018
                  v-model="local.mo00018ModelExample"
                  :oneway-model-value="mo00018OnewayExample"
                ></base-mo00018>

                <c-v-row
                  class="mt-1"
                  style="flex-direction: row"
                >
                  <!-- 特記事項9を印刷する -->
                  <base-mo00018
                    v-model="local.mo00018ModelMatter9"
                    :oneway-model-value="mo00018OnewayMatter9"
                    class="ml-1"
                  ></base-mo00018>
                  <!-- ※H18改訂版のみ有効 -->
                  <base-mo01379
                    :oneway-model-value="{
                      value: t('label.valid-h18-revision-only'),
                    }"
                    class="ml-2"
                  >
                  </base-mo01379>
                </c-v-row>
                <c-v-row
                  class="mt-1"
                  style="flex-direction: row"
                >
                  <!-- 前回結果を印刷する -->
                  <base-mo00018
                    v-model="local.mo00018ModelLastResult"
                    :oneway-model-value="mo00018OnewayLastResult"
                    class="ml-1"
                  ></base-mo00018>
                  <!-- ※H21改訂版以降有効 -->
                  <base-mo01379
                    :oneway-model-value="localOneway.mo01379OnewayH21"
                    class="ml-4"
                  >
                  </base-mo01379>
                </c-v-row>
                <c-v-row
                  class="mt-1"
                  style="flex-direction: row"
                >
                  <!-- 利用者名を印刷する -->
                  <base-mo00018
                    v-model="local.mo00018ModelUserName"
                    :oneway-model-value="mo00018OnewayUserName"
                    class="ml-1"
                  ></base-mo00018>
                  <!-- ※H21改訂版以降有効 -->
                  <base-mo01379
                    :oneway-model-value="localOneway.mo01379OnewayH21"
                    class="ml-4"
                  >
                  </base-mo01379>
                </c-v-row>
              </c-v-col>
            </c-v-row>
          </div>
          <c-v-row
            v-show="
              selectedRowData != undefined && selectedRowData.prtNo == Or28974Const.PRINT_NO_12
            "
            class="mt-2 ml-2"
            style="flex-direction: row; align-items: center"
          >
            <!-- 集計する要介護度ボタン -->
            <base-mo00611
              v-bind="{
                btnLabel: t('label.summary-of-level-of-care-required'),
              }"
              class="mr-4"
              @click="youkaigoBtnClick"
            ></base-mo00611>
            <!-- 集計する要介護度 -->
            <base-mo00040
              v-model="local.mo00040ModelKaigodo"
              :oneway-model-value="localOneway.mo00040OnewayCare"
            >
            </base-mo00040>
          </c-v-row>
          <c-v-row
            v-show="
              selectedRowData != undefined && selectedRowData.prtNo == Or28974Const.PRINT_NO_13
            "
            class="mt-2 ml-0"
            style="flex-direction: row; align-items: center"
          >
            <!-- 対象年月ラベル -->
            <base-mo00615
              :oneway-model-value="{
                itemLabel: t('label.subject-year-and-month'),
              }"
            >
            </base-mo00615>
            <!-- 対象年月前へアイコン -->
            <base-mo00009
              :oneway-model-value="localOneway.mo00009OnewayBack"
              @click="clickForwardOrBack(false)"
            />
            <!-- 対象年月テキストボックス -->
            <base-mo00020
              v-model="local.mo00020modelTaishou"
              :oneway-model-value="mo00020OnewayNoArrow"
            >
            </base-mo00020>
            <!-- 対象年月次へアイコン -->
            <base-mo00009
              :oneway-model-value="localOneway.mo00009OnewayForward"
              @click="clickForwardOrBack(true)"
            />
          </c-v-row>
        </c-v-col>
        <c-v-col
          v-show="
            selectedRowData != undefined &&
            ![
              Or28974Const.PRINT_NO_11,
              Or28974Const.PRINT_NO_12,
              Or28974Const.PRINT_NO_13,
            ].includes(selectedRowData.prtNo)
          "
          cols="12"
          sm="6"
          style="padding: 0px 8px 0px 0px"
        >
          <c-v-row
            class="ml-0"
            style="align-items: center"
          >
            <c-v-col cols="5">
              <!-- 利用者選択 -->
              <base-mo00039
                v-model="local.mo00039UserModel"
                :oneway-model-value="localOneway.mo00039OnewayUser"
                class="user-radio"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col>
              <!-- 基準日 -->
              <base-mo00020
                v-show="local.mo00039UserModel == Or28974Const.SELECT_CATEGORY_MULTIPLE"
                v-model="local.mo00020modelKijun"
                :oneway-model-value="mo00020OnewayKijun"
              >
              </base-mo00020>
              <!-- 履歴選択 -->
              <base-mo00039
                v-show="local.mo00039UserModel == Or28974Const.SELECT_CATEGORY_SINGLE"
                v-model="local.mo00039HistoryModel"
                :oneway-model-value="localOneway.mo00039OnewayHistory"
              >
              </base-mo00039>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="localOneway.or28974.processYmd != ''"
            class="ml-1"
            style="align-items: center"
          >
            <!-- 担当ケアマネプルダウン -->
            <g-custom-or-x-0145
              v-bind="orx0145"
              v-model="orX0145Type"
              :oneway-model-value="localOneway.orX0145Oneway"
              class="search-tack"
              style="display: flex"
            ></g-custom-or-x-0145>
          </c-v-row>
          <c-v-row
            class="no-margin"
            no-gutters
          >
            <c-v-col
              :cols="userCols"
              class="or28974_0130"
            >
              <g-custom-or-x-0130
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="local.mo00039UserModel == Or28974Const.SELECT_CATEGORY_SINGLE"
              cols="6"
              style="overflow-y: hidden"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0133
                v-bind="orX0133"
                :oneway-model-value="orX0133Oneway"
              ></g-custom-or-x-0133>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row
        no-gutters
        style="padding-right: 8px; padding-bottom: -8px"
      >
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 印刷ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          @click="printBtnClick"
        >
          <!--ツールチップ表示："PDFをダウンロードします"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.pdf-download')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- エラーダイアログを表示する。 -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  ></g-base-or21813>
  <!-- 印刷ログ画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrx0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
</template>
<style>
.or28974-nopadding-content {
  padding: 0px !important;
}
</style>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
:deep(.gray-label-style) {
  font-size: smaller;
}

:deep(.v-data-table__th) {
  min-width: 70px;
}

.has-border-bottom {
  margin-bottom: 8px;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
}

.no-margin {
  margin: 0px !important;
}

.np-border-right {
  padding: 0px !important;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

:deep(.no-pl .v-col) {
  padding-left: 0 !important;
}

:deep(td .v-row--no-gutters > .v-col) {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

:deep(.v-table--density-compact .row-height) {
  height: 31px !important;
}
:deep(.list-wrapper .v-table__wrapper) {
  height: 702px;
}
:deep(.search-tack) {
  margin-left: 0px;
  > div:last-child {
    .v-col {
      padding: 0 !important;
    }
  }
  .v-input__control {
    width: 200px;
  }
}

:deep(.user-radio) {
  .ma-1 {
    margin-left: 0px !important;
    .v-col {
      padding-left: 0px !important;
    }
  }
}

:deep(.or28974_0130) {
  .bg-w {
    .v-theme--mainTheme {
      padding: 0 !important;
    }
  }
  .list-wrapper {
    .v-table__wrapper {
      tbody {
        tr {
          td:first-child {
            .v-col {
              padding: 0 !important;
            }
          }
        }
      }
    }
  }
}
</style>
