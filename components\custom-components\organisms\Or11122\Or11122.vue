<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { cloneDeep } from 'lodash'
import type { SortableEvent } from 'vue-draggable-plus'
import { Or10004Const } from '../Or10004/Or10004.constants'
import type {
  Or11122OneWayType,
  Or11122StateType,
  Or11122TableData,
  Or11122TableThreeData,
  Or11122TableTwoData,
  Or11122Type,
} from './Or11122.type'
import { Or11122Const } from './Or11122.constants'
import { useScreenOneWayBind, useSetupChildProps, useValidation } from '#imports'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type {
  Mo01354Headers,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'

import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

/**
 * Or11122:((計画ﾓﾆﾀﾘﾝｸﾞ)表示順変更モニタリング記録表)タブ
 * Gui01230_表示順変更モニタリング記録表
 *
 * @description
 * ((計画ﾓﾆﾀﾘﾝｸﾞ)表示順変更モニタリング記録表)タブ
 *
 * <AUTHOR>
 */

/************************************************
 * Props
 ************************************************/

interface Props {
  uniqueCpId: string
  onewayModelValue: Or11122OneWayType
  modelValue: Or11122Type
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

const validation = useValidation()

//Or21814_有機体:確認ダイアログ
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })

const defaultOneway = reactive({
  fixedStyleDataPack: [
    {
      title: t('label.sort'),
      key: 'seq',
      sortable: false,
      minWidth: '88',
    },
    {
      title: t('label.display-order-modified-monitoring-record-table-1-header-1'),
      key: 'col1',
      sortable: false,
      minWidth: '20',
      required: false,
      children: [
        {
          title: t('label.display-order-modified-monitoring-record-table-1-header-2'),
          key: 'mokuhyoKnj',
          sortable: false,
          width: '186',
          required: false,
        },
        {
          title: t('label.display-order-modified-monitoring-record-table-1-header-3'),
          key: 'kikanKnj',
          sortable: false,
          width: '152',
          required: false,
        },
        {
          title: t('label.display-order-modified-monitoring-record-table-1-header-4'),
          key: 'jyoukyouKnj',
          sortable: false,
          width: '202',
          required: false,
        },
      ],
    },
    {
      title: t('label.display-order-modified-monitoring-record-table-1-header-5'),
      key: 'col2',
      sortable: false,
      minWidth: '20',
      children: [
        {
          title: t('label.display-order-modified-monitoring-record-table-1-header-6'),
          key: 'col2_1',
          sortable: false,
          minWidth: '20',
          children: [
            {
              title: t('label.display-order-modified-monitoring-record-table-1-header-7'),
              key: 'tantoHyoukaCd',
              sortable: false,
              width: '120',
            },
            {
              title: t('label.display-order-modified-monitoring-record-table-1-header-8'),
              key: 'tantoHyoukaKnj',
              sortable: false,
              width: '152',
            },
          ],
        },
        {
          title: t('label.display-order-modified-monitoring-record-table-1-header-9'),
          key: 'col2_2',
          sortable: false,
          minWidth: '20',
          children: [
            {
              title: t('label.display-order-modified-monitoring-record-table-1-header-7'),
              key: 'userHyoukaCd',
              sortable: false,
              width: '120',
            },
            {
              title: t('label.display-order-modified-monitoring-record-table-1-header-8'),
              key: 'userHyoukaKnj',
              sortable: false,
              width: '152',
            },
          ],
        },
        {
          title: t('label.display-order-modified-monitoring-record-table-1-header-10'),
          key: 'taiouCd',
          sortable: false,
          minWidth: '105',
        },
        {
          title: t('label.display-order-modified-monitoring-record-table-1-header-8'),
          key: 'taiouKnj',
          sortable: false,
          width: '152',
        },
      ],
    },
  ] as Mo01354Headers[],
  fixedStyleData: [
    {
      title: t('label.sort'),
      key: 'seq',
      sortable: false,
      minWidth: '20',
      width: '55',
    },
    {
      title: t('label.display-order-modified-monitoring-record-table-2-header-1'),
      key: 'kadaiKnj',
      sortable: false,
      minWidth: '20',
      required: false,
    },
    {
      title: t('label.display-order-modified-monitoring-record-table-2-header-2'),
      key: 'choukiKnj',
      sortable: false,
      minWidth: '20',
      required: false,
    },
    {
      title: t('label.display-order-modified-monitoring-record-table-2-header-3'),
      key: 'tankiKnj',
      sortable: false,
      minWidth: '20',
      required: false,
    },
    {
      title: t('label.display-order-modified-monitoring-record-table-2-header-4'),
      key: 'kaigoKnj',
      sortable: false,
      minWidth: '20',
      required: false,
    },
    {
      title: t('label.display-order-modified-monitoring-record-table-2-header-5'),
      key: 'col5',
      sortable: false,
      minWidth: '20',
      required: false,
      children: [
        {
          title: t('label.display-order-modified-monitoring-record-table-2-header-6'),
          key: 'kakuninKnj',
          sortable: false,
          minWidth: '20',
          required: false,
        },
        {
          title: t('label.display-order-modified-monitoring-record-table-2-header-7'),
          key: 'houhouKnj',
          sortable: false,
          minWidth: '20',
          required: false,
        },
        {
          title: t('label.display-order-modified-monitoring-record-table-2-header-8'),
          key: 'kakuninYmd',
          sortable: false,
          minWidth: '20',
          required: false,
        },
      ],
    },
    {
      title: t('label.display-order-modified-monitoring-record-table-2-header-9'),
      key: 'col6',
      sortable: false,
      minWidth: '20',
      required: false,
      children: [
        {
          title: t('label.display-order-modified-monitoring-record-table-2-header-10'),
          key: 'ikenHonKnj',
          sortable: false,
          minWidth: '20',
          required: false,
        },
        {
          title: t('label.display-order-modified-monitoring-record-table-2-header-11'),
          key: 'ikenKazKnj',
          sortable: false,
          minWidth: '20',
          required: false,
        },
      ],
    },
    {
      title: t('label.display-order-modified-monitoring-record-table-2-header-12'),
      key: 'col7',
      sortable: false,
      minWidth: '20',
      required: false,
      children: [
        {
          title: t('label.display-order-modified-monitoring-record-table-2-header-13'),
          key: 'jusokuKnj',
          sortable: false,
          minWidth: '20',
          required: false,
        },
      ],
    },
    {
      title: t('label.display-order-modified-monitoring-record-table-1-header-10'),
      key: 'col8',
      sortable: false,
      minWidth: '20',
      required: false,
      children: [
        {
          title: t('label.display-order-modified-monitoring-record-table-2-header-14'),
          key: 'taiouKnj',
          sortable: false,
          minWidth: '20',
          required: false,
        },
      ],
    },
  ] as Mo01354Headers[],
  customStyleData: [
    {
      title: t('label.sort'),
      key: 'seq',
      sortable: false,
      minWidth: '20',
    },
  ] as Mo01354Headers[],
  or11122: {
    list1: [],
    list2: [],
    list3: [],
  },
})

const tableData = ref<Or11122TableData[]>([])
const tableDataTwo = ref<Or11122TableTwoData[]>([])
const tableDataThree = ref<Or11122TableThreeData[]>([])

const local = reactive({
  or11122: {
    ...defaultOneway.or11122,
    ...props.modelValue,
  },
})

const localOneWay = reactive({
  or11122: {
    ...props.onewayModelValue,
  },
  mo01265OneWay: {
    btnLabel: t('label.display-order-delete'),
    customClass: {
      outerClass: 'pa-0',
    },
    prependIcon: 'delete',
    disabled: false,
  } as Mo01265OnewayType,
  // 表用数値専用テキストフィールド
  mo01278Oneway: {
    maxLength: '3',
    rules: [validation.integer],
    min: -999,
    max: 999,
  },
})

/**
 * 分子：表
 * 単方向バインドモデルのローカル変数
 */
const mo01354Oneway = ref<Mo01354OnewayType>({
  /** 初期値：ヘッダー情報 */
  headers: [],
  height: localOneWay.or11122.styleFlg === '1' ? '385px' : '313px',
  // Mo01354 表コンポーネントが提供する、デフォルトのヘッダーを利用しない（カスタマイズ可能になる）
  useDefaultHeader: false,
  // 行入替用のアイコンを表示
  showDragIndicatorFlg: localOneWay.or11122.tabFlg === '2',
  rowHeight: localOneWay.or11122.styleFlg === '1' ? '124px' : '85px',
})

const eventTarget = ref<SortableEvent>()

const fixedStyleData = ref<Mo01354Type>({
  values: {
    selectedRowId: '1',
    selectedRowIds: [],
    items: [],
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * ライフサイクルフック
 **************************************************/
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**
 * 「表示順テキストフィールド」クリック
 *
 * @param item - テーブルデータ
 */
function clickSort(item: Or11122TableData | Or11122TableTwoData | Or11122TableThreeData) {
  fixedStyleData.value.values.selectedRowId = item.id
  if (item.seq.value === '') {
    let retList: Or11122TableData[] | Or11122TableTwoData[] | Or11122TableThreeData[]
    if (localOneWay.or11122.styleFlg === '1') {
      retList = tableData.value
    } else if (localOneWay.or11122.styleFlg === '2') {
      retList = tableDataTwo.value
    } else {
      retList = tableDataThree.value
    }

    const notNullItems = retList.filter((item) => item.seq.value !== '')
    let maxValue = 0
    if (notNullItems.length > 0) {
      maxValue = Math.max(...notNullItems.map((i) => parseInt(i.seq.value)))
    }
    item.seq.value = maxValue + 1 + ''
  }
}

/**
 * 初期化
 */
const init = () => {
  if (localOneWay.or11122.styleFlg === '1') {
    mo01354Oneway.value.headers = defaultOneway.fixedStyleDataPack
  } else if (localOneWay.or11122.styleFlg === '2') {
    if (localOneWay.or11122.longTermFlg !== '1') {
      mo01354Oneway.value.headers = defaultOneway.fixedStyleData.filter(
        (item) => item.key !== 'choukiKnj'
      )
    } else {
      mo01354Oneway.value.headers = defaultOneway.fixedStyleData
    }
  } else {
    const newData = cloneDeep(defaultOneway.customStyleData)
    const columnCount = Number(localOneWay.or11122.seqList?.columnCount ?? '0')
    if (!columnCount) {
      return
    }
    localOneWay.or11122.seqList?.headList.forEach((item, index) => {
      if (index >= columnCount) {
        return
      }
      newData.push({
        title: item.nameKnj,
        key: 'col' + item.koumokuId,
        sortable: false,
        minWidth: '20',
      })
    })

    mo01354Oneway.value.headers = newData
  }
  if (!local.or11122.list1) {
    return
  }
  if (localOneWay.or11122.styleFlg === '1') {
    tableData.value = []
    local.or11122.list1.forEach((item) => {
      tableData.value.push({
        ...item,
        seq: { value: item.seq },
      })
    })
    fixedStyleData.value.values.items = tableData.value
  }
  if (!local.or11122.list2) {
    return
  }
  if (localOneWay.or11122.styleFlg === '2') {
    mo01354Oneway.value.height = '348px'
    tableDataTwo.value = []
    local.or11122.list2.forEach((item) => {
      tableDataTwo.value.push({
        ...item,
        seq: { value: item.seq },
      })
    })
    fixedStyleData.value.values.items = tableDataTwo.value
  }
  if (!local.or11122.list3) {
    return
  }
  if (localOneWay.or11122.styleFlg === '3') {
    tableDataThree.value = []
    local.or11122.list3.forEach((item) => {
      tableDataThree.value.push({
        ...item,
        seq: { value: item.seq },
      })
    })
    fixedStyleData.value.values.items = tableDataThree.value
  }
  if (localOneWay.or11122.tabFlg === '1') {
    fixedStyleData.value.values.selectedRowId = local.or11122.selectRowId
  } else {
    fixedStyleData.value.values.selectedRowId = '1'
  }
}

/**
 * ドラッグ終了後にダイアログを開いて移動するかどうかを確認する
 *
 * @param event - event
 */
const end = (event: SortableEvent) => {
  if (event.newIndex === event.oldIndex) {
    return
  }
  eventTarget.value = event
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10678', [event.oldIndex! + 1 + '', event.newIndex! + 1 + '']),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * ドラッグ終了時に表示順序の位置を変更する
 *
 * @param event - event
 */
const drop = (event: SortableEvent | undefined) => {
  if (!event) {
    return
  }
  let small
  let big
  if (event.oldIndex! < event.newIndex!) {
    small = event.oldIndex!
    big = event.newIndex!
  } else {
    small = event.newIndex!
    big = event.oldIndex!
  }
  if (localOneWay.or11122.styleFlg === '1') {
    const oldData = tableData.value[event.oldIndex!]
    tableData.value.splice(event.oldIndex!, 1)
    tableData.value.splice(event.newIndex!, 0, oldData)
    for (let index: number = small; index <= big; index++) {
      tableData.value[index].seq.value = index + 1 + ''
    }
  } else if (localOneWay.or11122.styleFlg === '2') {
    const oldData = tableDataTwo.value[event.oldIndex!]
    tableDataTwo.value.splice(event.oldIndex!, 1)
    tableDataTwo.value.splice(event.newIndex!, 0, oldData)
    for (let index: number = small; index <= big; index++) {
      tableDataTwo.value[index].seq.value = index + 1 + ''
    }
  } else {
    const oldData = tableDataThree.value[event.oldIndex!]
    tableDataThree.value.splice(event.oldIndex!, 1)
    tableDataThree.value.splice(event.newIndex!, 0, oldData)
    for (let index: number = small; index <= big; index++) {
      tableDataThree.value[index].seq.value = index + 1 + ''
    }
  }
  emitList(true)
}

/**
 * ドラッグのリセット
 *
 * @param event - event
 */
const reset = (event: SortableEvent | undefined) => {
  if (!event) {
    return
  }
  const newData = fixedStyleData.value.values.items[event.newIndex!]
  fixedStyleData.value.values.items.splice(event.newIndex!, 1)
  fixedStyleData.value.values.items.splice(event.oldIndex!, 0, newData)
}

/**
 * 「表示順位削除ボタン」押下
 */
const deleteRow = () => {
  tableData.value.forEach((item) => {
    item.seq.value = ''
  })
  tableDataTwo.value.forEach((item) => {
    item.seq.value = ''
  })
  tableDataThree.value.forEach((item) => {
    item.seq.value = ''
  })
}

/**
 * リストの内容を提出する
 *
 * @param type - タブの種類
 */
const emitList = (type: boolean) => {
  if (type) {
    if (localOneWay.or11122.styleFlg === Or10004Const.DEFAULT.FIXED_STYLE_PACK) {
      local.or11122.list1 = []
      tableData.value.forEach((item) => {
        local.or11122.list1.push({
          ...item,
          seq: item.seq.value,
        })
      })
      if (!local.or11122.list1.length) {
        return
      }
    }
    if (localOneWay.or11122.styleFlg === Or10004Const.DEFAULT.FIXED_STYLE) {
      local.or11122.list2 = []
      tableDataTwo.value.forEach((item) => {
        local.or11122.list2.push({
          ...item,
          seq: item.seq.value,
        })
      })
      if (!local.or11122.list2.length) {
        return
      }
    }
    if (localOneWay.or11122.styleFlg === Or10004Const.DEFAULT.CUSTOM_STYLE) {
      local.or11122.list3 = []
      tableDataThree.value.forEach((item) => {
        local.or11122.list3.push({
          ...item,
          seq: item.seq.value,
        })
      })
      if (!local.or11122.list3.length) {
        return
      }
    }
    local.or11122.selectRowId = fixedStyleData.value.values.selectedRowId
  } else {
    if (localOneWay.or11122.styleFlg === Or10004Const.DEFAULT.FIXED_STYLE_PACK) {
      if (!local.or11122.list1.length) {
        return
      }
      local.or11122.list1.forEach((item, index) => {
        item.seq = tableData.value[index].seq.value
      })
    } else if (localOneWay.or11122.styleFlg === Or10004Const.DEFAULT.FIXED_STYLE) {
      if (!local.or11122.list2.length) {
        return
      }
      local.or11122.list2.forEach((item, index) => {
        item.seq = tableDataTwo.value[index].seq.value
      })
    } else {
      if (!local.or11122.list3.length) {
        return
      }
      local.or11122.list3.forEach((item, index) => {
        item.seq = tableDataThree.value[index].seq.value
      })
    }
  }
  emit('update:modelValue', local.or11122)
}

/**
 * マスターコンテンツの設定
 *
 * @param rendouKbn - 連動区分
 *
 * @param kbnCd - 区分コード
 */
const setMasterText = (rendouKbn: string, kbnCd: string) => {
  const seqList = localOneWay.or11122.seqList!
  let text = ''
  switch (rendouKbn) {
    case Or11122Const.DEFAULT.EXECUTE_CONFIRM_MASTER:
      text = seqList.jikouList.find((item) => item.kbnCd === kbnCd)!.textKnj
      break
    case Or11122Const.DEFAULT.CONFIRM_METHOD_MASTER:
      text = seqList.kakuninList.find((item) => item.kbnCd === kbnCd)!.textKnj
      break
    case Or11122Const.DEFAULT.IKEN_MASTER:
      text = seqList.ikenList.find((item) => item.kbnCd === kbnCd)!.textKnj
      break
    case Or11122Const.DEFAULT.JUSOKUDO_MASTER:
      text = seqList.jusokuList.find((item) => item.kbnCd === kbnCd)!.textKnj
      break
    case Or11122Const.DEFAULT.TAIOU_MASTER:
      text = seqList.taiouList.find((item) => item.kbnCd === kbnCd)!.textKnj
      break
    default:
      break
  }
  return text
}

/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or11122StateType>({
  cpId:
    localOneWay.or11122.tabFlg === Or10004Const.DEFAULT.TAB_ID_1
      ? Or11122Const.CP_ID(0)
      : Or11122Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      localOneWay.or11122.seqList = value!.seqList
      local.or11122.list1 = props.modelValue.list1
      local.or11122.list2 = props.modelValue.list2
      local.or11122.list3 = props.modelValue.list3
      local.or11122.selectRowId = props.modelValue.selectRowId
      switch (value?.executeFlag) {
        // データ再取得
        case 'getData':
          void init()
          break
        default:
          break
      }
    },
  },
})

/**
 * Mo01354のデータを監視
 */
watch(
  () => fixedStyleData.value.values.items,
  () => {
    if (localOneWay.or11122.tabFlg === Or10004Const.DEFAULT.TAB_ID_1) {
      emitList(false)
    } else {
      emitList(true)
    }
  },
  { deep: true }
)

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      drop(eventTarget.value)
      return
    }

    reset(eventTarget.value)
  }
)
</script>

<template>
  <div class="pa-2">
    <c-v-col class="pa-0 pb-2 h-44">
      <!-- 削除ボタン -->
      <base-mo01265
        v-if="localOneWay.or11122.tabFlg === Or10004Const.DEFAULT.TAB_ID_1"
        v-bind="localOneWay.mo01265OneWay"
        @click.stop="deleteRow"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="t('tooltip.display-order-delete-success')"
        />
      </base-mo01265>
    </c-v-col>
    <!-- 固定様式（パッケージプラン） -->
    <c-v-row
      v-if="localOneWay.or11122.styleFlg === Or10004Const.DEFAULT.FIXED_STYLE_PACK"
      no-gutters
      class="table-header w-100"
    >
      <!-- 分子：表 -->
      <base-mo-01354
        v-model="fixedStyleData"
        :oneway-model-value="mo01354Oneway"
        class="list-wrapper w-100"
        @end="end"
      >
        <!-- 表示順 -->
        <template #[`item.seq`]="{ item }">
          <div
            v-if="localOneWay.or11122.tabFlg === Or10004Const.DEFAULT.TAB_ID_1"
            class="h-100 sort w-54"
          >
            <base-mo01278
              v-model="item.seq"
              :oneway-model-value="localOneWay.mo01278Oneway"
              @click.stop="clickSort(item)"
            />
          </div>
          <c-v-col
            v-else
            class="d-flex justify-end align-center number h-100 w-54 bold"
            >{{ item.seq.value }}</c-v-col
          >
        </template>
        <!-- 目標 -->
        <template #[`item.mokuhyoKnj`]="{ item }">
          <c-v-col class="d-flex align-start ellipsis h-100 w-160">
            <span class="six">{{ item.mokuhyoKnj }}</span>
            <c-v-tooltip
              v-if="item.mokuhyoKnj"
              activator="parent"
              location="bottom"
              :max-width="300"
              :text="item.col1_1"
              open-delay="200"
          /></c-v-col>
        </template>
        <!-- 対象期間 -->
        <template #[`item.kikanKnj`]="{ item }">
          <c-v-col class="white-space d-flex align-start h-100 w-120">{{ item.kikanKnj }}</c-v-col>
        </template>
        <!-- サービスの実施状況） -->
        <template #[`item.jyoukyouKnj`]="{ item }">
          <c-v-col class="d-flex align-start ellipsis h-100 w-170">
            <span class="six">{{ item.jyoukyouKnj }}</span>
            <c-v-tooltip
              v-if="item.jyoukyouKnj"
              activator="parent"
              location="bottom"
              :max-width="300"
              :text="item.jyoukyouKnj"
              open-delay="200"
          /></c-v-col>
        </template>
        <!-- 目標達成状況 -->
        <template #[`item.tantoHyoukaCd`]="{ item }">
          <c-v-col class="d-flex align-start w-54 h-100">{{ item.tantoHyoukaCd }}</c-v-col>
        </template>
        <!-- その理由 -->
        <template #[`item.tantoHyoukaKnj`]="{ item }">
          <c-v-col class="d-flex align-start ellipsis h-100 w-120">
            <span class="six">{{ item.tantoHyoukaKnj }}</span>
            <c-v-tooltip
              v-if="item.tantoHyoukaKnj"
              activator="parent"
              location="bottom"
              :max-width="300"
              :text="item.tantoHyoukaKnj"
              open-delay="200"
          /></c-v-col>
        </template>
        <!-- 目標達成状況 -->
        <template #[`item.userHyoukaCd`]="{ item }">
          <c-v-col class="d-flex align-start w-54 h-100">{{ item.userHyoukaCd }}</c-v-col>
        </template>
        <!-- その理由 -->
        <template #[`item.userHyoukaKnj`]="{ item }">
          <c-v-col class="d-flex align-start ellipsis h-100 w-120">
            <span class="six">{{ item.userHyoukaKnj }}</span>
            <c-v-tooltip
              v-if="item.userHyoukaKnj"
              activator="parent"
              location="bottom"
              :max-width="300"
              :text="item.userHyoukaKnj"
              open-delay="200"
          /></c-v-col>
        </template>
        <!-- 今後の対応 -->
        <template #[`item.taiouCd`]="{ item }">
          <c-v-col class="d-flex align-start w-54 h-100">{{ item.taiouCd }}</c-v-col>
        </template>
        <!-- その理由 -->
        <template #[`item.taiouKnj`]="{ item }">
          <c-v-col class="d-flex align-start ellipsis w-120 h-124">
            <span class="six">{{ item.taiouKnj }}</span>
            <c-v-tooltip
              v-if="item.taiouKnj"
              activator="parent"
              location="bottom"
              :max-width="300"
              :text="item.taiouKnj"
              open-delay="200"
          /></c-v-col>
        </template>
        <!-- ページングを非表示 -->
        <template #bottom />
      </base-mo-01354>
    </c-v-row>
    <!-- 固定様式（パッケージプラン以外) -->
    <c-v-row
      v-if="localOneWay.or11122.styleFlg === Or10004Const.DEFAULT.FIXED_STYLE"
      no-gutters
      class="table-header"
    >
      <!-- 分子：表 -->
      <base-mo-01354
        v-model="fixedStyleData"
        :oneway-model-value="mo01354Oneway"
        class="list-wrapper w-100"
        @end="end"
      >
        <!-- 表示順 -->
        <template #[`item.seq`]="{ item }">
          <div
            v-if="localOneWay.or11122.tabFlg === Or10004Const.DEFAULT.TAB_ID_1"
            class="h-100 sort w-54"
          >
            <base-mo01278
              v-model="item.seq"
              :oneway-model-value="localOneWay.mo01278Oneway"
              @click.stop="clickSort(item)"
            />
          </div>
          <c-v-col
            v-else
            class="d-flex justify-end align-center h-100 number bold w-54"
            >{{ item.seq.value }}</c-v-col
          >
        </template>
        <!-- 生活全般的解决すべき課題 -->
        <template #[`item.kadaiKnj`]="{ item }">
          <div
            class="d-flex h-100"
            style="padding: 0 !important"
          >
            <div
              v-if="localOneWay.or11122.noFlg === Or11122Const.DEFAULT.NO_FLG"
              class="d-flex justify-end align-center br-1 number w-30_1"
            >
              {{ item.kadaiNo }}
            </div>
            <c-v-col class="d-flex w-170 ellipsis">
              <span class="four">{{ item.kadaiKnj }}</span>
              <c-v-tooltip
                v-if="item.kadaiKnj"
                activator="parent"
                location="bottom"
                :max-width="300"
                :text="item.kadaiKnj"
                open-delay="200"
            /></c-v-col>
          </div>
        </template>
        <!-- 長期目標 -->
        <template #[`item.choukiKnj`]="{ item }">
          <c-v-col class="d-flex h-100 align-start ellipsis w-135">
            <span class="four">{{ item.choukiKnj }}</span>
            <c-v-tooltip
              v-if="item.choukiKnj"
              activator="parent"
              location="bottom"
              :max-width="300"
              :text="item.choukiKnj"
              open-delay="200"
          /></c-v-col>
        </template>
        <!-- 短期目標 -->
        <template #[`item.tankiKnj`]="{ item }">
          <c-v-col class="d-flex h-100 align-start ellipsis w-135">
            <span class="four">{{ item.tankiKnj }}</span>
            <c-v-tooltip
              v-if="item.tankiKnj"
              activator="parent"
              location="bottom"
              :max-width="300"
              :text="item.tankiKnj"
              open-delay="200"
          /></c-v-col>
        </template>
        <!-- サービスの実施状況） -->
        <template #[`item.kaigoKnj`]="{ item }">
          <div
            class="d-flex h-100"
            style="padding: 0 !important"
          >
            <div
              v-if="localOneWay.or11122.noFlg === Or11122Const.DEFAULT.NO_FLG"
              class="d-flex justify-end align-center number br-1 w-30_1"
            >
              {{ item.servNo }}
            </div>
            <c-v-col class="d-flex ellipsis w-135">
              <span class="four">{{ item.kaigoKnj }}</span>
              <c-v-tooltip
                v-if="item.kaigoKnj"
                activator="parent"
                location="bottom"
                :max-width="300"
                :text="item.kaigoKnj"
                open-delay="200"
            /></c-v-col>
          </div>
        </template>
        <!-- 実行確認 -->
        <template #[`item.kakuninKnj`]="{ item }">
          <div class="h-100 w-135">
            <c-v-col class="bt-1 h-50 d-flex align-start w-100">{{
              localOneWay.or11122.seqList?.jikouList.filter(
                (sub) => sub.kbnCd === item.kakuninCd
              )[0]?.textKnj
            }}</c-v-col>
            <c-v-col class="d-flex align-start w-100 ellipsis">
              <span class="two">{{ item.kakuninKnj }}</span>
              <c-v-tooltip
                v-if="item.kakuninKnj"
                activator="parent"
                location="bottom"
                :max-width="300"
                :text="item.kakuninKnj"
                open-delay="200"
            /></c-v-col>
          </div>
        </template>
        <!-- 確認方法 -->
        <template #[`item.houhouKnj`]="{ item }">
          <div class="h-100 w-135">
            <c-v-col class="bt-1 h-50 d-flex align-start w-100">{{
              localOneWay.or11122.seqList?.kakuninList.filter(
                (sub) => sub.kbnCd === item.houhouCd
              )[0]?.textKnj
            }}</c-v-col>
            <c-v-col class="h-50 d-flex align-start w-100 ellipsis"
              ><span class="two">{{ item.houhouKnj }}</span>
              <c-v-tooltip
                v-if="item.houhouKnj"
                activator="parent"
                location="bottom"
                :max-width="300"
                :text="item.houhouKnj"
                open-delay="200"
            /></c-v-col>
          </div>
        </template>
        <!-- 確認期日 -->
        <template #[`item.kakuninYmd`]="{ item }">
          <c-v-col class="d-flex h-100 align-start w-85">{{ item.kakuninYmd }}</c-v-col>
        </template>
        <!-- 本人 -->
        <template #[`item.ikenHonKnj`]="{ item }">
          <div class="h-100 w-135">
            <c-v-col class="bt-1 h-50 d-flex align-start w-100">{{
              localOneWay.or11122.seqList?.ikenList.filter((sub) => sub.kbnCd === item.ikenHonCd)[0]
                ?.textKnj
            }}</c-v-col>
            <c-v-col class="h-50 d-flex align-start w-100 ellipsis">
              <span class="two">{{ item.ikenHonKnj }}</span>
              <c-v-tooltip
                v-if="item.ikenHonKnj"
                activator="parent"
                location="bottom"
                :max-width="300"
                :text="item.ikenHonKnj"
                open-delay="200"
            /></c-v-col>
          </div>
        </template>
        <!-- 家族 -->
        <template #[`item.ikenKazKnj`]="{ item }">
          <div class="h-100 w-135">
            <c-v-col class="bt-1 h-50 d-flex align-start w-100">{{
              localOneWay.or11122.seqList?.ikenList.filter((sub) => sub.kbnCd === item.ikenKazCd)[0]
                ?.textKnj
            }}</c-v-col>
            <c-v-col class="h-50 d-flex align-start w-100 ellipsis">
              <span class="two">{{ item.ikenKazKnj }}</span>
              <c-v-tooltip
                v-if="item.ikenKazKnj"
                activator="parent"
                location="bottom"
                :max-width="300"
                :text="item.ikenKazKnj"
                open-delay="200"
            /></c-v-col>
          </div>
        </template>
        <!-- 充足度 -->
        <template #[`item.jusokuKnj`]="{ item }">
          <div class="h-100 w-170">
            <c-v-col class="bt-1 h-50 d-flex align-start w-100">{{
              localOneWay.or11122.seqList?.jusokuList.filter(
                (sub) => sub.kbnCd === item.jusokuCd
              )[0]?.textKnj
            }}</c-v-col>
            <c-v-col class="h-50 d-flex align-start w-100 ellipsis"
              ><span class="two">{{ item.jusokuKnj }}</span>
              <c-v-tooltip
                v-if="item.jusokuKnj"
                activator="parent"
                location="bottom"
                :max-width="300"
                :text="item.jusokuKnj"
                open-delay="200"
            /></c-v-col>
          </div>
        </template>
        <!-- 対応 -->
        <template #[`item.taiouKnj`]="{ item }">
          <div class="h-100 w-170">
            <c-v-col class="bt-1 h-50 d-flex align-start w-100">{{
              localOneWay.or11122.seqList?.taiouList.filter((sub) => sub.kbnCd === item.taiouCd)[0]
                ?.textKnj
            }}</c-v-col>
            <c-v-col class="h-50 d-flex align-start w-100 ellipsis">
              <span class="two">{{ item.taiouKnj }}</span>
              <c-v-tooltip
                v-if="item.taiouKnj"
                activator="parent"
                location="bottom"
                :max-width="300"
                :text="item.taiouKnj"
                open-delay="200"
            /></c-v-col>
          </div>
        </template>
        <!-- ページングを非表示 -->
        <template #bottom />
      </base-mo-01354>
    </c-v-row>
    <!-- カスタマイズ様式 -->
    <c-v-row
      v-if="localOneWay.or11122.styleFlg === Or10004Const.DEFAULT.CUSTOM_STYLE"
      no-gutters
      class="table-header"
    >
      <!-- 分子：表 -->
      <base-mo-01354
        v-model="fixedStyleData"
        :oneway-model-value="mo01354Oneway"
        class="list-wrapper w-100"
        @end="end"
      >
        <!-- 表示順 -->
        <template #[`item.seq`]="{ item }">
          <div
            v-if="localOneWay.or11122.tabFlg === Or10004Const.DEFAULT.TAB_ID_1"
            class="h-100 sort w-54"
          >
            <base-mo01278
              v-model="item.seq"
              :oneway-model-value="localOneWay.mo01278Oneway"
              @click.stop="clickSort(item)"
            />
          </div>

          <c-v-col
            v-else
            class="d-flex justify-end align-center h-100 number bold w-54"
            >{{ item.seq.value }}</c-v-col
          >
        </template>
        <template
          v-for="column in localOneWay.or11122.seqList?.headList"
          #[`item.col${column.koumokuId}`]="{ item }"
          :key="'col' + column.koumokuId"
        >
          <div
            :style="{
              width: `calc(${Number(column.widthCnt) * 2}ch + 36px)`,
            }"
          >
            <div
              v-if="column.inputKbn === '1'"
              class="d-flex h-85 align-start ellipsis w-100"
            >
              <span class="four white-space limited-width-text">{{
                item['col' + column.koumokuId]?.koumokuKnj
              }}</span>
              <c-v-tooltip
                v-if="item['col' + column.koumokuId]?.koumokuKnj"
                activator="parent"
                location="bottom"
                :max-width="300"
                :text="item['col' + column.koumokuId]?.koumokuKnj"
                open-delay="200"
              />
            </div>
            <div
              v-if="column.inputKbn === '2'"
              class="d-flex h-85"
            >
              <div class="text-right number w-100 ellipsis">
                <span class="four white-space limited-width-text">
                  {{ item['col' + column.koumokuId]?.koumokuCod }}
                </span>
                <c-v-tooltip
                  v-if="item['col' + column.koumokuId]?.koumokuCod"
                  activator="parent"
                  location="bottom"
                  :max-width="300"
                  :text="item['col' + column.koumokuId]?.koumokuCod"
                  open-delay="200"
                />
              </div>
            </div>
            <div
              v-if="column.inputKbn === '3'"
              class="d-flex h-85 align-start ellipsis"
            >
              <span class="four white-space limited-width-text">{{
                setMasterText(column.rendouKbn, item['col' + column.koumokuId]?.koumokuCod)
              }}</span>
              <c-v-tooltip
                v-if="setMasterText(column.rendouKbn, item['col' + column.koumokuId]?.koumokuCod)"
                activator="parent"
                location="bottom"
                :max-width="300"
                :text="setMasterText(column.rendouKbn, item['col' + column.koumokuId]?.koumokuCod)"
                open-delay="200"
              />
            </div>
            <div
              v-if="column.inputKbn === '4'"
              class="h-85"
            >
              <c-v-col class="bt-1 h-50 d-flex align-start w-100 ellipsis">
                <span class="two white-space limited-width-text">{{
                  setMasterText(column.rendouKbn, item['col' + column.koumokuId]?.koumokuCod)
                }}</span>
                <c-v-tooltip
                  v-if="setMasterText(column.rendouKbn, item['col' + column.koumokuId]?.koumokuCod)"
                  activator="parent"
                  location="bottom"
                  :max-width="300"
                  :text="
                    setMasterText(column.rendouKbn, item['col' + column.koumokuId]?.koumokuCod)
                  "
                  open-delay="200"
                />
              </c-v-col>
              <c-v-col class="h-50 d-flex align-start w-100 ellipsis">
                <span class="two white-space limited-width-text">
                  {{ item['col' + column.koumokuId]?.koumokuKnj }}</span
                >
                <c-v-tooltip
                  v-if="item['col' + column.koumokuId]?.koumokuKnj"
                  activator="parent"
                  location="bottom"
                  :max-width="300"
                  :text="item['col' + column.koumokuId]?.koumokuKnj"
                  open-delay="200"
              /></c-v-col>
            </div>
            <div
              v-if="column.inputKbn === '5'"
              class="h-85 ellipsis"
            >
              <c-v-col class="d-flex h-100 align-start">
                <span class="one white-space limited-width-text">{{
                  item['col' + column.koumokuId]?.koumokuYmd
                }}</span>
                <c-v-tooltip
                  v-if="item['col' + column.koumokuId]?.koumokuYmd"
                  activator="parent"
                  location="bottom"
                  :max-width="300"
                  :text="item['col' + column.koumokuId]?.koumokuYmd"
                  open-delay="200"
                />
              </c-v-col>
            </div>
          </div>
        </template>
        <!-- ページングを非表示 -->
        <template #bottom />
      </base-mo-01354>
    </c-v-row>
  </div>

  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';
.bt-1 {
  border-bottom: 1px solid rgb(var(--v-theme-black-200));
}
.br-1 {
  border-right: 1px solid rgb(var(--v-theme-black-200));
}

.table-header :deep(tr:nth-child(3) th:last-child) {
  border-right-width: 1px !important;
  border-right: 1px solid rgb(var(--v-theme-black-200)) !important;
}

.bold {
  font-weight: bold;
}

:deep(th, td) {
  white-space: pre;
}
:deep(.v-table__wrapper) {
  width: 100%;
  overflow-x: scroll !important;
}
.h-85 {
  height: 85px;
}
.sort {
  margin-top: 2px !important;
  margin-left: 1px !important;
}

:deep(.d-flex) {
  padding: 0 16px !important;
}
:deep(.v-row > .v-col) {
  width: 52px;
  display: flex;
  justify-content: center;
}
:deep(.v-data-table .v-table__wrapper > table tbody > tr > td.v-data-table-column--no-padding) {
  padding: 0 16px !important;
}

:deep(.full-width-field) {
  padding: 0 16px !important;
  width: 100%;
  text-align: right;
  font-weight: bold !important;
}

.w-54 {
  width: 86px;
}
.w-160 {
  width: 185px;
}
.w-120 {
  width: 152px;
}
.w-170 {
  width: 202px;
}
.w-30_1 {
  width: 62px;
}
.w-135 {
  width: 135px;
}
.w-85 {
  width: 117px;
}
.h-124 {
  height: 124px;
}
.h-44 {
  height: 44px;
}

.white-space {
  white-space: pre;
}

.ellipsis {
  .six {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 6;
    line-clamp: 6;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .four {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .two {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .one {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.limited-width-text {
  word-break: break-all;
  white-space: normal;
}
</style>
