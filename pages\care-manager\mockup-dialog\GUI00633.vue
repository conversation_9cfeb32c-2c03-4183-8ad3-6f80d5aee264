<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  watch,
} from '#imports'

import { Or10859Const } from '~/components/custom-components/organisms/Or10859/Or10859.constants'
import { Or10859Logic } from '~/components/custom-components/organisms/Or10859/Or10859.logic'
import type { Or10859Type, Or10859OnewayType } from '~/types/cmn/business/components/Or10859Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00633'
// ルーティング
const routing = 'GUI00633/pinia'
// 画面物理名
const screenName = 'GUI00633'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or10859 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00633' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or10859.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00633',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or10859Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or10859Const.CP_ID(1)]: or10859.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or10859Logic.initialize(or10859.value.uniqueCpId)
}

const or10859Type = ref<Or10859Type>({
  sortList: [],
})

// ダイアログ表示フラグ
const showDialogOr10859 = computed(() => {
  // Or10859のダイアログ開閉状態
  return Or10859Logic.state.get(or10859.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or10859)
 *
 */
function onClickOr10859() {
  // Or10859のダイアログ開閉状態を更新する
  Or10859Logic.state.set({
    uniqueCpId: or10859.value.uniqueCpId,
    state: { isOpen: true },
  })
}

watch(or10859Type, () => {
  console.log(or10859Type.value)
})

/**
 * 親画面からの初期値
 */
const or10859Data: Or10859OnewayType = {
  sortList: [
    {
      lifeIssues: '最近、移動動作が緩慢になって尿失禁が起こり始めたが、それに対応した機・練が行われておらず状態が悪化する可能性がある1111111111111111111111111111111111111111111111111111111111111111111。',
      longTermGoal: '活動範囲が広がり入院することがなく在宅生活が送れる。',
      shortTermGoal: '現状に満足されているご様子で、用具を利用して積極的にリハビリを行われています。',

    },
    {
      lifeIssues: '最近、移動動作が緩慢になって尿失禁が起こり始めたが、それに対応した機・練が行われておらず状態が悪化する可能性がある。',
      longTermGoal:'活動範囲が広がり入院することがなく在宅生活が送れる。',
      shortTermGoal: '現状に満足されているご様子で、用具を利用して積極的にリハビリを行われています。',
    },
    {
      lifeIssues: '最近、移動動作が緩慢になって尿失禁が起こり始めたが、それに対応した機・練が行われておらず状態が悪化する可能性がある。',
      longTermGoal: '活動範囲が広がり入院することがなく在宅生活が送れる。',
      shortTermGoal: '現状に満足されているご様子で、用具を利用して積極的にリハビリを行われています。',
    },
    {
      lifeIssues: 'もっと歩けるようになりたい。',
      longTermGoal: '身体の動きがよくなり在宅生活が続けられる。	',
      shortTermGoal: '日中は手引きにてトイレまで歩いていけるようになる。',
    },
     {
      lifeIssues: '体調を整え、入院?入所はしたくない。',
      longTermGoal: '体調が整い、在宅生活が継続できる。',
      shortTermGoal: '体調の変化に早期に発見できる。',
    },
  ],
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr10859"
        >GUI00633_表示順変更アセスメント
      </v-btn>
      <g-custom-or-10859
        v-if="showDialogOr10859"
        v-bind="or10859"
        v-model="or10859Type"
        :oneway-model-value="or10859Data"
      />
    </c-v-col>
  </c-v-row>
</template>
