<script setup lang="ts">
/**
 * Or17591:有機体:[優先順位]画面
 * GUI00908_[優先順位]画面
 *
 * @description
 * [優先順位]画面
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or17591Logic } from '~/components/custom-components/organisms/Or17591/Or17591.logic'
import { Or17591Const } from '~/components/custom-components/organisms/Or17591/Or17591.constants'
import type { Or17591OnewayType, Or17591Type } from '~/types/cmn/business/components/Or17591Type'
import type { PriorityOrderType } from '~/components/custom-components/organisms/Or17591/Or17591.type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00908'
// ルーティング
const routing = 'GUI00908/pinia'
// 画面物理名
const screenName = 'GUI00908'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or17591 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00908' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or17591Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or17591.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00908',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or17591Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or17591Const.CP_ID(1)]: or17591.value,
})

/**************************************************
 * Props
 **************************************************/

// ダイアログ表示フラグ
const showDialogOr17591 = computed(() => {
  // Or28824のダイアログ開閉状態
  return Or17591Logic.state.get(or17591.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or17591Data: Or17591OnewayType = {
  // 事业所ID
  svJigyoId: '1',
}

const or17591Type = ref<Or17591Type>({
  // 課題立案履歴ID
  assId: '4',
})

/**
 *  ボタン押下時の処理
 */
function or17591OnClick() {
  // Or17591のダイアログ開閉状態を更新する
  Or17591Logic.state.set({
    uniqueCpId: or17591.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function getOr17591Data(or17591Data: PriorityOrderType) {
  console.log(or17591Data)
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP GUI00908_[優先順位]画面 CD 2025/05/23 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or17591OnClick()"
        >GUI00908_[優先順位]画面
      </v-btn>
      <g-custom-or-17591
        v-if="showDialogOr17591"
        v-bind="or17591"
        v-model="or17591Type"
        :oneway-model-value="or17591Data"
        @update:model-value="getOr17591Data"
      />
    </c-v-col>
  </c-v-row>
  <!-- POP GUI00908_[優先順位]画面 CD 2025/05/23 ADD END-->
</template>
