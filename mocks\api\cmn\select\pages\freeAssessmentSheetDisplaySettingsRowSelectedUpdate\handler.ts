/**
 * Or54352:有機体:フリーアセスメントフェースシート表示設定マスタ
 * GUI00898_フリーアセスメントフェースシート表示設定マスタ
 *
 * @description
 * フリーアセスメントフェースシート表示設定マスタ
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { FreeAssessmentSheetDisplaySettingsRowSelectedUpdateInEntity } from '~/repositories/cmn/entities/FreeAssessmentSheetDisplaySettingsTabChangedUpdateEntity'

/**
 *  ［フリーアセスメントフェースシート表示設定マスタ］画面
 *
 * @description
 *  ［フリーアセスメントフェースシート表示設定マスタ］画面初期情報データを返却する。
 * dataName："freeAssessmentSheetDisplaySettingsRowSelectedUpdate"
 */
export function handler(inEntity: FreeAssessmentSheetDisplaySettingsRowSelectedUpdateInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
