import type { RyoikiMediumBunruiListType } from '~/repositories/cmn/entities/AssessmentPackageplanEntity'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00045Type } from '~/types/business/components/Mo00045Type'

/**
 * TeX0009:GUI00816_アセスメント(パッケージプラン)
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface TeX0009StateType {
  /**
   * 改定フラグ
   */
  ninteiFormF?: string
  /**
   * 選択中タブ
   */
  activeTabId?: string
  /**
   * 事業所ID
   */
  jigyoId?: string
  /**
   * 計画対象期間ID
   */
  sc1Id?: string
  /**
   * 履歴ID
   */
  recId?: string
  /**
   * アセスメントID
   */
  gdlId?: string
  /**
   * 作成者ID
   */
  createUserId?: string
  /**
   * 作成日
   */
  createYmd?: string
  /**
   * 課題と目標リスト
   */
  issuesAndGoalsList?: {
    /** id */
    id?: string
    /** アセスメントID */
    gdlId?: string
    /** 計画期間ID */
    sc1Id?: string
    /** アセスメント番号 */
    assNo?: string
    /** 課題 */
    kadaiKnj?: string
    /** 長期 */
    choukiKnj?: string
    /** 短期 */
    tankiKnj?: string
    /** 連番 */
    seq?: number
    /** 更新区分 */
    updateKbn?: string
  }[]
}

/**
 * TeX0009：有機体：画面メニューエリア
 * EventStatus領域に保持するデータ構造
 */
export interface TeX0009EventType {
  /** 再表示発火フラグ */
  isRefresh?: boolean

  /** 作成日変更フラグ */
  isCreateDateChanged?: boolean

  /** お気に入りイベント発火フラグ */
  favoriteEventFlg?: boolean

  /** 保存イベント発火フラグ */
  saveEventFlg?: boolean

  /** 新規イベント発火フラグ */
  createEventFlg?: boolean

  /** 複写イベント発火フラグ */
  copyEventFlg?: boolean

  /** 印刷イベント発火フラグ */
  printEventFlg?: boolean

  /** マスタ他イベント発火フラグ */
  masterEventFlg?: boolean

  /** 削除イベント発火フラグ */
  deleteEventFlg?: boolean
}

/**
 * 共通情報
 */
export interface CommonInfoType {
  /**
   * 計画期間ID
   */
  sc1Id?: string
  /**
   * 法人ID
   */
  houjinId?: string
  /**
   * 種別ID
   */
  syubetsuId?: string
  /**
   * 施設ID
   */
  shisetuId?: string
  /**
   * 事業者ID
   */
  svJigyoId?: string
  /**
   * 利用者ID
   */
  userId?: string
  /**
   * 作成日
   */
  createYmd?: string
  /**
   * 作成者
   */
  shokuId?: string
  /**
   * アセスメント履歴ＩＤ
   */
  assId?: string
  /**
   * アセスメントカスタマイズID
   */
  cstId?: string
}

/**
 * 分類TABS情報リスト
 */
export interface ClassificationTabsListType {
  /**
   * v-tab:分類区分
   */
  id: string
  /**
   * 分類略称
   */
  title: string
  /**
   * 領域区分
   */
  fieldId: string
  /**
   * 中分類情報リスト
   */
  projectsList: OrX0106ComponentsListType[]
}

/**
 * サブアセンブリ
 */
export interface OrX0106Component {
  /**
   * uniqueCpId
   */
  uniqueCpId: string
}

/**
 * アセスメント中分類-カスタムコンポーネント
 *
 */
export interface CustomComponentsType {
  /** コンポーネントタイプ*/
  type: string
  /** 物理名*/
  name: string
  /** テキスト*/
  label: string
  /** データ値*/
  value: Mo00018Type | Mo00045Type
}

/**
 * アセスメント中分類情報リスト
 */
export interface OrX0106ComponentsListType {
  /** 分類項目名 */
  mainTitle: string
  /** 分類内容 */
  content: RyoikiMediumBunruiListType[]
  /** 	行の数 */
  rowHeightSpeed: number
}
