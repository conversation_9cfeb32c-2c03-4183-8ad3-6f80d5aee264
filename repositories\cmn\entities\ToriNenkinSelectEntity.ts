/**
 * GUI00644_困難度マスタ
 *
 * <AUTHOR> DAO DINH DUONG
 */
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * 認証情報入力エンティティ
 */
export interface ToriNenkinInfoInputInEntity extends InWebEntity {
  /**
   * 利用者ID
   */
  userId: string
}

/**
 * 認証情報入力エンティティ
 */
export interface ToriNenkinOutEntity extends OutWebEntity {
  /**
   * 調査票年金情報のデータを格納するオブジェクト。
   * - `nenkinTList`: 年金情報の一覧（ToriNenkin 型の配列）
   * - その他必要なプロパティがあればここに追加
   */
  data: {
    /**
     * 困難度マスタリスト
     */
    nenkinTList: ToriNenkin[]
  }
}

/**
 * 利用者管理タブ
 */
export interface ToriNenkin {
  /**
   * 交付年月日
   */
  getYmd: string
  /**
   * 金額
   */
  kingaku: string
  /**
   * 手帳番号
   */
  tBango: string
  /**
   * 手帳種別名称
   */
  tKindKnj: string
  /**
   * 受給月１月
   */
  tuki1Flg: string
  /**
   * 受給月2月
   */
  tuki2Flg: string
  /**
   * 受給月3月
   */
  tuki3Flg: string
  /**
   * 受給月4月
   */
  tuki4Flg: string
  /**
   * 受給月5月
   */
  tuki5Flg: string
  /**
   * 受給月6月
   */
  tuki6Flg: string
  /**
   * 受給月7月
   */
  tuki7Flg: string
  /**
   * 受給月8月
   */
  tuki8Flg: string
  /**
   * 受給月9月
   */
  tuki9Flg: string
  /**
   * 受給月１0月
   */
  tuki10Flg: string
  /**
   * 受給月11月
   */
  tuki11Flg: string
  /**
   * 受給月12月
   */
  tuki12Flg: string
}
