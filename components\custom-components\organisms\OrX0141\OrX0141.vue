<script setup lang="ts">
/**
 * OrX0141Logic:有機体:主治医意見書②コンテンツタブ
 * GUI01286_主治医意見書
 *
 * @description
 * 主治医意見書コンテンツタブ
 *
 * <AUTHOR>
 */
import { reactive, onMounted, watch, computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or29757Logic } from '../Or29757/Or29757.logic'
import { Or29758Const } from '../Or29758/Or29758.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { useSetupChildProps, useSystemCommonsStore } from '#imports'
import type { OrX0141Type } from '~/types/cmn/business/components/OrX0141Type'
import type { AttendingPhysicianStatementRirekiComInfo } from '~/repositories/cmn/entities/AttendingPhysicianStatementSelectEntity'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
}
const props = defineProps<Props>()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * 変数定義
 **************************************************/
const local = reactive({
  orX0141: {
    // ＩＤ
    iks3Id: '',
    // 利き腕
    kikiudeFlg: '',
    // 身長
    height: {
      mo00045: { value: '' },
    },
    // 体重
    weight: {
      mo00045: { value: '' },
    },
    // 体重の変化
    weightFlg: '',
    // 四肢欠損選択フラグ
    sin1Flg: {
      modelValue: false,
    },
    // 四肢欠損部位の名前
    sin1BuiKnj: {
      value: '',
    },
    // 麻痺選択フラグ
    sin2Flg: {
      modelValue: false,
    },
    // 右上肢選択フラグ
    sin2R1Flg: {
      modelValue: false,
    },
    // 右上肢程度選択フラッグ
    sin2R1TeidoFlg: '',
    // 左上肢選択フラグ
    sin2L1Flg: {
      modelValue: false,
    },
    // 左上肢程度選択フラッグ
    sin2L1TeidoFlg: '',
    // 右下肢選択フラグ
    sin2R2Flg: {
      modelValue: false,
    },
    // 右下肢程度選択フラッグ
    sin2R2TeidoFlg: '',
    // 左下肢選択フラグ
    sin2L2Flg: {
      modelValue: false,
    },
    // 左下肢程度選択フラッグ
    sin2L2TeidoFlg: '',
    // その他選択フラッグ
    sin2ElseFlg: {
      modelValue: false,
    },
    // その他部位の名前
    sin2ElseBuiKnj: { value: '' },
    // その他程度選択フラッグ
    sin2ElseTeidoFlg: '',
    // 筋力の低下選択フラグ
    sin3Flg: {
      modelValue: false,
    },
    // 筋力の低下部位の名前
    sin3BuiKnj: { value: '' },
    // 筋力の低下程度選択フラッグ
    sin3TeidoFlg: '',
    // 関節の拘縮選択フラグ
    sin4Flg: {
      modelValue: false,
    },
    // 関節の拘縮部位の名前
    sin4BuiKnj: { value: '' },
    // 関節の拘縮程度選択フラッグ
    sin4TeidoFlg: '',
    // 関節の痛み選択フラグ
    sin5Flg: {
      modelValue: false,
    },
    // 関節の痛み部位の名前
    sin5BuiKnj: { value: '' },
    // 関節の痛み程度選択フラッグ
    sin5TeidoFlg: '',
    // 失調不随意運動選択フラグ
    sin6Flg: {
      modelValue: false,
    },
    // 失調不随意運動右上肢フラグ
    sin6JosiRightFlg: {
      modelValue: false,
    },
    // 失調不随意運動左上肢フラグ
    sin6JosiLeftFlg: {
      modelValue: false,
    },
    // 失調不随意運動右下肢フラグ
    sin6KasiRightFlg: {
      modelValue: false,
    },
    // 失調不随意運動左下肢フラグ
    sin6KasiLeftFlg: {
      modelValue: false,
    },
    // 失調不随意運動右体幹フラグ
    sin6TaikanRightFlg: {
      modelValue: false,
    },
    // 失調不随意運動左体幹フラグ
    sin6TaikanLeftFlg: {
      modelValue: false,
    },
    // 褥瘡選択フラグ
    sin7Flg: {
      modelValue: false,
    },
    // 褥瘡部位の名前
    sin7BuiKnj: { value: '' },
    // 褥瘡程度選択フラッグ
    sin7TeidoFlg: '',
    // その他皮膚疾患選択フラグ
    sin8Flg: {
      modelValue: false,
    },
    // その他皮膚疾患部位の名前
    sin8BuiKnj: { value: '' },
    // その他皮膚疾患程度選択フラッグ
    sin8TeidoFlg: '',
    // 屋外歩行選択フラッグ
    idou1Flg: '',
    // 車いすの使用選択フラッグ
    idou2Flg: '',
    // 歩行補助具・装具の使用選択フラッグ1
    idou3Flg1: {
      modelValue: false,
    },
    // 歩行補助具・装具の使用選択フラッグ2
    idou3Flg2: {
      modelValue: false,
    },
    // 歩行補助具・装具の使用選択フラッグ3
    idou3Flg3: {
      modelValue: false,
    },
    // 食事行為選択フラッグ
    eiyo1Flg: '',
    // 現在の栄養状態選択フラッグ
    eiyoFlg: '',
    // 留意点の内容
    eiyoPointKnj: { value: '' },
    // 可能の状態1
    jotai1Flg: {
      modelValue: false,
    },
    // 可能の状態2
    jotai2Flg: {
      modelValue: false,
    },
    // 可能の状態3
    jotai3Flg: {
      modelValue: false,
    },
    // 可能の状態4
    jotai4Flg: {
      modelValue: false,
    },
    // 可能の状態5
    jotai5Flg: {
      modelValue: false,
    },
    // 可能の状態6
    jotai6Flg: {
      modelValue: false,
    },
    // 可能の状態7
    jotai7Flg: {
      modelValue: false,
    },
    // 可能の状態8
    jotai8Flg: {
      modelValue: false,
    },
    // 可能の状態9
    jotai9Flg: {
      modelValue: false,
    },
    // 可能の状態10
    jotai10Flg: {
      modelValue: false,
    },
    // 可能の状態11
    jotai11Flg: {
      modelValue: false,
    },
    // 可能の状態12
    jotai12Flg: {
      modelValue: false,
    },
    // 可能の状態13
    jotai13Flg: {
      modelValue: false,
    },
    // 可能の状態14
    jotai14Flg: {
      modelValue: false,
    },
    // 可能の状態14その他の内容
    jotai14Knj: { value: '' },
    // 方針の内容
    housinKnj: { value: '' },
    // 生活機能の維持･改善の見通し選択フラッグ
    kitaiFlg: '',
    // 医学的管理の必要性1
    kanri1Flg: {
      modelValue: false,
    },
    // 医学的管理の必要性1（必要性が特に高い）
    kanri1TokFlg: {
      modelValue: false,
    },
    // 医学的管理の必要性2
    kanri2Flg: {
      modelValue: false,
    },
    // 医学的管理の必要性2（必要性が特に高い）
    kanri2TokFlg: {
      modelValue: false,
    },
    // 医学的管理の必要性3
    kanri3Flg: {
      modelValue: false,
    },
    // 医学的管理の必要性3（必要性が特に高い）
    kanri3TokFlg: {
      modelValue: false,
    },
    // 医学的管理の必要性4
    kanri4Flg: {
      modelValue: false,
    },
    // 医学的管理の必要性4（必要性が特に高い）
    kanri4TokFlg: {
      modelValue: false,
    },
    // 医学的管理の必要性5
    kanri5Flg: {
      modelValue: false,
    },
    // 医学的管理の必要性5（必要性が特に高い）
    kanri5TokFlg: {
      modelValue: false,
    },
    // 医学的管理の必要性6
    kanri6Flg: {
      modelValue: false,
    },
    // 医学的管理の必要性6（必要性が特に高い）
    kanri6TokFlg: {
      modelValue: false,
    },
    // 医学的管理の必要性7
    kanri7Flg: {
      modelValue: false,
    },
    // 医学的管理の必要性7（必要性が特に高い）
    kanri7TokFlg: {
      modelValue: false,
    },
    // 医学的管理の必要性8
    kanri8Flg: {
      modelValue: false,
    },
    // 医学的管理の必要性8（必要性が特に高い）
    kanri8TokFlg: {
      modelValue: false,
    },
    // 医学的管理の必要性9
    kanri9Flg: {
      modelValue: false,
    },
    // 医学的管理の必要性9（必要性が特に高い）
    kanri9TokFlg: {
      modelValue: false,
    },
    // 医学的管理の必要性10
    kanri10Flg: {
      modelValue: false,
    },
    // 医学的管理の必要性10（必要性が特に高い）
    kanri10TokFlg: {
      modelValue: false,
    },
    // 医学的管理の必要性11
    kanri11Flg: {
      modelValue: false,
    },
    // 医学的管理の必要性11（必要性が特に高い）
    kanri11TokFlg: {
      modelValue: false,
    },
    // 医学的管理の必要性12
    kanri12Flg: {
      modelValue: false,
    },
    // 医学的管理の必要性12（必要性が特に高い）
    kanri12TokFlg: {
      modelValue: false,
    },
    // 医学的管理の必要性13
    kanri13Flg: {
      modelValue: false,
    },
    // 医学的管理の必要性13（必要性が特に高い）
    kanri13TokFlg: {
      modelValue: false,
    },
    // 医学的管理の必要性14
    kanri14Flg: {
      modelValue: false,
    },
    // その他の医療系サービスの内容
    kanri10Knj: { value: '' },
    // 留意事項1フラッグ
    ryui1Flg: {
      modelValue: false,
    },
    // 留意事項1の内容
    ryui1Knj: { value: '' },
    // 留意事項2フラッグ
    ryui2Flg: {
      modelValue: false,
    },
    // 留意事項2の内容
    ryui2Knj: { value: '' },
    // 留意事項3フラッグ
    ryui3Flg: {
      modelValue: false,
    },
    // 留意事項3の内容
    ryui3Knj: { value: '' },
    // 留意事項4フラッグ
    ryui4Flg: {
      modelValue: false,
    },
    // 留意事項4の内容
    ryui4Knj: { value: '' },
    // 留意事項5フラッグ
    ryui5Flg: {
      modelValue: false,
    },
    // 留意事項5の内容
    ryui5Knj: { value: '' },
    // 留意事項6フラッグ
    ryui6Flg: {
      modelValue: false,
    },
    // 留意事項6その他の内容
    ryui6Knj: { value: '' },
    // 留意事項7フラッグ
    ryui7Flg: {
      modelValue: false,
    },
    // 感染症の有無のフラッグ
    kansenFlg: '',
    // 感染の内容
    kansenKnj: { value: '' },
    // 特記すべき事項
    ikenKnj: { value: '' },
    // 更新回数
    modifiedCnt: '',
  } as OrX0141Type,
  rirekiObj: {} as AttendingPhysicianStatementRirekiComInfo,
  disabled: false,
})
const localOneway = reactive({
  mo01299Oneway1: {
    title: t('label.section-title-11'),
  },
  mo00615Oneway1: {
    itemLabel: t('label.dominant-hand'),
  } as Mo00615OnewayType,
  mo00039Oneway1: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    customClass: new CustomClass({ outerStyle: 'display:flex; align-items:center' }),
    disabled: false,
  } as Mo00039OnewayType,
  mo00615Oneway2: {
    itemLabel: t('label.stature'),
    customClass: new CustomClass({ outerClass: 'ml-4' }),
  } as Mo00615OnewayType,
  mo00038Oneway1: {
    mo00045Oneway: {
      width: '80',
      showItemLabel: false,
      hideDetails: true,
      customClass: new CustomClass({ outerClass: 'ml-2 mr-2' }),
      disabled: false,
    } as Mo00045OnewayType,
    periodSortDigit: 4,
    min: 0,
    max: 99999,
  } as Mo00038OnewayType,
  mo00615Oneway3: {
    itemLabel: t('label.cm'),
  } as Mo00615OnewayType,
  mo00615Oneway4: {
    itemLabel: t('label.body-weight'),
    customClass: new CustomClass({ outerClass: 'ml-4' }),
  } as Mo00615OnewayType,
  mo00615Oneway5: {
    itemLabel: t('label.kg'),
  } as Mo00615OnewayType,
  mo00615Oneway6: {
    itemLabel: t('label.weight-change'),
  } as Mo00615OnewayType,
  mo00039Oneway2: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    customClass: new CustomClass({ outerStyle: 'display:flex; align-items:center' }),
    disabled: false,
  } as Mo00039OnewayType,
  mo00018Oneway1: {
    showItemLabel: false,
    checkboxLabel: t('label.limb-deficiency'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00615Oneway7: {
    itemLabel: t('label.part'),
  } as Mo00615OnewayType,
  mo00045Oneway1: {
    itemLabel: '',
    width: '200px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0 mr-2' }),
    maxLength: '20',
    disabled: false,
  } as Mo00045OnewayType,
  mo00018Oneway2: {
    showItemLabel: false,
    checkboxLabel: t('label.paralysis'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00615Oneway8: {
    itemLabel: t('label.degree'),
  } as Mo00615OnewayType,
  mo00018Oneway3: {
    showItemLabel: false,
    checkboxLabel: t('label.right-upper-limb'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00039Oneway3: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    disabled: false,
  } as Mo00039OnewayType,
  mo00018Oneway4: {
    showItemLabel: false,
    checkboxLabel: t('label.left-upper-limb'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway5: {
    showItemLabel: false,
    checkboxLabel: t('label.right-lower-limb'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway6: {
    showItemLabel: false,
    checkboxLabel: t('label.left-lower-limb'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway7: {
    showItemLabel: false,
    checkboxLabel: t('label.other-label'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway8: {
    showItemLabel: false,
    checkboxLabel: t('label.muscle-weakness'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway9: {
    showItemLabel: false,
    checkboxLabel: t('label.joint-contracture'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway10: {
    showItemLabel: false,
    checkboxLabel: t('label.joint-pain'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway11: {
    showItemLabel: false,
    checkboxLabel: t('label.involuntary-movement'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00615Oneway9: {
    itemLabel: t('label.upper-limb'),
  } as Mo00615OnewayType,
  mo00615Oneway10: {
    itemLabel: t('label.lower-limb'),
  } as Mo00615OnewayType,
  mo00615Oneway11: {
    itemLabel: t('label.trunk'),
  } as Mo00615OnewayType,
  mo00018Oneway12: {
    showItemLabel: false,
    checkboxLabel: t('label.radio-right'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway13: {
    showItemLabel: false,
    checkboxLabel: t('label.radio-left'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway14: {
    showItemLabel: false,
    checkboxLabel: t('label.bedsore'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway15: {
    showItemLabel: false,
    checkboxLabel: t('label.other-skin-disorders'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00615Oneway12: {
    itemLabel: t('label.section-title-12'),
    itemLabelFontWeight: 'blod',
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'font-size:16px',
    }),
  } as Mo00615OnewayType,
  mo01299Oneway2: {
    title: t('label.section-title-13'),
  },
  mo00615Oneway13: {
    itemLabel: t('label.outdoor-walking'),
    itemLabelCustomClass: new CustomClass({
      labelClass: 'ml-4',
    }),
  } as Mo00615OnewayType,
  mo00039Oneway4: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    disabled: false,
  } as Mo00039OnewayType,
  mo00615Oneway14: {
    itemLabel: t('label.wheelchair-use'),
    itemLabelCustomClass: new CustomClass({
      labelClass: 'ml-4',
    }),
  } as Mo00615OnewayType,
  mo00039Oneway5: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    disabled: false,
  } as Mo00039OnewayType,
  mo00615Oneway15: {
    itemLabel: t('label.walking-prosthetics'),
    itemLabelCustomClass: new CustomClass({
      labelClass: 'ml-4',
    }),
  } as Mo00615OnewayType,
  mo00018Oneway16: {
    showItemLabel: false,
    checkboxLabel: t('label.assessment-home-6-7-ido31'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway17: {
    showItemLabel: false,
    checkboxLabel: t('label.outdoor-use'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway18: {
    showItemLabel: false,
    checkboxLabel: t('label.indoor-use'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo01299Oneway3: {
    title: t('label.section-title-14'),
  },
  mo00615Oneway16: {
    itemLabel: t('label.meal-behavior'),
    itemLabelCustomClass: new CustomClass({
      labelClass: 'ml-4',
    }),
  } as Mo00615OnewayType,
  mo00039Oneway6: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    disabled: false,
  } as Mo00039OnewayType,
  mo00615Oneway17: {
    itemLabel: t('label.current-nutritional-status'),
    itemLabelCustomClass: new CustomClass({
      labelClass: 'ml-4',
    }),
  } as Mo00615OnewayType,
  mo00039Oneway7: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    disabled: false,
  } as Mo00039OnewayType,
  mo00615Oneway18: {
    itemLabel: t('label.nutritional-and-dietary-considerations'),
    itemLabelCustomClass: new CustomClass({
      labelClass: 'ml-4',
    }),
  } as Mo00615OnewayType,
  mo00045Oneway2: {
    itemLabel: '',
    width: '400px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0' }),
    maxLength: '60',
    disabled: false,
  } as Mo00045OnewayType,
  mo01299Oneway4: {
    title: t('label.section-title-15'),
  },
  mo00018Oneway19: {
    showItemLabel: false,
    checkboxLabel: t('label.urinary-incontinence'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway20: {
    showItemLabel: false,
    checkboxLabel: t('label.invert-fracture'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway21: {
    showItemLabel: false,
    checkboxLabel: t('label.move-ability-decline'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway22: {
    showItemLabel: false,
    checkboxLabel: t('label.bedsore'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway23: {
    showItemLabel: false,
    checkboxLabel: t('label.cardiopulmonary-function-decline'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway24: {
    showItemLabel: false,
    checkboxLabel: t('label.confinement'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway25: {
    showItemLabel: false,
    checkboxLabel: t('label.assessment-home-6-7-sickShu14'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway26: {
    showItemLabel: false,
    checkboxLabel: t('label.loitering'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway27: {
    showItemLabel: false,
    checkboxLabel: t('label.undernutrition'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway28: {
    showItemLabel: false,
    checkboxLabel: t('label.feeding-swallowing-function-decline'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway29: {
    showItemLabel: false,
    checkboxLabel: t('label.dehydration'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway30: {
    showItemLabel: false,
    checkboxLabel: t('label.susceptibility'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway31: {
    showItemLabel: false,
    checkboxLabel: t('label.cancer-pain'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway32: {
    showItemLabel: false,
    checkboxLabel: t('label.other'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00045Oneway3: {
    itemLabel: '',
    width: '200px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0' }),
    maxLength: '22',
    disabled: false,
  } as Mo00045OnewayType,
  mo00615Oneway19: {
    itemLabel: t('label.response-policy'),
    itemLabelCustomClass: new CustomClass({
      labelClass: 'ml-4',
    }),
  } as Mo00615OnewayType,
  mo00045Oneway4: {
    itemLabel: '',
    width: '600px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0' }),
    maxLength: '60',
    disabled: false,
  } as Mo00045OnewayType,
  mo01299Oneway5: {
    title: t('label.section-title-16'),
  },
  mo00039Oneway8: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    disabled: false,
  } as Mo00039OnewayType,
  mo01299Oneway6: {
    title: t('label.section-title-17'),
  },
  mo00615Oneway20: {
    itemLabel: t('label.home-visit-diagnosis'),
  } as Mo00615OnewayType,
  mo00018Oneway33: {
    showItemLabel: false,
    checkboxLabel: t('label.presence'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway34: {
    showItemLabel: false,
    checkboxLabel: t('label.heigh'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00615Oneway21: {
    itemLabel: t('label.home-visit-with-nursing'),
  } as Mo00615OnewayType,
  mo00615Oneway22: {
    itemLabel: t('label.home-nursing-support'),
  } as Mo00615OnewayType,
  mo00615Oneway23: {
    itemLabel: t('label.home-visit-dentistry-diagnosis'),
  } as Mo00615OnewayType,
  mo00615Oneway24: {
    itemLabel: t('label.home-visit-medicine-management-guidance'),
  } as Mo00615OnewayType,
  mo00615Oneway25: {
    itemLabel: t('label.home-visit-with-rehabilitation'),
  } as Mo00615OnewayType,
  mo00615Oneway26: {
    itemLabel: t('label.short-term-admission-recuperation-nursingCare'),
  } as Mo00615OnewayType,
  mo00615Oneway27: {
    itemLabel: t('label.home-visit-dentistry-hygiene-guidance'),
  } as Mo00615OnewayType,
  mo00615Oneway28: {
    itemLabel: t('label.home-visit-nutrition-meal-guidance'),
  } as Mo00615OnewayType,
  mo00615Oneway29: {
    itemLabel: t('label.dayservice-rehabilitation'),
  } as Mo00615OnewayType,
  mo00615Oneway30: {
    itemLabel: t('label.other-healthcare-services'),
  } as Mo00615OnewayType,
  mo00045Oneway5: {
    itemLabel: '',
    width: '200px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0 ml-4' }),
    maxLength: '30',
    disabled: false,
  } as Mo00045OnewayType,
  mo00615Oneway31: {
    itemLabel: t('label.elderly-health-facility'),
  } as Mo00615OnewayType,
  mo00615Oneway32: {
    itemLabel: t('label.nursing-care-hospital'),
  } as Mo00615OnewayType,
  mo00018Oneway35: {
    showItemLabel: false,
    checkboxLabel: t('label.special-note-item-absence'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo01299Oneway7: {
    title: '',
  },
  mo00615Oneway33: {
    itemLabel: t('label.blood-pressure'),
  } as Mo00615OnewayType,
  mo00039Oneway9: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    disabled: false,
  } as Mo00039OnewayType,
  mo00045Oneway6: {
    itemLabel: '',
    width: '250px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0 mr-4' }),
    maxLength: '30',
    disabled: false,
  } as Mo00045OnewayType,
  mo00615Oneway34: {
    itemLabel: t('label.mobility'),
  } as Mo00615OnewayType,
  mo00615Oneway35: {
    itemLabel: t('label.feeding'),
  } as Mo00615OnewayType,
  mo00615Oneway36: {
    itemLabel: t('label.exercise'),
  } as Mo00615OnewayType,
  mo00615Oneway37: {
    itemLabel: t('label.swallowing'),
  } as Mo00615OnewayType,
  mo00615Oneway38: {
    itemLabel: t('label.other-label'),
  } as Mo00615OnewayType,
  mo00045Oneway7: {
    itemLabel: '',
    width: '400px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0 mr-4' }),
    maxLength: '46',
    disabled: false,
  } as Mo00045OnewayType,
  mo00018Oneway36: {
    showItemLabel: false,
    checkboxLabel: t('label.blood-pressure'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway37: {
    showItemLabel: false,
    checkboxLabel: t('label.mobility'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway38: {
    showItemLabel: false,
    checkboxLabel: t('label.feeding'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway39: {
    showItemLabel: false,
    checkboxLabel: t('label.exercise'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway40: {
    showItemLabel: false,
    checkboxLabel: t('label.swallowing'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway41: {
    showItemLabel: false,
    checkboxLabel: t('label.other-label'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00018Oneway42: {
    showItemLabel: false,
    checkboxLabel: t('label.special-note-item-absence'),
    hideDetails: true,
    disabled: false,
  } as Mo00018OnewayType,
  mo00046Oneway1: {
    name: '',
    itemLabel: '',
    rows: 3,
    hideDetails: true,
    maxlength: '52',
    disabled: false,
  } as Mo00046OnewayType,
  mo01299Oneway8: {
    title: t('label.section-title-19'),
  },
  mo00039Oneway10: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    disabled: false,
  } as Mo00039OnewayType,
  mo00045Oneway8: {
    itemLabel: '',
    width: '500px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0 mr-4' }),
    maxLength: '60',
    disabled: false,
  } as Mo00045OnewayType,
  mo01299Oneway9: {
    title: t('label.section-title-20'),
  },
  mo00615Oneway39: {
    itemLabel: '',
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'font-size:16px; font-weight: bold',
    }),
  } as Mo00615OnewayType,
  mo00615Oneway40: {
    itemLabel: t('label.notable-matters'),
  } as Mo00615OnewayType,
  mo00046Oneway2: {
    name: '',
    itemLabel: '',
    rows: 10,
    hideDetails: true,
    maxlength: '1400',
    style: 'width: 1000px',
    disabled: false,
  } as Mo00046OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 入力支援［ケアマネ］
  or51775Oneway: {
    title: '特記すべき事項',
    t1Cd: '1500',
    t2Cd: '2',
    t3Cd: '0',
    tableName: 'cpn_tuc_ikensho3',
    columnName: 'iken_knj',
    inputContents: '',
    userId: '',
  } as Or51775OnewayType,
})
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(1) })
/**************************************************
 * Pinia
 **************************************************/
// const { refValue } = useScreenTwoWayBind<Ikensho2Obj>({
//   cpId: OrX0141Const.CP_ID(1),
//   uniqueCpId: props.uniqueCpId,
// })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(0)]: or51775.value,
})

onMounted(async () => {
  await initCodes()
  local.orX0141 = Or29757Logic.data.get(props.parentUniqueCpId)!.ikensho3Obj
  local.rirekiObj = Or29757Logic.data.get(props.parentUniqueCpId)!.rirekiObj
  local.disabled = !!Or29757Logic.state.get(props.parentUniqueCpId)!.disabled
  initTabData()
  changeInputDisabled()
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 利き腕
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_KIKIUDE_TYPE },
    // 体重の変化
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_WEIGHT_CHANGE_TYPE },
    // 程度
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DEGREE_TYPE },
    // 屋外歩行
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_OUTDOORWALKING },
    // 車いすの使用
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_WHEELCHAIRUSE },
    // 食事行為
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEALBEHAVIOR },
    // 現在の栄養状態
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CURRENTNUTRITIONALSTATUS },
    // 生活機能の見通し
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_LIFEFUNCTIONOUTLOOK },
    // 留意事項
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SHOWCONTENTITEMSOPINION },
    // 感染症の有無
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_INFECTION_STATUS_TYPE },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // コード取得
  // 利き腕
  localOneway.mo00039Oneway1.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_KIKIUDE_TYPE
  )
  // 体重の変化
  localOneway.mo00039Oneway2.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_WEIGHT_CHANGE_TYPE
  )
  // 程度
  localOneway.mo00039Oneway3.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DEGREE_TYPE
  )
  // 屋外歩行
  localOneway.mo00039Oneway4.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_OUTDOORWALKING
  )
  // 車いすの使用
  localOneway.mo00039Oneway5.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_WHEELCHAIRUSE
  )
  // 食事行為
  localOneway.mo00039Oneway6.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_MEALBEHAVIOR
  )
  // 現在の栄養状態
  localOneway.mo00039Oneway7.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CURRENTNUTRITIONALSTATUS
  )
  // 生活機能の見通し
  localOneway.mo00039Oneway8.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_LIFEFUNCTIONOUTLOOK
  )
  // 留意事項
  localOneway.mo00039Oneway9.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SHOWCONTENTITEMSOPINION
  )
  // 感染症の有無
  localOneway.mo00039Oneway10.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_INFECTION_STATUS_TYPE
  )
}
/**
 * TwoWayBind領域データ
 */
watch(
  () => Or29757Logic.data.get(props.parentUniqueCpId),
  (newValue) => {
    local.rirekiObj = newValue!.rirekiObj
    local.orX0141 = newValue!.ikensho3Obj
    initTabData()
  }
)
/**
 * 麻痺選択フラグ
 */
watch(
  () => local.orX0141.sin2Flg.modelValue,
  (newValue) => {
    if (!newValue) {
      local.orX0141.sin2R1Flg.modelValue = false
      local.orX0141.sin2R1TeidoFlg = ''
      local.orX0141.sin2L1Flg.modelValue = false
      local.orX0141.sin2L1TeidoFlg = ''
      local.orX0141.sin2R2Flg.modelValue = false
      local.orX0141.sin2R2TeidoFlg = ''
      local.orX0141.sin2L2Flg.modelValue = false
      local.orX0141.sin2L2TeidoFlg = ''
      local.orX0141.sin2ElseFlg.modelValue = false
      local.orX0141.sin2ElseTeidoFlg = ''
    }
  }
)

/**
 * 右上肢程度選択フラッグ
 */
watch(
  () => local.orX0141.sin2R1TeidoFlg,
  (newValue) => {
    if (!!newValue && !local.orX0141.sin2R1Flg.modelValue) {
      local.orX0141.sin2R1Flg.modelValue = true
      sinFlgChange()
    }
  }
)

/**
 * 左上肢程度選択フラッグ
 */
watch(
  () => local.orX0141.sin2L1TeidoFlg,
  (newValue) => {
    if (!!newValue && !local.orX0141.sin2L1Flg.modelValue) {
      local.orX0141.sin2L1Flg.modelValue = true
      sinFlgChange()
    }
  }
)

/**
 * 右下肢程度選択フラッグ
 */
watch(
  () => local.orX0141.sin2R2TeidoFlg,
  (newValue) => {
    if (!!newValue && !local.orX0141.sin2R2Flg.modelValue) {
      local.orX0141.sin2R2Flg.modelValue = true
      sinFlgChange()
    }
  }
)

/**
 * 左下肢程度選択フラッグ
 */
watch(
  () => local.orX0141.sin2L2TeidoFlg,
  (newValue) => {
    if (!!newValue && !local.orX0141.sin2L2Flg.modelValue) {
      local.orX0141.sin2L2Flg.modelValue = true
      sinFlgChange()
    }
  }
)

/**
 * その他程度選択フラッグ
 */
watch(
  () => local.orX0141.sin2ElseTeidoFlg,
  (newValue) => {
    if (!!newValue && !local.orX0141.sin2ElseFlg.modelValue) {
      local.orX0141.sin2ElseFlg.modelValue = true
      sinFlgChange()
    }
  }
)

/**
 * 筋力の低下程度選択フラッグ
 */
watch(
  () => local.orX0141.sin3TeidoFlg,
  (newValue) => {
    if (!!newValue && !local.orX0141.sin3Flg.modelValue) {
      local.orX0141.sin3Flg.modelValue = true
    }
  }
)

/**
 * 関節の拘縮程度選択フラッグ
 */
watch(
  () => local.orX0141.sin4TeidoFlg,
  (newValue) => {
    if (!!newValue && !local.orX0141.sin4Flg.modelValue) {
      local.orX0141.sin4Flg.modelValue = true
    }
  }
)

/**
 * 関節の痛み程度選択フラッグ
 */
watch(
  () => local.orX0141.sin5TeidoFlg,
  (newValue) => {
    if (!!newValue && !local.orX0141.sin5Flg.modelValue) {
      local.orX0141.sin5Flg.modelValue = true
    }
  }
)

/**
 * 褥瘡程度選択フラッグ
 */
watch(
  () => local.orX0141.sin7TeidoFlg,
  (newValue) => {
    if (!!newValue && !local.orX0141.sin7Flg.modelValue) {
      local.orX0141.sin7Flg.modelValue = true
    }
  }
)

/**
 * その他皮膚疾患程度選択フラッグ
 */
watch(
  () => local.orX0141.sin8TeidoFlg,
  (newValue) => {
    if (!!newValue && !local.orX0141.sin8Flg.modelValue) {
      local.orX0141.sin8Flg.modelValue = true
    }
  }
)

/**
 * 入力を無効にする
 */
watch(
  () => Or29757Logic.state.get(props.parentUniqueCpId)!.disabled,
  (newValue) => {
    local.disabled = !!newValue
    changeInputDisabled()
  }
)

/**
 * 入力無効状態を切り替える
 */
const changeInputDisabled = () => {
  // ボタン
  localOneway.mo00009Oneway.disabled = local.disabled
  // 利き腕
  localOneway.mo00039Oneway1.disabled = local.disabled
  // 身長/体重
  localOneway.mo00038Oneway1.mo00045Oneway!.disabled = local.disabled
  // 体重の変化
  localOneway.mo00039Oneway2.disabled = local.disabled
  // 四肢欠損選択フラグ
  localOneway.mo00018Oneway1.disabled = local.disabled
  // 四肢欠損部位の名前
  localOneway.mo00045Oneway1.disabled = local.disabled
  // 麻痺選択フラグ
  localOneway.mo00018Oneway2.disabled = local.disabled
  // 右上肢選択フラグ
  localOneway.mo00018Oneway3.disabled = local.disabled
  // 程度選択フラッグ
  localOneway.mo00039Oneway3.disabled = local.disabled
  // 左上肢選択フラグ
  localOneway.mo00018Oneway4.disabled = local.disabled
  // 右下肢選択フラグ
  localOneway.mo00018Oneway5.disabled = local.disabled
  // 左下肢選択フラグ
  localOneway.mo00018Oneway6.disabled = local.disabled
  // その他選択フラッグ
  localOneway.mo00018Oneway7.disabled = local.disabled
  // 筋力の低下選択フラグ
  localOneway.mo00018Oneway8.disabled = local.disabled
  // 関節の拘縮選択フラグ
  localOneway.mo00018Oneway9.disabled = local.disabled
  // 関節の痛み選択フラグ
  localOneway.mo00018Oneway10.disabled = local.disabled
  // 失調不随意運動選択フラグ
  localOneway.mo00018Oneway11.disabled = local.disabled
  // 失調右フラグ
  localOneway.mo00018Oneway12.disabled = local.disabled
  // 失調左フラグ
  localOneway.mo00018Oneway13.disabled = local.disabled
  // 褥瘡選択フラグ
  localOneway.mo00018Oneway14.disabled = local.disabled
  // その他皮膚疾患選択フラグ
  localOneway.mo00018Oneway15.disabled = local.disabled
  // 屋外歩行選択フラッグ
  localOneway.mo00039Oneway4.disabled = local.disabled
  // 車いすの使用選択フラッグ
  localOneway.mo00039Oneway5.disabled = local.disabled
  // 歩行補助具・装具の使用選択フラッグ1
  localOneway.mo00018Oneway16.disabled = local.disabled
  // 歩行補助具・装具の使用選択フラッグ2
  localOneway.mo00018Oneway17.disabled = local.disabled
  // 歩行補助具・装具の使用選択フラッグ3
  localOneway.mo00018Oneway18.disabled = local.disabled
  // 食事行為選択フラッグ
  localOneway.mo00039Oneway6.disabled = local.disabled
  // 現在の栄養状態選択フラッグ
  localOneway.mo00039Oneway7.disabled = local.disabled
  // 留意点の内容
  localOneway.mo00045Oneway2.disabled = local.disabled
  // 可能の状態1
  localOneway.mo00018Oneway19.disabled = local.disabled
  // 可能の状態2
  localOneway.mo00018Oneway20.disabled = local.disabled
  // 可能の状態3
  localOneway.mo00018Oneway21.disabled = local.disabled
  // 可能の状態4
  localOneway.mo00018Oneway22.disabled = local.disabled
  // 可能の状態5
  localOneway.mo00018Oneway23.disabled = local.disabled
  // 可能の状態6
  localOneway.mo00018Oneway24.disabled = local.disabled
  // 可能の状態7
  localOneway.mo00018Oneway25.disabled = local.disabled
  // 可能の状態8
  localOneway.mo00018Oneway26.disabled = local.disabled
  // 可能の状態9
  localOneway.mo00018Oneway27.disabled = local.disabled
  // 可能の状態10
  localOneway.mo00018Oneway28.disabled = local.disabled
  // 可能の状態11
  localOneway.mo00018Oneway29.disabled = local.disabled
  // 可能の状態12
  localOneway.mo00018Oneway30.disabled = local.disabled
  // 可能の状態13
  localOneway.mo00018Oneway31.disabled = local.disabled
  // 可能の状態14
  localOneway.mo00018Oneway32.disabled = local.disabled
  // 可能の状態14その他の内容
  localOneway.mo00045Oneway3.disabled = local.disabled
  // 方針の内容
  localOneway.mo00045Oneway4.disabled = local.disabled
  // 生活機能の維持･改善の見通し選択フラッグ
  localOneway.mo00039Oneway8.disabled = local.disabled
  // 医学的管理の必要性
  localOneway.mo00018Oneway33.disabled = local.disabled
  // 医学的管理の必要性（必要性が特に高い）
  localOneway.mo00018Oneway34.disabled = local.disabled
  // 特記すべき項目なしチェックボックス
  localOneway.mo00018Oneway35.disabled = local.disabled
  // その他の医療系サービスの内容
  localOneway.mo00045Oneway5.disabled = local.disabled
  // 留意事項フラッグ
  localOneway.mo00039Oneway9.disabled = local.disabled
  // 留意事項の内容
  localOneway.mo00045Oneway6.disabled = local.disabled
  // 留意事項6その他の内容
  localOneway.mo00045Oneway7.disabled = local.disabled
  // 留意事項1フラッグ
  localOneway.mo00018Oneway36.disabled = local.disabled
  // 留意事項2フラッグ
  localOneway.mo00018Oneway37.disabled = local.disabled
  // 留意事項3フラッグ
  localOneway.mo00018Oneway38.disabled = local.disabled
  // 留意事項4フラッグ
  localOneway.mo00018Oneway39.disabled = local.disabled
  // 留意事項5フラッグ
  localOneway.mo00018Oneway40.disabled = local.disabled
  // 留意事項6フラッグ
  localOneway.mo00018Oneway41.disabled = local.disabled
  // 留意事項7フラッグ
  localOneway.mo00018Oneway42.disabled = local.disabled
  // 感染症の有無のフラッグ
  localOneway.mo00039Oneway10.disabled = local.disabled
  // 感染の内容
  localOneway.mo00045Oneway8.disabled = local.disabled
  // 特記すべき事項
  localOneway.mo00046Oneway2.disabled = local.disabled
}

/**
 * タブデータを初期化する
 *
 */
const initTabData = () => {
  // 表示用「主治医意見書履歴」情報.改訂フラグが「R3/4改訂版」の場合
  if (local.rirekiObj.kaiteiFlg === '3') {
    localOneway.mo00615Oneway39.itemLabel = t('label.explanation-of-notable-matters-title1')
    localOneway.mo01299Oneway7.title = t('section-title-21');
  } else {
    localOneway.mo00615Oneway39.itemLabel = t('label.explanation-of-notable-matters-title2')
    localOneway.mo01299Oneway7.title = t('section-title-18');
  }
}

/**
 * 特記すべき事項支援アイコン
 */
const handleNotableMattersClick = () => {
  localOneway.or51775Oneway.inputContents = local.orX0141.ikenKnj.value ?? ''
  localOneway.or51775Oneway.userId = systemCommonsStore.getUserId ?? ''
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 麻痺チェックボックス
 */
const sinFlgChange = () => {
  if (
    !local.orX0141.sin2Flg.modelValue &&
    (local.orX0141.sin2R1Flg.modelValue ||
      local.orX0141.sin2L1Flg.modelValue ||
      local.orX0141.sin2R2Flg.modelValue ||
      local.orX0141.sin2L2Flg.modelValue ||
      local.orX0141.sin2ElseFlg.modelValue)
  ) {
    local.orX0141.sin2Flg.modelValue = true
  }
  // 右上肢選択フラグ
  if (!local.orX0141.sin2R1Flg.modelValue) {
    local.orX0141.sin2R1TeidoFlg = ''
  }
  // 左上肢選択フラグ
  if (!local.orX0141.sin2L1Flg.modelValue) {
    local.orX0141.sin2L1TeidoFlg = ''
  }
  // 右下肢選択フラグ
  if (!local.orX0141.sin2R2Flg.modelValue) {
    local.orX0141.sin2R2TeidoFlg = ''
  }
  // 左下肢選択フラグ
  if (!local.orX0141.sin2L2Flg.modelValue) {
    local.orX0141.sin2L2TeidoFlg = ''
  }
  // その他選択フラッグ
  if (!local.orX0141.sin2ElseFlg.modelValue) {
    local.orX0141.sin2ElseTeidoFlg = ''
  }
}

/**
 * 「失調-チェックボックス」変更
 */
const onDyskinesiaChange = () => {
  if (
    !local.orX0141.sin6JosiRightFlg.modelValue ||
    !local.orX0141.sin6JosiLeftFlg.modelValue ||
    !local.orX0141.sin6KasiRightFlg.modelValue ||
    !local.orX0141.sin6KasiLeftFlg.modelValue ||
    !local.orX0141.sin6TaikanRightFlg.modelValue ||
    !local.orX0141.sin6TaikanLeftFlg.modelValue
  ) {
    local.orX0141.sin6Flg.modelValue = false
  }
}

// ダイアログ表示フラグ
const showDialogOr51775 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  local.orX0141.ikenKnj.value = data.value
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}
</script>
<template>
  <div
    v-if="
      Or29757Logic.state.get(props.parentUniqueCpId)?.operaFlg !== Or29758Const.ACTION_TYPE.DELETE
    "
    class="view"
  >
    <base-mo01299
      :oneway-model-value="localOneway.mo01299Oneway1"
      class="section-view"
    >
      <template #content>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 利き腕左右ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway1" />
          <!-- 利き腕左右ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.kikiudeFlg"
            :oneway-model-value="localOneway.mo00039Oneway1"
          ></base-mo00039>
          <!-- 身長ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway2" />
          <!-- 身長テキストボックス -->
          <base-mo00038
            v-model="local.orX0141.height"
            class="mt-3 mb-2"
            :oneway-model-value="localOneway.mo00038Oneway1"
          />
          <!-- 身長単位ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway3" />
          <!-- 体重ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway4" />
          <!-- 体重テキストボックス -->
          <base-mo00038
            v-model="local.orX0141.weight"
            class="mt-3 mb-2"
            :oneway-model-value="localOneway.mo00038Oneway1"
          />
          <!-- 体重単位 -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway5" />
          <!-- 過去６ヶ月の体重の変化ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway6" />
          <!-- 過去６ヶ月の体重の変化ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.weightFlg"
            :oneway-model-value="localOneway.mo00039Oneway2"
          ></base-mo00039>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 四肢欠損チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin1Flg"
            class="width-200"
            :oneway-model-value="localOneway.mo00018Oneway1"
          />
          <!-- 部位ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway7" />
          <!-- 部位テキストボックス -->
          <base-mo-00045
            v-model="local.orX0141.sin1BuiKnj"
            :oneway-model-value="localOneway.mo00045Oneway1"
          />
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 麻痺チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin2Flg"
            class="width-200"
            :oneway-model-value="localOneway.mo00018Oneway2"
          />
          <!-- 麻痺-右上肢チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin2R1Flg"
            class="mr-2"
            :oneway-model-value="localOneway.mo00018Oneway3"
            @change="sinFlgChange()"
          />
          <!-- 麻痺-右上肢程度選択ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway8" />
          <!-- 麻痺-右上肢程度選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.sin2R1TeidoFlg"
            class="mr-4"
            :oneway-model-value="localOneway.mo00039Oneway3"
          ></base-mo00039>
          <!-- 麻痺-左上肢チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin2L1Flg"
            class="mr-2"
            :oneway-model-value="localOneway.mo00018Oneway4"
            @change="sinFlgChange()"
          />
          <!-- 麻痺-左上肢程度選択ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway8" />
          <!-- 麻痺-左上肢程度選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.sin2L1TeidoFlg"
            :oneway-model-value="localOneway.mo00039Oneway3"
          ></base-mo00039>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <div class="width-200 mr-2"></div>
          <!-- 麻痺-右下肢チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin2R2Flg"
            class="mr-2"
            :oneway-model-value="localOneway.mo00018Oneway5"
            @change="sinFlgChange()"
          />
          <!-- 麻痺-右下肢程度選択ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway8" />
          <!-- 麻痺-右下肢程度選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.sin2R2TeidoFlg"
            class="mr-4"
            :oneway-model-value="localOneway.mo00039Oneway3"
          ></base-mo00039>
          <!-- 麻痺-左下肢チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin2L2Flg"
            class="mr-2"
            :oneway-model-value="localOneway.mo00018Oneway6"
            @change="sinFlgChange()"
          />
          <!-- 麻痺-左下肢程度選択ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway8" />
          <!-- 麻痺-左下肢程度選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.sin2L2TeidoFlg"
            class="mr-4"
            :oneway-model-value="localOneway.mo00039Oneway3"
          ></base-mo00039>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <div class="width-200 mr-2"></div>
          <!-- 麻痺-その他チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin2ElseFlg"
            class="mr-2"
            :oneway-model-value="localOneway.mo00018Oneway7"
            @change="sinFlgChange()"
          />
          <!-- 麻痺-その他部位ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway7" />
          <!-- 麻痺-その他部位テキストボックス -->
          <base-mo-00045
            v-model="local.orX0141.sin2ElseBuiKnj"
            :oneway-model-value="localOneway.mo00045Oneway1"
          />
          <!-- 麻痺-その他部位程度選択ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway8" />
          <!-- 麻痺-その他部位程度選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.sin2ElseTeidoFlg"
            class="mr-4"
            :oneway-model-value="localOneway.mo00039Oneway3"
          ></base-mo00039>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 筋力の低下チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin3Flg"
            class="width-200"
            :oneway-model-value="localOneway.mo00018Oneway8"
            @change="
              () => {
                if (!local.orX0141.sin3Flg.modelValue) {
                  local.orX0141.sin3TeidoFlg = ''
                }
              }
            "
          />
          <!-- 筋力の低下-部位ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway7" />
          <!-- 筋力の低下-部位テキストボックス -->
          <base-mo-00045
            v-model="local.orX0141.sin3BuiKnj"
            :oneway-model-value="localOneway.mo00045Oneway1"
          />
          <!-- 筋力の低下-程度選択ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway8" />
          <!-- 筋力の低下-程度選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.sin3TeidoFlg"
            class="mr-4"
            :oneway-model-value="localOneway.mo00039Oneway3"
          ></base-mo00039>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 関節の拘縮チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin4Flg"
            class="width-200"
            :oneway-model-value="localOneway.mo00018Oneway9"
            @change="
              () => {
                if (!local.orX0141.sin4Flg.modelValue) {
                  local.orX0141.sin4TeidoFlg = ''
                }
              }
            "
          />
          <!-- 関節の拘縮-部位ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway7" />
          <!-- 関節の拘縮-部位テキストボックス -->
          <base-mo-00045
            v-model="local.orX0141.sin4BuiKnj"
            :oneway-model-value="localOneway.mo00045Oneway1"
          />
          <!-- 関節の拘縮-程度選択ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway8" />
          <!-- 関節の拘縮-程度選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.sin4TeidoFlg"
            class="mr-4"
            :oneway-model-value="localOneway.mo00039Oneway3"
          ></base-mo00039>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 関節の痛みチェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin5Flg"
            class="width-200"
            :oneway-model-value="localOneway.mo00018Oneway10"
            @change="
              () => {
                if (!local.orX0141.sin5Flg.modelValue) {
                  local.orX0141.sin5TeidoFlg = ''
                }
              }
            "
          />
          <!-- 関節の痛み-部位ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway7" />
          <!-- 関節の痛み-部位テキストボックス -->
          <base-mo-00045
            v-model="local.orX0141.sin5BuiKnj"
            :oneway-model-value="localOneway.mo00045Oneway1"
          />
          <!-- 関節の痛み-程度選択ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway8" />
          <!-- 関節の痛み-程度選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.sin5TeidoFlg"
            class="mr-4"
            :oneway-model-value="localOneway.mo00039Oneway3"
          ></base-mo00039>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 失調・不随意運動チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin6Flg"
            class="width-200"
            :oneway-model-value="localOneway.mo00018Oneway11"
            @change="
              () => {
                if (!local.orX0141.sin6Flg.modelValue) {
                  local.orX0141.sin6JosiRightFlg.modelValue = false
                  local.orX0141.sin6JosiLeftFlg.modelValue = false
                  local.orX0141.sin6KasiRightFlg.modelValue = false
                  local.orX0141.sin6KasiLeftFlg.modelValue = false
                  local.orX0141.sin6TaikanRightFlg.modelValue = false
                  local.orX0141.sin6TaikanLeftFlg.modelValue = false
                }
              }
            "
          />
          <!-- 失調-上肢ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway9" />
          <!-- 失調-上肢-右チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin6JosiRightFlg"
            :oneway-model-value="localOneway.mo00018Oneway12"
            @change="onDyskinesiaChange()"
          />
          <!-- 失調-上肢-左チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin6JosiLeftFlg"
            :oneway-model-value="localOneway.mo00018Oneway13"
            @change="onDyskinesiaChange()"
          />
          <!-- 失調-下肢ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway10" />
          <!-- 失調-下肢-右チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin6KasiRightFlg"
            :oneway-model-value="localOneway.mo00018Oneway12"
            @change="onDyskinesiaChange()"
          />
          <!-- 失調-下肢-左チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin6KasiLeftFlg"
            :oneway-model-value="localOneway.mo00018Oneway13"
            @change="onDyskinesiaChange()"
          />
          <!-- 失調-体幹ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway11" />
          <!-- 失調-体幹-右チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin6TaikanRightFlg"
            :oneway-model-value="localOneway.mo00018Oneway12"
            @change="onDyskinesiaChange()"
          />
          <!-- 失調-体幹-左チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin6TaikanLeftFlg"
            :oneway-model-value="localOneway.mo00018Oneway13"
            @change="onDyskinesiaChange()"
          />
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 褥瘡チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin7Flg"
            class="width-200"
            :oneway-model-value="localOneway.mo00018Oneway14"
            @change="
              () => {
                if (!local.orX0141.sin7Flg.modelValue) {
                  local.orX0141.sin7TeidoFlg = ''
                }
              }
            "
          />
          <!-- 褥瘡-部位ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway7" />
          <!-- 褥瘡-部位テキストボックス -->
          <base-mo-00045
            v-model="local.orX0141.sin7BuiKnj"
            :oneway-model-value="localOneway.mo00045Oneway1"
          />
          <!-- 褥瘡-程度選択ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway8" />
          <!--褥瘡-程度選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.sin7TeidoFlg"
            class="mr-4"
            :oneway-model-value="localOneway.mo00039Oneway3"
          ></base-mo00039>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- その他の皮膚疾患チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.sin8Flg"
            class="width-200"
            :oneway-model-value="localOneway.mo00018Oneway15"
            @change="
              () => {
                if (!local.orX0141.sin8Flg.modelValue) {
                  local.orX0141.sin8TeidoFlg = ''
                }
              }
            "
          />
          <!-- その他の皮膚疾患-部位ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway7" />
          <!-- その他の皮膚疾患-部位テキストボックス -->
          <base-mo-00045
            v-model="local.orX0141.sin8BuiKnj"
            :oneway-model-value="localOneway.mo00045Oneway1"
          />
          <!-- その他の皮膚疾患-程度選択ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway8" />
          <!-- その他の皮膚疾患-程度選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.sin8TeidoFlg"
            class="mr-4"
            :oneway-model-value="localOneway.mo00039Oneway3"
          ></base-mo00039>
        </c-v-row>
      </template>
    </base-mo01299>
    <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway12" />
    <base-mo01299
      :oneway-model-value="localOneway.mo01299Oneway2"
      class="section-view"
    >
      <template #content>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 屋外歩行ラベル -->
          <base-mo00615
            class="width-100"
            :oneway-model-value="localOneway.mo00615Oneway13"
          />
          <!-- 屋外歩行選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.idou1Flg"
            class="mr-4 radio-width-200"
            :oneway-model-value="localOneway.mo00039Oneway4"
          ></base-mo00039>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 車いすの使用ラベル -->
          <base-mo00615
            class="width-100"
            :oneway-model-value="localOneway.mo00615Oneway14"
          />
          <!-- 車いすの使用選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.idou2Flg"
            class="mr-4 radio-width-200"
            :oneway-model-value="localOneway.mo00039Oneway5"
          ></base-mo00039>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 歩行補助具・装具の使用（複数選択可）ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway15" />
          <!-- 用いていないチェックボックス -->
          <base-mo00018
            v-model="local.orX0141.idou3Flg1"
            :oneway-model-value="localOneway.mo00018Oneway16"
            @change="
              () => {
                if (local.orX0141.idou3Flg1.modelValue) {
                  local.orX0141.idou3Flg2.modelValue = false
                  local.orX0141.idou3Flg3.modelValue = false
                }
              }
            "
          />
          <!-- 屋外で使用チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.idou3Flg2"
            :oneway-model-value="localOneway.mo00018Oneway17"
            @change="
              () => {
                if (local.orX0141.idou3Flg2.modelValue) {
                  local.orX0141.idou3Flg1.modelValue = false
                }
              }
            "
          />
          <!-- 屋内で使用チェックボックス -->
          <base-mo00018
            v-model="local.orX0141.idou3Flg3"
            :oneway-model-value="localOneway.mo00018Oneway18"
            @change="
              () => {
                if (local.orX0141.idou3Flg3.modelValue) {
                  local.orX0141.idou3Flg1.modelValue = false
                }
              }
            "
          />
        </c-v-row>
      </template>
    </base-mo01299>
    <base-mo01299
      :oneway-model-value="localOneway.mo01299Oneway3"
      class="section-view"
    >
      <template #content>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 食事行為ラベル -->
          <base-mo00615
            class="width-100"
            :oneway-model-value="localOneway.mo00615Oneway16"
          />
          <!-- 食事行為選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.eiyo1Flg"
            class="mr-4 radio-width-300"
            :oneway-model-value="localOneway.mo00039Oneway6"
          ></base-mo00039>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 現在の栄養状態ラベル -->
          <base-mo00615
            class="width-100"
            :oneway-model-value="localOneway.mo00615Oneway17"
          />
          <!-- 現在の栄養状態選択ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.eiyoFlg"
            class="mr-4 radio-width-300"
            :oneway-model-value="localOneway.mo00039Oneway7"
          ></base-mo00039>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 栄養・食生活上の留意点ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway18" />
          <!-- 栄養・食生活上の留意点テキストボックス -->
          <base-mo-00045
            v-model="local.orX0141.eiyoPointKnj"
            :oneway-model-value="localOneway.mo00045Oneway2"
          />
        </c-v-row>
      </template>
    </base-mo01299>
    <base-mo01299
      :oneway-model-value="localOneway.mo01299Oneway4"
      class="section-view"
    >
      <template #content>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <div class="checkbox-group ml-2">
            <!-- 尿失禁チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai1Flg"
              class="width-180"
              :oneway-model-value="localOneway.mo00018Oneway19"
            />
            <!-- 転倒・骨折チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai2Flg"
              class="width-150"
              :oneway-model-value="localOneway.mo00018Oneway20"
            />
            <!-- 移動能力の低下チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai3Flg"
              class="width-150"
              :oneway-model-value="localOneway.mo00018Oneway21"
            />
            <!-- 褥瘡チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai4Flg"
              class="width-180"
              :oneway-model-value="localOneway.mo00018Oneway22"
            />
            <!-- 心肺機能の低下チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai5Flg"
              class="width-150"
              :oneway-model-value="localOneway.mo00018Oneway23"
            />
            <!-- 閉じこもりチェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai6Flg"
              class="width-150"
              :oneway-model-value="localOneway.mo00018Oneway24"
            />
            <!-- 意欲低下チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai7Flg"
              class="width-180"
              :oneway-model-value="localOneway.mo00018Oneway25"
            />
            <!-- 徘徊チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai8Flg"
              class="width-150"
              :oneway-model-value="localOneway.mo00018Oneway26"
            />
            <!-- 低栄養チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai9Flg"
              class="width-150"
              :oneway-model-value="localOneway.mo00018Oneway27"
            />
            <!-- 摂食・嚥下機能低下チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai10Flg"
              class="width-180"
              :oneway-model-value="localOneway.mo00018Oneway28"
            />
            <!-- 脱水チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai11Flg"
              class="width-150"
              :oneway-model-value="localOneway.mo00018Oneway29"
            />
            <!-- 易感染性チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai12Flg"
              class="width-150"
              :oneway-model-value="localOneway.mo00018Oneway30"
            />
            <!-- がん等による疼痛チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai13Flg"
              class="width-180"
              :oneway-model-value="localOneway.mo00018Oneway31"
            />
            <!-- その他チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.jotai14Flg"
              class="width-150"
              :oneway-model-value="localOneway.mo00018Oneway32"
            />
            <!-- その他の内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.jotai14Knj"
              :oneway-model-value="localOneway.mo00045Oneway3"
            />
          </div>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mb-2"
        >
          <!-- 対処方針ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway19" />
          <!-- 対処方針テキストボックス -->
          <base-mo-00045
            v-model="local.orX0141.housinKnj"
            :oneway-model-value="localOneway.mo00045Oneway4"
          />
        </c-v-row>
      </template>
    </base-mo01299>

    <base-mo01299
      :oneway-model-value="localOneway.mo01299Oneway5"
      class="section-view"
    >
      <template #content>
        <c-v-row no-gutters>
          <base-mo00039
            v-model="local.orX0141.kitaiFlg"
            :oneway-model-value="localOneway.mo00039Oneway8"
          ></base-mo00039>
        </c-v-row>
      </template>
    </base-mo01299>

    <base-mo01299
      :oneway-model-value="localOneway.mo01299Oneway6"
      class="section-view"
    >
      <template #content>
        <div v-if="local.rirekiObj.kaiteiFlg === '2' && local.rirekiObj.createYmd < '2018/10/01'">
          <c-v-row no-gutters>
            <!-- 訪問診療ラベル -->
            <base-mo00615
              class="width-120 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway20"
            />
            <!-- 訪問診療-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri1Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問診療-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri1TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri1TokFlg.modelValue) {
                    local.orX0141.kanri1Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問看護ラベル -->
            <base-mo00615
              class="width-150 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway21"
            />
            <!-- 訪問看護-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri2Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問看護-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri2TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri2TokFlg.modelValue) {
                    local.orX0141.kanri2Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 看護職員の訪問ラベル -->
            <base-mo00615
              class="width-150 ml-4 break-spaces"
              :oneway-model-value="localOneway.mo00615Oneway22"
            />
            <!-- 看護職員の訪問-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri11Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 看護職員の訪問-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri11TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri11TokFlg.modelValue) {
                    local.orX0141.kanri11Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問歯科診療ラベル -->
            <base-mo00615
              class="width-100 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway23"
            />
            <!-- 訪問歯科診療-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri3Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問歯科診療-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri3TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri3TokFlg.modelValue) {
                    local.orX0141.kanri3Flg.modelValue = true
                  }
                }
              "
            />
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 訪問薬剤管理指導ラベル -->
            <base-mo00615
              class="width-120 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway24"
            />
            <!-- 訪問薬剤管理指導-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri4Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問薬剤管理指導-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri4TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri4TokFlg.modelValue) {
                    local.orX0141.kanri4Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問リハビリテーションラベル -->
            <base-mo00615
              class="width-150 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway25"
            />
            <!-- 訪問リハビリテーション-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri5Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問リハビリテーション-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri5TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri5TokFlg.modelValue) {
                    local.orX0141.kanri5Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 短期入所療養介護ラベル -->
            <base-mo00615
              class="width-150 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway26"
            />
            <!-- 短期入所療養介護-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri6Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 短期入所療養介護-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri6TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri6TokFlg.modelValue) {
                    local.orX0141.kanri6Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問歯科衛生指導ラベル -->
            <base-mo00615
              class="width-100 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway27"
            />
            <!-- 訪問歯科衛生指導-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri7Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問歯科衛生指導-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri7TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri7TokFlg.modelValue) {
                    local.orX0141.kanri7Flg.modelValue = true
                  }
                }
              "
            />
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 訪問栄養食事指導ラベル -->
            <base-mo00615
              class="width-120 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway28"
            />
            <!-- 訪問栄養食事指導-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri8Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問栄養食事指導-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri8TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri8TokFlg.modelValue) {
                    local.orX0141.kanri8Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 通所リハビリテーションラベル -->
            <base-mo00615
              class="width-150 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway29"
            />
            <!-- 通所リハビリテーション-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri9Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 通所リハビリテーション-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri9TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri9TokFlg.modelValue) {
                    local.orX0141.kanri9Flg.modelValue = true
                  }
                }
              "
            />
            <!-- その他の医療系サービスラベル -->
            <base-mo00615
              class="width-150 ml-4 break-spaces"
              :oneway-model-value="localOneway.mo00615Oneway30"
            />
            <!-- その他の医療系サービス-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri10Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- その他の医療系サービス-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri10TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri10TokFlg.modelValue) {
                    local.orX0141.kanri10Flg.modelValue = true
                  }
                }
              "
            />
            <!-- その他の医療系サービスの内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.kanri10Knj"
              :oneway-model-value="localOneway.mo00045Oneway5"
            />
          </c-v-row>
        </div>
        <div v-if="local.rirekiObj.kaiteiFlg === '2' && local.rirekiObj.createYmd > '2018/10/01'">
          <c-v-row no-gutters>
            <!-- 訪問診療ラベル -->
            <base-mo00615
              class="width-150 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway20"
            />
            <!-- 訪問診療-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri1Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問診療-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri1TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri1TokFlg.modelValue) {
                    local.orX0141.kanri1Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問看護ラベル -->
            <base-mo00615
              class="width-130 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway21"
            />
            <!-- 訪問看護-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri2Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問看護-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri2TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri2TokFlg.modelValue) {
                    local.orX0141.kanri2Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問歯科診療ラベル -->
            <base-mo00615
              class="width-100 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway23"
            />
            <!-- 訪問歯科診療-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri3Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問歯科診療-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri3TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri3TokFlg.modelValue) {
                    local.orX0141.kanri3Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問薬剤管理指導ラベル -->
            <base-mo00615
              class="width-120 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway24"
            />
            <!-- 訪問薬剤管理指導-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri4Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問薬剤管理指導-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri4TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri4TokFlg.modelValue) {
                    local.orX0141.kanri4Flg.modelValue = true
                  }
                }
              "
            />
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 訪問リハビリテーションラベル -->
            <base-mo00615
              class="width-150 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway25"
            />
            <!-- 訪問リハビリテーション-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri5Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問リハビリテーション-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri5TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri5TokFlg.modelValue) {
                    local.orX0141.kanri5Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 短期入所療養介護ラベル -->
            <base-mo00615
              class="width-130 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway26"
            />
            <!-- 短期入所療養介護-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri6Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 短期入所療養介護-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri6TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri6TokFlg.modelValue) {
                    local.orX0141.kanri6Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問歯科衛生指導ラベル -->
            <base-mo00615
              class="width-100 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway27"
            />
            <!-- 訪問歯科衛生指導-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri7Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問歯科衛生指導-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri7TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri7TokFlg.modelValue) {
                    local.orX0141.kanri7Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問栄養食事指導ラベル -->
            <base-mo00615
              class="width-120 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway28"
            />
            <!-- 訪問栄養食事指導-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri8Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問栄養食事指導-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri8TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri8TokFlg.modelValue) {
                    local.orX0141.kanri8Flg.modelValue = true
                  }
                }
              "
            />
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 通所リハビリテーションラベル -->
            <base-mo00615
              class="width-150 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway29"
            />
            <!-- 通所リハビリテーション-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri9Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 通所リハビリテーション-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri9TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri9TokFlg.modelValue) {
                    local.orX0141.kanri9Flg.modelValue = true
                  }
                }
              "
            />
            <!-- その他の医療系サービスラベル -->
            <base-mo00615
              class="width-130 ml-4 break-spaces"
              :oneway-model-value="localOneway.mo00615Oneway30"
            />
            <!-- その他の医療系サービス-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri10Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- その他の医療系サービス-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri10TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri10TokFlg.modelValue) {
                    local.orX0141.kanri10Flg.modelValue = true
                  }
                }
              "
            />
            <!-- その他の医療系サービスの内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.kanri10Knj"
              :oneway-model-value="localOneway.mo00045Oneway5"
            />
          </c-v-row>
        </div>
        <div v-if="local.rirekiObj.kaiteiFlg === '3'">
          <c-v-row no-gutters>
            <!-- 訪問診療ラベル -->
            <base-mo00615
              class="width-150 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway20"
            />
            <!-- 訪問診療-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri1Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問診療-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri1TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri1TokFlg.modelValue) {
                    local.orX0141.kanri1Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問看護ラベル -->
            <base-mo00615
              class="width-100 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway21"
            />
            <!-- 訪問看護-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri2Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問看護-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri2TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri2TokFlg.modelValue) {
                    local.orX0141.kanri2Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問歯科診療ラベル -->
            <base-mo00615
              class="width-100 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway23"
            />
            <!-- 訪問歯科診療-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri3Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問歯科診療-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri3TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri3TokFlg.modelValue) {
                    local.orX0141.kanri3Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問薬剤管理指導ラベル -->
            <base-mo00615
              class="width-130 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway24"
            />
            <!-- 訪問薬剤管理指導-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri4Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問薬剤管理指導-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri4TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri4TokFlg.modelValue) {
                    local.orX0141.kanri4Flg.modelValue = true
                  }
                }
              "
            />
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 訪問リハビリテーションラベル -->
            <base-mo00615
              class="width-150 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway25"
            />
            <!-- 訪問リハビリテーション-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri5Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問リハビリテーション-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri5TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri5TokFlg.modelValue) {
                    local.orX0141.kanri5Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 短期入所療養介護ラベル -->
            <base-mo00615
              class="width-100 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway26"
            />
            <!-- 短期入所療養介護-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri6Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 短期入所療養介護-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri6TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri6TokFlg.modelValue) {
                    local.orX0141.kanri6Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問歯科衛生指導ラベル -->
            <base-mo00615
              class="width-100 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway27"
            />
            <!-- 訪問歯科衛生指導-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri7Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問歯科衛生指導-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri7TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri7TokFlg.modelValue) {
                    local.orX0141.kanri7Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 訪問栄養食事指導ラベル -->
            <base-mo00615
              class="width-130 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway28"
            />
            <!-- 訪問栄養食事指導-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri8Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 訪問栄養食事指導-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri8TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri8TokFlg.modelValue) {
                    local.orX0141.kanri8Flg.modelValue = true
                  }
                }
              "
            />
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 通所リハビリテーションラベル -->
            <base-mo00615
              class="width-150 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway29"
            />
            <!-- 通所リハビリテーション-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri9Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 通所リハビリテーション-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri9TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri9TokFlg.modelValue) {
                    local.orX0141.kanri9Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 老人保健施設ラベル -->
            <base-mo00615
              class="width-100 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway31"
            />
            <!-- 老人保健施設-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri12Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 老人保健施設-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri12TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri12TokFlg.modelValue) {
                    local.orX0141.kanri12Flg.modelValue = true
                  }
                }
              "
            />
            <!-- 介護医療院ラベル -->
            <base-mo00615
              class="width-100 ml-4"
              :oneway-model-value="localOneway.mo00615Oneway32"
            />
            <!-- 介護医療院-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri13Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- 介護医療院-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri13TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri13TokFlg.modelValue) {
                    local.orX0141.kanri13Flg.modelValue = true
                  }
                }
              "
            />
            <!-- その他の医療系サービスラベル -->
            <base-mo00615
              class="width-130 ml-4 break-spaces"
              :oneway-model-value="localOneway.mo00615Oneway30"
            />
            <!-- その他の医療系サービス-有チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri10Flg"
              :oneway-model-value="localOneway.mo00018Oneway33"
            />
            <!-- その他の医療系サービス-高チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri10TokFlg"
              :oneway-model-value="localOneway.mo00018Oneway34"
              @change="
                () => {
                  if (local.orX0141.kanri10TokFlg.modelValue) {
                    local.orX0141.kanri10Flg.modelValue = true
                  }
                }
              "
            />
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 特記すべき項目なしチェックボックス -->
            <base-mo00018
              v-model="local.orX0141.kanri14Flg"
              :oneway-model-value="localOneway.mo00018Oneway35"
            />
            <div class="width-750 ml-4"></div>
            <!-- その他の医療系サービスの内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.kanri10Knj"
              :oneway-model-value="localOneway.mo00045Oneway5"
            />
          </c-v-row>
        </div>
      </template>
    </base-mo01299>
    <base-mo01299
      :oneway-model-value="localOneway.mo01299Oneway7"
      class="section-view"
    >
      <template #content>
        <div v-if="local.rirekiObj.kaiteiFlg === '2'">
          <c-v-row
            no-gutters
            class="mb-2"
          >
            <!-- 留意事項1ラベル -->
            <base-mo00615
              class="ml-4"
              :oneway-model-value="localOneway.mo00615Oneway33"
            />
            <!-- 留意事項1選択ラジオボタン -->
            <base-mo00039
              v-model="local.orX0141.ryui1Flg.modelValue"
              class="mr-4"
              :oneway-model-value="localOneway.mo00039Oneway9"
            ></base-mo00039>
            <!-- 留意事項1の内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.ryui1Knj"
              :oneway-model-value="localOneway.mo00045Oneway6"
            />
            <!-- 留意事項2ラベル -->
            <base-mo00615
              class="ml-4"
              :oneway-model-value="localOneway.mo00615Oneway34"
            />
            <!-- 留意事項2選択ラジオボタン -->
            <base-mo00039
              v-model="local.orX0141.ryui2Flg.modelValue"
              class="mr-4"
              :oneway-model-value="localOneway.mo00039Oneway9"
            ></base-mo00039>
            <!-- 留意事項2の内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.ryui2Knj"
              :oneway-model-value="localOneway.mo00045Oneway6"
            />
          </c-v-row>
          <c-v-row
            no-gutters
            class="mb-2"
          >
            <!-- 留意事項3ラベル -->
            <base-mo00615
              class="ml-4"
              :oneway-model-value="localOneway.mo00615Oneway35"
            />
            <!-- 留意事項3選択ラジオボタン -->
            <base-mo00039
              v-model="local.orX0141.ryui3Flg.modelValue"
              class="mr-4"
              :oneway-model-value="localOneway.mo00039Oneway9"
            ></base-mo00039>
            <!-- 留意事項3の内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.ryui3Knj"
              :oneway-model-value="localOneway.mo00045Oneway6"
            />
            <!-- 留意事項4ラベル -->
            <base-mo00615
              class="ml-4"
              :oneway-model-value="localOneway.mo00615Oneway36"
            />
            <!-- 留意事項4選択ラジオボタン -->
            <base-mo00039
              v-model="local.orX0141.ryui4Flg.modelValue"
              class="mr-4"
              :oneway-model-value="localOneway.mo00039Oneway9"
            ></base-mo00039>
            <!-- 留意事項4の内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.ryui4Knj"
              :oneway-model-value="localOneway.mo00045Oneway6"
            />
          </c-v-row>
          <c-v-row
            no-gutters
            class="mb-2"
          >
            <!-- 留意事項5ラベル -->
            <base-mo00615
              class="ml-4"
              :oneway-model-value="localOneway.mo00615Oneway37"
            />
            <!-- 留意事項5選択ラジオボタン -->
            <base-mo00039
              v-model="local.orX0141.ryui5Flg.modelValue"
              class="mr-4"
              :oneway-model-value="localOneway.mo00039Oneway9"
            ></base-mo00039>
            <!-- 留意事項5の内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.ryui5Knj"
              :oneway-model-value="localOneway.mo00045Oneway6"
            />
            <!-- 留意事項6ラベル -->
            <base-mo00615
              class="ml-4"
              :oneway-model-value="localOneway.mo00615Oneway38"
            />
            <!-- 留意事項6の内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.ryui6Knj"
              :oneway-model-value="localOneway.mo00045Oneway7"
            />
          </c-v-row>
        </div>
        <div v-if="local.rirekiObj.kaiteiFlg === '3'">
          <c-v-row
            no-gutters
            class="mb-2"
          >
            <!-- 留意事項1の内容チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.ryui1Flg"
              :oneway-model-value="localOneway.mo00018Oneway36"
            />
            <!-- 留意事項1の内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.ryui1Knj"
              :oneway-model-value="localOneway.mo00045Oneway6"
            />
            <!-- 留意事項2の内容チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.ryui2Flg"
              :oneway-model-value="localOneway.mo00018Oneway37"
            />
            <!-- 留意事項2の内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.ryui2Knj"
              :oneway-model-value="localOneway.mo00045Oneway6"
            />
            <!-- 留意事項3の内容チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.ryui3Flg"
              :oneway-model-value="localOneway.mo00018Oneway38"
            />
            <!-- 留意事項3の内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.ryui3Knj"
              :oneway-model-value="localOneway.mo00045Oneway6"
            />
          </c-v-row>
          <c-v-row
            no-gutters
            class="mb-2"
          >
            <!-- 留意事項4の内容チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.ryui4Flg"
              :oneway-model-value="localOneway.mo00018Oneway39"
            />
            <!-- 留意事項4の内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.ryui4Knj"
              :oneway-model-value="localOneway.mo00045Oneway6"
            />
            <!-- 留意事項5の内容チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.ryui5Flg"
              :oneway-model-value="localOneway.mo00018Oneway40"
            />
            <!-- 留意事項5の内容テキストボックス -->
            <base-mo-00045
              v-model="local.orX0141.ryui5Knj"
              :oneway-model-value="localOneway.mo00045Oneway6"
            />
            <!-- 留意事項6の内容チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.ryui6Flg"
              :oneway-model-value="localOneway.mo00018Oneway41"
            />
            <!-- 留意事項6の内容テキストエリア -->
            <base-mo-00046
              v-model="local.orX0141.ryui6Knj"
              :oneway-model-value="localOneway.mo00046Oneway1"
            />
          </c-v-row>
          <c-v-row
            no-gutters
            class="mb-2"
          >
            <!-- 留意事項の特記すべき項目チェックボックス -->
            <base-mo00018
              v-model="local.orX0141.ryui7Flg"
              :oneway-model-value="localOneway.mo00018Oneway42"
            />
          </c-v-row>
        </div>
      </template>
    </base-mo01299>
    <base-mo01299
      :oneway-model-value="localOneway.mo01299Oneway8"
      class="section-view"
    >
      <template #content>
        <c-v-row no-gutters>
          <!-- 感染症の有無ラジオボタン -->
          <base-mo00039
            v-model="local.orX0141.kansenFlg"
            class="mr-4"
            :oneway-model-value="localOneway.mo00039Oneway10"
          ></base-mo00039>
          <!-- 感染の内容テキストボックス -->
          <base-mo-00045
            v-model="local.orX0141.kansenKnj"
            :oneway-model-value="localOneway.mo00045Oneway8"
          />
        </c-v-row>
      </template>
    </base-mo01299>
    <base-mo01299
      :oneway-model-value="localOneway.mo01299Oneway9"
      class="section-view"
    >
      <template #content>
        <c-v-row no-gutters>
          <!-- 特記すべき事項説明ラベル -->
          <base-mo00615
            class="ml-4 title"
            :oneway-model-value="localOneway.mo00615Oneway39"
          />
        </c-v-row>
        <c-v-row
          no-gutters
          class="align-item"
        >
          <!-- 特記すべき事項支援ラベル -->
          <base-mo00615
            class="ml-4 break-spaces"
            :oneway-model-value="localOneway.mo00615Oneway40"
          />
          <!-- 特記すべき事項テキストエリア -->
          <base-mo-00046
            v-model="local.orX0141.ikenKnj"
            class="mr-2"
            :oneway-model-value="localOneway.mo00046Oneway2"
          />
          <div class="notable-matters">
            <c-v-divider
              vertical
              inset
            />
            <!-- 特記すべき事項支援アイコン -->
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="handleNotableMattersClick"
            />
          </div>
        </c-v-row>
      </template>
    </base-mo01299>
  </div>

  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="handleOr51775Confirm"
  />
</template>
<style scoped lang="scss">
.view {
  padding: 8px 24px;
}
.section-view {
  display: flex;
  flex-direction: column;
  :deep(.section-header) {
    width: unset !important;
    max-width: unset !important;
    min-width: unset !important;
  }
}
.radio-width-200 {
  :deep(.v-radio) {
    width: 200px;
  }
}
.radio-width-300 {
  :deep(.v-radio) {
    width: 300px;
  }
}
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  width: 1100px;
}
.break-spaces {
  white-space: break-spaces !important;
}
.title {
  white-space: break-spaces !important;
  max-width: 1500px;
}
.notable-matters {
  display: flex;
  align-items: center;
  height: 45px;
}
.align-item {
  display: flex;
  align-items: center;
}
.width-750 {
  width: 750px;
}
.width-200 {
  width: 200px;
}
.width-180 {
  width: 180px;
}
.width-150 {
  width: 150px;
}
.width-130 {
  width: 130px;
}
.width-120 {
  width: 120px;
}
.width-100 {
  width: 100px;
}
</style>
