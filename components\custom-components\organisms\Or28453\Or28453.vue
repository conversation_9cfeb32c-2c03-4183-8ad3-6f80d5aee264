<script setup lang="ts">
/**
 * Or28453:有機体:(課題・目標取込)履歴選択一覧
 *
 * @description
 * 履歴一覧を表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import { onMounted, reactive, watch } from 'vue'
import { Or28453Const } from './Or28453.constants'
import type { Or28453Type, Or28453OneWayType } from '~/types/cmn/business/components/Or28453Type'
import type { Mo01334OnewayType, Mo01334Type } from '~/types/business/components/Mo01334Type'
import { useScreenTwoWayBind } from '#build/imports'

/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  modelValue?: Or28453Type
  /** 単方向モデル  */
  onewayModelValue?: Or28453OneWayType
}

/**
 * props
 */
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
/**
 * ローカルOneway
 */
const localOneway = reactive({
  or28453: {
    ...props.modelValue,
  },
  or28453OneWay: {
    ...props.onewayModelValue,
  } as Or28453OneWayType,
  mo01334Oneway: {
    headers: [],
    items: [],
    height: '140px'
  } as Mo01334OnewayType,
})

/**
 * refValue
 */
const { refValue } = useScreenTwoWayBind<Mo01334Type>({
  cpId: Or28453Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  localOneway.mo01334Oneway.headers = localOneway.or28453OneWay.headers
  localOneway.mo01334Oneway.items =  localOneway.or28453OneWay.items
})

/**************************************************
 * ウォッチャー
 **************************************************/
 watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue) {
      localOneway.or28453OneWay = {
        ...newValue,
      }
      localOneway.mo01334Oneway.headers = localOneway.or28453OneWay.headers
      localOneway.mo01334Oneway.items =  localOneway.or28453OneWay.items
    }
  },
  { deep: true }
)
</script>

<template>
  <c-v-col style="margin: 0px !important; padding: 0px !important;" class="table-header table-wrapper">
    <base-mo-01334
      v-model="refValue"
      :oneway-model-value="localOneway.mo01334Oneway"
      width="650px"
      class="list-wrapper"
      :hide-default-footer="true"
      >
      <template #[`item.caseNo`]="{ item }">
        <base-mo01337
          :oneway-model-value="{
          value: item.caseNo,
            customClass:
              {
                outerStyle: 'background-color: rgba(0, 0, 0, 0); font-size: 12px !important;',
                itemClass: 'text-right',
              },
          }"></base-mo01337>
      </template>
    </base-mo-01334>
  </c-v-col>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
</style>
