<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { OrX0077Logic } from '../../template/OrX0077/OrX0077.logic'
import { OrX0077Const } from '../../template/OrX0077/OrX0077.constants'
import { Or31869Const } from '../Or31869/Or31869.constants'
import type { Or31928StateType } from './Or31928.type'
import { Or31928Const } from './Or31928.constants'
import {
  computed,
  onMounted,
  reactive,
  ref,
  useScreenOneWayBind,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
  watch,
} from '#imports'
import type {
  HistType,
  newCareHistType,
  Or31928DataType,
  Or31928OnewayType,
  PlanPeriodType,
} from '~/types/cmn/business/components/Or31928Type'
import type { Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { OrX0077OnewayType } from '~/types/cmn/business/components/OrX0077Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Or31869OnewayType, Or31869Type } from '~/types/cmn/business/components/Or31869Type'
import type {
  AttendeesHeaderInfo,
  AttendeesInfo,
  ContentsInfo,
  HeldInfo,
} from '~/repositories/cmn/entities/MeetingMinutesDetailPtn1UpdateEntity'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type {
  ICopyMeetingMinuteAttendeesInfo,
  ICopyMeetingMinuteNewCareAttendeesInfo,
  MeetingMinuteInfoCopySelectInEntity,
  MeetingMinuteInfoCopySelectOutEntity,
} from '~/repositories/cmn/entities/MeetingMinuteInfoCopySelectEntity'
import type {
  MeetingMinuteCopyPlanPeriodSelectInEntity,
  MeetingMinuteCopyPlanPeriodSelectOutEntity,
} from '~/repositories/cmn/entities/MeetingMinuteCopyPlanPeriodSelectEntity'
import type {
  MeetingMinuteCopyHistorySelectInEntity,
  MeetingMinuteCopyHistorySelectOutEntity,
} from '~/repositories/cmn/entities/MeetingMinuteCopyHistorySelectEntity'
import type { Or01444OnewayType } from '~/types/cmn/business/components/Or01444Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CustomClass } from '~/types/CustomClassType'
/**
 * Or31928:会議録複写モーダル
 * GUI01124_会議録複写
 *
 * @description
 * 会議録複写モーダル
 *
 * <AUTHOR> PHAM TIEN THANH
 */
const { t } = useI18n()
const systemCommonsStore = useSystemCommonsStore()

const Or01444DefaultOnewayModelValue: Or01444OnewayType = {
  pattern: 1,
  userId: '9999',
  shosiki: '2',
  svJigyoId: '1',
  cpnFlg: '1',
  screenCategory: '',
  conferenceNumber: '',
  syubetsuId: '2',
  periodControlFlag: 'true',
  journalFlag: '',
  shisetuId: '1',
}

const Or31928DefaultOnewayModelValue: Or31928OnewayType = {
  kikanFlag: '1',
  svJigyoId: '1',
  userId: '1',
  syubetsuID: '1',
  shisetuID: '1',
  svJigyoIdList: [],
  houjinId: '1',
  shosikiFlag: '1',
  cpnFlg: '1',
}

const orx0077 = ref({ uniqueCpId: '' })
const or31869 = ref({ uniqueCpId: '' })

const { setChildCpBinds } = useScreenUtils()

const emit = defineEmits(['copyData'])

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or31928OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

// ローカル状態管理
const localOneway = reactive({
  // Or01444設定
  Or01444: {
    ...Or01444DefaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // Or31928設定
  Or31928: {
    ...Or31928DefaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 複写テンプレート設定
  orx0077Oneway: {
    mo00024Oneway: {
      maxWidth: '1580px',
      height: '850px',
      persistent: true,
      showCloseBtn: true,
      mo01344Oneway: {
        name: 'Or31928',
        toolbarTitle: t('label.meeting-record-copy'),
        toolbarName: 'Or31928ToolBar',
        toolbarTitleCenteredFlg: false,
        showCardActions: true,
      },
    } as Mo00024OnewayType,
  } as OrX0077OnewayType,
  // データ管理
  Or31928DataType: {
    planPeriodList: [],
    histList: [],
    newCareHistList: [],
    heldInfoList: [],
    attendeesList: [],
    newCareHeldList: [],
    newCareAttendeesList: [],
  } as Or31928DataType,
  // ボタン設定
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00609OverwriteBtnOneway: {
    btnLabel: t('btn.confirm'),
    width: '90px',
  } as Mo00609OnewayType,
  // Or31869設定
  Or31869Oneway: {
    pattern: 1,
  } as Or31869OnewayType,
})
// Or31869デフォルト設定
const defaultOr31869: Or31869Type = {
  pattern: localOneway.Or01444.pattern,
  heldInfo: {} as HeldInfo,
  attendeesList: [] as AttendeesInfo[],
  attendeesHeaderInfo: {} as AttendeesHeaderInfo,
  contentsInfo: {} as ContentsInfo,
  isCopy: false,
  attendanceMode: [],
  screenCategory: '',
  cpnFlg: '',
  svJigyoId: '1',
  svJigyoCd: '',
  kikanFlag: '',
  sc1Id: '',
  createDate: '',
  heldDate: '',
  attendeesListResult: [],
  startYmd: '',
  endYmd: '',
}

// Or31869状態管理（双方向バインディング用）
const local = reactive({
  or31869: { ...defaultOr31869 } as Or31869Type,
})

// Or31869内部状態管理
const localComponents = reactive({
  or31869: { ...defaultOr31869 } as Or31869Type,
})
/**************************************************
 * 変数定義
 **************************************************/
// ダイアログ管理
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const or21815 = ref({ uniqueCpId: Or21815Const.CP_ID(0) })

// 履歴データ管理
const historyListType = ref<HistType[]>([])
const newCareHistoryListType = ref<newCareHistType[]>([])

// UI表示制御
const showOffice = ref<boolean>(false)
const showWhereKnj = ref<boolean>(true)
const showHeldTime = ref<boolean>(true)
const showHeldNumber = ref<boolean>(true)

// ダイアログ表示状態
const showDialogOr21814 = computed(() => {
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21815 = computed(() => {
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or31928StateType>({
  cpId: Or31928Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      OrX0077Logic.state.set({
        uniqueCpId: orx0077.value.uniqueCpId,
        state: { isOpen: value ?? Or31928Const.DEFAULT.IS_OPEN },
      })
    },
  },
})

/**
 * 計画期間テーブルヘッダー
 */
const headers = computed(() => {
  const baseHeaders = [
    {
      title: t('label.plan-period'),
      value: 'select',
      key: 'planPeriod',
      sortable: false,
    },
    {
      title: t('label.within-the-period-number-of-history'),
      value: '1',
      key: 'numberHis',
      sortable: false,
    },
  ]

  // 親画面.種別ID＝４或は＝6或は＝7の場合、計画期間一覧の事業所名を非表示
  if (
    props.onewayModelValue.syubetsuID === Or31928Const.TYPE_ID.TYPE_4 ||
    props.onewayModelValue.syubetsuID === Or31928Const.TYPE_ID.TYPE_6 ||
    props.onewayModelValue.syubetsuID === Or31928Const.TYPE_ID.TYPE_7
  ) {
    return baseHeaders
  }

  return [
    ...baseHeaders,
    {
      title: t('label.office-name'),
      value: '1',
      key: 'jigyoknj',
      sortable: false,
    },
  ]
})

/** テーブルヘッダ */
const header1 = reactive({
  headers: [
    // 作成日
    {
      title: t('label.create_ymd'),
      value: 'select',
      key: 'createYmd',
      width: '120px',
      sortable: false,
    },
    // 作成者
    {
      title: t('label.author'),
      value: '1',
      key: 'shokuKnj',
      width: '150px',
      sortable: false,
    },
    // 開催日
    {
      title: t('label.held-date'),
      value: '1',
      key: 'kaigiYmd',
      width: '150px',
      sortable: false,
    },
    // 開催場所
    {
      title: t('label.held-location'),
      value: '1',
      key: 'whereKnj',
      width: '150px',
      sortable: false,
    },
    // 開催時間
    {
      title: t('label.held-time'),
      value: '1',
      key: 'timeHm',
      width: '120px',
      sortable: false,
    },
    // 回数
    {
      title: t('label.number-of-times'),
      value: '1',
      key: 'kaisuu',
      width: '100px',
      sortable: false,
    },
    // 事業所名
    {
      title: t('label.office-name'),
      value: '1',
      key: 'jigyoKnj',
      width: '180px',
      sortable: false,
    },
  ],
})

/** テーブルヘッダ */
const header2 = reactive({
  headers: [
    // 作成日
    {
      title: t('label.consultation-date'),
      value: 'select',
      key: 'kaigiYmd',
      width: '120px',
      sortable: false,
    },
    // 作成者
    {
      title: t('label.author'),
      value: '1',
      key: 'shokuKnj',
      width: '150px',
      sortable: false,
    },
    // 開催場所
    {
      title: t('label.held-location'),
      value: '1',
      key: 'whereKnj',
      width: '150px',
      sortable: false,
    },
    // 開催時間
    {
      title: t('label.held-time'),
      value: '1',
      key: 'timeHm',
      width: '150px',
      sortable: false,
    },
    // 回数
    {
      title: t('label.number-of-times'),
      value: '1',
      key: 'kaisuu',
      width: '100px',
      sortable: false,
    },
    // ケース番号,
    {
      title: t('label.caseNo'),
      value: '1',
      key: 'caseNo',
      width: '120px',
      sortable: false,
    },
    // 改訂
    {
      title: t('label.revision'),
      value: '1',
      key: 'kaiteiFlg',
      width: '100px',
      sortable: false,
    },
    // 事業所名
    {
      title: t('label.office-name'),
      value: '1',
      key: 'jigyoKnj',
      width: '180px',
      sortable: false,
    },
  ],
})

const filteredHeaders = ref()
/**
 * 期間選択行ID
 */
const kikanSelectedRowId = ref<string | null>(null)
/**
 * 履歴選択行ID
 */
const rirekiSelectedRowId = ref<string | null>(null)

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0077Const.CP_ID(0)]: orx0077.value,
  [Or31869Const.CP_ID(0)]: or31869.value,
})

onMounted(async () => {
  if (props.onewayModelValue.kikanFlag === Or31928Const.FLAG.MANAGE_PERIOD) {
    if (
      props.onewayModelValue.cpnFlg !== Or31928Const.ASSESS_METHOD.NEW_NURSING_HOME_PACKAGE_PLAN
    ) {
      filteredHeaders.value = header1.headers.filter(
        (header) => header.key !== Or31928Const.NAME_OFFICE
      )
      showOffice.value = false
    } else {
      filteredHeaders.value = header2.headers.filter(
        (header) => header.key !== Or31928Const.NAME_OFFICE
      )
      showOffice.value = false
    }
  } else {
    if (
      props.onewayModelValue.cpnFlg !== Or31928Const.ASSESS_METHOD.NEW_NURSING_HOME_PACKAGE_PLAN
    ) {
      filteredHeaders.value = header1.headers
      showOffice.value = true
    } else {
      filteredHeaders.value = header2.headers
      showOffice.value = true
      showWhereKnj.value = false
      showHeldTime.value = false
      showHeldNumber.value = false
    }
  }
  await init()
})

/**************************************************
 * API処理
 **************************************************/

/**
 * ［会議録複写］初期処理
 *
 * @param userId - 利用者ID
 */
async function loadInitialData(userId: string | undefined) {
  const inputData: MeetingMinuteInfoCopySelectInEntity = {
    svJigyoIdList: localOneway.Or31928.svJigyoIdList,
    userID: userId ?? '9999',
    syubetsuID: localOneway.Or31928.syubetsuID,
    shisetuID: localOneway.Or31928.shisetuID,
    kikanFlag: localOneway.Or31928.kikanFlag,
    cpnFlg: localOneway.Or31928.cpnFlg,
    shosiki: localOneway.Or31928.shosikiFlag,
  }

  const response = await ScreenRepository.select('meetingMinuteInfoCopySelect', inputData)
  processInitialData(response as MeetingMinuteInfoCopySelectOutEntity)
  setupInitialUI()
}

/**
 * 計画期間データ読み込み（meetingMinuteCopyPlanPeriodSelect）
 *
 * @param planPeriodId - 計画期間ID
 */
async function loadPlanPeriodData(planPeriodId: string) {
  const inputData: MeetingMinuteCopyPlanPeriodSelectInEntity = {
    sc1Id: planPeriodId,
    svJigyoId: localOneway.Or31928.svJigyoId ?? '1',
    userID: localOneway.Or31928.userId,
    kikanFlag: localOneway.Or31928.kikanFlag,
    cpnFlg: localOneway.Or31928.cpnFlg,
    shosiki: localOneway.Or31928.shosikiFlag,
  }

  const response = await ScreenRepository.select('meetingMinuteCopyPlanPeriodSelect', inputData)
  processPlanPeriodData(planPeriodId, response as MeetingMinuteCopyPlanPeriodSelectOutEntity)
}

/**
 * 履歴データ読み込み（meetingMinuteCopyHistorySelect）
 *
 * @param kaigi1Id - 会議1ID
 *
 * @param sc1Id - 総合計画1ID
 */
async function loadHistoryData(kaigi1Id: string, sc1Id: string) {
  const inputData: MeetingMinuteCopyHistorySelectInEntity = {
    sc1Id: sc1Id,
    kaigi1Id: kaigi1Id,
    kikanFlag: localOneway.Or31928.kikanFlag,
    cpnFlg: localOneway.Or31928.cpnFlg,
    shosiki: localOneway.Or31928.shosikiFlag,
  }

  const response = await ScreenRepository.select('meetingMinuteCopyHistorySelect', inputData)
  processHistoryData(response as MeetingMinuteCopyHistorySelectOutEntity)
}

/**************************************************
 * データ処理
 **************************************************/

/**
 * 初期データ処理
 *
 * @param response - APIレスポンス
 */
function processInitialData(response: MeetingMinuteInfoCopySelectOutEntity) {
  const resData = response.data
  if (!resData) return

  // Or31869データ設定
  setOr31869DataFromInitial(resData)

  // 計画期間リスト設定（ソート順：開始日 降順、終了日 降順、計画期間ＩＤ 降順）
  const planPeriodList = resData.copymeetingminutesplanningperiodlist
    .map((item) => ({
      sc1Id: item.sc1Id,
      startYmd: item.startYmd,
      endYmd: item.endYmd,
      planPeriod: item.startYmd + '～' + item.endYmd,
      recordsCnt: item.recordsCnt ?? '',
      svJigyoKnj: item.svJigyoKnj ?? '',
    }))
    .sort((a, b) => {
      // 開始日 降順
      if (a.startYmd !== b.startYmd) {
        return (b.startYmd ?? '').localeCompare(a.startYmd ?? '')
      }
      // 終了日 降順
      if (a.endYmd !== b.endYmd) {
        return (b.endYmd ?? '').localeCompare(a.endYmd ?? '')
      }
      // 計画期間ＩＤ 降順
      return (b.sc1Id ?? '').localeCompare(a.sc1Id ?? '')
    })

  // 履歴リスト設定（ソート順：作成日 降順、履歴ＩＤ 降順）
  const histList = resData.copymeetingminutesheaderlist
    .map((item) => ({
      kaigi1Id: item.kaigi1Id,
      sc1Id: item.sc1Id,
      createYmd: item.createYmd,
      kaigiYmd: item.kaigiYmd,
      whereKnj: item.whereKnj ?? '',
      timeHm: item.timeHm ?? '',
      kaisuu: item.kaisuu ?? '',
      shokuName: item.shokuName,
      jigyoKnj: item.jigyoknj ?? '',
    }))
    .sort((a, b) => {
      // 作成日 降順
      if (a.createYmd !== b.createYmd) {
        return b.createYmd.localeCompare(a.createYmd)
      }
      // 履歴ＩＤ 降順
      return b.kaigi1Id.localeCompare(a.kaigi1Id)
    })

  // 新形式履歴リスト設定（ソート順：作成日 降順、履歴ＩＤ 降順）
  const newCareHistList = resData.copymeetingminutesnewcareheaderlist
    .map((item) => ({
      kaigi1Id: item.kaigi1Id,
      sc1Id: item.sc1Id,
      createYmd: item.createYmd ?? '',
      kaigiYmd: item.createYmd ?? '',
      whereKnj: item.whereKnj ?? '',
      timeHm: item.timeHm ?? '',
      kaisuu: item.kaisuu ?? '',
      shokuName: item.shokuName ?? '',
      caseNo: item.caseNo ?? '',
      kaiteiFlg: item.kaiteiFlg ?? '',
      jigyoKnj: item.jigyoknj ?? '',
    }))
    .sort((a, b) => {
      // 作成日 降順
      if (a.createYmd !== b.createYmd) {
        return b.createYmd.localeCompare(a.createYmd)
      }
      // 履歴ＩＤ 降順
      return b.kaigi1Id.localeCompare(a.kaigi1Id)
    })

  // データ保存
  localOneway.Or31928DataType.planPeriodList = planPeriodList
  localOneway.Or31928DataType.histList = histList
  localOneway.Or31928DataType.newCareHistList = newCareHistList
  localOneway.Or31928DataType.heldInfoList = resData.copymeetingminutesheldInfolist
  localOneway.Or31928DataType.attendeesList = resData.copymeetingminutesattendeesList
  localOneway.Or31928DataType.newCareHeldList = resData.copymeetingminutesnewcareheldInfolist
  localOneway.Or31928DataType.newCareAttendeesList = resData.copymeetingminutesnewcareattendeesList

  historyListType.value = histList
  newCareHistoryListType.value = newCareHistList
}

/**
 * 計画期間データ処理
 *
 * @param planPeriodId - 計画期間ID
 *
 * @param response - APIレスポンス
 */
function processPlanPeriodData(
  planPeriodId: string,
  response: MeetingMinuteCopyPlanPeriodSelectOutEntity
) {
  const resData = response.data
  if (!resData) return

  // Or31869データ更新
  setOr31869DataFromPlanPeriod(resData)

  // 履歴リスト更新（ソート順：作成日 降順、履歴ＩＤ 降順）
  const filteredHistList = resData.copymeetingminutesheaderlist
    .filter((x) => x.sc1Id === planPeriodId)
    .map((item) => {
      const matchedPeriod = localOneway.Or31928DataType.planPeriodList.find(
        (period) => period.sc1Id === item.sc1Id
      )
      const jigyoKnj = matchedPeriod ? matchedPeriod.svJigyoKnj : ''

      return {
        kaigi1Id: item.kaigi1Id,
        sc1Id: item.sc1Id,
        createYmd: item.createYmd,
        kaigiYmd: item.kaigiYmd,
        shokuName: item.shokuName,
        whereKnj: item.whereKnj ?? '',
        timeHm: item.timeHm ?? '',
        kaisuu: item.kaisuu ?? '',
        svJigyoKnj: jigyoKnj,
      }
    })
    .sort((a, b) => {
      // 作成日 降順
      if (a.createYmd !== b.createYmd) {
        return (b.createYmd ?? '').localeCompare(a.createYmd ?? '')
      }
      // 履歴ＩＤ 降順
      return (b.kaigi1Id ?? '').localeCompare(a.kaigi1Id ?? '')
    })

  // 新形式履歴リスト更新（ソート順：作成日 降順、履歴ＩＤ 降順）
  const filteredNewCareHistList = resData.copymeetingminutesnewcareheaderlist
    .filter((x) => x.sc1Id === planPeriodId)
    .map((item) => {
      const matchedPeriod = localOneway.Or31928DataType.planPeriodList.find(
        (period) => period.sc1Id === item.sc1Id
      )
      const jigyoKnj = matchedPeriod ? matchedPeriod.svJigyoKnj : ''

      return {
        kaigi1Id: item.kaigi1Id,
        sc1Id: item.sc1Id,
        createYmd: item.createYmd ?? '',
        kaigiYmd: item.createYmd ?? '',
        shokuName: item.shokuName ?? '',
        whereKnj: item.whereKnj ?? '',
        timeHm: item.timeHm ?? '',
        kaisuu: item.kaisuu ?? '',
        caseNo: item.caseNo ?? '',
        kaiteiFlg: item.kaiteiFlg ?? '',
        svJigyoKnj: jigyoKnj,
      }
    })
    .sort((a, b) => {
      // 作成日 降順
      if (a.createYmd !== b.createYmd) {
        return (b.createYmd ?? '').localeCompare(a.createYmd ?? '')
      }
      // 履歴ＩＤ 降順
      return (b.kaigi1Id ?? '').localeCompare(a.kaigi1Id ?? '')
    })

  localOneway.Or31928DataType.histList = filteredHistList
  localOneway.Or31928DataType.newCareHistList = filteredNewCareHistList
}

/**
 * 履歴データ処理
 *
 * @param response - APIレスポンス
 */
function processHistoryData(response: MeetingMinuteCopyHistorySelectOutEntity) {
  const resData = response.data
  if (!resData) return

  // Or31869データ更新
  setOr31869DataFromHistory(resData)
}

/**************************************************
 * Or31869データ設定
 **************************************************/

/**
 * 初期データからOr31869データ設定
 *
 * @param resData - APIレスポンス
 */
function setOr31869DataFromInitial(
  resData:
    | MeetingMinuteInfoCopySelectOutEntity['data']
    | MeetingMinuteCopyPlanPeriodSelectOutEntity['data']
    | MeetingMinuteCopyHistorySelectOutEntity['data']
) {
  if (props.onewayModelValue.cpnFlg === Or31928Const.ASSESS_METHOD.NEW_NURSING_HOME_PACKAGE_PLAN) {
    // NEW_NURSING_HOME_PACKAGE_PLANの場合: 全てnewcareデータを使用
    // 会議の詳細_イベント情報の新形式
    localComponents.or31869.heldInfo = {
      kaigi1Id: resData.copymeetingminutesnewcareheldInfolist[0]?.kaigi1Id ?? '',
      sc1Id: resData.copymeetingminutesnewcareheldInfolist[0]?.sc1Id ?? '',
      kaigiYmd: '', // newcareには開催日がない
      whereKnj: resData.copymeetingminutesnewcareheldInfolist[0]?.whereKnj ?? '',
      timeHm: resData.copymeetingminutesnewcareheldInfolist[0]?.timeHm ?? '',
      kaisuu: resData.copymeetingminutesnewcareheldInfolist[0]?.kaisuu ?? '',
      honninKnj: '', // newcareには本人情報がない
      kazokuKnj: '', // newcareには家族情報がない
      zokugaraKnj: '', // newcareには続柄情報がない
      bikoKnj: '', // newcareには備考がない
      riyuKnj: '', // newcareには欠席理由がない
      kentoKnj: resData.copymeetingminutesnewcareheldInfolist[0]?.kentoKnj ?? '',
      memoKnj: '', // newcareには検討内容がない
      ketsuronKnj: resData.copymeetingminutesnewcareheldInfolist[0]?.ketsuronKnj ?? '',
      kadaiKnj: resData.copymeetingminutesnewcareheldInfolist[0]?.kadaiKnj ?? '',
      jikaiYmd: resData.copymeetingminutesnewcareheldInfolist[0]?.jikaiYmd ?? '',
      modifiedCnt: resData.copymeetingminutesnewcareheldInfolist[0]?.modifiedCnt ?? '',
      sankaKbn: resData.copymeetingminutesnewcareheldInfolist[0]?.sankaKbn ?? '',
      sankaKnj: resData.copymeetingminutesnewcareheldInfolist[0]?.sankaKnj ?? '',
      naiyoknj: resData.copymeetingminutesnewcareheldInfolist[0]?.naiyoKnj ?? '',
    }

    // 会議の詳細_イベント情報の新形式
    localComponents.or31869.contentsInfo = {
      kaigi1Id: resData.copymeetingminutesnewcareheldInfolist[0]?.kaigi1Id ?? '',
      riyuKnj: '', // newcareには欠席理由がない
      kentoKnj: resData.copymeetingminutesnewcareheldInfolist[0]?.kentoKnj ?? '',
      memoKnj: '', // newcareには検討内容がない
      ketsuronKnj: resData.copymeetingminutesnewcareheldInfolist[0]?.ketsuronKnj ?? '',
      kadaiKnj: resData.copymeetingminutesnewcareheldInfolist[0]?.kadaiKnj ?? '',
      jikaiYmd: resData.copymeetingminutesnewcareheldInfolist[0]?.jikaiYmd ?? '',
      modifiedCnt: resData.copymeetingminutesnewcareheldInfolist[0]?.modifiedCnt ?? '',
      naiyoKnj: resData.copymeetingminutesnewcareheldInfolist[0]?.naiyoKnj ?? '',
      sogoHenkoKnj: resData.copymeetingminutesnewcareheldInfolist[0]?.sogoHenkoKnj ?? '',
      keikakuHenkoKnj: resData.copymeetingminutesnewcareheldInfolist[0]?.keikakuHenkoKnj ?? '',
      kessekiTaioKnj: resData.copymeetingminutesnewcareheldInfolist[0]?.kessekiTaioKnj ?? '',
      reasonAbsenceDisplay: '',
    }

    // 出席者ヘッダー情報
    localComponents.or31869.attendeesHeaderInfo = {
      khn13Id: '',
      sankaKbn: resData.copymeetingminutesnewcareheldInfolist[0]?.sankaKbn ?? '',
      sankaKnj: resData.copymeetingminutesnewcareheldInfolist[0]?.sankaKnj ?? '1',
      modifiedCnt: resData.copymeetingminutesnewcareheldInfolist[0]?.modifiedCnt ?? '',
      kinship: '',
    }

    // 出席者リスト設定 (newcare)
    const newCareAttendeesList =
      resData.copymeetingminutesnewcareattendeesList?.map(
        (item: ICopyMeetingMinuteNewCareAttendeesInfo) => ({
          ...item,
          kaigi1Id: item.kaigi1Id ?? '',
          kaigi2Id: item.kaigi2Id ?? '',
          seq: item.seq ?? '',
          shozokuKnj: item.shozokuKnj ?? '',
          shokushuKnj: item.shokushuKnj ?? '',
          nameKnj: item.nameKnj ?? '',
          tantoKnj: item.tantoKnj ?? '',
          shozoku2Knj: item.shozoku2Knj ?? '',
          shokushu2Knj: item.shokushu2Knj ?? '',
          name2Knj: item.name2Knj ?? '',
          tanto2Knj: item.tanto2Knj ?? '',
          modifiedCnt: item.modifiedCnt ?? '',
        })
      ) || []

    localComponents.or31869.attendeesList = newCareAttendeesList
  } else {
    // 通常の場合: 全て通常データを使用
    // 会議の詳細_イベント情報の旧形式
    localComponents.or31869.heldInfo = {
      kaigi1Id: resData.copymeetingminutesheldInfolist[0]?.kaigi1Id ?? '',
      sc1Id: resData.copymeetingminutesheldInfolist[0]?.sc1Id ?? '',
      kaigiYmd: resData.copymeetingminutesheldInfolist[0]?.kaigiYmd ?? '',
      whereKnj: resData.copymeetingminutesheldInfolist[0]?.whereKnj ?? '',
      timeHm: resData.copymeetingminutesheldInfolist[0]?.timeHm ?? '',
      kaisuu: resData.copymeetingminutesheldInfolist[0]?.kaisuu ?? '',
      honninKnj: resData.copymeetingminutesheldInfolist[0]?.honninKnj ?? '',
      kazokuKnj: resData.copymeetingminutesheldInfolist[0]?.kazokuKnj ?? '',
      zokugaraKnj: resData.copymeetingminutesheldInfolist[0]?.zokugaraKnj ?? '',
      bikoKnj: resData.copymeetingminutesheldInfolist[0]?.bikoKnj ?? '',
      riyuKnj: resData.copymeetingminutesheldInfolist[0]?.riyuKnj ?? '',
      kentoKnj: resData.copymeetingminutesheldInfolist[0]?.kentoKnj ?? '',
      memoKnj: resData.copymeetingminutesheldInfolist[0]?.memoKnj ?? '',
      ketsuronKnj: resData.copymeetingminutesheldInfolist[0]?.ketsuronKnj ?? '',
      kadaiKnj: resData.copymeetingminutesheldInfolist[0]?.kadaiKnj ?? '',
      jikaiYmd: resData.copymeetingminutesheldInfolist[0]?.jikaiYmd ?? '',
      modifiedCnt: resData.copymeetingminutesheldInfolist[0]?.modifiedCnt ?? '',
      sankaKbn: '',
      sankaKnj: '',
      naiyoknj: '',
    }

    // 会議の詳細_イベント情報の新形式 (通常データから)
    localComponents.or31869.contentsInfo = {
      kaigi1Id: resData.copymeetingminutesheldInfolist[0]?.kaigi1Id ?? '',
      riyuKnj: resData.copymeetingminutesheldInfolist[0]?.riyuKnj ?? '',
      kentoKnj: resData.copymeetingminutesheldInfolist[0]?.kentoKnj ?? '',
      memoKnj: resData.copymeetingminutesheldInfolist[0]?.memoKnj ?? '',
      ketsuronKnj: resData.copymeetingminutesheldInfolist[0]?.ketsuronKnj ?? '',
      kadaiKnj: resData.copymeetingminutesheldInfolist[0]?.kadaiKnj ?? '',
      jikaiYmd: resData.copymeetingminutesheldInfolist[0]?.jikaiYmd ?? '',
      modifiedCnt: resData.copymeetingminutesheldInfolist[0]?.modifiedCnt ?? '',
      naiyoKnj: '',
      sogoHenkoKnj: '',
      keikakuHenkoKnj: '',
      kessekiTaioKnj: '',
      reasonAbsenceDisplay: '',
    }

    // 出席者ヘッダー情報
    localComponents.or31869.attendeesHeaderInfo = {
      khn13Id: '',
      sankaKbn: '',
      sankaKnj: '1',
      modifiedCnt: resData.copymeetingminutesheldInfolist[0]?.modifiedCnt ?? '',
      kinship: '',
    }

    // 出席者リスト設定 (通常)
    const attendeesList =
      resData.copymeetingminutesattendeesList?.map((item: ICopyMeetingMinuteAttendeesInfo) => ({
        ...item,
        kaigi1Id: item.kaigi1Id ?? '',
        kaigi2Id: item.kaigi2Id ?? '',
        susseki: item.susseki ?? '',
        shussekiId: item.shussekiId ?? '',
        shozoku1Knj: item.shozoku1Knj ?? '',
        name1Knj: item.name1Knj ?? '',
        shozoku2Knj: item.shozoku2Knj ?? '',
        name2Knj: item.name2Knj ?? '',
        shozoku3Knj: item.shozoku3Knj ?? '',
        name3Knj: item.name3Knj ?? '',
        modifiedCnt: item.modifiedCnt ?? '',
      })) || []

    localComponents.or31869.attendeesList = attendeesList
  }

  localComponents.or31869.isCopy = true
}

/**
 * 計画期間データからOr31869データ設定
 *
 * @param resData - APIレスポンス
 */
function setOr31869DataFromPlanPeriod(resData: MeetingMinuteCopyPlanPeriodSelectOutEntity['data']) {
  if (props.onewayModelValue.cpnFlg === Or31928Const.ASSESS_METHOD.NEW_NURSING_HOME_PACKAGE_PLAN) {
    // NEW_NURSING_HOME_PACKAGE_PLANの場合: 全てnewcareデータを使用
    if (resData.copymeetingminutesnewcareheldInfolist?.length > 0) {
      localComponents.or31869.heldInfo = {
        kaigi1Id: resData.copymeetingminutesnewcareheldInfolist[0].kaigi1Id ?? '',
        sc1Id: resData.copymeetingminutesnewcareheldInfolist[0].sc1Id ?? '',
        kaigiYmd: '', // newcareには開催日がない
        whereKnj: resData.copymeetingminutesnewcareheldInfolist[0].whereKnj ?? '',
        timeHm: resData.copymeetingminutesnewcareheldInfolist[0].timeHm ?? '',
        kaisuu: resData.copymeetingminutesnewcareheldInfolist[0].kaisuu ?? '',
        honninKnj: '', // newcareには本人情報がない
        kazokuKnj: '', // newcareには家族情報がない
        zokugaraKnj: '', // newcareには続柄情報がない
        bikoKnj: '', // newcareには備考がない
        riyuKnj: '', // newcareには欠席理由がない
        kentoKnj: resData.copymeetingminutesnewcareheldInfolist[0].kentoKnj ?? '',
        memoKnj: '', // newcareには検討内容がない
        ketsuronKnj: resData.copymeetingminutesnewcareheldInfolist[0].ketsuronKnj ?? '',
        kadaiKnj: resData.copymeetingminutesnewcareheldInfolist[0].kadaiKnj ?? '',
        jikaiYmd: resData.copymeetingminutesnewcareheldInfolist[0].jikaiYmd ?? '',
        modifiedCnt: resData.copymeetingminutesnewcareheldInfolist[0].modifiedCnt ?? '',
        sankaKbn: resData.copymeetingminutesnewcareheldInfolist[0].sankaKbn ?? '',
        sankaKnj: resData.copymeetingminutesnewcareheldInfolist[0].sankaKnj ?? '',
        naiyoknj: resData.copymeetingminutesnewcareheldInfolist[0].naiyoKnj ?? '',
      }

      localComponents.or31869.contentsInfo = {
        kaigi1Id: resData.copymeetingminutesnewcareheldInfolist[0].kaigi1Id ?? '',
        riyuKnj: '', // newcareには欠席理由がない
        kentoKnj: resData.copymeetingminutesnewcareheldInfolist[0].kentoKnj ?? '',
        memoKnj: '', // newcareには検討内容がない
        ketsuronKnj: resData.copymeetingminutesnewcareheldInfolist[0].ketsuronKnj ?? '',
        kadaiKnj: resData.copymeetingminutesnewcareheldInfolist[0].kadaiKnj ?? '',
        jikaiYmd: resData.copymeetingminutesnewcareheldInfolist[0].jikaiYmd ?? '',
        modifiedCnt: resData.copymeetingminutesnewcareheldInfolist[0].modifiedCnt ?? '',
        naiyoKnj: resData.copymeetingminutesnewcareheldInfolist[0].naiyoKnj ?? '',
        sogoHenkoKnj: resData.copymeetingminutesnewcareheldInfolist[0].sogoHenkoKnj ?? '',
        keikakuHenkoKnj: resData.copymeetingminutesnewcareheldInfolist[0].keikakuHenkoKnj ?? '',
        kessekiTaioKnj: resData.copymeetingminutesnewcareheldInfolist[0].kessekiTaioKnj ?? '',
        reasonAbsenceDisplay: '',
      }

      localComponents.or31869.attendeesHeaderInfo = {
        khn13Id: '',
        sankaKbn: resData.copymeetingminutesnewcareheldInfolist[0].sankaKbn ?? '',
        sankaKnj: resData.copymeetingminutesnewcareheldInfolist[0].sankaKnj ?? '1',
        modifiedCnt: resData.copymeetingminutesnewcareheldInfolist[0].modifiedCnt ?? '',
        kinship: '',
      }
    }

    // 出席者リスト設定 (newcare)
    if (resData.copymeetingminutesnewcareattendeesList) {
      const newCareAttendeesList = resData.copymeetingminutesnewcareattendeesList.map(
        (item: ICopyMeetingMinuteNewCareAttendeesInfo) => ({
          ...item,
          kaigi1Id: item.kaigi1Id ?? '',
          kaigi2Id: item.kaigi2Id ?? '',
          seq: item.seq ?? '',
          shozokuKnj: item.shozokuKnj ?? '',
          shokushuKnj: item.shokushuKnj ?? '',
          nameKnj: item.nameKnj ?? '',
          tantoKnj: item.tantoKnj ?? '',
          shozoku2Knj: item.shozoku2Knj ?? '',
          shokushu2Knj: item.shokushu2Knj ?? '',
          name2Knj: item.name2Knj ?? '',
          tanto2Knj: item.tanto2Knj ?? '',
          modifiedCnt: item.modifiedCnt ?? '',
        })
      )
      localComponents.or31869.attendeesList = newCareAttendeesList
    }
  } else {
    // 通常の場合: 全て通常データを使用
    if (resData.copymeetingminutesheldInfolist?.length > 0) {
      localComponents.or31869.heldInfo = {
        kaigi1Id: resData.copymeetingminutesheldInfolist[0].kaigi1Id ?? '',
        sc1Id: resData.copymeetingminutesheldInfolist[0].sc1Id ?? '',
        kaigiYmd: resData.copymeetingminutesheldInfolist[0].kaigiYmd ?? '',
        whereKnj: resData.copymeetingminutesheldInfolist[0].whereKnj ?? '',
        timeHm: resData.copymeetingminutesheldInfolist[0].timeHm ?? '',
        kaisuu: resData.copymeetingminutesheldInfolist[0].kaisuu ?? '',
        honninKnj: resData.copymeetingminutesheldInfolist[0].honninKnj ?? '',
        kazokuKnj: resData.copymeetingminutesheldInfolist[0].kazokuKnj ?? '',
        zokugaraKnj: resData.copymeetingminutesheldInfolist[0].zokugaraKnj ?? '',
        bikoKnj: resData.copymeetingminutesheldInfolist[0].bikoKnj ?? '',
        riyuKnj: resData.copymeetingminutesheldInfolist[0].riyuKnj ?? '',
        kentoKnj: resData.copymeetingminutesheldInfolist[0].kentoKnj ?? '',
        memoKnj: resData.copymeetingminutesheldInfolist[0].memoKnj ?? '',
        ketsuronKnj: resData.copymeetingminutesheldInfolist[0].ketsuronKnj ?? '',
        kadaiKnj: resData.copymeetingminutesheldInfolist[0].kadaiKnj ?? '',
        jikaiYmd: resData.copymeetingminutesheldInfolist[0].jikaiYmd ?? '',
        modifiedCnt: resData.copymeetingminutesheldInfolist[0].modifiedCnt ?? '',
        sankaKbn: '',
        sankaKnj: '',
        naiyoknj: '',
      }

      localComponents.or31869.contentsInfo = {
        kaigi1Id: resData.copymeetingminutesheldInfolist[0].kaigi1Id ?? '',
        riyuKnj: resData.copymeetingminutesheldInfolist[0].riyuKnj ?? '',
        kentoKnj: resData.copymeetingminutesheldInfolist[0].kentoKnj ?? '',
        memoKnj: resData.copymeetingminutesheldInfolist[0].memoKnj ?? '',
        ketsuronKnj: resData.copymeetingminutesheldInfolist[0].ketsuronKnj ?? '',
        kadaiKnj: resData.copymeetingminutesheldInfolist[0].kadaiKnj ?? '',
        jikaiYmd: resData.copymeetingminutesheldInfolist[0].jikaiYmd ?? '',
        modifiedCnt: resData.copymeetingminutesheldInfolist[0].modifiedCnt ?? '',
        naiyoKnj: '',
        sogoHenkoKnj: '',
        keikakuHenkoKnj: '',
        kessekiTaioKnj: '',
        reasonAbsenceDisplay: '',
      }

      localComponents.or31869.attendeesHeaderInfo = {
        khn13Id: '',
        sankaKbn: '',
        sankaKnj: '1',
        modifiedCnt: resData.copymeetingminutesheldInfolist[0].modifiedCnt ?? '',
        kinship: '',
      }
    }

    // 出席者リスト設定 (通常)
    if (resData.copymeetingminutesattendeesList) {
      const attendeesList = resData.copymeetingminutesattendeesList.map(
        (item: ICopyMeetingMinuteAttendeesInfo) => ({
          ...item,
          kaigi1Id: item.kaigi1Id ?? '',
          kaigi2Id: item.kaigi2Id ?? '',
          susseki: item.susseki ?? '',
          shussekiId: item.shussekiId ?? '',
          shozoku1Knj: item.shozoku1Knj ?? '',
          name1Knj: item.name1Knj ?? '',
          shozoku2Knj: item.shozoku2Knj ?? '',
          name2Knj: item.name2Knj ?? '',
          shozoku3Knj: item.shozoku3Knj ?? '',
          name3Knj: item.name3Knj ?? '',
          modifiedCnt: item.modifiedCnt ?? '',
        })
      )
      localComponents.or31869.attendeesList = attendeesList
    }
  }

  localComponents.or31869.isCopy = true
}

/**
 * 履歴データからOr31869データ設定
 *
 * @param resData - APIレスポンス
 */
function setOr31869DataFromHistory(resData: MeetingMinuteCopyHistorySelectOutEntity['data']) {
  if (props.onewayModelValue.cpnFlg === Or31928Const.ASSESS_METHOD.NEW_NURSING_HOME_PACKAGE_PLAN) {
    // NEW_NURSING_HOME_PACKAGE_PLANの場合: 全てnewcareデータを使用
    if (resData.copymeetingminutesnewcareheldInfolist?.length > 0) {
      localComponents.or31869.heldInfo = {
        kaigi1Id: resData.copymeetingminutesnewcareheldInfolist[0].kaigi1Id ?? '',
        sc1Id: resData.copymeetingminutesnewcareheldInfolist[0].sc1Id ?? '',
        kaigiYmd: '', // newcareには開催日がない
        whereKnj: resData.copymeetingminutesnewcareheldInfolist[0].whereKnj ?? '',
        timeHm: resData.copymeetingminutesnewcareheldInfolist[0].timeHm ?? '',
        kaisuu: resData.copymeetingminutesnewcareheldInfolist[0].kaisuu ?? '',
        honninKnj: '', // newcareには本人情報がない
        kazokuKnj: '', // newcareには家族情報がない
        zokugaraKnj: '', // newcareには続柄情報がない
        bikoKnj: '', // newcareには備考がない
        riyuKnj: '', // newcareには欠席理由がない
        kentoKnj: resData.copymeetingminutesnewcareheldInfolist[0].kentoKnj ?? '',
        memoKnj: '', // newcareには検討内容がない
        ketsuronKnj: resData.copymeetingminutesnewcareheldInfolist[0].ketsuronKnj ?? '',
        kadaiKnj: resData.copymeetingminutesnewcareheldInfolist[0].kadaiKnj ?? '',
        jikaiYmd: resData.copymeetingminutesnewcareheldInfolist[0].jikaiYmd ?? '',
        modifiedCnt: resData.copymeetingminutesnewcareheldInfolist[0].modifiedCnt ?? '',
        sankaKbn: resData.copymeetingminutesnewcareheldInfolist[0].sankaKbn ?? '',
        sankaKnj: resData.copymeetingminutesnewcareheldInfolist[0].sankaKnj ?? '',
        naiyoknj: resData.copymeetingminutesnewcareheldInfolist[0].naiyoKnj ?? '',
      }

      localComponents.or31869.contentsInfo = {
        kaigi1Id: resData.copymeetingminutesnewcareheldInfolist[0].kaigi1Id ?? '',
        riyuKnj: '', // newcareには欠席理由がない
        kentoKnj: resData.copymeetingminutesnewcareheldInfolist[0].kentoKnj ?? '',
        memoKnj: '', // newcareには検討内容がない
        ketsuronKnj: resData.copymeetingminutesnewcareheldInfolist[0].ketsuronKnj ?? '',
        kadaiKnj: resData.copymeetingminutesnewcareheldInfolist[0].kadaiKnj ?? '',
        jikaiYmd: resData.copymeetingminutesnewcareheldInfolist[0].jikaiYmd ?? '',
        modifiedCnt: resData.copymeetingminutesnewcareheldInfolist[0].modifiedCnt ?? '',
        naiyoKnj: resData.copymeetingminutesnewcareheldInfolist[0].naiyoKnj ?? '',
        sogoHenkoKnj: resData.copymeetingminutesnewcareheldInfolist[0].sogoHenkoKnj ?? '',
        keikakuHenkoKnj: resData.copymeetingminutesnewcareheldInfolist[0].keikakuHenkoKnj ?? '',
        kessekiTaioKnj: resData.copymeetingminutesnewcareheldInfolist[0].kessekiTaioKnj ?? '',
        reasonAbsenceDisplay: '',
      }

      localComponents.or31869.attendeesHeaderInfo = {
        khn13Id: '',
        sankaKbn: resData.copymeetingminutesnewcareheldInfolist[0].sankaKbn ?? '',
        sankaKnj: resData.copymeetingminutesnewcareheldInfolist[0].sankaKnj ?? '1',
        modifiedCnt: resData.copymeetingminutesnewcareheldInfolist[0].modifiedCnt ?? '',
        kinship: '',
      }
    }

    // 出席者リスト設定 (newcare)
    if (resData.copymeetingminutesnewcareattendeesList) {
      const newCareAttendeesList = resData.copymeetingminutesnewcareattendeesList.map(
        (item: ICopyMeetingMinuteNewCareAttendeesInfo) => ({
          ...item,
          kaigi1Id: item.kaigi1Id ?? '',
          kaigi2Id: item.kaigi2Id ?? '',
          seq: item.seq ?? '',
          shozokuKnj: item.shozokuKnj ?? '',
          shokushuKnj: item.shokushuKnj ?? '',
          nameKnj: item.nameKnj ?? '',
          tantoKnj: item.tantoKnj ?? '',
          shozoku2Knj: item.shozoku2Knj ?? '',
          shokushu2Knj: item.shokushu2Knj ?? '',
          name2Knj: item.name2Knj ?? '',
          tanto2Knj: item.tanto2Knj ?? '',
          modifiedCnt: item.modifiedCnt ?? '',
        })
      )
      localComponents.or31869.attendeesList = newCareAttendeesList
    }
  } else {
    // 通常の場合: 全て通常データを使用
    if (resData.copymeetingminutesheldInfolist?.length > 0) {
      localComponents.or31869.heldInfo = {
        kaigi1Id: resData.copymeetingminutesheldInfolist[0].kaigi1Id ?? '',
        sc1Id: resData.copymeetingminutesheldInfolist[0].sc1Id ?? '',
        kaigiYmd: resData.copymeetingminutesheldInfolist[0].kaigiYmd ?? '',
        whereKnj: resData.copymeetingminutesheldInfolist[0].whereKnj ?? '',
        timeHm: resData.copymeetingminutesheldInfolist[0].timeHm ?? '',
        kaisuu: resData.copymeetingminutesheldInfolist[0].kaisuu ?? '',
        honninKnj: resData.copymeetingminutesheldInfolist[0].honninKnj ?? '',
        kazokuKnj: resData.copymeetingminutesheldInfolist[0].kazokuKnj ?? '',
        zokugaraKnj: resData.copymeetingminutesheldInfolist[0].zokugaraKnj ?? '',
        bikoKnj: resData.copymeetingminutesheldInfolist[0].bikoKnj ?? '',
        riyuKnj: resData.copymeetingminutesheldInfolist[0].riyuKnj ?? '',
        kentoKnj: resData.copymeetingminutesheldInfolist[0].kentoKnj ?? '',
        memoKnj: resData.copymeetingminutesheldInfolist[0].memoKnj ?? '',
        ketsuronKnj: resData.copymeetingminutesheldInfolist[0].ketsuronKnj ?? '',
        kadaiKnj: resData.copymeetingminutesheldInfolist[0].kadaiKnj ?? '',
        jikaiYmd: resData.copymeetingminutesheldInfolist[0].jikaiYmd ?? '',
        modifiedCnt: resData.copymeetingminutesheldInfolist[0].modifiedCnt ?? '',
        sankaKbn: '',
        sankaKnj: '',
        naiyoknj: '',
      }

      localComponents.or31869.contentsInfo = {
        kaigi1Id: resData.copymeetingminutesheldInfolist[0].kaigi1Id ?? '',
        riyuKnj: resData.copymeetingminutesheldInfolist[0].riyuKnj ?? '',
        kentoKnj: resData.copymeetingminutesheldInfolist[0].kentoKnj ?? '',
        memoKnj: resData.copymeetingminutesheldInfolist[0].memoKnj ?? '',
        ketsuronKnj: resData.copymeetingminutesheldInfolist[0].ketsuronKnj ?? '',
        kadaiKnj: resData.copymeetingminutesheldInfolist[0].kadaiKnj ?? '',
        jikaiYmd: resData.copymeetingminutesheldInfolist[0].jikaiYmd ?? '',
        modifiedCnt: resData.copymeetingminutesheldInfolist[0].modifiedCnt ?? '',
        naiyoKnj: '',
        sogoHenkoKnj: '',
        keikakuHenkoKnj: '',
        kessekiTaioKnj: '',
        reasonAbsenceDisplay: '',
      }

      localComponents.or31869.attendeesHeaderInfo = {
        khn13Id: '',
        sankaKbn: '',
        sankaKnj: '1',
        modifiedCnt: resData.copymeetingminutesheldInfolist[0].modifiedCnt ?? '',
        kinship: '',
      }
    }

    // 出席者リスト設定 (通常)
    if (resData.copymeetingminutesattendeesList) {
      const attendeesList = resData.copymeetingminutesattendeesList.map(
        (item: ICopyMeetingMinuteAttendeesInfo) => ({
          ...item,
          kaigi1Id: item.kaigi1Id ?? '',
          kaigi2Id: item.kaigi2Id ?? '',
          susseki: item.susseki ?? '',
          shussekiId: item.shussekiId ?? '',
          shozoku1Knj: item.shozoku1Knj ?? '',
          name1Knj: item.name1Knj ?? '',
          shozoku2Knj: item.shozoku2Knj ?? '',
          name2Knj: item.name2Knj ?? '',
          shozoku3Knj: item.shozoku3Knj ?? '',
          name3Knj: item.name3Knj ?? '',
          modifiedCnt: item.modifiedCnt ?? '',
        })
      )
      localComponents.or31869.attendeesList = attendeesList
    }
  }

  localComponents.or31869.isCopy = true
}

/**
 * 初期UI設定
 */
function setupInitialUI() {
  if (props.onewayModelValue.kikanFlag === Or31928Const.FLAG.MANAGE_PERIOD) {
    if (
      props.onewayModelValue.cpnFlg !== Or31928Const.ASSESS_METHOD.NEW_NURSING_HOME_PACKAGE_PLAN
    ) {
      filteredHeaders.value = header1.headers.filter(
        (header) => header.key !== Or31928Const.NAME_OFFICE
      )
      showOffice.value = false
    } else {
      filteredHeaders.value = header2.headers.filter(
        (header) => header.key !== Or31928Const.NAME_OFFICE
      )
      showOffice.value = false
    }
  } else {
    if (
      props.onewayModelValue.cpnFlg !== Or31928Const.ASSESS_METHOD.NEW_NURSING_HOME_PACKAGE_PLAN
    ) {
      filteredHeaders.value = header1.headers
      showOffice.value = true
    } else {
      filteredHeaders.value = header2.headers.filter((header) => {
        if (!showWhereKnj.value && header.key === Or31928Const.WHERE_KNJ) return false
        if (!showHeldTime.value && header.key === Or31928Const.HELD_TIME) return false
        if (!showHeldNumber.value && header.key === Or31928Const.HELD_NUMBER) return false
        return true
      })
      showOffice.value = true
      showWhereKnj.value = false
      showHeldTime.value = false
      showHeldNumber.value = false
    }
  }
}

/**************************************************
 * イベントハンドラー
 **************************************************/

/**
 * 計画期間選択処理
 *
 * @param item - 計画期間
 */
async function onPeriodSelect(item: PlanPeriodType) {
  if (kikanSelectedRowId.value === item.sc1Id) return
  kikanSelectedRowId.value = item.sc1Id
  await loadPlanPeriodData(item.sc1Id)

  const firstHist =
    localOneway.Or31928DataType.histList[0] || localOneway.Or31928DataType.newCareHistList[0]
  if (firstHist) {
    rirekiSelectedRowId.value = firstHist.kaigi1Id
    updateOr31869Display()
  }
}

/**
 * 履歴選択処理
 *
 * @param item - 履歴
 */
async function onHistorySelect(item: HistType | newCareHistType) {
  if (rirekiSelectedRowId.value === item.kaigi1Id) return
  rirekiSelectedRowId.value = item.kaigi1Id
  await loadHistoryData(item.kaigi1Id, item.sc1Id)
  updateOr31869Display()
}

/**
 * Or31869表示更新
 */
function updateOr31869Display() {
  local.or31869 = {
    ...localComponents.or31869,
    isCopy: true,
  }

  localOneway.Or31869Oneway = JSON.parse(JSON.stringify(localComponents.or31869)) as Or31869Type

  setChildCpBinds(props.uniqueCpId, {
    Or31869_0: {
      twoWayValue: {
        heldInfoList: local.or31869.heldInfo,
        attendeesList: local.or31869.attendeesList,
        newCareHeldList: local.or31869.contentsInfo,
        newCareAttendeesList: local.or31869.attendeesHeaderInfo,
      },
    },
  })
}

/**
 * 初期化処理
 */
async function init() {
  await loadInitialData(systemCommonsStore.getUserId)

  if (props.onewayModelValue.kikanFlag === Or31928Const.FLAG.MANAGE_PERIOD) {
    const firstPeriod = localOneway.Or31928DataType.planPeriodList[0]
    if (firstPeriod) {
      kikanSelectedRowId.value = firstPeriod.sc1Id
      const filteredHistList = localOneway.Or31928DataType.histList.filter(
        (h) => h.sc1Id === firstPeriod.sc1Id
      )
      const filteredNewCareHistList = localOneway.Or31928DataType.newCareHistList.filter(
        (h) => h.sc1Id === firstPeriod.sc1Id
      )
      localOneway.Or31928DataType.histList = filteredHistList
      localOneway.Or31928DataType.newCareHistList = filteredNewCareHistList
      const firstHist = filteredHistList[0] || filteredNewCareHistList[0]
      if (firstHist) {
        rirekiSelectedRowId.value = firstHist.kaigi1Id
        updateOr31869Display()
      }
    }
  } else {
    const firstHist =
      localOneway.Or31928DataType.histList[0] || localOneway.Or31928DataType.newCareHistList[0]
    if (firstHist) {
      rirekiSelectedRowId.value = firstHist.kaigi1Id
      updateOr31869Display()
    }
  }
}

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      setChildCpBinds(props.uniqueCpId, {
        Or31869_0: {
          twoWayValue: {
            heldInfoList: local.or31869.heldInfo,
            attendeesList: local.or31869.attendeesList,
            newCareHeldList: local.or31869.contentsInfo,
            newCareAttendeesList: local.or31869.attendeesHeaderInfo,
          },
        },
      })
    }
  }
)

/**
 * ダイアログを閉じる処理
 *
 * @description
 */
function close() {
  // 画面を閉じる。
  setState({ isOpen: false })
}

/**
 * AC007 確定ボタン押下時の処理
 */
async function onClickConfirm() {
  // 履歴一覧が選択しない場合
  // メッセージID：i.cmn.11289
  // メッセージ内容："履歴を選択してください。"
  if (
    localOneway.Or31928DataType.histList === undefined ||
    localOneway.Or31928DataType.histList.length === Or31928Const.DEFAULT.ZERO_VALUE ||
    localOneway.Or31928DataType.newCareHistList === undefined ||
    localOneway.Or31928DataType.newCareHistList.length === Or31928Const.DEFAULT.ZERO_VALUE
  ) {
    Or21815Logic.state.set({
      uniqueCpId: or21815.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.i-cmn-11289'),
      },
    })
    // OK：処理終了。
    return
  } else {
    // 履歴一覧が選択する場合
    const dialogResult = await openConfirmDialog(
      t('message.i-cmn-10193', [t('label.meeting-minutes')])
    )
    if (dialogResult === Or31928Const.YES) {
      // 返却情報.初期設定マスタの情報.パッケージプラン改訂フラグ
      // ＝ 詳細対象.総合計画IDを複写総合計画IDにして、履歴リスト.様式区分
      rtnDataSet()
      // 処理終了
      return
    }
  }
}

// 戻り値設定
function rtnDataSet() {
  emit('copyData', localOneway.Or31928DataType)
  // 画面閉じる
  close()
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no
 */
async function openConfirmDialog(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 利用者選択
 *
 * @param userSelfId - 利用者ID
 */
async function onChangeUserSelect(userSelfId: string) {
  await loadInitialData(userSelfId)

  if (props.onewayModelValue.kikanFlag === Or31928Const.FLAG.MANAGE_PERIOD) {
    const firstPeriod = localOneway.Or31928DataType.planPeriodList[0]
    if (firstPeriod) {
      kikanSelectedRowId.value = firstPeriod.sc1Id
      const filteredHistList = localOneway.Or31928DataType.histList.filter(
        (h) => h.sc1Id === firstPeriod.sc1Id
      )
      const filteredNewCareHistList = localOneway.Or31928DataType.newCareHistList.filter(
        (h) => h.sc1Id === firstPeriod.sc1Id
      )
      localOneway.Or31928DataType.histList = filteredHistList
      localOneway.Or31928DataType.newCareHistList = filteredNewCareHistList
      const firstHist = filteredHistList[0] || filteredNewCareHistList[0]
      if (firstHist) {
        rirekiSelectedRowId.value = firstHist.kaigi1Id
        updateOr31869Display()
      }
    }
  } else {
    const firstHist =
      localOneway.Or31928DataType.histList[0] || localOneway.Or31928DataType.newCareHistList[0]
    if (firstHist) {
      rirekiSelectedRowId.value = firstHist.kaigi1Id
      updateOr31869Display()
    }
  }
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => OrX0077Logic.state.get(orx0077.value.uniqueCpId),
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue?.isOpen) {
      close()
    }
  }
)
</script>
<template>
  <!-- 複写テンプレート -->
  <g-custom-or-x-0077
    v-bind="orx0077"
    :oneway-model-value="localOneway.orx0077Oneway"
    @on-change-user-select="onChangeUserSelect"
  >
    <!-- テーブル -->
    <template #filter>
      <c-v-row class="table-data ga-2">
        <c-v-col
          v-if="props.onewayModelValue.kikanFlag === Or31928Const.FLAG.MANAGE_PERIOD"
          class="pa-0"
          style="max-width: 450px"
        >
          <c-v-data-table
            :items="localOneway.Or31928DataType.planPeriodList"
            :headers="headers"
            :hide-default-footer="true"
            class="table-header table-wrapper"
            height="194px"
            :items-per-page="-1"
            hover
            fixed-header
          >
            <!-- 一覧 -->
            <template #item="{ item }">
              <tr
                :class="{ 'row-selected': kikanSelectedRowId === item.sc1Id }"
                @click="onPeriodSelect(item)"
              >
                <!-- 計画期間 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.planPeriod,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 期間内履歴数 -->
                <td class="text-align-right">
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.recordsCnt,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 事業所名 -->
                <td
                  v-if="
                    props.onewayModelValue.syubetsuID !== Or31928Const.TYPE_ID.TYPE_4 &&
                    props.onewayModelValue.syubetsuID !== Or31928Const.TYPE_ID.TYPE_6 &&
                    props.onewayModelValue.syubetsuID !== Or31928Const.TYPE_ID.TYPE_7
                  "
                >
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.svJigyoKnj,
                    }"
                  ></base-mo01337>
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
        <c-v-col>
          <!-- 通常 -->
          <c-v-data-table
            v-if="
              props.onewayModelValue.cpnFlg !==
              Or31928Const.ASSESS_METHOD.NEW_NURSING_HOME_PACKAGE_PLAN
            "
            :items="localOneway.Or31928DataType.histList"
            :headers="filteredHeaders"
            :hide-default-footer="true"
            class="table-header table-wrapper history-table"
            height="164px"
            :items-per-page="-1"
            hover
            fixed-header
          >
            <template #item="{ item }">
              <tr
                :class="{ 'row-selected': rirekiSelectedRowId === item.kaigi1Id }"
                @click="() => onHistorySelect(item)"
              >
                <!-- 作成日 -->
                <td>
                  <base-mo01337 :oneway-model-value="{ value: item.createYmd }" />
                </td>
                <!-- 作成者氏名 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.shokuName,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 開催日 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.kaigiYmd,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 開催場所 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.whereKnj,
                    }"
                  ></base-mo01337>
                </td>
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.timeHm,
                    }"
                  ></base-mo01337>
                </td>
                <td class="text-align-right">
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.kaisuu,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 事業所名 -->
                <td v-if="showOffice">
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.jigyoKnj,
                    }"
                  ></base-mo01337>
                </td>
              </tr>
            </template>
          </c-v-data-table>
          <!-- 新型養護 -->
          <c-v-data-table
            v-if="
              props.onewayModelValue.cpnFlg ===
              Or31928Const.ASSESS_METHOD.NEW_NURSING_HOME_PACKAGE_PLAN
            "
            :items="localOneway.Or31928DataType.newCareHistList"
            :headers="filteredHeaders"
            :hide-default-footer="true"
            class="table-header table-wrapper"
            height="164px"
            :items-per-page="-1"
            hover
            fixed-header
          >
            <template #item="{ item }">
              <tr
                :class="{ 'row-selected': rirekiSelectedRowId === item.kaigi1Id }"
                @click="() => onHistorySelect(item)"
              >
                <!-- 履歴ID -->
                <td>
                  <base-mo01337 :oneway-model-value="{ value: item.kaigiYmd }" />
                </td>
                <!-- 作成者氏名 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.shokuName,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 開催場所 -->
                <td v-if="showWhereKnj">
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.whereKnj,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 開催時間 -->
                <td v-if="showHeldTime">
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.timeHm,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 開催回数 -->
                <td
                  v-if="showHeldNumber"
                  class="text-align-right"
                >
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.kaisuu,
                    }"
                  ></base-mo01337>
                </td>
                <!-- ケースNo -->
                <td>
                  <base-mo01336
                    :oneway-model-value="{
                      value: item.caseNo,
                      customClass: new CustomClass({
                        outerClass: 'mr-2',
                        outerStyle: 'background-color: rgba(0, 0, 0, 0);',
                        labelClass: 'ma-0',
                        itemClass: 'ml-0',
                        itemStyle: 'text-align: left !important;',
                      }),
                    }"
                  ></base-mo01336>
                </td>
                <!-- 改定フラグ -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.kaiteiFlg,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 事業所名 -->
                <td v-if="showOffice">
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.jigyoKnj,
                    }"
                  ></base-mo01337>
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>
    <!-- 複写body -->
    <template #copyMain>
      <c-v-row>
        <c-v-col class="copy-body">
          <g-custom-or-31869
            v-model="local.or31869"
            :unique-cp-id="or31869.uniqueCpId"
            :parent-unique-cp-id="props.uniqueCpId"
          />
        </c-v-col>
      </c-v-row>
    </template>
    <!-- フッターボタン -->
    <template #footer>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609OverwriteBtnOneway"
          class="mx-2"
          @click="onClickConfirm()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </g-custom-or-x-0077>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- Or21815:有機体:警告ダイアログ -->
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
@use '@/styles/base.scss';
.v-row {
  margin: -8px;
}
:deep(.v-col) {
  padding: 0px !important;
}
:deep(.v-card-text) {
  overflow-y: unset !important;
}
// 右寄せのCSS
.text-align-right {
  text-align: right;
}
:deep(.copy-body > div:first-of-type) {
  width: 100% !important;
}
</style>
