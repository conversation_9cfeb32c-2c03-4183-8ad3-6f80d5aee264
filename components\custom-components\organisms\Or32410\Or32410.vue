<script setup lang="ts">
/**
 * Or32410：有機体：GUI00678_［情報収集］画面（6）画面
 *
 * @description
 * ［情報収集］画面（6）
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51773Const } from '../Or51773/Or51773.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or32410Const } from './Or32410.constants'
import { useScreenTwoWayBind, useSetupChildProps } from '#imports'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
import type {
  InfoCollectionInfoType,
  RirekiInfo,
} from '~/types/cmn/business/components/TeX0005Type'
import type { Or32410OnewayType, Or32410Type } from '~/types/cmn/business/components/Or32410Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Or21744StateType } from '~/components/base-components/organisms/Or21744/Or21744.type'
import type { Mo01280OnewayType } from '~/types/business/components/Mo01280Type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or32410OnewayType
  uniqueCpId: string
  modelValue: Or32410Type
}

const props: Props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/

const { t } = useI18n()

// 子コンポーネント用変数
const or21815 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(0) })
const contentRef = ref<HTMLDivElement | null>(null)
const selectedRow = ref<{ groupIndex: number; subIndex: number } | null>(null)
const Mo0009OnewayModelValue: Mo00009OnewayType = {
  icon: true,
  btnIcon: 'edit_square',
  prependIcon: 'edit_square',
  density: 'compact',
}

const defaultOnewayModelValue: Or32410OnewayType = {
  periodManageFlag: '0',
  rirekiInfo: {} as RirekiInfo,
  deleteFlg: false,
}

const localOneway = reactive({
  or32410Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // アセスメント表タイトル
  mo01338Oneway: {
    value: t('label.relationship-with-society'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: 'contentTitle',
      itemStyle: 'font-size: 20px',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 全て[〇]コンポーネント
  mo00611OnewayAllRound: {
    btnLabel: t('btn.all-round'),
    width: '90px',
  },
  // 全て[×]コンポーネント
  mo00611OnewayAllWrong: {
    btnLabel: t('btn.all-wrong'),
    width: '90px',
  },
  // 全解除コンポーネント
  mo00611OnewayCancelAll: {
    btnLabel: t('btn.full-release'),
    width: '90px',
  },

  mo01280Oneway: {
    // デフォルト値の設定
    maxLength: 4000,
    rows: '3',
    class: 'area23',
  } as Mo01280OnewayType,
  // ******Visioラジオグループセクション******
  cnsiderOneway: {
    customClass: new CustomClass({ outerClass: 'radio-style' }),
    showItemLabel: false,
    itemLabel: '',
    name: 'body',
    inline: false,
    items: [],
  } as Mo00039OnewayType,
  or21744Oneway: {
    width: '100px',
    minWidth: '100px',
    disabled: false,
  } as Or21744StateType,
  or51775OnewayTypeOther: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '12',
    t3Cd: '',
    tableName: 'cpn_tuc_myg_ass2',
    columnName: '',
    assessmentMethod: '2',
    inputContents: '',
    shokuinId: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
})

const local = reactive({
  or32410: {
    ...props.modelValue,
  } as Or32410Type,
})

/**
 *  ラジオボタン初期化
 */
function initCodes() {
  localOneway.cnsiderOneway.items?.push({
    label: t('label.circle'),
    value: Or32410Const.DEFAULT.RIGHTSELECTED,
  })
  localOneway.cnsiderOneway.items?.push({
    label: t('label.wrong'),
    value: Or32410Const.DEFAULT.WRONGSELECTED,
  })
}

/**************************************************
 * Pinia
 **************************************************/
const { refValue: sortList } = useScreenTwoWayBind<Record<string, InfoCollectionInfoType[]>>({
  cpId: Or32410Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
sortList.value = { ...sortList.value }

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * ウォッチャー
 **************************************************/

/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(() => {
  // 初期情報取得
  void init()
})

/**
 * 初期化
 */
const init = () => {
  // ラジオボタン初期化
  void initCodes()

  // 階層1タイトルラベル(情報収集画面詳細情報の「第1階層ID」の値、情報収集画面詳細情報の「第1階層名称」の値の組合文字列)
  if (sortList.value?.['1'] !== undefined) {
    localOneway.mo01338Oneway.value =
      sortList.value['1'][0].level1Id + ' ' + sortList.value['1'][0].level1Knj
  }
}
/**
 * 全て[〇]ボタン押下
 *
 */
function onAllSelect1Click() {
  Object.entries(sortList.value!).map((group) => {
    group[1].map((item) => {
      item.kentoFlg = Or32410Const.DEFAULT.RIGHTSELECTED
    })
  })
}

/**
 * 全て[×]ボタン押下
 *
 */
function onAllSelect2Click() {
  Object.entries(sortList.value!).map((group) => {
    group[1].map((item) => {
      item.kentoFlg = Or32410Const.DEFAULT.WRONGSELECTED
    })
  })
}

/**
 * 全解除ボタン押下
 *
 */
function onNoSelectClick() {
  Object.entries(sortList.value!).map((group) => {
    group[1].map((item) => {
      item.kentoFlg = Or32410Const.DEFAULT.UNSELECTED
    })
  })
}

// 選択した行の第１階層Index
const selectedItemIndex = ref<string>('')

// 選択した行の第２階層Index
const selectedItemSubIndex = ref<number>(-1)

// 選択した行のメモ１ORメモ２
const selectedItemType = ref<number>(-1)
/**
 * 「ケアマネ入力支援アイコンボタン」押下onClickOther
 *
 * @param title - id
 *
 * @param index - 第１階層Index
 *
 * @param subIndex - 第２階層Index
 *
 * @param type - メモ１ORメモ２
 */
function onClickOther(title: string, index: string, subIndex: number, type: number) {
  // その他1の入力支援ポップアップを開く
  or51775Other.value.modelValue = ''

  selectedItemIndex.value = index
  selectedItemSubIndex.value = subIndex
  selectedItemType.value = type

  // タイトルを「その他1」または「その他2」に設定
  localOneway.or51775OnewayTypeOther.title = title

  // 入力支援ポップアップを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援ポップアップの表示状態を返すComputed`
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

//課題t入力
const or51775Other = ref({ modelValue: '' })

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or51773Const.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or51773Const.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  // メモ１の場合
  if (selectedItemType.value === 1) {
    if (sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].memo1Knj) {
      sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].memo1Knj.value =
        setOrAppendValue(
          sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].memo1Knj.value ?? '',
          data
        )
    }
  } else {
    if (sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].memo2Knj) {
      sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].memo2Knj.value =
        setOrAppendValue(
          sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].memo2Knj.value ?? '',
          data
        )
    }
  }
}

/**
 * スクロールバーのスクロール
 *
 * @param event - WheelEvent
 */
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()

  const delta = Math.sign(event.deltaY)

  const contentElement = contentRef.value
  if (!contentElement) return

  const currentScroll = contentElement.scrollTop
  const maxScroll = contentElement.scrollHeight - contentElement.clientHeight

  let newScroll = currentScroll + delta * 50

  if (newScroll < 0) newScroll = 0
  if (newScroll > maxScroll) newScroll = maxScroll

  if (newScroll !== currentScroll) {
    contentElement.scrollTo({
      top: newScroll,
      behavior: 'auto',
    })
  }
}

watch(
  () => local.or32410,
  () => {
    emit('update:modelValue', local.or32410)
  },
  { deep: true }
)
/**
 * 行を選択したときの処理
 *
 * @param groupIndex - 子行
 *
 * @param subIndex - 子行
 */
function onSelectRow(groupIndex: number, subIndex: number) {
  if (selectedRow.value?.groupIndex === groupIndex && selectedRow.value?.subIndex === subIndex) {
    selectedRow.value = null
  } else {
    selectedRow.value = { groupIndex, subIndex }
  }
}
</script>

<template>
  <!-- 対象期間管理しない場合、非表示 -->
  <c-v-row
    v-if="
      props.onewayModelValue.periodManageFlag !== Or32410Const.PLANNING_PERIOD_NO_MANAGE ||
      props.onewayModelValue.copyFlg === true
    "
    no-gutters
    style="padding: 8px;background-color: white;"
  >
    <!-- タイトル -->
    <c-v-col
      cols="6"
      class="h-100"
    >
      <c-v-row no-gutters>
        <base-mo01338
          :oneway-model-value="localOneway.mo01338Oneway"
          style="background: transparent"
        ></base-mo01338>
      </c-v-row>
    </c-v-col>
    <!-- 3ボタン -->
    <c-v-col
      cols="6"
      class="h-100"
    >
      <c-v-row
        v-if="!props.onewayModelValue.hiddenAction"
        no-gutters
        class="top-button"
        justify="end"
      >
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAllRound"
          class="mx-1"
          @click="onAllSelect1Click"
        />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAllWrong"
          class="mx-1"
          @click="onAllSelect2Click"
        />
        <base-mo00611
          style="margin-right: 0 !important;"
          :oneway-model-value="localOneway.mo00611OnewayCancelAll"
          class="mx-1"
          @click="onNoSelectClick"
        />
      </c-v-row>
    </c-v-col>
    <!-- タイトル -->
    <c-v-row no-gutters>
      <c-v-col class="h-100">
        <c-v-row
          no-gutters
          class="row-tl-border"
          style="width: calc(100% - 15px)"
        >
          <!-- No -->
          <c-v-col
            cols="auto"
            class="col-br-border tbl-title-bg title-text-center col1"
            ><label class="col-pl">{{ t('label.no') }}</label></c-v-col
          >
          <!-- 情報項目 -->
          <c-v-col class="col-br-border tbl-title-bg title-text-center col2"
            ><label class="col-pl">{{ t('label.info-collection-info-item') }}</label></c-v-col
          >
          <!-- 具体的状況 -->
          <c-v-col class="col-br-border tbl-title-bg title-text-center col3"
            ><label class="col-pl">{{ t('label.specific-situation') }}</label></c-v-col
          >
          <!-- 検討 -->
          <c-v-col
            cols="auto"
            class="col-br-border tbl-title-bg title-text-center col4"
            ><label class="col-pl">{{ t('label.info-collection-consider') }}</label></c-v-col
          >
        </c-v-row>
        <!-- body -->
        <div
          ref="contentRef"
          class="or32410-main-div"
          style="
            background: linear-gradient(
              to right,
              white calc(100% - 15px),
              transparent calc(100% - 15px)
            );
          "
        >
          <template
            v-for="(group, index) in sortList!"
            :key="index"
          >
            <div
              v-if="!props.onewayModelValue.deleteFlg"
              no-gutters
              class="row-l-border"
            >
              <c-v-row no-gutters>
                <c-v-col
                  cols="12"
                  class="col-br-border tbl-title-bg"
                  ><label class="font-bold col-pl">{{ group[0].level2Knj }}</label></c-v-col
                >
              </c-v-row>
              <c-v-row
                v-for="(item, subIndex) in group"
                :key="subIndex"
                no-gutters
                class="or32410-row"
                :class="{
                  'select-row':
                    selectedRow?.groupIndex === Number(index) && selectedRow?.subIndex === subIndex,
                }"
                @click="onSelectRow(Number(index), subIndex)"
              >
                <!-- No -->
                <c-v-col
                  v-if="item.shosiki1Flg === Or32410Const.DEFAULT.FORMAT_ONE"
                  cols="auto"
                  class="col-br-border align-items-center col1 text-bg"
                  style="justify-content: center"
                  ><label class="font-bold col-pl">{{ item.koumokuNo }}</label>
                </c-v-col>
                <!-- 情報項目 -->
                <c-v-col
                  v-if="item.shosiki1Flg === Or32410Const.DEFAULT.FORMAT_ONE"
                  class="col-br-border align-items-center col2 text-bg"
                  ><label class="font-bold col-pl">{{ item.level3Knj }}</label></c-v-col
                >
                <c-v-col
                  v-else
                  cols="2"
                  class="col-br-border align-items-center text-bg"
                  ><label class="col-pl">{{ item.level3Knj }}</label></c-v-col
                >
                <!-- 具体的状況 -->
                <c-v-col
                  v-if="item.shosiki1Flg === Or32410Const.DEFAULT.FORMAT_ONE"
                  class="col-br-border col3"
                >
                  <div style="display: flex">
                    <base-mo01280
                      v-model="item.memo1Knj"
                      :oneway-model-value="localOneway.mo01280Oneway"
                    >
                    </base-mo01280>
                    <c-v-divider
                      vertical
                      class="mr-1 mt-1 mb-1"
                    />
                    <base-mo00009
                      :oneway-model-value="Mo0009OnewayModelValue"
                      variant="flat"
                      density="compact"
                      style="margin-top: 18px;background: inherit"
                      @click="onClickOther(item.level3Knj, index, subIndex, 1)"
                    ></base-mo00009></div
                ></c-v-col>
                <c-v-col
                  v-else
                  cols="10"
                  class="col-br-border"
                >
                  <div style="display: flex">
                    <base-mo01280
                      v-model="item.memo1Knj"
                      :oneway-model-value="localOneway.mo01280Oneway"
                    >
                    </base-mo01280>
                    <c-v-divider
                      vertical
                      class="mr-1 mt-1 mb-1"
                    />
                    <base-mo00009
                      :oneway-model-value="Mo0009OnewayModelValue"
                      variant="flat"
                      density="compact"
                      style="margin-top: 18px;background: inherit"
                      @click="onClickOther(item.level3Knj, index, subIndex, 1)"
                    ></base-mo00009></div
                ></c-v-col>
                <!-- 検討 -->
                <c-v-col
                  v-if="item.shosiki1Flg === Or32410Const.DEFAULT.FORMAT_ONE"
                  cols="auto"
                  class="col-br-border radio-center col4"
                  ><div class="radio1">
                    <base-mo00039
                      v-model="item.kentoFlg"
                      style="background: inherit"
                      :oneway-model-value="localOneway.cnsiderOneway"
                    /></div
                ></c-v-col>
              </c-v-row>
            </div>
          </template>
        </div>
      </c-v-col>
    </c-v-row>
  </c-v-row>
  <div
    v-if="props.onewayModelValue.copyFlg === true"
    class="overlay"
    @wheel="handleWheel"
  ></div>
  <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Other"
    :oneway-model-value="localOneway.or51775OnewayTypeOther"
    @confirm="handleOr51775Confirm"
  >
  </g-custom-or-51775>
</template>

<style lang="scss" scoped>
@use '@/styles/base-data-table-list.scss';

.radio1 {
  :deep(.v-selection-control-group > div) {
    margin-bottom: -6px;
    margin-top: -8px;
  }
}

.row-tl-border {
  border-top: 1px rgb(var(--v-theme-black-200)) solid;
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

.row-l-border {
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-br-border {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-pl {
  padding-left: 4px !important;
}

.tbl-title-bg {
  background-color: rgb(var(--v-theme-black-100));
}

.top-button {
  padding-bottom: 8px;
}

.font-bold {
  font-weight: bold;
}

.align-items-center {
  display: flex;
  align-items: center;
}

.radio-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.or32410-main-div {
  overflow-y: auto;
  max-height: 510px;
  scrollbar-gutter: stable;
  position: relative;
  z-index: 999;
}

.or32410-row {
  height: 68px;
}

:deep(.col1) {
  width: 54px;
  flex-shrink: 0;
}

:deep(.col2) {
  flex-grow: 2;
}

:deep(.col3) {
  flex-grow: 3;
}

:deep(.col4) {
  width: 64px;
  flex-shrink: 0;
}

.title-text-center {
  text-align: center;
  height: 40px;
  padding-top: 10px !important;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 1000;
  pointer-events: auto;
  cursor: not-allowed;
}
.text-bg {
  background-color: rgb(242, 242, 242);
}
.full-width-field {
  padding: 0 3px !important;
}
// 選択した行のCSS
.select-row {
  background: rgb(var(--v-theme-blue-100)) !important;
}
</style>
