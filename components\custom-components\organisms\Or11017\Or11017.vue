<script setup lang="ts">
/**
 * Or11017:支援経過確認一覧
 * GUI01259_支援経過確認一覧
 *
 * @description
 * 支援経過確認一覧
 *
 * <AUTHOR> DAO DINH DUONG
 */
import { reactive, ref, watch, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or11017Const } from './Or11017.constants'
import type { Or11017StateType, ParamSogaiyouinList, CPType } from './Or11017.type'
import { Or01473Const } from '~/components/custom-components/organisms/Or01473/Or01473.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { useRoute, useScreenOneWayBind, useSetupChildProps, useScreenStore } from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Or11017OnewayType } from '~/types/cmn/business/components/Or11017Type'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import type { ParamComfirm } from '~/components/custom-components/organisms/Or01473/Or01473.type'
/**
 * 国際化関数の取得
 */
const { t } = useI18n()

/**
 * 親からのプロパティ
 */
const props = defineProps<Or11017OnewayType>()
const screenStore = useScreenStore()
/**
 * Or01473=のID
 */
const or01473 = ref<Or11017OnewayType>({ ...props, uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
useSetupChildProps(props.uniqueCpId, {
  [Or01473Const.CP_ID(0)]: or01473.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
})

/**
 * Mo00024の状態
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or11017Const.DEFAULT.IS_OPEN,
})

/**
 * こな度リストのパラメータ
 */
const paramSogaiyouinList = ref<ParamSogaiyouinList>()
/**
 * 削除確認ダイアログを表示する
 *
 * @param param
 */
const openPopupComfirm = (param: ParamComfirm) => {
  return new Promise((resolve) => {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        isOpen: true,
        dialogText: param.message,
        dialogTitle: t('label.confirm'),
        firstBtnType: 'normal1',
        firstBtnLabel: param?.firstBtnLabel || t('btn.yes'),
        secondBtnType: param?.secondBtnType || 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnLabel: t('btn.cancel'),
        thirdBtnType: param?.thirdBtnType || 'blank',
      },
    })
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        if (event?.firstBtnClickFlg) {
          if (param.excuteFunction) {
            void param.excuteFunction()
          }
          resolve(true)
        }
        if (event?.secondBtnClickFlg) {
          if (param.excuteFunction1) {
            void param.excuteFunction1()
          }
        }
        if (event?.thirdBtnClickFlg || event?.closeBtnClickFlg) {
          if (param.excuteFunction2) {
            void param.excuteFunction2()
          }
        }
        resolve(false)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
      },
      { once: true }
    )
  })
}

/**
 * OneWayバインドの状態管理
 */
const { setState } = useScreenOneWayBind<Or11017StateType>({
  cpId: Or11017Const.CP_ID(0), // CP IDを取得
  uniqueCpId: props.uniqueCpId, // ユニークなCP ID
  onUpdate: {
    /**
     * ダイアログ表示状態を更新
     *
     * @param value - 表示フラグ
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or11017Const.DEFAULT.IS_OPEN // isOpenの状態を更新
    },
    /**
     * パラメータ情報を更新
     *
     * @param value - 子コンポーネントからのデータ
     */
    paramSogaiyouinList: (value) => {
      paramSogaiyouinList.value = value
    },
  },
})
/**
 * ルート
 */
const route = useRoute()
/**
 * 保存権限
 */
const isPermissionRegist = ref<boolean>(true)
onMounted(async () => {
  // isPermissionRegist.value = await hasRegistAuth(route.path)
})
/**
 * OneWayバインド用のローカル状態
 */
const localOneway = reactive({
  // 認定情報入力ダイアログの設定
  mo00024Oneway: {
    persistent: true,
    showCloseBtn: true,
    width: '1180px',
    height: '70vh',
    mo01344Oneway: {
      name: 'Or11017',
      toolbarName: 'Or11017ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      toolbarTitle: t('支援経過確認一覧'),
    },
  } as Mo00024OnewayType,
  mo00611CloseBtnOneWay: {
    tooltipText: t('tooltip.screen-close'),
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    tooltipText: t('tooltip.save'),
    btnLabel: t('btn.save'),
  } as Mo00609OnewayType,
})
const or01473CP = ref<CPType>()
const isEdit = computed(() => screenStore.getCpNavControl(or01473.value.uniqueCpId))
/**
 * ダイアログを閉じる処理（非同期）
 *
 * @returns Promise
 */
const close = async () => {
  const closePopup = () => {
    setState({ isOpen: false })
  }
  const closeAndSave = async () => {
    await or01473CP.value!.onSave()
    closePopup()
  }
  if (isEdit.value) {
    const param: ParamComfirm = {
      message: t('message.i-cmn-10430'),
      excuteFunction: closeAndSave,
      excuteFunction1: closePopup,
      thirdBtnType: 'normal3',
    }
    openPopupComfirm(param)
  } else {
    closePopup()
  }
}
/**
 * 保存処理
 *
 */
const onSave = () => {
  or01473CP.value!.onSave()
}

/**
 * mo00024のisOpen監視
 * 組織ダイアログの自動クローズを手動判定に変更
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    mo00024.value.isOpen = true
    if (!newValue) {
      void close()
    }
  }
)
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    class="dialog-container"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <g-custom-or01473
        ref="or01473CP"
        v-bind="or01473"
      ></g-custom-or01473>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          :oneway-model-value="{
            ...localOneway.mo00609ConfirmOneway,
            disabled: !isPermissionRegist,
          }"
          class="ml-2"
          @click="onSave"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21814 v-bind="or21814" />
</template>
<style scoped lang="scss">
:deep(.dialog-container) {
  overflow: hidden;
}
</style>
