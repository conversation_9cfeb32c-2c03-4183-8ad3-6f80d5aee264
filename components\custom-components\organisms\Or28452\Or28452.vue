<script setup lang="ts">
/**
 * Or28452:有機体:(課題・目標取込)計画期間選択一覧
 *
 * @description
 * 計画期間一覧を表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import { onMounted, reactive, watch } from 'vue'
import { Or28452Const } from './Or28452.constants'
import type { Or28452Type, Or28452OneWayType } from '~/types/cmn/business/components/Or28452Type'
import type { Mo01334OnewayType, Mo01334Type } from '~/types/business/components/Mo01334Type'
import { useScreenTwoWayBind } from '#build/imports'
/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  modelValue?: Or28452Type
  /** 単方向モデル  */
  onewayModelValue?: Or28452OneWayType
}

/**
 * props
 */
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
/**
 * ローカルOneway
 */
const localOneway = reactive({
  or28452: {
    ...props.modelValue,
  },
  or28452OneWay: {
    ...props.onewayModelValue,
  } as Or28452OneWayType,
  mo01334Oneway: {
    headers: [],
    items: [],
    height: '140px'
  } as Mo01334OnewayType,
})

/**
 * refValue
 */
const { refValue } = useScreenTwoWayBind<Mo01334Type>({
  cpId: Or28452Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  localOneway.mo01334Oneway.headers = localOneway.or28452OneWay.headers
  localOneway.mo01334Oneway.items =  localOneway.or28452OneWay.items
})
/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue) {
      localOneway.or28452OneWay = {
        ...newValue,
      }
      localOneway.mo01334Oneway.headers = localOneway.or28452OneWay.headers
      localOneway.mo01334Oneway.items =  localOneway.or28452OneWay.items
    }
  },
  { deep: true }
)

/**************************************************
 * コンポーネント固有処理
 **************************************************/
</script>

<template>
  <c-v-col style="margin: 0px !important; padding: 0px !important;" class="table-header table-wrapper">
    <base-mo-01334
      v-if = "refValue"
      v-model="refValue"
      :oneway-model-value="localOneway.mo01334Oneway"
      width="550px"
      class="list-wrapper"
      :hide-default-footer="true"
      >
      <template #[`item.numberOfWithinThePeriodHistory`]="{ item }">
        <base-mo01337
          :oneway-model-value="{
          value: item.numberOfWithinThePeriodHistory,
            customClass:
              {
                outerStyle: 'background-color: rgba(0, 0, 0, 0); font-size: 12px !important;',
                itemClass: 'text-right',
              },
          }"></base-mo01337>
      </template>
    </base-mo-01334>
  </c-v-col>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
</style>
