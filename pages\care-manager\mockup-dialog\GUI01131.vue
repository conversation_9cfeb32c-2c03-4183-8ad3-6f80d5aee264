<script lang="ts" setup>
/**
 * Or10444:処理ロジック
 * GUI01131_［印刷設定］画面
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> DAO VAN DUONG
 */
import _ from 'lodash'
import {
  computed,
  definePageMeta,
  ref,
  useScreenStore,
  useSetupChildProps,
  useInitialize,
  nextTick
} from '#imports'
import type { Or10444OnewayType } from '~/types/cmn/business/components/Or10444Type'
import { Or10444Logic } from '~/components/custom-components/organisms/Or10444/Or10444.logic'
import { Or10444Const } from '~/components/custom-components/organisms/Or10444/Or10444.constants'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

const screenId = 'GUI01131'
const routing = 'GUI01131/pinia'
const screenName = 'GUI01131'
const screenStore = useScreenStore()
const Or10444 = ref({ uniqueCpId: '' })
const Or10444OnewayModel: Or10444OnewayType = {
  kikanFlg: '1',
  userId: '31',
  sectionName: '1',
  choIndex: '1',
  historyId: '3',
  careManagerInChargeSettingsFlag: '1',
  svJigyoId: '1',
  svJigyoIdList: ['1'],
  tantoShokuId: '0',
  kinounameKnj: '1',
  kojinhogoFlg: '1',
  sectionAddNo: '1',
  local: '',
  kaigiFlg: '1',
  shosikiFlg: '1',
  gamenKbn: '2',
  mode: '1'
}

screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01131' },
})

const pageComponent = screenStore.screen().supplement.pageComponent
Or10444.value.uniqueCpId = pageComponent.uniqueCpId

/**
 * 自身のPinia領域をセットアップ
 * - 現在の画面のコンポーネントIDとユニークコンポーネントIDを設定
 * - 子コンポーネントの情報を登録
 */
const { childCpIds } = useInitialize({
  cpId: 'GUI01131', // 現在の画面のコンポーネントID
  uniqueCpId: pageComponent.uniqueCpId, // ユニークコンポーネントID
  childCps: [{ cpId: Or10444Const.CP_ID(0) }], // 子コンポーネントの情報
})

Or10444Logic.initialize(childCpIds.Or10444.uniqueCpId)

/**
 * 子コンポーネントのプロパティを設定
 * - 子コンポーネントのユニークIDを設定
 */
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or10444Const.CP_ID(0)]: Or10444.value,
})

const isShowDialogOr10444 = computed(() => {
  return Or10444Logic.state.get(Or10444.value.uniqueCpId)?.isOpen ?? false
})
async function onClickOr10444(mode = '1') {
  Or10444OnewayModel.mode = mode
  await nextTick()
  Or10444Logic.state.set({
    uniqueCpId: Or10444.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters>
    <c-v-col> ケアマネモックアップ開発ダイヤログ画面確認用ページ </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- GUI01131_［印刷設定］画面 KMD DAO VAN DUONG 2025/05/30 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        class="block"
        variant="plain"
        @click="onClickOr10444('1')"
      >
        GUI01131_［印刷設定］画面 mode === '1'
      </v-btn>
      <v-btn
        class="block"
        variant="plain"
        @click="onClickOr10444('2')"
      >
        GUI01131_［印刷設定］画面 mode === '2'
      </v-btn>
      <v-btn
        class="block"
        variant="plain"
        @click="onClickOr10444('3')"
      >
        GUI01131_［印刷設定］画面 mode === '3'
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-10444
    v-if="isShowDialogOr10444"
    v-bind="Or10444"
    :oneway-model-value="Or10444OnewayModel"
  />
  <!-- GUI01131_［印刷設定］画面 KMD DAO VAN DUONG 2025/05/30 ADD END-->
</template>
<style lang="scss" scoped>
  .block {
    display: block;
  }
</style>
