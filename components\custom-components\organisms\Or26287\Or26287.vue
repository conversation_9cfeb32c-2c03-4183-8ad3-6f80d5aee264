<script setup lang="ts">
/**
 * Or26287:有機体:サービス選択モーダル
 * GUI01151_［サービス選択モーダル］画面
 *
 * @description
 * ［サービス選択モーダル］画面
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or26287Const } from './Or26287.constants'
import type { Or26287StateType, selectType, isCdSougouType } from './Or26287.type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
// import { useScreenOneWayBind, useSetupChildProps, useScreenStore } from '#imports'
import type { Mo01334OnewayType, Mo01334Type } from '~/types/business/components/Mo01334Type'
import { CustomClass } from '~/types/CustomClassType'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
// import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Or26287Type, Or26287OnewayType } from '~/types/cmn/business/components/Or26287Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Mo01376OnewayType, Mo01376Type } from '~/types/business/components/Mo01376Type'

import { Or21833Const } from '~/components/base-components/organisms/Or21833/Or21833.constants'
import { Or21834Const } from '~/components/base-components/organisms/Or21834/Or21834.constants'
import type { Mo00039Items } from '~/types/business/components/Mo00039Type'
// import { Or21833Logic } from '~/components/base-components/organisms/Or21833/Or21833.logic'
import type {
  ServiceSelectionIniSelectInEntity,
  ServiceSelectionIniSelectOutEntity,
} from '~/repositories/cmn/entities/ServiceSelectionIniSelectEntity'
const { t } = useI18n()

/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or26287Type
  onewayModelValue: Or26287OnewayType
  uniqueCpId: string
  parentCpId: string
}
const props = defineProps<Props>()

const or21833 = ref({ uniqueCpId: '' })
const or21834 = ref({ uniqueCpId: '' })
// 引継情報を取得する
// システム共有情報ストア
// const systemCommonsStore = useSystemCommonsStore()

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21833Const.CP_ID]: or21833.value,
  [Or21834Const.CP_ID]: or21834.value,
})

/**************************************************
 * 変数定義
 **************************************************/
// const isEdit = computed(() => {
//   return useScreenStore().getCpNavControl(props.uniqueCpId)
// })

// ローカル双方向bind
const local = reactive({
  kanrenSetKbn: { modelValue: false } as Mo00018Type,
  oldMo00045Left: '',
  // サービスコード
  mo00045Left: {
    value: '',
  } as Mo00045Type,
  // サービス項目コード
  mo00045Right: {
    value: '',
  } as Mo00045Type,
  // サービス種別
  serviceType1: {
    modelValue: true,
  } as Mo00018Type,
  serviceType2: {
    modelValue: false,
  } as Mo00018Type,
  serviceType3: {
    modelValue: false,
  } as Mo00018Type,
  serviceTypeChange: { modelValue: '' } as Mo00040Type,
  // 事業所番号
  mo00045CareerOffice: {
    value: '',
  } as Mo00045Type,
  // 事業所選択
  careerOfficeChange: { modelValue: '' } as Mo00040Type,
  // 日帰りショート
  dayTrip: {
    modelValue: false,
  } as Mo00018Type,

  // 総合事業種類コード選択
  chordTypeChange: { modelValue: '' } as Mo00040Type,

  startTime: { value: '00:00' },
  endTime: { value: '00:00' },
  // 利用者要介護度
  userChange: { modelValue: '' } as Mo00040Type,
  // 要介護度絞り込み
  careNeed: { modelValue: false } as Mo00018Type,
  // 曜日ラベル
  frequencymonth: { modelValue: false } as Mo00018Type,
  frequencyFire: { modelValue: false } as Mo00018Type,
  frequencyWater: { modelValue: false } as Mo00018Type,
  frequencyWood: { modelValue: false } as Mo00018Type,
  frequencyGold: { modelValue: false } as Mo00018Type,
  frequencySoil: { modelValue: false } as Mo00018Type,
  frequencyDay: { modelValue: false } as Mo00018Type,
  outsideWeek: { modelValue: false } as Mo00018Type,
  satrtDate: { value: '21' } as Mo00045Type,
  endDate: { value: '21' } as Mo00045Type,
  // 短期入所優先設定
  shortTrim: '1',
  // 施設区分
  constructionDifferentiation: { modelValue: '' } as Mo00040Type,
  // 人員配置
  staffing: { modelValue: '' } as Mo00040Type,
  // 療養機能強化型
  healingFunction: { modelValue: '' } as Mo00040Type,
  // 居室区分
  residentialDivision: { modelValue: '' } as Mo00040Type,
  // ユニットケア体制
  unitCareSystem: { modelValue: '' } as Mo00040Type,
  // 夜間勤務条件
  nightDutyConditions: { modelValue: '' } as Mo00040Type,
  // 超過・欠員
  exceedingStaff: { modelValue: '' } as Mo00040Type,
  // 基本項目一覧
  mo01334TypeBasicProject: {
    value: '',
    values: [],
  } as Mo01334Type,

  // コード
  mo00045Code: { value: '' } as Mo00045Type,
  // 加算サービス項目
  mo00045AdditionServiceItem: { value: '' } as Mo00045Type,
  // 加算項目一覧
  mo01334TypeAddProject: {
    value: '',
    values: [],
  } as Mo01334Type,

  isCdKaigo: [] as selectType[],
  isCdYobou: [] as selectType[],
  isCdSougou: [] as selectType[],

  // 総合事業種類コード選択
  mo00040OnewayChordTypeA1: [] as isCdSougouType[],
  mo00040OnewayChordTypeA5: [] as isCdSougouType[],
  mo00040OnewayChordTypeA9: [] as isCdSougouType[],
})

const localOneway = reactive({
  // 「サービス選択モーダル」ダイアログ
  mo00024Oneway: {
    width: '1400px',
    maxWidth: '1400px',
    height: '800px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.service-select'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  } as Mo00024OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  },
  mo00609AppendOneway: {
    btnLabel: t('btn.add'),
    tooltipText: t('tooltip.add'),
  },
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
    tooltipText: t('tooltip.confirm-btn'),
  },
  // 入力區分
  mo01338Oneway: {
    value: t('label.input-division'),
    customClass: new CustomClass({
      outerClass: 'ml-0',
      labelClass: 'ml-0',
      itemClass: 'ml-0',
    }),
  } as Mo01338OnewayType,
  // 追加
  mo00609AddOneway: {
    btnLabel: t('btn.add'),
  },
  mo00018OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.connection-office-display'),
    isVerticalLabel: true,
    showItemLabel: false,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00009OnewayMaster: {
    btnIcon: 'database',
    density: 'compact',
    size: '36px',
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.case-call-other'),
  },
  mo00009Oneway: {
    name: '3pointLeaderMenuIcon',
    btnIcon: 'more_horiz',
  } as Mo00009OnewayType,
  // サービスコードラベル
  mo00615Oneway: {
    itemLabel: t('label.service-code'),
    customClass: new CustomClass({ labelClass: 'ma-2 ml-0' }),
  } as Mo00615OnewayType,
  mo00045OnewayLeft: {
    //項目名ラベルの領域を非表示
    showItemLabel: false,
    width: '50px',
    maxLength: '2',
    isVerticalLabel: false,
    hideDetails: true,
  } as Mo00045OnewayType,

  // ハイフンラベル
  mo01338OnewayHyphen: {
    value: t('label.admission-meaning-btn-icon-3'),
    customClass: new CustomClass({
      outerClass: 'mr-2',
      labelClass: 'ml-0',
      itemClass: 'ml-0',
    }),
  } as Mo01338OnewayType,
  mo00045OnewayRight: {
    //項目名ラベルの領域を非表示
    showItemLabel: false,
    width: '70px',
    maxLength: '4',
    isVerticalLabel: false,
    hideDetails: true,
  } as Mo00045OnewayType,
  // サービス種別
  mo00615OnewayServiceType: {
    itemLabel: t('label.service-type'),
    customClass: new CustomClass({ labelClass: 'ma-2 ml-0' }),
  } as Mo00615OnewayType,
  mo00018Oneway1: {
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.care'),
  } as Mo00018OnewayType,
  mo00018Oneway2: {
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.prevention'),
  } as Mo00018OnewayType,
  mo00018Oneway3: {
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.comprehensive'),
  } as Mo00018OnewayType,
  // サービス種別選択
  mo00040OnewayServiceType: {
    showItemLabel: false,
    width: '180px',
    items: [],
    itemTitle: 'svShuruiName',
    itemValue: 'svShuruiCd',
  } as Mo00040OnewayType,
  // 事業所ラベル
  mo00615OnewayCareerOffice: {
    itemLabel: t('label.kotobimisho'),
    customClass: new CustomClass({ labelClass: 'ma-2 ml-0' }),
  } as Mo00615OnewayType,
  //事業所番号
  mo00045OnewayCareerOffice: {
    showItemLabel: false,
    width: '120px',
    maxLength: '10',
    isVerticalLabel: false,
    hideDetails: true,
  } as Mo00045OnewayType,
  // 事業所選択
  mo00040OnewayCareerOffice: {
    showItemLabel: false,
    width: '180px',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  // 日帰りショート
  mo00018OnewayDayTrip: {
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.day-trip'),
  } as Mo00018OnewayType,
  // 種類コードラベル
  mo00615OnewayChordType: {
    itemLabel: t('label.chord-type'),
    customClass: new CustomClass({ labelClass: 'ma-2 ml-0' }),
  } as Mo00615OnewayType,
  // 総合事業種類コード選択
  mo00040OnewayChordType: {
    showItemLabel: false,
    width: '180px',
    items: [],
    itemTitle: 'svShuruiName',
    itemValue: 'svShuruiCd',
  } as Mo00040OnewayType,
  // 時間
  mo00615OnewayTime: {
    itemLabel: t('label.time'),
    customClass: new CustomClass({ labelClass: 'ma-2 ml-0' }),
  } as Mo00615OnewayType,
  // 所要時間
  mo00615OnewayTimeRequired: {
    itemLabel: t('label.required-time'),
    customClass: new CustomClass({ labelClass: 'ma-2 ml-0' }),
  } as Mo00615OnewayType,

  mo01376Oneway: {
    inputMode: false,
    rangeInputMode: true,
    rangeHhFlg: false,
  } as Mo01376OnewayType,
  // 時間
  Mo01272OnewayType: {
    name: 'time',
    width: '100px',
    hideDetails: true,
    showItemLabel: false,
    customClass: new CustomClass({ outerClass: 'ma-0' }),
  } as Mo00045OnewayType,
  // コネクタ
  mo01338OnewayConnect: {
    value: t('label.and-new'),
    customClass: new CustomClass({ outerClass: 'ma-0' }),
  } as Mo01338OnewayType,
  // 所要時間
  mo01338OnewayRequiredTime: {
    value: '0.0h～0.0h',
    customClass: new CustomClass({ outerClass: 'ma-0 ml-2' }),
  } as Mo01338OnewayType,
  // 要介護度ラベル
  mo00615OnewayCareNeeds: {
    itemLabel: t('label.yokai-knj'),
    customClass: new CustomClass({ labelClass: 'ma-2 ml-0' }),
  } as Mo00615OnewayType,
  // 利用者要介護度
  mo00040OnewayUserChange: {
    showItemLabel: false,
    width: '180px',
    items: [],
    itemTitle: 'modifiedCnt',
    itemValue: 'yokaiKbn',
  } as Mo00040OnewayType,
  // 要介護度絞り込み
  mo00018OnewayCareNeed: {
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.care-need'),
  } as Mo00018OnewayType,

  // 曜日ラベル
  mo00615OnewayDayLabel: {
    itemLabel: t('label.jpy-day'),
    customClass: new CustomClass({ labelClass: 'ma-2 ml-0' }),
  } as Mo00615OnewayType,
  // 頻度週（月 火 水 木 金 土 日）
  mo00018OnewayMonth: {
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.weekly-plan-day-short-sunday'),
  } as Mo00018OnewayType,
  mo00018OnewayFire: {
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.weekly-plan-day-short-monday'),
  } as Mo00018OnewayType,
  mo00018OnewayWater: {
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.weekly-plan-day-short-tuesday'),
  } as Mo00018OnewayType,
  mo00018OnewayWood: {
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.weekly-plan-day-short-wednesday'),
  } as Mo00018OnewayType,
  mo00018OnewayGold: {
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.weekly-plan-day-short-thursday'),
  } as Mo00018OnewayType,
  mo00018OnewaySoil: {
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.weekly-plan-day-short-friday'),
  } as Mo00018OnewayType,
  mo00018OnewayDay: {
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.weekly-plan-day-short-saturday'),
  } as Mo00018OnewayType,
  // 週単位以外
  mo00018OnewayOutsideWeek: {
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.not-weekly'),
  } as Mo00018OnewayType,
  // 利用期間
  mo00615OnewayUtilzePeriod: {
    itemLabel: t('label.utilze-period'),
    customClass: new CustomClass({ labelClass: 'ma-2 ml-0' }),
  } as Mo00615OnewayType,
  // 開始日ラベル
  mo00045OnewayStartDate: {
    showItemLabel: true,
    itemLabel: t('label.start-date'),
    width: '50px',
    maxLength: '2',
    isVerticalLabel: false,
    hideDetails: true,
  } as Mo00045OnewayType,
  // 終了日ラベル
  mo00045OnewayEndtDate: {
    showItemLabel: true,
    itemLabel: t('label.end-date'),
    width: '50px',
    maxLength: '2',
    isVerticalLabel: false,
    hideDetails: true,
  } as Mo00045OnewayType,
  // 日
  mo00615OnewayDay: {
    itemLabel: t('label.day'),
    customClass: new CustomClass({ labelClass: 'ma-0 ml-0' }),
  } as Mo00615OnewayType,
  // 短期入所優先設定
  mo00615OnewayShortSetting: {
    itemLabel: t('label.short-term-setting'),
    customClass: new CustomClass({ labelClass: 'ma-2 ml-0' }),
  } as Mo00615OnewayType,
  // 病院・診療所
  mo00615OnewayHospitalClincis: {
    itemLabel: t('label.hospital-clinics'),
    customClass: new CustomClass({ labelClass: 'ma-2 ml-0' }),
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  // 施設区分
  mo00615OnewayConstructionDifferentiation: {
    itemLabel: t('label.construction-differentiation'),
    customClass: new CustomClass({ labelClass: 'ma-0 mb-2' }),
  } as Mo00615OnewayType,
  // 施設区分
  mo00040OnewayConstructionDifferentiation: {
    showItemLabel: false,
    width: '212px',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  // 人員配置
  mo00615OnewayStaffing: {
    itemLabel: t('label.staffing'),
    customClass: new CustomClass({ labelClass: 'mt-2 mb-2' }),
  } as Mo00615OnewayType,
  // 人員配置
  mo00040OnewayStaffing: {
    showItemLabel: false,
    width: '212px',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  // 療養機能強化型
  mo00615OnewayHealingFunction: {
    itemLabel: t('label.healing-function'),
    customClass: new CustomClass({ labelClass: 'mt-2 mb-2' }),
  } as Mo00615OnewayType,
  // 療養機能強化型
  mo00040OnewayHealingFunction: {
    showItemLabel: false,
    width: '212px',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  // 共通
  mo00615OnewayCommon: {
    itemLabel: t('label.common'),
    customClass: new CustomClass({ labelClass: 'ma-2 ml-0' }),
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  // 居室区分
  mo00615OnewayResidentialDivision: {
    itemLabel: t('label.residential-division'),
    customClass: new CustomClass({ labelClass: 'mt-0 mb-2' }),
  } as Mo00615OnewayType,
  // 居室区分
  mo00040OnewayResidentialDivision: {
    showItemLabel: false,
    width: '212px',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  // ユニットケア体制
  mo00615OnewayUnitCareSystem: {
    itemLabel: t('label.unit-care-system'),
    customClass: new CustomClass({ labelClass: 'mt-2 mb-2' }),
  } as Mo00615OnewayType,
  // ユニットケア体制
  mo00040OnewayUnitCareSystem: {
    showItemLabel: false,
    width: '212px',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  // 夜間勤務条件
  mo00615OnewayNightDutyConditions: {
    itemLabel: t('label.night-duty-conditions'),
    customClass: new CustomClass({ labelClass: 'mt-2 mb-2' }),
  } as Mo00615OnewayType,
  // 夜間勤務条件
  mo00040OnewayNightDutyConditions: {
    showItemLabel: false,
    width: '212px',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  // 超過・欠員
  mo00615OnewayExceedingStaff: {
    itemLabel: t('label.exceeding-staff'),
    customClass: new CustomClass({ labelClass: 'mt-2 mb-2' }),
  } as Mo00615OnewayType,
  // 超過・欠員
  mo00040OnewayExceedingStaff: {
    showItemLabel: false,
    width: '212px',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  // 基本サービス項目
  mo01337OnewayBasicCheck: {
    value: t('label.project-number'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: 'gokeiClass',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 基本項目一覧
  mo01334OnewayBasicProject: {
    headers: [
      { title: t('label.code'), key: 'jigyoKnj', sortable: false },
      { title: t('label.basic-project'), key: 'jigyoKnj', sortable: false, minWidth: '285px' },
      { title: t('label.required-time'), key: 'jigyoKnj', sortable: false },
      { title: t('label.classification'), key: 'jigyoKnj', sortable: false },
      { title: t('label.unit'), key: 'jigyoKnj', sortable: false },
    ],
    items: [],
    height: 224,
    showSelect: true,
    selectStrategy: 'all',
    mandatory: false,
  } as Mo01334OnewayType,
  // 加算絞り込み
  mo00615OnewayAdditionSqueeze: {
    itemLabel: t('label.addition-squeeze'),
    customClass: new CustomClass({ labelClass: 'mr-4 mb-2' }),
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  // コード
  mo00615OnewayCode: {
    itemLabel: t('label.code'),
    customClass: new CustomClass({ labelClass: 'mt-0 mb-2' }),
  } as Mo00615OnewayType,
  // コード
  mo00045OnewayCode: {
    showItemLabel: false,
    width: '70px',
    maxLength: '4',
    isVerticalLabel: false,
    hideDetails: true,
  } as Mo00045OnewayType,
  // 加算サービス項目
  mo00615AdditionServiceItem: {
    itemLabel: t('label.addition-service-item'),
    customClass: new CustomClass({ labelClass: 'mt-0 mb-2' }),
  } as Mo00615OnewayType,
  // 加算サービス項目
  mo00045OnewayAdditionServiceItem: {
    showItemLabel: false,
    width: '180px',
    maxLength: '64',
    isVerticalLabel: false,
    hideDetails: true,
  } as Mo00045OnewayType,
  // 加算サービス項目(単一)
  mo01337OnewayAddCheck: {
    value: t('label.project-number'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: 'gokeiClass',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 加算項目一覧
  mo01334OnewayAddProject: {
    headers: [
      { title: t('label.calculation'), key: 'jigyoKnj', sortable: false },
      { title: t('label.code'), key: 'jigyoKnj', sortable: false },
      { title: t('label.basic-project'), key: 'jigyoKnj', sortable: false, minWidth: '285px' },
      { title: t('label.classification'), key: 'jigyoKnj', sortable: false },
      { title: t('label.unit'), key: 'jigyoKnj', sortable: false },
    ],
    items: [],
    height: 224,
    showSelect: true,
    selectStrategy: 'all',
    mandatory: false,
  } as Mo01334OnewayType,
})

// 時間ラベル
const mo01376 = ref<Mo01376Type>({
  value: local.startTime.value ?? '',
  valueHour: getValueHour(local.startTime.value ?? '', 'hour'),
  valueMinute: getValueHour(local.startTime.value ?? '', 'minutes'),
  valueTo: local.startTime.value,
  valueHourTo: getValueHour(local.endTime.value ?? '', 'hour'),
  valueMinuteTo: getValueHour(local.endTime.value ?? '', 'minutes'),
  mo00024: {
    isOpen: false,
  },
  mo00038HourTens: {
    mo00045: {
      value: '0',
    },
  },
  mo00038HourOnes: {
    mo00045: {
      value: '0',
    },
  },
  mo00038MinuteTens: {
    mo00045: {
      value: '0',
    },
  },
  mo00038MinuteOnes: {
    mo00045: {
      value: '0',
    },
  },
})

const showDialog = computed(() => {
  return mo01376.value.mo00024.isOpen ?? false
})

watch(
  () => mo01376.value,
  (newValue) => {
    local.startTime.value = newValue.value
    local.endTime.value = newValue.valueTo ?? ''
  }
)

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or26287Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/

const { setState } = useScreenOneWayBind<Or26287StateType>({
  cpId: Or26287Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or26287Const.DEFAULT.IS_OPEN
    },
  },
})

//
const serviceType = ref('1')

/**
 * 出力帳票名一覧
 */
// const mo01334OnewayReport = ref<Mo01334OnewayType>({
//   headers: [
//     {
//       title: t('label.report'),
//       key: 'prtTitle',
//       sortable: false,
//       minWidth: '180',
//     },
//   ],
//   items: [],
//   height: 400,
// })
/**
 * 出力帳票名一覧
 */
// const mo01334TypeReport = ref<Mo01334Type>({
//   value: '',
//   values: [],
// } as Mo01334Type)

// const { refValue } = useScreenTwoWayBind<Or26287TwoWayData>({
//   cpId: Or26287Const.CP_ID(0),
//   uniqueCpId: props.uniqueCpId,
// })
onMounted(async () => {
  if (props.onewayModelValue.svtype === '1') {
    local.serviceType1.modelValue = true
    serviceType.value = '1'
  } else if (props.onewayModelValue.svtype === '2') {
    local.serviceType2.modelValue = true
    serviceType.value = '2'
  } else {
    local.serviceType3.modelValue = true
    serviceType.value = '3'
  }
  // 共通code
  await initCodes()
  // 初期化
  await init()
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_KAISUU }, // 394
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SMALL_SCALE_DIFFERENTIATION }, // 705
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SMALL_FAREWELL_DIFFERENTIATION }, // 706
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SMALL_POINT_SELECTION }, // 707
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SMALL_A_SERVICE_CALCULATION_UNIT }, // 708
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
}

/**
 * サービス選択画面初期情報を取得する。
 */
const init = async () => {
  const inputData: ServiceSelectionIniSelectInEntity = {
    mainParm: props.onewayModelValue.mainParm,
    kinouName: props.onewayModelValue.kinouName,
    yokaiKbn: props.onewayModelValue.yokaiKbn,
    yokaiKbn2: props.onewayModelValue.yokaiKbn2,
    termId: props.onewayModelValue.termId,
    chsGaibuFlg: props.onewayModelValue.chsGaibuFlg,
    teiYmdYm: props.onewayModelValue.teiYmdYm,
    svParm: props.onewayModelValue.svParm,
    svtype: props.onewayModelValue.svtype,
    hokenId: props.onewayModelValue.hokenId,
  }

  // バックエンドAPIから初期情報取得
  const res: ServiceSelectionIniSelectOutEntity = await ScreenRepository.select(
    'serviceSelectionIniSelect',
    inputData
  )
  // メインパラメータ
  local.mo00045Left.value = props.onewayModelValue.mainParm

  // 利用者要介護度
  localOneway.mo00040OnewayUserChange.items = res.data.ilYokaiKbn
  // サービス種別選択
  localOneway.mo00040OnewayServiceType.items =
    serviceType.value === '1'
      ? res.data.isCdKaigo
      : serviceType.value === '2'
        ? res.data.isCdYobou
        : res.data.isCdSougou
  // サービス種別選択
  local.isCdKaigo = res.data.isCdKaigo
  local.isCdYobou = res.data.isCdYobou
  local.isCdSougou = res.data.isCdSougou

  mainParmChange()
  // 総合事業種類コード選択
  local.mo00040OnewayChordTypeA1 = res.data.isCdSougouType.filter(
    (item) => item.svShuruiCdMain === 'A1'
  )
  local.mo00040OnewayChordTypeA5 = res.data.isCdSougouType.filter(
    (item) => item.svShuruiCdMain === 'A5'
  )
  local.mo00040OnewayChordTypeA9 = res.data.isCdSougouType.filter(
    (item) => item.svShuruiCdMain === 'A9'
  )

  // localOneway.mo00040OnewayChordType.items = res.data.isCdSougouType
}

/**
 * 「閉じるボタン」押下
 */
function close() {
  setState({ isOpen: false })
}

/**
 * サービス種別
 *
 * @param value - servicetype
 */
const changeServiceType = (value: string) => {
  local.serviceType1.modelValue = false
  local.serviceType2.modelValue = false
  local.serviceType3.modelValue = false
  serviceType.value = value
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
// watch(
//   () => mo00024.value.emitType,
//   (newValue) => {
//     if (newValue === 'closeBtnClick') {
//       // await onClickCloseBtn()
//       setState({ isOpen: false })
//     }
//     mo00024.value.emitType = 'blank'
//   }
// )
// week 1,2,3,4,5,6,7
watch(
  () => [
    local.frequencymonth.modelValue,
    local.frequencyFire.modelValue,
    local.frequencyWater.modelValue,
    local.frequencyWood.modelValue,
    local.frequencyGold.modelValue,
    local.frequencySoil.modelValue,
    local.frequencyDay.modelValue,
  ],
  ([val1, val2, val3, val4, val5, val6, val7]) => {
    if (val1 || val2 || val3 || val4 || val5 || val6 || val7) {
      local.outsideWeek.modelValue = false
    }
  },
  { deep: true }
)

// 週単位以外
watch(
  () => local.outsideWeek.modelValue,
  (newVal) => {
    if (newVal) {
      local.frequencymonth.modelValue = false
      local.frequencyFire.modelValue = false
      local.frequencyWater.modelValue = false
      local.frequencyWood.modelValue = false
      local.frequencyGold.modelValue = false
      local.frequencySoil.modelValue = false
      local.frequencyDay.modelValue = false
    }
  },
  { deep: true }
)

// サービス種別選択
watch(
  () => serviceType.value,
  (newValue) => {
    if (newValue === '1') {
      localOneway.mo00040OnewayServiceType.items = local.isCdKaigo
      local.serviceTypeChange.modelValue = local.isCdKaigo[0].svShuruiCd
    } else if (newValue === '2') {
      localOneway.mo00040OnewayServiceType.items = local.isCdYobou
      local.serviceTypeChange.modelValue = local.isCdYobou[0].svShuruiCd
    } else {
      localOneway.mo00040OnewayServiceType.items = local.isCdSougou
      local.serviceTypeChange.modelValue = local.isCdSougou[0].svShuruiCd
    }
  }
)

/** サービス種別選択  */
watch(
  () => local.serviceTypeChange.modelValue,
  (newValue) => {

    if (newValue === 'A1') {
      localOneway.mo00040OnewayChordType.items = local.mo00040OnewayChordTypeA1
      local.mo00045Left.value = local.mo00040OnewayChordTypeA1[0].svShuruiCd
    } else if (newValue === 'A5') {
      localOneway.mo00040OnewayChordType.items = local.mo00040OnewayChordTypeA5
      local.mo00045Left.value = local.mo00040OnewayChordTypeA5[0].svShuruiCd
    } else if (newValue === 'A9') {
      localOneway.mo00040OnewayChordType.items = local.mo00040OnewayChordTypeA9
      local.mo00045Left.value = local.mo00040OnewayChordTypeA9[0].svShuruiCd
    }

    if (serviceType.value === '3') {

      if (local.serviceTypeChange.modelValue === 'A1') {
        local.chordTypeChange.modelValue = local.mo00040OnewayChordTypeA1[0].svShuruiCd
      } else if (local.serviceTypeChange.modelValue === 'A5') {
        local.chordTypeChange.modelValue = local.mo00040OnewayChordTypeA5[0].svShuruiCd
      } else if (local.serviceTypeChange.modelValue === 'A9') {
        local.chordTypeChange.modelValue = local.mo00040OnewayChordTypeA9[0].svShuruiCd
      }
    }
    // local.mo00045Left.value = newValue ?? ''
  }
)

/** サービス種別選択  */
watch(
  () => local.chordTypeChange.modelValue,
  (newValue) => {
    local.mo00045Left.value = newValue ?? ''
  }
)

/** サービスコード  */
const mainParmChange = () => {
  if (local.isCdKaigo.findIndex((item) => item.svShuruiCd === local.mo00045Left.value) !== -1) {
    serviceType.value = '1'
    local.serviceTypeChange.modelValue = local.mo00045Left.value
    void changeServiceType('1')
  } else if (
    local.isCdYobou.findIndex((item) => item.svShuruiCd === local.mo00045Left.value) !== -1
  ) {
    serviceType.value = '2'
    local.serviceTypeChange.modelValue = local.mo00045Left.value
    void changeServiceType('2')
  } else if (
    local.isCdSougou.findIndex((item) => item.svShuruiCd === local.mo00045Left.value) !== -1
  ) {
    serviceType.value = '3'
    local.serviceTypeChange.modelValue = local.mo00045Left.value
    void changeServiceType('3')
  } else if (
    local.mo00040OnewayChordTypeA1.findIndex(
      (item) => item.svShuruiCd === local.mo00045Left.value
    ) !== -1
  ) {
    serviceType.value = '3'
    local.serviceTypeChange.modelValue = local.mo00040OnewayChordTypeA1[0].svShuruiCdMain
    void changeServiceType('3')
  } else if (
    local.mo00040OnewayChordTypeA5.findIndex(
      (item) => item.svShuruiCd === local.mo00045Left.value
    ) !== -1
  ) {
    serviceType.value = '3'
    local.serviceTypeChange.modelValue = local.mo00040OnewayChordTypeA5[0].svShuruiCdMain
    void changeServiceType('3')
  } else if (
    local.mo00040OnewayChordTypeA9.findIndex(
      (item) => item.svShuruiCd === local.mo00045Left.value
    ) !== -1
  ) {
    serviceType.value = '3'
    local.serviceTypeChange.modelValue = local.mo00040OnewayChordTypeA9[0].svShuruiCdMain
    void changeServiceType('3')
  } else {
    local.mo00045Left.value = local.oldMo00045Left
  }
  local.oldMo00045Left = local.mo00045Left.value
}

/**
 * 取得値時間
 *
 * @param value - 値
 *
 * @param type - 型
 */
function getValueHour(value: string, type: string) {
  if (!value) return ''
  const [hour, minutes] = value.split(':')
  return type === 'minutes' ? minutes : hour
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row class="content-title">
        <div style="display: flex">
          <c-v-col
            cols="auto"
            class="pa-0 input-division pr-2"
          >
            <!-- 入力区分： -->
            <base-mo-01338 :oneway-model-value="localOneway.mo01338Oneway" />
          </c-v-col>
          <c-v-col
            :cols="3"
            class="pa-0"
          >
            <!-- 追加 -->
            <base-mo00609
              :oneway-model-value="localOneway.mo00609AddOneway"
              class="add-btn"
            >
            </base-mo00609>
          </c-v-col>
        </div>
        <div style="display: flex">
          <c-v-col
            cols="auto"
            class="pa-0"
          >
            <base-mo00018
              v-model="local.kanrenSetKbn"
              :oneway-model-value="localOneway.mo00018OneWay"
            >
            </base-mo00018>
            <!-- <g-base-or21833 v-bind="or21833">
              <template #optionItems>
              </template>
            </g-base-or21833> -->
          </c-v-col>
          <c-v-col
            cols="auto"
            class="pa-0"
          >
            <base-mo-00009 :oneway-model-value="localOneway.mo00009OnewayMaster" />
          </c-v-col>
          <c-v-col
            cols="auto"
            class="pa-0"
          >
            <!-- <g-base-or21834 v-bind="or21834"></g-base-or21834> -->
            <base-mo00009 :oneway-model-value="localOneway.mo00009Oneway" />
          </c-v-col>
        </div>
      </c-v-row>
      <c-v-divider class="my-0"></c-v-divider>
      <c-v-row class="content-content">
        <c-v-col
          cols="4"
          style="border-right: 1px solid rgb(var(--v-theme-black-100))"
        >
          <!-- サービスコード -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway" />
          <div class="display-content">
            <base-mo00045
              v-model="local.mo00045Left"
              :oneway-model-value="localOneway.mo00045OnewayLeft"
              @blur="mainParmChange"
              @keyup.enter="mainParmChange"
            />
            <base-mo01338 :oneway-model-value="localOneway.mo01338OnewayHyphen" />
            <base-mo00045
              v-model="local.mo00045Right"
              :oneway-model-value="localOneway.mo00045OnewayRight"
            />
          </div>
          <!-- サービス種別ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayServiceType" />
          <div class="display-content">
            <!-- ラジオボタングループ -->
            <base-mo00018
              v-model="local.serviceType1"
              :oneway-model-value="localOneway.mo00018Oneway1"
              @click.stop="changeServiceType('1')"
            />
            <base-mo00018
              v-model="local.serviceType2"
              :oneway-model-value="localOneway.mo00018Oneway2"
              @click.stop="changeServiceType('2')"
            />
            <base-mo00018
              v-model="local.serviceType3"
              :oneway-model-value="localOneway.mo00018Oneway3"
              @click.stop="changeServiceType('3')"
            />
            <!-- サービス種別選択 -->
            <base-mo00040
              v-model="local.serviceTypeChange"
              :oneway-model-value="localOneway.mo00040OnewayServiceType"
            ></base-mo00040>
          </div>
          <!-- 事業所ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayCareerOffice" />
          <div class="display-content">
            <base-mo00045
              v-model="local.mo00045CareerOffice"
              :oneway-model-value="localOneway.mo00045OnewayCareerOffice"
            />
            <!-- 事業所選択 -->
            <base-mo00040
              v-model="local.careerOfficeChange"
              :oneway-model-value="localOneway.mo00040OnewayCareerOffice"
            ></base-mo00040>
          </div>
          <!-- 日帰りショート -->
          <base-mo00018
            v-model="local.dayTrip"
            :oneway-model-value="localOneway.mo00018OnewayDayTrip"
            @click.stop
          />
          <!-- 種類コードラベル -->
          <base-mo00615
            v-if="serviceType === '3'"
            :oneway-model-value="localOneway.mo00615OnewayChordType"
          />
          <!-- 総合事業種類コード選択 -->
          <base-mo00040
            v-if="serviceType === '3'"
            v-model="local.chordTypeChange"
            :oneway-model-value="localOneway.mo00040OnewayChordType"
          ></base-mo00040>
          <!-- 時間 -->
          <div class="display-content">
            <base-mo00615
              :oneway-model-value="localOneway.mo00615OnewayTime"
              style="margin-right: 208px"
            />
            <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayTimeRequired" />
          </div>
          <!-- 時間 -->
          <div class="display-content">
            <base-mo01272
              v-model="local.startTime"
              :oneway-model-value="localOneway.Mo01272OnewayType"
            ></base-mo01272>
            <base-mo01338
              :oneway-model-value="localOneway.mo01338OnewayConnect"
              style="height: 36px; line-height: 36px; padding: 0 8px !important"
            />
            <base-mo01272
              v-model="local.endTime"
              :oneway-model-value="localOneway.Mo01272OnewayType"
            ></base-mo01272>
            <base-mo01338
              :oneway-model-value="localOneway.mo01338OnewayRequiredTime"
              class="required-time"
            />
          </div>
          <!-- 要介護度ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayCareNeeds" />
          <div class="display-content">
            <!-- 利用者要介護度 -->
            <base-mo00040
              v-model="local.userChange"
              :oneway-model-value="localOneway.mo00040OnewayUserChange"
            ></base-mo00040>
            <!-- 要介護度絞り込み -->
            <base-mo00018
              v-model="local.careNeed"
              :oneway-model-value="localOneway.mo00018OnewayCareNeed"
              @click.stop
            />
          </div>
          <!-- 曜日ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayDayLabel" />
          <div class="display-content">
            <base-mo00018
              v-model="local.frequencymonth"
              :oneway-model-value="localOneway.mo00018OnewayMonth"
              @click.stop
            />
            <base-mo00018
              v-model="local.frequencyFire"
              :oneway-model-value="localOneway.mo00018OnewayFire"
              @click.stop
            />
            <base-mo00018
              v-model="local.frequencyWater"
              :oneway-model-value="localOneway.mo00018OnewayWater"
              @click.stop
            />
            <base-mo00018
              v-model="local.frequencyWood"
              :oneway-model-value="localOneway.mo00018OnewayWood"
              @click.stop
            />
            <base-mo00018
              v-model="local.frequencyGold"
              :oneway-model-value="localOneway.mo00018OnewayGold"
              @click.stop
            />
            <base-mo00018
              v-model="local.frequencySoil"
              :oneway-model-value="localOneway.mo00018OnewaySoil"
              @click.stop
            />
            <base-mo00018
              v-model="local.frequencyDay"
              :oneway-model-value="localOneway.mo00018OnewayDay"
              @click.stop
            />
          </div>
          <div class="display-content">
            <base-mo00018
              v-model="local.outsideWeek"
              :oneway-model-value="localOneway.mo00018OnewayOutsideWeek"
              @click.stop
            />
          </div>
          <!-- 利用期間 -->
          <base-mo00615
            v-if="local.outsideWeek.modelValue"
            :oneway-model-value="localOneway.mo00615OnewayUtilzePeriod"
          />
          <div
            v-if="local.outsideWeek.modelValue"
            class="display-content"
          >
            <base-mo00045
              v-model="local.satrtDate"
              :oneway-model-value="localOneway.mo00045OnewayStartDate"
            />

            <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayDay" />
            <base-mo01338
              :oneway-model-value="localOneway.mo01338OnewayConnect"
              style="height: 36px; line-height: 36px; padding: 0 8px !important"
            />
            <base-mo00045
              v-model="local.endDate"
              :oneway-model-value="localOneway.mo00045OnewayEndtDate"
            />
            <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayDay" />
          </div>
          <!-- 短期入所優先設定 -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayShortSetting" />
          <base-mo00039
            v-model="local.shortTrim"
            :oneway-model-value="{
              name: 'mo00039_1',
              showItemLabel: false,
              class: 'short-trim-setting',
              items: [
                { label: '自動補正', value: '1' },
                { label: '全て優先', value: '2' },
                { label: '入所日以外', value: '3' },
                { label: '退所日以外', value: '4' },
                { label: '入退所日以外', value: '5' },
                { label: '何もしない', value: '6' },
              ] as Mo00039Items[],
            }"
          >
          </base-mo00039>
        </c-v-col>
        <c-v-col
          cols="2"
          style="border-right: 1px solid rgb(var(--v-theme-black-100)); padding: 0 8px"
        >
          <!-- 病院・診療所 -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayHospitalClincis" />
          <!-- 施設区分 -->
          <base-mo00615
            :oneway-model-value="localOneway.mo00615OnewayConstructionDifferentiation"
          />
          <base-mo00040
            v-model="local.constructionDifferentiation"
            :oneway-model-value="localOneway.mo00040OnewayConstructionDifferentiation"
          ></base-mo00040>
          <!-- 人員配置 -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayStaffing" />
          <base-mo00040
            v-model="local.staffing"
            :oneway-model-value="localOneway.mo00040OnewayStaffing"
          ></base-mo00040>
          <!-- 療養機能強化型 -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayHealingFunction" />
          <base-mo00040
            v-model="local.healingFunction"
            :oneway-model-value="localOneway.mo00040OnewayHealingFunction"
          ></base-mo00040>

          <!-- 共通 -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayCommon" />
          <!-- 居室区分 -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayResidentialDivision" />
          <base-mo00040
            v-model="local.residentialDivision"
            :oneway-model-value="localOneway.mo00040OnewayResidentialDivision"
          ></base-mo00040>
          <!-- ユニットケア体制 -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayUnitCareSystem" />
          <base-mo00040
            v-model="local.unitCareSystem"
            :oneway-model-value="localOneway.mo00040OnewayUnitCareSystem"
          ></base-mo00040>
          <!-- 夜間勤務条件 -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayNightDutyConditions" />
          <base-mo00040
            v-model="local.nightDutyConditions"
            :oneway-model-value="localOneway.mo00040OnewayNightDutyConditions"
          ></base-mo00040>
          <!-- 超過・欠員 -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayExceedingStaff" />
          <base-mo00040
            v-model="local.exceedingStaff"
            :oneway-model-value="localOneway.mo00040OnewayExceedingStaff"
          ></base-mo00040>
        </c-v-col>
        <c-v-col cols="6">
          <!-- 基本サービス項目 -->
          <div class="project-table">
            <base-mo01338
              :oneway-model-value="localOneway.mo01337OnewayBasicCheck"
              class="text-right pb-2"
            />
            <base-mo-01334
              v-model="local.mo01334TypeBasicProject"
              :oneway-model-value="localOneway.mo01334OnewayBasicProject"
              class="list-wrapper"
              hide-default-footer
            ></base-mo-01334>
          </div>
          <c-v-divider class="my-0"></c-v-divider>
          <!-- 加算絞り込みサブセクション -->
          <div class="display-content content-table">
            <div style="height: 65px; display: flex; align-items: end">
              <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayAdditionSqueeze" />
            </div>
            <div>
              <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayCode" />
              <base-mo00045
                v-model="local.mo00045Code"
                :oneway-model-value="localOneway.mo00045OnewayCode"
              />
            </div>
            <div>
              <base-mo00615 :oneway-model-value="localOneway.mo00615AdditionServiceItem" />
              <base-mo00045
                v-model="local.mo00045AdditionServiceItem"
                :oneway-model-value="localOneway.mo00045OnewayAdditionServiceItem"
              />
            </div>
          </div>
          <!-- 加算項目一覧 -->
          <div style="padding-left: 8px">
            <base-mo01338
              :oneway-model-value="localOneway.mo01337OnewayAddCheck"
              class="text-right pb-2"
            />
            <base-mo-01334
              v-model="local.mo01334TypeAddProject"
              :oneway-model-value="localOneway.mo01334OnewayAddProject"
              class="list-wrapper"
              hide-default-footer
            ></base-mo-01334>
          </div>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 追加 -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609AppendOneway"
          class="mr-2"
        >
        </base-mo00609>
        <!-- 確定 -->
        <base-mo00609 :oneway-model-value="localOneway.mo00609ConfirmOneway"> </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <base-mo01376
    v-if="showDialog"
    v-model="mo01376"
    :oneway-model-value="localOneway.mo01376Oneway"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';

.content-title {
  padding: 0;
  margin: 0 0 8px;
  justify-content: space-between;
  :deep(.input-division) {
    label {
      font-weight: bold !important;
      font-size: 14px;
      line-height: 36px;
    }
  }
  :deep(.add-btn) {
    margin-top: 4px;
    height: 18px;
    line-height: 18px;
    background-color: #f6e192 !important;
    color: #663315 !important;
    cursor: default;
    > div {
      display: none;
    }
  }
  :deep(.add-btn:hover) {
    background-color: #f6e192 !important;
    color: #663315 !important;
  }
}
.content-content {
  padding: 0;
  margin: 0;
  .v-col {
    margin: 0;
    padding: 0;
  }
  .display-content {
    display: flex;
    align-items: center;
    .required-time {
      width: 120px;
      height: 36px;
      border: 1px solid #ababab;
      border-radius: 4px;
      line-height: 34px;
      text-indent: 12px;
    }
  }
  :deep(.short-trim-setting) {
    .v-selection-control-group {
      > div {
        width: 30%;
      }
    }
  }
  .project-table {
    padding: 8px 0 0 8px;
  }
  .content-table {
    padding: 8px;
  }
}
</style>
