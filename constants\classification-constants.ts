/**
 * 更新区分
 */
export namespace UPDATE_KBN {
  /**
   * 更新なし
   */
  export const NONE = ''
  /**
   * "C":新規
   */
  export const CREATE = 'C'
  /**
   * "U":更新
   */
  export const UPDATE = 'U'
  /**
   * "D":削除
   */
  export const DELETE = 'D'
}
/**
 * ダイアログButton
 */
export namespace DIALOG_BTN {
  /**
   * yes
   */
  export const YES = 'yes'
  /**
   * NO
   */
  export const NO = 'no'
  /**
   * CONFIRM
   */
  export const CONFIRM = 'confirm'

  /**
   * CANCEL
   */
  export const CANCEL = 'cancel'
  /**
   * OK
   */
  export const OK = 'ok'
}
/**  ～  */
export const SPACE_WAVE = ' ～ '
/**  /  */
export const SPACE_FORWARD_SLASH = ' / '
/**  ：  */
export const SPACE_SPLIT_COLON = '：'
/**
 * 丸括弧- 左
 */
export const PARENTHESES_LEFT = '（'
/**
 * 丸括弧- 右
 */
export const PARENTHESES_RIGHT = '）'
/**
 * 種別id
 */
export namespace SYUBETU {
  /**
   * 種別id: 2
   */
  export const SYUBETU_2 = '2'
  /**
   * 種別id: 0
   */
  export const SYUBETU_0 = '0'
  /**
   * 種別id: 3
   */
  export const SYUBETU_3 = '3'
  /**
   * 種別id: 4
   */
  export const SYUBETU_4 = '4'
  /**
   * 種別id: 5
   */
  export const SYUBETU_5 = '5'
  /**
   * 種別id: 6
   */
  export const SYUBETU_6 = '6'
  /**
   * 種別id: 7
   */
  export const SYUBETU_7 = '7'
  /**
   * 種別id: 12
   */
  export const SYUBETU_12 = '12'
  /**
   * 種別id: 13
   */
  export const SYUBETU_13 = '13'
}
/**
 * システム種別ID取得: ケアプランの画面IDリスト
 */
export const SYUBETU_CARE_PLAN_SCREEN_IDS = [
  'GUI00612', // 進捗管理
  'GUI00622', // ［経過管理］画面
  'GUI00635', // ［フェースシート（パッケージプラン）］①画面
  'GUI00636', // ［フェースシート（パッケージプラン）］②画面
  'GUI00637', // ［フェースシート（パッケージプラン）］③画面
  'GUI00638', // ［フェースシート（パッケージプラン）］④画面
  'GUI00639', // ［フェースシート複写］画面
  'GUI00653', // ［課題検討］画面
  'GUI00654', // 課題検討複写
  'GUI00658', // ［情報収集］画面（12）
  'GUI00659', // ［情報収集］画面（13）
  'GUI00660', // ［情報収集］画面（14）
  'GUI00661', // ［情報収集］画面（その他）
  'GUI00662', // ［情報収集］画面（服薬状況）
  'GUI00665', // 情報収集複写
  'GUI00673', // ［情報収集］画面（1）
  'GUI00674', // ［情報収集］画面（2）
  'GUI00675', // ［情報収集］画面（3）
  'GUI00676', // ［情報収集］画面（4）
  'GUI00677', // ［情報収集］画面（5）
  'GUI00678', // ［情報収集］画面（6）
  'GUI00679', // ［情報収集］画面（7）
  'GUI00680', // ［情報収集］画面（8）
  'GUI00681', // ［情報収集］画面（9）
  'GUI00682', // ［情報収集］画面（10）
  'GUI00683', // ［情報収集］画面（11）
  'GUI00794', // ［アセスメント］画面（居宅）（1）
  'GUI00795', // ［アセスメント］画面（居宅）（2）
  'GUI00796', // ［アセスメント］画面（居宅）（3）
  'GUI00797', // ［アセスメント］画面（居宅）（4）
  'GUI00798', // ［アセスメント］画面（居宅）（5）
  'GUI00799', // ［アセスメント］画面（居宅）（6①）
  'GUI00800', // ［アセスメント］画面（居宅）（6②）
  'GUI00801', // ［アセスメント］画面（居宅）（6③-④）
  'GUI00802', // ［アセスメント］画面（居宅）（6⑤）
  'GUI00803', // ［アセスメント］画面（居宅）（6⑥）
  'GUI00804', // ［アセスメント］画面（居宅）（6医）
  'GUI00805', // ［アセスメント］画面（居宅）（7まとめ）
  'GUI00806', // ［アセスメント］画面（居宅）（7スケジュール）
  'GUI00807', // アセスメント複写
  'GUI00816', // アセスメント(パッケージプラン)
  'GUI00820', // ［ｱｾｽﾒﾝﾄ複写］画面
  'GUI00834', // ［アセスメント（包括）］画面
  'GUI00838', // ［ｱｾｽﾒﾝﾄ複写］画面
  'GUI00872', // 検討表
  'GUI00873', // 検討表複写
  'GUI00892', // チェック項目画面
  'GUI00893', // チェック項目・課題立案複写
  'GUI00899', // ［フェースシート］画面（フリーアセスメント）
  'GUI00906', // 課題立案
  'GUI00911', // 課題整理総括設定マスタ
  'GUI00917', // ［課題整理総括複写］画面
  'GUI00930', // 課題分析
  'GUI00931', // 課題分析複写
  'GUI00939', // 月間・年間表
  'GUI00941', // 月間・年間表複写
  'GUI00946', // 実施計画～①
  'GUI00948', // 実施計画～①複写
  'GUI00955', // 実施計画～②
  'GUI00958', // 実施計画～②複写
  'GUI00964', // 実施計画～③
  'GUI00965', // 実施計画～③複写
  'GUI00971', // 総合計画
  'GUI00974', // 総合計画複写
  'GUI00978', // 週間表イメージ
  'GUI00982', // 週間表複写
  'GUI00989', // 日課表イメージ
  'GUI00997', // 日課表複写
  'GUI01004', // 計画書（１）
  'GUI01006', // 計画書複写（計画書（1））
  'GUI01014', // 計画書（2）
  'GUI01017', // ［計画書複写］（計画書（2））
  'GUI01038', // 週間計画
  'GUI01046', // 週間計画複写
  'GUI01057', // 日課計画
  'GUI01061', // 日課計画複写
  'GUI01067', // 基本情報
  'GUI01071', // 予防基本複写
  'GUI01080', // 基本チェックリスト
  'GUI01081', // 基本チェックリスト複写
  'GUI01116', // 興味・関心チェックシート
  'GUI01117', // 興味・関心チェックシート複写
  'GUI01122', // 会議録
  'GUI01133', // 照会内容
  'GUI01135', // 照会内容複写
  'GUI01149', // 利用票
  'GUI01180', // ［カレンダー］画面（［利用・提供票］画面）
  'GUI01182', // ［給付状況］画面（個別）
  'GUI01183', // ［給付状況］画面（一覧）
  'GUI01188', // 提供事業所
  'GUI01213', // シミュレーション
  'GUI01216', // 計画実施複写
  'GUI01217', // 計画実施‐実績登録
  'GUI01218', // 計画表取込
  'GUI01227', // 計画モニタリング
  'GUI01228', // モニタリング複写画面
  'GUI01233', // 評価表マスタ
  'GUI01234', // 評価表設定マスタ
  'GUI01235', // 評価表
  'GUI01236', // 評価表複写
  'GUI01237', // 実施計画書取込
  'GUI01238', // 表示順変更評価表
  'GUI01239', // ［履歴選択］画面 評価表
  'GUI01240', // 印刷設定
  'GUI01241', // 予防評価表マスタ
  'GUI01242', // 予防評価表設定マスタ
  'GUI01243', // 予防評価表タイトルマスタ
  'GUI01244', // 予防評価表
  'GUI01245', // 予防評価複写
  'GUI01246', // 表示順変更サービス評価表
  'GUI01247', // ［履歴選択］画面 予防評価表
  'GUI01248', // 印刷設定
  'GUI01258', // 支援経過記録
  'GUI01268', // 概況調査
  'GUI01269', // 特記事項
  'GUI01270', // 調査票複写
  'GUI01275', // 基本調査１
  'GUI01276', // 基本調査２
  'GUI01277', // 基本調査３
  'GUI01278', // 基本調査４
  'GUI01279', // 基本調査５
  'GUI01286', // 主治医意見書
  'GUI01287', // 主治医複写
  'GUI01300', // ［入院時情報提供書］画面
  'GUI01301', // ［入院時情報提供書複写］画面
  'GUI01305', // 退院・退所情報記録書
  'GUI01306', // 退院・退所情報記録書複写
]
/**
 * システム種別ID取得: 個別計画の画面IDリスト
 */
export const SYUBETU_INDIVIDUAL_PLAN_SCREEN_IDS = [
  'GUI01975', // 評価表
  'GUI01651', // 入庫処理（個別）
  'GUI01652', // 入庫処理（一覧）
  'GUI01641', // 出庫処理（個別）
  'GUI01642', // 出庫処理（利用者出庫一覧）
  'GUI01643', // 出庫処理（その他出庫一覧）
  'GUI01658', // 用具点検（個別）画面
  'GUI01659', // 用具点検（一覧）画面
  'GUI01973', // 同意書
  'GUI01902', // 実施記録・個別援助詳細計画共有
  'GUI01903', // 個別援助詳細計画
  'GUI01887', // 個別援助計画書
  'GUI01888', // 個別援助週間計画表
  'GUI01889', // 排せつ支援計画書
  'GUI01878', // カンファレンス
  'GUI01936', // ふくせん基本情報
  'GUI01941', // ふくせん選定提案
  'GUI01946', // ふくせん利用計画
  'GUI01950', // ふくせんモニタリング
  'GUI01958', // 興味・関心チェックシート
  'GUI01964', // 個別機能訓練計画複写
  'GUI01961', // 居宅訪問チェックシート
  'GUI01963', // 個別機能訓練計画書
  'GUI01969', // 通所介護計画書
  'GUI03095', // 興味・関心ﾁｪｯｸｼｰﾄ
  'GUI03096', // 生活機能ﾁｪｯｸｼｰﾄ
  'GUI03097', // 個別機能訓練計画書
  'GUI03098', // 一体的計画書
]
/**
 * システム種別ID取得: リハビリの画面IDリスト
 */
export const SYUBETU_REHABILITATION_SCREEN_IDS = [
  'GUI01327', // リハ実施計画書（日常生活・社会活動・起居動作）（アセスメント）
  'GUI01328', // リハ総合実施計画書
  'GUI01329', // 人体図
  'GUI01330', // バーセルインデックス（［運動機能］、［HDS-R］）
  'GUI01331', // 運動機能（新予防給付版）
  'GUI01332', // HRS-R（改訂長谷川式簡易知能評価スケール）
  'GUI01333', // 痛みの評価
  'GUI01334', // 麻痺と痛みの評価（筋トレくん連動）
  'GUI01335', // 体力テスト（筋トレくん連動）
  'GUI01336', // リハ実施計画書（健康状態・参加・心身機能）
  'GUI01339', // FIM（機能的自立度評価表）
  'GUI01341', // カスタマイズ独自様式
  'GUI01337', // リハビリ複写
  'GUI01346', // カンファレンス（リハビリ）
  'GUI01361', // リハビリテーション実施計画書
  'GUI01362', // リハビリ自由設定計画書
  'GUI01396', // リハビリスケジュール
  'GUI01406', // 詳細計画(リハビリ)
  'GUI01409', // リハビリ自由設定報告書
  'GUI01325', // リハビリ個人設定
  'GUI01418', // リハビリ統計
  'GUI01429', // リハビリCSV
  'GUI01381', // 計画書（固定）複写
  'GUI01382', // 様式１（H27）
  'GUI01383', // 様式１（H30）
  'GUI01384', // 様式２（H27）
  'GUI01385', // 様式２（H30）
  'GUI01386', // 様式３（H27）
  'GUI01387', // 様式３（H30）
  'GUI01388', // 様式４（H27）
  'GUI01389', // 様式４（H30）
  'GUI01390', // 様式５（H27）
  'GUI01391', // 様式５（H30）
  'GUI01392', // 様式６（H27）
  'GUI03099', // 様式１
  'GUI03100', // 様式２
  'GUI03101', // 様式３
  'GUI03102', // 様式４
  'GUI03103', // 様式５
  'GUI03104', // 様式６
  'GUI03105', // 様式１
  'GUI03106', // ２－１
  'GUI03107', // 様式３
  'GUI03108', // 様式４
  'GUI03109', // 様式５
  'GUI03110', // 様式１
  'GUI03111', // ２－１
  'GUI03112', // 様式３
  'GUI03113', // 様式４
  'GUI03114', // 様式５
]
/**
 * システム種別ID取得: 栄養の画面IDリスト
 */
export const SYUBETU_NUTRITION_SCREEN_IDS = [
  'GUI02069', // スクリーニング（栄養ケア）
  'GUI02070', // 栄養スクリーニング複写
  'GUI02134', // アセスメント（褥瘡ケア）
  'GUI02054', // プロセス
  'GUI02135', // 褥瘡ケア複写
  'GUI02075', // カンファレンス（栄養ケア）
  'GUI02079', // 意向・課題・長期目標
  'GUI02080', // 短期目標・栄養ケア・特記事項
  'GUI02081', // 栄養ケア提供経過記録
  'GUI02082', // 栄養ケア計画書複写
  'GUI02097', // サービス提供前
  'GUI02098', // 通常
  'GUI02099', // 達成（改善）率
  'GUI02112', // 経過記録
  'GUI02044', // 栄養個人設定
  'GUI02117', // 栄養ケア統計
  'GUI02063', // 栄養ﾌﾟﾛｾｽ複写
  'GUI02093', // 検査・支援の観点
  'GUI02094', // 議論の概要
  'GUI02095', // 計画
  'GUI02092', // 栄養ケア経口移行・維持計画複写
  'GUI03085', // 1
  'GUI03086', // 2
  'GUI03087', // 3
  'GUI03088', // 総合的評価・判定
  'GUI03089', // 経口維持
  'GUI03090', // 一体的計画書
]
/**
 * システム種別ID取得: 口腔の画面IDリスト
 */
export const SYUBETU_ORAL_CAVITY_SCREEN_IDS = [
  'GUI02040', // 口腔ケアCSV
  'GUI02029', // 口腔ケア統計
  'GUI01983', // 口腔個人設定
  'GUI02017', // 口腔機能サービスに関する課・ア・モ・評
  'GUI02018', // 口腔自由設定報告書
  'GUI02019', // 口腔機能の向上の記録
  'GUI02020', // 口腔衛生管理に関する実施記録
  'GUI02015', // 詳細計画(口腔ケア)
  'GUI02008', // 口腔機能の向上の指導管理計画
  'GUI02009', // 個別サービス計画・口腔機能改善管理指導計画
  'GUI02010', // 口腔自由設定計画書
  'GUI02006', // カンファレンス（口腔ケア）
  'GUI01991', // 口腔機能アセスメント
  'GUI01992', // 実施のための利用者の情報
  'GUI01993', // 居宅療養管理指導の提供経過記録
  'GUI01994', // （様式２－Ⅰ）口腔機能アセスメント
  'GUI01995', // （様式２－Ⅱ）口腔機能アセスメント
  'GUI01996', // 専門職による課題把握のためのアセスメント、モニタリング
  'GUI01997', // 問題点リスト
  'GUI01998', // 改定水飲みテスト
  'GUI01999', // 総合評価
  'GUI02000', // H24様式-関連職種等により把握された課題等
  'GUI02001', // H24様式-事前・事後アセスメント・モニタリング
  'GUI02002', // 口腔ケアアセスメント複写
  'GUI01986', // スクリーニング（口腔ケア）
  'GUI01987', // 口腔ケアスクリーニング複写
  'GUI03091', // 口腔ｹｱ報告書
  'GUI03093', // 口腔ｹｱ計画書
]
/**
 * システム種別ID取得: 褥瘡の画面IDリスト
 */
export const SYUBETU_BEDSORE_SCREEN_IDS = [
  'GUI02134', // アセスメント（褥瘡ケア）
  'GUI02135', // 褥瘡ケア複写
  'GUI02138', // カンファレンス（褥瘡ケア）
  'GUI02140', // 褥瘡自由設定計画書
  'GUI02150', // 詳細計画(褥瘡ケア)
  'GUI02153', // 褥瘡評価
  'GUI02154', // 褥瘡評価複写
  'GUI02157', // 褥瘡自由設定報告書
  'GUI02130', // 褥瘡個人設定
  'GUI02164', // 褥瘡ケア統計
  'GUI02177', // 褥瘡ケアCSV出力
  'GUI03135', // 褥瘡ｹｱ計画書
  'GUI03136', // 褥瘡診療計画書
]
/**
 * システム種別ID取得: 24Hシートの画面IDリスト
 */
export const SYUBETU_24H_SHEET_SCREEN_IDS = [
  'GUI01712', // 聞き取りシート
  'GUI01720', // 24Hシート
  'GUI01734', // 24Hシート一覧表
  'GUI01713', // 聞き取りシート複写ダイアログ
  'GUI01721', // 24Hシート複写ダイアログ
]
/**
 * システム種別ID取得: R4システムの画面IDリスト
 */
export const SYUBETU_R4_SYSTEM_SCREEN_IDS = [
  'GUI01779', // 進捗管理
  'GUI01782', // プレインテーク
  'GUI01791', // A-1（裏）
  'GUI01792', // A-1（表）
  'GUI01809', // A-2（裏）
  'GUI01810', // A-2（表）
  'GUI01818', // A-3(裏)
  'GUI01819', // A-3(表)
  'GUI01826', // 実施要点
  'GUI01827', // サービス内容
  'GUI01828', // 週間計画
  'GUI01848', // ADL比較一覧
  'GUI01841', // 実施状況確認
  'GUI01834', // リハ実施計画書（表1）
  'GUI01835', // リハ実施計画書（表2）
  'GUI01836', // リハ実施計画書（裏）
  'GUI01851', // 外部連携
  'GUI01866', // 様式A
  'GUI01867', // 様式B
  'GUI01868', // 様式C
  'GUI01869', // 様式D
  'GUI01870', // 様式E
  'GUI01871', // 様式F
  'GUI01872', // 様式G
  'GUI01784', // ﾌﾟﾚｲﾝﾃｰｸ複写
  'GUI01793', // A-1複写
  'GUI01812', // A-2複写
  'GUI01820', // A-3（表）複写
  'GUI01821', // A-3（裏）複写
  'GUI01830', // 総合計画複写
  'GUI01837', // リハ実施計画書複写
  'GUI03115', // 様式A
  'GUI03116', // B-1
  'GUI03117', // B-2
  'GUI03118', // C-1
  'GUI03119', // C-2
  'GUI03120', // 様式D
  'GUI03121', // 様式E
  'GUI03122', // 様式F
  'GUI03123', // 様式G
  'GUI03124', // 様式１
  'GUI03125', // ２－１
  'GUI03126', // 様式３
  'GUI03127', // 様式４
  'GUI03128', // 様式５
  'GUI03129', // 様式１
  'GUI03130', // ２－１
  'GUI03131', // 様式３
  'GUI03132', // 様式４
  'GUI03133', // 様式５
]
