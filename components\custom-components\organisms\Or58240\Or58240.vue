<script setup lang="ts">
/**
 * Or58240:有機体:印刷設定
 * GUI00895_印刷設定
 *
 * @description
 * GUI00895_印刷設定
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed, onUnmounted, nextTick } from 'vue'
import { isEqual } from 'lodash'
import { OrX0128Const } from '../OrX0128/OrX0128.constants'
import { OrX0128Logic } from '../OrX0128/OrX0128.logic'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import { Or58240Const } from './Or58240.constants'
import type { Or58240Param, Or58240StateType } from './Or58240.type'
import {
  useSetupChildProps,
  useScreenOneWayBind,
  useSystemCommonsStore,
  useNuxtApp,
} from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '@/types/business/components/Mo01338Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00020Type, Mo00020OnewayType } from '@/types/business/components/Mo00020Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type {
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CustomClass } from '~/types/CustomClassType'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type {
  CheckItemHistoryEntity,
  FreeAssessmentCheckPrintSettingsHistorySelectInEntity,
  FreeAssessmentCheckPrintSettingsHistorySelectOutEntity,
  FreeAssessmentCheckPrintSettingsSelectInEntity,
  FreeAssessmentCheckPrintSettingsSelectOutEntity,
  FreeAssessmentCheckPrintSettingsSubjectSelectInEntity,
  FreeAssessmentCheckPrintSettingsSubjectSelectOutEntity,
  PrtEntity,
  SysIniInfoEntity,
  UserEntity,
} from '~/repositories/cmn/entities/FreeAssessmentCheckPrintSettingsEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { ResBodyStatusCode } from '~/constants/api-constants'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsPrtNoChangeUpdateEntity'
import type {
  IFreeAssessmentFacePrintSettingsUpdateInEntity,
  IFreeAssessmentFacePrintSettingsUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsUpdateEntity'
import { reportOutputType, useReportUtils } from '~/utils/useReportUtils'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type {
  CheckItemUserListSelectInEntity,
  OutputLedgerPrintEntity,
  PrintHistoryEntity,
} from '~/repositories/cmn/entities/CheckItemUserListSelectEntity'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { useCmnCom } from '@/utils/useCmnCom'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import type {
  OrX0128OnewayType,
  OrX0128Items,
  OrX0128Headers,
} from '~/types/cmn/business/components/OrX0128Type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'

/** 国際化 */
const { t } = useI18n()
/** 共通メソッド */
const { reportOutput } = useReportUtils()
/** システムstore */
const systemCommonsStore = useSystemCommonsStore()
const $log = useNuxtApp().$log as DebugLogPluginInterface

// route共有情報
const cmnRouteCom = useCmnRouteCom()

// 画面ID
const screenId = 'GUI00895'
// ルーティング
const routing = 'GUI00895/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: ''
}
/** props取得 */
const props = defineProps<Props>()

// 子コンポーネント用変数
const or21813 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })
const orX0117 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orX0145 = ref({ uniqueCpId: '' })

/** 初期化フラグ */
const initFlg = ref<boolean>(false)
/** タイトルの一時保存変数 */
const tempTitle = ref('')

/** 帳票情報 */
const ledgerInfo = {
  /**
   * 選択された帳票のプロファイル
   */
  profile: '',
  /**
   * 履歴ID
   */
  assId: '',
  /**
   * 出力帳票名一覧に選択行番号
   */
  index: '',
  /**
   * システムINI情報
   */
  sysIniInfo: {} as SysIniInfoEntity,
  /**
   * 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
   */
  historySelect: false,
  /**
   * 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
   */
  userSelect: false,
  /**
   * 出力帳票ID
   */
  reportId: '',
  /**
   * 利用者ID
   */
  userId: '',
  /**
   * 利用者名
   */
  userName: '',
  /**
   * セクション番号
   */
  sectionNo: '',
  /**
   * 選択した利用者ID
   */
  selectedUserId: '',
  /**
   * 選択した利用者名
   */
  selectedUserName: '',
  /**
   * 印刷設定情報リスト
   */
  prtList: [] as PrtEntity[],
  /**
   * 利用者リスト
   */
  userList: [] as UserEntity[],
  /**
   * 履歴データ
   */
  historyList: [] as OrX0128Items[],
}

/** Oneway */
const localOneway = reactive({
  /** 親画面の情報 */
  or58240Param: {
    processYmd: '',
    basicDate: '',
    sectionName: '',
    shisetuId: '',
    svJigyoId: '',
    userId: '',
    assId: '',
    tantoId: '',
    focusSettingInitial: [] as string[],
    selectedUserCounter: '',
  },
  kikanFlg: '',
  /** 共通情報 */
  commonInfo: {
    // システムコード：共通情報.システムコード
    sysCd: systemCommonsStore.getSystemCode ?? '',
    // 法人ID：共通情報.法人ID
    houjinId: systemCommonsStore.getHoujinId ?? '',
    // 職員ID：共通情報.職員ID
    shokuId: systemCommonsStore.getStaffId ?? '',
    // 種別ID：共通情報.種別ID
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    // 適用事業所IDリスト：共通情報.適用事業所IDリスト
    svJigyoIdList: systemCommonsStore.getSvJigyoIdList ?? [],
    // 共通情報.担当ケアマネ設定フラグ
    kkjTantoFlg: cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? '',
  },
  /**
   * 初期化履歴データの一時保存
   */
  initCheckItemHistoryList: [] as CheckItemHistoryEntity[],
  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  /**
   * 基本設定
   */
  mo01338OneWayBasicSettings: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * タイトル
   */
  mo01338OneWayTitle: {
    value: t('label.title'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * タイトルテキストフィールド
   */
  mo00045OneWay: {
    showItemLabel: false,
    width: '280',
  } as Mo00045OnewayType,
  /**
   * 日付印刷区分
   */
  mo00039OneWayDatePrint: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 印刷オプション
   */
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 指定日
   */
  mo00020OneWayDesignation: {
    showItemLabel: false,
  } as Mo00020OnewayType,
  /**
   * 氏名等を伏字にするチェックボックス
   */
  mo00018OneWayAmikake: {
    // '氏名等を伏字にする'
    checkboxLabel: t('label.name-censored'),
    showItemLabel: false,
  } as Mo00018OnewayType,
  /**
   * 敬称を変更するチェックボックス
   */
  mo00018OneWayHonorifics: {
    name: '',
    itemLabel: '',
    // '敬称を変更する'
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    customClass: new CustomClass({
      outerClass: 'honorifics-label',
    }),
  } as Mo00018OnewayType,
  /**
   * 敬称テキストフィールド
   */
  mo00045OneWayHonorifics: {
    showItemLabel: false,
    maxLength: '2',
  } as Mo00045OnewayType,
  /**
   * 記入用シートを印刷するチェックボックス
   */
  mo00018OneWayEntry: {
    name: '',
    itemLabel: '',
    // '記入用シートを印刷する'
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    customClass: new CustomClass({
      outerClass: 'entry-label',
    }),
  } as Mo00018OnewayType,
  /**
   * チェック項目利用者一覧表の場合
   * 記入用シートを印刷するチェックボックス
   */
  mo00018OneWayEntry_1: {
    name: '',
    itemLabel: '',
    // '記入用シートを印刷する'
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    // チェック項目利用者一覧表の場合、非活性とする
    disabled: true,
    customClass: new CustomClass({
      outerClass: 'entry-label',
    }),
  } as Mo00018OnewayType,
  /**
   * 利用者選択ラベル
   */
  mo01338OneWayPrinterUserSelect: {
    // '利用者選択'
    value: t('label.printer-user-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 履歴選択ラベル
   */
  mo01338OneWayPrinterHistorySelect: {
    // '履歴選択'
    value: t('label.printer-history-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択
   */
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 履歴選択
   */
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 基準日: 表用テキストフィールド
   */
  mo00020OneWayBasic: {
    // '基準日'
    itemLabel: t('label.base-date'),
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '220px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: {
      labelClass: 'tanto-label pb-2',
    } as CustomClass,
  } as OrX0145OnewayType,
})

/**************************************************
 * 変数定義
 **************************************************/
/** ダイアログOneway */
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1300px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or58240',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or58240ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'or58240-content',
  } as Mo01344OnewayType,
})
/** ダイアログTwoway */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or58240Const.DEFAULT.IS_OPEN,
})

/**
 * 出力帳票名一覧Oneway
 */
const mo01334OnewayLedger = ref<Mo01334OnewayType>({
  headers: [
    {
      // '帳票'
      title: t('label.report'),
      key: 'ledgerName',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 600,
})
/**
 * 出力帳票名一覧Twoway
 */
const mo01334TypeLedger = ref<Mo01334Type>({
  value: '1',
  values: [],
} as Mo01334Type)

/**
 * タイトルテキストフィールドmodelValue
 */
const mo00045TitleType = ref<Mo00045Type>({
  value: '',
} as Mo00045Type)

/**
 * 氏名等を伏字にするmodelValue
 */
const mo00018AmikakeType = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 日付印刷区分modelValue
 */
const mo00039DatePrintType = ref<string>('')

/**
 * 指定日modelValue
 */
const mo00020DesignationType = ref<Mo00020Type>({
  // 初期値：システム日付
  value: systemCommonsStore.getSystemDate ?? '',
} as Mo00020Type)
/**
 * 指定日非表示/表示フラグ
 */
const designationShowFlag = ref<boolean>(false)

/**
 * 敬称を変更するmodelValue
 */
const mo00018HonorificsType = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 敬称テキストフィールドmodelValue
 */
const mo00045HonorificsType = ref<Mo00045Type>({
  value: '',
} as Mo00045Type)

/**
 * 記入用シートを印刷するmodelValue
 */
const mo00018EntryType = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)
/**
 * チェック項目利用者一覧表の場合
 * 記入用シートを印刷するmodelValue
 */
const mo00018EntryType_1 = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 利用者選択modelValue
 */
const mo00039UserSelectType = ref<string>('')
/**
 * 履歴選択modelValue
 */
const mo00039HistorySelectType = ref<string>('')
/**
 * 基準日modelValue
 */
const mo00020BasicType = ref<Mo00020Type>({
  value: '',
} as Mo00020Type)

/**
 * 担当ケアマネプルダウンmodelValue
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
})

/**
 * チェック項目フラグ
 */
const checkItemFlag = ref<boolean>(false)

/**
 * 利用者列幅
 */
const userCols = ref<number>(4)
/**
 * 利用者一覧
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: Or58240Const.DEFAULT.STR.EMPTY,
  // 指定行選択
  userId: Or58240Const.DEFAULT.STR.EMPTY,
  /**
   * 複数選択時、50音の選択数を表示するか
   */
  showKanaSelectionCount: true,
  /**
   * フォーカス設定用イニシャル
   */
  focusSettingInitial: [] as string[],
})

/**
 * 履歴一覧
 */
const orX0128Oneway = reactive<OrX0128OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: Or58240Const.DEFAULT.STR.EMPTY,
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: Or58240Const.DEFAULT.STR.EMPTY,
  tableStyle: 'height:514px',
  headers: [
    { title: t('label.create-date'), key: 'createYmd', width: '120px', sortable: false },
    { title: t('label.author'), key: 'shokuinKnj', minWidth: '160px', sortable: false },
    { title: t('label.style-name'), key: 'youshikiKnj', sortable: false },
  ] as unknown as OrX0128Headers[],
  items: [],
})

/**
 * 印刷設定帳票出力
 */
const orX0117Oneway: OrX0117OnewayType = {
  type: Or58240Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType

/**************************************************
 * Pinia
 **************************************************/
/** ダイヤログ状態 */
const { setState } = useScreenOneWayBind<Or58240StateType>({
  cpId: Or58240Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 開閉
     *
     * @param value -パラメータ
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or58240Const.DEFAULT.IS_OPEN
    },
    /**
     * パラメータ
     *
     * @param value -パラメータ
     */
    param: (value) => {
      if (value) {
        // 親画面のデータを取得
        localOneway.or58240Param.processYmd = value.processYmd
        localOneway.or58240Param.basicDate = value.basicDate
        localOneway.or58240Param.sectionName = value.sectionName
        localOneway.or58240Param.shisetuId = value.shisetuId
        localOneway.or58240Param.svJigyoId = value.svJigyoId
        localOneway.or58240Param.userId = value.userId
        localOneway.or58240Param.assId = value.assId
        localOneway.or58240Param.tantoId = value.tantoId
        localOneway.or58240Param.focusSettingInitial = value.focusSettingInitial
        localOneway.or58240Param.selectedUserCounter = value.selectedUserCounter
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0145Const.CP_ID(0)]: orX0145.value,
})

/**************************************************
 * 算出プロパティ
 **************************************************/
// 記入用シートを印刷するチェックボックス活性制御
/** 利用者選択方法が「単一」 また 履歴選択方法が「単一」の場合、活性表示。 */
const mo00018EntryDisabled = computed(
  () => !(mo00039UserSelectType.value === '0') || !(mo00039HistorySelectType.value === '0')
)

// 利用者選択方法表示制御
/** 記入用シートを印刷するチェックボックスが選択の場合、非表示 */
const mo00039UserSelectShowFlg = computed(() => !mo00018EntryType.value.modelValue)

// 履歴選択方法表示制御
/**
 * 記入用シートを印刷するチェックボックスが選択の場合、非表示
 * または、利用者選択方法が「複数」の場合、非表示
 */
const mo00039HistorySelectShowFlg = computed(
  () => !(mo00018EntryType.value.modelValue || mo00039UserSelectType.value === '1')
)

// 担当ケアマネプルダウンリスト活性制御
/** 共通情報.担当ケアマネ設定フラグ > 0、且つ、親画面.担当者IDが0以外の場合、非活性 */
const orX0145Disabled = computed(
  () => Number(localOneway.commonInfo.kkjTantoFlg) > 0 && localOneway.or58240Param.tantoId !== '0'
)

// 基準日表示制御
/** 利用者選択方法が「複数」、または「出力帳票名」選択はチェック項目利用者一覧表の場合、表示 */
const mo00020BasicShowFlg = computed(
  () => mo00039UserSelectType.value === '1' || mo01334TypeLedger.value.value === '3'
)

/** 印刷ダイアログ表示フラグ */
const showDialogOrX0117 = computed(() => {
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * ライフサイクル
 **************************************************/
onUnmounted(() => {
  OrX0130Logic.event.set({
    uniqueCpId: orX0130.value.uniqueCpId,
    events: {
      clickFlg: false,
      userList: [],
    },
  })
})
/**************************************************
 * 関数
 **************************************************/
/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // 性別区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    const list: Mo00039Items[] = []
    for (const item of bizukePrintCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayDatePrint.items = list
  }

  // 利用者選択 TAN_MULTIPLE_SELECT_CATEGORY
  const tanMultipleSelectCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  if (tanMultipleSelectCategoryCodeTypes?.length > 0) {
    mo00039UserSelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    mo00039HistorySelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of tanMultipleSelectCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayHistorySelectType.items = list
    localOneway.mo00039OneWayUserSelectType.items = list
  }
}

/**
 * 初期情報取得
 */
const getInitData = async () => {
  // 基準日初期値=親画面.処理年月日
  mo00020BasicType.value.value = localOneway.or58240Param.processYmd
  // バックエンドAPIから初期情報取得
  const inputData: FreeAssessmentCheckPrintSettingsSelectInEntity = {
    // システムコード：共通情報.システムコード
    sysCd: localOneway.commonInfo.sysCd,
    // システム略称："3gk"
    sysRyaku: Or58240Const.DEFAULT.SYS_RYAKU,
    // 法人ID：共通情報.法人ID
    houjinId: localOneway.commonInfo.houjinId,
    // 施設ID：親画面.施設ID
    shisetuId: localOneway.or58240Param.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.or58240Param.svJigyoId,
    // 職員ID：共通情報.職員ID
    shokuId: localOneway.commonInfo.shokuId,
    // 利用者ID：親画面.利用者ID
    userId: localOneway.or58240Param.userId,
    // 担当者ID：親画面.担当者ID
    tantoId: localOneway.or58240Param.tantoId,
    // セクション名：親画面.セクション名
    sectionName: localOneway.or58240Param.sectionName,
    // インデックス：0
    index: Or58240Const.DEFAULT.INDEX,
    // 適用事業所IDリスト：共通情報.適用事業所IDリスト
    svJigyoIds: localOneway.commonInfo.svJigyoIdList as string[],
    // メニュー２名称
    menu2Knj: Or58240Const.DEFAULT.NM_MNU2_3GK_FREE_FREE_ASSESSMENT,
    // メニュー３名称
    menu3Knj: Or58240Const.DEFAULT.NM_MNU3_3GK_FREE_FREE_CHECK_ITEM,
    // 個人情報使用フラグ：0 ※0：不使用、1：使用
    kojinhogoUsedFlg: Or58240Const.DEFAULT.KOJINHOGO_USED_FLG,
    // 個人情報番号：0 ※0：主に日誌以外、1：主に日誌系
    sectionAddNo: Or58240Const.DEFAULT.SECTION_ADD_NO,
  }
  const res: FreeAssessmentCheckPrintSettingsSelectOutEntity = await ScreenRepository.update(
    'freeAssessmentCheckPrintSettingsUpdate',
    inputData
  )
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    ledgerInfo.prtList = res.data.prtList
    // 履歴一覧データの一時保存
    localOneway.initCheckItemHistoryList = res.data.checkItemHistoryList
    const prtList: Mo01334Items[] = []
    // Onewayの期間管理フラグを設定
    localOneway.kikanFlg = res.data.kikanFlg
    // 氏名等を伏字にするチェックボックスを設定
    mo00018AmikakeType.value.modelValue = res.data.sysIniInfo.amikakeFlg === '1'
    // 履歴一覧の期間管理フラグを設定
    orX0128Oneway.kikanFlg = res.data.kikanFlg
    let selectable = true
    for (const item of res.data.prtList) {
      if (res.data.prtList.length === 1) {
        selectable = false
      }
      prtList.push({
        id: item.index,
        mo01337OnewayLedgerName: {
          value: item.defPrtTitle,
          unit: '',
        } as Mo01337OnewayType,
        prtTitle: item.prtTitle,
        prnDate: item.prnDate,
        sectionNo: item.sectionNo,
        selectable: selectable,
        profile: item.profile,
        index: item.index,
        prtNo: item.prtNo,
        honorificsCheckBoxValue: item.param03,
        honorificsTextValue: item.param04,
      } as Mo01334Items)
    }
    // 初期化フラグ設定
    initFlg.value = true
    // 帳票一覧テーブルにデータを追加
    mo01334OnewayLedger.value.items = prtList
    // 履歴一覧データ処理
    setHistoryData(res.data.checkItemHistoryList)
    // 「出力帳票名」選択し、画面データ設定
    selectLedgerName(res.data.prtList[0]?.index)
    // 履歴一覧明細に親画面.アセスメントIDが存在する場合
    if (localOneway.or58240Param.assId) {
      // 履歴一覧明細に親画面.アセスメントIDを対するレコードを選択状態にする
      orX0128Oneway.initSelectId = (
        orX0128Oneway.items.findIndex((item) => item.assId === localOneway.or58240Param.assId) + 1
      ).toString()
    }
  }
}

/**
 * 「出力帳票名」選択
 *
 * @param selectId - 出力帳票ID
 */
const selectLedgerName = (selectId: string) => {
  for (const item of mo01334OnewayLedger.value.items) {
    if (selectId === item.id && item.mo01337OnewayLedgerName) {
      // タイトルテキストフィールド設定
      mo00045TitleType.value.value = item.prtTitle as string
      // 「出力帳票名」選択行設定
      if (mo01334OnewayLedger.value.items.length > 1) {
        mo01334TypeLedger.value.value = item.id
      }
      // 日付表示有無を設定
      mo00039DatePrintType.value = item.prnDate as string
      // 敬称を変更するチェックボックスを設定
      mo00018HonorificsType.value.modelValue =
        item.honorificsCheckBoxValue === Or58240Const.DEFAULT.CHECKBOX_STATUS_CHECK_IN
      // 敬称テキストフィールドを設定
      mo00045HonorificsType.value.value = item.honorificsTextValue as string
      // プロファイルを設定
      ledgerInfo.profile = item.profile as string
      // 出力帳票名一覧の選択行番号を設定
      ledgerInfo.index = item.index as string
      // セクション番号を設定
      ledgerInfo.sectionNo = item.sectionNo as string
      // 出力帳票名」選択行はチェック項目利用者一覧表の場合
      if (item.id === '3') {
        checkItemFlag.value = true
        userCols.value = 12
        orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
        orX0130Oneway.tableStyle = 'width:500px'
      } else {
        checkItemFlag.value = false
        userCols.value = 4
        if (mo00039UserSelectType.value === '0') {
          orX0130Oneway.tableStyle = 'width:204px'
          orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
        } else {
          orX0130Oneway.tableStyle = 'width:500px'
          orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
        }
      }
      // 出力帳票ID設定
      setReportId(item.id)
      break
    }
  }
}

/**
 * 出力帳票ID設定 TODO
 *
 * @param index - インデックス
 */
const setReportId = (index: string) => {
  switch (index) {
    case '1':
      ledgerInfo.reportId = Or58240Const.DEFAULT.PERSONAL_CHECK_ITEM_REPORT_TYPE_1
      break
    case '2':
      ledgerInfo.reportId = Or58240Const.DEFAULT.PERSONAL_CHECK_ITEM_REPORT_TYPE_2
      break
    case '3':
      ledgerInfo.reportId = Or58240Const.DEFAULT.CHECK_ITEM_REPORT
      break
    default:
      ledgerInfo.reportId = Or58240Const.DEFAULT.STR.EMPTY
      break
  }
}

/**
 * 選択行のシステムINI情報を取得する
 */
const getSelectedRowSysInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity = {
    // プロファイル：画面.帳票一覧選択行.プロファイル
    profile: ledgerInfo.profile,
    // システムコード：共通情報.システムコード
    gsyscd: systemCommonsStore.getSystemCode ?? '',
    // 職員ID：共通情報.職員ID
    shokuId: systemCommonsStore.getStaffId ?? '',
    // 個人情報使用フラグ：0 ※0：不使用、1：使用
    kojinhogoUsedFlg: Or58240Const.DEFAULT.KOJINHOGO_USED_FLG,
    // 個人情報番号：0 ※0：主に日誌以外、1：主に日誌系
    sectionAddNo: Or58240Const.DEFAULT.SECTION_ADD_NO,
  } as IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity
  const res: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity =
    await ScreenRepository.update('freeAssessmentFacePrintSettingsPrtNoChangeUpdate', inputData)
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    ledgerInfo.sysIniInfo = res.data.sysIniInfo
    mo00018AmikakeType.value.modelValue = res.data.sysIniInfo.amikakeFlg === '1'
  }
}

/**
 * 印刷設定履歴リスト取得
 */
const getPrintSettingsHistoryList = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: FreeAssessmentCheckPrintSettingsHistorySelectInEntity = {
    // 適用事業所IDリスト：共通情報.適用事業所IDリスト
    svJigyoIds: systemCommonsStore.getSvJigyoIdList as string[],
    // 利用者ID：画面.利用者一覧に選択した利用者ID
    userId: ledgerInfo.selectedUserId,
    // 期間管理フラグ
    kikanFlg: localOneway.kikanFlg,
  }
  const res: FreeAssessmentCheckPrintSettingsHistorySelectOutEntity = await ScreenRepository.select(
    'freeAssessmentCheckPrintSettingsHistorySelect',
    inputData
  )
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    // 履歴一覧データの一時保存
    localOneway.initCheckItemHistoryList = res.data.checkItemHistoryList
    // 履歴一覧データ処理
    setHistoryData(res.data.checkItemHistoryList)
  }
}

/**
 * 履歴一覧データ処理
 *
 * @param checkItemHistoryList - 履歴リスト
 */
const setHistoryData = (checkItemHistoryList: CheckItemHistoryEntity[]) => {
  // 期間管理フラグが「1:管理する」の場合
  if (localOneway.kikanFlg === Or58240Const.DEFAULT.KIKAN_FLG_MANAGE) {
    // ソート
    checkItemHistoryList.sort((a, b) => {
      if (b.sc1Id !== a.sc1Id) {
        return Number(b.sc1Id) - Number(a.sc1Id)
      }
      return new Date(b.startYmd).getTime() - new Date(a.startYmd).getTime()
    })
  }
  // 履歴テーブルデータ
  const orX0128TableData: OrX0128Items[] = []

  // 期間Id一時保存変数
  let tempSc1Id = ''
  checkItemHistoryList.forEach((item) => {
    // 期間管理フラグが「1:管理する」の場合
    if (localOneway.kikanFlg === Or58240Const.DEFAULT.KIKAN_FLG_MANAGE) {
      if (tempSc1Id !== item.sc1Id) {
        orX0128TableData.push({
          id: Or58240Const.DEFAULT.STR.EMPTY,
          sc1Id: item.sc1Id,
          startYmd: item.startYmd,
          endYmd: item.endYmd,
          isPeriodManagementMergedRow: true,
          planPeriod:
            t('label.plan-period') +
            Or58240Const.DEFAULT.STR.SPLIT_COLON +
            item.startYmd +
            Or58240Const.DEFAULT.STR.SPLIT_TILDE +
            item.endYmd,
        } as OrX0128Items)
      }
      orX0128TableData.push({
        id: Or58240Const.DEFAULT.STR.EMPTY,
        sc1Id: item.sc1Id,
        startYmd: item.startYmd,
        endYmd: item.endYmd,
        sel: item.sel,
        assId: item.assId,
        createYmd: item.createYmd,
        shokuinKnj: item.shokuinKnj,
        youshikiKnj: item.youshikiKnj,
      } as OrX0128Items)

      // 期間Id一時保存
      tempSc1Id = item.sc1Id
    } else {
      orX0128TableData.push({
        id: Or58240Const.DEFAULT.STR.EMPTY,
        sc1Id: item.sc1Id,
        startYmd: item.startYmd,
        endYmd: item.endYmd,
        sel: item.sel,
        assId: item.assId,
        createYmd: item.createYmd,
        shokuinKnj: item.shokuinKnj,
        youshikiKnj: item.youshikiKnj,
      } as OrX0128Items)
    }
  })
  orX0128TableData.forEach((item, index) => (item.id = String(index + 1)))
  // 履歴一覧テーブルを設定
  orX0128Oneway.items = orX0128TableData
}

/**
 * 画面項目変更した後、出力帳票印刷情報リストを更新
 *
 * @param itemType -項目区分
 *  1:帳票タイトル 2:氏名等を伏字にする 3:日付印刷区分 4:敬称を変更する 5:敬称テキスト
 *
 * @param id -現在出力帳票印刷選択行のid
 */
const changePrintList = (itemType: string, id: string) => {
  switch (itemType) {
    case '1':
      // 個人別チェック項目一覧表(TYPE1)
      if (id === '1' && ledgerInfo.prtList[0] && mo01334OnewayLedger.value.items[0]) {
        ledgerInfo.prtList[0].defPrtTitle = mo00045TitleType.value.value
        mo01334OnewayLedger.value.items[0].prtTitle = mo00045TitleType.value.value
      }
      // 個人別チェック項目一覧表(TYPE2)
      else if (id === '2' && ledgerInfo.prtList[1] && mo01334OnewayLedger.value.items[1]) {
        ledgerInfo.prtList[1].defPrtTitle = mo00045TitleType.value.value
        mo01334OnewayLedger.value.items[1].prtTitle = mo00045TitleType.value.value
      }
      // チェック項目利用者一覧表
      else if (id === '3' && ledgerInfo.prtList[2] && mo01334OnewayLedger.value.items[2]) {
        ledgerInfo.prtList[2].defPrtTitle = mo00045TitleType.value.value
        mo01334OnewayLedger.value.items[2].prtTitle = mo00045TitleType.value.value
      }
      break
    case '2':
      // 個人別チェック項目一覧表(TYPE1)
      ledgerInfo.sysIniInfo.amikakeFlg = mo00018AmikakeType.value.modelValue ? '1' : '0'
      break
    case '3':
      // 個人別チェック項目一覧表(TYPE1)
      if (id === '1' && ledgerInfo.prtList[0] && mo01334OnewayLedger.value.items[0]) {
        ledgerInfo.prtList[0].prnDate = mo00039DatePrintType.value
        mo01334OnewayLedger.value.items[0].prnDate = mo00039DatePrintType.value
      }
      // 個人別チェック項目一覧表(TYPE2)
      else if (id === '2' && ledgerInfo.prtList[1] && mo01334OnewayLedger.value.items[1]) {
        ledgerInfo.prtList[1].prnDate = mo00039DatePrintType.value
        mo01334OnewayLedger.value.items[1].prnDate = mo00039DatePrintType.value
      }
      // チェック項目利用者一覧表
      else if (id === '3' && ledgerInfo.prtList[2] && mo01334OnewayLedger.value.items[2]) {
        ledgerInfo.prtList[2].prnDate = mo00039DatePrintType.value
        mo01334OnewayLedger.value.items[2].prnDate = mo00039DatePrintType.value
      }
      break
    case '4':
      // 個人別チェック項目一覧表(TYPE1)
      if (id === '1' && ledgerInfo.prtList[0] && mo01334OnewayLedger.value.items[0]) {
        ledgerInfo.prtList[0].param03 = mo00018HonorificsType.value.modelValue ? '1' : '0'
        mo01334OnewayLedger.value.items[0].honorificsCheckBoxValue = mo00018HonorificsType.value
          .modelValue
          ? '1'
          : '0'
      }
      // 個人別チェック項目一覧表(TYPE2)
      else if (id === '2' && ledgerInfo.prtList[1] && mo01334OnewayLedger.value.items[1]) {
        ledgerInfo.prtList[1].param03 = mo00018HonorificsType.value.modelValue ? '1' : '0'
        mo01334OnewayLedger.value.items[1].honorificsCheckBoxValue = mo00018HonorificsType.value
          .modelValue
          ? '1'
          : '0'
      }
      // チェック項目利用者一覧表
      else if (id === '3' && ledgerInfo.prtList[2] && mo01334OnewayLedger.value.items[2]) {
        ledgerInfo.prtList[2].param03 = mo00018HonorificsType.value.modelValue ? '1' : '0'
        mo01334OnewayLedger.value.items[2].honorificsCheckBoxValue = mo00018HonorificsType.value
          .modelValue
          ? '1'
          : '0'
      }
      break
    case '5':
      // 個人別チェック項目一覧表(TYPE1)
      if (id === '1' && ledgerInfo.prtList[0]) {
        ledgerInfo.prtList[0].param04 = mo00045HonorificsType.value.value
        mo01334OnewayLedger.value.items[0].honorificsTextValue = mo00045HonorificsType.value.value
      }
      // 個人別チェック項目一覧表(TYPE2)
      else if (id === '2' && ledgerInfo.prtList[1]) {
        ledgerInfo.prtList[1].param04 = mo00045HonorificsType.value.value
        mo01334OnewayLedger.value.items[1].honorificsTextValue = mo00045HonorificsType.value.value
      }
      // チェック項目利用者一覧表
      else if (id === '3' && ledgerInfo.prtList[2]) {
        ledgerInfo.prtList[2].param04 = mo00045HonorificsType.value.value
        mo01334OnewayLedger.value.items[2].honorificsTextValue = mo00045HonorificsType.value.value
      }
      break
  }
}

/**
 * 画面印刷設定内容を保存
 *
 * @returns -保存出力エンティティ
 */
const save = async (): Promise<IFreeAssessmentFacePrintSettingsUpdateOutEntity> => {
  // バックエンドAPIから印刷設定情報保存
  const inputData: IFreeAssessmentFacePrintSettingsUpdateInEntity = {
    // システム略称："3gk"
    sysRyaku: Or58240Const.DEFAULT.SYS_RYAKU,
    // セクション名：親画面.セクション名
    sectionName: localOneway.or58240Param.sectionName,
    // システムコード：共通情報.システムコード
    gsyscd: systemCommonsStore.getSystemCode,
    // 職員ID：共通情報.職員ID
    shokuId: systemCommonsStore.getStaffId,
    // 法人ID：共通情報.法人ID
    houjinId: systemCommonsStore.getHoujinId,
    // 施設ID：親画面.施設ID
    shisetuId: localOneway.or58240Param.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.or58240Param.svJigyoId,
    // インデックス：画面.出力帳票名一覧に選択行番号
    index: ledgerInfo.index,
    // システムINI情報：画面.システムINI情報
    sysIniInfo: {
      amikakeFlg: mo00018AmikakeType.value ? '1' : '0',
      amikakeModifiedCnt: ledgerInfo.sysIniInfo.amikakeModifiedCnt,
      iso9001Flg: ledgerInfo.sysIniInfo.iso9001Flg,
      iso9001ModifiedCnt: ledgerInfo.sysIniInfo.iso9001ModifiedCnt,
      kojinhogoUsedFlg: Or58240Const.DEFAULT.KOJINHOGO_USED_FLG,
      kojinhogoFlg: ledgerInfo.sysIniInfo.kojinhogoFlg,
      kojinhogoModifiedCnt: ledgerInfo.sysIniInfo.kojinhogoModifiedCnt,
      sectionAddNo: Or58240Const.DEFAULT.SECTION_ADD_NO,
    },
    // 印刷設定情報リスト：画面.印刷設定情報リスト
    prtList: ledgerInfo.prtList,
  } as IFreeAssessmentFacePrintSettingsUpdateInEntity
  const res: IFreeAssessmentFacePrintSettingsUpdateOutEntity = await ScreenRepository.update(
    'freeAssessmentFacePrintSettingsUpdate',
    inputData
  )
  return res
}

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  // 画面の印刷設定情報を保存する
  await save()
  setState({
    isOpen: false,
    param: {} as Or58240Param,
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const pdfDownload = async () => {
  // 選択された帳票のプロファイルが''の場合
  if (!ledgerInfo.profile) {
    // メッセージを表示
    const dialogResult = await openErrorDialog(t('message.e-cmn-40172'))
    // はい
    if (dialogResult === Or58240Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
      // 処理終了
      return
    }
  }
  // 記入用シートを印刷するチェックボックスがオフ、且つ、利用者一覧に選択されない場合
  if (
    !mo00018EntryType.value.modelValue &&
    OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList.length === 0
  ) {
    // メッセージを表示
    const dialogResult = await openConfirmDialog(t('message.i-cmn-11393'))
    // はい
    if (dialogResult === Or58240Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
      // 処理終了
      return
    }
  }
  // 記入用シートを印刷するチェックボックスがオフ、且つ、利用者選択が「単一」
  // 且つ、履歴一覧に選択されない場合
  if (
    !mo00018EntryType.value.modelValue &&
    mo00039UserSelectType.value === Or58240Const.DEFAULT.TANI &&
    OrX0128Logic.event.get(orX0128.value.uniqueCpId)?.orX0128DetList.length === 0
  ) {
    // メッセージを表示
    const dialogResult = await openConfirmDialog(t('message.i-cmn-11455'))
    // はい
    if (dialogResult === Or58240Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
      // 処理終了
      return
    }
  }
  // 画面の印刷設定を保存する
  await save()

  // 利用者選択が「単一」、且つ、履歴選択が「単一」の場合
  if (
    mo00039UserSelectType.value === Or58240Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or58240Const.DEFAULT.TANI
  ) {
    // 帳票出力
    await reportOutputPdf()
    return
  }

  // 利用者選択が「単一」、または、履歴選択が「複数」の場合
  if (
    mo00039UserSelectType.value === Or58240Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or58240Const.DEFAULT.HUKUSUU
  ) {
    // 印刷設定情報リストを作成
    createReportOutputData(ledgerInfo.sectionNo)
  }
  // 利用者選択が「複数」の場合
  if (mo00039UserSelectType.value === Or58240Const.DEFAULT.HUKUSUU) {
    // 印刷設定情報リストを再取得する
    await getPrintSettingsSubject()
  }

  // 「AC019-6」または「AC019-7-2」で取得した印刷用情報リスト＞0件の場合
  if (orX0117Oneway.historyList.length > 0) {
    // OrX0117のダイアログ開閉状態を更新する
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 印刷設定情報リストを作成
 *
 * @param sectionNo - 帳票番号
 */
const createReportOutputData = (sectionNo: string) => {
  const orX0117HistoryList: OrX0117History[] = []
  for (const history of ledgerInfo.historyList) {
    const reportData = {
      // 印刷設定
      printConfigure: {
        amikakeFlg: mo00018AmikakeType.value.modelValue ? '1' : '0',
        shiTeiKubun: mo00039DatePrintType.value,
        shiTeiDate: mo00020DesignationType.value.value,
      },
      // 印刷オプション
      printOption: {
        emptyFlg: mo00018EntryType.value.modelValue ? '1' : '0',
      },
      // 印刷対象履歴リスト
      printHistoryList: [],
    } as CheckItemUserListSelectInEntity

    const outputLedgerPrintList: OutputLedgerPrintEntity[] = []
    for (const item of ledgerInfo.prtList) {
      if (sectionNo === item.sectionNo) {
        // 印刷対象処理
        const printSubject = setPrintSubject(item)
        outputLedgerPrintList.push(printSubject)
      }
    }

    reportData.printHistoryList.push({
      userId:
        ledgerInfo.userList.length > 0
          ? ledgerInfo.userList[0].userId
          : Or58240Const.DEFAULT.STR.EMPTY,
      userName:
        ledgerInfo.userList.length > 0
          ? ledgerInfo.userList[0].userName
          : Or58240Const.DEFAULT.STR.EMPTY,
      sc1Id: history.sc1Id as string,
      startYmd: history.startYmd as string,
      endYmd: history.endYmd as string,
      youshikiKnj: history.youshikiKnj as string,
      result: Or58240Const.DEFAULT.STR.EMPTY,
      outputLedgerPrintList: outputLedgerPrintList,
    })
    orX0117HistoryList.push({
      reportId: ledgerInfo.reportId,
      outputType: reportOutputType.DOWNLOAD,
      reportData: reportData,
      userName:
        ledgerInfo.userList.length > 0
          ? ledgerInfo.userList[0].userName
          : Or58240Const.DEFAULT.STR.EMPTY,
      historyDate: history.createYmd as string,
      result: Or58240Const.DEFAULT.STR.EMPTY,
    })
  }
  orX0117Oneway.historyList = orX0117HistoryList
}

/**
 * 印刷対象処理
 *
 * @param item -帳票エンティティ
 *
 * @returns 印刷対象
 */
const setPrintSubject = (item: PrtEntity): OutputLedgerPrintEntity => {
  return {
    defPrtTitle: item.defPrtTitle,
    prtTitle: item.prtTitle,
    sectionNo: item.sectionNo,
    prtNo: item.prtNo,
    choPro: item.profile,
    sectionName: localOneway.or58240Param.sectionName,
    dwObject: item.dwobject,
    prtOrient: item.prtOrient,
    prtSize: item.prtSize,
    listTitle: item.listTitle,
    mTop: item.mtop,
    mBottom: item.mbottom,
    mLeft: item.mleft,
    mRight: item.mright,
    ruler: item.ruler,
    prnDate: mo00039DatePrintType.value,
    prnShoku: item.prnshoku,
    serialFlg: item.serialFlg,
    modFlg: item.modFlg,
    secFlg: item.secFlg,
    serialHeight: item.serialHeight,
    serialPagelen: item.serialPagelen,
    zoomRate: item.zoomRate,
    param01: item.param01,
    param02: item.param02,
    param03: mo00018HonorificsType.value.modelValue ? '1' : '0',
    param04: mo00045HonorificsType.value.value,
    param05: item.param05,
    param06: item.param06,
    param07: item.param07,
    param08: item.param08,
    param09: item.param09,
    param10: item.param10,
    param11: item.param11,
    param12: item.param12,
    param13: item.param13,
    param14: item.param14,
    param15: item.param15,
    param16: item.param16,
    param17: item.param17,
    param18: item.param18,
    param19: item.param19,
    param20: item.param20,
    param21: item.param21,
    param22: item.param22,
    param23: item.param23,
    param24: item.param24,
    param25: item.param25,
    param26: item.param26,
    param27: item.param28,
    param28: item.param28,
    param29: item.param29,
    param30: item.param30,
    param31: item.param31,
    param32: item.param32,
    param33: item.param33,
    param34: item.param34,
    param35: item.param35,
    param36: item.param36,
    param37: item.param37,
    param38: item.param38,
    param39: item.param39,
    param40: item.param40,
    param41: item.param41,
    param42: item.param42,
    param43: item.param43,
    param44: item.param44,
    param45: item.param45,
    param46: item.param46,
    param47: item.param47,
    param48: item.param48,
    param49: item.param49,
    param50: item.param50,
  }
}

/**
 * 印刷処理
 */
const reportOutputPdf = async () => {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    const reportData: CheckItemUserListSelectInEntity = {
      // 印刷設定
      printConfigure: {
        amikakeFlg: mo00018AmikakeType.value.modelValue ? '1' : '0',
        shiTeiKubun: mo00039DatePrintType.value,
        shiTeiDate: mo00020DesignationType.value.value,
      },
      // 印刷オプション
      printOption: {
        emptyFlg: mo00018EntryType.value.modelValue ? '1' : '0',
      },
      // 印刷対象履歴リスト
      printHistoryList: [],
    }

    const outputLedgerPrintList: OutputLedgerPrintEntity[] = []
    for (const item of ledgerInfo.prtList) {
      if (item.sectionNo === ledgerInfo.sectionNo) {
        // 印刷対象処理
        const printSubject = setPrintSubject(item)
        outputLedgerPrintList.push(printSubject)
      }
    }

    // TODO 印刷設定情報リストパラメータを作成
    reportData.printHistoryList.push({
      userId:
        ledgerInfo.userList.length > 0
          ? ledgerInfo.userList[0].userId
          : Or58240Const.DEFAULT.STR.EMPTY,
      userName:
        ledgerInfo.userList.length > 0
          ? ledgerInfo.userList[0].userName
          : Or58240Const.DEFAULT.STR.EMPTY,
      sc1Id:
        ledgerInfo.historyList.length > 0
          ? (ledgerInfo.historyList[0].sc1Id as string)
          : Or58240Const.DEFAULT.STR.EMPTY,
      startYmd:
        ledgerInfo.historyList.length > 0
          ? (ledgerInfo.historyList[0].startYmd as string)
          : Or58240Const.DEFAULT.STR.EMPTY,
      endYmd:
        ledgerInfo.historyList.length > 0
          ? (ledgerInfo.historyList[0].endYmd as string)
          : Or58240Const.DEFAULT.STR.EMPTY,
      youshikiKnj:
        ledgerInfo.historyList.length > 0
          ? (ledgerInfo.historyList[0].youshikiKnj as string)
          : Or58240Const.DEFAULT.STR.EMPTY,
      result: Or58240Const.DEFAULT.STR.EMPTY,
      outputLedgerPrintList: outputLedgerPrintList,
    } as PrintHistoryEntity)
    // 帳票出力
    await reportOutput(ledgerInfo.reportId, reportData, reportOutputType.DOWNLOAD)
  } catch (e) {
    $log.debug('帳票の出力に失敗しました。', ledgerInfo.reportId, reportOutputType.DOWNLOAD, e)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * 印刷設定情報リストを再取得する
 */
const getPrintSettingsSubject = async () => {
  // 印刷設定情報リストを作成する
  const inputData: FreeAssessmentCheckPrintSettingsSubjectSelectInEntity = {
    // 利用者リスト：画面.利用者リストに選択対象
    userList: ledgerInfo.userList,
    // 適用事業所IDリスト：共通情報.適用事業所IDリスト
    svJigyoIds: systemCommonsStore.getSvJigyoIdList as string[],
    // 基準日：画面.基準日
    kijunbiYmd: mo00020BasicType.value.value,
  }
  const res: FreeAssessmentCheckPrintSettingsSubjectSelectOutEntity = await ScreenRepository.select(
    'freeAssessmentCheckPrintSettingsSubjectSelect',
    inputData
  )

  const orX0117HistoryList: OrX0117History[] = []
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    for (const history of res.data.ledgerPrintResultInfoList) {
      // 利用者複数の場合
      const reportData = {
        // 印刷設定
        printConfigure: {
          amikakeFlg: mo00018AmikakeType.value.modelValue ? '1' : '0',
          shiTeiKubun: mo00039DatePrintType.value,
          shiTeiDate: mo00020DesignationType.value.value,
        },
        // 印刷オプション
        printOption: {
          emptyFlg: mo00018EntryType.value.modelValue ? '1' : '0',
        },
        // 印刷対象履歴リスト
        printHistoryList: [],
      } as CheckItemUserListSelectInEntity

      const outputLedgerPrintList: OutputLedgerPrintEntity[] = []
      for (const item of ledgerInfo.prtList) {
        if (ledgerInfo.sectionNo === item.sectionNo) {
          // 印刷対象処理
          const printSubject = setPrintSubject(item)
          outputLedgerPrintList.push(printSubject)
        }
      }

      reportData.printHistoryList.push({
        userId: history.userId,
        userName: history.userName,
        sc1Id: Or58240Const.DEFAULT.STR.EMPTY,
        startYmd: Or58240Const.DEFAULT.STR.EMPTY,
        endYmd: Or58240Const.DEFAULT.STR.EMPTY,
        youshikiKnj: Or58240Const.DEFAULT.STR.EMPTY,
        result: history.result,
        outputLedgerPrintList: outputLedgerPrintList,
      })
      orX0117HistoryList.push({
        reportId: ledgerInfo.reportId,
        outputType: reportOutputType.DOWNLOAD,
        reportData: reportData,
        userName: history.userName,
        historyDate: Or58240Const.DEFAULT.STR.EMPTY,
        result: Or58240Const.DEFAULT.STR.EMPTY,
      })
    }
  }
  orX0117Oneway.historyList = orX0117HistoryList
}

/**
 * エラーダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
const openErrorDialog = async (paramDialogText: string): Promise<'yes' | 'no'> => {
  // エラーダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // エラーダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result: 'yes' | 'no' = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // エラーダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
const openConfirmDialog = async (paramDialogText: string): Promise<'yes' | 'no'> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result: 'yes' | 'no' = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
const openWarnDialog = async (paramDialogText: string): Promise<'yes' | 'no'> => {
  // 警告ダイアログを開く
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 警告ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result: 'yes' | 'no' = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 警告ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * タイトルのfocus一時保存
 */
const saveTitle = () => {
  tempTitle.value = mo00045TitleType.value.value
}
/**
 * タイトルのblur復元
 */
const recoveryTitle = async () => {
  // タイトルが空の場合
  if (!mo00045TitleType.value.value) {
    // メッセージを表示
    const dialogResult = await openWarnDialog(t('message.w-cmn-20845'))
    // はい
    if (
      dialogResult === Or58240Const.DEFAULT.MESSAGE_BTN_TYPE_YES ||
      dialogResult === Or58240Const.DEFAULT.MESSAGE_BTN_TYPE_NO
    ) {
      // タイトル復元
      mo00045TitleType.value.value = tempTitle.value
    }
  }
}

/**
 * 右上部'×'押す
 *
 * @param mo00024Type -ダイアログmodel
 */
const topRightClose = async (mo00024Type: Mo00024Type) => {
  if (!mo00024Type.isOpen) {
    await close()
  }
}

/**
 * 担当ケアマネプルダウン
 *
 * @param result - 戻り値
 */
const orx0145UpdateModelValue = (result: OrX0145Type) => {
  if (result) {
    if (result.value) {
      if (!Array.isArray(result.value) && 'chkShokuId' in result.value) {
        // TODO API疎通時に確認
        orX0130Oneway.tantouCareManager = result.value.chkShokuId
      }
    }

    // 画面.利用者選択が単一の場合
    if (Or58240Const.DEFAULT.TANI === mo00039UserSelectType.value) {
      // 画面.利用者一覧明細の1件目レコードを選択状態にする
      orX0130Oneway.userId = Or58240Const.DEFAULT.STR.EMPTY
    }
  }
}

/**
 * 初期選択データ設定
 */
const selectRowDataSetting = () => {
  // フォーカス設定用イニシャル設定
  orX0130Oneway.focusSettingInitial = localOneway.or58240Param.focusSettingInitial
  orX0130Oneway.userId = localOneway.or58240Param.userId
  // 利用者一覧明細に親画面.利用者IDが存在する場合
  if (localOneway.or58240Param.userId) {
    // 利用者IDを対するレコードを選択状態にする
    orX0130Oneway.userId = localOneway.or58240Param.userId
  }
  // 親画面.利用者IDが存在しない場合
  else {
    orX0130Oneway.userId = Or58240Const.DEFAULT.STR.EMPTY
  }
  // 初期選択状態の担当者カウンタ値設定
  localOneway.orX0145Oneway.selectedUserCounter = localOneway.or58240Param.selectedUserCounter
}
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // 汎用コード取得API実行
  await initCodes()
  // 初期情報取得
  await getInitData()
  // 初期選択データ設定
  selectRowDataSetting()
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 「出力帳票名」選択の変更監視
 */
watch(
  () => mo01334TypeLedger.value.value,
  async (newVal, oldValue) => {
    if (!newVal) return
    if (oldValue && newVal !== oldValue) {
      // 「出力帳票名」選択した、画面データを変更
      selectLedgerName(newVal)
      // 選択行のシステムINI情報を取得する
      await getSelectedRowSysInfo()
    }
    // 日付印刷区分が2の場合
    if ('2' === mo00039DatePrintType.value) {
      // 指定日を活性表示にする。
      designationShowFlag.value = true
    } else {
      // 指定日を非表示にする。
      designationShowFlag.value = false
    }
  }
)

/**
 * タイトルテキストフィールドの変更監視
 */
watch(
  () => mo00045TitleType.value.value,
  () => {
    // 画面項目変更した後、出力帳票印刷情報リストを更新
    changePrintList('1', ledgerInfo.index)
  }
)

/**
 * 氏名等を伏字にするチェックボックスの変更監視
 */
watch(
  () => mo00018AmikakeType.value.modelValue,
  () => {
    // 画面項目変更した後、出力帳票印刷情報リストを更新
    changePrintList('2', ledgerInfo.index)
  }
)

/**
 * 日付印刷区分の変更監視
 */
watch(
  () => mo00039DatePrintType.value,
  (newValue) => {
    // 画面項目変更した後、出力帳票印刷情報リストを更新
    changePrintList('3', ledgerInfo.index)
    // 日付印刷区分が2の場合
    if ('2' === newValue) {
      // 指定日を活性表示にする。
      designationShowFlag.value = true
    } else {
      // 指定日を非表示にする。
      designationShowFlag.value = false
    }
  }
)

/**
 * 敬称を変更するチェックボックスの変更監視
 */
watch(
  () => mo00018HonorificsType.value.modelValue,
  (newValue) => {
    // 画面項目変更した後、出力帳票印刷情報リストを更新
    changePrintList('4', ledgerInfo.index)
    // 敬称を変更するチェックボックスが選択の場合、活性表示
    if (newValue) {
      localOneway.mo00045OneWayHonorifics.disabled = false
    } else {
      // 以外の場合、非活性表示
      localOneway.mo00045OneWayHonorifics.disabled = true
    }
  },
  { immediate: true }
)

/**
 * 敬称テキストフィールドの変更監視
 */
watch(
  () => mo00045HonorificsType.value.value,
  () => {
    // 画面項目変更した後、出力帳票印刷情報リストを更新
    changePrintList('5', ledgerInfo.index)
  }
)

/**
 * 利用者選択方法の変更監視
 */
watch(
  () => mo00039UserSelectType.value,
  async (newValue) => {
    // 利用者選択方法が「単一」 の場合
    if (newValue === '0') {
      orX0117Oneway.type = Or58240Const.DEFAULT.STR.TWO
      // 利用者一覧テーブルの幅は狭くなる
      userCols.value = 4
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width:204px'
      await nextTick()
      // 履歴一覧データ処理
      setHistoryData(localOneway.initCheckItemHistoryList)
      // 履歴初期化選択行IDの再設定
      orX0128Oneway.initSelectId = ''
    } else {
      orX0117Oneway.type = Or58240Const.DEFAULT.STR.ONE
      // 利用者一覧テーブルの幅は広くなる
      userCols.value = 12
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width:500px'
    }
  }
)

/**
 * 履歴選択方法の変更監視
 */
watch(
  () => mo00039HistorySelectType.value,
  async (newValue) => {
    // 履歴選択方法が「単一」 の場合
    if (newValue === '0') {
      orX0117Oneway.type = Or58240Const.DEFAULT.STR.TWO
      orX0128Oneway.singleFlg = OrX0128Const.DEFAULT.TANI
      await nextTick()
      // 履歴一覧データ処理
      setHistoryData(localOneway.initCheckItemHistoryList)
      // 履歴初期化選択行IDの再設定
      orX0128Oneway.initSelectId = ''
    } else {
      orX0117Oneway.type = Or58240Const.DEFAULT.STR.ZERO
      orX0128Oneway.singleFlg = OrX0128Const.DEFAULT.HUKUSUU
    }
  }
)

/**
 * 記入用シートを印刷するチェックボックス算出プロパティの監視
 */
watch(
  () => mo00018EntryDisabled.value,
  (newValue) => {
    if (newValue) {
      // 以外の場合、非活性表示
      localOneway.mo00018OneWayEntry.disabled = true
    } else {
      // 利用者選択方法が「単一」 また 履歴選択方法が「単一」の場合、活性表示。
      localOneway.mo00018OneWayEntry.disabled = false
    }
  },
  { immediate: true }
)

/**
 * 担当ケアマネプルダウンリスト算出プロパティの監視
 */
watch(
  () => orX0145Disabled.value,
  (newValue) => {
    if (newValue) {
      localOneway.orX0145Oneway.disabled = true
    } else {
      localOneway.orX0145Oneway.disabled = false
    }
  },
  { immediate: true }
)

/**
 * 「利用者一覧」明細選択の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue, oldValue) => {
    if (newValue?.clickFlg) {
      // 利用者一覧選択行 >0 の場合
      if (newValue.userList.length > 0) {
        ledgerInfo.selectedUserId = newValue.userList[0].userId
        ledgerInfo.selectedUserName =
          newValue.userList[0].name1Knj + Or58240Const.DEFAULT.SAPCE + newValue.userList[0].name2Knj
        ledgerInfo.userSelect = false

        // 利用者リスト再設定
        ledgerInfo.userList = []
        for (const item of newValue.userList) {
          if (item) {
            ledgerInfo.userList.push({
              userId: item.userId,
              userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
            } as UserEntity)
          }
        }

        // 履歴初期化選択行IDの再設定
        orX0128Oneway.initSelectId = ''

        // 利用者選択方法が「単一」の場合
        if (Or58240Const.DEFAULT.TANI === mo00039UserSelectType.value) {
          if (
            initFlg.value &&
            oldValue?.userList &&
            (oldValue?.userList.length ?? 0) > 0 &&
            !isEqual(newValue.userList, oldValue?.userList)
          ) {
            // 印刷設定履歴リスト取得
            await getPrintSettingsHistoryList()
            // 履歴初期化選択行IDの再設定
            orX0128Oneway.initSelectId = ''
          }
        }
      } else {
        ledgerInfo.userList = []
      }
    } else {
      ledgerInfo.userList = []
    }
  },
  { deep: true }
)

/**
 * 「履歴一覧」明細選択の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    if (!newValue) return
    if (newValue.historyDetClickFlg && newValue.orX0128DetList.length > 0) {
      ledgerInfo.historyList = []
      ledgerInfo.historyList = newValue.orX0128DetList
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
    @update:model-value="topRightClose"
  >
    <template #cardItem>
      <c-v-row
        class="or58240-row"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="2"
          class="table-header"
        >
          <!-- 帳票一覧テーブル -->
          <base-mo-01334
            v-model="mo01334TypeLedger"
            :oneway-model-value="mo01334OnewayLedger"
            class="list-wrapper"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="3"
          class="content-center"
        >
          <c-v-row
            no-gutter
            class="printerOption custom-col or58240-row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pl-2"
            >
              <!-- '基本設定' -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayBasicSettings"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="custom-col or58240-row"
          >
            <c-v-col
              cols="12"
              sm="3"
              class="label-left padding-bottom-none pl-2"
            >
              <!-- 'タイトル' -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="auto"
              class="label-right pt-2 pl-2 pr-2 w-100"
            >
              <!-- 指定された帳票名 -->
              <base-mo00045
                v-model="mo00045TitleType"
                :oneway-model-value="localOneway.mo00045OneWay"
                class="w-100"
                @focus="saveTitle"
                @blur="recoveryTitle"
              ></base-mo00045>
            </c-v-col>
            <div class="pb-2">
              <!-- 氏名等を伏字にするチェックボックス -->
              <base-mo00018
                v-model="mo00018AmikakeType"
                :oneway-model-value="localOneway.mo00018OneWayAmikake"
              ></base-mo00018>
            </div>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            no-gutter
            class="custom-col or58240-row"
          >
            <c-v-col
              cols="12"
              sm="7"
              style="padding-left: 0px; padding-right: 0px"
            >
              <!-- 日付印刷区分 -->
              <base-mo00039
                v-model="mo00039DatePrintType"
                :oneway-model-value="localOneway.mo00039OneWayDatePrint"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              class="date-area"
              style="padding-left: 0px; padding-right: 8px"
            >
              <!-- 指定日 -->
              <base-mo00020
                v-if="designationShowFlag"
                v-model="mo00020DesignationType"
                :oneway-model-value="localOneway.mo00020OneWayDesignation"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="printerOption custom-col or58240-row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pl-2"
            >
              <!-- '印刷オプション' -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="custom-col or58240-row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-bottom: 0px; padding-left: 0px"
            >
              <div class="d-flex align-center">
                <!-- 敬称を変更するチェックボックス -->
                <base-mo00018
                  v-model="mo00018HonorificsType"
                  :oneway-model-value="localOneway.mo00018OneWayHonorifics"
                />
                <span>（</span>
                <!-- 敬称テキストフィールド -->
                <base-mo00045
                  v-model="mo00045HonorificsType"
                  :oneway-model-value="localOneway.mo00045OneWayHonorifics"
                  class="honorifics-margin"
                />
                <span>）</span>
              </div>
              <!-- 記入用シートを印刷するチェックボックス -->
              <!-- チェック項目利用者一覧表以外の場合 -->
              <base-mo00018
                v-if="!checkItemFlag"
                v-model="mo00018EntryType"
                :oneway-model-value="localOneway.mo00018OneWayEntry"
              />
              <!-- 記入用シートを印刷するチェックボックス -->
              <!-- チェック項目利用者一覧表の場合 -->
              <base-mo00018
                v-if="checkItemFlag"
                v-model="mo00018EntryType_1"
                :oneway-model-value="localOneway.mo00018OneWayEntry_1"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="7"
          class="pa-0"
        >
          <c-v-row
            class="or58240-row"
            no-gutter
          >
            <c-v-col
              cols="4"
              class="pl-0 pb-0 pr-2"
            >
              <div
                v-if="mo00039UserSelectShowFlg && !mo00018EntryType.modelValue && !checkItemFlag"
              >
                <!-- 利用者選択ラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                  class="pl-2 pb-0"
                >
                </base-mo01338>
                <!-- 利用者選択方法ラジオボタン -->
                <base-mo00039
                  v-model="mo00039UserSelectType"
                  :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                >
                </base-mo00039>
              </div>
            </c-v-col>
            <c-v-col
              cols="4"
              class="pl-2"
            >
              <div
                v-if="mo00039HistorySelectShowFlg && !mo00018EntryType.modelValue && !checkItemFlag"
              >
                <!-- 履歴選択ラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                  class="pl-2"
                >
                </base-mo01338>
                <!-- 履歴選択方法ラジオボタン -->
                <base-mo00039
                  v-model="mo00039HistorySelectType"
                  :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                >
                </base-mo00039>
              </div>
              <div
                v-if="mo00020BasicShowFlg"
                class="basic-date-label"
              >
                <!-- 年月日選択セクション -->
                <base-mo00020
                  v-model="mo00020BasicType"
                  :oneway-model-value="localOneway.mo00020OneWayBasic"
                />
              </div>
            </c-v-col>
            <c-v-col
              cols="4"
              class="pl-2"
            >
              <div v-if="!mo00018EntryType.modelValue || localOneway.or58240Param.basicDate">
                <!-- 担当ケアマネプルダウン -->
                <g-custom-or-x-0145
                  v-bind="orX0145"
                  v-model="orX0145Type"
                  :oneway-model-value="localOneway.orX0145Oneway"
                  @update:model-value="orx0145UpdateModelValue"
                />
              </div>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            class="or58240-row"
            no-gutter
          >
            <c-v-col
              v-if="checkItemFlag || !mo00018EntryType.modelValue"
              :cols="userCols"
              class="user-table-area pa-0 pl-2 pb-2 pt-2"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="initFlg"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo00039UserSelectType === '0' && !mo00018EntryType.modelValue && !checkItemFlag"
              cols="8"
              class="pa-2"
            >
              <div>
                <!-- 計画期間＆履歴一覧 -->
                <g-custom-or-x-0128
                  v-bind="orX0128"
                  :oneway-model-value="orX0128Oneway"
                ></g-custom-or-x-0128>
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="ml-2 mr-0"
          @click="pdfDownload"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813 v-bind="or21813" />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <!-- Or21815:有機体:警告ダイアログ -->
  <g-base-or-21815 v-bind="or21815" />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
</template>
<style>
.or58240-content {
  padding: 0px !important;
}
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
.or58240-row {
  margin: 0px !important;
}

// 帳票一覧表
.table-header {
  padding: 8px;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}
:deep(.table-header .mr-2) {
  margin-right: 0px !important;
}
.content-center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label-left {
    padding-right: 0px;
  }

  .label-right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .custom-col {
    margin-left: 0px;
    margin-right: 0px;
  }
}
.padding-bottom-none {
  padding-bottom: 0px;
}
:deep(.honorifics-margin) {
  margin-left: 8px !important;
  margin-right: 8px !important;
}
.parentheses-class {
  text-align: center;
}

// 出力帳票名一覧ヘーダ
:deep(.v-data-table-header__content) {
  font-weight: bold;
  font-size: 14px;
}
:deep(.basic-date-label > .v-sheet > .text-date-area-style > .pt-1) {
  padding-left: 8px !important;
  padding-top: 0px !important;
  margin-top: 0px !important;
  margin-bottom: 5px !important;
}
// 敬称を変更するラベルfontweight
:deep(.honorifics-label .v-label) {
  font-weight: bold !important;
}
// 記入用シートを印刷するラベルfontweight
:deep(.entry-label .v-label) {
  font-weight: bold !important;
}
// '基本設定'margin
:deep(.ml-4) {
  margin-left: 0px !important;
}
// 担当ケアマネ
:deep(.tanto-label .v-col) {
  height: 18px !important;
}
// 利用者一覧区域
:deep(.user-table-area > .v-row > .v-col) {
  padding: 0px 0px 0px 8px;
}
// 指定日
:deep(.date-area .v-field__input) {
  padding-left: 12px !important;
}
</style>
