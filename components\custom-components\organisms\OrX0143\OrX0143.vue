<script setup lang="ts">
/**
 * OrX0143:有機体:履歴一覧情報
 * 履歴一覧情報
 *
 * @description
 * 履歴一覧情報画面では、内容を選択し、呼び出し元の画面に反映します。
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { groupBy } from 'lodash'
import { OrX0143Const } from '../OrX0143/OrX0143.constants'
import { OrX0143Const } from './OrX0143.constants'
import type { OrX0143Selected, OrX0143TableData, SortConfig } from './OrX0143.type'
import type {
  OrX0143EventType,
  OrX0143OnewayType,
} from '~/types/cmn/business/components/OrX0143Type'
import { useScreenEventStatus, useSetupChildProps } from '~/composables/useComponentVue'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: OrX0143OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: OrX0143OnewayType = {
  // 期間管理フラグ
  kikanFlg: '',
  // 単一複数フラグ(0:単一,1:複数)
  singleFlg: '',
  // 表形式スタイル設定
  tableStyle: 'width:520px',
  // 期間履歴タイトルリスト
  kikanRirekiTitleList: [],
  // 期間履歴情報リスト
  rirekiList: [],
}

const localOneway = reactive({
  orX0143: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
    rirekiList: {
      ...props.onewayModelValue.rirekiList.map((item, index) => ({
        ...item,
        id: index.toString(),
      })),
    },
  },
})

// テーブルヘッダ
const headers = computed(() => {
  const headersList: { title: string; key: string; style: string; sortable: boolean }[] = []
  if (localOneway.orX0143.kikanRirekiTitleList?.length > 0) {
    localOneway.orX0143.kikanRirekiTitleList.forEach((item) => {
      headersList.push({
        // タイトル
        title: t(`label.${item.title}`),
        // key
        key: item.key,
        // 列幅
        style: `width:${item.width}px;min-width:${item.width}px`,
        sortable: false,
      })
    })
  }
  return headersList
})

const multiHeaders = computed(() => {
  return [{ title: '', key: 'sel', sortable: false }].concat(headers.value)
})
/**************************************************
 * Pinia
 **************************************************/
const { setEvent } = useScreenEventStatus<OrX0143EventType>({
  cpId: OrX0143Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
useSetupChildProps(props.uniqueCpId, {})

/**************************************************
 * 変数定義
 **************************************************/
const checkBoxLabel = ref<string>('')
const historyData = ref<OrX0143TableData[]>([])
let selectedRows = ref<boolean[]>([])

/** 表の双方向モデル */
const tableSelectArr = ref<string[]>([])
/** 選択結果が保持 */
const selectedHist = ref<OrX0143Selected>({
  taniSelectedId: '',
  hukusuuSelectedId: [],
} as OrX0143Selected)
// 全選択フラグ
const allSelectVal = ref<boolean>(false)
/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**
 * 明細行クリックイベント
 *
 * @param row - 行情報
 */
const handleRowClick = (row: OrX0143TableData) => {
  if (
    tableSelectArr.value.includes(row.id!) &&
    localOneway.orX0143.singleFlg === OrX0143Const.DEFAULT.HUKUSUU
  ) {
    // チェックを外す
    tableSelectArr.value = tableSelectArr.value.filter((i) => i !== row.id)
  } else {
    // 未チェックの場合は追加
    if (localOneway.orX0143.singleFlg === OrX0143Const.DEFAULT.TANI) {
      tableSelectArr.value = [row.id!]
    } else {
      tableSelectArr.value = [...tableSelectArr.value, ...[row.id!]]
    }
  }
}

/**
 * 履歴一覧の「選択」クリック
 *
 * @param index - インデックス
 */
const checkedChange = (index: number) => {
  if (!historyData.value[index].planPeriod) {
    // 複数
    if (localOneway.orX0143.singleFlg !== OrX0143Const.DEFAULT.TANI) {
      selectedRows.value[index] = !selectedRows.value[index]
      for (let i = 0; i < selectedRows.value.length; i++) {
        if (!historyData.value[i].planPeriod && !selectedRows.value[i]) {
          allSelectVal.value = false
          return
        }
      }
      allSelectVal.value = true
    }
    // 単一
    else {
      selectedRows.value = new Array<boolean>(historyData.value.length).fill(false)
      selectedRows.value[index] = true
    }
  }
}

/**
 * アセスメント履歴一覧データ
 *
 */
const historyEdit = () => {
  let sortedData: OrX0143TableData[] = []
  if (localOneway.orX0143.kikanFlg === '1') {
    sortedData = dynamicSort(localOneway.orX0143.rirekiList, [
      { field: OrX0143Const.CELL_START_YMD, order: OrX0143Const.DEFAULT.DESC },
      { field: OrX0143Const.CELL_END_YMD, order: OrX0143Const.DEFAULT.DESC },
      { field: OrX0143Const.CELL_CREATE_YMD, order: OrX0143Const.DEFAULT.DESC },
      { field: OrX0143Const.CELL_RIREKI_ID, order: OrX0143Const.DEFAULT.DESC },
    ])
    historyData.value = addSummaryAboveGroup(sortedData)
  } else {
    historyData.value = dynamicSort(localOneway.orX0143.rirekiList, [
      { field: OrX0143Const.CELL_CREATE_YMD, order: OrX0143Const.DEFAULT.DESC },
      { field: OrX0143Const.CELL_RIREKI_ID, order: OrX0143Const.DEFAULT.DESC },
    ])
  }
  if (!localOneway.orX0143.rirekiId) {
    selectedRows = ref<boolean[]>(new Array<boolean>(historyData.value.length).fill(false))
    allSelectVal.value = false
    // 履歴一覧明細の1件目を選択状態にする。
    if (
      selectedRows.value.length > 0 &&
      localOneway.orX0143.singleFlg === OrX0143Const.DEFAULT.TANI
    ) {
      selectedRows.value[localOneway.orX0143.kikanFlg === OrX0143Const.KIKAN_FLG_ON ? 1 : 0] = true
      onClickEvent()
    }
  }
}

/**
 * データのグループ化
 *
 * @param data - 元のデータ
 */
function addSummaryAboveGroup(data: OrX0143TableData[]): OrX0143TableData[] {
  // sc1Idでグループ化
  const grouped = groupBy(data, OrX0143Const.CELL_SC1ID)

  // 結果配列の生成
  const result: OrX0143TableData[] = []

  Object.entries(grouped).forEach(([sc1Id, items]) => {
    result.push({
      sc1Id: sc1Id,
      startYmd: '',
      endYmd: '',
      createYmd: '',
      rirekiId: '',
      planPeriod:
        t('label.plan-period') +
        t('label.colon-mark') +
        items[0].startYmd +
        t('label.wavy') +
        items[0].endYmd,
      isSummaryRow: true,
    })

    // 元のデータを追加
    result.push(...items)
  })

  return result
}

/**
 * データを必要に応じてソート
 *
 * @param data - 元のデータ
 *
 * @param configs - ソート情報
 */
function dynamicSort<T>(data: T[], configs: SortConfig<T>[]): T[] {
  return [...data].sort((a, b) => {
    for (const { field, order } of configs) {
      // 比較値を取得（null値の可能性を処理）
      const aValue = a[field] ?? ''
      const bValue = b[field] ?? ''

      // 文字列の場合は直接比較し、数値または日付の場合は特別な処理が必要です
      let compareResult: number
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        compareResult = aValue.localeCompare(bValue)
      } else {
        compareResult = aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      }

      // 現在のフィールドの比較結果が等しくない場合、直ちに返す
      if (compareResult !== 0) {
        return order === OrX0143Const.DEFAULT.ASC ? compareResult : -compareResult
      }
    }
    // すべてのフィールドが等しい
    return 0
  })
}

/**
 * すべて選択
 */
const toggleAllRows = () => {
  if (allSelectVal.value) {
    tableSelectArr.value = []
    allSelectVal.value = false
  } else {
    const filteredRowIds: string[] = localOneway.orX0143.rirekiList.map((item) => item.id)
    tableSelectArr.value = [...filteredRowIds]
    allSelectVal.value = true
  }
}

/**
 * 選択の解除
 */
const clearSelection = () => {
  selectedRows.value = new Array<boolean>(historyData.value.length).fill(false)
  allSelectVal.value = false
  onClickEvent()
}

/**
 *  選択押下時の処理
 */
function onClickEvent() {
  setEvent({
    historyDetClickFlg: true,
    orX0143DetList: historyData.value.filter((data, index) => selectedRows.value[index]),
  })
}

/**
 * 履歴選択 - 値の監視
 */
watch(
  () => localOneway.orX0143.singleFlg,
  (newValue) => {
    // 単一と複数の切り替え時に、選択結果が保持
    if (newValue) {
      if (newValue === OrX0143Const.DEFAULT.HUKUSUU) {
        selectedHist.value.taniSelectedId = tableSelectArr.value[0]
        tableSelectArr.value = selectedHist.value.hukusuuSelectedId
      } else {
        selectedHist.value.hukusuuSelectedId = tableSelectArr.value
        tableSelectArr.value = [
          selectedHist.value.taniSelectedId === '' && localOneway.orX0143.rirekiList.length > 0
            ? '1'
            : selectedHist.value.taniSelectedId,
        ]
      }
    }
  }
)

// /**
//  * 履歴ID値の監視
//  */
// watch(
//   () => localOneway.orX0143.rirekiId,
//   (newValue) => {
//     if (newValue) {
//       const index = historyData.value.findIndex((x) => x.rirekiId === localOneway.orX0143.rirekiId)
//       if (index > -1) {
//         selectedRows.value = new Array<boolean>(historyData.value.length).fill(false)
//         selectedRows.value[index] = !selectedRows.value[index]
//         allSelectVal.value = false
//         onClickEvent()
//       }
//     }
//   }
// )

/**
 * 行選択監視
 */
watch(
  () => tableSelectArr.value,
  () => {
    if (localOneway.orX0143.singleFlg === OrX0143Const.DEFAULT.HUKUSUU) {
      allSelectVal.value =
        tableSelectArr.value.length === localOneway.orX0143.rirekiList.length &&
        tableSelectArr.value.length > 0
    }
    const selectedRows: OrX0143TableData[] = localOneway.orX0143.rirekiList.filter((item) =>
      tableSelectArr.value.includes(item.id)
    )
    setEvent({
      orX0143DetList: selectedRows,
      historyDetClickFlg: tableSelectArr.value.length !== 0,
    })
  }
)

/**
 * 利用者選択
 */
watch(
  () => props.onewayModelValue.rirekiList,
  (newValue) => {
    selectedHist.value = {
      taniSelectedId: '',
      hukusuuSelectedId: [],
    } as OrX0143Selected
    if (localOneway.orX0143.singleFlg === OrX0143Const.DEFAULT.HUKUSUU) {
      tableSelectArr.value = []
    } else {
      if (newValue && newValue.length > 0) {
        if (localOneway.orX0143.rirekiId && Number(localOneway.orX0143.rirekiId) > 0) {
          // 親画面を対するレコードを選択状態
          tableSelectArr.value = [localOneway.orX0143.rirekiId]
          localOneway.orX0143.rirekiId = ''
        } else {
          tableSelectArr.value = ['1']
        }
      } else {
        tableSelectArr.value = []
      }
    }
  }
)

/**
 * アセスメント履歴一覧データ
 */
// watch(
//   () => props.onewayModelValue,
//   (newValue) => {
//     localOneway.orX0143 = {
//       ...defaultOnewayModelValue,
//       ...newValue,
//       rirekiList: {
//         ...props.onewayModelValue.rirekiList.map((item, index) => ({
//           ...item,
//           id: index.toString(),
//         })),
//       },
//     }
//   },
//   { deep: true }
// )
</script>

<template>
  <c-v-row>
    <c-v-col class="d-flex">
      <!-- 初期表示(期間管理フラグが「管理する」の場合) -->
      <div v-if="OrX0143Const.DEFAULT.TANI === localOneway.orX0143.singleFlg">
        <c-v-form ref="tableForm">
          <c-v-data-table
            v-model="tableSelectArr"
            :headers="headers"
            class="table-wrapper"
            fixed-header
            :items="historyData"
            height="483px"
            hide-default-footer
            :style="localOneway.orX0143.tableStyle"
            :items-per-page="-1"
            :show-select="localOneway.orX0143.singleFlg === OrX0143Const.DEFAULT.HUKUSUU"
            item-selectable="selectable"
            item-value="id"
          >
            <!-- ヘッダ -->
            <template #headers>
              <tr>
                <th
                  v-for="header in headers"
                  :key="header.key"
                  :style="header.style"
                >
                  {{ header.title }}
                </th>
              </tr>
            </template>
            <!-- 一覧 -->
            <template #item="{ item, index }">
              <tr
                :class="{
                  'header-row': item.planPeriod && item.planPeriod !== '',
                  'selected-row': selectedRows[index] && !item.planPeriod,
                }"
                @click="handleRowClick(item)"
              >
                <td
                  v-if="item.planPeriod && item.planPeriod !== ''"
                  :colspan="headers.length"
                >
                  {{ item.planPeriod }}
                </td>
                <!-- 初期表示(期間管理フラグが「管理しない」の場合) -->
                <template v-else>
                  <td
                    v-for="header in headers"
                    :key="header.key"
                  >
                    {{ item[header.key] }}
                  </td>
                </template>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-form>
      </div>
      <!-- 履歴選択方法が「複数」の場合 -->
      <div v-if="OrX0143Const.DEFAULT.HUKUSUU === localOneway.orX0143.singleFlg">
        <c-v-data-table
          :headers="multiHeaders"
          class="table-wrapper"
          hide-default-footer
          :items="historyData"
          height="483px"
          fixed-header
          hover
          :style="localOneway.orX0143.tableStyle"
          :items-per-page="-1"
        >
          <!-- ヘッダ -->
          <template #headers>
            <tr>
              <th class="table-checkbox width-60">
                <base-at-checkbox
                  v-model="allSelectVal"
                  :indeterminate="
                    historyData.length > 0 && selectedRows.includes(true) && !allSelectVal
                  "
                  :checkbox-label="checkBoxLabel"
                  :disabled="historyData.length === 0"
                  @change="toggleAllRows"
                ></base-at-checkbox>
              </th>
              <th
                v-for="header in headers"
                :key="header.key"
                :style="header.style"
              >
                {{ header.title }}
              </th>
            </tr>
          </template>
          <template #item="{ item, index }">
            <tr
              :class="{
                'header-row': item.planPeriod && item.planPeriod !== '',
                'selected-row': selectedRows[index] && !item.planPeriod,
              }"
              @click="handleRowClick(item)"
            >
              <td
                v-if="!item.planPeriod"
                class="table-checkbox"
              >
                <base-at-checkbox
                  v-model="selectedRows[index]"
                  :checkbox-label="checkBoxLabel"
                  @change="checkedChange(index)"
                ></base-at-checkbox>
              </td>
              <td
                v-if="item.planPeriod && item.planPeriod !== ''"
                :colspan="multiHeaders.length"
              >
                {{ item.planPeriod }}
              </td>
              <!-- 履歴選択方法が「複数」且つ 期間管理フラグが「管理しない」の場合 -->
              <template v-else>
                <td
                  v-for="header in headers"
                  :key="header.key"
                >
                  {{ item[header.key] }}
                </td>
              </template>
            </tr>
          </template>
        </c-v-data-table>
      </div>
    </c-v-col>
  </c-v-row>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
.table-wrapper .v-table__wrapper td {
  padding: 0;
}
:deep(.table-header td) {
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-color: lightgrey;
  max-height: 40px;
  font-size: 14px;
}
:deep(.table-wrapper .v-table__wrapper table) {
  table-layout: fixed;
}
:deep(.v-table--fixed-header) {
  position: sticky;
  // ジッタ除去
  top: 0px;
  z-index: 2;
}

.header-row {
  cursor: default;
  background-color: rgb(var(--v-theme-green-200));
}
// 選択した行のCSS
.selected-row {
  background-color: rgb(var(--v-theme-blue-100));
}
:deep(.table-checkbox > div) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
}
// 行の高さ: 32px
:deep(.table-wrapper table tbody tr td),
:deep(.table-wrapper .row-height) {
  height: 32px !important;
}
// 最小幅: 60px
.width-60 {
  min-width: 60px;
  width: 60px;
}
</style>
