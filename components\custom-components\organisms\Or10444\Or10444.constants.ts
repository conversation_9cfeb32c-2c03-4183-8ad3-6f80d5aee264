import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or10444:処理ロジック
 * GUI01131_［印刷設定］画面
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> DAO VAN DUONG
 */
export namespace Or10444Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or10444', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * ZERO
     */
    export const ZERO = '0'
    /**
     * ONE
     */
    export const ONE = '1'
    /**
     * TWO
     */
    export const TWO = '2'
    /**
     * 単一
     */
    export const TANI = '0'
    /**
     * 複数
     */
    export const HUKUSUU = '1'
    /**
     * 空文字
     */
    export const EMPTY  = ''
    /**
     * 区切り文字-コロン
     */
    export const SPLIT_COLON = '：'
    /**
     * 区切り文字- チルダ
     */
    export const SPLIT_TILDE = ' ～ '
  }

  /**
   * 引続情報.起動パターン=1:複写の場合
   */
  export const STARTUP_PATTERN_IS_COPY = 1
  /**
   * 文字列：全
   */
  export const STR_ALL = '全'
  /**
   * サービス担当者会議の要点
   */
  export const SERVICE_MANAGER_MEETING_MAIN_POINT = 'serviceManagerMeetingMainPoint'
  /**
   * サービス担当者会議の要点
   */
  export const SERVICE_MANAGER_MEETING_MAIN_POINT_U00911 = 'serviceManagerMeetingMainPointU00911'
  /**
   * サービス担当者会議の要点
   */
  export const SERVICE_MANAGER_MEETING_MAIN_POINT_U00912 = 'serviceManagerMeetingMainPointU00912'
}
