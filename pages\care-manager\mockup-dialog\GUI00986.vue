<script setup lang="ts">
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or29976Const } from '~/components/custom-components/organisms/Or29976/Or29976.constants'
import { Or29976Logic } from '~/components/custom-components/organisms/Or29976/Or29976.logic'
import type { Or29976OnewayType } from '~/types/cmn/business/components/Or29976Type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 * GUI00986_［印刷設定］画面
 * KMD 張凱旋 2025/06/04 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI00986'
// ルーティング
const routing = 'GUI00986/pinia'
// 画面物理名
const screenName = 'GUI00986'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const localOneway = reactive({
  or29976Oneway: {
    userList: [
      {
        userId: '1',
        nameKnj: '相川まつ',
        userNumber: '1',
        sex: '1',
      },
      {
        userId: '2',
        nameKnj: '相川 雅美',
        userNumber: '33',
        sex: '1',
      },
      {
        userId: '3',
        nameKnj: '安達 三津子',
        userNumber: '23',
        sex: '1',
      },
      {
        userId: '4',
        nameKnj: '阿藤 かよ',
        userNumber: '65',
        sex: '1',
      },
      {
        userId: '5',
        nameKnj: '安部 花子',
        userNumber: '25',
        sex: '1',
      },
    ],
    kikanFlg: '1',
    svJigyoId: '10',
    houjinId: '1',
    shisetuId: '9',
    /** 事業所名 */
    svJigyoName: '事業所名',
    userId: '1',
    sectionName: '（Ⅳ）週間表(H21改訂版)',
    choIndex: '1',
    week1Id: '1',
    processDate: '2025/05/05',
    shokuId: '1',
    careManagerInChargeSettingsFlag: -1,
    focusSettingInitial: ['あ'],
  } as Or29976OnewayType,
})
/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00986' },
})

/**************************************************
 * Props
 **************************************************/
const or29976 = ref({ uniqueCpId: '' })
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 自身のPinia領域をセットアップ
const { childCpIds } = useInitialize({
  cpId: 'GUI00986',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or29976Const.CP_ID(0) }],
})
Or29976Logic.initialize(childCpIds.Or29976.uniqueCpId)
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or29976Const.CP_ID(0)]: or29976.value,
})

/**************************************************
 * Props
 **************************************************/

// ダイアログ表示フラグ
const showDialogOr29976 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or29976Logic.state.get(or29976.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 初期データ取得
 */
async function initData() {}

/***
 * ボタン押下時の処理
 */
function or29976OnClick() {
  Or29976Logic.state.set({
    uniqueCpId: or29976.value.uniqueCpId,
    state: { isOpen: true },
  })
}

await initData()
/**************************************************
 * ［印刷設定］画面
 * KMD 張凱旋 2025/06/04 ADD START
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or29976OnClick()"
        >GUI00986_［印刷設定］画面
      </v-btn>
      <g-custom-or-29976
        v-if="showDialogOr29976"
        v-bind="or29976"
        :oneway-model-value="localOneway.or29976Oneway"
        :unique-cp-id="or29976.uniqueCpId"
        :parent-cp-id="pageComponent.uniqueCpId"
      />
    </c-v-col>
  </c-v-row>
</template>
