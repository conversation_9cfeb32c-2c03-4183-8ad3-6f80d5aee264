/**
 * Or60075の［退院・退所情報記録書］画面初期情報mock
 * GUI01305_退院・退所情報記録書画面
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { DischargeFromHospitalLeavingInfoInitInfoSelectInEntity } from '~/repositories/cmn/entities/DischargeFromHospitalLeavingInfoSelectServiceEntity'

/**
 * GUI01305_退院・退所情報記録書画面の取得APIモック
 *
 * @description
 * GUI01305_退院・退所情報記録書のメイン画面に表示されるデータを返却する。
 * dataName："AdditionInfo"
 */
export function handler(inEntity: DischargeFromHospitalLeavingInfoInitInfoSelectInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
