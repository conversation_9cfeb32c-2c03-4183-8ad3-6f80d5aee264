<script setup lang="ts">
/**
 * Or27514:有機体:パターン（グループ）タイトル
 * GUI00995_パターン（グループ）タイトル
 *
 * @description
 * パターン（グループ）：タイトル
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { VForm } from 'vuetify/components'
import { OrX0053Const } from './OrX0053.constants'
import type { TableData } from './OrX0053.type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type {
  OrX0053OnewayType,
  OrX0053Type,
  Title,
} from '~/types/cmn/business/components/OrX0053Type'

import { UPDATE_KBN } from '~/constants/classification-constants'
import type {
  PatternGroupInfoSelectInEntity,
  PatternGroupInfoSelectOutEntity,
} from '~/repositories/cmn/entities/PatternGroupInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  Or21814EventType,
  Or21814FirstBtnType,
  Or21814OnewayType,
  Or21814SecondBtnType,
  Or21814ThirdBtnType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { useScreenUtils } from '~/utils/useScreenUtils'
import { useScreenTwoWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import { useValidation } from '~/utils/useValidation'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useScreenStore } from '~/stores/session/screen'
import type { PatternGroupInfoUpdateInEntity } from '~/repositories/cmn/entities/PatternGroupInfoUpdateEntity'
import type { Mo01274OnewayType } from '~/types/business/components/Mo01274Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
const { setChildCpBinds } = useScreenUtils()

const { t } = useI18n()

const validation = useValidation()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: OrX0053OnewayType
  modelValue: OrX0053Type
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()

// ナビゲーション制御領域のいずれかの編集フラグがON
useSystemCommonsStore().setShowEditDiscardDialog(useScreenStore().isEditNavControl())
const isEdit = computed(() => {
  return useSystemCommonsStore().getShowEditDiscardDialog
})

const defaultOnewayModelValue: OrX0053OnewayType = {
  mstKbn: '2',
}

const defaultModelValue: OrX0053Type = {
  editFlg: false,
  titleList: [],
  initFlg: false,
}

// 画面.画面タイトル
let titleId: unknown
switch (props.onewayModelValue.mstKbn) {
  case OrX0053Const.DEFAULT.MSTKBN_LIST.DAILY_PARTTEN:
    titleId = t('label.daily-table-pattern')
    break

  case OrX0053Const.DEFAULT.MSTKBN_LIST.WEEK_PARTTEN:
    titleId = t('label.week-table-pattern')
    break
  case OrX0053Const.DEFAULT.MSTKBN_LIST.MONTHYEAR_PARTTEN:
    titleId = t('label.monthly-yearly-table-pattern')
    break
  default:
    break
}

const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })

const showErrDialog = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showInfoDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

const localOneWay = reactive({
  orX0053: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 新規
  mo00611OneWay: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
  } as Mo00611OnewayType,
  // 行削除
  mo01265OneWay: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    disabled: true,
  } as Mo01265OnewayType,
  // 表示順: 表用テキストフィールド
  mo1274SeqInputOneWay: {
    length: '3',
    rules: [validation.integer, validation.required, validation.minValue(1)],
  } as Mo01274OnewayType,
  // タイトル: 表用テキストフィールド
  mo1274GroupInputOneWay: {
    length: '40',
    rules: [validation.required],
  } as Mo01274OnewayType,
})

const local = reactive({
  orX0053: {
    ...defaultModelValue,
    ...props.modelValue,
  },
})
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<TableData[]>({
  cpId: OrX0053Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = []
/**************************************************
 * 変数定義
 **************************************************/
// 選択した行のindex
const selectedItemIndex = ref<number>(-1)
// テーブルヘッダ
const headers = [
  { title: t('label.cd'), key: 'group', width: '100px', sortable: false },
  { title: t('label.group'), key: 'groupKnj', width: '100px', sortable: false, required: true },
  { title: t('label.display-order'), key: 'seq', width: '50px', sortable: false },
]

// "*"
const required: string = OrX0053Const.DEFAULT.REQUIRED

// 元のテーブルデータ
const orgTableData = ref<string>('')
const tableForm = ref<VForm>()

const tableDataFilter = computed(() => {
  const titleList = refValue.value
  return titleList!.filter((i: { updateKbn: string }) => i.updateKbn !== UPDATE_KBN.DELETE)
})
const refs = ref<Record<string, HTMLInputElement>>({})
const setRef = (el: HTMLInputElement | null, tableIndex: string) => {
  if (el) {
    refs.value[tableIndex] = el
  }
}
// ポスト最小幅
const columnMinWidth = ref<number[]>([143, 563, 563])
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**************************************************
 * コンポーネント固有処理
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
})
onMounted(async () => {
  await init()
})

/**
 * パターン（グループ）情報取得
 */
async function init() {
  // パターン（グループ）情報取得IN)
  const param: PatternGroupInfoSelectInEntity = {
    // マスタ区分: 引継情報.マスタ区分
    mstKbn: localOneWay.orX0053.mstKbn,
  }
  // パターン（グループ）情報取得
  const ret: PatternGroupInfoSelectOutEntity = await ScreenRepository.select(
    'patternGroupInfoSelect',
    param
  )
  // 戻り値はテーブルデータとして処理されます
  let tmpArr = []
  tmpArr = []
  for (const item of ret.data.parttenGroupInfoList) {
    tmpArr.push({
      groupCd: item.groupCd,
      groupKnj: { value: item.groupKnj },
      seq: { value: item.seq },
      tableIndex: tmpArr.length,
      updateKbn: UPDATE_KBN.NONE,
    })
  }
  // 元のテーブルデータの設定
  orgTableData.value = JSON.stringify(refValue.value)
  setChildCpBinds(props.parentUniqueCpId, {
    OrX0053: {
      twoWayValue: tmpArr,
    },
  })
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
  // 行削除活性
  localOneWay.mo01265OneWay.disabled = false
}

/**
 * 「新規」押下
 */
async function createRow() {
  let lastSeq = 0
  if (refValue.value!.length > 0) {
    for (const data of refValue.value!) {
      if (data.updateKbn !== UPDATE_KBN.DELETE) {
        lastSeq = Math.max(lastSeq, Number(data.seq.value))
      }
    }
  }
  // 表示順最大値＜999の場合
  if (lastSeq < 999) {
    // 表示順最大値＋1を設定
    lastSeq += 1
  } else {
    // 上記以外の場合、999を設定
    lastSeq = 999
  }
  // パターンのグループ一覧の最終に新しい行を追加する。
  const data = {
    groupCd: '',
    // タイトル：空白
    groupKnj: { value: '' },
    // 表示順
    seq: { value: String(lastSeq) },
    // テーブルINDEX(行固有ID)
    tableIndex: refValue.value!.length,
    // 更新区分
    updateKbn: UPDATE_KBN.CREATE,
    // 更新回数
    modifiedCnt: '',
  }
  refValue.value!.push(data)
  await nextTick()
  const lastInput = refs.value[`input-${data.tableIndex}`]
  if (lastInput) {
    lastInput.focus()
  }
}

/**
 * 行削除ボタン押下
 */
async function deleteRowMsg() {
  if (selectedItemIndex.value !== -1) {
    // 以下のメッセージを表示
    const rs = showOr21814Msg(t('message.i-cmn-10878'))
    if ((await rs).secondBtnClickFlg) {
      deleteRow()
    }
  }
}

/**
 * 行削除
 */
function deleteRow() {
  if (selectedItemIndex.value !== null) {
    refValue.value!.forEach((item: { tableIndex: number; updateKbn: string }) => {
      if (item.tableIndex === selectedItemIndex.value) {
        item.updateKbn = UPDATE_KBN.DELETE
        selectedItemIndex.value = -1
        // 行削除非活性
        localOneWay.mo01265OneWay.disabled = true
      }
    })
  }
}

/**
 * 行削除の開閉
 *
 * @param infomsg - Message
 */
async function showOr21814Msg(infomsg: string) {
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: infomsg,
    firstBtnType: 'normal3',
    firstBtnLabel: t('btn.no'),
    secondBtnType: 'normal1',
    secondBtnLabel: t('btn.yes'),
    thirdBtnType: 'blank',
  })
  return rs
}

// ダイアログResolve
let resolvePromise: (value: Or21814EventType) => void

/**
 * 確定メッセージを開きます
 *
 * @param uniqueCpId -uniqueCpId
 *
 * @param state -Or21814OnewayType
 */
function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    resolvePromise = resolve
  })
}

/**
 * コンテンツの更新
 */
function onUpdate() {
  refValue.value!.forEach((item: { tableIndex: number; updateKbn: string }) => {
    if (item.tableIndex === selectedItemIndex.value && item.updateKbn !== UPDATE_KBN.CREATE) {
      item.updateKbn = UPDATE_KBN.UPDATE
      selectedItemIndex.value = -1
      // 行削除非活性
      localOneWay.mo01265OneWay.disabled = true
    }
  })
}

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    await nextTick()
    if (resolvePromise !== undefined && newValue !== undefined) {
      resolvePromise(newValue)
    }
    return
  }
)

watch(
  () => refValue.value,
  () => {
    local.orX0053.editFlg = orgTableData.value !== JSON.stringify(refValue.value)
    // タイトルのリスト
    const titleList: Title[] = []
    for (const title of refValue.value!) {
      titleList.push({
        ...title,
        groupKnj: title.groupKnj.value,
        seq: title.seq.value,
      })
    }
    local.orX0053.titleList = titleList
    emit('update:modelValue', local.orX0053)
  },
  { deep: true }
)

/**
 * 表データの設定
 */
watch(
  () => local.orX0053.saveResultTitleList,
  () => {
    if (Array.isArray(local.orX0053.saveResultTitleList)) {
      const titleList = []
      for (const item of local.orX0053.saveResultTitleList) {
        titleList.push({
          // CD
          groupCd: item.groupCd,
          // グループ名
          groupKnj: { value: item.groupKnj },
          // 表示順
          seq: { value: item.seq },
          // テーブルINDEX(行固有ID)
          tableIndex: titleList.length,
          // 更新区分
          updateKbn: UPDATE_KBN.NONE,
          // 更新回数
          modifiedCnt: item.modifiedCnt,
        })
      }
      refValue.value = titleList
      // 元のテーブルデータの設定
      orgTableData.value = JSON.stringify(refValue.value)
    }
  }
)
/**
 * 初期表示フラグを傍受する
 */
watch(
  () => local.orX0053.initFlg,
  async (newVal) => {
    if (newVal) {
      await init()
      local.orX0053.initFlg = false
      emit('update:modelValue', local.orX0053)
    }
  }
)

/**
 * 「閉じるボタン」押下
 */
async function close() {
  if (isEdit.value) {
    // 画面.表示順が数値以外の場合
    // 変更ありのデータ行をINPUT情報として、下記の保存APIを実行する
    const dataArray = local.orX0053.titleList.filter(
      (data) => data.updateKbn !== UPDATE_KBN.DELETE && Number.isNaN(Number(data.seq))
    )
    let isSeqNotNum = false
    dataArray.forEach((element) => {
      if (Number.isNaN(Number(element.seq))) {
        isSeqNotNum = true
      }
    })
    // 画面.表示順が数値以外の場合
    if (isSeqNotNum) {
      const dialogResult = await openInfoDialog(
        t('message.i-cmn-11167', [titleId]),
        'normal1',
        'destroy1',
        'blank'
      )
      switch (dialogResult) {
        // はい：AC008-1-2～AC008-1-3、AC008-2～AC008-3を実行して、処理続き
        case OrX0053Const.DEFAULT.DIALOG_RESULT_YES:
          await save()
          break
        case OrX0053Const.DEFAULT.DIALOG_RESULT_NO:
          break
        case OrX0053Const.DEFAULT.DIALOG_RESULT_CANCEL:
          return {
            error: false,
            info: false,
            msg: '',
            isBreak: true,
          }
      }
    } else {
      const dialogResult = await openInfoDialog(
        t('message.i-cmn-10430'),
        'normal1',
        'destroy1',
        'normal1'
      )
      switch (dialogResult) {
        // はい：AC008-1-2～AC008-1-3、AC008-2～AC008-3を実行して、処理続き
        case OrX0053Const.DEFAULT.DIALOG_RESULT_YES:
          await save()
          break
        case OrX0053Const.DEFAULT.DIALOG_RESULT_NO:
          break
        case OrX0053Const.DEFAULT.DIALOG_RESULT_CANCEL:
          return {
            error: false,
            info: false,
            msg: '',
            isBreak: true,
          }
      }
    }
  }
  return {
    error: false,
    info: false,
    msg: '',
    isBreak: false,
  }
}

/**
 * 保存
 */
async function save() {
  await nextTick()
  // 画面入力データ変更があるかどうかを判定する
  if (isEdit.value) {
    // 変更がある場合、処理継続
    const dataChkArray = local.orX0053.titleList.filter(
      (data) => data.updateKbn !== UPDATE_KBN.DELETE
    )
    let isGroupNmEmpty = false
    let isSeqNotNum = false
    let isSeqRightNum = false
    dataChkArray.forEach((element) => {
      if (element.groupKnj === null || element.groupKnj === undefined || element.groupKnj === '') {
        isGroupNmEmpty = true
      }
      if (Number.isNaN(Number(element.seq))) {
        isSeqNotNum = true
      }
      if (!Number.isNaN(Number(element.seq)) && Number(element.seq) <= 0) {
        isSeqRightNum = true
      }
    })
    // 画面.表示順が数値以外の場合
    if (isSeqNotNum) {
      return
    }
    // 画面.グループ名が空白の場合
    if (isGroupNmEmpty) {
      await openErrorDialog(t('message.e-cmn-41708'))
      return
    }
    // 画面.表示順が空白、又は、「0」以下の場合
    if (isSeqRightNum) {
      await openErrorDialog(t('message.e-cmn-40792'))
      return
    }
    // 変更ありのデータ行をINPUT情報として、下記の保存APIを実行する
    const dataArray = local.orX0053.titleList.filter(
      (data) => (data.groupCd === '' && data.updateKbn !== UPDATE_KBN.DELETE) || data.groupCd !== ''
    )
    const tmpArr: PatternGroupInfoUpdateInEntity['groupList'] = []
    for (const item of dataArray) {
      tmpArr.push({
        groupCd: item.groupCd,
        mstKbn: localOneWay.orX0053.mstKbn,
        groupKnj: item.groupKnj,
        seq: item.seq,
        updateKbn: item.updateKbn,
        modifiedCnt: item.modifiedCnt,
      })
    }
    const param: PatternGroupInfoUpdateInEntity = {
      groupList: tmpArr,
    }
    // パターン（グループ）タイトル情報保存
    await ScreenRepository.insert('patternGroupInfoUpdate', param)
    // パターン（グループ）情報取得
    await init()
  }
}

/**
 * errorダイアログを閉じたタイミングで結果を返却
 *
 * @param msg - msg
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openErrorDialog(msg: string): Promise<string> {
  // 選択行削除確認ダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: 'blank',
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'blank',
    },
  })

  /**
   * 選択行削除確認ダイアログを閉じたタイミングで結果を返却
   *
   * @returns ダイアログの選択結果（yes, no）
   */
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = OrX0053Const.DEFAULT.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = OrX0053Const.DEFAULT.DIALOG_RESULT_YES
        }

        // 選択行削除確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 編集破棄ダイアログ表示
 *
 * @param msg - msg
 *
 * @param firstBtn - firstBtn
 *
 * @param secondBtn - secondBtn
 *
 * @param thirdBtn - thirdBtn
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openInfoDialog(
  msg: string,
  firstBtn: Or21814FirstBtnType,
  secondBtn: Or21814SecondBtnType,
  thirdBtn: Or21814ThirdBtnType
): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: firstBtn,
      firstBtnLabel: t('btn.yes'),
      secondBtnType: secondBtn,
      secondBtnLabel: t('btn.no'),
      thirdBtnType: thirdBtn,
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = OrX0053Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = OrX0053Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = OrX0053Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = OrX0053Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

defineExpose({
  save,
  close,
})
</script>

<template>
  <c-v-row
    no-gutters
    class="mt-0"
  >
    <c-v-col>
      <!-- 新規ボタン -->
      <base-mo00611
        v-bind="localOneWay.mo00611OneWay"
        @click="createRow"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.add-row')"
        />
      </base-mo00611>
      <!-- 削除ボタン -->
      <base-mo01265
        v-bind="localOneWay.mo01265OneWay"
        class="mx-2"
        @click="deleteRowMsg"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="t('tooltip.display-order-delete-success')"
        />
      </base-mo01265>
    </c-v-col>
  </c-v-row>
  <c-v-form ref="tableForm">
    <!-- パターン一覧 -->
    <c-v-row no-gutters>
      <c-v-data-table
        v-resizable-grid="{ columnWidths: columnMinWidth }"
        fixed-header
        :headers="headers"
        :items="tableDataFilter"
        class="table-header mt-2 ml-2 mr-2 mb-2"
        hover
        height="487px"
        hide-default-footer
        :items-per-page="-1"
      >
        <!-- ヘッダ Part -->
        <template #headers>
          <tr>
            <!-- CD -->
            <th class="width-100">{{ t('label.cd') }}</th>
            <!-- *グループ名 -->
            <th class="width-300">
              <span style="color: red">{{ required }}</span
              >{{ t('label.group-knj') }}
            </th>
            <!-- *表示順 -->
            <th class="width-100">
              <span style="color: red">{{ required }}</span
              >{{ t('label.display-order') }}
            </th>
          </tr>
        </template>
        <!-- 一覧 -->
        <template #item="{ item, index }">
          <tr
            :class="{ 'select-row': selectedItemIndex === item.tableIndex }"
            @click="selectRow(item.tableIndex)"
          >
            <!-- ID: 右寄せ -->
            <td>{{ item.groupCd }}</td>
            <!-- グループ名: テキストフィールド -->
            <td class="input-padding-none">
              <base-mo01274
                v-model="tableDataFilter[index].groupKnj"
                :re-ref="(el: HTMLInputElement | null) => setRef(el, `input-${item.tableIndex}`)"
                :oneway-model-value="localOneWay.mo1274GroupInputOneWay"
                @change="onUpdate"
              ></base-mo01274>
            </td>
            <!-- 表示順: テキストフィールド -->
            <td class="input-padding-none">
              <base-mo01274
                v-model="tableDataFilter[index].seq"
                :oneway-model-value="localOneWay.mo1274SeqInputOneWay"
                :reverse="true"
                @change="onUpdate"
              ></base-mo01274>
            </td>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-row>
  </c-v-form>
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showErrDialog"
    v-bind="or21813"
  />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showInfoDialog"
    v-bind="or21814"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
.text-align-right {
  text-align: right;
}
.text-align-left {
  text-align: left;
}
.input-padding-none {
  padding-left: 0px !important;
  padding-right: 0px !important;
}
:deep(.v-data-table) {
  width: 100%;
  .v-table__wrapper {
    table {
      border-collapse: collapse;
      border: 1px solid rgb(var(--v-theme-black-300));
    }
  }
  td,
  th {
    border: 1px solid rgb(var(--v-theme-black-300)) !important;
  }
  .v-data-table__tr:hover td {
    border-color: rgb(var(--v-theme-black-300)) !important;
  }
}
// 選択した行のCSS
.select-row {
  background: #dbeefe;
}
</style>
