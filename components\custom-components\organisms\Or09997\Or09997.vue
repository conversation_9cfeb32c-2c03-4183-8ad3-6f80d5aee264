<script setup lang="ts">
/**
 * Or09997:有機体:モーダル（画面/特殊コンポーネント）
 * GUI00825_［表示順変更アセスメント］画面
 *
 * <AUTHOR>
 */
import { onMounted, reactive, watch, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import type { SortableEvent } from 'vue-draggable-plus'
import { Or09997Const } from './Or09997.constants'
import type { Or09997StateType } from './Or09997.type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Or09997Type, Or09997OnewayType } from '~/types/cmn/business/components/Or09997Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo01278OnewayType, Mo01278Type } from '~/types/business/components/Mo01278Type'
import type {
  Mo01354Headers,
  Mo01354Items,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or09997Type
  onewayModelValue: Or09997OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
/** ディフォルトOneway */
const defaultOneay = reactive({
  or09997: {
    /** カスタマイズ様式Oneway */
    mo01354Oneway: {
      height: 353,
      headers: [
        {
          title: t('label.display-order'),
          key: 'displayOrder',
          minWidth: '78',
          sortable: false,
        },
        {
          title: t('label.issues'),
          key: 'issues',
          sortable: false,
        },
        {
          title: t('label.goal'),
          key: 'goal',
          sortable: false,
        },
      ] as Mo01354Headers[],
      showPaginationBottomFlg: false,
      showPaginationTopFlg: false,
      density: 'default',
    } as Mo01354OnewayType,
  },
})

const localOneway = reactive({
  or09997: {
    ...props.onewayModelValue,
  },
  // 情報ダイアログ
  mo00024Oneway: {
    width: '1074px',
    height: '563px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or09997',
      toolbarTitle: t('label.display-order-modified-assessment'),
      toolbarName: 'Or09997ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      cardTextClass: 'pa-2 pt-0',
    },
  } as Mo00024OnewayType,
  mo01265OnewayModelValue: {
    btnLabel: t('btn.delete-display-order'),
    width: '140px',
    prependIcon: 'delete',
  } as Mo01265OnewayType,
  mo00043OneWay: {
    tabItems: [
      {
        id: '0',
        title: t('label.display-order-modified'),
        tooltipText: t('label.display-order-modified'),
        tooltipLocation: 'bottom',
      },
      {
        id: '1',
        title: t('label.display-order-move'),
        tooltipText: t('label.display-order-move'),
        tooltipLocation: 'bottom',
      },
    ],
  } as Mo00043OnewayType,
  /** 表示順変更 */
  mo01354OnewayChange: {
    ...defaultOneay.or09997.mo01354Oneway,
  } as Mo01354OnewayType,
  /** 表示順移動 */
  mo01354OnewayMove: {
    ...defaultOneay.or09997.mo01354Oneway,
    showDragIndicatorFlg: true,
  } as Mo01354OnewayType,
  mo01278Oneway: {
    max: 99999,
    min: 1,
    maxLength: '5',
  } as Mo01278OnewayType,
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/

// 確認ダイアログ
const or21814 = ref({ uniqueCpId: '' })

const mo00024 = ref<Mo00024Type>({
  isOpen: Or09997Const.DEFAULT.IS_OPEN,
})

const n1AMinWidth = ref('0px')

const local = reactive({
  mo00043: { id: '0' } as Mo00043Type,
  mo01354: {
    values: {
      selectedRowId: '',
      selectedRowIds: [],
      items: [],
    },
  } as Mo01354Type,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or09997StateType>({
  cpId: Or09997Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or09997Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  // 確認ダイアログ
  [Or21814Const.CP_ID(1)]: or21814.value,
})

onMounted(() => {
  init()
})

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * タブ切替を監視
 */
watch(
  () => local.mo00043.id,
  (newValue) => {
    // 「表示順変更」タブの場合、
    if (newValue === '0') {
      if (local.mo01354.values.items.length > 5) {
        n1AMinWidth.value = '445.5px'
      } else {
        n1AMinWidth.value = '453px'
      }
    } else {
      // 「表示順移動」タブの場合、
      if (local.mo01354.values.items.length > 5) {
        n1AMinWidth.value = '422.5px'
      } else {
        n1AMinWidth.value = '430px'
      }
    }
  }
)

/**
 * AC001_初期情報取得
 */
function init() {
  // 「表示順変更」タブ初期表示
  n1AMinWidth.value = '453px'
  // 取得した課題一覧データを課題一覧に設定する。
  if (props.onewayModelValue.sortList?.length > 0) {
    local.mo01354.values.items.splice(0)
    const tempArray = props.onewayModelValue.sortList
    for (const data of tempArray) {
      // 表示順を設定
      const item = {
        ...data,
        displayOrder: { value: data.seqNo },
        issues: data.issues,
        goal: data.goal,
      } as Mo01354Items
      local.mo01354.values.items.push(item)
    }
    local.mo00043.id = '0'
    if (local.mo01354.values.items.length > 5) {
      n1AMinWidth.value = '445.5px'
    }
    // 一行目を選択状態にする。
    local.mo01354.values.selectedRowId = local.mo01354.values.items[0].id
  }
}

/**
 * 表示順設定 タブ変更
 *
 * @param tableData - テーブルデータ
 */
function setDisplayOrder(tableData: Mo01354Items) {
  // 全空白チェック
  const allBlankFlg = local.mo01354.values.items.every(
    (item) => (item.displayOrder as Mo01278Type).value === ''
  )
  if (allBlankFlg) {
    ;(tableData.displayOrder as Mo01278Type).value = '1'
  } else {
    // 最大値を取得する
    let maxSeq = Math.max(
      ...local.mo01354.values.items
        .map((item) => parseInt((item.displayOrder as Mo01278Type).value))
        .filter((displayOrder) => !isNaN(displayOrder))
    )
    // 最大値チェック
    if (maxSeq < 0) {
      maxSeq = 0
    }
    if ((tableData.displayOrder as Mo01278Type).value === '') {
      // 最大値に1を足して新しいseq.valueを設定
      ;(tableData.displayOrder as Mo01278Type).value = (maxSeq + 1).toString()
    }
  }
}

/**
 * AC005_「表示順位削除」押下
 */
function sortDeleteClick() {
  local.mo01354.values.items.forEach((item) => {
    ;(item.displayOrder as Mo01278Type).value = ''
  })
}

/**
 * ソート処理
 */
function sortTableDataList() {
  const items = local.mo01354.values.items as Mo01354Items[]
  // 配列を絞り出す、負数データ、または''を取得する
  const minus = items.filter(
    (item) =>
      parseInt((item.displayOrder as Mo01278Type).value) < 0 ||
      (item.displayOrder as Mo01278Type).value === ''
  )
  // 負数または''データ抜くのデータを取得し、ソートする
  const sortedList = items
    .filter((item) => !minus.includes(item))
    .sort((a, b) => {
      return (
        parseInt((a.displayOrder as Mo01278Type).value) -
        parseInt((b.displayOrder as Mo01278Type).value)
      )
    })
  minus.forEach((item) => {
    sortedList.push(item)
  })
  // 表示順再設定
  sortedList.forEach((item, index) => {
    ;(item.displayOrder as Mo01278Type).value = (index + 1).toString()
  })
  local.mo01354.values.items = sortedList
}

/**
 * 表示順設定 タブ移動
 *
 * @param e - 移動操作が行ったデータ
 */
function moveDisplayOrder(e: SortableEvent) {
  // 変更前のindex
  const oldIndex = e.oldIndex
  // 変更後のindex
  const newIndex = e.newIndex
  // 移動操作が行わない場合は、処理を中断する
  if (oldIndex === newIndex) return
  // 移動確認ダイアログを呼び出す
  openDialog(oldIndex!, newIndex!)
  return new Promise(() => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      (newValue) => {
        if (newValue) return
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        if (event?.firstBtnClickFlg) {
          // 表示順を更新
          local.mo01354.values.items.forEach((item, index) => {
            ;(item.displayOrder as Mo01278Type).value = (index + 1).toString()
          })
          local.mo01354.values.selectedRowId = local.mo01354.values.items[newIndex!].id
        } else {
          // 移動操作を戻す
          const items = local.mo01354.values.items
          const movedData = items[newIndex!]

          // 移動操作
          items.splice(newIndex!, 1)
          // 旧位置に挿入
          items.splice(oldIndex!, 0, movedData)
          local.mo01354.values.selectedRowId = items[oldIndex!].id
        }
      },
      { once: true }
    )
  })
}

/**
 * ダイヤログを呼び出し
 *
 * @param oldIndex - 変更前のindex
 *
 * @param newIndex - 変更後のindex
 */
function openDialog(oldIndex: number, newIndex: number) {
  const moveBefore = (oldIndex + 1).toString()
  const moveAfter = (newIndex + 1).toString()
  // ダイヤログを呼び出し
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.confirm-dialog-title-info'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10678', [moveBefore, moveAfter]),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンタイプ
      secondBtnType: 'normal3',
      // 第2ボタンラベル
      secondBtnLabel: t('btn.no'),
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    },
  })
}

/**
 * AC002_「×ボタン」押下
 * AC007_「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * AC008_「確定ボタン」押下
 * 「確定」ボタン押下
 */
function confirm() {
  // 表示されているタブ画面の表示順欄の値で課題データ情報の内容をソートする
  sortTableDataList()
  // 返却情報.課題データ情報 = ソート後の課題データ情報の内容
  emit('update:modelValue', { sortList: local.mo01354.values.items })
  // 本画面を閉じ、親画面に返却する。
  close()
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043"
        style="padding-left: 0 !important"
        :oneway-model-value="localOneway.mo00043OneWay"
        @click="sortTableDataList"
      >
      </base-mo00043>
      <!-- タブ switch -->
      <c-v-window
        v-model="local.mo00043.id"
        class="table-header"
      >
        <c-v-window-item
          value="0"
          class="h-100 change"
        >
          <!-- タブ：表示順変更 -->
          <c-v-card>
            <template #title>
              <base-mo01265
                :oneway-model-value="localOneway.mo01265OnewayModelValue"
                @click="sortDeleteClick"
              >
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :max-width="300"
                  :text="t('tooltip.display-order-delete-success')"
                  open-delay="200"
                />
              </base-mo01265>
            </template>
            <c-v-card-text>
              <base-mo01354
                :oneway-model-value="localOneway.mo01354OnewayChange"
                hide-default-footer
                :model-value="local.mo01354"
                class="list-wrapper"
              >
                <!-- 表示順 -->
                <template #[`item.displayOrder`]="{ item }">
                  <c-v-col class="d-flex h-100 width-78">
                    <base-mo01278
                      v-model="item.displayOrder"
                      :oneway-model-value="localOneway.mo01278Oneway"
                      class="sort_textfield margin-top-left w-100"
                      @click="setDisplayOrder(item)"
                    />
                  </c-v-col>
                </template>
                <!-- 課題 -->
                <template #[`item.issues`]="{ item }">
                  <c-v-col class="d-flex align-center">
                    <div
                      class="overflowText"
                      :style="{ width: n1AMinWidth + '!important' }"
                    >
                      {{ item.issues }}
                    </div>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.issues"
                      open-delay="200"
                    />
                  </c-v-col>
                </template>
                <!-- 目標 -->
                <template #[`item.goal`]="{ item }">
                  <c-v-col class="d-flex align-center">
                    <div
                      class="overflowText"
                      :style="{ width: n1AMinWidth + '!important' }"
                    >
                      {{ item.goal }}
                    </div>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.goal"
                      open-delay="200"
                    />
                  </c-v-col>
                </template>
              </base-mo01354>
            </c-v-card-text>
          </c-v-card>
        </c-v-window-item>
        <c-v-window-item
          value="1"
          class="w-auto flex-0-0"
        >
          <!-- タブ：表示順移動 -->
          <c-v-card>
            <template #title>
              <base-mo01265
                :oneway-model-value="localOneway.mo01265OnewayModelValue"
                class="hidden-class-btn"
              />
            </template>
            <c-v-card-text id="customCard">
              <base-mo01354
                :oneway-model-value="localOneway.mo01354OnewayMove"
                hide-default-footer
                :model-value="local.mo01354"
                class="list-wrapper"
                @end="moveDisplayOrder"
              >
                <!-- 表示順 -->
                <template #[`item.displayOrder`]="{ item }">
                  <c-v-col class="d-flex h-100 width-49">
                    <span class="d-flex sort_textfield w-100 align-center justify-end">
                      {{ item.displayOrder.value }}
                    </span>
                  </c-v-col>
                </template>
                <!-- 課題 -->
                <template #[`item.issues`]="{ item }">
                  <c-v-col class="d-flex align-center">
                    <div
                      class="overflowText"
                      :style="{ width: n1AMinWidth + '!important' }"
                    >
                      {{ item.issues }}
                    </div>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.issues"
                      open-delay="200"
                    />
                  </c-v-col>
                </template>
                <!-- 目標 -->
                <template #[`item.goal`]="{ item }">
                  <c-v-col class="d-flex align-center">
                    <div
                      class="overflowText"
                      :style="{ width: n1AMinWidth + '!important' }"
                    >
                      {{ item.goal }}
                    </div>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.goal"
                      open-delay="200"
                    />
                  </c-v-col>
                </template>
              </base-mo01354>
            </c-v-card-text>
          </c-v-card>
        </c-v-window-item>
      </c-v-window>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="300"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mx-2"
          @click="confirm"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="300"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814" />
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';

.hidden-class-btn {
  visibility: hidden;
}

// 表示順のスタイル
:deep(.sort_textfield) {
  font-weight: bold;
}

:deep(th) {
  align-content: center;
  .me-2 {
    margin-inline-end: 0 !important;
  }
}

// 行ドラッグアイコン
:deep(.list-wrapper .drag-indicator-col) {
  display: flex;
  justify-content: center;
}

.width-49 {
  max-width: 49px;
  width: 49px !important;
}

.width-78 {
  max-width: 78px;
  width: 78px !important;
}

.table-header {
  :deep(.v-table__wrapper) {
    overflow: auto;
    border: 0 !important;
  }
  :deep(.v-table__wrapper th) {
    border-top: 1px solid rgba(var(--v-theme-black-200)) !important;
  }
  :deep(.v-table__wrapper th:first-child) {
    border-left: 1px solid rgba(var(--v-theme-black-200)) !important;
  }
  :deep(.v-table__wrapper th:last-child) {
    border-right: 1px solid rgba(var(--v-theme-black-200)) !important;
  }
  :deep(.v-table__wrapper .selected-row td) {
    background: rgb(var(--v-theme-blue-100)) !important;
  }
  :deep(.v-table__wrapper tr td) {
    background-color: #fafafa;
    padding: 0 16px;
  }
  :deep(.v-table__wrapper td:first-child) {
    border-left: 1px solid rgba(var(--v-theme-black-200)) !important;
  }
  :deep(.v-table__wrapper td:last-child) {
    border-right: 1px solid rgba(var(--v-theme-black-200)) !important;
  }

  .change {
    :deep(.v-table__wrapper tr td:first-child) {
      padding: 1px !important;
      background-color: rgb(var(--v-theme-secondaryBackground));
    }
    :deep(.v-table__wrapper .table-cell:focus) {
      box-shadow: 0 0 10px rgb(var(--v-theme-key));
    }
    :deep(.full-width-field) {
      padding: 0 15px !important;
    }
  }
}

:deep(.v-card-item) {
  padding: 8px 0 !important;
}

:deep(.v-card-text) {
  padding: 0;
}

.displayOrderTd {
  :deep(.input-wrapper) {
    height: auto !important;
    input {
      background: transparent;
    }
  }
}
.overflowText {
  width: 100%;
  height: 60px;
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 3;
  -webkit-line-clamp: 3;
}

:deep(.v-btn__content) {
  .v-icon--size-small {
    display: none;
  }
}
</style>
