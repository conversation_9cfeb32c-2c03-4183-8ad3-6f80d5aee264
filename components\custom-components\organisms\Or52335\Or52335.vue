<script setup lang="ts">
/**
 * Or52335:(支援経過記録)会議録取込モーダル
 * GUI01260_会議録取込
 *
 * @description
 * (支援経過記録)会議録取込モーダル
 *
 * <AUTHOR> PHAM HO HAI DANG
 */
import { useI18n } from 'vue-i18n'
import { Or52274Const } from '../Or52274/Or52274.constants'
import { Or52275Const } from '../Or52275/Or52275.constants'
import { Or52274Logic } from '../Or52274/Or52274.logic'
import { Or52275Logic } from '../Or52275/Or52275.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import type { Or52335StateType } from './Or52335.type'
import { Or52335Const } from './Or52335.constants'
import {
  computed,
  nextTick,
  onMounted,
  reactive,
  ref,
  useScreenOneWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
  watch,
} from '#imports'
import type {
  KmkList1,
  KmkList2,
  Or52335ModelValue,
  Or52335OnewayType,
} from '~/types/cmn/business/components/Or52335Type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type {
  IImportMeetingMinutesInitInfoSelectInEntity,
  IImportMeetingMinutesInitInfoSelectOutEntity,
} from '~/repositories/cmn/entities/importMeetingMinutesInitInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or52275Type } from '~/types/cmn/business/components/Or52275Type'
import type {
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

/**************************************************
 * プロパティ (Props)
 **************************************************/
interface Props {
  modelValue: Or52335ModelValue
  onewayModelValue: Or52335OnewayType
  uniqueCpId: string
}
/**
 *引継情報を取得する
 */
const props = defineProps<Props>()

/**************************************************
 * Pinia
 **************************************************/
/**
 *ワンウェイバインドの状態設定
 */
const { setState } = useScreenOneWayBind<Or52335StateType>({
  cpId: Or52335Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 開閉フラグ更新時
     *
     * @param value - 開閉状態
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or52335Const.DEFAULT.IS_OPEN
    },
  },
})
/************************************************
 * Emit
 ************************************************/
/**
 * v-modelの更新イベントを定義
 */
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 定数(Constants)
 **************************************************/
/**
 * 国際化関数の取得
 */
const { t } = useI18n()
/**
 *デフォルトの一方向バインド値
 */
const defaultOnewayModelValue: Or52335OnewayType = {
  cpnFlg: '',
  svJigyoId: '',
  syubetsuId: '',
  shisetuId: '',
  kaigiFlg: '',
  carePlanMethod: '',
  revision: '',
  content: '',
}

/**
 * API取得データ格納用変数
 */
let resData: IImportMeetingMinutesInitInfoSelectOutEntity

/**
 * ローディング状態（API処理中など）
 */
const isLoading = ref(false)

/**
 * 参加区分の選択値
 */
const sankaKbnRef = ref('')

/**
 * 上部チェックボックスのバインドデータ
 */
const topCheckbox = ref<
  {
    label: string
    modelValue: {
      modelValue: boolean
    }
  }[]
>([])

/**
 * 下部チェックボックスのバインドデータ
 */
const botCheckbox = ref<
  {
    label: string
    modelValue: {
      modelValue: boolean
    }
  }[]
>([])

/**
 * 選択中の項目（単一選択）
 */
const selectedItem = ref<Or52275Type>()

/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**
 * OR52274子コンポーネントの識別用ID
 */
const or52274 = ref({ uniqueCpId: '' })

/**
 * OR52275子コンポーネントの識別用ID
 */
const or52275 = ref({ uniqueCpId: '' })

/**
 * OR51775子コンポーネントの識別用ID
 */
const or51775 = ref({ uniqueCpId: '' })

/**
 *Or21814_有機体:確認ダイアログ
 */
const or21814 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or52274Const.CP_ID(1)]: or52274.value,
  [Or52275Const.CP_ID(1)]: or52275.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
})

/**************************************************
 * テンプレートリファレンス(Template Refs)
 **************************************************/
/**
 * Mo00024Type 型のリアクティブ変数
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or52335Const.DEFAULT.IS_OPEN,
})

/**
 * 会議情報リスト（OR52275）
 */
const or52275Data = ref<Or52275Type[]>([])

// システム共有情報ストア
/**
 * システム共通ストアの利用
 */
const systemCommonsStore = useSystemCommonsStore()

/** コピー元の計画期間ID */
const selectedSc1Id = ref<string>('')

/** コピー元のフェースシート履歴ID */
const selectedKaigi1Id = ref<string>('')

/** 履歴データ取得フラグ */
const fetchHistoryDataFlg = ref<boolean>(false)

/**************************************************
 * リアクティブステート(Reactive State)
 **************************************************/

/**
 * ローカルステート（画面用データ）
 */
const local = reactive({
  // OR52335（v-model）
  or52335: {
    ...props.modelValue,
  },

  // 出席者リスト（mo01354）
  mo01354SyusekisyaList: {
    values: {
      selectedRowId: '', // 選択行ID
      selectedRowIds: [], // 複数選択行ID
      items: [], // リスト項目
    },
  } as unknown as Mo01354Type,

  // 会議内容リスト（OR52275に対応）
  mo01354KghTucKrkKaigi2List: {
    values: {
      selectedRowId: '',
      selectedRowIds: [],
      items: [],
    },
  } as unknown as Mo01354Type,

  // 項目一覧1
  kmkList1: [] as KmkList1[],

  // 項目一覧2
  kmkList2: [] as KmkList2[],

  // 会議リスト（SYP会議用）
  mo01354cpnTucSypKaigi2List: {
    values: {
      selectedRowId: '',
      selectedRowIds: [],
      items: [],
    },
  } as unknown as Mo01354Type,

  // 改行フラグ（出力時用）
  lineBreak: {
    modelValue: true,
  },
})

/**
 *  一方向バインディング用のリアクティブなローカルデータ
 */
const localOneway = reactive({
  or52335: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '1400px',
    maxWidth: '1400px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or52335',
      toolbarName: 'Or52335ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      toolbarTitle: t('label.import-meeting-minutes'),
    },
  } as unknown as Mo00024OnewayType,
  mo01354SyusekisyaList: {
    height: '70px',
    showPaginationTopFlg: false,
    showPaginationBottomFlg: false,
    headers: [
      {
        title: t('label.person'),
        key: 'honninKnj',
        width: '95px',
        sortable: false,
      },
      {
        title: t('label.family'),
        key: 'kazokuKnj',
        width: '95px',
        sortable: false,
      },
      {
        title: t('label.relationship'),
        key: 'zokugaraKnj',
        width: '130px',
        sortable: false,
      },
      {
        title: t('label.remarks'),
        key: 'bikoKnj',
        width: '190px',
        sortable: false,
      },
    ],
  } as unknown as Mo01354OnewayType,
  mo01354KghTucKrkKaigi2List: {
    height: '134px',
    showPaginationTopFlg: false,
    showPaginationBottomFlg: false,
    headers: [
      {
        title: `${t('label.affiliation')}（${t('label.job-type')}）`,
        key: 'affiliation',
        width: '225px',
        sortable: false,
      },
      {
        title: t('label.name-consent'),
        key: 'nameConsent',
        width: '170px',
        sortable: false,
      },
      {
        title: `${t('label.affiliation')}（${t('label.job-type')}）`,
        key: 'affiliation2',
        width: '225px',
        sortable: false,
      },
      {
        title: t('label.name-consent'),
        key: 'nameConsent2',
        width: '170px',
        sortable: false,
      },
      {
        title: `${t('label.affiliation')}（${t('label.job-type')}）`,
        key: 'affiliation3',
        width: '225px',
        sortable: false,
      },
      {
        title: t('label.name-consent'),
        key: 'nameConsent3',
        width: '170px',
        sortable: false,
      },
    ],
  } as unknown as Mo01354OnewayType,
  // 閉じる
  mo00611OneWay: {
    btnLabel: t('btn.close'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 保存
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.confirm-btn'),
    disabled: false,
  } as Mo00609OnewayType,
  riyuKnjMo00615: {
    itemLabel: t('label.reason-absence-pre-line'),
    showItemLabel: true,
    showRequiredLabel: false,
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  kentoKnjMo00615: {
    itemLabel: t('label.consider-item-pre-line'),
    showItemLabel: true,
    showRequiredLabel: false,
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  naiyoKnjMo00615: {
    itemLabel: t('label.consider-contents-pre-line'),
    showItemLabel: true,
    showRequiredLabel: false,
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  ketsuronKnjMo00615: {
    itemLabel: t('label.conclusion'),
    showItemLabel: true,
    showRequiredLabel: false,
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  kadaiKnjMo00615: {
    itemLabel: t('label.remaining-issues-pre-line'),
    showItemLabel: true,
    showRequiredLabel: false,
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  jikaiYmdMo00615: {
    itemLabel: t('label.next-event-date'),
    showItemLabel: true,
    showRequiredLabel: false,
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  mo01354SussekiList: {
    height: '134px',
    showPaginationTopFlg: false,
    showPaginationBottomFlg: false,
    headers: [
      {
        title: t('label.meeting-attendees'),
        key: 'susseki',
        sortable: false,
      },
    ],
  } as unknown as Mo01354OnewayType,
  sussekiMo00615: {
    itemLabel: t('label.main-topics-discussed-pre-line'),
    showItemLabel: true,
    showRequiredLabel: false,
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  sankaKbnMo00615: {
    itemLabel: t('label.resident-and-family-participation'),
    showItemLabel: true,
    showRequiredLabel: false,
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  sankaKbnMo00039: {
    showItemLabel: false,
    items: [
      { label: Or52335Const.DEFAULT.Missing, value: '1' },
      { label: Or52335Const.DEFAULT.Out, value: '2' },
    ] as CodeType[],
    disabled: true,
  } as Mo00039OnewayType,
  mo01354cpnTucSypKaigi2List: {
    height: '134px',
    showPaginationTopFlg: false,
    showPaginationBottomFlg: false,
    headers: [
      {
        title: t('label.affiliation'),
        key: 'shozokuKnj',
        width: '170px',
        sortable: false,
      },
      {
        title: t('label.occupation'),
        key: 'shokushuKnj',
        width: '170px',
        sortable: false,
      },
      {
        title: t('label.name'),
        key: 'nameKnj',
        width: '170px',
        sortable: false,
      },
      {
        title: t('label.main-duties'),
        key: 'tantoKnj',
        width: '170px',
        sortable: false,
      },
      {
        title: t('label.affiliation'),
        key: 'shozoku2Knj',
        width: '170px',
        sortable: false,
      },
      {
        title: t('label.occupation'),
        key: 'shokushu2Knj',
        width: '170px',
        sortable: false,
      },
      {
        title: t('label.name'),
        key: 'name2Knj',
        width: '170px',
        sortable: false,
      },
      {
        title: t('label.main-duties'),
        key: 'tanto2Knj',
        width: '170px',
        sortable: false,
      },
    ],
  } as unknown as Mo01354OnewayType,
  mo01354cpnTucSypKaigi2List_2: {
    height: '134px',
    showPaginationTopFlg: false,
    showPaginationBottomFlg: false,
    headers: [
      {
        title: t('label.affiliation'),
        key: 'shozokuKnj',
        sortable: false,
      },
      {
        title: t('label.occupation'),
        key: 'shokushuKnj',
        sortable: false,
      },
      {
        title: t('label.name'),
        key: 'nameKnj',
        sortable: false,
      },
      {
        title: t('label.main-duties'),
        key: 'tantoKnj',
        sortable: false,
      },
    ],
  } as unknown as Mo01354OnewayType,
  // 上書ボタン
  mo00611OverWriteOneway: {
    btnLabel: t('btn.overwrite'),
    appendIcon: 'arrow_downward',
    variant: 'tonal',
    labelColor: 'black',
    color: 'black',
  } as Mo00611OnewayType,
  // 追加ボタン
  mo00611OnewayAddDown: {
    btnLabel: t('btn.add'),
    appendIcon: 'arrow_downward',
    variant: 'tonal',
    labelColor: 'black',
    color: 'black',
  } as Mo00611OnewayType,
  mo00018OneWayLineBreak: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.line-break'),
    isVerticalLabel: true,
    showItemLabel: false,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  captureFieldsMo00615: {
    itemLabel: t('label.import-item-x'),
    showItemLabel: true,
    showRequiredLabel: false,
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  mo00009Oneway: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  or51775Oneway: {
    title: t('label.content'),
    t1Cd: '2700',
    t2Cd: '1',
    t3Cd: '0',
    tableName: 'cpn_tuc_syp_keika',
    columnName: 'memo_knj',
    assessmentMethod: props.onewayModelValue.cpnFlg,
    inputContents: local.or52335.modelValue,
    shokuinId: systemCommonsStore.getUserId,
  } as unknown as Or51775OnewayType,
  mo00045Oneway: {
    width: '400',
    maxLength: '4000',
    showItemLabel: false,
    maxRows: 6,
    rows: 6,
  },
})

/**************************************************
 * 計算プロパティ(Computed Properties)
 **************************************************/

/**
 * 画面モード判定（carePlanMethod / kaigiFlg / revisionにより分岐）
 */
const modeScreen = computed(() => {
  if (
    localOneway.or52335.carePlanMethod === Or52335Const.DEFAULT.CAREPLANMETHOD_5 &&
    localOneway.or52335.kaigiFlg === Or52335Const.DEFAULT.KAIGIFLG_2
  ) {
    return Or52335Const.DEFAULT.MODE_1
  } else if (
    localOneway.or52335.carePlanMethod === Or52335Const.DEFAULT.CAREPLANMETHOD_5 &&
    localOneway.or52335.kaigiFlg === Or52335Const.DEFAULT.KAIGIFLG_1
  ) {
    return Or52335Const.DEFAULT.MODE_2
  } else if (
    localOneway.or52335.carePlanMethod !== Or52335Const.DEFAULT.CAREPLANMETHOD_5 &&
    localOneway.or52335.revision === Or52335Const.DEFAULT.REVISION_H21
  ) {
    return Or52335Const.DEFAULT.MODE_3
  } else {
    return Or52335Const.DEFAULT.MODE_4
  }
})

/**
 *ダイアログ表示フラグ
 */
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * ウォッチャー(Watchers)
 **************************************************/
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      close()
    }
  }
)

/**
 * 計画期間変更時の処理
 */
watch(
  () => Or52274Logic.state.get(or52274.value.uniqueCpId)?.selectedItemId,
  async (newValue) => {
    selectedSc1Id.value = newValue ?? ''
    await fetchData().finally(() => {
      InitCode()
    })
  }
)

/**
 * 履歴変更時の処理
 */
watch(
  () => Or52275Logic.state.get(or52275.value.uniqueCpId)?.selectedItemId,
  async (newValue) => {
    selectedKaigi1Id.value = newValue ?? ''
    await fetchData().finally(() => {
      InitCode()
    })
  }
)

watch(selectedSc1Id, () => {
  if (!selectedSc1Id.value) {
    return
  }

  // 履歴データ取得フラグ設定
  fetchHistoryDataFlg.value = true
})

watch(fetchHistoryDataFlg, async () => {
  if (!fetchHistoryDataFlg.value) {
    return
  }

  await fetchHistoryData().finally(() => {
    fetchHistoryDataFlg.value = false
  })
})

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.secondBtnClickFlg) {
      local.or52335.modelValue = ''
      add()
    }
  }
)
/**************************************************
 * ライフサイクルフック(Lifecycle Hooks)
 **************************************************/
onMounted(async () => {
  await Init()
})

/**************************************************
 * メソッド(Methods)
 **************************************************/
/**
 * 初期化処理
 */
async function Init() {
  isLoading.value = true
  // ケアマネ文章マスタ情報取得
  const inputData: IImportMeetingMinutesInitInfoSelectInEntity = {
    cpnFlg: localOneway.or52335.cpnFlg,
    mode: '1',
    svJigyoId: localOneway.or52335.svJigyoId,
    userid: systemCommonsStore.getUserId ?? '',
    syubetsuId: localOneway.or52335.syubetsuId,
    shisetuId: localOneway.or52335.shisetuId,
    sc1Id: '0',
    kaigi1Id: '0',
    kaigiFlg: localOneway.or52335.kaigiFlg,
  }
  // ケアマネ文章マスタ初期情報取得
  resData = await ScreenRepository.select('importMeetingMinutesInitInfoSelect', inputData)
  selectedSc1Id.value = resData.data.kghTucKrkKikanList[0].sc1Id
  Or52274Logic.state.set({
    uniqueCpId: or52274.value.uniqueCpId,
    state: {
      items: resData.data.kghTucKrkKikanList,
      selectedItemId: selectedSc1Id.value,
    },
  })
  await fetchData().finally(() => {
    InitCode()
  })
  isLoading.value = false
}
/**
 *データ取得処理
 */
async function fetchData() {
  const fetchDataAction = new Promise((resolve) => {
    resolve('')
  })
  await fetchDataAction

  local.or52335.modelValue = localOneway.or52335.content
  fetchDataAllMode()
  switch (modeScreen.value) {
    case Or52335Const.DEFAULT.MODE_1:
      fetchDataMode1()
      break
    case Or52335Const.DEFAULT.MODE_2:
      fetchDataMode2()
      break
    case Or52335Const.DEFAULT.MODE_3:
      fetchDataMode3()
      break
    case Or52335Const.DEFAULT.MODE_4:
      fetchDataMode4()
      break
  }

  await nextTick()
}
/**
 *全モード共通のデータ取得処理
 */
function fetchDataAllMode() {
  local.mo01354KghTucKrkKaigi2List.values.items = []
  local.kmkList1 = []
  local.kmkList2 = []
  local.mo01354cpnTucSypKaigi2List.values.items = []

  local.mo01354SyusekisyaList.values.items = []
  resData.data.kghTucKrkKaigi2List
    .filter((x) => x.kaigi1Id === selectedKaigi1Id.value)
    .forEach((x, index) => {
      local.mo01354KghTucKrkKaigi2List.values.items.push({
        id: index.toString(),
        susseki: x.susseki,
        affiliation: x.shozoku1Knj,
        nameConsent: x.name1Knj,
        affiliation2: x.shozoku2Knj,
        nameConsent2: x.name2Knj,
        affiliation3: x.shozoku3Knj,
        nameConsent3: x.name3Knj,
        selectable: false,
      })
    })
  if (
    resData.data.kghTucKrkKaigi2List.filter((x) => x.kaigi1Id === selectedKaigi1Id.value).length < 3
  ) {
    for (
      let i = 0;
      i <
      3 -
        resData.data.kghTucKrkKaigi2List.filter((x) => x.kaigi1Id === selectedKaigi1Id.value)
          .length;
      i++
    ) {
      local.mo01354KghTucKrkKaigi2List.values.items.push({
        id: '',
        affiliation: '',
        nameConsent: '',
        affiliation2: '',
        nameConsent2: '',
        affiliation3: '',
        nameConsent3: '',
        selectable: false,
      })
    }
  }

  resData.data.kmkList1
    .filter((x) => x.kaigi1Id === selectedKaigi1Id.value)
    .forEach((x) => {
      local.kmkList1.push({
        sankaKbn: x.sankaKbn,
        sankaKnj: x.sankaKnj,
        riyuKnj: x.riyuKnj ?? '',
        kentoKnj: x.kentoKnj ?? '',
        memoKnj: x.memoKnj ?? '',
        ketsuronKnj: x.ketsuronKnj ?? '',
        kadaiKnj: x.kadaiKnj ?? '',
        jikaiYmd: x.jikaiYmd ?? '',
        kaigi1Id: x.kaigi1Id,
        sc1Id: x.sc1Id,
        honninKnj: x.honninKnj,
        kazokuKnj: x.kazokuKnj,
        zokugaraKnj: x.zokugaraKnj,
        bikoKnj: x.bikoKnj,
      })
      local.mo01354SyusekisyaList.values.items.push({
        id: x.kaigi1Id,
        sc1Id: x.sc1Id,
        honninKnj: x.honninKnj,
        kazokuKnj: x.kazokuKnj,
        zokugaraKnj: x.zokugaraKnj,
        bikoKnj: x.bikoKnj,
        selectable: false,
      })
    })
  sankaKbnRef.value = local.kmkList1[0]?.sankaKbn ?? ''

  resData.data.cpnTucSypKaigi2List
    .filter((x) => x.kaigi1Id === selectedKaigi1Id.value)
    .forEach((x) => {
      local.mo01354cpnTucSypKaigi2List.values.items.push({
        id: x.kaigi1Id,
        shozokuKnj: x.shozokuKnj,
        shokushuKnj: x.shokushuKnj,
        nameKnj: x.nameKnj,
        tantoKnj: x.tantoKnj,
        shozoku2Knj: x.shozoku2Knj,
        shokushu2Knj: x.shokushu2Knj,
        name2Knj: x.name2Knj,
        tanto2Knj: x.tanto2Knj,
        selectable: false,
      })
    })

  if (
    resData.data.cpnTucSypKaigi2List.filter((x) => x.kaigi1Id === selectedKaigi1Id.value).length < 3
  ) {
    for (
      let i = 0;
      i <
      3 -
        resData.data.cpnTucSypKaigi2List.filter((x) => x.kaigi1Id === selectedKaigi1Id.value)
          .length;
      i++
    ) {
      local.mo01354cpnTucSypKaigi2List.values.items.push({
        id: '',
        shozokuKnj: '',
        shokushuKnj: '',
        nameKnj: '',
        tantoKnj: '',
        shozoku2Knj: '',
        shokushu2Knj: '',
        name2Knj: '',
        tanto2Knj: '',
        selectable: false,
      })
    }
  }

  resData.data.kmkList2
    .filter((x) => x.kaigi1Id === selectedKaigi1Id.value)
    .forEach((x) => {
      local.kmkList2.push({
        kentoKnj: x.kentoKnj,
        naiyoKnj: x.naiyoKnj,
        ketsuronKnj: x.ketsuronKnj,
        kadaiKnj: x.kadaiKnj,
        jikaiYmd: x.jikaiYmd,
        sogoHenkoKnj: x.sogoHenkoKnj,
        keikakuHenkoKnj: x.keikakuHenkoKnj,
        kessekiTaioKnj: x.kessekiTaioKnj,
        kaigi1Id: x.kaigi1Id,
        sc1Id: x.sc1Id,
      })
    })
}
/**
 *モード1用のデータ取得処理
 */
function fetchDataMode1() {
  or52275Data.value = []
  resData.data.kghTucKrkKaigi1List.map((x) => {
    return or52275Data.value.push({
      sc1Id: x.sc1Id,
      kaigi1Id: x.kaigi1Id,
      createYmd: x.createYmd ?? '',
      shokuKnj:
        resData.data.kmkList1.find((y) => y.sc1Id === x.sc1Id && y.kaigi1Id === x.kaigi1Id)
          ?.shokuKnj ?? '',
      kaigiYmd: x.kaigiYmd ?? '',
      whereKnj: x.whereKnj ?? '',
      timeHm: x.timeHm ?? '',
      kaisuu: x.kaisuu ?? '',
    })
  })
}
/**
 *モード2用のデータ取得処理
 */
function fetchDataMode2() {
  or52275Data.value = []
  resData.data.kghTucKrkKaigi1List.map((x) => {
    return or52275Data.value.push({
      sc1Id: x.sc1Id,
      kaigi1Id: x.kaigi1Id,
      createYmd: x.createYmd ?? '',
      shokuKnj:
        resData.data.kmkList1.find((y) => y.sc1Id === x.sc1Id && y.kaigi1Id === x.kaigi1Id)
          ?.shokuKnj ?? '',
      kaigiYmd: x.kaigiYmd ?? '',
      whereKnj: x.whereKnj ?? '',
      timeHm: x.timeHm ?? '',
      kaisuu: x.kaisuu ?? '',
    })
  })
}
/**
 *モード3用のデータ取得処理
 */
function fetchDataMode3() {
  or52275Data.value = []
  local.mo01354SyusekisyaList.values.items = []
  local.kmkList2 = []
  resData.data.cpnTucSypKaigi1List.map((x) => {
    return or52275Data.value.push({
      sc1Id: x.sc1Id ?? '',
      kaigi1Id: x.kaigi1Id,
      createYmd: x.createYmd ?? '',
      shokuKnj:
        resData.data.kmkList1.find((y) => y.sc1Id === x.sc1Id && y.kaigi1Id === x.kaigi1Id)
          ?.shokuKnj ?? '',
      whereKnj: x.whereKnj ?? '',
      timeHm: x.timeHm ?? '',
      kaisuu: x.kaisuu ?? '',
      caseNo: x.caseNo ?? '',
      kaiteiFlg: x.kaiteiFlg ?? '',
    })
  })
}
/**
 *モード4用のデータ取得処理
 */
function fetchDataMode4() {
  or52275Data.value = []
  resData.data.cpnTucSypKaigi1List.map((x) => {
    return or52275Data.value.push({
      sc1Id: x.sc1Id ?? '',
      kaigi1Id: x.kaigi1Id,
      createYmd: x.createYmd ?? '',
      shokuKnj:
        resData.data.kmkList1.find((y) => y.sc1Id === x.sc1Id && y.kaigi1Id === x.kaigi1Id)
          ?.shokuKnj ?? '',
      whereKnj: x.whereKnj ?? '',
      timeHm: x.timeHm ?? '',
      kaisuu: x.kaisuu ?? '',
      caseNo: x.caseNo ?? '',
      kaiteiFlg: x.kaiteiFlg ?? '',
    })
  })
}
/**
 *各種初期コード設定処理
 */
function InitCode() {
  switch (modeScreen.value) {
    case Or52335Const.DEFAULT.MODE_1:
      initCheckBoxMode1()
      break
    case Or52335Const.DEFAULT.MODE_2:
      initCheckBoxMode2()
      break
    case Or52335Const.DEFAULT.MODE_3:
      initCheckBoxMode34()
      break
    case Or52335Const.DEFAULT.MODE_4:
      initCheckBoxMode34()
      break
  }
}
/**
 *モード1用チェックボックス初期化処理
 */
function initCheckBoxMode1() {
  topCheckbox.value = [
    {
      label: Or52335Const.CHECK_BOX.CREATE_YMD,
      modelValue: {
        modelValue: !isNumOrEmpty(selectedItem.value?.createYmd),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.WHERE_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(selectedItem.value?.whereKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.TIME_HM,
      modelValue: {
        modelValue: !isNumOrEmpty(selectedItem.value?.timeHm),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.KAISU,
      modelValue: {
        modelValue: !isNumOrEmpty(selectedItem.value?.kaisuu),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.HONNIN_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(
          local.mo01354SyusekisyaList.values.items[0]?.honninKnj as string | undefined
        ),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.KAZOKU_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(
          local.mo01354SyusekisyaList.values.items[0]?.kazokuKnj as string | undefined
        ),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.ZOKUGARA_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(
          local.mo01354SyusekisyaList.values.items[0]?.zokugaraKnj as string | undefined
        ),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.BIKO_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(
          local.mo01354SyusekisyaList.values.items[0]?.bikoKnj as string | undefined
        ),
      },
    },
  ]
  botCheckbox.value = [
    {
      label: Or52335Const.CHECK_BOX.SUSSEKI,
      modelValue: {
        modelValue: !isNumOrEmpty(local.mo01354KghTucKrkKaigi2List.values.items[0].id),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.RIYU_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList1[0]?.riyuKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.KENTO_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList1[0]?.kentoKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.MEMO_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList1[0]?.memoKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.KETSURON_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList1[0]?.ketsuronKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.KADAI_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList1[0]?.kadaiKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.JIKAI_YMD,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList1[0]?.jikaiYmd),
      },
    },
  ]
}
/**
 *モード2用チェックボックス初期化処理
 */
function initCheckBoxMode2() {
  topCheckbox.value = [
    {
      label: Or52335Const.CHECK_BOX.CREATE_YMD,
      modelValue: {
        modelValue: !isNumOrEmpty(selectedItem.value?.createYmd),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.WHERE_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(selectedItem.value?.whereKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.TIME_HM,
      modelValue: {
        modelValue: !isNumOrEmpty(selectedItem.value?.timeHm),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.KAISU,
      modelValue: {
        modelValue: !isNumOrEmpty(selectedItem.value?.kaisuu),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.SUSSEKI,
      modelValue: {
        modelValue: !isNumOrEmpty(local.mo01354KghTucKrkKaigi2List.values.items[0].id),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.MEMO_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList1[0]?.kentoKnj),
      },
    },
  ]
  botCheckbox.value = []
}
/**
 *モード3用チェックボックス初期化処理
 */
function initCheckBoxMode34() {
  topCheckbox.value = [
    {
      label: Or52335Const.CHECK_BOX.CREATE_YMD,
      modelValue: {
        modelValue: !isNumOrEmpty(selectedItem.value?.createYmd),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.WHERE_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(selectedItem.value?.whereKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.TIME_HM,
      modelValue: {
        modelValue: !isNumOrEmpty(selectedItem.value?.timeHm),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.KAISU,
      modelValue: {
        modelValue: !isNumOrEmpty(selectedItem.value?.kaisuu),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.SANKA_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList1[0]?.sankaKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.SUSSEKI,
      modelValue: {
        modelValue: !isNumOrEmpty(local.mo01354cpnTucSypKaigi2List.values.items[0].id),
      },
    },
  ]
  botCheckbox.value = [
    {
      label: Or52335Const.CHECK_BOX.NAIYO_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList2[0]?.kentoKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.MEMO_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList2[0]?.naiyoKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.KETSURON_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList2[0]?.ketsuronKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.KADAI_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList2[0]?.kadaiKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.JIKAI_YMD,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList2[0]?.jikaiYmd),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.SOGOHENKO_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList2[0]?.sogoHenkoKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.KEIKAKUHENKO_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList2[0]?.keikakuHenkoKnj),
      },
    },
    {
      label: Or52335Const.CHECK_BOX.KESSEKITAIO_KNJ,
      modelValue: {
        modelValue: !isNumOrEmpty(local.kmkList2[0]?.kessekiTaioKnj),
      },
    },
  ]
}
/**
 * 数値または空文字かどうかを判定して真偽値を返却
 *
 * @param value - 判定対象の文字列
 *
 * @returns true: 数値または空文字／false: それ以外
 */
function isNumOrEmpty(value: string | undefined) {
  if (value === undefined || value === '') return true
  return false
}
/**
 * 上書き処理（条件チェック後、add実行）
 */
function overwrite() {
  if (!isNumOrEmpty(local.or52335.modelValue)) {
    showOr21814MsgTwoBtn(t('message.i-cmn-10218'))
  } else {
    add()
  }
}

/**
 * 新規追加処理
 */
function add() {
  const line = local.lineBreak.modelValue ? Or52335Const.DEFAULT.line : ''
  let str = ''
  const fullCheckbox = topCheckbox.value.concat(botCheckbox.value)
  fullCheckbox
    .filter((item) => item.modelValue.modelValue)
    .forEach((item) => {
      switch (item.label) {
        case Or52335Const.CHECK_BOX.CREATE_YMD:
          str = str + formatText(Or52335Const.TEXT.CREATE_YMD, line, selectedItem.value?.createYmd)
          break
        case Or52335Const.CHECK_BOX.WHERE_KNJ:
          str = str + formatText(Or52335Const.TEXT.WHERE_KNJ, line, selectedItem.value?.whereKnj)
          break
        case Or52335Const.CHECK_BOX.TIME_HM:
          str = str + formatText(Or52335Const.TEXT.TIME_HM, line, selectedItem.value?.timeHm)
          break
        case Or52335Const.CHECK_BOX.KAISU:
          str = str + formatText(Or52335Const.TEXT.KAISU, line, selectedItem.value?.kaisuu)
          break
        case Or52335Const.CHECK_BOX.HONNIN_KNJ:
          str =
            str +
            formatText(
              Or52335Const.TEXT.HONNIN_KNJ,
              line,
              local.mo01354SyusekisyaList.values.items[0]?.person as string | undefined
            )
          break
        case Or52335Const.CHECK_BOX.KAZOKU_KNJ:
          str =
            str +
            formatText(
              Or52335Const.TEXT.KAZOKU_KNJ,
              line,
              local.mo01354SyusekisyaList.values.items[0]?.family as string | undefined
            )
          break
        case Or52335Const.CHECK_BOX.ZOKUGARA_KNJ:
          str =
            str +
            formatText(
              Or52335Const.TEXT.ZOKUGARA_KNJ,
              line,
              local.mo01354SyusekisyaList.values.items[0]?.relationship as string | undefined
            )
          break
        case Or52335Const.CHECK_BOX.BIKO_KNJ:
          str =
            str +
            formatText(
              Or52335Const.TEXT.BIKO_KNJ,
              line,
              local.mo01354SyusekisyaList.values.items[0]?.remarks as string | undefined
            )
          break
        case Or52335Const.CHECK_BOX.SUSSEKI:
          str += formatTextSusseki(Or52335Const.TEXT.SUSSEKI, line)
          break
        case Or52335Const.CHECK_BOX.RIYU_KNJ:
          str = str + formatText(Or52335Const.TEXT.RIYU_KNJ, line, local.kmkList1[0]?.riyuKnj)
          break
        case Or52335Const.CHECK_BOX.KENTO_KNJ:
          str = str + formatText(Or52335Const.TEXT.KENTO_KNJ, line, local.kmkList1[0]?.kentoKnj)
          break
        case Or52335Const.CHECK_BOX.MEMO_KNJ:
          str = str + formatText(Or52335Const.TEXT.MEMO_KNJ, line, local.kmkList1[0]?.memoKnj)
          break
        case Or52335Const.CHECK_BOX.KETSURON_KNJ:
          str =
            str + formatText(Or52335Const.TEXT.KETSURON_KNJ, line, local.kmkList1[0]?.ketsuronKnj)
          break
        case Or52335Const.CHECK_BOX.KADAI_KNJ:
          str = str + formatText(Or52335Const.TEXT.KADAI_KNJ, line, local.kmkList1[0]?.kadaiKnj)
          break
        case Or52335Const.CHECK_BOX.JIKAI_YMD:
          str = str + formatText(Or52335Const.TEXT.JIKAI_YMD, line, local.kmkList1[0]?.jikaiYmd)
          break
        case Or52335Const.CHECK_BOX.SANKA_KNJ:
          str = str + formatText(Or52335Const.TEXT.SANKA_KNJ, line, local.kmkList1[0]?.sankaKnj)
          break
        case Or52335Const.CHECK_BOX.NAIYO_KNJ:
          str = str + formatText(Or52335Const.TEXT.NAIYO_KNJ, line, local.kmkList2[0]?.kentoKnj)
          break
        case Or52335Const.CHECK_BOX.SOGOHENKO_KNJ:
          str =
            str + formatText(Or52335Const.TEXT.SOGOHENKO_KNJ, line, local.kmkList2[0]?.sogoHenkoKnj)
          break
        case Or52335Const.CHECK_BOX.KEIKAKUHENKO_KNJ:
          str =
            str +
            formatText(Or52335Const.TEXT.KEIKAKUHENKO_KNJ, line, local.kmkList2[0]?.keikakuHenkoKnj)
          break
        case Or52335Const.CHECK_BOX.KESSEKITAIO_KNJ:
          str =
            str +
            formatText(Or52335Const.TEXT.KESSEKITAIO_KNJ, line, local.kmkList2[0]?.kessekiTaioKnj)
          break
      }
    })

  local.or52335.modelValue += isNumOrEmpty(local.or52335.modelValue) ? str.slice(2) : str
}
/**
 * テキストを整形して返却（空なら空文字）
 *
 * @param title - 見出しタイトル
 *
 * @param line - 改行または区切り文字列
 *
 * @param text - 表示対象のテキスト
 *
 * @returns 整形済みの文字列
 */
function formatText(title: string, line: string, text: string | undefined) {
  if (isNumOrEmpty(text)) return ''
  return line + '【' + title + '】' + text
}

/**
 * 出席者情報をモード別に整形して返却
 *
 * @param title - 見出しタイトル
 *
 * @param line - 改行または区切り文字列
 *
 * @returns 整形済みの出席者文字列
 */
function formatTextSusseki(title: string, line: string) {
  let str = line + '【' + title + '】'
  switch (modeScreen.value) {
    case '1':
      local.mo01354KghTucKrkKaigi2List.values.items.forEach((item) => {
        if (!isNumOrEmpty(item.id)) {
          str += item.nameConsent
          if (!isNumOrEmpty(item.affiliation as string | undefined)) {
            str += '（' + item.affiliation + '）、'
          }
          str += item.nameConsent2
          if (!isNumOrEmpty(item.affiliation2 as string | undefined)) {
            str += '（' + item.affiliation2 + '）、'
          }
          str += item.nameConsent
          if (!isNumOrEmpty(item.affiliation3 as string | undefined)) {
            str += '（' + item.affiliation3 + '）'
          }
        }
      })
      break
    case '2':
      local.mo01354KghTucKrkKaigi2List.values.items.forEach((item) => {
        if (!isNumOrEmpty(item.id)) {
          str += item.susseki
        }
      })
      break
    case '3':
      local.mo01354cpnTucSypKaigi2List.values.items.forEach((item) => {
        if (!isNumOrEmpty(item.id)) {
          str += item.nameKnj
          if (
            !isNumOrEmpty(item.shozokuKnj as string | undefined) ||
            !isNumOrEmpty(item.shokushuKnj as string | undefined) ||
            !isNumOrEmpty(item.tantoKnj as string | undefined)
          ) {
            str += '（' + item.shozokuKnj + '、' + item.shokushuKnj + '、' + item.tantoKnj + '）、'
          }
          str += item.name2Knj
          if (
            !isNumOrEmpty(item.shozoku2Knj as string | undefined) ||
            !isNumOrEmpty(item.shokushu2Knj as string | undefined) ||
            !isNumOrEmpty(item.tanto2Knj as string | undefined)
          ) {
            str += '（' + item.shozoku2Knj + '、' + item.shokushu2Knj + '、' + item.tanto2Knj + '）'
          }
        }
      })
      break
  }
  return str.replace('、、', '、')
}
/**
 * 確認処理を実行
 */
function confirm() {
  emit('update:modelValue', local.or52335)
  close()
}
/**
 * フェースシート履歴データ取得
 */
const fetchHistoryData = async () => {
  const fetchDataAction = new Promise((resolve) => {
    resolve('')
  })
  await fetchDataAction
  selectedItem.value = {
    kaigi1Id: '',
  }
  const listData = or52275Data.value.filter((x) => x.sc1Id === selectedSc1Id.value)
  if (listData.length > 0) {
    selectedKaigi1Id.value = listData[0].kaigi1Id
    selectedItem.value = listData[0]
  } else {
    selectedKaigi1Id.value = ''
  }
  Or52275Logic.state.set({
    uniqueCpId: or52275.value.uniqueCpId,
    state: {
      items: listData,
      selectedItemId: selectedKaigi1Id.value,
      kikanKanriFlg:
        localOneway.or52335.carePlanMethod === Or52335Const.DEFAULT.CAREPLANMETHOD_5 ? '1' : '0',
    },
  })

  await fetchData()
}
/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  // 画面を閉じる。
  setState({ isOpen: false })
}

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleConfirm51775 = (data: Or51775ConfirmType) => {
  local.or52335.modelValue += data.value
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * 入力支援ポップアップを表示
 */
function onClick() {
  // 入力支援ポップアップを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 閉じるボタン押下_保存権限がない場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet class="container">
        <!-- ローディング -->
        <v-overlay
          :model-value="isLoading"
          :persistent="false"
          class="align-center justify-center"
          ><v-progress-circular
            indeterminate
            color="primary"
          ></v-progress-circular
        ></v-overlay>
        <c-v-row>
          <!-- 計画期間一覧セクション -->
          <g-custom-or52274
            v-bind="or52274"
            style="margin: 0px 8px"
          />

          <g-custom-or52275 v-bind="or52275" />
        </c-v-row>
        <!-- １. 初期表示(親画面.ケアプラン方式 ≠ 5:パッケージプラン  且つ  親画面.会議録書式 = 2:新の場合)-->
        <div v-show="modeScreen === Or52335Const.DEFAULT.MODE_1">
          <!-- 利用者・家族の出席セクション -->
          <c-v-row class="table-wrapper v-col">
            <base-mo01354
              v-model="local.mo01354SyusekisyaList"
              :oneway-model-value="localOneway.mo01354SyusekisyaList"
              hide-default-footer
              hide-no-data
            />
          </c-v-row>
          <!-- 所属一覧セクション１ -->
          <c-v-row class="table-wrapper v-col">
            <base-mo01354
              v-model="local.mo01354KghTucKrkKaigi2List"
              :oneway-model-value="localOneway.mo01354KghTucKrkKaigi2List"
              hide-default-footer
              hide-no-data
            />
          </c-v-row>
          <!-- 欠席者及び欠席理由 -->
          <c-v-row
            class="ma-0 px-2 pt-2"
            name="row-2"
          >
            <c-v-col
              cols="1"
              class="label-item border d-flex align-center"
            >
              <base-mo00615
                class="ma-0 bg-transparent"
                :oneway-model-value="localOneway.riyuKnjMo00615"
                style="white-space: pre-line"
              />
            </c-v-col>
            <c-v-col
              cols="11"
              class="border"
            >
              {{ local.kmkList1[0]?.riyuKnj ?? '' }}
            </c-v-col>
          </c-v-row>
          <!-- 検討内容セクション１ -->
          <div class="mt-1 memo-content">
            <!-- 検討した項目 -->
            <c-v-row
              class="ma-0 px-2"
              name="row-2"
            >
              <c-v-col
                cols="1"
                class="label-item border d-flex align-center"
              >
                <base-mo00615
                  class="ma-0 bg-transparent"
                  :oneway-model-value="localOneway.kentoKnjMo00615"
                  style="white-space: pre-line"
                />
              </c-v-col>
              <c-v-col
                cols="11"
                class="border"
              >
                {{ local.kmkList1[0]?.kentoKnj }}
              </c-v-col>
            </c-v-row>
            <!-- 検討内容 -->
            <c-v-row
              class="ma-0 px-2"
              name="row-2"
            >
              <c-v-col
                cols="1"
                class="label-item border d-flex align-center"
              >
                <base-mo00615
                  class="ma-0 bg-transparent"
                  :oneway-model-value="localOneway.naiyoKnjMo00615"
                  style="white-space: pre-line"
                />
              </c-v-col>
              <c-v-col
                cols="11"
                class="border"
              >
                {{ local.kmkList1[0]?.memoKnj }}
              </c-v-col>
            </c-v-row>
            <!-- 結論 -->
            <c-v-row
              class="ma-0 px-2"
              name="row-2"
            >
              <c-v-col
                cols="1"
                class="label-item border d-flex align-center"
              >
                <base-mo00615
                  class="ma-0 bg-transparent"
                  :oneway-model-value="localOneway.ketsuronKnjMo00615"
                  style="white-space: pre-line"
                />
              </c-v-col>
              <c-v-col
                cols="11"
                class="border"
              >
                {{ local.kmkList1[0]?.ketsuronKnj }}
              </c-v-col>
            </c-v-row>
            <!-- 残された課題 -->
            <c-v-row
              class="ma-0 px-2"
              name="row-2"
            >
              <c-v-col
                cols="1"
                class="label-item border d-flex align-center"
              >
                <base-mo00615
                  class="ma-0 bg-transparent"
                  :oneway-model-value="localOneway.kadaiKnjMo00615"
                  style="white-space: pre-line"
                />
              </c-v-col>
              <c-v-col
                cols="11"
                class="border"
              >
                {{ local.kmkList1[0]?.kadaiKnj }}
              </c-v-col>
            </c-v-row>
            <!-- 次回開催日 -->
            <c-v-row
              class="ma-0 px-2 mt-1"
              name="row-2"
            >
              <c-v-col
                cols="2"
                class="label-item border d-flex align-center"
              >
                <base-mo00615
                  class="ma-0 bg-transparent"
                  :oneway-model-value="localOneway.jikaiYmdMo00615"
                  style="white-space: pre-line"
                />
              </c-v-col>
              <c-v-col
                cols="3"
                class="border"
              >
                {{ local.kmkList1[0]?.jikaiYmd }}
              </c-v-col>
            </c-v-row>
          </div>
          <!-- 取込項目セクション１ -->
          <c-v-row> </c-v-row>
        </div>
        <!-- 2. 初期表示(親画面.ケアプラン方式 ≠ 5:パッケージプラン  且つ  親画面.会議録書式 = 1:旧の場合)-->
        <div v-show="modeScreen === Or52335Const.DEFAULT.MODE_2">
          <!-- 会議出席者 -->
          <c-v-row>
            <c-v-col class="table-wrapper">
              <base-mo01354
                v-model="local.mo01354KghTucKrkKaigi2List"
                :oneway-model-value="localOneway.mo01354SussekiList"
                hide-default-footer
                hide-no-data
            /></c-v-col>
          </c-v-row>
          <!-- 主な討議内容 -->
          <c-v-row
            class="ma-0 px-2"
            name="row-2"
          >
            <c-v-col
              cols="1"
              class="label-item border d-flex align-center"
            >
              <base-mo00615
                class="ma-0 bg-transparent"
                :oneway-model-value="localOneway.sussekiMo00615"
                style="white-space: pre-line; height: 200px; align-items: start"
              />
            </c-v-col>
            <c-v-col
              cols="11"
              class="border"
            >
              {{ local.kmkList1[0]?.kentoKnj }}
            </c-v-col>
          </c-v-row>
        </div>
        <!-- 3. 初期表示(親画面.ケアプラン方式 = 5：パッケージプラン 且つ 開催リスト.処理行.改訂 = H21の場合)-->
        <div v-show="modeScreen === Or52335Const.DEFAULT.MODE_3">
          <!-- 入所者及び家族の参加 -->
          <c-v-row
            class="ma-0 px-2"
            name="row-2"
          >
            <c-v-col
              class="d-flex align-center"
              style="padding-left: 0px !important"
            >
              <div class="label-item mr-2">
                <base-mo00615
                  class="ma-0 bg-transparent"
                  :oneway-model-value="localOneway.sankaKbnMo00615"
                  style="white-space: pre-line; align-items: start"
                />
              </div>
              {{ t('label.resident') }}
              <base-mo00039
                v-model="sankaKbnRef"
                :oneway-model-value="localOneway.sankaKbnMo00039"
                class="ma-0 mr-2"
              />
              {{ t('label.family') }}
              <div
                class="pa-2"
                style="height: 100%; width: 200px"
              >
                <base-mo01299
                  :oneway-model-value="{
                    visible: false,
                  }"
                  style="height: 100%"
                >
                  <template #content> {{ local.kmkList1[0]?.sankaKnj ?? '' }} </template>
                </base-mo01299>
              </div>
            </c-v-col>
          </c-v-row>
          <!-- 所属一覧セクション３ -->
          <c-v-row class="table-wrapper pl-2">
            <base-mo01354
              v-model="local.mo01354cpnTucSypKaigi2List"
              :oneway-model-value="localOneway.mo01354cpnTucSypKaigi2List"
              hide-default-footer
              hide-no-data
            />
          </c-v-row>
          <!-- 検討内容セクション3 -->
          <div style="height: 300px; overflow-y: scroll">
            <c-v-row>
              <!-- 実施計画に基づくサービス提供その他検討した事項 -->
              <c-v-col
                cols="5"
                class="padding-r-0 padding-b-0"
              >
                <c-v-row
                  no-gutters
                  class="header"
                >
                  <div class="flex-1-1 pa-2">
                    {{ t('label.provision-services-based-on-implementation-plan-other-matters') }}
                  </div>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="border"
                >
                  <div
                    class="flex-1-1 pa-2"
                    style="height: 110px"
                  >
                    {{ local.kmkList2[0]?.kentoKnj }}
                  </div>
                </c-v-row>
              </c-v-col>
              <!-- 検討事項の内容 -->
              <c-v-col
                cols="5"
                class="padding-l-0 padding-b-0"
              >
                <c-v-row
                  no-gutters
                  class="header"
                >
                  <div class="flex-1-1 pa-2">
                    {{ t('label.consider-matter-contents') }}
                  </div>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="border"
                >
                  <div
                    class="flex-1-1 pa-2"
                    style="height: 110px"
                  >
                    {{ local.kmkList2[0]?.naiyoKnj }}
                  </div>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <c-v-row>
              <!-- 会議の結論 -->
              <c-v-col
                cols="6"
                class="padding-r-0 padding-b-0"
              >
                <c-v-row
                  no-gutters
                  class="header"
                >
                  <div class="flex-1-1 pa-2">
                    {{ t('label.meeting-conclusion') }}
                  </div>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="border"
                >
                  <div
                    class="flex-1-1 pa-2"
                    style="height: 110px"
                  >
                    {{ local.kmkList2[0]?.ketsuronKnj }}
                  </div>
                </c-v-row>
              </c-v-col>
              <!-- 残された課題や新たな課題 -->
              <c-v-col
                cols="4"
                class="padding-l-0 padding-b-0"
              >
                <!-- 残された課題や新たな課題 -->
                <c-v-row
                  no-gutters
                  class="header"
                >
                  <div class="flex-1-1 pa-2">
                    {{ t('label.remaining-and-new-challenges') }}
                  </div>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="border"
                >
                  <div
                    class="flex-1-1 pa-2"
                    style="height: 70px"
                  >
                    {{ local.kmkList2[0]?.kadaiKnj }}
                  </div>
                </c-v-row>
                <!-- 次回開催日 -->
                <c-v-row no-gutters>
                  <c-v-col
                    class="header"
                    cols="3"
                  >
                    <div>
                      {{ t('label.next-event-date') }}
                    </div>
                  </c-v-col>
                  <c-v-col
                    class="border"
                    cols="5"
                  >
                    <div>
                      {{ local.kmkList2[0]?.jikaiYmd }}
                    </div>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <!-- 残された課題に対する当面の対応並びに会議結果の情報の共有化 -->
            <c-v-row>
              <c-v-col
                class="padding-b-0"
                cols="10"
              >
                <c-v-row
                  no-gutters
                  class="header"
                >
                  <div class="flex-1-1 pa-2">
                    {{ t('label.immediate-response') }}
                  </div>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <c-v-row>
              <!-- 総合計画方針の変更の必要性 -->
              <div
                class="pl-2 padding-t-0"
                style="width: 350px !important"
              >
                <c-v-row
                  no-gutters
                  class="header"
                >
                  <div class="flex-1-1 pa-2">
                    {{ t('label.comprehensive-plan-policy-modified-necessity') }}
                  </div>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="border"
                >
                  <div
                    class="flex-1-1 pa-2"
                    style="height: 110px"
                  >
                    {{ local.kmkList2[0]?.sogoHenkoKnj }}
                  </div>
                </c-v-row>
              </div>
              <!-- 実施計画方針の変更の必要性 -->
              <div
                class="padding-t-0"
                style="width: 350px !important"
              >
                <c-v-row
                  no-gutters
                  class="header"
                >
                  <div class="flex-1-1 pa-2">
                    {{ t('label.implementation-plan-policy-modified-necessity') }}
                  </div>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="border"
                >
                  <div
                    class="flex-1-1 pa-2"
                    style="height: 110px"
                  >
                    {{ local.kmkList2[0]?.keikakuHenkoKnj }}
                  </div>
                </c-v-row>
              </div>
              <!-- 欠席者への対応 -->
              <div
                class="padding-t-0"
                style="width: 350px !important"
              >
                <c-v-row
                  no-gutters
                  class="header"
                >
                  <div class="flex-1-1 pa-2">
                    {{ t('label.absentee-correspondence') }}
                  </div>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="border"
                >
                  <div
                    class="flex-1-1 pa-2"
                    style="height: 110px"
                  >
                    {{ local.kmkList2[0]?.kessekiTaioKnj }}
                  </div>
                </c-v-row>
              </div>
            </c-v-row>
          </div>
        </div>
        <!-- 4. 初期表示(親画面.ケアプラン方式 = 5:パッケージプラン且つ開催リスト.処理行.改訂 <> H21の場合)-->
        <div v-show="modeScreen === Or52335Const.DEFAULT.MODE_4">
          <!-- 入所者及び家族の参加 -->
          <c-v-row
            class="ma-0 px-2"
            name="row-2"
          >
            <c-v-col
              class="d-flex align-center"
              style="padding-left: 0px !important"
            >
              <div class="label-item mr-2">
                <base-mo00615
                  class="ma-0 bg-transparent"
                  :oneway-model-value="localOneway.sankaKbnMo00615"
                  style="white-space: pre-line; align-items: start"
                />
              </div>
              {{ t('label.resident') }}
              <base-mo00039
                v-model="sankaKbnRef"
                :oneway-model-value="localOneway.sankaKbnMo00039"
                class="ma-0 mr-2"
              />
              {{ t('label.family') }}
              <div
                class="pa-2"
                style="height: 100%; width: 200px"
              >
                <base-mo01299
                  :oneway-model-value="{
                    visible: false,
                  }"
                  style="height: 100%"
                >
                  <template #content> {{ local.kmkList1[0]?.sankaKnj ?? '' }} </template>
                </base-mo01299>
              </div>
            </c-v-col>
          </c-v-row>
          <!-- 所属一覧セクション３ -->
          <c-v-row>
            <c-v-col
              cols="6"
              class="table-wrapper"
            >
              <base-mo01354
                v-model="local.mo01354cpnTucSypKaigi2List"
                :oneway-model-value="localOneway.mo01354cpnTucSypKaigi2List_2"
                hide-default-footer
                hide-no-data
            /></c-v-col>
          </c-v-row>
          <!-- 検討内容セクション3 -->
          <c-v-row>
            <!-- 実施計画に基づくサービス提供その他検討した事項 -->
            <c-v-col
              cols="6"
              class="padding-0"
            >
              <c-v-row
                no-gutters
                class="header"
              >
                <div class="flex-1-1 pa-2">
                  {{ t('label.provision-services-based-on-implementation-plan-other-matters') }}
                </div>
              </c-v-row>
              <c-v-row
                no-gutters
                class="border"
              >
                <div
                  class="flex-1-1 pa-2"
                  style="height: 70px"
                >
                  {{ local.kmkList2[0]?.kentoKnj }}
                </div>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <!-- 検討事項の内容 -->
          <c-v-row>
            <c-v-col
              cols="6"
              class="padding-0"
            >
              <c-v-row
                no-gutters
                class="header"
              >
                <div class="flex-1-1 pa-2">
                  {{ t('label.consider-matter-contents') }}
                </div>
              </c-v-row>
              <c-v-row
                no-gutters
                class="border"
              >
                <div
                  class="flex-1-1 pa-2"
                  style="height: 70px"
                >
                  {{ local.kmkList2[0]?.naiyoKnj }}
                </div>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <!-- 会議の結論 -->
          <c-v-row>
            <c-v-col
              cols="6"
              class="padding-0"
            >
              <c-v-row
                no-gutters
                class="header"
              >
                <div class="flex-1-1 pa-2">
                  {{ t('label.meeting-conclusion') }}
                </div>
              </c-v-row>
              <c-v-row
                no-gutters
                class="border"
              >
                <div
                  class="flex-1-1 pa-2"
                  style="height: 70px"
                >
                  {{ local.kmkList2[0]?.ketsuronKnj }}
                </div>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <!-- 残された課題や新たな課題 -->
          <c-v-row>
            <c-v-col
              cols="6"
              class="padding-0"
            >
              <c-v-row
                no-gutters
                class="header"
              >
                <div class="flex-1-1 pa-2">
                  {{ t('label.remaining-and-new-challenges') }}
                </div>
              </c-v-row>
              <c-v-row
                no-gutters
                class="border"
              >
                <div
                  class="flex-1-1 pa-2"
                  style="height: 70px"
                >
                  {{ local.kmkList2[0]?.kadaiKnj }}
                </div>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <!-- 次回開催日 -->
          <c-v-row>
            <c-v-col
              cols="6"
              class="padding-0"
            >
              <c-v-row no-gutters>
                <c-v-col
                  class="header"
                  cols="3"
                >
                  <div>
                    {{ t('label.next-event-date') }}
                  </div>
                </c-v-col>
                <c-v-col
                  class="border"
                  cols="5"
                >
                  <div>
                    {{ local.kmkList2[0]?.jikaiYmd }}
                  </div>
                </c-v-col>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <!-- 残された課題に対する当面の対応並びに会議結果の情報の共有化 -->
          <c-v-row>
            <c-v-col
              cols="6"
              class="padding-0"
            >
              <c-v-row
                no-gutters
                class="header"
              >
                <div class="flex-1-1 pa-2">
                  {{ t('label.immediate-response') }}
                </div>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <!-- 総合計画方針の変更の必要性 -->
          <c-v-row>
            <c-v-col
              cols="6"
              class="padding-0"
            >
              <c-v-row
                no-gutters
                class="header"
              >
                <div class="flex-1-1 pa-2">
                  {{ t('label.comprehensive-plan-policy-modified-necessity') }}
                </div>
              </c-v-row>
              <c-v-row
                no-gutters
                class="border"
              >
                <div
                  class="flex-1-1 pa-2"
                  style="height: 70px"
                >
                  {{ local.kmkList2[0]?.sogoHenkoKnj }}
                </div>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <!-- 実施計画方針の変更の必要性 -->
          <c-v-row>
            <c-v-col
              cols="6"
              class="padding-0"
            >
              <c-v-row
                no-gutters
                class="header"
              >
                <div class="flex-1-1 pa-2">
                  {{ t('label.implementation-plan-policy-modified-necessity') }}
                </div>
              </c-v-row>
              <c-v-row
                no-gutters
                class="border"
              >
                <div
                  class="flex-1-1 pa-2"
                  style="height: 70px"
                >
                  {{ local.kmkList2[0]?.keikakuHenkoKnj }}
                </div>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <!-- 欠席者への対応-->
          <c-v-row>
            <c-v-col
              cols="6"
              class="padding-0"
            >
              <c-v-row
                no-gutters
                class="header"
              >
                <div class="flex-1-1 pa-2">
                  {{ t('label.absentee-correspondence') }}
                </div>
              </c-v-row>
              <c-v-row
                no-gutters
                class="border"
              >
                <div
                  class="flex-1-1 pa-2"
                  style="height: 70px"
                >
                  {{ local.kmkList2[0]?.kessekiTaioKnj }}
                </div>
              </c-v-row>
            </c-v-col>
          </c-v-row>
        </div>
        <!-- 取込項目 -->
        <c-v-row
          v-show="modeScreen !== Or52335Const.DEFAULT.MODE_2"
          class="checkBoxCus"
        >
          <c-v-col class="padding-0 padding-l-0 padding-r-0">
            <!-- 改行 -->
            <base-mo00018
              v-model="local.lineBreak"
              :oneway-model-value="localOneway.mo00018OneWayLineBreak"
            >
            </base-mo00018>
            <!-- 上書↓ボタン -->
            <base-mo00611
              v-bind="localOneway.mo00611OverWriteOneway"
              class="mx-2"
              @click="overwrite"
              ><c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.overwrite')"
            /></base-mo00611>
            <!-- 追加↓ボタン -->
            <base-mo00611
              v-bind="localOneway.mo00611OnewayAddDown"
              class="mx-2"
              @click="add"
              ><c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.add')"
            /></base-mo00611>
          </c-v-col>
          <!-- 取込項目 -->
          <c-v-col cols="9">
            <c-v-row
              class="ma-0 px-2"
              name="row-2"
            >
              <c-v-col
                cols="2"
                class="label-item border d-flex align-center"
              >
                <base-mo00615
                  class="ma-0 bg-transparent"
                  :oneway-model-value="localOneway.captureFieldsMo00615"
                  style="white-space: pre-line"
                />
              </c-v-col>
              <c-v-col
                cols="10"
                class="border padding-0 padding-l-0 padding-r-0"
                style="height: 80px"
              >
                <c-v-row>
                  <div
                    v-for="item in topCheckbox"
                    :key="item.label"
                  >
                    <base-mo00018
                      v-model="item.modelValue"
                      :oneway-model-value="{
                        ...localOneway.mo00018OneWayLineBreak,
                        checkboxLabel: item.label,
                      }"
                    >
                    </base-mo00018>
                  </div>
                </c-v-row>
                <c-v-row>
                  <div
                    v-for="item in botCheckbox"
                    :key="item.label"
                  >
                    <base-mo00018
                      v-model="item.modelValue"
                      :oneway-model-value="{
                        ...localOneway.mo00018OneWayLineBreak,
                        checkboxLabel: item.label,
                      }"
                    >
                    </base-mo00018>
                  </div>
                </c-v-row>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <div
          v-show="modeScreen === Or52335Const.DEFAULT.MODE_2"
          class="checkBoxCus"
        >
          <!-- 取込項目 -->
          <c-v-row
            class="ma-0 px-2 pt-2"
            name="row-2"
          >
            <c-v-col
              cols="1"
              class="label-item border d-flex align-center"
            >
              <base-mo00615
                class="ma-0 bg-transparent"
                :oneway-model-value="localOneway.captureFieldsMo00615"
                style="white-space: pre-line"
              />
            </c-v-col>
            <c-v-col
              cols="11"
              class="border padding-0 padding-l-0 padding-r-0"
            >
              <c-v-row>
                <div
                  v-for="(item, index) in topCheckbox"
                  :key="item.label"
                  :style="{ paddingLeft: index === topCheckbox.length - 1 ? '70px' : '' }"
                >
                  <base-mo00018
                    v-model="item.modelValue"
                    :oneway-model-value="{
                      ...localOneway.mo00018OneWayLineBreak,
                      checkboxLabel: item.label,
                    }"
                  />
                </div>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <!-- 取込項目 -->
          <c-v-row class="d-flex align-center">
            <!-- 改行 -->
            <base-mo00018
              v-model="local.lineBreak"
              :oneway-model-value="localOneway.mo00018OneWayLineBreak"
            >
            </base-mo00018>
            <!-- 上書↓ボタン -->
            <base-mo00611
              v-bind="localOneway.mo00611OverWriteOneway"
              class="mx-2"
              @click="overwrite"
              ><c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.overwrite')"
            /></base-mo00611>
            <!-- 追加↓ボタン -->
            <base-mo00611
              v-bind="localOneway.mo00611OnewayAddDown"
              class="mx-2"
              @click="add"
              ><c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.add')"
            /></base-mo00611>
          </c-v-row>
        </div>
        <!-- 内容 -->
        <c-v-row>
          <c-v-col
            cols="4"
            class="padding-0"
          >
            <c-v-row
              no-gutters
              class="header d-flex align-center"
            >
              <div class="flex-1-1 pa-2">
                {{ t('label.content') }}
              </div>
              <div class="divider-div">
                <c-v-divider
                  vertical
                  inset
                />
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="onClick"
                />
              </div>
            </c-v-row>
            <c-v-row no-gutters>
              <base-at-textarea
                v-model="local.or52335.modelValue"
                v-bind="{ ...localOneway.mo00045Oneway }"
              >
              </base-at-textarea>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <!-- 予定コンテンツエリア-フッターアクションバー -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00611OneWay.tooltipText"
            :text="localOneway.mo00611OneWay.tooltipText"
            :location="localOneway.mo00611OneWay.tooltipLocation"
            activator="parent"
          />
        </base-mo00611>
        <!-- 保存ボタン -->
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mx-2"
          @click="confirm"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00609OneWay.tooltipText"
            :text="localOneway.mo00609OneWay.tooltipText"
            :location="localOneway.mo00609OneWay.tooltipLocation"
            activator="parent"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="handleConfirm51775"
  />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
</template>
<style lang="scss" scoped>
@use '@/styles/cmn/dialog-data-table-list.scss';

.table-wrapper :deep(.v-table__wrapper .selected-row) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}

.container {
  :deep(.v-row) {
    margin: 0px;
  }
}

.bordered {
  border: 1px rgb(var(--v-theme-form)) solid;
}

.text-red {
  color: rgb(var(--v-theme-error));
}

.min-h-0 {
  min-height: 0;
}

.hidden {
  visibility: hidden;
}

.border {
  border: 1px solid rgb(var(--v-theme-secondaryBackground));
  :deep(.v-col) {
    padding: 0px !important;
  }
}

.label-item {
  background-color: rgb(var(--v-theme-black-100)) !important;
  border-right: 1px solid rgb(var(--v-theme-secondaryBackground));
}

.bg-transparent {
  background-color: transparent !important;
}

.border-bottom {
  border-bottom: 1px solid rgb(var(--v-theme-secondaryBackground));
}

.border-right {
  border-right: 1px solid rgb(var(--v-theme-secondaryBackground));
}
.row-0 {
  padding-bottom: 8px !important;
  // border-bottom: 1px solid rgb(var(--v-theme-secondaryBackground));
  margin-left: -8px !important;
  margin-right: -8px !important;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.padding-0 {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.padding-r-0 {
  padding-right: 0px !important;
}
.padding-l-0 {
  padding-left: 0px !important;
}
.padding-t-0 {
  padding-top: 0px !important;
}
.padding-b-0 {
  padding-bottom: 0px !important;
}

.memo-content {
  height: 150px;
  overflow-y: scroll;
}

.header {
  min-height: 40px;
  background-color: rgb(var(--v-theme-black-100));
  border: 1px rgb(var(--v-theme-black-200)) solid;
  font-weight: bold;
}

:deep .v-col-1 {
  max-width: 5%;
}

:deep .v-col-11 {
  flex: 0 0 95%;
  max-width: 100%;
}
:deep .v-col-2 {
  max-width: fit-content !important;
}
.checkBoxCus {
  :deep .v-col-2 {
    max-width: 9% !important;
  }
  :deep .v-col-10 {
    flex: 0 0 91%;
    max-width: 91%;
  }

  :deep .v-col-1 {
    max-width: 6%;
  }

  :deep .v-col-11 {
    flex: 0 0 94%;
    max-width: 100%;
  }
}
</style>
