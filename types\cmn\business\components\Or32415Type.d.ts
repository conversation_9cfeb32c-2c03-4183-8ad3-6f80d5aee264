import type {
  RirekiInfo,
  InfoCollectionInfoType,
} from '~/types/cmn/business/components/TeX0005Type'

/**
 * Or32415：有機体：［情報収集］画面（9）画面
 * 単方向バインドのデータ構造
 */
export interface Or32415OnewayType {
  /**
   * 期間管理フラグ「0:管理しない 1:管理する」
   */
  periodManageFlag: string
  /**
   * 履歴情報
   */
  rirekiInfo: RirekiInfo

  /**
   * 削除フラグ
   */
  deleteFlg: boolean

  /**
   *copyParentId
   */
  copyParentId?: string
  /**
   *複数flg
   */
  copyFlg?: boolean
  /**
   *ボタン非表示フラグ
   */
  hiddenAction?: boolean
}

/**
 * 双方向バインドModelValue
 */
export interface Or32415Type {
  /**
   * 画面データ
   */
  infoCollectionInfoList: InfoCollectionInfoType[]
}
