import type {
  RirekiInfo,
  InfoCollectionInfoType,
} from '~/types/cmn/business/components/TeX0005Type'

/**
 * Or33109：有機体：アセスメント(インターライ)画面
 * 単方向バインドのデータ構造
 */
export interface Or33109OnewayType {
  /**
   * 期間管理フラグ「0:管理しない 1:管理する」
   */
  periodManageFlag: string
  /**
   * 履歴情報
   */
  rirekiInfo: RirekiInfo

  /**
   * 削除フラグ
   */
  deleteFlg: boolean

  /**
   *copyParentId
   */
  copyParentId?: string
  /**
   *複数flg
   */
  copyFlg?: boolean
  /**
   *ボタン非表示フラグ
   */
  hiddenAction?: boolean
}

/**
 * 双方向バインドModelValue
 */
export interface Or33109Type {
  /**
   * 画面データ
   */
  infoCollectionInfoList: InfoCollectionInfoType[]
}
