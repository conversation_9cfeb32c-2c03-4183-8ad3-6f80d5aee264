<script setup lang="ts">
/**
 * Or29235:有機体:印刷設定モーダル
 * GUI00976_［印刷設定］画面
 *
 * @description
 * ［印刷設定］画面
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { OrX0133Logic } from '../OrX0133/OrX0133.logic'
import type { Or29235StateType, Or29235TwoWayData, selList } from './Or29235.type'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenStore,
  useScreenTwoWayBind,
} from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or29235OnewayType } from '~/types/cmn/business/components/Or29235Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or29235Const } from '~/components/custom-components/organisms/Or29235/Or29235.constants'

import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type {
  PrintSetEntity,
  PrintOptionEntity,
  PrintSubjectHistoryEntity,
  ICpnTucRaiAssReportSelectInEntity,
} from '~/repositories/cmn/entities/CpnTucRaiAssReportSelectEntity'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  printSettingsScreenInfoSelectGUI00976OutEntity,
  printSettingsScreenInfoSelectGUI00976InEntity,
  choPrtList,
} from '~/repositories/cmn/entities/printSettingsScreenInfoSelectGUI00976Entity'
import type {
  LedgerInitializeDataComSelectOutEntity,
  LedgerInitializeDataComSelectInEntity,
} from '~/repositories/cmn/entities/ledgerInitializeDataComSelect'
import type { PrintSettingsInfoComUpdateInEntity } from '~/repositories/cmn/entities/printSettingsInfoComUpdate'
import { CustomClass } from '~/types/CustomClassType'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import { Or10016Logic } from '~/components/custom-components/organisms/Or10016/Or10016.logic'
import { Or10016Const } from '~/components/custom-components/organisms/Or10016/Or10016.constants'
import type { Or10016OnewayType } from '~/types/cmn/business/components/Or10016Type'
import { OrX0135Logic } from '~/components/custom-components/organisms/OrX0135/OrX0135.logic'
import { OrX0135Const } from '~/components/custom-components/organisms/OrX0135/OrX0135.constants'
import type { OrX0135OnewayType } from '~/types/cmn/business/components/OrX0135Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  OrX0133OnewayType,
  OrX0133TableData,
} from '~/types/cmn/business/components/OrX0133Type'
import { OrX0133Const } from '~/components/custom-components/organisms/OrX0133/OrX0133.constants'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import type { InWebEntity } from '@/repositories/AbstructWebRepository'
import { useReportUtils, reportOutputType } from '~/utils/useReportUtils'
import type { Mo01408OnewayType } from '~/types/business/components/Mo01408Type'
import type { Mo00038OnewayType, Mo00038Type } from '~/types/business/components/Mo00038Type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'

const { t } = useI18n()
const { reportOutput } = useReportUtils()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or29235OnewayType
  uniqueCpId: string
  parentCpId: string
}

const props = defineProps<Props>()

// 引継情報を取得する

const or21813 = ref({ uniqueCpId: '' })

const or21814 = ref({ uniqueCpId: '' })

const or21815 = ref({ uniqueCpId: '' })
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

const orX0128 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const or10016 = ref({ uniqueCpId: '' })
const orX0135 = ref({ uniqueCpId: '' })
const orX0117 = ref({ uniqueCpId: '' })
const orX0133 = ref({ uniqueCpId: OrX0133Const.CP_ID(0) })
const orx0145 = ref({ uniqueCpId: '' })

// プロファイル
const choPro = ref('')
//セクション番号
const sectionNo = ref('')
//利用者ID
const userId = ref('')

// 印刷権限
const prtFlg: boolean = await hasPrintAuth()

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [Or10016Const.CP_ID(0)]: or10016.value,
  [OrX0135Const.CP_ID(0)]: orX0135.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

// 帳票イニシャライズデータを取得する
const reportInitData = ref([
  {
    // 氏名伏字印刷
    prtName: '',
    // 文書番号印刷
    prtBng: '',
    // 個人情報印刷
    prtKojin: '',
  },
])

/**
 * 担当ケアマネ
 */
const mo01408OnewayCare = reactive({
  itemLabel: '',
  showItemLabel: false,
  width: '200px',
  items: [],
}) as Mo01408OnewayType

// ダイアログ表示フラグ
const showDialogOr10016 = computed(() => {
  // Or10016のダイアログ開閉状態
  return Or10016Logic.state.get(or10016.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOrx0135 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0135Logic.state.get(orX0135.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOrx0117 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * 変数定義
 **************************************************/
const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(props.uniqueCpId)
})

/**
 * 処理区分
 */
const shoriKbn = ref('')

/**
 * 最小調整行入力
 */
const inputNumberModelValue = ref<Mo00038Type>({
  mo00045: {
    value: '',
  },
})
/**
 * 最小調整行入力
 */
const inputNumberOnewayModelValue = ref<Mo00038OnewayType>({
  showSpinBtn: true,
  isEditCamma: false,
  isEditPeriod: false,
  disalbeSpinBtnDefaultProcessing: false,
  min: 1,
  max: 20,
})

/**
 * 利用者列幅
 */
const userCols = ref<number>(4)

/**
 * 履歴一覧セクションフラゲ
 */
const mo01334TypeHistoryFlag = ref<boolean>(false)
/**
 * 履歴一覧
 */
const orX0133OnewayModel = reactive<OrX0133OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: Or29235Const.DEFAULT.ZERO,
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: Or29235Const.DEFAULT.TANI,
  tableStyle: 'width:335px',
  itemShowFlg: {},
  rirekiList: [] as OrX0133TableData[],
})

/**
 * 利用者選択
 */
const mo00039OneWayUserSelectType = ref<string>('')

/**
 * 履歴選択
 */
const mo00039OneWayHistorySelectType = ref<string>('')
/**
 * 職員基本リスト
 */
const selsList = ref<selList[]>([])

// ローカル双方向bind
const local = reactive({
  mo00040: { modelValue: '' } as Mo00040Type,
  /**
   * 敬称
   */
  textInput: {
    value: '',
  } as Mo00045Type,
  /**
   * 日付印刷区分
   */
  mo00039Type: '',
  /**
   * 印刷日付
   */
  mo00020Type: {
    value: '',
  } as Mo00020Type,
  /**
   * 基準日
   */
  mo00020TypeKijunbi: {
    value: '',
  } as Mo00020Type,
  /**
   * 帳票タイトル
   */
  titleInput: {
    value: '',
  } as Mo00045Type,
  /**
   * 担当ケアマネ
   */
  mo01408modelCare: {
    value: '',
  },
  /**
   * 敬称を変更する
   */
  mo00018TypeChangeTitle: {
    modelValue: false,
  } as Mo00018Type,
  /**
   * 記入用シートを印刷する
   */
  mo00018TypePrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  /**
   * 作成者を印刷するチェック
   */
  mo00018TypePrintAuthor: {
    modelValue: false,
  } as Mo00018Type,
  /**
   * 印刷枠の高さを自動調整するチェック
   */
  mo00018TypePrintMode: {
    modelValue: false,
  } as Mo00018Type,
  /**
   * 承認欄を印刷するチェック
   */
  mo00018TypeAdmitMode: {
    modelValue: false,
  } as Mo00018Type,
})

const localOneway = reactive({
  /**
   * 改訂区分リスト
   */
  mo00039OneWayAssessmentType: {
    name: '',
    showItemLabel: false,
    inline: false,
    disabled: true,
  } as Mo00039OnewayType,
  /**
   * 閉じるボタン
   */
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  /**
   * 印刷日付選択ラジオボタングループ
   */
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 印刷日付ラベル
   */
  mo00020OneWay: {
    showItemLabel: true,
    width: '132px',
  } as Mo00020OnewayType,
  /**
   * 利用者選択ラジオボタングループ
   */
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 履歴選択-ラジオボタングループ
   */
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  Or29235: {
    ...props.onewayModelValue,
  },
  /**
   * タイトル
   */
  mo01338OneWayTitle: {
    value: t('label.title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 敬称左ラベル
   */
  mo01338OneWayLeftTitle: {
    value: t('label.left-bracket'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 敬称右ラベル
   */
  mo01338OneWayRightTitle: {
    value: t('label.right-bracket'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 最小調整行左ラベル
   */
  mo01338OneWayLeftMin: {
    value: t('label.left-bracket') + t('label.min-heigth'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 最小調整行右ラベル
   */
  mo01338OneWayRightMin: {
    value: t('label.min-row') + t('label.right-bracket'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 印刷オプションセクション
   */
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 帳票タイトル
   */
  mo00045OnewayTitleInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
  } as Mo00045OnewayType,
  /**
   * 敬称を変更するチェックボックス
   */
  mo00018OneWayChangeTitle: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 敬称テキストボックス
   */
  mo00045OnewayTextInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '50',
    maxLength: '2',
  } as Mo00045OnewayType,
  /**
   * 承認欄の登録
   */
  mo00611OneWay: {
    btnLabel: t('btn.confirm-form-registration‌'),
    disabled: false,
  } as Mo00611OnewayType,
  /**
   * 作成者を印刷するチェックボックス
   */
  mo00018OneWayPrintAuthor: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-author'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 承認欄を印刷する
   */
  mo00018OneWayAdmitMode: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.confirm-form-copying'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 印刷枠の高さを自動調整する
   */
  mo00018OneWayPrintMode: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.confirm-form-copying'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 記入用シートを印刷するチェック
   */
  mo00018OneWayPrintTheForm: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 利用者選択ラベル
   */
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 担当ケアマネラベル
   */
  mo01338OneWayCareManagerInCharge: {
    value: t('label.care-manager-in-charge'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 基準日ラベル
   */
  mo01338OneWayBaseDate: {
    value: t('label.base-date'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 基準日
   */
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 履歴選択ラベル
   */
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 印刷設定帳票出力状態リスト
   */
  orX0117Oneway: {
    reportId: '',
    type: '1',
    reportData: {} as InWebEntity,
    outputType: reportOutputType.DOWNLOAD,
    historyList: [] as OrX0117History[],
    replaceKey: 'printHistoryList',
  } as OrX0117OnewayType,
  /**
   * 「印刷設定」ダイアログ
   */
  mo00024Oneway: {
    width: 'auto',
    maxWidth: '1420px',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: false,
    mo01344Oneway: {
      toolbarTitle: t('label.print-set'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  },
  /**
   * 印鑑欄
   */
  mo00610OneWayCopy: {
    btnLabel: t('btn.seal-column'),
    disabled: false,
  } as Mo00610OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-0' }),
  } as OrX0145OnewayType,
})

/**
 * 担当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or29235Const.DEFAULT.IS_OPEN,
})

/**
 * 利用者
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width: 309px',
  focusSettingInitial: localOneway.Or29235.focusSettingInitial,
})

/**************************************************
 * Pinia
 **************************************************/

const { setState } = useScreenOneWayBind<Or29235StateType>({
  cpId: Or29235Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or29235Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

const or10016Data: Or10016OnewayType = {
  /**
   * 法人ID
   */
  houjinId: '',
  /**
   * 施設ID
   */
  shisetsuId: '',
  /**
   * 職員ID
   */
  shokuinId: '',
  /**
   * システムコード
   */
  systemCode: '',
  /**
   * 事業所ID
   */
  jigyoshoId: '',
  /**
   * ログイン番号
   */
  loginNumber: '',
  /**
   * ログインユーザタイプ
   */
  loginUserType: '',
  /**
   * 電子カルテ連携フラグ
   */
  emrLinkFlag: '',
  /**
   * 帳票セクション番号
   */
  reportSectionNumber: '',
  /**
   * 引続情報.アセスメント
   */
  assessment: '',
  /**
   * 引続情報.会議禄フラグ
   */
  conferenceFlag: true,
}
const orX0135Data: OrX0135OnewayType = {
  // ★ｻｰﾋﾞｽ事業者ID
  svJigyoId: '',
  // 法人ID
  houjinId: '',
  // 施設ID
  shisetuId: '',
  chohyoCd:'',
  // 印刷セクション番号
  // printSectionNo: '',
  // 承認フラグ
  // shoninFlg: '',
}
/**
 * 出力帳票名一覧
 */
const mo01334OnewayReport = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'defPrtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})
/**
 * 出力帳票名一覧
 */
const mo01334TypeReport = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

const { refValue } = useScreenTwoWayBind<Or29235TwoWayData>({
  cpId: Or29235Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
onMounted(async () => {
  orX0133OnewayModel.itemShowFlg = {
    createYmdShokuKnjFlg: true,
    caseNoFlg: true,
    kaiteiKnjFlg: true,
  }
  orX0133OnewayModel.kikanFlg = localOneway.Or29235.kikanFlg
  await initCodes()
  await getPrintSettingList(userId.value)
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_REVISION_CATEGORY_PRINT_SETTING },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  //日付印刷区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  //単複数選択区分
  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  localOneway.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  //改訂区分リスト
  localOneway.mo00039OneWayAssessmentType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_REVISION_CATEGORY_PRINT_SETTING
  )

  // 初期値
  mo00039OneWayUserSelectType.value = Or29235Const.DEFAULT.TANI
  mo00039OneWayHistorySelectType.value = Or29235Const.DEFAULT.TANI
  local.mo00020TypeKijunbi.value = localOneway.Or29235.processDate
  shoriKbn.value = Or29235Const.DEFAULT.ONE
  if (localOneway.Or29235.userList.length > 0) {
    userId.value =
      localOneway.Or29235.userList?.find((item) => item.userId === localOneway.Or29235.userId)
        ?.userId ?? localOneway.Or29235.userList[0].userId
  }
}

/**
 * 印刷設定画面初期情報を取得する
 *
 * @param userId - 利用者ID
 */
async function getPrintSettingList(userId: string) {
  const inputData: printSettingsScreenInfoSelectGUI00976InEntity = {
    shoriKbn: shoriKbn.value,
    sysCd: localOneway.Or29235.sysCd,
    sysRyaku: localOneway.Or29235.sysRyaku,
    kinounameKnj: Or29235Const.DEFAULT.KINOU_NAMEKNJ,
    houjinId: localOneway.Or29235.houjinId,
    shisetuId: localOneway.Or29235.shisetuId,
    svJigyoId: localOneway.Or29235.svJigyoId,
    shokuId: systemCommonsStore.getStaffId!,
    sectionName: localOneway.Or29235.sectionName,
    choIndex: localOneway.Or29235.choIndex,
    kojinhogoFlg: Or29235Const.DEFAULT.ZERO,
    sectionAddNo: Or29235Const.DEFAULT.ZERO,
    userId: userId,
    kikanFlg: localOneway.Or29235.kikanFlg,
  }

  // バックエンドAPIから初期情報取得
  const ret: printSettingsScreenInfoSelectGUI00976OutEntity = await ScreenRepository.select(
    'printSettingsScreenInfoSelectGUI00976',
    inputData
  )
  orX0133OnewayModel.rirekiList = []
  mo01408OnewayCare.items = []
  if (ret?.data) {
    // 担当ケアマネ
    orX0145Type.value.value = {
      counter: Or29235Const.DEFAULT.EMPTY,
      chkShokuId: Or29235Const.DEFAULT.EMPTY,
      houjinId: Or29235Const.DEFAULT.EMPTY,
      shisetuId: Or29235Const.DEFAULT.EMPTY,
      svJigyoId: Or29235Const.DEFAULT.EMPTY,
      shokuin1Kana: Or29235Const.DEFAULT.EMPTY,
      shokuin2Kana: Or29235Const.DEFAULT.EMPTY,
      shokuin1Knj: Or29235Const.DEFAULT.EMPTY,
      shokuin2Knj: Or29235Const.DEFAULT.EMPTY,
      sex: Or29235Const.DEFAULT.EMPTY,
      birthdayYmd: Or29235Const.DEFAULT.EMPTY,
      zip: Or29235Const.DEFAULT.EMPTY,
      kencode: Or29235Const.DEFAULT.EMPTY,
      citycode: Or29235Const.DEFAULT.EMPTY,
      areacode: Or29235Const.DEFAULT.EMPTY,
      addressKnj: Or29235Const.DEFAULT.EMPTY,
      tel: Or29235Const.DEFAULT.EMPTY,
      kaikeiId: Or29235Const.DEFAULT.EMPTY,
      kyuyoKbn: Or29235Const.DEFAULT.EMPTY,
      partKbn: Or29235Const.DEFAULT.EMPTY,
      inYmd: Or29235Const.DEFAULT.EMPTY,
      outYmd: Or29235Const.DEFAULT.EMPTY,
      shozokuId: Or29235Const.DEFAULT.EMPTY,
      shokushuId: Or29235Const.DEFAULT.EMPTY,
      shokuId: Or29235Const.DEFAULT.EMPTY,
      timeStmp: Or29235Const.DEFAULT.EMPTY,
      delFlg: Or29235Const.DEFAULT.EMPTY,
      shokuNumber: Or29235Const.DEFAULT.EMPTY,
      caremanagerKbn: Or29235Const.DEFAULT.EMPTY,
      shokuType1: Or29235Const.DEFAULT.EMPTY,
      shokuType2: Or29235Const.DEFAULT.EMPTY,
      kGroupid: Or29235Const.DEFAULT.EMPTY,
      bmpPath: Or29235Const.DEFAULT.EMPTY,
      bmpYmd: Or29235Const.DEFAULT.EMPTY,
      hankoPath: Or29235Const.DEFAULT.EMPTY,
      kojinPath: Or29235Const.DEFAULT.EMPTY,
      keitaitel: Or29235Const.DEFAULT.EMPTY,
      eMail: Or29235Const.DEFAULT.EMPTY,
      senmonNo: Or29235Const.DEFAULT.EMPTY,
      sgfFlg: Or29235Const.DEFAULT.EMPTY,
      srvSekiKbn: Or29235Const.DEFAULT.EMPTY,
      shokushuId2: Or29235Const.DEFAULT.EMPTY,
      shokushuId3: Or29235Const.DEFAULT.EMPTY,
      shokushuId4: Or29235Const.DEFAULT.EMPTY,
      shokushuId5: Or29235Const.DEFAULT.EMPTY,
      shikakuId1: Or29235Const.DEFAULT.EMPTY,
      shikakuId2: Or29235Const.DEFAULT.EMPTY,
      shikakuId3: Or29235Const.DEFAULT.EMPTY,
      shikakuId4: Or29235Const.DEFAULT.EMPTY,
      shikakuId5: Or29235Const.DEFAULT.EMPTY,
      kyuseiFlg: Or29235Const.DEFAULT.EMPTY,
      kyuseiKana: Or29235Const.DEFAULT.EMPTY,
      kyuseiKnj: Or29235Const.DEFAULT.EMPTY,
      sort: Or29235Const.DEFAULT.EMPTY,
      selfNumber: Or29235Const.DEFAULT.EMPTY,
      ichiranShokushuIdNm: Or29235Const.DEFAULT.EMPTY,
      shokushuId2Nm: Or29235Const.DEFAULT.EMPTY,
      shokushuId3Nm: Or29235Const.DEFAULT.EMPTY,
      shokushuId4Nm: Or29235Const.DEFAULT.EMPTY,
      shokushuId5Nm: Or29235Const.DEFAULT.EMPTY,
      stopFlg: Or29235Const.DEFAULT.EMPTY,
      shokuinKnj: '',
      shokuinKana: Or29235Const.DEFAULT.EMPTY,
      title: Or29235Const.DEFAULT.EMPTY,
      value: Or29235Const.DEFAULT.EMPTY,
    } as TantoCmnShokuin

    //職員基本リスト
    selsList.value = ret.data.selList ?? []
    // 担当ケアマネ
    selsList.value.forEach((item) => {
      mo01408OnewayCare.items?.push({ title: item.shokuinKnj, value: item.chkShokuId })
    })
    //印刷日付
    local.mo00020Type.value = ret.data.sysYmd
    //出力帳票印刷情報リスト
    const mo01334OnewayList: Mo01334Items[] = []
    for (const item of ret.data.choPrtList) {
      if (item) {
        mo01334OnewayList.push({
          id: item.prtNo,
          mo01337OnewayReport: {
            value: item.defPrtTitle,
            unit: '',
          } as Mo01337OnewayType,
          ...item,
        } as Mo01334Items)
      }
    }
    mo01334OnewayReport.value.items = mo01334OnewayList

    if (ret.data.choPrtList.length > 0) mo01334TypeReport.value.value = ret.data.choPrtList[0].prtNo

    refValue.value = { choPrtList: ret.data.choPrtList }

    useScreenStore().setCpTwoWay({
      cpId: Or29235Const.CP_ID(0),
      uniqueCpId: props.uniqueCpId,
      value: refValue.value,
      isInit: true,
    })
    //総合計画印刷設定履歴リスト
    if (ret.data.rirekiList.length > 0) {
      for (const item of ret.data.rirekiList) {
        if (localOneway.Or29235.kikanFlg === Or29235Const.DEFAULT.ONE) {
          const tmpItem = {
            planPeriod:
              t('label.plan-period') +
              t('label.colon-mark') +
              item.startYmd +
              t('label.wavy') +
              item.endYmd,
          } as OrX0133TableData
          orX0133OnewayModel.rirekiList.push(tmpItem)
        }
        if (item.historyList.length > 0) {
          for (const e of item.historyList) {
            const tmpItem1 = {
              planPeriod: '',
              createYmd: e.createYmd,
              kijunbiYmd: '',
              shokuKnj: e.shokuId,
              shokuinKnj: '',
              caseNo: e.caseNo,
              tougaiYm: '',
              kaisuu: '',
              kaiteiKnj: localOneway.mo00039OneWayAssessmentType.items?.find(
                (item) => item.value === e.kaiteiFlg
              )?.label,
              youshikiKnj: '',
              gdlId: '',
              assType: '',
              assDateYmd: '',
            } as OrX0133TableData
            orX0133OnewayModel.rirekiList.push(tmpItem1)
          }
        }
      }
    }

    reportInitData.value = ret.data.iniDataList
  }
}

/**
 * 帳票選択切替
 */
watch(
  () => mo01334TypeReport.value.value,
  async (newValue, oldValue) => {
    if (oldValue) {
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (oldValue === item.id) {
            // 画面.帳票タイトルが空白以外の場合
            // 画面.出力帳票一覧明細に切替前選択される行.帳票タイトル = 画面.帳票タイトル
            if (local.titleInput.value) item.prtTitle = local.titleInput.value
            // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
            item.prndate = local.mo00039Type
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ03 = 画面.敬称を変更する
            item.param03 = local.mo00018TypeChangeTitle.modelValue
              ? Or29235Const.DEFAULT.ONE
              : Or29235Const.DEFAULT.ZERO
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ04 = 画面.敬称
            item.param04 = local.textInput.value
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ05 = 画面.承認欄を印刷するチェックボックス
            item.param05 = local.mo00018TypeAdmitMode.modelValue
              ? Or29235Const.DEFAULT.ONE
              : Or29235Const.DEFAULT.ZERO
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ06 = 画面.印刷枠の高さを自動調整するチェックボックス
            item.param06 = local.mo00018TypePrintMode.modelValue
              ? Or29235Const.DEFAULT.ONE
              : Or29235Const.DEFAULT.ZERO
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ07 = 画面.最小調整行入力
            item.param07 = inputNumberModelValue.value.mo00045.value
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ08 = 画面.作成者を印刷するチェックボックス
            item.param08 = local.mo00018TypePrintAuthor.modelValue
              ? Or29235Const.DEFAULT.ONE
              : Or29235Const.DEFAULT.ZERO
          }
        }
      }
    }
    for (const item of mo01334OnewayReport.value.items) {
      if (item) {
        if (newValue === item.id) {
          // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
          local.titleInput.value = item?.prtTitle as string
          // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
          local.mo00039Type = item?.prndate as string
          // 画面.敬称を変更する = 画面.出力帳票一覧明細に選択される行.パラメータ03
          local.mo00018TypeChangeTitle.modelValue =
            item?.param03 === Or29235Const.DEFAULT.ONE ? true : false
          // 画面.敬称 = 画面.出力帳票一覧明細に選択される行.パラメータ04
          local.textInput.value = item?.param04 as string
          // 画面.承認欄を印刷するチェックボックス = 画面.出力帳票一覧明細に選択される行.パラメータ05
          local.mo00018TypeAdmitMode.modelValue =
            item?.param05 === Or29235Const.DEFAULT.ONE ? true : false
          // 画面.画面.印刷枠の高さを自動調整するチェックボックス = 画面.出力帳票一覧明細に選択される行.パラメータ06
          local.mo00018TypePrintMode.modelValue =
            item?.param06 === Or29235Const.DEFAULT.ONE ? true : false
          // 画面.最小調整行入力 = 画面.出力帳票一覧明細に選択される行.パラメータ07
          inputNumberModelValue.value.mo00045.value = item?.param07 as string
          // 画面.作成者を印刷するチェックボックス = 画面.出力帳票一覧明細に選択される行.パラメータ08
          local.mo00018TypePrintAuthor.modelValue =
            item?.param05 === Or29235Const.DEFAULT.ONE ? true : false
          //画面.選択された帳票のプロファイル
          choPro.value = item?.choPro as string
          //選択された帳票.セクション番号
          sectionNo.value = item?.sectionNo as string

          await getReportInfoDataList()
        }
      }
    }
  }
)

/**
 * 帳票イニシャライズデータを取得する。
 */
async function getReportInfoDataList() {
  const inputData: LedgerInitializeDataComSelectInEntity = {
    sysCd: localOneway.Or29235.sysCd,
    kinounameKnj: Or29235Const.DEFAULT.KINOU_NAMEKNJ,
    shokuId: localOneway.Or29235.shokuId,
    sectionKnj: choPro.value,
    kojinhogoFlg: Or29235Const.DEFAULT.ZERO,
    sectionAddNo: Or29235Const.DEFAULT.ZERO,
  }
  // バックエンドAPIから初期情報取得
  const ret: LedgerInitializeDataComSelectOutEntity = await ScreenRepository.select(
    'ledgerInitializeDataComSelect',
    inputData
  )
  reportInitData.value = ret.data.iniDataObject
}

/**
 * 変更データを取得する
 *
 * @returns 出力帳票印刷情報リスト
 */
async function getDataTable() {
  const choPrtList = mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => {
    if (id === mo01334TypeReport.value.value) {
      return {
        ...rest,
        prtTitle: local.titleInput.value,
        prndate: local.mo00039Type,
        param03: local.mo00018TypeChangeTitle.modelValue
          ? Or29235Const.DEFAULT.ONE
          : Or29235Const.DEFAULT.ZERO,
        param04: local.textInput.value,
        param05: local.mo00018TypeAdmitMode.modelValue
          ? Or29235Const.DEFAULT.ONE
          : Or29235Const.DEFAULT.ZERO,
        param06: local.mo00018TypePrintMode.modelValue
          ? Or29235Const.DEFAULT.ONE
          : Or29235Const.DEFAULT.ZERO,
        param07: inputNumberModelValue.value.mo00045.value,
        param08: local.mo00018TypePrintAuthor.modelValue
          ? Or29235Const.DEFAULT.ONE
          : Or29235Const.DEFAULT.ZERO,
      }
    }
    return {
      ...rest,
    }
  }) as choPrtList[]

  refValue.value = { choPrtList: choPrtList }

  await nextTick()

  return choPrtList
}

/**
 * 帳票タイトルが入力していない場合
 */
async function checkTitleInput() {
  if (local.titleInput.value) return
  const dialogResult = await showOr21815MsgBtn(t('message.w-cmn-20845'))
  switch (dialogResult) {
    case 'ok': {
      let label = ''
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (mo01334TypeReport.value.value === item.id && item.mo01337OnewayReport) {
            const data = item.mo01337OnewayReport as Mo01337OnewayType
            label = data.value
          }
        }
      }
      local.titleInput.value = label
      break
    }
  }
  return
}
/**
 * 確認ダイアログの開閉
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21814MsgBtn(paramDialogText: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
/**
 * エラーダイアログの開閉
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21813MsgBtn(paramDialogText: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログの開閉
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（ok）
 */
async function showOr21815MsgBtn(paramDialogText: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = 'ok'

        if (event?.firstBtnClickFlg) {
          result = 'ok'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 「利用者単複数選択」ボタン押下 単一複数
 */
watch(
  () => mo00039OneWayUserSelectType.value,
  (newValue) => {
    // 利用者選択方法が「単一」の場合
    if (newValue === OrX0128Const.DEFAULT.TANI) {
      //利用者「単一」
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width: 309px'
      //記入用シートを印刷するを活性表示にする
      localOneway.mo00018OneWayPrintTheForm.disabled = false
      userCols.value = 6
      //履歴一覧を活性
      mo01334TypeHistoryFlag.value = true
      localOneway.orX0117Oneway.type = Or29235Const.DEFAULT.TANI
      //変数.取得処理区分 = 2
      shoriKbn.value = Or29235Const.DEFAULT.TWO
    } else {
      //利用者「複数」
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width: 430px'
      //記入用シートを印刷するを非活性表示にする
      local.mo00018TypePrintTheForm.modelValue = false
      localOneway.mo00018OneWayPrintTheForm.disabled = true
      userCols.value = 11
      //履歴一覧を活性
      mo01334TypeHistoryFlag.value = false
      localOneway.orX0117Oneway.type = Or29235Const.DEFAULT.HUKUSUU
    }
  }
)

/**
 * 「履歴単複数選択」ボタン押下 単一複数
 */
watch(
  () => mo00039OneWayHistorySelectType.value,
  (newValue) => {
    // 履歴選択方法が「単一」の場合
    if (newValue === OrX0128Const.DEFAULT.TANI) {
      orX0133OnewayModel.singleFlg = OrX0133Const.DEFAULT.TANI
      localOneway.orX0117Oneway.type = Or29235Const.DEFAULT.ONE
      //画面.記入用シートを印刷するを活性表示にする
      localOneway.mo00018OneWayPrintTheForm.disabled = false
    } else {
      orX0133OnewayModel.singleFlg = OrX0133Const.DEFAULT.HUKUSUU
      localOneway.orX0117Oneway.type = Or29235Const.DEFAULT.ZERO
      //画面.記入用シートを印刷するを非活性表示にする
      local.mo00018TypePrintTheForm.modelValue = false
      localOneway.mo00018OneWayPrintTheForm.disabled = true
    }
  }
)

/**
 * ラジオボタンの選択状態を追跡する
 */
watch(
  () => local.mo00039Type,
  async () => {
    await checkTitleInput()
  }
)

/**
 * 「担当ケアマネ」の監視
 */
watch(
  () => local.mo01408modelCare.value,
  async () => {}
)

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickFlg) {
      // 利用者選択
      // 利用者選択方法が「単一」の場合
      if (mo00039OneWayUserSelectType.value === Or29235Const.DEFAULT.TANI) {
        userId.value = newValue.userList[0].userId
        await getPrintSettingList(userId.value)
      }
    }
  }
)

/**
 * 「印鑑欄」ボタン押下
 */
function open() {
  or10016Data.houjinId = localOneway.Or29235.houjinId
  or10016Data.shisetsuId = localOneway.Or29235.shisetuId
  or10016Data.shokuinId = localOneway.Or29235.shokuId
  or10016Data.systemCode = localOneway.Or29235.sysCd
  or10016Data.jigyoshoId = localOneway.Or29235.svJigyoId
  or10016Data.loginNumber = localOneway.Or29235.loginNumber
  or10016Data.loginUserType = localOneway.Or29235.loginUserType
  or10016Data.emrLinkFlag = localOneway.Or29235.emrLinkFlag
  // Or10016のダイアログ開閉状態を更新する
  Or10016Logic.state.set({
    uniqueCpId: or10016.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「承認欄の登録」ボタン押下
 */
function openAdmit() {
  orX0135Data.svJigyoId = localOneway.Or29235.svJigyoId
  // OrX0135のダイアログ開閉状態を更新する
  OrX0135Logic.state.set({
    uniqueCpId: orX0135.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「閉じるボタン」押下
 */
async function close() {
  await checkTitleInput()
  //利用者選択が「単一」 」且つ利用者一覧が0件選択の場合
  const OrX0130Event = OrX0130Logic.event.get(orX0130.value.uniqueCpId)
  if (
    mo00039OneWayUserSelectType.value === Or29235Const.DEFAULT.TANI &&
    OrX0130Event?.userList.length === 0
  ) {
    await showOr21814MsgBtn(t('message.i-cmn-11393'))
    return
  }
  //利用者選択が「単一」  且つ履歴一覧が0件選択の場合
  const OrX0133Event = OrX0133Logic.event.get(orX0133.value.uniqueCpId)
  if (
    mo00039OneWayUserSelectType.value === Or29235Const.DEFAULT.TANI &&
    OrX0133Event?.orX0133DetList.length === 0
  ) {
    await showOr21814MsgBtn(t('message.i-cmn-11455'))
    return
  }
  //利用者選択が「複数」 」且つ利用者一覧が0件選択の場合
  if (
    mo00039OneWayUserSelectType.value === Or29235Const.DEFAULT.HUKUSUU &&
    OrX0130Event?.userList.length === 0
  ) {
    await showOr21814MsgBtn(t('message.i-cmn-11393'))
    return
  }
  const choPrtList = await getDataTable()
  if (isEdit.value) await savePrintSettingInfo(choPrtList)
  setState({ isOpen: false })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
async function pdfDownload() {
  await checkTitleInput()
  //画面.選択された帳票のプロファイルが""の場合
  if (!choPro.value) {
    await showOr21813MsgBtn(t('message.e-cmn-40172'))
    return
  }
  //画面.選択された帳票のプロファイルが "3GKU0P131P005"の場合
  if (choPro.value === Or29235Const.DEFAULT.CHOPRO) {
    await showOr21813MsgBtn(t('message.e-cmn-41000'))
    return
  }
  //利用者選択が「単一」 」且つ利用者一覧が0件選択の場合
  const OrX0130Event = OrX0130Logic.event.get(orX0130.value.uniqueCpId)
  if (
    mo00039OneWayUserSelectType.value === Or29235Const.DEFAULT.TANI &&
    OrX0130Event?.userList.length === 0
  ) {
    await showOr21814MsgBtn(t('message.i-cmn-11393'))
    return
  }
  //利用者選択が「単一」  且つ履歴一覧が0件選択の場合
  const OrX0133Event = OrX0133Logic.event.get(orX0133.value.uniqueCpId)
  if (
    mo00039OneWayUserSelectType.value === Or29235Const.DEFAULT.TANI &&
    OrX0133Event?.orX0133DetList.length === 0
  ) {
    await showOr21814MsgBtn(t('message.i-cmn-11455'))
    return
  }
  //利用者選択が「複数」 」且つ利用者一覧が0件選択の場合
  if (
    mo00039OneWayUserSelectType.value === Or29235Const.DEFAULT.HUKUSUU &&
    OrX0130Event?.userList.length === 0
  ) {
    await showOr21814MsgBtn(t('message.i-cmn-11393'))
    return
  }

  //印刷設定情報保存
  const choPrtList = await getDataTable()

  if (isEdit.value) await savePrintSettingInfo(choPrtList)
  //利用者選択が「単一」
  if (mo00039OneWayUserSelectType.value === Or29235Const.DEFAULT.TANI) {
    //履歴選択方法が「単一」
    if (mo00039OneWayHistorySelectType.value === Or29235Const.DEFAULT.TANI) {
      if (
        OrX0133Event?.orX0133DetList.length !== undefined &&
        OrX0133Event?.orX0133DetList.length === 0
      ) {
        // 帳票側の処理を呼び出し、帳票レイアウトのみ印刷する
        const inputData: ICpnTucRaiAssReportSelectInEntity = {
          svJigyoKnj: localOneway.Or29235.svJigyoKnj,
          syscd: localOneway.Or29235.sysCd,
          printSet: {
            shiTeiKubun: local.mo00039Type,
            shiTeiDate: local.mo00020Type.value ? local.mo00020Type.value.split('/').join('-') : '',
          } as PrintSetEntity,
          printOption: {
            emptyFlg: String(local.mo00018TypePrintTheForm.modelValue),
            kinyuAssType: '',
            colorFlg: '',
          } as PrintOptionEntity,
          printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
        } as ICpnTucRaiAssReportSelectInEntity
        // 帳票出力
        await reportOutput(localOneway.Or29235.reportId, inputData, reportOutputType.DOWNLOAD)
        return
      }
    }
  }

  // OrX0117のダイアログ開閉状態を更新する
  OrX0117Logic.state.set({
    uniqueCpId: orX0117.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 印刷設定情報を保存する
 *
 * @param choPrtList - 出力帳票印刷情報リスト
 */
async function savePrintSettingInfo(choPrtList: choPrtList[]) {
  const inputData: PrintSettingsInfoComUpdateInEntity = {
    sysCd: localOneway.Or29235.sysCd,
    sysRyaku: localOneway.Or29235.sysRyaku,
    kinounameKnj: Or29235Const.DEFAULT.KINOU_NAMEKNJ,
    houjinId: localOneway.Or29235.houjinId,
    shisetuId: localOneway.Or29235.shisetuId,
    svJigyoId: localOneway.Or29235.svJigyoId,
    shokuId: localOneway.Or29235.shokuId,
    choPro: choPro.value,
    kojinhogoFlg: Or29235Const.DEFAULT.ZERO,
    sectionAddNo: Or29235Const.DEFAULT.ZERO,
    iniDataObject: { ...reportInitData.value },
    choPrtList: choPrtList,
    sectionName: '',
  }

  // バックエンドAPIから初期情報取得
  await ScreenRepository.update('printSettingsInfoComUpdate', inputData)
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or29235_screen"
      >
        <c-v-col
          cols="12"
          sm="2"
          class="pa-0 pt-2 px-2 or29235_border_right"
        >
          <!-- 帳票 -->
          <base-mo-01334
            v-model="mo01334TypeReport"
            :oneway-model-value="mo01334OnewayReport"
            class="list-wrapper"
          >
            <!-- 出力帳票名  -->
            <template #[`item.defPrtTitle`]="{ item }">
              <base-mo01337 :oneway-model-value="item.mo01337OnewayReport" />
            </template>
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="4"
          class="pa-0 pt-2 or29235_border_right content_center"
        >
          <c-v-row style="margin-left: 8px; margin-top: 8px; margin-bottom: 8px">
            <!--印鑑欄-->
            <base-mo00610
              class="mr-2"
              :oneway-model-value="localOneway.mo00610OneWayCopy"
              @click="open()"
            />
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="or29235_row flex-center"
          >
            <c-v-col
              cols="12"
              sm="3"
              class="pa-2"
            >
              <!-- タイトル  -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <base-mo00045
                v-model="local.titleInput"
                :oneway-model-value="localOneway.mo00045OnewayTitleInput"
              />
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or29235_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              class="pa-2"
            >
              <!-- 印刷日付選択ラジオボタングループ -->
              <base-mo00039
                v-model="local.mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              id="test"
              cols="12"
              sm="5"
              class="pa-2"
            >
              <!-- 印刷日付ラベル -->
              <base-mo00020
                v-show="local.mo00039Type == Or29235Const.DEFAULT.TWO"
                v-model="local.mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="printerOption customCol or29235_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <!-- 印刷オプションセクション -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or29235_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2 d-flex"
              style="padding: 0 12px 0 0; align-items: center"
            >
              <!-- 敬称を変更するチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypeChangeTitle"
                class="label-blue"
                :oneway-model-value="localOneway.mo00018OneWayChangeTitle"
              >
              </base-mo00018>
              <!-- 敬称左ラベル  -->
              <base-mo01338
                class="item-color"
                :oneway-model-value="localOneway.mo01338OneWayLeftTitle"
              ></base-mo01338>
              <!-- 敬称テキストボックス -->
              <base-mo00045
                v-model="local.textInput"
                :oneway-model-value="localOneway.mo00045OnewayTextInput"
                :disabled="!local.mo00018TypeChangeTitle.modelValue"
              />
              <!-- 敬称右ラベル  -->
              <base-mo01338
                class="item-color"
                :oneway-model-value="localOneway.mo01338OneWayRightTitle"
              ></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <!-- 記入用シートを印刷するチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypePrintTheForm"
                class="label-blue"
                :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2 d-flex"
            >
              <!-- 承認欄を印刷する -->
              <base-mo00018
                v-model="local.mo00018TypeAdmitMode"
                class="label-blue"
                :oneway-model-value="localOneway.mo00018OneWayAdmitMode"
              >
              </base-mo00018>
              <base-mo00611
                class="mr-2"
                :oneway-model-value="localOneway.mo00611OneWay"
                @click="openAdmit()"
              />
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <!-- 作成者を印刷するチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypePrintAuthor"
                class="label-blue"
                :oneway-model-value="localOneway.mo00018OneWayPrintAuthor"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2 d-flex"
              style="padding: 0 12px 0 0; align-items: center"
            >
              <!-- 印刷枠の高さを自動調整する -->
              <base-mo00018
                v-model="local.mo00018TypePrintMode"
                class="label-blue"
                :oneway-model-value="localOneway.mo00018OneWayPrintMode"
              >
              </base-mo00018>
              <!-- 最小調整行左ラベル  -->
              <base-mo01338
                class="item-color"
                :oneway-model-value="localOneway.mo01338OneWayLeftMin"
              ></base-mo01338>
              <!-- 最小調整行入力 -->
              <base-mo00038
                v-model="inputNumberModelValue"
                :oneway-model-value="inputNumberOnewayModelValue"
                :disabled="!local.mo00018TypePrintMode.modelValue"
                style="width: 60px"
                class="textSize rowHeight"
              ></base-mo00038>
              <!-- 最小調整行右ラベル  -->
              <base-mo01338
                class="item-color"
                :oneway-model-value="localOneway.mo01338OneWayRightMin"
              ></base-mo01338>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          class="pa-2"
        >
          <c-v-row
            class="or29235_row"
            no-gutter
            style="align-items: center"
          >
            <c-v-col
              cols="12"
              sm="6"
              class="pa-2 col_height col-paddding"
            >
              <c-v-row
                class="or29235_row"
                style="padding-bottom: 8px"
              >
                <!-- 利用者選択ラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                  style="background-color: transparent"
                >
                </base-mo01338
              ></c-v-row>
              <c-v-row class="or29235_row">
                <!-- 利用者選択ラジオボタングループ -->
                <base-mo00039
                  v-model="mo00039OneWayUserSelectType"
                  :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                >
                </base-mo00039>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="mo00039OneWayUserSelectType === Or29235Const.DEFAULT.HUKUSUU"
              cols="12"
              sm="6"
              class="pa-2 col_height"
            >
              <c-v-row class="or29235_row">
                <!-- 基準日  -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayBaseDate"
                  style="background-color: transparent"
                >
                </base-mo01338>
              </c-v-row>
              <c-v-row class="or29235_row">
                <base-mo00020
                  v-model="local.mo00020TypeKijunbi"
                  :oneway-model-value="localOneway.mo00020KijunbiOneWay"
                />
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="mo00039OneWayUserSelectType === Or29235Const.DEFAULT.TANI"
              cols="12"
              sm="6"
              class="pa-2 col_height col-paddding"
            >
              <c-v-row
                class="or29235_row"
                style="padding-bottom: 8px"
              >
                <!-- 履歴選択ラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                  style="background-color: transparent"
                >
                </base-mo01338>
              </c-v-row>
              <c-v-row class="or29235_row">
                <!-- 履歴選択-ラジオボタングループ -->
                <base-mo00039
                  v-model="mo00039OneWayHistorySelectType"
                  :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                >
                </base-mo00039>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="
                localOneway.Or29235.careManagerInChargeSettingsFlag <= 0 ||
                localOneway.Or29235.managerId === Or29235Const.DEFAULT.ZERO
              "
              cols="12"
              sm="12"
              class="pa-2 flex-center"
              style="padding-left: 0px !important"
            >
              <!-- 担当ケアマネプルダウン -->
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
                class="search-tack"
                style="display: flex"
              ></g-custom-or-x-0145>
            </c-v-col>
          </c-v-row>
          <c-v-row
            class="or29235_row or29235-x0130"
            no-gutter
          >
            <c-v-col :cols="userCols">
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag"
              cols="12"
              sm="6"
              style="padding-left: 8px; padding-right: 0px"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0133
                v-if="orX0133OnewayModel.singleFlg"
                v-bind="orX0133"
                :oneway-model-value="orX0133OnewayModel"
              ></g-custom-or-x-0133>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          :disabled="!prtFlg"
          @click="pdfDownload()"
        >
          <!--ツールチップ表示："PDFをダウンロードします"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.pdf-download')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  >
  </g-base-or21813>
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  >
  </g-base-or21815>
  <!-- GUI01110_「印鑑欄設定」画面を起動する -->
  <g-custom-or-10016
    v-if="showDialogOr10016"
    v-bind="or10016"
    :oneway-model-value="or10016Data"
  />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0135
    v-if="showDialogOrx0135"
    v-bind="orX0135"
    :oneway-model-value="orX0135Data"
  ></g-custom-or-x-0135>
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrx0117"
    v-bind="orX0117"
    :oneway-model-value="localOneway.orX0117Oneway"
  ></g-custom-or-x-0117>
</template>

<style scoped lang="scss">
.or29235_screen {
  margin: -8px !important;
}

.or29235_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or29235_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}
.rowHeight > :first-child {
  display: none;
}
.customCol {
  margin-left: 0px;
  margin-right: 0px;
}
.col_height {
  height: 70px;
}
.col-paddding {
  padding: 0 !important;
}
:deep(.label-blue) {
  .v-input__control {
    .v-label {
      color: #0000ff !important;
    }
  }
}
:deep(.item-color) {
  .item-label {
    color: #0000ff !important;
  }
}

.or29235-x0130 {
  > .v-col {
    padding: 0 !important;
  }
}

:deep(.search-tack) {
  margin-left: 0px;
  .ma-0 {
    margin-right: 8px !important;
  }
  .v-input__control {
    width: 200px;
  }
}
</style>
