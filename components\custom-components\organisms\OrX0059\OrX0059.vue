<script setup lang="ts">
/**
 * OrX0059:有機体:(予定マスタ)アセスメント（包括）マスタリスト
 * GUI00833_予定マスタ
 *
 * @description
 * 予定マスタ一覧
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import type { VForm } from 'vuetify/components'
import type { TableData } from './OrX0059.type'
import { OrX0059Const } from './OrX0059.constants'
import type {
  OrX0059OnewayType,
  OrX0059Type,
  YoTei,
} from '~/types/cmn/business/components/OrX0059Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { useScreenTwoWayBind } from '~/composables/useComponentVue'
import { useScreenUtils } from '~/utils/useScreenUtils'
import { useValidation } from '~/utils/useValidation'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'

const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()
const validation = useValidation()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: OrX0059OnewayType
  modelValue: OrX0059Type
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()

const defaultOnewayModelValue: OrX0059OnewayType = {
  yoTeiList: [],
}
// ポスト最小幅
const columnMinWidth = ref<number[]>([105, 610])
const defaultModelValue: OrX0059Type = {
  editFlg: false,
  delBtnDisabled: false,
  yoTeiList: [],
  saveResultYoTeiList: [],
}

const localOneWay = reactive({
  orX0059: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo01278Oneway: {
    maxLength: '4',
    isEditCamma: false,
    min: 1000,
    max: 9999,
    rules: [
      validation.integer,
      validation.required,
      validation.minValue(1000),
      validation.maxValue(9999),
    ],
  },
  mo01274Oneway: {
    maxLength: '30',
    rules: [validation.required],
  },
})

const local = reactive({
  orX0059: {
    ...defaultModelValue,
    ...props.modelValue,
  },
})
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<TableData[]>({
  cpId: OrX0059Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = []
/**************************************************
 * 変数定義
 **************************************************/
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// "*"
const required: string = OrX0059Const.DEFAULT.REQUIRED
// 説明-区分番号
const descriptionCategoryNumber: string = t('label.category-number-input')
// 説明-全共通
const descriptionAllCommon: string = t('label.all-common')

// 元のテーブルデータ
const orgTableData = ref<string>('')
const tableForm = ref<VForm>()
// テーブルデータ
const tableDataFilter = computed(() => {
  const titleList = refValue.value
  return titleList!.filter((i: TableData) => i.updateKbn !== UPDATE_KBN.DELETE)
})
const refs = ref<Record<string, HTMLInputElement>>({})
const setRef = (el: HTMLInputElement | null, tableIndex: string) => {
  if (el) {
    refs.value[tableIndex] = el
  }
}
// 選択した行のindex
const selectedItemIndex = ref<number>(-1)
// テーブルヘッダ
const headers = [
  {
    title: t('label.category-number'),
    key: 'kbnCd',
    width: '80px',
    sortable: true,
    required: true,
  },
  { title: t('label.content'), key: 'textKnj', width: '180px', sortable: true, required: true },
]
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  init()
})

/**
 * 予定マスタ情報取得
 *
 */
function init() {
  // 戻り値はテーブルデータとして処理されます
  let tmpArr = []
  tmpArr = []
  for (const item of local.orX0059.yoTeiList) {
    tmpArr.push({
      kbnCd: { value: item.kbnCd },
      textKnj: { value: item.textKnj },
      cf1Id: item.cf1Id,
      modifiedCnt: item.modifiedCnt,
      tableIndex: tmpArr.length,
      updateKbn: UPDATE_KBN.NONE,
    })
  }
  // 元のテーブルデータの設定
  orgTableData.value = JSON.stringify(refValue.value)
  setChildCpBinds(props.parentUniqueCpId, {
    OrX0059: {
      twoWayValue: tmpArr,
    },
  })
  if (tmpArr.length > 0) {
    selectedItemIndex.value = 0
    // 行削除活性
    local.orX0059.delBtnDisabled = true
    emit('update:modelValue', local.orX0059)
  }
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
  // 行削除活性
  local.orX0059.delBtnDisabled = true
  emit('update:modelValue', local.orX0059)
}
/**
 * 「Enter」押下
 *
 * @param index - 選択した行のindex
 */
function handleEnter(index: number) {
  selectedItemIndex.value = index + 1
  // 行削除活性
  local.orX0059.delBtnDisabled = true
  emit('update:modelValue', local.orX0059)
}
/**
 * 「新規」押下
 */
async function createRow() {
  // 予定マスタのタイトル一覧の最終に新しい行を追加する。
  const data = {
    // 区分番号：空白
    kbnCd: { value: '' },
    // 内容：空白
    textKnj: { value: '' },
    // 入力ID
    cf1Id: '',
    // 更新回数
    modifiedCnt: '',
    // テーブルINDEX(行固有ID)
    tableIndex: refValue.value!.length,
    // 更新区分
    updateKbn: UPDATE_KBN.CREATE,
  }
  refValue.value!.push(data)
  selectRow(data.tableIndex)
  await nextTick()
  const id = 'input[id="input-' + data.tableIndex + '"]'
  const input = document?.querySelector(id) as HTMLElement | undefined
  input?.focus()
}

/**
 * 行削除ボタン押下
 */
function deleteRow() {
  if (selectedItemIndex.value !== -1) {
    // メッセージを表示
    // 確認ダイアログのpiniaを設定
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.top-btn-title'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11397'),
        // 第1ボタンタイプ
        firstBtnType: 'destroy1',
        // 第1ボタンラベル
        firstBtnLabel: t('btn.yes'),
        // 第2ボタンタイプ
        secondBtnType: 'normal3',
        // 第2ボタンラベル
        secondBtnLabel: t('btn.no'),
        // 第3ボタンタイプ
        thirdBtnType: 'blank',
        iconColor: 'rgb(var(--v-theme-blue-700))',
        iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
      },
    })
    // 確認ダイアログ表示
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * 行複写ボタン押下
 */
async function copyRow() {
  if (selectedItemIndex.value !== -1) {
    let kbnCd = ''
    let textKnj = ''
    refValue.value!.forEach((item: TableData) => {
      if (item.tableIndex === selectedItemIndex.value) {
        kbnCd = item.kbnCd.value
        textKnj = item.textKnj.value
      }
    })
    // 予定マスタのタイトル一覧の最終に新しい行を追加する。
    const data = {
      // 区分番号
      kbnCd: { value: kbnCd },
      // 内容
      textKnj: { value: textKnj },
      // 入力ID
      cf1Id: '',
      // 更新回数
      modifiedCnt: '',
      // テーブルINDEX(行固有ID)
      tableIndex: refValue.value!.length,
      // 更新区分
      updateKbn: UPDATE_KBN.CREATE,
    }
    const newRowPosition = selectedItemIndex.value + 1
    refValue.value!.splice(newRowPosition, 0, data)
    let index = 0
    refValue.value = refValue.value!.map((item) => {
      const newItem = { ...item } // 他のプロパティは変更せずにtableIndexだけ更新
      if (item.updateKbn !== UPDATE_KBN.DELETE) {
        // 非删除行処理
        newItem.tableIndex = index
        index++ // 削除されていない行のみに連番を付与する
      } else {
        // 行削除処理
        newItem.tableIndex = -2
      }
      return newItem
    })
    selectRow(newRowPosition)
    await nextTick()
    const id = 'input[id="input-' + newRowPosition + '"]'
    const input = document?.querySelector(id) as HTMLElement | undefined
    input?.focus()
  }
}

/**
 * 検証
 */
async function tableValidation() {
  return (await tableForm.value!.validate()).valid
}

/**
 * コンテンツの更新
 */
function onUpdate() {
  refValue.value!.forEach((item: TableData) => {
    if (item.tableIndex === selectedItemIndex.value && item.updateKbn !== UPDATE_KBN.CREATE) {
      item.updateKbn = UPDATE_KBN.UPDATE
    }
  })
}

watch(
  () => refValue.value,
  () => {
    local.orX0059.editFlg = orgTableData.value !== JSON.stringify(refValue.value)
    // 予定マスタのリスト
    const yoTeiList: YoTei[] = []
    for (const item of refValue.value!) {
      yoTeiList.push({
        ...item,
        kbnCd: item.kbnCd.value,
        textKnj: item.textKnj.value,
      })
    }
    local.orX0059.yoTeiList = yoTeiList
    emit('update:modelValue', local.orX0059)
  },
  { deep: true }
)
/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      if (selectedItemIndex.value !== null) {
        refValue.value!.forEach((item: TableData) => {
          if (item.tableIndex === selectedItemIndex.value) {
            if (item.updateKbn !== UPDATE_KBN.DELETE) {
              item.updateKbn = UPDATE_KBN.DELETE
            }
          }
        })
        let index = 0
        refValue.value = refValue.value!.map((item) => {
          const newItem = { ...item } // 他のプロパティは変更せずにtableIndexだけ更新
          if (item.updateKbn !== UPDATE_KBN.DELETE) {
            // 非删除行処理
            newItem.tableIndex = index
            index++ // 削除されていない行のみに連番を付与する
          } else {
            // 行削除処理
            newItem.tableIndex = -2
          }
          return newItem
        })
        let nextIndex = -1
        if (tableDataFilter.value.length - 1 > 0) {
          if (selectedItemIndex.value >= tableDataFilter.value.length - 1) {
            nextIndex = tableDataFilter.value.length - 2
          }
          nextIndex = selectedItemIndex.value
          selectRow(nextIndex)
          await nextTick()
        }
        if (tableDataFilter.value.length === 0) {
          selectedItemIndex.value = -1
          // 行削除非活性
          local.orX0059.delBtnDisabled = false
          emit('update:modelValue', local.orX0059)
        }
      }
    } else {
      return
    }
  },
  { deep: true }
)

/**
 * 表データの設定
 */
watch(
  () => local.orX0059.saveResultYoTeiList,
  () => {
    if (Array.isArray(local.orX0059.saveResultYoTeiList)) {
      const yoTeiList = []
      for (const item of local.orX0059.saveResultYoTeiList) {
        yoTeiList.push({
          kbnCd: { value: item.kbnCd },
          textKnj: { value: item.textKnj },
          cf1Id: item.cf1Id,
          modifiedCnt: item.modifiedCnt,
          // テーブルINDEX(行固有ID)
          tableIndex: yoTeiList.length,
          // 更新区分
          updateKbn: UPDATE_KBN.NONE,
        })
      }
      refValue.value = yoTeiList
      // 元のテーブルデータの設定
      orgTableData.value = JSON.stringify(refValue.value)
      if (yoTeiList.length > 0) {
        selectedItemIndex.value = 0
        // 行削除活性
        local.orX0059.delBtnDisabled = true
        emit('update:modelValue', local.orX0059)
      }
    }
  }
)

defineExpose({
  tableValidation,
  createRow,
  copyRow,
  deleteRow,
  init,
})
</script>

<template>
  <div>
    <!-- 予定マスタ一覧 -->
    <c-v-form ref="tableForm">
      <c-v-data-table
        v-resizable-grid="{ columnWidths: columnMinWidth }"
        fixed-header
        :headers="headers"
        :items="tableDataFilter"
        height="400px"
        class="table-wrapper mt-2"
        hover
        hide-default-footer
        :items-per-page="-1"
      >
        <!-- ヘッダ Part -->
        <template #headers>
          <tr>
            <!-- *区分番号 -->
            <th style="width: 105px">
              <span style="color: red">{{ required }}</span
              >{{ t('label.category-number') }}
            </th>
            <!-- *内容 -->
            <th style="width: 600px">
              <span style="color: red">{{ required }}</span
              >{{ t('label.content') }}
            </th>
          </tr>
        </template>

        <!-- 一覧 -->
        <template #item="{ item, index }">
          <tr
            :class="{ 'select-row': selectedItemIndex === item.tableIndex }"
            @click="selectRow(item.tableIndex)"
          >
            <!-- 区分番号 -->
            <td class="text-padding text-align-right">
              <!-- 分子：表用数値専用テキストフィールド -->
              <base-mo01278
                :id="`input-${item.tableIndex}`"
                :ref="(el: HTMLInputElement | null) => setRef(el, `input-${item.tableIndex}`)"
                v-model="tableDataFilter[index].kbnCd"
                :oneway-model-value="localOneWay.mo01278Oneway"
                style="min-width: 100px; width: 100%"
                @keydown.enter="handleEnter(index)"
                @change="onUpdate"
              />
            </td>
            <!-- 内容 -->
            <td class="text-padding">
              <!-- 分子：表用テキストフィールド -->
              <base-mo01274
                v-model="tableDataFilter[index].textKnj"
                :one-model-value="localOneWay.mo01274Oneway"
                @keydown.enter="handleEnter(index)"
                @change="onUpdate"
              />
            </td>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-form>
  </div>
  <!-- 説明:※区分番号は1000以上の値を入力してください。  -->
  <div class="body-text-category">
    {{ descriptionCategoryNumber }}
  </div>
  <!-- 説明:※全共通  -->
  <div>
    {{ descriptionAllCommon }}
  </div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
:deep(.table-wrapper .v-table__wrapper td:has(input:not([readonly]):not([disabled]):not([type=checkbox]):not([type=radio])) input:focus){
  outline: solid 1px rgb(var(--v-theme-key)) !important;
}
:deep(.table-wrapper .v-table__wrapper td:has(input:not([readonly]):not([disabled]):not([type=checkbox]):not([type=radio])) input){
  border: solid 2px transparent !important;
  outline: solid 1px transparent !important;
  border-radius: unset !important;
}
:deep(.table-wrapper td):has(input),
:deep(.table-wrapper td):has(select) {
  padding: 0 !important;
}
:deep(.v-table--fixed-header > .v-table__wrapper > table > thead) {
  position: sticky;
  top: 0px !important;
  z-index: 2;
}
//区分番号幅
.number-width {
  width: 80px;
}
//内容幅
.content-width {
  width: 180px;
}
// 右寄せのCSS
.text-align-right {
  text-align: right;
}
//区分番号
.body-text-category {
  margin-top: 8px;
  margin-left: 20px;
}
:deep(.table-header .v-data-table-rows-no-data) {
  display: none !important;
}
:deep(.table-cell.invalid) {
  border: unset;
}
</style>
