<script setup lang="ts">
/**
 * Or10453:処理ロジック
 * GUI00938_印刷設定
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> DAO VAN DUONG
 */
import { onMounted, reactive, ref, watch, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10453Const } from '~/components/custom-components/organisms/Or10453/Or10453.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CustomClass } from '@/types/CustomClassType'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import { OrX0135Const } from '~/components/custom-components/organisms/OrX0135/OrX0135.constants'
import { OrX0135Logic } from '~/components/custom-components/organisms/OrX0135/OrX0135.logic'
import {
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
} from '#imports'

import type { PrintSettingsInfoUpdateGUI00938InEntity } from '~/repositories/cmn/entities/PrintSettingsInfoUpdateGUI00938'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or10453OnewayType } from '~/types/cmn/business/components/Or10453Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo01334Items, Mo01334OnewayType } from '~/types/business/components/Mo01334Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { UserEntity } from '~/repositories/cmn/entities/LeavingInfoRecordPrintSettingsEntity'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type { OrX0135OnewayType } from '~/types/cmn/business/components/OrX0135Type'
import type {
  PrintSettingsScreenInitialInfoSelectGUI00938InEntity,
  PrintSettingsScreenInitialInfoSelectGUI00938OutEntity,
  choPrtList,
  IniDataObject,
} from '~/repositories/cmn/entities/PrintSettingsScreenInitialInfoSelectGUI00938Entity'
import type {
  CarePLan1HistoryInfoSelectGUI00938InEntity,
  CarePLan1HistoryInfoSelectGUI00938OutEntity,
} from '~/repositories/cmn/entities/CarePLan1HistoryInfoSelectGUI00938'
import type {
  LedgerInitializeDataSelectGUI00938InEntity,
  LedgerInitializeDataSelectGUI00938OutEntity,
} from '~/repositories/cmn/entities/LedgerInitializeDataSelectGUI00938'
import type {
  Or10453TwoWayData,
  TyppeCustomClass,
  ChoPrtListMaping
} from '~/components/custom-components/organisms/Or10453/Or10453.type'
import type {
  OrX0128OnewayType,
  OrX0128Items,
  OrX0128Headers,
} from '~/types/cmn/business/components/OrX0128Type'

const { t } = useI18n()

/* システム共有情報ストア */
const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or10453OnewayType
  uniqueCpId: string
}
/* 引継情報を取得する */
const props = defineProps<Props>()

const or00094 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })
const orX0135 = ref({ uniqueCpId: '' })
const mo00610Oneway = ref<Mo00610OnewayType>({
  btnLabel: t('btn.confirm-form-registration‌'), // ボタンラベル
  width: '100px',
  disabled: false,
  prependIcon: '',
  appendIcon: '',
  class: 'btn-mo00610',
})
/* プロファイル */
const userCols = ref(6)
/* 帳票イニシャライズデータを取得する */
const iniDataObject = ref<IniDataObject>({
  /* 氏名伏字印刷 */
  prtName: '',
  /* 文書番号印刷 */
  prtBng: '',
  /* 個人情報印刷 */
  prtKojin: '',
})
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10453Const.DEFAULT.IS_OPEN,
})
/**
 * 出力帳票名一覧
 */
const mo01334OnewayReport = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'prtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})
/**
 * 出力帳票名一覧
 */
const mo01334TypeReport = ref({
  value: '',
  values: [] as choPrtList[],
})
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)
const localOneway = reactive({
  Or10453: {
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '1300px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10453',
      toolbarTitle: t('label.print-set'),
      toolbarName: 'Or10453ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  mo01338OneWayTitle: {
    value: t('label.title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  prndateOneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  param07Option: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  param08Option: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo00020OneWay: {
    showItemLabel: false,
    width: '132px',
  } as Mo00020OnewayType,
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  param03OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00045OnewayTitleInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
  } as Mo00045OnewayType,
  param04Oneway: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '50',
    maxLength: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1' }),
  } as Mo00045OnewayType,
  param050OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param05OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: '承認欄を印刷する',
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintAuthor: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-author'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintDescription: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-description'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      labelStyle: 'display: none',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      labelStyle: 'display: none',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayBaseDate: {
    value: t('label.base-date'),
    customClass: {
      labelStyle: 'display: none',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo01338OneWayCareManagerInCharge: {
    value: t('label.care-manager-in-charge'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo00009OnewayType: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo01338OneWayCareManagerInChargeLabel: {
    value: 'ほのぼの 三郎',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as TyppeCustomClass,
  } as Mo01338OnewayType,
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: false,
    multiple: false,
    selectedUserCounter: '1',
  } as OrX0145OnewayType,
})
const local = reactive({
  createYmd: {
    value: '',
  } as Mo00020Type,
  mo00018TypePrintDescription: {
    modelValue: false,
  } as Mo00018Type,
  mo00039TypeUserSelectType: '',
  mo00039TypeHistorySelectType: '',
  mo00020TypeKijunbi: {
    value: '2024/11/11',
  } as Mo00020Type,
  shokuin2Knj: '',
  userList: [] as UserEntity[],
  orX0128DetList: [] as OrX0128Items[],
})
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width: 330px',
  userId: Or10453Const.DEFAULT.EMPTY,
  showKanaSelectionCount: true,
})
const orX0128OnewayModel = reactive<OrX0128OnewayType>({
  kikanFlg: localOneway.Or10453.kikanFlg ?? '',
  singleFlg: OrX0128Const.DEFAULT.TANI,
  tableStyle: 'width:370px; height: 510px;',
  headers: [
    { title: t('label.consultation-date'), key: 'createYmd', minWidth: '180px', sortable: false, align: 'center' },
    { title: t('label.shoku_knj'), key: 'sogoShitenKnj', minWidth: '180px', sortable: false, align: 'center' },
  ] as OrX0128Headers[],
  items: [],
  initSelectId: Or10453Const.DEFAULT.EMPTY,
})
const orX0135Data: OrX0135OnewayType = {
  svJigyoId: systemCommonsStore.getSvJigyoId!,
  houjinId: systemCommonsStore.getHoujinId!,
  shisetuId: systemCommonsStore.getShisetuId!,
}
/**************************************************
 * Pinia
 **************************************************/
/**
 * Piniaストアの設定
 * - ダイアログの開閉状態を管理
 * - uniqueCpIdを使用して一意の状態を識別
 */
const { setState } = useScreenOneWayBind({
  cpId: Or10453Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or10453Const.DEFAULT.IS_OPEN
    },
  },
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
/* 子コンポーネントのユニークIDを設定する */
useSetupChildProps(props.uniqueCpId, {
  [Or00094Const.CP_ID(0)]: or00094.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
  [OrX0135Const.CP_ID(0)]: orX0135.value,
})
const { refValue } = useScreenTwoWayBind<Or10453TwoWayData>({
  cpId: Or10453Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
const showDialogOrx0135 = computed(() => {
  return OrX0135Logic.state.get(orX0135.value.uniqueCpId)?.isOpen ?? false
})

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    /* 組織dialog自動クローズを手動判定に変更 */
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)
/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334TypeReport.value.value,
  async () => {
    localOneway.orX0145Oneway.selectedUserCounter = refValue.value.choPrtList[mo01334TypeReport.value.value]?.param06 ?? ''
    await ledgerInitializeDataSelect()
  },
  { immediate: true }
)

/**
 * 「利用者選択方」ラジオボタン選択
 */
watch(
  () => local.mo00039TypeUserSelectType,
  (newValue) => {
    orX0130Oneway.selectMode = newValue
    local.userList = []
    const userList = OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList || []
    if (userList.length) {
      for (const item of userList) {
        local.userList.push({
          userId: item.userId,
          userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
        } as UserEntity)
      }
    }
    if (newValue === Or10453Const.DEFAULT.TANI) {
      userCols.value = 6
      orX0130Oneway.tableStyle = 'width: 320px'
      localOneway.param050OneWay.disabled = false
      return
    }
    userCols.value = 12
    orX0130Oneway.tableStyle = 'width: 430px'
    localOneway.param050OneWay.disabled = true
    orX0130Oneway.userId = localOneway.Or10453.userId || Or10453Const.DEFAULT.EMPTY
  }
)

/**
 * 「履歴選択」ラジオボタン選択
 */
watch(
  () => local.mo00039TypeHistorySelectType,
  (newValue) => {
    orX0128OnewayModel.singleFlg = newValue
  }
)

/**
 * ラジオボタンの選択状態を追跡する
 */
watch(
  () => !refValue.value?.choPrtList[mo01334TypeReport.value.value]?.prndate,
  async () => {
    await checkTitleInput()
  }
)

watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    local.userList = newValue.userList ?? []
    await nextTick()
    await carePLan1HistoryInfoSelectGUI00938()
  }
)

watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    if (!newValue.historyDetClickFlg) return
    local.orX0128DetList = newValue.orX0128DetList
  }
)

watch(
  () => orX0145Type.value.value,
  (newValue) => {
    local.shokuin2Knj = `${newValue.shokuinKnj ?? Or10453Const.DEFAULT.EMPTY}`
  }
)

onMounted(async () => {

  /* 50音ヘッドラインの表示設定ボタンを表示 */
  Or00094Logic.state.set({
    uniqueCpId: or00094.value.uniqueCpId,
    state: { dispSettingBtnDisplayFlg: true },
  })
  await initCodes()
  await printSettingsScreenInitialInfoSelectGUI00938()
  await carePLan1HistoryInfoSelectGUI00938()
  orX0130Oneway.userId = localOneway.Or10453.userId || Or10453Const.DEFAULT.EMPTY
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  /* 汎用コード取得APIのinEntity生成 */
  const selectCodeKbnList = [
    /* 回数区分 */
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CREATED_DATE_PRINT_TYPE },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.PRINT_LEVEL_OF_CARE_REQUIRED },
  ]

  /* 汎用コード取得API実行 */
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  /* コード取得 */
  /* 回数区分 */
  localOneway.prndateOneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  localOneway.param08Option.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CREATED_DATE_PRINT_TYPE
  )
  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  localOneway.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  localOneway.param07Option.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.PRINT_LEVEL_OF_CARE_REQUIRED
  )

  /* 初期値 */
  local.mo00039TypeUserSelectType = '0'
  local.mo00039TypeHistorySelectType = '0'
}

/**
 * 印刷設定画面初期情報を取得する
 */
async function printSettingsScreenInitialInfoSelectGUI00938() {
  const params: PrintSettingsScreenInitialInfoSelectGUI00938InEntity = {
    sysCd: systemCommonsStore.getSystemCode ?? '',
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    shokuId: systemCommonsStore.getStaffId ?? '',
    tantoId: systemCommonsStore.getManagerId ?? '',
    appYmd: systemCommonsStore.getProcessDate ?? '',
    kikanFlg: localOneway.Or10453.kikanFlg ?? '',
    userId: localOneway.Or10453.userId ?? '',
    sectionName: localOneway.Or10453.sectionName ?? '',
    choIndex: localOneway.Or10453.choIndex ?? '',
    kinounameKnj: localOneway.Or10453.kinounameKnj ?? '',
    kojinhogoFlg: localOneway.Or10453.kojinhogoFlg ?? '',
    sectionAddNo: localOneway.Or10453.sectionAddNo ?? '',
    local: localOneway.Or10453.local ?? '',
  }

  /* バックエンドAPIから初期情報取得 */
  const res: PrintSettingsScreenInitialInfoSelectGUI00938OutEntity = await ScreenRepository.select(
    'printSettingsScreenInitialInfoSelectGUI00938',
    params
  )
  const choPrtList: choPrtList[] = res.data?.choPrtList ?? []
  mo01334OnewayReport.value.items = choPrtList.map((item) => {
    return {
      id: item.prtNo,
      mo01337OnewayReport: {
        value: item.prtTitle,
        unit: Or10453Const.DEFAULT.EMPTY,
      } as Mo01337OnewayType,
      ...item,
    } as Mo01334Items
  })
  if (choPrtList.length > 0) mo01334TypeReport.value.value = choPrtList[0].prtNo ?? ''
  mappingRefValue(choPrtList)
  await nextTick()
  localOneway.orX0145Oneway.selectedUserCounter = refValue.value.choPrtList[mo01334TypeReport.value.value]?.param06 ?? ''
}

/**
 * AC029_利用者選択
 */
async function carePLan1HistoryInfoSelectGUI00938() {
  if (local.mo00039TypeUserSelectType === Or10453Const.DEFAULT.HUKUSUU || !local.userList.length) return
  local.orX0128DetList = []
  const userId = local.userList[0]?.userId || ''
  const params: CarePLan1HistoryInfoSelectGUI00938InEntity = {
    kikanFlg: localOneway.Or10453.kikanFlg ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    userId: userId,
  }
  /* バックエンドAPIから初期情報取得 */
  const res: CarePLan1HistoryInfoSelectGUI00938OutEntity = await ScreenRepository.select(
    'carePLan1HistoryInfoSelectGUI00938',
    params
  )
  const rirekiList = res?.data?.rirekiList ?? []
  const tempList: string[] = [] as string[]
  orX0128OnewayModel.items = []
  rirekiList.forEach((item, index) => {
    const obj = {...item}
    const planPeriod =
      t('label.plan-period') +
      Or10453Const.DEFAULT.SPLIT_COLON +
      item.startYmd +
      Or10453Const.DEFAULT.SPLIT_TILDE +
      item.endYmd
    if (!tempList.includes(planPeriod)) {
      tempList.push(planPeriod)
      obj.planPeriod = planPeriod
      obj.isPeriodManagementMergedRow = true
      orX0128OnewayModel.items.push(obj)
      const objMap = {id: `${index + 1}`, ...obj, isPeriodManagementMergedRow: false}
      delete objMap.planPeriod
      orX0128OnewayModel.items.push(objMap)
      return
    }
    obj.isPeriodManagementMergedRow = false
    obj.id = `${index + 1}`
    orX0128OnewayModel.items.push(obj)
  })
}

/**
 * AC027_印刷
 */
async function printSettingsInfoUpdateGUI00938() {
  const choPrtList = mappingRefValue([], true) ?? []
  const params: PrintSettingsInfoUpdateGUI00938InEntity = {
    sysCd: systemCommonsStore.getSystemCode ?? '',
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    shokuId: systemCommonsStore.getStaffId ?? '',
    kinounameKnj: localOneway.Or10453.kinounameKnj ?? '',
    choPro: refValue.value?.choPrtList[mo01334TypeReport.value.value]?.choPro ?? '',
    kojinhogoFlg: localOneway.Or10453.kojinhogoFlg ?? '',
    sectionAddNo: localOneway.Or10453.sectionAddNo ?? '',
    choPrtList: choPrtList ?? [],
    iniDataObject: iniDataObject.value,
  }
  /* バックエンドAPIから初期情報取得 */
  await ScreenRepository.update('printSettingsInfoUpdateGUI00938', params)
}

/**
 * エラーダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21813MsgOneBtn() {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        /* 確認ダイアログのフラグをOFF */
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21815MsgOneBtn() {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      /* ダイアログタイトル */
      dialogTitle: t('label.caution'),
      /* ダイアログテキスト */
      iconName: 'warning',
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        /* 確認ダイアログのフラグをOFF */
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 帳票タイトルが入力していない場合
 */
async function checkTitleInput() {
  if (refValue.value?.choPrtList[mo01334TypeReport.value.value]?.prtTitle.value) return
  const dialogResult = await showOr21815MsgOneBtn()
  if (dialogResult !== 'yes') return
  const item: choPrtList = mo01334OnewayReport.value.items.find(e => e.id === mo01334TypeReport.value.value)
  if (!item || !refValue.value.choPrtList[mo01334TypeReport.value.value]) return
  refValue.value.choPrtList[mo01334TypeReport.value.value].prtTitle.value = item.prtTitle
}

/**
 * 閉じる
 */
async function close() {
  await checkTitleInput()
  setState({ isOpen: false })
}

/**
 * 印刷
 */
async function print() {
  await checkTitleInput()
  if (!refValue.value?.choPrtList[mo01334TypeReport.value.value]?.choPro) await showOr21813MsgOneBtn()
  if (!local.userList.length) return
  await nextTick()
  await printSettingsInfoUpdateGUI00938()
  setState({ isOpen: false })
}
/* AC013_承認欄印刷登録 */
function handleOpenModalGUI00617() {
  OrX0135Logic.state.set({
    uniqueCpId: orX0135.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function mappingRefValue(choPrtList:choPrtList[] = [], reverse = false) {
  if (!refValue.value) return
  if (reverse) {
    refValue.value.choPrtList[mo01334TypeReport.value.value].param06 = `${orX0145Type.value.value.value}`
    const choPrtListMap: ChoPrtListMaping[] = Object.values(refValue.value.choPrtList) ?? []
    const array = choPrtListMap.map((e: ChoPrtListMaping) => {
      return {
        ...e,
        prtTitle: e.prtTitle.value ?? '',
        param02: e.param02.value ?? '',
        param03: e.param03.modelValue ? 'TRUE' : 'FALSE',
        param04: e.param04.value ?? '',
        param05: e.param05.modelValue ? 'TRUE' : 'FALSE',
        param050: e.param050.modelValue ? 'TRUE' : 'FALSE',
      }
    }) ?? []
    return array
  }
  const output = choPrtList.reduce((obj: Record<string, choPrtList>, item: choPrtList) => {
    obj[item.prtNo] = {
      ...item,
      prtTitle: { value: item.prtTitle ?? '' },
      param02: { value: hasData(item.param02) ? item.param02 : systemCommonsStore.getSystemDate },
      param03: { modelValue: item.param03 === 'TRUE' ? true : false },
      param04: { value: item.param04 ?? '' },
      param05: { modelValue: item.param05 === 'TRUE' ? true : false },
      param050: { modelValue: item.param050 === 'TRUE' ? true : false },
      param07: hasData(item.param07) ? item.param07 : localOneway.param07Option.items[0]?.value
    }
    return obj
  }, {})
  refValue.value = { choPrtList: output }
  useScreenStore().setCpTwoWay({
    cpId: Or10453Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}
/**
 * AC003_帳票選択切替
 */
async function ledgerInitializeDataSelect() {
  const params: LedgerInitializeDataSelectGUI00938InEntity = {
    sysCd: systemCommonsStore.getSystemCode ?? '',
    kinounameKnj: localOneway.Or10453.kinounameKnj ?? '',
    shokuId: systemCommonsStore.getStaffId ?? '',
    sectionKnj: refValue.value?.choPrtList[mo01334TypeReport.value.value]?.choPro ?? '',
    kojinhogoFlg: localOneway.Or10453.kojinhogoFlg ?? '',
    sectionAddNo: localOneway.Or10453.sectionAddNo ?? '',
  }
  /* バックエンドAPIから初期情報取得 */
  const res: LedgerInitializeDataSelectGUI00938OutEntity = await ScreenRepository.select(
    'ledgerInitializeDataSelectGUI00938',
    params
  )
  iniDataObject.value = res.data?.iniDataObject ?? {
    prtName: Or10453Const.DEFAULT.EMPTY,
    prtBng: Or10453Const.DEFAULT.EMPTY,
    prtKojin: Or10453Const.DEFAULT.EMPTY,
  }
}
function hasData(data: unknown) {
  if (data !== '' && data !== null && data !== undefined) return true
  else return false
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or10453_screen"
      >
        <!-- box-left -->
        <c-v-col
          cols="12"
          sm="2"
          class="pa-0 pt-2 pl-2 or10453_border_right"
        >
          <base-mo-01334
            v-model="mo01334TypeReport"
            :oneway-model-value="mo01334OnewayReport"
            class="list-wrapper"
          >
            <!-- 帳票 -->
            <template #[`item.prtTitle`]="{ item }">
              <base-mo01337 :oneway-model-value="item.mo01337OnewayReport" />
            </template>
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <!-- box-center -->
        <c-v-col
          v-if="refValue.choPrtList[mo01334TypeReport.value]"
          cols="12"
          sm="3"
          class="pa-0 pt-2 or10453_border_right align-center"
        >
          <!-- タイトル -->
          <c-v-row
            no-gutter
            class="or10453_row flex-center"
          >
            <c-v-col
              cols="12"
              class="pr-2 flex flex-row align-center"
            >
              <div class="mr-2">{{ t('label.title') }}</div>
              <base-mo00045
                v-model="refValue.choPrtList[mo01334TypeReport.value].prtTitle"
                class="block flex-1"
                :oneway-model-value="localOneway.mo00045OnewayTitleInput"
                @keyup.enter="checkTitleInput"
              />
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0" />
          <c-v-row
            no-gutter
            class="customCol or10453_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              style="padding-left: 0px; padding-right: 0px"
            >
              <!-- 印刷日付選択ラジオボタングループ -->
              <base-mo00039
                v-model="refValue.choPrtList[mo01334TypeReport.value].prndate"
                :oneway-model-value="localOneway.prndateOneWay"
              />
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              style="padding-left: 0px; padding-right: 0px"
            >
              <!-- 印刷日付ラベル -->
              <base-mo00020
                v-if="refValue.choPrtList[mo01334TypeReport.value].prndate === '2'"
                v-model="refValue.choPrtList[mo01334TypeReport.value].param02"
                :oneway-model-value="localOneway.prndateOneWay"
                @mousedown="checkTitleInput"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="localOneway.Or10453.kikanFlg === '2'"
            no-gutter
            class="customCol or10453_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              class="pa-0"
            >
              <!-- 作成年月日印刷区分 -->
              <base-mo00039
                v-model="refValue.choPrtList[mo01334TypeReport.value].param08"
                :oneway-model-value="localOneway.param08Option"
              />
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              class="pa-0"
            >
              <!-- 印刷日付ラベル -->
              <base-mo01338
                v-if="refValue.choPrtList[mo01334TypeReport.value].param08 === Or10453Const.DEFAULT.HUKUSUU"
                :oneway-model-value="local.createYmd"
                style="background-color: transparent"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="printerOption customCol or10453_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pt-0 pb-0"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or10453_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 flex flex-row align-center mb-2"
            >
              <!-- 敬称を変更する -->
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param03"
                :oneway-model-value="localOneway.param03OneWay"
              />
              (<base-mo00045
                v-model="refValue.choPrtList[mo01334TypeReport.value].param04"
                :oneway-model-value="localOneway.param04Oneway"
                :disabled="!refValue.choPrtList[mo01334TypeReport.value].param03.modelValue"
              />)
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 flex flex-row align-center"
            >
              <!-- 承認欄を印刷する -->
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param05"
                :oneway-model-value="localOneway.param05OneWay"
              />
              <base-mo00610
                :oneway-model-value="mo00610Oneway"
                @click="handleOpenModalGUI00617()"
              />
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <!-- 記入用シートを印刷する -->
              <base-mo00018
                v-model="refValue.choPrtList[mo01334TypeReport.value].param050"
                :oneway-model-value="localOneway.param050OneWay"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or10453_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pt-0 pb-0"
            >
              <b>{{ t('label.print-nursing-care-required') }}</b>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pt-0 pb-0"
            >
              <base-at-select
                v-model="refValue.choPrtList[mo01334TypeReport.value].param07"
                class="select-w mt-2"
                :items="localOneway.param07Option.items"
                item-title="label"
                item-value="value"
                :class="{ active: false }"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <!-- box-right -->
        <c-v-col
          v-if="refValue.choPrtList[mo01334TypeReport.value]"
          cols="12"
          sm="7"
          class="pa-0"
        >
          <c-v-row
            class="or10453_row pa-2"
            no-gutter
            style="align-items: center"
          >
            <c-v-col
              cols="4"
              class="pa-0"
            >
              <!-- 利用者選択 -->
              <base-mo01338
                class="mt-2 pl-2"
                :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                style="background-color: transparent"
              />
              <!-- 利用者選択ラジオボタングループ -->
              <base-mo00039
                v-model="local.mo00039TypeUserSelectType"
                :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                @click="carePLan1HistoryInfoSelectGUI00938()"
              />
            </c-v-col>
            <c-v-col
              v-if="local.mo00039TypeUserSelectType === Or10453Const.DEFAULT.TANI"
              cols="4"
              class="pa-0"
            >
              <!-- 履歴選択 -->
              <base-mo01338
                class="mt-2 pl-2"
                :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                style="background-color: transparent"
              />
              <!-- 履歴選択 -->
              <base-mo00039
                v-model="local.mo00039TypeHistorySelectType"
                :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
              />
            </c-v-col>
            <c-v-col
              v-if="local.mo00039TypeUserSelectType === Or10453Const.DEFAULT.HUKUSUU"
              cols="4"
              class="pa-0"
            >
              <!-- 履歴選択ラベル -->
              <base-mo01338
                class="mt-2 pl-2"
                :oneway-model-value="localOneway.mo01338OneWayBaseDate"
                style="background-color: transparent"
              />
              <base-mo00020
                v-model="local.mo00020TypeKijunbi"
                :oneway-model-value="localOneway.mo00020KijunbiOneWay"
              />
            </c-v-col>
            <c-v-col
              cols="4"
              class="pa-0"
            >
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            class="or10453_row"
            no-gutter
          >
            <c-v-col
              :cols="userCols"
              class="overflow-auto"
            >
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              />
            </c-v-col>
            <c-v-col
              v-if="local.mo00039TypeUserSelectType === Or10453Const.DEFAULT.TANI"
              cols="6"
            >
              <g-custom-or-x-0128
                v-if="orX0128OnewayModel.singleFlg"
                v-bind="orX0128"
                :oneway-model-value="orX0128OnewayModel"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        />
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="print()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813 v-bind="or21813"> </g-base-or21813>
  <g-base-or21815 v-bind="or21815"> </g-base-or21815>
  <g-custom-or-x0135
    v-if="showDialogOrx0135"
    v-bind="orX0135"
    :oneway-model-value="orX0135Data"
    :parent-unique-cp-id="props.uniqueCpId"
  />
</template>
<style lang="scss">
  .or10453_row {
    table {
      th {
        padding: 0px!important;
        .v-data-table-header__content {
          padding: 0px 5px!important;
        }
      }
      td {
        padding: 0px!important;
        .v-col {
          padding: 0px 5px!important;
        }
      }
    }
  }
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';
.or10453_screen {
  margin: -8px !important;
}

.or10453_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or10453_row {
  margin: 0px !important;
}

.flex-center {
  display: flex;
  align-items: center;
}

.user_class {
  margin-left: -12px;
}
.flex {
  display: flex;
}
.flex-row {
  flex-direction: row;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.align-center {
  align-items: center;
}
.flex-1 {
  flex: 1;
}
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
.btn-mo00610 {
  outline: none;
  background-color: rgb(var(--v-theme-black-100));
  border-color: rgb(var(--v-theme-black-100));
  color: black !important;
}
.customCol {
  margin-left: 0px;
  margin-right: 0px;
}
.overflow-auto {
  overflow: auto!important;
}
.or56885-pd-8 {
  padding: 8px;
}
</style>
