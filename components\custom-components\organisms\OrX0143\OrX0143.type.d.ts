/**
 * OrX0143:有機体:履歴一覧情報
 *
 * <AUTHOR>
 */

/**
 * 期間履歴タイトルリスト
 */
export interface KikanRirekiTitleInfo {
  /**
   * タイトル
   */
  title: string
  /**
   * 列幅
   */
  width: string
  /**
   * key
   */
  key: string
}

/**
 * 明細情報
 */
export interface OrX0143TableData {
  /** id */
  id?: string
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
  /**
   * 作成日
   */
  createYmd: string
  /**
   * 履歴ID
   */
  rirekiId: string
  /**
   * 任意の項目
   */
  [key: string]: Ref
}

/**
 * ソート情報
 */
interface SortConfig<T> {
  /**
   * ソートフィールド
   */
  field: keyof T
  /**
   * ソート順（asc/desc）
   */
  order: 'asc' | 'desc'
}

/**
 * 選択結果が保持
 */
export interface OrX0143Selected {
  /**
   * 単一id
   */
  taniSelectedId: number
  /**
   * 複数id
   */
  hukusuuSelectedId: boolean[]
}
