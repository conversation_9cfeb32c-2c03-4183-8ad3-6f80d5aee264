<script lang="ts" setup>
/**
 * Or51898: （日課表（日常生活活動等））時間帯入力
 * GUI00999_日課表入力画面
 *
 * <AUTHOR> LE VAN CUONG
 */

import { useI18n } from 'vue-i18n'
import { reactive, ref, watch } from '#imports'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Or51898Type } from '~/types/cmn/business/components/Or51898Type'
import type { Mo01272OnewayType, Mo01272Type } from '~/types/business/components/Mo01272Type'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or51898Type
}

// 引継情報を取得する
const props = defineProps<Props>()
const endTime = ref()
const startTime = ref()
// 双方向バインド用の内部変数
const local = reactive({
  or51898: props.modelValue,
})

// 片方向バインド用の内部変数
const localOneway = reactive({
  mo01299ListOneway: {
    anchorPoint: 'ss-1',
    title: t('label.time-zone-label'),
  },
  timeZoneInputSupportBtn: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  wavy: {
    value: t('label.wavy'),
    customClass: new CustomClass({
      outerClass: 'mr-2',
      labelClass: 'ma-1',
      itemClass: 'align-center',
    }),
  } as Mo01338OnewayType,
  endTime: {
    name: 'endTime',
    width: '100px',
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: true,
    selectionMode: false,
  } as Mo01272OnewayType,

  startTime: {
    name: 'startTime',
    width: '100px',
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: true,
    selectionMode: false,
  } as Mo01272OnewayType,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['onClick', 'update:modelValue'])

/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => local.or51898.startTime as Mo01272Type,
  (newValue) => {
    if (newValue.value) {
      const startTime = newValue.value.split(':')
      let isMustChange = false
      if (Number(startTime[1]) > 59) {
        startTime[1] = '59'
        isMustChange = true
      }
      if (Number(startTime[0]) > 24) {
        startTime[0] = '24'
        isMustChange = true
      }
      if (isMustChange) {
        local.or51898.startTime = {
          value: startTime[0] + ':' + startTime[1],
        }
      }
    }
  }
)

watch(
  () => local.or51898.endTime as Mo01272Type,
  (newValue) => {
    if (newValue.value) {
      const endTime = newValue.value.split(':')
      let isMustChange = false
      if (Number(endTime[1]) > 59) {
        endTime[1] = '59'
        isMustChange = true
      }
      if (Number(endTime[0]) > 24) {
        endTime[0] = '24'
        isMustChange = true
      }
      if (isMustChange) {
        local.or51898.endTime = {
          value: endTime[0] + ':' + endTime[1],
        }
      }
    }
  }
)
</script>
<template>
  <div class="stack-overlap">
    <div class="mo01299-position">
      <base-mo01299
        :oneway-model-value="localOneway.mo01299ListOneway"
        style="height: 100%"
      >
        <template #content>
          <div class="pt-1 pr-1 pl-1 pb-1">
            <c-v-row no-gutters>
              <g-custom-or-26194
              v-model:start-time="startTime"
              v-model:end-time="endTime"
              class="pr-1"
            />
            </c-v-row>
          </div>
        </template>
      </base-mo01299>
    </div>
    <div class="text-required-position">
      {{ t('label.required') }}
    </div>
    <div class="time-zone-input-support-position">
      <div style="border-left: solid thin rgb(var(--v-theme-black-200))">
        <base-mo00009
          :oneway-model-value="localOneway.timeZoneInputSupportBtn"
          @click.stop="emit('onClick')"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use '@/styles/base.scss';

.stack-overlap {
  position: relative;
  width: 100%;
  height: 54px;
  align-content: center;
  align-self: center;
  align-items: center;
}

.mo01299-position {
  z-index: 1;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  align-content: center;
  align-self: center;
  align-items: center;
}

.time-zone-input-support-position {
  z-index: 3;
  position: absolute;
  align-content: center;
  align-self: center;
  align-items: center;
  left: 130px;
  top: 50%;
  transform: translate(-50%, -50%);
}
.text-required-position {
  z-index: 2;
  position: absolute;
  align-content: center;
  align-self: center;
  align-items: center;
  left: 70px;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 10px;
  color: rgb(var(--v-theme-error));
}
</style>
