import { getSequencedCpId } from '#imports'

/**
 * Or33787:印刷設定モーダル
 * GUI01184_印刷設定
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> 劉顕康
 */
export namespace Or33787Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or33787', seq)

  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * 固定文字セット
     */
    export namespace STR {
      /**
       * 空文字
       */
      export const EMPTY = ''
    }
  }

  /**
   * 選択モード 12(※条件無検索)
   */
  export const SELECT_MODE_12 = '12'

  /**
   * 未設定行表示フラグ 1(※表示する)
   */
  export const MISETTEI_SHOW_FLG_1 = '1'

  /**
   * フィルターフラグ 1(※項目絞込みパターン)
   */
  export const FILTER_FLG_1 = '1'

  /**
   * 表示するカラム
   */
  export const SHOW_COL_VALUE = 'shokushu_id'

  /**
   * 出力帳票名 給付状況一覧(個別)
   */
  export const OUTPUT_LEDGER_NAME_INDIVIDUAL = '給付状況一覧(個別)'

  /**
   * 個人情報表示フラグ 0
   */
  export const PERSONAL_INFO_SHOW_FLG_0 = '0'

  /**
   * 個人情報表示値 0
   */
  export const PERSONAL_INFO_SHOW_VALUE_0 = '0'

  /**
   * 機能名 PRT
   */
  export const FUNCTION_NAME_PRINT = 'PRT'

  /**
   * システム略称 3GK
   */
  export const SYSTEM_NAME_SHORT = '3GK'

  /**
   * 日付印刷区分 2：指定日を印刷する
   */
  export const DATE_PRINT_CATEGORY_2 = '2'

  /**
   * 給付状況一覧(個別)：帳票ID
   */
  export const CHOUHYOU_ID_KOBETU = 'benefitStatusKobetsuListReport'

  /**
   * 給付状況一覧：帳票ID
   */
  export const CHOUHYOU_ID = 'benefitStatusListReport'
}
