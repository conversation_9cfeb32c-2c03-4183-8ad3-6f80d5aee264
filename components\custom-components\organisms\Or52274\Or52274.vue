<script setup lang="ts">
/**
 * Or52274:(会議録取込)計画期間一覧
 * GUI01260_会議録取込
 *
 * @description
 * (会議録取込)計画期間一覧
 *
 * <AUTHOR> PHAM HO HAI DANG
 */
import { reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type {
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { Or52274Const } from '~/components/custom-components/organisms/Or52274/Or52274.constants'
import { useCommonProps } from '~/composables/useCommonProps'
import { useScreenOneWayBind } from '~/composables/useComponentVue'
import type { Or52274StateType } from '~/types/cmn/business/components/Or52274Type'

/**
 * i18nの初期化
 */
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
/**
 * 共通Propsを定義
 */
const props = defineProps(useCommonProps())

/**
 * ステートとの双方向バインド設定
 */
const { setState } = useScreenOneWayBind<Or52274StateType>({
  cpId: Or52274Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * ステート「items」が更新された時、ローカル変数に反映
     *
     * @param value - 更新値
     */
    items: (value) => {
      if (!value) {
        return
      }

      local.mo01354.values.items.splice(0)
      value.forEach((item) => {
        const tableItem = {
          id: item.sc1Id,
          startEndYmdPeriod: ref(item.startEndYmdPeriod),
          dmyCntPeriod: ref(item.dmyCntPeriod),
          selectable: true,
        }

        local.mo01354.values.items.push(tableItem)
      })
    },
    /**
     * ステート「selectedItemId」が更新された時、ローカル変数に反映
     *
     * @param value - 選択中のID
     */
    selectedItemId: (value) => {
      if (value && value !== local.mo01354.values.selectedRowId) {
        local.mo01354.values.selectedRowId = value
      }
    },
  },
})

// ⑤ウォッチャー（必要に応じて実装）

/**************************************************
 * Emit
 **************************************************/
// ※現在未実装、必要に応じて追加

/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**
 * テーブルのローカル状態（バインド用）
 */
const local = reactive({
  mo01354: {
    values: {
      selectedRowId: '',
      selectedRowIds: [],
      items: [],
    },
  } as unknown as Mo01354Type,
})

/**
 * テーブルの外観・表示設定
 */
const localOneway = reactive({
  mo01354: {
    height: '102px',
    showPaginationTopFlg: false,
    showPaginationBottomFlg: false,
    useDefaultHeader: false,
    headers: [
      {
        title: t('label.plan-period'),
        key: 'startEndYmdPeriod',
        sortable: false,
        width: '190px', // Vuetify Data Tableパラメータ
      },
      {
        title: t('label.within-the-period-number-of-history'),
        key: 'dmyCntPeriod',
        sortable: false,
        width: '135px', // Vuetify Data Tableパラメータ
      },
    ],
  } as unknown as Mo01354OnewayType,
})

/**
 * 選択行変更
 */
watch(
  () => local.mo01354.values.selectedRowId,
  () => {
    setState({ selectedItemId: local.mo01354.values.selectedRowId })
  }
)

/**************************************************
 * ライフサイクルフック
 **************************************************/
</script>

<template>
  <c-v-sheet class="table-wrapper">
    <base-mo01354
      v-model="local.mo01354"
      :oneway-model-value="localOneway.mo01354"
      hide-default-footer
      hide-no-data
    >
      <template #[`item.dmyCntPeriod`]="{ item }">
        <span class="d-flex w-100 align-end justify-end">{{ item.dmyCntPeriod }}</span>
      </template>
    </base-mo01354>
  </c-v-sheet>
</template>

<style lang="scss" scoped>
@use '@/styles/cmn/dialog-data-table-list.scss';

.table-wrapper :deep(.v-table__wrapper .selected-row) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}
</style>
