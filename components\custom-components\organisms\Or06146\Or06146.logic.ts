import { Or15169Const } from '../Or15169/Or15169.constants'
import { Or15194Const } from '../Or15194/Or15194.constants'
import { Or15169Logic } from '../Or15169/Or15169.logic'
import { Or15194Logic } from '../Or15194/Or15194.logic'

// import { Or30798Logic } from '../Or30798/Or30798.logic'
import type { TeX0002EventType } from '../../template/TeX0002/TeX0002.type'
import { Or15133Const } from '../Or15133/Or15133.constants'
import { Or15133Logic } from '../Or15133/Or15133.logic'
import { Or15183Const } from '../Or15183/Or15183.constants'
import { Or15183Logic } from '../Or15183/Or15183.logic'
import { Or15141Const } from '../Or15141/Or15141.constants'
import { Or15141Logic } from '../Or15141/Or15141.logic'
import { Or28287Logic } from '../Or28287/Or28287.logic'
import { Or28287Const } from '../Or28287/Or28287.constants'
import { Or06146Const } from './Or06146.constants'
import type { Or06146CopyDataType, Or06146StateType } from './Or06146.type'
import {
  useEventStatusAccessor,
  useInitialize,
  useOneWayBindAccessor,
  useTwoWayBindAccessor,
} from '~/composables/useComponentLogic'

/**
 * Or06146Const:有機体:［退院・退所情報記録書］画面
 * 処理ロジック
 *
 * @description
 * initialize処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or06146Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or06146Const.CP_ID(0),
      uniqueCpId,
      childCps: [
        { cpId: Or28287Const.CP_ID(1) },
        // { cpId: Or15169Const.CP_ID(1) },
        // { cpId: Or15194Const.CP_ID(1) },
        // { cpId: Or15133Const.CP_ID(1) },
        // { cpId: Or15183Const.CP_ID(1) },
        // { cpId: Or15141Const.CP_ID(1) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or28287Logic.initialize(childCpIds[Or28287Const.CP_ID(1)].uniqueCpId)
    // Or15169Logic.initialize(childCpIds[Or15169Const.CP_ID(1)].uniqueCpId)
    // Or15194Logic.initialize(childCpIds[Or15194Const.CP_ID(1)].uniqueCpId)
    // Or15133Logic.initialize(childCpIds[Or15133Const.CP_ID(1)].uniqueCpId)
    // Or15183Logic.initialize(childCpIds[Or15183Const.CP_ID(1)].uniqueCpId)
    // Or15141Logic.initialize(childCpIds[Or15141Const.CP_ID(1)].uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or06146StateType>(Or06146Const.CP_ID(0))

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<Or06146CopyDataType>(Or06146Const.CP_ID(0))
  /**
   * 自身のEventStatus領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const event = useEventStatusAccessor<TeX0002EventType>(Or06146Const.CP_ID(1))
}
