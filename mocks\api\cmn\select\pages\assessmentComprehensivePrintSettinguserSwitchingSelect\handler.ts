import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { AssessmentComprehensivePrintSettinguserSwitchingSelectInEntity } from '~/repositories/cmn/entities/AssessmentComprehensivePrintSettingsInitialSelectEntity'

/**
 * GUI00842_印刷設定
 *
 * @description
 * GUI00842_アセスメント履歴情報を取得する
 * dataName："assessmentComprehensivePrintSettinguserSwitchingSelect"
 */
export function handler(inEntity: AssessmentComprehensivePrintSettinguserSwitchingSelectInEntity) {
  let result
  if (inEntity.userId === '1') {
    result = defaultData.data1
  } else if (inEntity.userId === '2') {
    result = defaultData.data2
  } else if (inEntity.userId === '12') {
    result = defaultData.data1
  } else if (inEntity.userId === '25') {
    result = defaultData.data2
  } else {
    result = defaultData.data3
  }
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...result,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
