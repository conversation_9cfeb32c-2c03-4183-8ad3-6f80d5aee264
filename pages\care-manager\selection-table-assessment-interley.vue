<script setup lang="ts">
/**
 * GUI00848_選定表(アセスメント(インターライ))
 *
 * @description
 * 選定表(アセスメント(インターライ))
 *
 * <AUTHOR> DAM XUAN HIEU
 */
import { definePageMeta, ref, useScreenStore } from '#imports'
import { Or11752Const } from '~/components/custom-components/template/Or11752/Or11752.constants'
import { Or11752Logic } from '~/components/custom-components/template/Or11752/Or11752.logic'
import type { Or11752OnewayType } from '~/types/cmn/business/components/Or11752Type'
import { useCmnCom } from '~/utils/useCmnCom'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * 変数定義
 **************************************************/
/**
 * 一方向データ
 */
const or11752OnewayType = {
  styleFlag: '1',
  planPeriodFlag: '1',
} as Or11752OnewayType

/**
 * 画面ID
 */
const screenId = 'GUI00848'

/**
 * ルーティング
 */
const routing = 'care-manager/selection-table-assessment-interley'

/**
 * 画面物理名
 */
const screenName = 'selection-table-assessment-interley'

/**
 * 画面状態管理用操作変数
 */
const screenStore = useScreenStore()

/**
 * GUI00848
 */
const GUI00848 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 ***********************************************/
/**
 * piniaの画面領域を初期化する
 */
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: Or11752Const.CP_ID(0) },
})

/**
 * piniaから最上位の画面コンポーネント情報を取得する
 * これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
 */
const pageComponent = screenStore.screen().supplement.pageComponent

/**
 * コンポーネントの初期化処理を開始する
 */
Or11752Logic.initialize(pageComponent.uniqueCpId)

/**
 * 子コンポーネントのユニークIDを設定する
 */
GUI00848.value.uniqueCpId = pageComponent.uniqueCpId

/**
 * ケアマネ画面を初期化する
 */
await useCmnCom().initialize({
  screenId,
  routing,
})
</script>
<template>
  <g-custom-or-11752
    :="GUI00848"
    :oneway-model-value="or11752OnewayType"
  ></g-custom-or-11752>
</template>
