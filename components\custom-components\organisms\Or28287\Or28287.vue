<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28287Const } from './Or28287.constants'
import type { DataInfoType, Or28287StateType } from './Or28287.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or28287OnewayType, Or28287Type } from '~/types/cmn/business/components/Or28287Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { useScreenOneWayBind } from '#imports'
import type {
  NinteiInfoSelectInEntity,
  NinteiInfoSelectOutEntity,
} from '~/repositories/cmn/entities/NinteiInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'

/**
 * Or28287:有機体:介護保険 認定情報選択モーダル
 * GUI00649_［認定情報選択］画面
 *
 * @description
 *［認定情報選択］画面では、認定情報選択画面を表示します。
 *
 * <AUTHOR> 沈溢良
 */

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or28287Type
  onewayModelValue: Or28287OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or28287OnewayType = {
  // 利用者ID
  userId: '',
}

const localOneway = reactive({
  Or28287: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定ボタン
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  //［認定情報選択］画面
  mo00024Oneway: {
    width: '800px',
    height: '412px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or28287',
      toolbarTitle: t('label.certification-information-select'),
      toolbarName: 'Or28287ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00009Oneway: {
    btnIcon: 'database',
  } as Mo00009OnewayType,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28287Const.DEFAULT.IS_OPEN,
})

// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

const or21815 = ref({ uniqueCpId: '' })

// 認定情報一覧初期情報
const tableData = ref<DataInfoType>({
  ninteiInfo: [],
})

const tableDataFilter = computed(() => {
  const ninteiInfo = tableData.value.ninteiInfo
  return { ninteiInfo: ninteiInfo }
})

const originData = {
  ninteiInfoType: [] as Or28287Type[],
}

// テーブルヘッダ
const headers = [
  // 保険者
  {
    title: t('label.insurer'),
    width: '140px',
    align: 'left',
    sortable: false,
    key: 'hokensya',
  },
  // 被保険者番号
  {
    title: t('label.insured-person-number-1'),
    width: '120px',
    align: 'left',
    sortable: false,
    key: 'hHokenNo',
  },
  // 認定期間
  {
    title: t('label.certification-eriod'),
    width: '200px',
    align: 'left',
    sortable: false,
    key: 'certificationEriod',
  },
  // 要介護度
  {
    title: t('label.yokai-knj'),
    width: '100px',
    align: 'left',
    sortable: false,
    key: 'yokaiKnj',
  },
  // 限度額
  {
    title: t('label.limit'),
    width: '100px',
    align: 'right',
    sortable: false,
    key: 'limit',
  },
]

// マスタアイコンボタン
// const masterBtnMo00009 = ref<Mo00009OnewayType>({
//   btnIcon: 'database',
//   icon: true,
//   width: '32px',
//   disabled: false,
// })

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or28287StateType>({
  cpId: Or28287Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or28287Const.DEFAULT.IS_OPEN
    },
  },
})

const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await init()
})

/**
 * 認定情報一覧初期情報取得
 */
async function init() {
  const inputData: NinteiInfoSelectInEntity = {
    /** ユーザID */
    userId: props.onewayModelValue.userId,
  }
  // 認定情報一覧初期情報取得
  const ret: NinteiInfoSelectOutEntity = await ScreenRepository.select(
    'certificationInfoSelect',
    inputData
  )
  const sortNinteiList = [...ret.data.ninteiList].sort((a, b) => {
    //認定有効開始日 降順
    if (a.dispStartYmd > b.dispStartYmd) return -1
    if (a.dispStartYmd < b.dispStartYmd) return 1
    return 0
  })

  for (const acquInfo of sortNinteiList) {
    tableData.value.ninteiInfo.push({
      // 保険者
      hokensya: acquInfo.kHokenKnj,
      // 被保険者番号
      hHokenNo: acquInfo.hHokenNo,
      // 認定期間"認定情報リスト.認定有効開始日（表示用）+'~'+認定情報リスト.認定有効終了日（表示用）"
      certificationEriod: acquInfo.dispStartYmd + Or28287Const.DEFAULT.WAVE + acquInfo.dispEndYmd,
      // 要介護度
      yokaiKnj: acquInfo.yokaiKnj,
      // 限度額
      limit: acquInfo.tusho1Gendo,
    })
  }

  for (const acquInfo of ret.data.ninteiList) {
    originData.ninteiInfoType.push({
      // 保険者
      hokensya: acquInfo.kHokenKnj,
      // 被保険者番号
      hHokenNo: acquInfo.hHokenNo,
      // 認定有効開始日（表示用）
      certificationValidityStartDate: acquInfo.dispStartYmd,
      // 認定有効終了日（表示用）
      certificationValidityEndDate: acquInfo.dispEndYmd,
      // 要介護度
      yokaiKnj: acquInfo.yokaiKnj,
      // 限度額
      limit: acquInfo.tusho1Gendo,
    })
  }

  if (tableData.value.ninteiInfo.length > 0) {
    onSelectRow(0)
  }
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function onSelectRow(index: number) {
  selectedItemIndex.value = index
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 *  認定情報一覧に行が選択されない場合
 */
function onClick_Confirm() {
  if (selectedItemIndex.value === -1) {
    showOr21815Msg(t('message.w-cmn-20742'))
  } else if (selectedItemIndex.value !== -1) {
    rtnDataSet()
  }
}

/**
 * 明細行ダブルクリック
 */
function doubleClickSelectRow() {
  rtnDataSet()
}

/**
 * 入力エラーを表示する
 *
 * @param errorMsg - メッセージ内容
 */
function showOr21815Msg(errorMsg: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      dialogTitle: t('label.warning'),
      dialogText: errorMsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

//認定情報一覧に行が選択されない場合の選択結果を聞きます
watch(
  () => Or21815Logic.event.get(or21815.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.thirdBtnClickFlg) {
      return
    }
  }
)

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

// 戻り値設定
function rtnDataSet() {
  // 戻り値設定
  const rtnData: Or28287Type = {
    // 保険者
    hokensya: originData.ninteiInfoType[selectedItemIndex.value].hokensya,
    // 被保険者番号
    hHokenNo: originData.ninteiInfoType[selectedItemIndex.value].hHokenNo,
    // 認定有効開始日（表示用）
    certificationValidityStartDate:
      originData.ninteiInfoType[selectedItemIndex.value].certificationValidityStartDate,
    // 認定有効終了日（表示用）
    certificationValidityEndDate:
      originData.ninteiInfoType[selectedItemIndex.value].certificationValidityEndDate,
    // 要介護度
    yokaiKnj: originData.ninteiInfoType[selectedItemIndex.value].yokaiKnj,
    // 限度額
    limit: originData.ninteiInfoType[selectedItemIndex.value].limit,
  }
  // 選択情報値戻り
  emit('update:modelValue', rtnData)
  // 画面閉じる
  close()
}

// 限度額format
function amtFormat(num: number) {
  if (isNaN(num)) return
  const [integerPart, decimalPart] = Number(num).toFixed(0).split('.')
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return decimalPart ? `${formattedInteger}` + '.' + `${decimalPart}` : formattedInteger
}

/**
 * マスタ他ボタンクリック
 *
 * @description
 * 自身のState領域のフラグを更新する。
 */
// TODO 遷移先未実装
// function masterBtnClick() {}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <!--
      <div class="mo-1">
        <c-v-row no-gutters>
          <c-v-col>
            <div class="flex-end">
              <base-mo00009
                :oneway-model-value="masterBtnMo00009"
                @click="masterBtnClick"
              >
                <c-v-tooltip
                  :text="t('tooltip.others-function')"
                  activator="parent"
                  location="bottom"
                >
                </c-v-tooltip>
              </base-mo00009>
            </div>
          </c-v-col>
        </c-v-row>
      </div>
      -->
      <c-v-row>
        <!-- マスタアイコンボタン -->
        <c-v-col>
          <c-v-data-table
            :headers="headers"
            class="table-wrapper"
            hide-default-footer
            :items="tableDataFilter.ninteiInfo"
            fixed-header
            hover
            height="294px"
            :items-per-page="-1"
          >
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="onSelectRow(index)"
                @dblclick="doubleClickSelectRow()"
              >
                <!-- 保険者 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{ value: item.hokensya }"
                    style="width: 100%"
                  />
                </td>
                <!-- 被保険者番号 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{ value: item.hHokenNo }"
                    style="width: 100%"
                  />
                </td>
                <!-- 認定期間 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{ value: item.certificationEriod }"
                    style="width: 100%"
                  />
                </td>
                <!-- 要介護度 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{ value: item.yokaiKnj }"
                    style="width: 100%;"
                  />
                </td>
                <!-- 限度額 -->
                <td>
                  <base-mo01336
                    :oneway-model-value="{ value: amtFormat(item.limit) }"
                    style="width: 100%"
                  />
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row
        no-gutters
        style="padding-right: 8px"
      >
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          @click="onClick_Confirm"
        >
          <!--ツールチップ表示："設定を確定します。"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
    <!-- ワーニングダイアログを表示する。 -->
    <g-base-or21815
      v-if="showDialogOr21815"
      v-bind="or21815"
    ></g-base-or21815>
  </base-mo00024>
</template>
<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
</style>
