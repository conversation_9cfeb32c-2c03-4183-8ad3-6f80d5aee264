<script setup lang="ts">
/**
 * Or33396:［フェースシート複写］画面
 * GUI00639_［フェースシート複写］画面
 *
 * @description
 * ［フェースシート複写］画面
 *
 * <AUTHOR>
 */
import { cloneDeep } from 'lodash'
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or33216Const } from '~/components/custom-components/organisms/Or33216/Or33216.constants'
import { Or33216Logic } from '~/components/custom-components/organisms/Or33216/Or33216.logic'
import { Or33396Const } from '~/components/custom-components/organisms/Or33396/Or33396.constants'
import { Or33396Logic } from '~/components/custom-components/organisms/Or33396/Or33396.logic'
import { Or33423Const } from '~/components/custom-components/organisms/Or33423/Or33423.constants'
import { Or33423Logic } from '~/components/custom-components/organisms/Or33423/Or33423.logic'
import { Or35170Const } from '~/components/custom-components/organisms/Or35170/Or35170.constants'
import { Or35170Logic } from '~/components/custom-components/organisms/Or35170/Or35170.logic'
import { Or35478Const } from '~/components/custom-components/organisms/Or35478/Or35478.constants'
import { Or35486Const } from '~/components/custom-components/organisms/Or35486/Or35486.constants'
import { Or35486Logic } from '~/components/custom-components/organisms/Or35486/Or35486.logic'
import { Or35487Const } from '~/components/custom-components/organisms/Or35487/Or35487.constants'
import { Or35487Logic } from '~/components/custom-components/organisms/Or35487/Or35487.logic'
import { OrX0142Const } from '~/components/custom-components/organisms/OrX0142/OrX0142.constants'
import { OrX0142Logic } from '~/components/custom-components/organisms/OrX0142/OrX0142.logic'
import { useCommonProps } from '~/composables/useCommonProps'
import {
  useScreenEventStatus,
  useScreenOneWayBind,
  useSetupChildProps,
} from '~/composables/useComponentVue'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  FaceSheet3SelectInEntity,
  FaceSheet3SelectSelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheet3SelectEntity'
import type {
  FaceSheetCopyHistoryInfo,
  FaceSheetCopyHistorySelectInEntity,
  FaceSheetCopyHistorySelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheetCopyHistorySelectEntity'
import type {
  FaceSheetCopyKikanInfo,
  FaceSheetCopyKikanSelectInEntity,
  FaceSheetCopyKikanSelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheetCopyKikanSelectEntity'
import type {
  FaceSheetCopyUpdateInEntity,
  FaceSheetCopyUpdateOutEntity,
} from '~/repositories/cmn/entities/FaceSheetCopyUpdateEntity'
import type {
  FaceSheetPackage1InitSelectInEntity,
  FaceSheetPackage1InitSelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheetPackage1InitSelectEntity'
import type {
  FaceSheetPackage2InitSelectInEntity,
  FaceSheetPackage2InitSelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheetPackage2InitSelectEntity'
import type {
  FaceSheetPackage4InitSelectEntity,
  FaceSheetPackage4InitSelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheetPackage4InitSelectEntity'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Or33423OnewayType } from '~/types/cmn/business/components/Or33423Type'
import type {
  Or35478EventType,
  Or35478InParam,
  Or35478StateType,
} from '~/types/cmn/business/components/Or35478Type'
import type { Or35486ItemData } from '~/types/cmn/business/components/Or35486Type'
import type { Or35487ItemData } from '~/types/cmn/business/components/Or35487Type'
import type { OrX0142Data } from '~/types/cmn/business/components/OrX0142Type'
import { useUserListInfo } from '~/utils/useUserListInfo'

const { t } = useI18n()
// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * Props
 **************************************************/
// ①インターフェース定義
const props = defineProps(useCommonProps())

// ②デフォルト定義
const defaultOneway = reactive({
  // チェックボックス
  mo00018: {
    showItemLabel: false,
  } as Mo00018OnewayType,
  // ラベル
  mo01338: {
    customClass: { itemClass: 'align-center' },
  } as Mo01338OnewayType,
})

// ③双方向バインド用の内部変数

// ④片方向バインド用の内部変数
const { setState } = useScreenOneWayBind<Or35478StateType>({
  cpId: Or35478Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    // ステート領域に更新があった場合、ローカル変数を更新
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or35478Const.DEFAULT.IS_OPEN

      if (value) {
        //初期化
        void nextTick(() => {
          doInitAction()
        })
      }
    },
    inParam: (value) => {
      if (!value) {
        return
      }

      inParam.value = value
    },
  },
})
const { setEvent } = useScreenEventStatus<Or35478EventType>({
  cpId: Or35478Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

const localOneway = reactive({
  // 閉じるボタン
  mo00611: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定ボタン
  mo00609: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  // ダイアログ
  mo00024: {
    width: '96vw',
    minWidth: '1200px',
    maxWidth: '100%',
    height: '704px',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: true,
    mo01344Oneway: {
      name: 'Or35478',
      toolbarTitle: t('label.face-sheet-copy'),
      toolbarName: 'Or35478ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      cardTextClass: 'card-text pa-0',
    },
  } as Mo00024OnewayType,
  // ラベル
  mo01338CopyWarn: {
    ...defaultOneway.mo01338,
    value: t('label.face-sheet-copy-warn-msg'),
  } as Mo01338OnewayType,
  mo01338CopyAllWarn: {
    ...defaultOneway.mo01338,
    value: t('label.face-sheet-copy-all-warn-msg'),
  } as Mo01338OnewayType,
  // フェースシート①
  faceSheet1: { ...cloneDeep(Or33216Const.DEFAULT.ONE_WAY), isCopy: true },
  // フェースシート①
  faceSheet2: { ...cloneDeep(Or35170Const.DEFAULT.ONE_WAY), isCopy: true },
  // フェースシート③
  faceSheet3: { ...cloneDeep(Or33396Const.DEFAULT.ONE_WAY), isCopy: true },
  // フェースシート④
  faceSheet4: {
    svJigyoId: 1,
    userId: '',
    surveyAssessmentKind: '',
  } as Or33423OnewayType,
})

// ⑤ウォッチャー

/**************************************************
 * Emit
 **************************************************/

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or00248 = ref({ uniqueCpId: '' }) // 使用者
const or00094 = ref({ uniqueCpId: '' }) // 五十音ヘッドライン
const or33216 = ref({ uniqueCpId: '' }) // フェースシート①
const or35170 = ref({ uniqueCpId: '' }) // フェースシート②
const or33396 = ref({ uniqueCpId: '' }) // フェースシート③
const or33423 = ref({ uniqueCpId: '' }) // フェースシート④
const or35486 = ref({ uniqueCpId: '' }) // 計画期間テーブル
const or35487 = ref({ uniqueCpId: '' }) // 履歴テーブル
const orX0142 = ref({ uniqueCpId: '' }) // 複写画面一覧
const or21814 = ref({ uniqueCpId: '' }) // インフォメーショダイアログ

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(1)]: or00248.value,
  [Or33216Const.CP_ID(1)]: or33216.value,
  [Or35170Const.CP_ID(1)]: or35170.value,
  [Or33396Const.CP_ID(1)]: or33396.value,
  [Or33423Const.CP_ID(1)]: or33423.value,
  [Or35486Const.CP_ID(1)]: or35486.value,
  [Or35487Const.CP_ID(1)]: or35487.value,
  [OrX0142Const.CP_ID(1)]: orX0142.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
})
useSetupChildProps(or00248.value.uniqueCpId, {
  [Or00094Const.CP_ID(0)]: or00094.value,
})

// （利用者基本）利用者一覧詳細表示の初期設定
Or00248Logic.state.set({
  uniqueCpId: or00248.value.uniqueCpId,
  state: {
    regionKey: props.uniqueCpId, // メイン画面と連動しないようにする
    displayUserInfoSectionFlg: false, // 利用者情報セクションを表示しない
  },
})
// ★利用者選択監視関数を実行
useUserListInfo().syscomUserSelectWatchFunc((newSelfId) => {
  /**
   * AC003
   * 「利用者」選択変更
   */
  if (newSelfId !== '') {
    selectedUserId.value = newSelfId

    // [利用者ID]の変更により、計画対象期間情報取得を行う
  }
}, props.uniqueCpId)

// ダイアログ
const mo00024 = ref<Mo00024Type>({
  isOpen: Or35478Const.DEFAULT.IS_OPEN,
})
watch(mo00024, () => {
  if (mo00024.value.emitType === 'closeBtnClick') {
    mo00024.value.emitType = 'blank'
    doCloseAction()
  }
})

/** INパラメータ */
const inParam = ref<Or35478InParam>({ ...Or35478Const.DEFAULT.IN_PARAM })
/** 計算期間情報リスト */
const planDataInfos = ref<FaceSheetCopyKikanInfo[]>([])
/** 履歴情報リスト */
const historyDataInfos = ref<FaceSheetCopyHistoryInfo[]>([])
/** タブチェックリスト */
const tabChecks = ref<OrX0142Data[]>([])
/** 利用者ID */
const selectedUserId = ref<string>('')
watch(selectedUserId, () => {
  if (!selectedUserId.value) {
    return
  }

  if (showKiKanFlag.value) {
    // 計画対象期間情報を取得する
    fetchPlanDataFlg.value = true
  } else {
    // 履歴データ取得フラグ設定
    fetchHistoryDataFlg.value = true
  }
})
/** コピー元の計画期間ID */
const selectedSc1Id = ref<string>('')
watch(selectedSc1Id, () => {
  if (!selectedSc1Id.value) {
    return
  }

  // 履歴データ取得フラグ設定
  fetchHistoryDataFlg.value = true
})
/** コピー元のフェースシート履歴ID */
const selectedFaceId = ref<string>('')
watch(selectedFaceId, () => {
  if (!selectedFaceId.value) {
    return
  }

  // タブデータ取得フラグ設定
  fetchTabDataFlg.value = true
})
/** 変数.改訂フラグ */
const kaiteiFlg = ref<string>('')
/** 変数.画面表示フラグ */
const displayFlg = ref<number>(Or35478Const.DISPLAY_TYPE_2)

/** 計画期間データ取得フラグ */
const fetchPlanDataFlg = ref<boolean>(false)
watch(fetchPlanDataFlg, async () => {
  if (!fetchPlanDataFlg.value) {
    return
  }

  await fetchPlanData().finally(() => {
    fetchPlanDataFlg.value = false
  })
})
/** 履歴データ取得フラグ */
const fetchHistoryDataFlg = ref<boolean>(false)
watch(fetchHistoryDataFlg, async () => {
  if (!fetchHistoryDataFlg.value) {
    return
  }

  await fetchHistoryData().finally(() => {
    fetchHistoryDataFlg.value = false
  })
})
/** タブデータ取得フラグ */
const fetchTabDataFlg = ref<boolean>(false)
watch(fetchTabDataFlg, async () => {
  if (!fetchTabDataFlg.value) {
    return
  }

  await fetchTabData().finally(() => {
    fetchTabDataFlg.value = false
  })
})
/** 計画期間選択一覧表示フラグ */
const showKiKanFlag = computed(() => {
  // ・初期表示：期間管理フラグが「管理しない」の場合、非表示
  return !(inParam.value.kikanFlg === Or35478Const.PERIOD_MANAGE_NO)
})
/** 複写セクション表示フラグ */
const showCopySectionFlag = computed(() => {
  return historyDataInfos.value.length > 0
})
/** [全ての項目を一括して複写します]ラベル表示フラグ */
const showCopyAllWarnFlag = computed(() => {
  // 変数.画面表示フラグ = 0の場合、表示
  // それ以外の場合、非表示
  return displayFlg.value === Or35478Const.DISPLAY_TYPE_0
})
/** 複写画面一覧表示フラグ */
const showCopyFaceSheetFlag = computed(() => {
  // ・変数.画面表示フラグ = 0の場合、非表示
  return displayFlg.value !== Or35478Const.DISPLAY_TYPE_0
})
/** 詳細セクション表示フラグ */
const showCopyFaceSheetTabFlag = computed(() => {
  // ・変数.画面表示フラグ = 0 or 1の場合、非表示
  return (
    displayFlg.value !== Or35478Const.DISPLAY_TYPE_0 &&
    displayFlg.value !== Or35478Const.DISPLAY_TYPE_1
  )
})

/**
 * AC001
 * 初期表示
 */
const doInitAction = () => {
  // Eventフラグをクリア
  setEvent({ singleConfirmFlg: false, multiConfirmFlg: false })

  // 五十音ヘッドラインの初期設定
  systemCommonsStore.setUserSelectFilterInitials([Or35478Const.STR_ALL], props.uniqueCpId)
  // 利用者番号の選択値を設定
  systemCommonsStore.setUserSelectSelfId(inParam.value.userId, props.uniqueCpId)

  selectedUserId.value = inParam.value.userId

  // [利用者ID]の変更により、計画対象期間情報取得を行う
}

/**
 * 計画期間データ取得
 */
const fetchPlanData = async () => {
  resetPlanData()
  resetHistoryData()

  // 計画対象期間情報を取得する。
  // 利用者ID：変数.利用者ID
  // 事業所ID：親画面.事業所ID
  // 施設ID：親画面.施設ID
  // 種別ID：親画面.種別ID
  const req: FaceSheetCopyKikanSelectInEntity = {
    /** 種別ID */
    syubetuId: inParam.value.syubetuId,
    /** 適用事業所IDリスト */
    jigyoIdList: inParam.value.jigyoIdList.map((item) => ({ jigyoId: item })),
    /** 施設ID */
    shisetuId: inParam.value.shisetuId,
    /** 利用者ID */
    userid: selectedUserId.value,
  }
  // ・期間情報
  const res: FaceSheetCopyKikanSelectOutEntity = await ScreenRepository.select(
    'faceSheetCopyKikanSelect',
    req
  )

  if (Or35478Const.RES_SUCCESS === res.statusCode && res.data) {
    planDataInfos.value = [...res.data.kikanInfoList]
    const planInfos: Or35486ItemData[] = []
    res.data.kikanInfoList?.forEach((kikanInfo) => {
      planInfos.push({
        /** 計画期間ID */
        sc1Id: kikanInfo.sc1Id,
        /** 開始日 */
        startYmd: kikanInfo.startYmd,
        /** 終了日 */
        endYmd: kikanInfo.endYmd,
        /** 履歴件数 */
        rirekiCnt: kikanInfo.cnt,
        /** 事業者名称(略称) */
        jigyoRyakuKnj: kikanInfo.jigyoRyakuKnj,
      })
    })

    // 一行目を選択
    selectedSc1Id.value = planInfos[0]?.sc1Id ?? ''

    // 計画期間
    Or35486Logic.state.set({
      uniqueCpId: or35486.value.uniqueCpId,
      state: {
        items: planInfos,
        selectedItemId: selectedSc1Id.value,
      },
    })

    // [計画期間ID]の変更により、フェースシート履歴データ取得を行う
  }
}

/**
 * フェースシート履歴データ取得
 */
const fetchHistoryData = async () => {
  resetHistoryData()

  // 履歴情報を取得する。
  // 計画期間ID：変数.計画期間ID
  // 利用者ID：変数.利用者ID
  // 事業所ID：親画面.事業所ID
  // 適用事業所IDリスト：親画面.適用事業所IDリスト
  // 期間管理フラグ：親画面.期間管理フラグ
  const req: FaceSheetCopyHistorySelectInEntity = {
    /** 計画期間ID */
    sc1Id: selectedSc1Id.value || '0',
    /** 事業所ID */
    svjigyoId: inParam.value.svJigyoId,
    /** 利用者ID */
    userid: selectedUserId.value,
    /** 期間管理フラグ */
    kikanFlg: inParam.value.kikanFlg,
    /** 適用事業所IDリスト */
    jigyoIdList: inParam.value.jigyoIdList.map((item) => ({ jigyoId: item })),
  }
  // ・履歴情報リスト
  // ・タブチェックリスト
  const res: FaceSheetCopyHistorySelectOutEntity = await ScreenRepository.select(
    'faceSheetCopyHistorySelect',
    req
  )

  if (Or35478Const.RES_SUCCESS === res.statusCode && res.data) {
    historyDataInfos.value = [...res.data.historyInfoList]
    const historyInfos: Or35487ItemData[] = []
    res.data.historyInfoList.forEach((historyInfo) => {
      historyInfos.push({
        /** フェースシート履歴ID */
        faceId: historyInfo.faceId,
        /** 作成日 */
        createYmd: historyInfo.create_ymd,
        /** 作成者名 */
        shokuName: historyInfo.shokuKnj,
        /** ケース番号 */
        caseNo: historyInfo.caseNo,
        /** 変更回数 */
        henkoKaisu: historyInfo.henkoKaisu,
        /** 改訂フラグ */
        kaiteiFlg: historyInfo.kaiteiFlg,
        /** 事業者ID */
        jigyoRyakuKnj: historyInfo.jigyoRyakuKnj,
      })
    })
    res.data.tabCheckList.forEach((tabCheck) => {
      tabChecks.value.push({
        tabCheck1: tabCheck.check1,
        tabCheck2: tabCheck.check2,
        tabCheck3: tabCheck.check3,
        tabCheck4: tabCheck.check4,
      })
    })

    // 一行目を選択
    selectedFaceId.value = historyInfos[0]?.faceId ?? ''
    kaiteiFlg.value = historyInfos[0]?.faceId ?? ''

    // 履歴情報
    Or35487Logic.state.set({
      uniqueCpId: or35487.value.uniqueCpId,
      state: {
        items: historyInfos,
        selectedItemId: selectedFaceId.value,
        kikanKanriFlg: inParam.value.kikanFlg,
      },
    })

    // 複写画面一覧
    OrX0142Logic.state.set({
      uniqueCpId: orX0142.value.uniqueCpId,
      state: {
        data: tabChecks.value[0],
      },
    })

    // 親画面.複写先改訂フラグが履歴選択一覧選択された行の改訂フラグが異いの場合
    if (kaiteiFlg.value !== inParam.value.kaiteiFlg) {
      // ・変数.画面表示フラグ = 0
      displayFlg.value = Or35478Const.DISPLAY_TYPE_0
    }

    // [フェースシート履歴ID]の変更により、フェースシートデータ取得を行う
  }
}

/**
 * 該当タブのフェースシートデータ取得
 */
const fetchTabData = async () => {
  // フェースシート情報取得
  switch (inParam.value.tabId) {
    case Or35478Const.TAB_NUM_1: {
      // タブ1

      // タブID：親画面.タブID
      // フェースシート履歴ID：変数.フェースシート履歴ID
      // 職員ID：親画面.ユーザログイン情報.職員ID
      // システムコード：”71101”
      // 改訂フラグ：変数.改訂フラグ
      // 事業所ID：親画面.事業所ID
      // 適用事業所IDリスト：親画面.適用事業所IDリスト
      // 事業者グループ適用ID：親画面.事業者グループ適用ID
      // 期間処理区分：親画面.期間処理区分
      // 履歴処理区分：親画面.履歴処理区分
      const inputData: FaceSheetPackage1InitSelectInEntity = {
        tabId: inParam.value.tabId,
        faceId: selectedFaceId.value,
        syoriKbn: inParam.value.planActFlag,
        rirekiSyoriKbn: inParam.value.historyActFlag,
        defSvJigyoId: inParam.value.svJigyoId,
        shokuinId: inParam.value.staffId,
        sysCd: Or35478Const.SYS_CODE,
        jigyoIdList: inParam.value.jigyoIdList,
        groupId: inParam.value.tekiyouGroupId,
        kaiteiFlg: kaiteiFlg.value,
      }

      const resData: FaceSheetPackage1InitSelectOutEntity = await ScreenRepository.select(
        'FaceSheetPackage1InitSelect',
        inputData
      )

      // 画面項目設定
      if (
        Or35478Const.RES_SUCCESS === resData.statusCode &&
        resData.data &&
        resData.data.face1List?.length > 0
      ) {
        // 返却した総件数<>0の場合
        // ・変数.画面表示フラグ = 2
        displayFlg.value = Or35478Const.DISPLAY_TYPE_2

        const or33216Data = Or33216Logic.data.get(or33216.value.uniqueCpId)
        if (or33216Data) {
          localOneway.faceSheet1 = {
            ...localOneway.faceSheet1,
            ...Or33216Logic.convertApiEntityToOneWayValue(resData.data),
            isCopy: true,
          }

          const faceSheet1Data = {
            ...or33216Data,
            ...Or33216Logic.convertApiEntityToTwoWayValue(resData.data),
          }

          Or33216Logic.data.set({
            uniqueCpId: or33216.value.uniqueCpId,
            value: faceSheet1Data,
            isInit: true,
          })
        }
      } else {
        // 返却した総件数＝0の場合
        // ・変数.画面表示フラグ = 1
        displayFlg.value = Or35478Const.DISPLAY_TYPE_1
      }
      break
    }
    case Or35478Const.TAB_NUM_2: {
      // タブ2
      // 返却した総件数<>0の場合
      // ・変数.画面表示フラグ = 2
      displayFlg.value = Or35478Const.DISPLAY_TYPE_2

      const inputData: FaceSheetPackage2InitSelectInEntity = {
        /** フェースシート履歴ID */
        faceId: selectedFaceId.value,
        /** 事業所ID */
        defSvJigyoId: inParam.value.svJigyoId,
        /** 職員ID */
        shokuinId: inParam.value.staffId,
        /** システムコード */
        sysCd: Or35478Const.SYS_CODE,
      }

      const resData: FaceSheetPackage2InitSelectOutEntity = await ScreenRepository.select(
        'FaceSheetPackage2InitSelect',
        inputData
      )

      // 画面項目設定
      if (
        Or35478Const.RES_SUCCESS === resData.statusCode &&
        resData.data &&
        resData.data.face2List?.length > 0
      ) {
        // 返却した総件数<>0の場合
        // ・変数.画面表示フラグ = 2
        displayFlg.value = Or35478Const.DISPLAY_TYPE_2

        const or35170Data = Or35170Logic.data.get(or35170.value.uniqueCpId)
        if (or35170Data) {
          localOneway.faceSheet2 = {
            ...localOneway.faceSheet2,
            ...Or35170Logic.convertApiEntityToOneWayValue(resData.data),
            isCopy: true,
          }

          const faceSheet2Data = {
            ...or35170Data,
            ...Or35170Logic.convertApiEntityToTwoWayValue(resData.data),
          }

          Or35170Logic.data.set({
            uniqueCpId: or35170.value.uniqueCpId,
            value: faceSheet2Data,
            isInit: true,
          })
        }
      } else {
        // 返却した総件数＝0の場合
        // ・変数.画面表示フラグ = 1
        displayFlg.value = Or35478Const.DISPLAY_TYPE_1
      }
      break
    }
    case Or35478Const.TAB_NUM_3: {
      // タブ3
      const inputData: FaceSheet3SelectInEntity = {
        userid: selectedUserId.value,
        getYmd: '', // 交付年月日 不明
        faceId: selectedFaceId.value,
      }

      const resData: FaceSheet3SelectSelectOutEntity = await ScreenRepository.select(
        'faceSheet3Select',
        inputData
      )

      // 画面項目設定
      if (
        Or35478Const.RES_SUCCESS === resData.statusCode &&
        resData.data &&
        resData.data.faceSheet3InfoOutDtoList?.length > 0
      ) {
        // 返却した総件数<>0の場合
        // ・変数.画面表示フラグ = 2
        displayFlg.value = Or35478Const.DISPLAY_TYPE_2

        const or33396Data = Or33396Logic.data.get(or33396.value.uniqueCpId)
        if (or33396Data) {
          localOneway.faceSheet3 = {
            ...localOneway.faceSheet3,
            isCopy: true,
          }

          const faceSheet3Data = {
            ...or33396Data,
            ...Or33396Logic.convertApiEntityToTwoWayValue(resData.data),
          }

          Or33396Logic.data.set({
            uniqueCpId: or33396.value.uniqueCpId,
            value: faceSheet3Data,
            isInit: true,
          })
        }
      } else {
        // 返却した総件数＝0の場合
        // ・変数.画面表示フラグ = 1
        displayFlg.value = Or35478Const.DISPLAY_TYPE_1
      }
      break
    }
    case Or35478Const.TAB_NUM_4: {
      // タブ4
      const inputData: FaceSheetPackage4InitSelectEntity = {
        /** フェースシート履歴ID */
        faceId: selectedFaceId.value,
        /**
         * 利用者ID
         */
        uid: selectedUserId.value,
        /**
         * 事業者ID
         */
        jid: inParam.value.svJigyoId,
        /**
         * 計画期間ID
         */
        sc1: selectedSc1Id.value,
        /**
         * 改訂フラグ
         */
        kaiteiFlg: inParam.value.kaiteiFlg,
      }
      const facesheet4: FaceSheetPackage4InitSelectOutEntity = await ScreenRepository.select(
        'FaceSheetPackage4InitSelect',
        inputData
      )
      // 画面項目設定
      if (Or35478Const.RES_SUCCESS === facesheet4.statusCode && facesheet4.data) {
        // 返却した総件数<>0の場合
        // ・変数.画面表示フラグ = 2
        displayFlg.value = Or35478Const.DISPLAY_TYPE_2

        const or33423Data = Or33423Logic.data.get(or33423.value.uniqueCpId)
        Or33423Logic.state.set({
          uniqueCpId: or33423.value.uniqueCpId,
          state: {
            kaigoHokenshaList: facesheet4.data.kaigoHokenshaList,
            zokuList: facesheet4.data.zokuList,
          },
        })
        if (facesheet4 && or33423Data) {
          const resData = facesheet4.data.FaceSheet3InfoSelDmyYoshien
          const faceSheet4Data = cloneDeep(or33423Data)
          faceSheet4Data[0].leftContent.data.SI011.value = resData.kHokenUmu
          faceSheet4Data[0].leftContent.data.SI013.value.modelValue = resData.kHokenCd
          faceSheet4Data[0].leftContent.data.SI015.value.value = resData.hHokenNo
          faceSheet4Data[0].leftContent.data.SI017.value = resData.kHokenTokuKbn
          faceSheet4Data[0].leftContent.data.SI019.value.value = resData.kHokenTokuYmd
          faceSheet4Data[0].leftContent.data.SI023.value = resData.ninteiUmu
          faceSheet4Data[0].leftContent.data.SI025.value = resData.yokaiKbn
          faceSheet4Data[0].leftContent.data.SI027.value = resData.yokaiKbn
          faceSheet4Data[0].leftContent.data.SI030.value.value = resData.ninStartYmd
          faceSheet4Data[0].leftContent.data.SI033.value.value = resData.ninEndYmd
          faceSheet4Data[0].rightContent.data.SI037.value.value = resData.nyushomaeKnj
          faceSheet4Data[1].leftContent.data.SI040.value = resData.techoUmu0
          faceSheet4Data[1].leftContent.data.SI042.value = resData.techoUmu1
          faceSheet4Data[1].leftContent.data.SI044.value.value = resData.techoShogaiKnj
          faceSheet4Data[1].leftContent.data.SI046.value = resData.techoUmu2
          faceSheet4Data[1].leftContent.data.SI048.value.value = resData.techoShogai2Knj
          faceSheet4Data[1].leftContent.data.SI050.value = resData.techoUmu3
          faceSheet4Data[1].leftContent.data.SI052.value.value = resData.techoShogai3Knj
          faceSheet4Data[1].leftContent.data.SI054.value = resData.shougaiNinteiUmu
          faceSheet4Data[1].leftContent.data.SI056.value = resData.teidoKbn
          faceSheet4Data[1].leftContent.data.SI059.value.value = resData.shogaiKnj
          faceSheet4Data[1].leftContent.data.SI062.value.value = resData.techoSYmd
          faceSheet4Data[1].leftContent.data.SI065.value.value = resData.techoEYmd
          faceSheet4Data[1].rightContent.data.SI069.value.value = resData.keizokuKnj
          faceSheet4Data[1].rightContent.data.SI071.value = resData.sotiKentoKbn
          faceSheet4Data[2].leftContent.data.SI074.value = resData.keiyakuKbn
          faceSheet4Data[2].leftContent.data.SI077.value.value = resData.keiyakuTokkiKnj
          faceSheet4Data[2].leftContent.data.SI079.value = resData.yogoKentoKbn
          faceSheet4Data[2].leftContent.data.SI082.value.value = resData.kenriHituyoKnj
          faceSheet4Data[2].leftContent.data.SI084.value = resData.yogoUmu
          faceSheet4Data[2].leftContent.data.SI087.value.value = resData.yogoTantoKnj
          faceSheet4Data[2].leftContent.data.SI090.value = resData.skoukenKentoKbn
          faceSheet4Data[2].leftContent.data.SI093.value.value = resData.seinenHituyoKnj
          faceSheet4Data[2].leftContent.data.SI095.value = resData.kokenUmu
          faceSheet4Data[2].leftContent.data.SI098.value.value = resData.kokenKnj
          faceSheet4Data[2].leftContent.data.SI101.value.value = resData.hosaKnj
          faceSheet4Data[2].leftContent.data.SI104.value.value = resData.hojoKnj
          faceSheet4Data[2].rightContent.data.SI106.value.modelValue = resData.nenkinUmu === '1'
          faceSheet4Data[2].rightContent.data.SI108.value.value = resData.nenkinKnj
          faceSheet4Data[2].rightContent.data.SI109.value.mo00045.value = resData.nenkinGaku
          faceSheet4Data[2].rightContent.data.SI111.value.modelValue =
            resData.syunyuSonotaUmu === '1'
          faceSheet4Data[2].rightContent.data.SI113.value.value = resData.syunyuSonotaKnj
          faceSheet4Data[2].rightContent.data.SI114.value.mo00045.value = resData.syunyuSonotaGaku
          faceSheet4Data[2].rightContent.data.SI118.value.value = resData.shotokuDankaiKnj
          faceSheet4Data[2].rightContent.data.SI120.value = resData.keizaiKbn
          faceSheet4Data[2].rightContent.data.SI123.value.value = resData.kazeijokyoKnj
          faceSheet4Data[2].rightContent.data.SI126.value.mo00045.value =
            resData.hiyogakuKaisouNyusho
          faceSheet4Data[2].rightContent.data.SI129.value.mo00045.value = resData.hiyogakuNyusho
          faceSheet4Data[2].rightContent.data.SI132.value.mo00045.value = resData.hiyogakuHuyo
          faceSheet4Data[2].rightContent.data.SI135.value.mo00045.value = resData.hiyogakuKaisouHuyo
          faceSheet4Data[2].rightContent.data.SI139.value.value = resData.shisanKnj
          faceSheet4Data[2].rightContent.data.SI141.value.mo00045.value = resData.kougakuGendogaku
          faceSheet4Data[3].leftContent.data.SI145.value = resData.seihoIryohojoUmu
          faceSheet4Data[3].leftContent.data.SI148.value.value = resData.seihoJisshiKnj
          faceSheet4Data[3].leftContent.data.SI150.value.value = resData.hujoKnj
          faceSheet4Data[3].leftContent.data.SI152.value.modelValue = resData.iryohujoUmu === '1'
          faceSheet4Data[3].rightContent.data.SI156.value.value = resData.sosaiJisshiKnj
          faceSheet4Data[3].rightContent.data.SI158.value = resData.sozokuUmu
          faceSheet4Data[3].rightContent.data.SI160.value.value = resData.sozoku1Knj
          faceSheet4Data[3].rightContent.data.SI162.value.modelValue = resData.sozoku1ZokuCd
          faceSheet4Data[3].rightContent.data.SI164.value.value = resData.sozoku2Knj
          faceSheet4Data[3].rightContent.data.SI166.value.modelValue = resData.sozoku2ZokuCd
          Or33423Logic.data.set({
            uniqueCpId: or33423.value.uniqueCpId,
            value: faceSheet4Data,
            isInit: true,
          })
        }
      } else {
        // 返却した総件数＝0の場合
        // ・変数.画面表示フラグ = 1
        displayFlg.value = Or35478Const.DISPLAY_TYPE_1
      }
      break
    }
    default:
      break
  }
}

/**
 * 計画期間データクリア
 */
const resetPlanData = () => {
  selectedSc1Id.value = ''
  planDataInfos.value.splice(0)
  // 計画期間
  Or35486Logic.state.set({
    uniqueCpId: or35486.value.uniqueCpId,
    state: {
      items: [],
      selectedItemId: selectedSc1Id.value,
    },
  })
}

/**
 * フェースシート履歴データクリア
 */
const resetHistoryData = () => {
  selectedFaceId.value = ''
  historyDataInfos.value.splice(0)
  tabChecks.value.splice(0)
  kaiteiFlg.value = ''
  displayFlg.value = Or35478Const.DISPLAY_TYPE_2
  // 履歴情報
  Or35487Logic.state.set({
    uniqueCpId: or35487.value.uniqueCpId,
    state: {
      items: [],
      selectedItemId: selectedFaceId.value,
      kikanKanriFlg: inParam.value.kikanFlg,
    },
  })
}

/**
 * 確認ダイアログのResolve
 */
let resolveInfoDialog: (value: string | PromiseLike<string>) => void

/**
 * 確認ダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openInfoDialog = (state: Or21814OnewayType) => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogTitle: t('label.top-btn-title'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      ...state,
      isOpen: true,
    },
  })
  return new Promise<string>((resolve) => {
    resolveInfoDialog = resolve
  })
}

/**
 * 確認ダイアログ閉じる時の処理
 */
watch(
  () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
  (newValue) => {
    if (newValue) {
      return
    }

    const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

    let result = Or35478Const.DIALOG_RESULT_CANCEL
    if (event?.firstBtnClickFlg) {
      result = Or35478Const.DIALOG_RESULT_YES
    }
    if (event?.secondBtnClickFlg) {
      result = Or35478Const.DIALOG_RESULT_NO
    }

    // 確認ダイアログのフラグをOFF
    Or21814Logic.event.set({
      uniqueCpId: or21814.value.uniqueCpId,
      events: {
        firstBtnClickFlg: false,
        secondBtnClickFlg: false,
        thirdBtnClickFlg: false,
        closeBtnClickFlg: false,
      },
    })

    if (resolveInfoDialog) {
      resolveInfoDialog(result)
    }
  }
)

/**
 * AC008
 * 「確定ボタン」押下
 */
const doConfirmAction = async () => {
  const selectedSheets = OrX0142Logic.state.get(orX0142.value.uniqueCpId)?.selectedTabs ?? []
  if (selectedSheets.length === 0) {
    // フェースシート未選択
    await openInfoDialog({
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11289'),
      secondBtnType: 'blank',
    })
    return
  }

  // 確認ダイアログを表示する
  const result = await openInfoDialog({
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10193', [t('label.face-sheet')]),
  })
  if (result !== Or35478Const.DIALOG_RESULT_YES) {
    return
  }

  // TODO No.150145

  // APIを呼び出して、複写を行う
  const selectedHistorys = historyDataInfos.value.filter(
    (historyInfo) => historyInfo.faceId === selectedFaceId.value
  )
  const selectedHistory = selectedHistorys[0]

  // 画面Noリスト：変数.画面Noリスト
  // 複写先フェースシート履歴ＩＤ：変数.複写先フェースシート履歴ＩＤ
  // 複写先期間ID：変数.複写先計画期間ID
  // 複写先改訂フラグ：親画面.複写先改訂フラグ
  // 作成日：履歴選択一覧選択された行の.作成日
  // 記載者ID：履歴選択一覧選択された行の.記載者ID
  // 複写元フェースシートID：履歴選択一覧選択された行の.フェースシートID
  // 複写元計画期間ID：履歴選択一覧選択された行の.計画期間ID
  // 複写元改訂フラグ：履歴選択一覧選択された行の.改訂
  // 更新回数：履歴選択一覧選択された行の.更新回数
  // 期間管理フラグ：親画面.期間管理フラグ
  // 法人ID：親画面.法人ID
  // 施設ID：親画面.施設ID
  // 事業所ID：親画面.事業所ID
  // 利用者ID：親画面.利用者ID
  // 職員ID：親画面.職員ID
  // システムコート：親画面.システムコート
  // 電子カルテ連携フラグ：親画面.電子カルテ連携フラグ
  // 機能名：親画面.機能名
  const req: FaceSheetCopyUpdateInEntity = {
    /** 期間管理フラグ */
    kikanFlg: inParam.value.kaiteiFlg,
    /** 事業者ID */
    svJigyoId: inParam.value.svJigyoId,
    /** 法人ID */
    hojinId: inParam.value.hojinId,
    /** 施設ID */
    shisetuId: inParam.value.shisetuId,
    /** 利用者ID */
    userId: inParam.value.userId,
    /** 種別ID */
    syubetsuId: inParam.value.syubetuId,
    /** 複写先アセスメントID */
    defFace1Id: inParam.value.face1Id,
    /** 複写先計画期間ID */
    defSc1Id: inParam.value.sc1Id,
    /** 複写先履歴更新回数 */
    defModifiedCnt: inParam.value.modifiedCnt,
    /** 複写先改訂フラグ */
    defKaiteiFlg: inParam.value.kaiteiFlg,
    /** 複写元アセスメントID */
    face1Id: selectedFaceId.value,
    /** 複写元計画期間ID */
    sc1Id: selectedSc1Id.value,
    /** 複写元改訂フラグ */
    kaiteiFlg: selectedHistory.kaiteiFlg,
    /** 複写元作成日 */
    createYmd: selectedHistory.create_ymd,
    /** 複写元作成者 */
    shokuId: selectedHistory.shokuId,
    /** 複写元ケース番号 */
    caseNo: selectedHistory.caseNo,
    /** 複写元変更回数 */
    henkoKaisu: selectedHistory.henkoKaisu,
    /** 複写元初回作成日 */
    shokaiYmd: selectedHistory.shokai_ymd,
    /** 画面Noリスト */
    noList: selectedSheets,
  }

  const res: FaceSheetCopyUpdateOutEntity = await ScreenRepository.update(
    'faceSheetCopyUpdate',
    req
  )

  if (Or35478Const.RES_SUCCESS === res.statusCode && res.data) {
    // "返却情報リストに格納する
    // ・返却情報リスト.複写先フェースシート履歴ＩＤ=フェースシートID
    // ・返却情報リスト.複写先計画期間ID =計画期間ID"
    setState({
      outParam: {
        sc1Id: res.data.sc1Id,
        faceId: res.data.face1Id,
      },
    })

    setEvent({ multiConfirmFlg: true })

    // '返却情報リストを前画面に返却し、画面を閉じる。
    doCloseAction()
  }
}

/**
 * AC002
 * 「×ボタン」押下
 * AC007
 * 「閉じるボタン」押下
 */
const doCloseAction = () => {
  setState({ isOpen: false })
}

/**
 * AC004
 * 「計画期間選択一覧」選択変更
 */
watch(
  () => Or35486Logic.state.get(or35486.value.uniqueCpId)?.selectedItemId,
  (newValue) => {
    selectedSc1Id.value = newValue ?? ''
  }
)

/**
 * AC005
 * 「履歴選択一覧」選択変更
 */
watch(
  () => Or35487Logic.state.get(or35487.value.uniqueCpId)?.selectedItemId,
  (newValue) => {
    for (let index = 0; index < historyDataInfos.value.length; index++) {
      if (historyDataInfos.value[index].faceId === newValue) {
        // 改訂フラグ更新
        kaiteiFlg.value = historyDataInfos.value[index].kaiteiFlg

        // 複写画面一覧を更新
        const tabCheck = tabChecks.value[index]

        OrX0142Logic.state.set({
          uniqueCpId: orX0142.value.uniqueCpId,
          state: {
            data: tabCheck,
          },
        })

        break
      }
    }

    selectedFaceId.value = newValue ?? ''
  }
)

/**************************************************
 * ライフサイクルフック
 **************************************************/
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024"
  >
    <template #cardItem>
      <c-v-sheet class="container h-100">
        <c-v-row class="h-100 flex-nowrap">
          <c-v-col
            cols="auto"
            class="pa-2 user-list overflow-auto"
          >
            <!-- 利用者選択一覧 -->
            <g-base-or00248 v-bind="or00248" />
          </c-v-col>
          <c-v-col class="pa-0 d-flex flex-column border-s flex-1-1-0">
            <c-v-row class="flex-0-0 border-b">
              <!-- 計画期間一覧 -->
              <c-v-col
                v-if="showKiKanFlag"
                cols="6"
                class="pa-2 pr-0"
              >
                <g-custom-or35486 v-bind="or35486" />
              </c-v-col>
              <!-- 履歴一覧 -->
              <c-v-col
                :cols="showKiKanFlag ? 6 : 9"
                class="pa-2"
              >
                <g-custom-or35487 v-bind="or35487" />
              </c-v-col>
            </c-v-row>
            <c-v-row class="flex-1-1-0 min-h-0">
              <c-v-col
                v-if="showCopySectionFlag"
                cols="auto"
                class="sheet-select-col border-e h-100"
              >
                <c-v-row>
                  <base-mo01338
                    v-if="!showCopyAllWarnFlag"
                    :oneway-model-value="localOneway.mo01338CopyWarn"
                  />
                  <base-mo01338
                    v-else
                    :oneway-model-value="localOneway.mo01338CopyAllWarn"
                  />
                </c-v-row>
                <c-v-row
                  v-if="showCopyFaceSheetFlag"
                  class="pt-2"
                >
                  <!-- 複写画面一覧 -->
                  <g-custom-or-x0142 v-bind="orX0142" />
                </c-v-row>
              </c-v-col>
              <c-v-col
                v-if="showCopySectionFlag"
                class="flex-1-1-0 h-100 overflow-auto"
              >
                <!-- フェースシート① -->
                <g-custom-or33216
                  v-if="showCopyFaceSheetTabFlag && inParam.tabId === Or35478Const.TAB_NUM_1"
                  v-bind="or33216"
                  :oneway-model-value="localOneway.faceSheet1"
                />
                <!-- フェースシート② -->
                <g-custom-or35170
                  v-else-if="showCopyFaceSheetTabFlag && inParam.tabId === Or35478Const.TAB_NUM_2"
                  v-bind="or35170"
                  :oneway-model-value="localOneway.faceSheet2"
                />
                <!-- フェースシート③ -->
                <g-custom-or33396
                  v-else-if="showCopyFaceSheetTabFlag && inParam.tabId === Or35478Const.TAB_NUM_3"
                  v-bind="or33396"
                  :oneway-model-value="localOneway.faceSheet3"
                />
                <!-- フェースシート④-->
                <g-custom-or33423
                  v-else-if="showCopyFaceSheetTabFlag && inParam.tabId === Or35478Const.TAB_NUM_4"
                  v-bind="or33423"
                  :oneway-model-value="localOneway.faceSheet4"
                />
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611"
          class="mx-2"
          @click="doCloseAction"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609"
          @click="doConfirmAction"
        >
          <!--ツールチップ表示："設定を確定します。"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>

  <!-- インフォメーションダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>

<style lang="scss" scoped>
@use '@/styles/base.scss';

.container {
  :deep(.v-row) {
    margin: 0px;
  }

  .user-list {
    :deep(br) {
      display: none;
    }

    :deep(.v-card-actions) {
      padding: 0;
    }
  }

  .sheet-select-col {
    width: 384px;
  }
}

.text-red {
  color: rgb(var(--v-theme-error));
}

.min-h-0 {
  min-height: 0;
}

.hidden {
  visibility: hidden;
}
</style>
