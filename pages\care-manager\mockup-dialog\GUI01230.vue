<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { Or10004Logic } from '~/components/custom-components/organisms/Or10004/Or10004.logic'
import { Or10004Const } from '~/components/custom-components/organisms/Or10004/Or10004.constants'
import type {
  Or10004OneWayType,
  Or10004ThreeList,
  Or10004TwoList,
  Or10004Type,
} from '~/components/custom-components/organisms/Or10004/Or10004.type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01230'
// ルーティング
const routing = 'GUI01230/pinia'
// 画面物理名
const screenName = 'GUI01230'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or10004 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01230' },
})

const fieldStylePackList = [
  {
    id: '1',
    seq: '1',
    mokuhyoKnj: '精神不安の為、拒否',
    kikanKnj: '2024/11/12\r\n ~ \r\n2024/11/12',
    jyoukyouKnj: 'OK',
    tantoHyoukaCd: '△',
    tantoHyoukaKnj: 'ああ方',
    userHyoukaCd: '△',
    userHyoukaKnj: 'ああ方',
    taiouCd: '継',
    taiouKnj:
      '精神的に不安になる出来事があったようで(内容はまだわからない状態)うで(内容はまだわからない状態))うで(内容はまだわからない状態)(内容はまだわからない状態)',
  },
  {
    id: '2',
    seq: '1',
    mokuhyoKnj: 'り帰日',
    kikanKnj: '2024/11/12\r\n ~ \r\n2024/11/12',
    jyoukyouKnj: 'NG',
    tantoHyoukaCd: '◎',
    tantoHyoukaKnj: '○',
    userHyoukaCd: '◎',
    userHyoukaKnj: '12300',
    taiouCd: '継',
    taiouKnj: '悪寒がある為、体を温めます',
  },
]

const fieldStyleList = [
  {
    id: '1',
    seq: '1',
    kadaiNo: '1',
    kadaiKnj: '最近, 移動動作が緩慢になり、尿失禁が始まりましたが、それに対応し',
    choukiKnj: '移動動作は自分ひとりで行うことができます。',
    tankiKnj: '移動は一人でできる',
    servNo: '2',
    kaigoKnj: '本院により、移動作立・姿勢保持等についての機器訓練',
    kakuninCd: '1000',
    kakuninKnj: '一部介助が必要だが、自分から食事',
    houhouCd: '1000',
    houhouKnj: '一部介助が必要だが、自分から食事',
    kakuninYmd: '2024/10/10',
    ikenHonCd: '1000',
    ikenHonKnj: 'コントロールが出',
    ikenKazCd: '1000',
    ikenKazKnj: 'コントロールが出',
    jusokuCd: '1000',
    jusokuKnj: '精神不安の為、拒否',
    taiouCd: '1000',
    taiouKnj: '精神不安の為、拒否',
  },
  {
    id: '2',
    seq: '2',
    kadaiNo: '1',
    kadaiKnj: '最近, 移動動作が緩慢になり',
    choukiKnj: '移動動作は自分ひとりで行うことができます。',
    tankiKnj: '移動は一人でできる',
    servNo: '2',
    kaigoKnj: '本院により、移動作立・姿勢保持等についての機器訓練',
    kakuninCd: '1000',
    kakuninKnj: '一部介助が必要だが、自分から食事',
    houhouCd: '1000',
    houhouKnj: '一部介助が必要だが、自分から食事',
    kakuninYmd: '2024/10/10',
    ikenHonCd: '1000',
    ikenHonKnj: 'コントロールが出',
    ikenKazCd: '1000',
    ikenKazKnj: 'コントロールが出',
    jusokuCd: '1000',
    jusokuKnj: '精神不安の為、拒否',
    taiouCd: '1000',
    taiouKnj: '精神不安の為、拒否',
  },
  {
    id: '3',
    seq: '3',
    kadaiNo: '1',
    kadaiKnj: '最近, 移動動作が緩慢になり、それに対応し',
    choukiKnj: '移動動作は自分ひとりで行うことができます。',
    tankiKnj: '移動は一人でできる',
    servNo: '2',
    kaigoKnj: '本院により、移動作立・姿勢保持等についての機器訓練',
    kakuninCd: '1000',
    kakuninKnj: '一部介助が必要だが、自分から食事',
    houhouCd: '1000',
    houhouKnj: '一部介助が必要だが、自分から食事',
    kakuninYmd: '2024/10/10',
    ikenHonCd: '1000',
    ikenHonKnj: 'コントロールが出',
    ikenKazCd: '1000',
    ikenKazKnj: 'コントロールが出',
    jusokuCd: '1000',
    jusokuKnj: '精神不安の為、拒否',
    taiouCd: '1000',
    taiouKnj: '精神不安の為、拒否',
  },
] as Or10004TwoList[]

const customStyleList = [
    {
    id: '1',
    seq: '1',
    col1:{
      koumokuKnj:'',
      koumokuCod:'1',
      koumokuYmd:''
    },
    col2:{
      koumokuKnj:'最近,, 移動動作が緩慢になり、それに対応し',
      koumokuCod:'',
      koumokuYmd:''
    },
    col3:{
      koumokuKnj:'移動動作は自分ひとりで行うことができます。',
      koumokuCod:'',
      koumokuYmd:''
    },
    col4:{
      koumokuKnj:'移動は一人でできる',
      koumokuCod:'',
      koumokuYmd:''
    },
    col5:{
      koumokuKnj:'',
      koumokuCod:'2',
      koumokuYmd:''
    },
    col6:{
      koumokuKnj:'本院により、移動作立・姿勢保持等についての機器訓練',
      koumokuCod:'',
      koumokuYmd:''
    },
    col7:{
      koumokuKnj:'一部介助が必要だが、自分から食事',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col8:{
      koumokuKnj:'一部介助が必要だが、自分から食事',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col9:{
      koumokuKnj:'',
      koumokuCod:'',
      koumokuYmd:'2025/07/24'
    },
    col10:{
      koumokuKnj:'コントロールが出',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col11:{
      koumokuKnj:'コントロールが出',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col12:{
      koumokuKnj:'精神不安の為、拒否',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col13:{
      koumokuKnj:'精神不安の為、拒否',
      koumokuCod:'1000',
      koumokuYmd:''
    },
  },
    {
    id: '2',
    seq: '2',
    col1:{
      koumokuKnj:'',
      koumokuCod:'2',
      koumokuYmd:''
    },
    col2:{
      koumokuKnj:'最近, 移動動作が緩慢になり、それに対応し',
      koumokuCod:'',
      koumokuYmd:''
    },
    col3:{
      koumokuKnj:'移動動作は自分ひとりで行うことができます。',
      koumokuCod:'',
      koumokuYmd:''
    },
    col4:{
      koumokuKnj:'移動は一人でできる',
      koumokuCod:'',
      koumokuYmd:''
    },
    col5:{
      koumokuKnj:'',
      koumokuCod:'2',
      koumokuYmd:''
    },
    col6:{
      koumokuKnj:'本院により、移動作立・姿勢保持等についての機器訓練',
      koumokuCod:'',
      koumokuYmd:''
    },
    col7:{
      koumokuKnj:'一部介助が必要だが、自分から食事',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col8:{
      koumokuKnj:'一部介助が必要だが、自分から食事',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col9:{
      koumokuKnj:'',
      koumokuCod:'',
      koumokuYmd:'2025/07/24'
    },
    col10:{
      koumokuKnj:'コントロールが出',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col11:{
      koumokuKnj:'コントロールが出',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col12:{
      koumokuKnj:'精神不安の為、拒否',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col13:{
      koumokuKnj:'精神不安の為、拒否',
      koumokuCod:'1000',
      koumokuYmd:''
    },
  },
    {
    id: '3',
    seq: '3',
    col1:{
      koumokuKnj:'',
      koumokuCod:'3',
      koumokuYmd:''
    },
    col2:{
      koumokuKnj:'最近, 移動動作が緩慢になり、それに対応し',
      koumokuCod:'',
      koumokuYmd:''
    },
    col3:{
      koumokuKnj:'移動動作は自分ひとりで行うことができます。',
      koumokuCod:'',
      koumokuYmd:''
    },
    col4:{
      koumokuKnj:'移動は一人でできる',
      koumokuCod:'',
      koumokuYmd:''
    },
    col5:{
      koumokuKnj:'',
      koumokuCod:'2',
      koumokuYmd:''
    },
    col6:{
      koumokuKnj:'本院により、移動作立・姿勢保持等についての機器訓練',
      koumokuCod:'',
      koumokuYmd:''
    },
    col7:{
      koumokuKnj:'一部介助が必要だが、自分から食事',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col8:{
      koumokuKnj:'一部介助が必要だが、自分から食事',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col9:{
      koumokuKnj:'',
      koumokuCod:'',
      koumokuYmd:'2025/07/24'
    },
    col10:{
      koumokuKnj:'コントロールが出',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col11:{
      koumokuKnj:'コントロールが出',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col12:{
      koumokuKnj:'精神不安の為、拒否',
      koumokuCod:'1000',
      koumokuYmd:''
    },
    col13:{
      koumokuKnj:'精神不安の為、拒否',
      koumokuCod:'1000',
      koumokuYmd:''
    },
  },
] as Or10004ThreeList[]

const systemCommons = useSystemCommonsStore()

const or10004OneWayType = ref({
  youshikiId: '',
  carePlanFlg: '',
  sys3ryaku: '',
  list: [],
} as Or10004OneWayType)

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or10004.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01230',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or10004Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or10004Const.CP_ID(1)]: or10004.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or10004Logic.initialize(or10004.value.uniqueCpId)
}

/**
 *  ボタン押下時の処理
 *
 * @param type - 様式
 *
 * @param show - 番号を表示する
 */
function onClickOr10004(type: string, show?: boolean) {
  if (show) {
    or10004OneWayType.value = {
      youshikiId: '0',
      carePlanFlg: '1',
      sys3ryaku: systemCommons.getSystemAbbreviation ?? '',
      free1Id: '1',
      noFlg: '0',
      longTermFlg: '0',
      list: fieldStyleList,
    }
  } else {
    if (type === '1') {
      or10004OneWayType.value = {
        youshikiId: '0',
        carePlanFlg: '5',
        sys3ryaku: systemCommons.getSystemAbbreviation ?? '',
        list: fieldStylePackList,
      }
    } else if (type === '2') {
      or10004OneWayType.value = {
        youshikiId: '0',
        carePlanFlg: '1',
        sys3ryaku: systemCommons.getSystemAbbreviation ?? '',
        free1Id: '1',
        noFlg: '1',
        longTermFlg: '1',
        list: fieldStyleList,
      }
    } else {
      or10004OneWayType.value = {
        youshikiId: '1',
        carePlanFlg: '1',
        sys3ryaku: systemCommons.getSystemAbbreviation ?? '',
        free1Id: '1',
        noFlg: '1',
        longTermFlg: '1',
        list: customStyleList,
      }
    }
  }

  Or10004Logic.state.set({
    uniqueCpId: or10004.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const onUpdate = (val: Or10004Type) => {
  console.log(val)
}

// ダイアログ表示フラグ
const showDialogOr10004 = computed(() => {
  // Or10004のダイアログ開閉状態
  return Or10004Logic.state.get(or10004.value.uniqueCpId)?.isOpen ?? false
})
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ CD 2025/05/09 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr10004('1')"
        >GUI01230_表示順変更：モニタリング記録表_固定様式パッケージプラン
      </v-btn>
      <g-custom-or-10004
        v-if="showDialogOr10004"
        v-bind="or10004"
        :oneway-model-value="or10004OneWayType"
        @update:model-value="onUpdate"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <v-btn
      variant="plain"
      @click="onClickOr10004('2')"
      >GUI01230_表示順変更：モニタリング記録表_固定様式パッケージプラン以外
    </v-btn>
  </c-v-row>
  <c-v-row no-gutters>
    <v-btn
      variant="plain"
      @click="onClickOr10004('3')"
      >GUI01230_表示順変更：モニタリング記録表_カスタマイズ様式
    </v-btn>
  </c-v-row>
  <c-v-row no-gutters>
    <v-btn
      variant="plain"
      @click="onClickOr10004('2', true)"
      >GUI01230_表示順変更：モニタリング記録表_固定様式パッケージプラン以外[番号の区分と長期目標の区分は表示しません]
    </v-btn>
  </c-v-row>
  <!-- POP画面ポップアップ CD 2025/05/09 ADD END-->
</template>
